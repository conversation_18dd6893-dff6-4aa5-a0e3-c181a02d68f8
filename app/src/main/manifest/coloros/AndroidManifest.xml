<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <queries>
        <!-- required to export note-->
        <package android:name="com.coloros.note" />
        <package android:name="com.oplus.note" />
        <!-- required to need hide call recordings-->
        <package android:name="com.android.incallui" />
        <!-- required to need wechat share-->
        <package android:name="com.tencent.mm" />
        <!-- required to need bootreg-->
        <package android:name="com.coloros.bootreg" />
        <!-- required to wps export-->
        <package android:name="cn.wps.moffice_eng" />
        <!-- required to getOpenId to bind stdId app -->
        <package android:name="com.oplus.stdid" />
        <package android:name="com.oplus.dmp" />
        <!-- 图片标记新增查询包名-->
        <package android:name="com.oppo.camera" />
        <package android:name="com.oplus.camera" />
        <!--一加内销未融合项目还是oneplus相机包名-->
        <package android:name="com.oneplus.camera" />
        <package android:name="com.coloros.gallery3d" />
        <!--一加相册包名-->
        <package android:name="com.oneplus.gallery" />
        <!--负一屏包名-->
        <package android:name="com.coloros.assistantscreen" />
        <!--悬停空间-->
        <package android:name="com.oplus.bracketspace" />
        <!--电池-->
        <package android:name="com.coloros.oppoguardelf" />
        <package android:name="com.oplus.battery" />
        <!--手机管家-->
        <package android:name="com.coloros.phonemanager" />
        <!--三方应用通话录音-->
        <package android:name="com.oplus.audiomonitor" />
    </queries>

    <!-- for AlarmManager API，such as: setAlarmClock、setExact、setExactAndAllowWhileIdle-->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- set recorder mode -->
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />

    <!-- need check -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <!-- BlueSpeaker-->
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />

    <!-- CTS -->
    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
    <uses-permission android:name="oplus.permission.OPLUS_COMPONENT_SAFE" />
    <uses-permission android:name="oppo.permission.cloud.ACCESS_CLOUD" />

    <!--SAU-->
    <uses-permission android:name="com.oppo.permission.safe.SAU" />
    <uses-permission android:name="com.oplus.permission.safe.SAU" />
    <!--for encyption-->
    <uses-permission android:name="com.oppo.permission.safe.PRIVATE" />
    <uses-permission android:name="com.oplus.permission.safe.PRIVATE" />
    <!--云同步sdk底层获取guid-->
    <uses-permission android:name="com.oplus.permission.safe.SECURITY" />
    <!--for center dmp search-->
    <uses-permission android:name="com.oplus.dmp.IndexProvider.PERMISSION" />
    <uses-permission android:name="com.oplus.dmp.SearchProvider.PERMISSION" />
    <!--虚拟化从设备需要的权限-->
    <uses-permission android:name="com.oppo.permission.safe.PHONE" />
    <uses-permission android:name="com.oplus.permission.safe.PHONE" />

    <!-- Android T -->
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!-- Android T -->
    <!--悬停空间所需permission-->
    <uses-permission android:name="oplus.bracketspace.permission.INSERT_PERMISSION" />

    <uses-permission android:name="com.oplus.permission.safe.WINDOW" />

    <uses-sdk tools:overrideLibrary="com.oplus.sdk.addon.sdk"/>
    <!--震动权限-->
    <uses-permission android:name="android.permission.VIBRATE"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED"/>
    <uses-permission android:name="android.permission.TURN_SCREEN_ON"/>

    <application
        android:name="oplus.multimedia.soundrecorder.RecorderApplication"
        android:allowBackup="false"
        android:directBootAware="true"
        android:extractNativeLibs="true"
        android:icon="@drawable/ic_launcher_recorder"
        android:label="@string/app_name_main"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:replace="android:label,android:allowBackup,android:theme,android:name,android:icon">
        <meta-data
            android:name="color.support.options"
            android:value="@string/color_support_value" />
        <meta-data
            android:name="versionCommit"
            android:value="${versionCommit}" />
        <meta-data
            android:name="versionDate"
            android:value="${versionDate}" />
        <meta-data
            android:name="AppCode"
            android:value="20007" />
        <meta-data
            android:name="upgrade_product_code"
            android:value="20199" />

        <meta-data
            android:name="application_do_not_disable"
            android:value="true" />

        <meta-data
            android:name="support_mp3_callrecordings"
            android:value="true" />

        <meta-data
            android:name="OppoPermissionKey"
            android:value="@string/permission_key" />

        <!--api-adapter必要权限2：授权码 -->
        <meta-data
            android:name="AppPlatformKey"
            android:value="@string/appPlatform_permission_key" />

        <meta-data
            android:name="color_material_enable"
            android:value="true" />

        <meta-data
            android:name="OplusAppCompatibilityToken"
            android:value="12,22" />

        <meta-data
            android:name="support_speech_assist"
            android:value="true" />

        <!--搬家配置，由于资源读取不到，放到这里来-->
        <meta-data
            android:name="backup_name_resId"
            android:value="@string/app_name_main" />

        <meta-data
            android:name="backup_icon_resId"
            android:value="@drawable/ic_launcher_recorder" />
        <!--搬家配置结束-->
        <!--配置当前版本支持悬停空间-->
        <meta-data
            android:name="BracketSpaceSupport"
            android:value="true" />

        <!--照片与视频权限弹窗内销自定义描述文案-->
        <meta-data
            android:name="android.permission-group.READ_MEDIA_VISUAL"
            android:resource="@string/permission_open_read_image_desc_new" />

        <!--录音文件权限弹窗内销自定义描述文案-->
        <meta-data
            android:name="android.permission-group.READ_MEDIA_AURAL"
            android:resource="@string/permission_save_show_audio_file" />

        <!--通知权限弹窗内销自定义描述文案-->
        <meta-data
            android:name="android.permission-group.NOTIFICATIONS"
            android:resource="@string/permission_notification" />

        <!--使用麦克风权限弹窗内销自定义描述文案-->
        <meta-data
            android:name="android.permission-group.MICROPHONE"
            android:resource="@string/permission_recording_use_record" />

        <meta-data
            android:name="oplus_search_config"
            android:resource="@xml/oplus_search_config"/>

        <activity
            android:name="oplus.multimedia.soundrecorder.slidebar.TransparentActivity"
            android:configChanges="locale|orientation|keyboardHidden|mcc|mnc|density"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:label="@string/app_name_main"
            android:launchMode="singleTop"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:screenOrientation="behind"
            android:theme="@style/TransparentActivityTheme"
            android:uiOptions="splitActionBarWhenNarrow"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <!--新增action启动快捷录音，侧边栏未修改，仍以className调起的，此处增加为了后续兼容-->
                <action android:name="com.oplus.soundrecorder.open_quick_recording" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>

            <intent-filter>
                <!--新增action 小布助手启动-->
                <action android:name="oplus.intent.action.START_RECORD_FROM_BREENO" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 悬停空间-->
                <action android:name="com.oplus.soundrecorder.LAUNCH_FROM_BRACKET_SPACE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 负一屏卡片启动-->
                <action android:name="oplus.intent.action.provider.start_transparent_from_smallcard" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.soundrecorder.dragonfly.startRecordActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 个人信息保护政策启动-->
                <action android:name="com.oplus.soundrecorder.thirdapp.record.startPrivacyPolicyActivity" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 火烈鸟miniApp接续-->
                <action android:name="com.soundrecorder.MINI_APP_CONTINUE_LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 泛在状态栏胶囊点击跳转录制页面逻辑-->
                <action android:name="oplus.intent.action.com.soundrecorder.SEEDLING_CARD" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 魔方按钮-->
                <action android:name="oplus.intent.action.START_RECORD_FROM_CUBE_BUTTON" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 录音摘要卡-->
                <action android:name="oplus.intent.action.com.soundrecorder.SUMMARY_CARD" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <!--新增action 锁屏-->
                <action android:name="com.oplus.soundrecorder.LOCK_SCREEN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <!--增加侧边栏启动activity重定向，侧边栏目前是通过硬编码路径跳转的-->
        <activity-alias
            android:name="com.coloros.soundrecorder.TransparentActivity"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE"
            android:targetActivity="oplus.multimedia.soundrecorder.slidebar.TransparentActivity" />

        <provider
            android:name="oplus.multimedia.soundrecorder.RecorderStateProvider"
            android:authorities="com.multimedia.record.state.provider"
            android:exported="true"
            android:permission="oplus.permission.OPLUS_COMPONENT_SAFE" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/transfer_paths" />
        </provider>

    </application>

</manifest>
