/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  TransparentActivity
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/11/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.slidebar

import android.app.Activity
import android.content.Intent
import android.content.res.Resources
import android.os.Bundle
import android.os.PowerManager
import android.provider.MediaStore.Audio
import android.view.OplusWindowManager
import android.view.View
import android.widget.FrameLayout
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import com.coloros.soundrecorder.R
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.ext.IntentExt.getIntValue
import com.soundrecorder.base.ext.getStringExtraSecure
import com.soundrecorder.base.splitwindow.bracketspace.BracketSpaceProviderAgent
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.browsefile.BrowseFile
import com.soundrecorder.browsefile.StartActivityUtils
import com.soundrecorder.browsefile.StartRecordModel
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.BuryingPoint.addRecordStartType
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.RecorderUserActionKt
import com.soundrecorder.common.card.JsonUtils
import com.soundrecorder.common.card.KeyMagicMessengerService
import com.soundrecorder.common.card.RecordSeedlingSmallCardWidgetProvider
import com.soundrecorder.common.card.zoom.fanzaiai.channel.AppCardEventProcessor
import com.soundrecorder.common.card.zoom.fanzaiai.channel.AppCardStateProcessor
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.common.permission.PermissionActivity
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.common.task.ActivityTaskUtils.any
import com.soundrecorder.common.task.RecordRouterManager
import com.soundrecorder.common.utils.BrightScreenUtil
import com.soundrecorder.common.utils.CoroutineUtils.doInIOThread
import com.soundrecorder.common.utils.RecordFileChangeNotify
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.EditRecordInterface
import com.soundrecorder.modulerouter.recorder.RecordInterface
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.utils.Injector
import oplus.multimedia.soundrecorder.BreenoStartRecordUtil
import oplus.multimedia.soundrecorder.SoundRecordBinder
import oplus.multimedia.soundrecorder.card.small.SmallCardManager
import org.json.JSONObject

class TransparentActivity : PermissionActivity() {

    companion object {
        private const val TAG = "ATransparentActivity"
        private const val SMALL_CARD_METHOD = "small_card_method"
        private const val METHOD_START_SERVICE = "method_start_service"
        private const val METHOD_JUST_START = "method_just_start"
        private const val ACTION_BREENO = "oplus.intent.action.START_RECORD_FROM_BREENO"
        private const val ACTION_BRACKET_SPACE = "com.oplus.soundrecorder.LAUNCH_FROM_BRACKET_SPACE"
        private const val ACTION_SMALL_CARD = "oplus.intent.action.provider.start_transparent_from_smallcard"
        private const val ACTION_DRAGON_FLY = "com.soundrecorder.dragonfly.startRecordActivity"
        private const val ACTION_THIRDAPP_START_PRIVA = "com.oplus.soundrecorder.thirdapp.record.startPrivacyPolicyActivity"
        private const val ACTION_SEEDLING_CARD = "oplus.intent.action.com.soundrecorder.SEEDLING_CARD"
        //魔方按鍵
        private const val ACTION_CUBE_BUTTON = "oplus.intent.action.START_RECORD_FROM_CUBE_BUTTON"

        //锁屏
        private const val ACTION_LOCK_SCREEN = "com.oplus.soundrecorder.LOCK_SCREEN"

        //1*2小泛在卡
        private const val ACTION_SMALL_SEEDLING_CARD = "oplus.intent.action.com.soundrecorder.SMALL_SEEDLING_CARD"

        /**
         * 录音摘要卡
         */
        private const val ACTION_SUMMARY_CARD = "oplus.intent.action.com.soundrecorder.SUMMARY_CARD"

        /*火烈鸟外屏接续到内屏*/
        private const val ACTION_MINI_APP_CONTINUE = "com.soundrecorder.MINI_APP_CONTINUE_LAUNCHER"

        //三方应用通话录音跳转个人信息页面入口
        private const val PAGE_FROM_THIRD_APP_RECORD = "dragonThirdRecord"
        private const val START_TYPE_FROM_MINI_APP_CONTINUE = "MINI_APP_CONTINUE"
        private const val FROM_SEEDLING_CARD_CLICK = "SEEDLING_CARD_CLICK"
        private const val UN_LOCK_OR_SHOW_SECURITY = "unlockOrShowSecurity"
    }

    private var mStartType = RecordRouterManager.PAGE_FROM_SLIDE_BAR
    private var needShowSnackBar = false
    private var mWakeLock: PowerManager.WakeLock? = null

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val settingApi by lazy {
        Injector.injectFactory<SettingInterface>()
    }

    private val recordApi by lazy {
        Injector.injectFactory<RecordInterface>()
    }

    private val editRecordApi by lazy {
        Injector.injectFactory<EditRecordInterface>()
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    private val seedlingApi by lazy {
        Injector.injectFactory<SeedingInterface>()
    }

    private val mPermissionGrantedListener = {
        when (mStartType) {
            RecordRouterManager.PAGE_FROM_SUMMARY_CARD -> dealIntentAfterPermission()
            else -> {
                val hasAllPermission = PermissionUtils.hasReadAudioPermission() && PermissionUtils.hasRecordAudioPermission()
                DebugUtil.d(TAG, "onPermissionGranted, hasAllPermission:$hasAllPermission")
                if (hasAllPermission) {
                    //已经申请过通知权限，但是被拒绝了，本次运行期间，重新进入录制界面则需要展示snackBar，本次活动期间内没展示，则以后都不展示
                    needShowSnackBar =
                        PermissionUtils.isNeedShowNotificationPermissionSnackBar(this@TransparentActivity)
                    //设置已经展示过snackBar
                    PermissionUtils.setHasShowNotificationPermissionSnackBar(this@TransparentActivity)
                    dealIntentAfterPermission()
                }
                if (RecordRouterManager.PAGE_FROM_SMALL_CARD == mStartType) {
                    SmallCardManager.onRequestPermissionCallBack(
                        intent.getStringExtraSecure(SMALL_CARD_METHOD) == METHOD_START_SERVICE,
                        hasAllPermission
                    )
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        parseDataFromIntent()
        saveActionCubeState()
        val hasAllPermission = PermissionUtils.hasReadAudioPermission() && PermissionUtils.hasRecordAudioPermission()
        if (!hasAllPermission && (mStartType == RecordRouterManager.PAGE_FROM_LOCK_SCREEN
                    || mStartType == RecordRouterManager.PAGE_FROM_CUBE_BUTTON)) {
            OplusWindowManager().requestKeyguard(UN_LOCK_OR_SHOW_SECURITY)
        }
        super.onCreate(savedInstanceState)
        if (dealIntentBeforePermission(savedInstanceState != null)) {
            return
        }
        if ("true" == intent?.extras?.getString("is_from_seeding_small_card")) {
            ActivityTaskUtils.clearAllTaskExclude(TransparentActivity::class.java)
        }
        setPermissionGrantedListener(mPermissionGrantedListener)
        initiateWindowInsets()
    }

    private fun saveActionCubeState() {
        intent.putExtra(
            FROM_CUBE_CLICK, mStartType == RecordRouterManager.PAGE_FROM_CUBE_BUTTON
        )
    }

    private fun parseDataFromIntent() {
        try {
            when (intent?.action) {
                ACTION_BREENO -> {
                    BuryingPoint.addFromBreeno()
                    mStartType = RecordRouterManager.PAGE_FROM_BREENO
                }
                ACTION_BRACKET_SPACE -> {
                    mStartType = RecordRouterManager.PAGE_FROM_BRACKET_SPACE
                }
                ACTION_SMALL_CARD -> {
                    BuryingPoint.addLaunchAppSmallCard(RecorderUserAction.VALUE_LAUNCH_APP_SMALL_CARD)
                    mStartType = RecordRouterManager.PAGE_FROM_SMALL_CARD
                }
                ACTION_DRAGON_FLY -> {
                    mStartType = RecordRouterManager.PAGE_FROM_DRAGON_FLY
                }
                ACTION_THIRDAPP_START_PRIVA -> mStartType = PAGE_FROM_THIRD_APP_RECORD
                ACTION_MINI_APP_CONTINUE -> mStartType = START_TYPE_FROM_MINI_APP_CONTINUE
                ACTION_SEEDLING_CARD -> mStartType = ACTION_SEEDLING_CARD
                ACTION_SMALL_SEEDLING_CARD -> mStartType = ACTION_SMALL_SEEDLING_CARD
                ACTION_CUBE_BUTTON -> mStartType = RecordRouterManager.PAGE_FROM_CUBE_BUTTON
                ACTION_LOCK_SCREEN -> mStartType = RecordRouterManager.PAGE_FROM_LOCK_SCREEN
            }
        } catch (e: Exception) {
            DebugUtil.w(TAG, "parseDataFromIntent error: $e")
        }
    }

    override fun onPrivacyPolicySuccess(type: Int, pageFrom: Int?) {
        super.onPrivacyPolicySuccess(type, pageFrom)
        when (mStartType) {
            RecordRouterManager.PAGE_FROM_SUMMARY_CARD -> {
                if (PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_FROM_CARD == type) {
                    // 返回获取权限
                    handleBreenoCardResult(true)
                }
            }
        }
    }

    override fun onPrivacyPolicyFail(type: Int, pageFrom: Int?) {
        super.onPrivacyPolicyFail(type, pageFrom)
        /*不同意转文本功能须知*/
        when (mStartType) {
            RecordRouterManager.PAGE_FROM_SUMMARY_CARD -> {
                if (PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_FROM_CARD == type) {
                    // 返回获取权限
                    handleBreenoCardResult(false)
                }
            }
        }
    }

    /**
     * 校验权限前先拦截一部分逻辑
     */
    private fun dealIntentBeforePermission(isRecreate: Boolean): Boolean {
        DebugUtil.d(TAG, "dealIntentBeforePermission, startType:$mStartType")
        when (mStartType) {
            RecordRouterManager.PAGE_FROM_BRACKET_SPACE -> {
                handleStartFromBracketSpace()
                return true
            }
            RecordRouterManager.PAGE_FROM_SMALL_CARD -> {
                updateLaunchTime(isRecreate)
                if (handleStartFromSmallCardBefore()) {
                    return true
                }
            }
            RecordRouterManager.PAGE_FROM_DRAGON_FLY -> {
                updateLaunchTime(isRecreate)
                StartActivityUtils.startRecordActivity(this, null, false, false, false)
                finishPage(false)
                return true
            }
            RecordRouterManager.PAGE_FROM_SLIDE_BAR -> {
                updateLaunchTime(isRecreate)
                if (handleStartFromSlideBarBefore()) {
                    return true
                }
            }
            PAGE_FROM_THIRD_APP_RECORD -> {
                settingApi?.launchRecordPrivacy(this, R.string.privacy_policy_settings_policy_key)
                finishPage(false)
                return true
            }
            RecordRouterManager.PAGE_FROM_BREENO -> {
                updateLaunchTime(isRecreate)
                // 统一在权限之后校验
            }
            START_TYPE_FROM_MINI_APP_CONTINUE -> {
                /**火烈鸟默认页面展开手机接续到内屏
                 *  1.内屏页面再快捷播放、播放、裁切页面： 把录音拉到前台
                 *  2.否则，到录音首页列表
                 *  */
                if (canJumpToBrowseFileFromMiniApp()) {
                    // 不满足上述场景，默认到首页
                    browseFileApi?.createBrowseFileIntent(this)?.run {
                        addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                        startActivity(this)
                    }
                }
                finishPage()
                return true
            }
            ACTION_SEEDLING_CARD -> {
                updateLaunchTime(isRecreate)
                startRecordFromSeedlingCard()
                return true
            }
            ACTION_SMALL_SEEDLING_CARD -> {
                updateLaunchTime(isRecreate)
                startRecordFromSmallSeedlingCard()
                return true
            }
            RecordRouterManager.PAGE_FROM_CUBE_BUTTON,
            RecordRouterManager.PAGE_FROM_LOCK_SCREEN -> {
                /*魔方/锁屏，目前调用相同逻辑*/
                lightUpScreen()
                updateLaunchTime(isRecreate)
                if (handleFromCubeOrLockScreenBefore()) {
                    return true
                }
            }
            else -> {
                updateLaunchTime(isRecreate)
            }
        }

        val recordClass: Class<*>? = recordApi?.getRecorderActivityClass()
        if (recordClass != null && this.any(recordClass as Class<out Activity>)) {
            finishPage()
            return true
        }
        return false
    }

    private fun canJumpToBrowseFileFromMiniApp(): Boolean {
        /**火烈鸟默认页面展开手机接续到内屏
         *  1.内屏页面再快捷播放、播放、裁切页面： 把录音拉到前台
         *  2.否则，到录音首页列表
         *  */
        if (!ActivityTaskUtils.isTaskEmptyExceptActivity(taskId, this)) {
            val editActivity = ActivityTaskUtils.getActivity(taskId, editRecordApi?.getEditRecordActivityClass())
            if (editActivity != null) { // 内屏在裁切页面： 将录音拉到前台
                return false
            }
            val browseFile = ActivityTaskUtils.getActivity(taskId, browseFileApi?.getBrowseFileClass())
            // 内屏录音在播放、快捷播放页面，则将录音拉到前台即可
            return browseFileApi?.hasPlayBackRecord(browseFile) == false && browseFileApi?.hasFastPlayingFile(browseFile) == false
        }
        return true
    }

    private fun dealIntentAfterPermission() {
        DebugUtil.i(TAG, "dealIntentAfterPermission mStartType: $mStartType")

        when (mStartType) {
            RecordRouterManager.PAGE_FROM_BREENO -> {
                handleStartFromBreenoAfter()
            }
            RecordRouterManager.PAGE_FROM_SLIDE_BAR -> {
                handleStartFromSlideBarAfter()
            }
            RecordRouterManager.PAGE_FROM_SMALL_CARD -> {
                //从负一屏卡片启动后台录音
                handleStartFromSmallCardAfter()
            }
            RecordRouterManager.PAGE_FROM_CUBE_BUTTON,
            RecordRouterManager.PAGE_FROM_LOCK_SCREEN -> {
                //从魔方启动录音/从锁屏启动录音
                handleStartFromCubeOrLockScreenAfter()
            }

            RecordRouterManager.PAGE_FROM_SUMMARY_CARD -> {
                // 从摘要卡进入
                handleStartFromSummaryAfter()
            }
        }
    }

    private fun updateLaunchTime(isRecreate: Boolean) {
        if (isRecreate) {
            return
        }

        doInIOThread<Any?>({
            BracketSpaceProviderAgent.updateAppLaunchTime(BaseApplication.getAppContext())
            null
        }, this.lifecycleScope)
    }

    /**
     * 悬停空间进入
     * 1. 主进程再前台有页面，拉起应用到前台，当前页面finish()
     * 2. 主进程在前台无页面（侧边栏、小步），跳转到录制页面，当前页面finish()
     * 3. 否则，就跳转到录制页面（若通话录音再录制页面录制，那么就跳转首页），当前页面finish()
     */
    private fun handleStartFromBracketSpace() {
        if (checkJustPullStack()) {
            //do nothing
        } else if (recorderViewModelApi?.isAlreadyRecording() == true) {
            when {
                RecordRouterManager.instance?.interceptStartRecord(RecorderDataConstant.PAGE_FROM_BRACKET_SPACE) == true -> {
                    startActivity(Intent(this, BrowseFile::class.java))
                    finishPage(false)
                }
                else -> {
                    DebugUtil.i(TAG, "main process is recording only by background service")
                    StartActivityUtils.startRecordActivity(this, null, false, false, false)
                    finishPage(false)
                }
            }
        } else {
            val model = StartRecordModel().also {
                it.isFromCall = false
                it.noEnterAnimation = true
            }
            StartActivityUtils.startRecordActivity(this, model, false, false, true)
            finishPage(false)
        }
    }

    private fun handleStartFromBreenoAfter() {
        //从小布助手启动，处理流程，检查是否满足开启录音服务的的前提条件
        if (BreenoStartRecordUtil.doCheckExceptionWhenStartRecordFromBreeno()) {
            val mainTaskId = ActivityTaskUtils.getMainTaskId()
            when {
                ActivityTaskUtils.isTaskEmpty(mainTaskId) -> {
                    //本地栈中没有录音的Activity
                    DebugUtil.i(TAG, "startRecordFromBreeno")
                    recorderViewModelApi?.startRecorderService {
                        putExtra(RecorderDataConstant.PAGE_FROM_NAME, RecorderDataConstant.PAGE_FROM_BREENO)
                        putExtra(RecorderDataConstant.SERVICE_IS_FROM_OTHER_APP, false)
                        putExtra(
                            RecorderDataConstant.SERVICE_MAX_FILE_SIZE,
                            SoundRecordBinder.RECORD_SIZE_LIMIT
                        )
                        putExtra(RecorderDataConstant.SERVICE_MAX_DURATION, 0L)
                        putExtra(RecorderDataConstant.SERVICE_NEED_RESULT, false)
                        putExtra(RecorderDataConstant.SERVICE_NEED_SHOW_SNACKBAR, needShowSnackBar)
                    }
                    DebugUtil.i(TAG, " startAndBindRecorderService startForeground Service")
                    finishPage()
                }
                ActivityTaskUtils.isFirstTaskBrowseFileActivity(mainTaskId, BrowseFile::class.java.name)
                        || ActivityTaskUtils.isSettingOrSecondaryActivity(mainTaskId) -> {
                    //本地录音栈中存在Activity
                    //不管录音是否在前台，都跳转录制界面
                    recordApi?.sendBrowseFrontToRecordBroadCast()
                    finishPage()
                }
                else -> {
                    //本地栈中存在其他页面，告诉小布，不能开始录音
                    DebugUtil.d(TAG, "sendStartOtherPageFailedBroadCast")
                    recordApi?.sendStartOtherPageFailedBroadCast()
                    finishPage()
                }
            }
        } else {
            DebugUtil.d(TAG, "handleStartFromBreenoAfter, just finish")
            finishPage()
        }
    }

    private fun lightUpScreen() {
        DebugUtil.d(TAG, "lightUpScreen")
        mWakeLock = BrightScreenUtil.acquireWakeLock(this, KeyMagicMessengerService.TAG)
    }

    private fun releaseWakeLock() {
        if (mWakeLock != null) {
            BrightScreenUtil.releaseWakeLock(mWakeLock)
            mWakeLock = null
        }
    }

    /**
     * 魔方/锁屏
     * 仅拉起录音
     * 从魔方来
     * 1.应用外：长按开始录音，流体云反馈，录音中再次长按，结束录音，并自动保存，出现流体云提示
     * 2.应用内：长按跳转录制页开始录音，录音中再次长按，模拟右上角「保存」按钮，停留至保存页面
     */
    private fun handleFromCubeOrLockScreenBefore(): Boolean {
        DebugUtil.d(TAG, "handleFromCubeOrLockScreenBefore")
        if (checkJustPullStack(false)) {
            if (ActivityTaskUtils.isForeground()) {
                //有进程在，并且在前台
                if (!ActivityTaskUtils.isFirstTaskRecorderActivity(taskId, recordApi?.getRecorderActivityClass()?.name)) {
                    val browseFile = ActivityTaskUtils.getActivity(taskId, browseFileApi?.getBrowseFileClass())
                    val hasFastPlaying = browseFileApi?.hasFastPlayingFile(browseFile) ?: false
                    val hasPlayBackRecord = browseFileApi?.hasPlayBackRecord(browseFile) ?: false
                    //录音正在播放、快捷播放时，需停止当前播放
                    if (hasFastPlaying || hasPlayBackRecord) {
                        val intent = Intent(RecordFileChangeNotify.CUBE_CLEAR_PLAY_RECORD_DATA)
                        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
                    }
                    val isFirstBrowseFile = ActivityTaskUtils.isFirstTaskBrowseFileActivity(taskId, BrowseFile::class.java.name)
                    if (!isFirstBrowseFile) {
                        ActivityTaskUtils.backToBrowse(taskId)
                    }
                    val model = StartRecordModel()
                    DebugUtil.d(TAG, "handleFromCubeOrLockScreenBefore, startRecordActivity")
                    StartActivityUtils.startRecordActivity(this, model,
                        brenoFront = false,
                        true,
                        checkIsCall = false
                    )
                }
                if (recorderViewModelApi?.isAlreadyRecording() == true) {
                    val intent = Intent(RecorderDataConstant.ACTION_RECORDER_STOP_AUTO_SAVE)
                    BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
                }
                releaseWakeLock()
                finishPage(false)
                return true
            } else {
                return saveRecordInfo(true)
            }
        } else {
            return saveRecordInfo()
        }
    }

    private fun saveRecordInfo(isMoveRecord: Boolean = false): Boolean {
        releaseWakeLock()
        if (recorderViewModelApi?.hasInitRecorderService() == true && recorderViewModelApi?.isAlreadyRecording() == true) {
            if (isMoveRecord) {
                moveRecorderActivityToBack()
            }
            DebugUtil.d(TAG, "handleStartFromCubeButtonBefore, saveRecordInfo")
            val saveRecordFromWhere = if (mStartType == RecordRouterManager.PAGE_FROM_CUBE_BUTTON) {
                RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_CUBE_BUTTON
            } else {
                RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_LOCK_SCREEN
            }
            recorderViewModelApi?.saveRecordInfo(saveRecordFromWhere = RecorderDataConstant.MSG_ARG2_SAVE_RECORD_FROM_CUBE_BUTTON)
            finishPage()
            return true
        }
        return false
    }

    /**
     * 保存时先把RecorderActivity置于后台
     * 录制页面在后台时，拉起TransparentActivity一定会把RecorderActivity拉起来
     */
    private fun moveRecorderActivityToBack() {
//        val mainTaskId = ActivityTaskUtils.getMainTaskId()
        val isTopRecorder = ActivityTaskUtils.isFirstTaskRecorderActivity(taskId, recordApi?.getRecorderActivityClass()?.name)
        if (isTopRecorder) {
            val recordActivity =
                ActivityTaskUtils.getActivity(taskId, recordApi?.getRecorderActivityClass())
            recordActivity?.moveTaskToBack(true)
        }
    }

    /**
     * 仅仅拉起录音
     * 1、如果后台为空，则跳首页；
     * 2、若不为空，则直接拉起然后finish
     */
    private fun handleStartFromSmallCardBefore(): Boolean {
        try {
            if (intent.getStringExtra(SMALL_CARD_METHOD) == METHOD_JUST_START) {
                //
                if (!checkJustPullStack()) {
                    val intent = Intent(this, BrowseFile::class.java)
                    intent.putExtra(RecordRouterManager.PAGE_FROM_NAME, RecordRouterManager.PAGE_FROM_SMALL_CARD)
                    startActivity(intent)
                    finishPage(false)
                    DebugUtil.d(TAG, "justLaunchFromSmallCard")
                }
                return true
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "onCreate 获取method失败" + e.message)
        }
        return false
    }

    /**
     * 1.处于应用内，长按跳转到录制页开始录音，再次长按跳转到录制页面并显示保存弹窗
     * 2.不在应用内，长按开始后台录音，再次长按保存录音并显示流体云
     */
    private fun handleStartFromCubeOrLockScreenAfter() {
        if (handleFromCubeOrLockScreenBefore()) {
            return
        }
        DebugUtil.i(TAG, "handleStartFromCubeOrLockScreenAfter")
        if (checkCanStartService()) {
            ActivityTaskUtils.clearAllTask()
            var pageFrom: String = RecorderDataConstant.PAGE_FROM_CUBE_BUTTON
            if (mStartType == RecordRouterManager.PAGE_FROM_CUBE_BUTTON) {
                pageFrom = RecorderDataConstant.PAGE_FROM_CUBE_BUTTON
                addRecordStartType(RecorderUserAction.VALUE_START_RECORD_FROM_CUBE_BUTTON)
                /*入口来源：魔方卡片*/
                RecorderUserActionKt.sValueEntryFrom = RecorderUserAction.VALUE_ENTRY_FROM_CUBE_BUTTON
            } else {
                pageFrom = RecorderDataConstant.PAGE_FROM_LOCK_SCREEN
            }
            DebugUtil.i(TAG, "handleStartFromCubeOrLockScreenAfter, startRecorderService")
            recorderViewModelApi?.startRecorderService {
                putExtra(RecorderDataConstant.PAGE_FROM_NAME, pageFrom)
                putExtra(RecorderDataConstant.SERVICE_AUTO_START_RECORD, true)
                putExtra(RecorderDataConstant.SERVICE_NEED_SHOW_SNACKBAR, needShowSnackBar)
                putExtra(RecorderDataConstant.SERVICE_IS_FROM_OTHER_APP, false)
                putExtra(
                    RecorderDataConstant.SERVICE_MAX_FILE_SIZE,
                    SoundRecordBinder.RECORD_SIZE_LIMIT
                )
            }
        }
        finishPage()
    }

    /**
     * 仅仅拉起录音
     * 1、如果后台为空，则跳首页；
     * 2、若不为空，则直接拉起然后finish
     */
    private fun handleStartFromSmallCardAfter() {
        DebugUtil.i(TAG, "startRecordFromSmallCard")
        val method = try {
            intent.getStringExtra(SMALL_CARD_METHOD)
        } catch (e: Resources.NotFoundException) {
            DebugUtil.e(TAG, "startRecordFromSmallCard 获取SMALL_CARD_METHOD失败，" + e.message)
            null
        }

        when (method) {
            METHOD_START_SERVICE -> {
                //已经开始录制
                if (recorderViewModelApi?.isAlreadyRecording() == true) {
                    DebugUtil.i(TAG, "其他应用正在录制中，不能启动service")
                    ToastManager.showShortToast(applicationContext, R.string.record_conflict)
                    finishPage()
                } else {
                    //没有开始录制，则开始后台录制
                    if (checkCanStartService()) {
                        if ("true" == intent?.extras?.getString("is_from_seeding_small_card")) {
                            val jsonData = JSONObject()
                            JsonUtils.updateJsonDataForRecordBtn(jsonData)
                            seedlingApi?.forceRefreshSeedlingData(jsonData)
                            RecordSeedlingSmallCardWidgetProvider.animationInProgress = false
                        }
                        BuryingPoint.addClickSmallCardToRecord()
                        recorderViewModelApi?.startRecorderService {
                            putExtra(RecorderDataConstant.PAGE_FROM_NAME, RecorderDataConstant.PAGE_FROM_SMALL_CARD)
                            putExtra(RecorderDataConstant.SERVICE_AUTO_START_RECORD, true)
                            putExtra(RecorderDataConstant.SERVICE_NEED_SHOW_SNACKBAR, needShowSnackBar)
                            // 添加文件大小限制
                            val maxSize = intent.getLongExtra(RecorderDataConstant.MAX_SIZE, 0)
                            if (maxSize > 0) {
                                intent.putExtra(RecorderDataConstant.SERVICE_MAX_FILE_SIZE, maxSize)
                            } else {
                                intent.putExtra(
                                    RecorderDataConstant.SERVICE_MAX_FILE_SIZE,
                                    RecorderDataConstant.SERVICE_MAX_FILE_LIMIT_BYTES
                                )
                            }
                            // 添加录音时长限制
                            val maxDuration = intent.getIntExtra(Audio.Media.DURATION, 0).toLong()
                            if (maxDuration > 0) {
                                intent.putExtra(
                                    RecorderDataConstant.SERVICE_MAX_DURATION,
                                    maxDuration
                                )
                            }
                        }
                    }
                    finishPage()
                }
            }
        }
    }

    /**
     * 权限请求前处理流体云卡片的跳转
     * 1、应用正在录制中且录制页存在，则直接拉起然后finish；
     * 2、应用正在录制中且录制页不存在拉起录制页
     * 3、应用未在录制状态进入首页
     */
    private fun startRecordFromSeedlingCard() {
        if (recorderViewModelApi?.isAlreadyRecording() == true) {
            DebugUtil.i(TAG, "startRecordFromSeedlingCard isAlreadyRecording == true.")
            StartActivityUtils.startRecordActivity(this, null, false, false, false, true)
        } else {
            val fileName = intent.getStringExtraSecure(RecorderDataConstant.SHOULD_AUTO_FIND_FILE_NAME)
            val intent = Intent(this, BrowseFile::class.java)
            intent.putExtra(RecordRouterManager.PAGE_FROM_NAME, RecordRouterManager.PAGE_FROM_SEEDLING_CARD)
            intent.putExtra(RecorderDataConstant.SHOULD_AUTO_FIND_FILE_NAME, fileName)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            startActivity(intent)
            recorderViewModelApi?.fluidCardDismiss(FROM_SEEDLING_CARD_CLICK)
            DebugUtil.w(TAG, "startRecordFromSeedlingCard justLaunchFromSeedlingCard")
        }
        finishPage(false)
    }

    private fun startRecordFromSmallSeedlingCard() {
        DebugUtil.i(TAG, "startRecordFromSeedlingCard")
        if (recorderViewModelApi?.isAlreadyRecording() == true) {
            StartActivityUtils.startRecordActivity(this, null, false, false, false)
        } else {
            val fileName = intent.getStringExtraSecure(RecorderDataConstant.SHOULD_AUTO_FIND_FILE_NAME)
            val intent = Intent(this, BrowseFile::class.java)
            intent.putExtra(RecordRouterManager.PAGE_FROM_NAME, RecordRouterManager.PAGE_FROM_SEEDLING_CARD)
            intent.putExtra(RecorderDataConstant.SHOULD_AUTO_FIND_FILE_NAME, fileName)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            startActivity(intent)
            recorderViewModelApi?.fluidCardDismiss(FROM_SEEDLING_CARD_CLICK)
            DebugUtil.w(TAG, "startRecordFromSeedlingCard justLaunchFromSeedlingCard")
        }
        finishPage(false)
    }

    private fun handleStartFromSummaryAfter() {
        val type = intent.getIntValue("type", -1)
        DebugUtil.i(TAG, "handleStartFromSummaryAfter, type=$type")
        /**
         * 只有转文本才需要check是否同意功能相关隐私政策
         */
        if (type == AppCardEventProcessor.ON_CLICK_START_TEXT && !PermissionUtils.isStatementConvertGranted(BaseApplication.getAppContext())) {
            lifecycleScope.launchWhenResumed {
                /*
                    此方法必须在onResume中执行，onRestoreInstanceState重建机制在onResume之前执行
                 */
                if (!PermissionUtils.isStatementConvertGranted(BaseApplication.getAppContext())) {
                    getPrivacyPolicyDelegate()?.resumeShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_FROM_CARD, true)
                }
            }
        } else {
            // 返回获取权限
            handleBreenoCardResult(true, type)
        }
    }

    private fun handleBreenoCardResult(agreePermission: Boolean, type: Int? = intent.getIntValue("type", -1)) {
        var granted = agreePermission
        if (needAudioPermissionFromBreenoCard(type)) {
            granted = agreePermission && PermissionUtils.hasReadAudioPermission()
        }
        AppCardStateProcessor.onRequestPermissionCallBack(granted, type)
        /*需要启动页面场景，不能remove - Task，会导致activity启不来*/
        val immediately = !(granted && type == AppCardEventProcessor.ON_CLICK_START_TEXT || type == AppCardEventProcessor.ON_CLICK_START_SUMMARY)
        finishPage(immediately)
    }

    /**
     * 权限请求前处理侧边栏启动的跳转
     * 1、如果后台不为空，则直接拉起然后finish；
     * 2、如果录制中，1）侧边栏启动的录制，则finish
     */
    private fun handleStartFromSlideBarBefore(): Boolean {
        if (checkJustPullStack()) {
            return true
        }

        when {
            recorderViewModelApi?.isFromSlidBar() == true -> {
                finishPage()
                return true
            }

            recorderViewModelApi?.isFromSmallCard() == true
                    || recorderViewModelApi?.isFromAppCard() == true
                    || recorderViewModelApi?.isFromMiniApp() == true -> {
                //如果是桌面卡片开启的录制，则拉起到前台
                StartActivityUtils.startRecordActivity(this, null, false, false, false)
                finishPage(false)
                return true
            }
            //todo
        }
        return false
    }

    /**
     * 权限请求后处理侧边栏启动： 开始录音
     */
    private fun handleStartFromSlideBarAfter() {
        if (handleStartFromSlideBarBefore()) {
            return
        }

        //后台没有界面，并且没有从侧边栏启动过录音服务，侧边栏后台录音 startService
        DebugUtil.i(TAG, "startRecordFromSlidBar")
        val canStartService = checkCanStartService()
        if (canStartService) {
            addRecordStartType(RecorderUserAction.VALUE_START_RECORD_FROM_SIDEBAR)
            /*入口来源：侧边栏*/
            RecorderUserActionKt.sValueEntryFrom = RecorderUserAction.VALUE_ENTRY_FROM_SIDEBAR
            recorderViewModelApi?.startRecorderService {
                putExtra(RecorderDataConstant.PAGE_FROM_NAME, RecorderDataConstant.PAGE_FROM_SLIDE_BAR)
                putExtra(RecorderDataConstant.SERVICE_NEED_SHOW_SNACKBAR, needShowSnackBar)
            }
        }

        finishPage()
    }

    private fun checkCanStartService(): Boolean {
        var isStorageEnough = false
        try {
            isStorageEnough = StorageManager.getInstance(applicationContext).checkStorageSpaceForRecordFile(false)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "checkCanStartService checkStorageSpaceForRecordFile error", e)
        }
        if (!isStorageEnough) {
            ToastManager.showShortToast(applicationContext, R.string.record_error_disk_full)
            return false
        } else if (recorderViewModelApi?.isAlreadyRecording() == true) {
            // check other app is already start this RecorderService
            ToastManager.showShortToast(applicationContext, R.string.record_conflict)
            return false
        } else if (false == recorderViewModelApi?.checkModeCanRecord(true)) {
            return false
        }
        return true
    }

    /**
     * 有进程在，直接拉起进程，不做任何跳转
     */
    private fun checkJustPullStack(finishPage: Boolean = true): Boolean {
        if (!ActivityTaskUtils.isTaskEmptyExceptActivity(taskId, this)) {
            DebugUtil.i(TAG, "main process have activity in background")
            if (finishPage) {
                finishPage()
            }
            return true
        }

        return false
    }

    private fun finishPage(immediately: Boolean = true) {
        if (immediately) {
            finishAndRemoveTask()
        } else {
            finish()
        }
    }

    override fun doFinishActivityWhenRefusePermission() {
        // 拒绝权限，销毁页面同时销毁栈
        finishPage()
    }

    override fun onBackPress(alertType: Int) {
        finishPage()
    }

    private fun initiateWindowInsets() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        val contentView = findViewById<FrameLayout>(android.R.id.content)
        ViewCompat.setOnApplyWindowInsetsListener(contentView) { _: View?, _: WindowInsetsCompat? ->
            contentView.setPadding(0, 0, 0, 0)
            WindowInsetsCompat.CONSUMED
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        releaseWakeLock()
    }

    override fun onNewIntent(intent: Intent?) {
        if ("true" == intent?.extras?.getString("is_from_seeding_small_card")) {
            //如果入口是1*2录音卡,onResume需要重新申请系统权限
            requestingPermission = false
        }

        super.onNewIntent(intent)
    }

    override fun configPermission(): Array<String> {
        return when (mStartType) {
            /*从小步建议过来，目前只有：查看文本摘要、转文本、摘要 过来，只需要文件读取权限即可*/
            RecordRouterManager.PAGE_FROM_SUMMARY_CARD -> {
                val type = intent.getIntValue("type", -1)
                if (needAudioPermissionFromBreenoCard(type) && !PermissionUtils.hasReadAudioPermission()) {
                    val permissionList = ArrayList<String>()
                    if (BaseUtil.isAndroidROrLater) {
                        permissionList.add(PermissionUtils.READ_AUDIO_PERMISSION())
                    } else {
                        permissionList.addAll(PermissionUtils.STORAGE_PERMISSIONS_Q)
                    }
                    permissionList.toTypedArray()
                } else {
                    emptyArray()
                }
            }

            else -> super.configPermission()
        }
    }

    private fun needAudioPermissionFromBreenoCard(type: Int?): Boolean {
        return type == AppCardEventProcessor.ON_CLICK_START_TEXT || type == AppCardEventProcessor.ON_CLICK_START_SUMMARY
    }
}