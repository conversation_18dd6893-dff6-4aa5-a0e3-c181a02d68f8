/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  RecorderApplication.kt
 * * Description : RecorderApplication
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        convert to kt
 ***********************************************************************/
package oplus.multimedia.soundrecorder

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.util.Log
import com.coloros.soundrecorder.BuildConfig
import com.oplus.recorderlog.util.CommonFlavor
import com.oplus.statistics.OTrackConfig
import com.oplus.statistics.OplusTrack
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.home.load.LoadAmplitudeHelper
import com.soundrecorder.browsefile.search.load.center.filechange.CenterFileChangeObserver
import com.soundrecorder.common.base.CommonApplication
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.fileobserve.MultiFileObserver
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.common.utils.DisplayUtils
import com.soundrecorder.common.utils.DisplayUtils.isDefaultDisplay
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.notification.NotificationInterface
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.player.speaker.SpeakerReceiver
import com.soundrecorder.record.picturemark.PopTimeSliceManager
import com.soundrecorder.wavemark.mark.ClearDataUtils
import com.soundrecorder.wavemark.wave.WaveViewUtil
import oplus.multimedia.soundrecorder.slidebar.TransparentActivity
import oplus.multimedia.soundrecorder.utils.KoinRegister
import oplus.multimedia.soundrecorder.utils.RecorderUtil
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.GlobalContext.startKoin
import org.koin.core.logger.Level
import org.koin.dsl.module
import java.io.File
import java.io.FileFilter

class RecorderApplication : CommonApplication() {
    private var densityDpi = 0

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val cloudTipManagerAction by lazy {
        Injector.injectFactory<CloudTipManagerAction>()
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    private val notificationApi by lazy {
        Injector.injectFactory<NotificationInterface>()
    }

    private val playbackApi by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    private val smartNameAction by lazy {
        Injector.injectFactory<SmartNameAction>()
    }

    override fun attachBaseContext(base: Context) {
        inject()
        super.attachBaseContext(base)
        CommonFlavor.getInstance().init(
            BuildConfig.DEBUG,
            BuildConfig.FLAVOR,
            BuildConfig.FLAVOR_B,
            BuildConfig.FLAVOR_P,
            BuildConfig.FLAVOR_region,
            BuildConfig.FLAVOR_apilevel
        )
        moveDataToCredentialStorage(base)
    }

    private fun inject() {
        startKoin {
            androidLogger(Level.INFO)
            androidContext(this@RecorderApplication)
            val context = module {
                single<Context> { this@RecorderApplication }
            }
            modules(context)
            modules(KoinRegister.getKoinModules())
        }
    }

    protected override fun onCreateInit() {
        super.onCreateInit()
        DebugUtil.i(TAG, "RecorderApplication onCreate, current version sdk int:" + Build.VERSION.SDK_INT)
        OplusTrack.init(getApplicationContext(), OTrackConfig.Builder().build())
        val eventInfo = HashMap<String, String>()
        eventInfo[RecorderUserAction.KEY_LAUNCH_RECORDER_APP] = RecorderUserAction.DEFAULT_VALUE
        RecorderUserAction.addCommonUserAction(
            this, RecorderUserAction.USER_ACTION_MAIN_VIEW_TAG,
            RecorderUserAction.EVENT_LAUNCH_RECORDER_APP, eventInfo, false
        )


        //#endif /* COLOROS_EDIT */
        RecorderUtil.enableBackgroundService(this)
        RecorderUtil.deleteLogs()

        ClearDataUtils.clearPictureMark()
        PopTimeSliceManager.addRecorderListener()
        densityDpi = getResources().getDisplayMetrics().densityDpi
    }

    //#ifdef COLOROS_EDIT
    //<EMAIL>.1376279, 2018/6/25, Add for demand 5.2
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        DebugUtil.i(TAG, "onApplicationConfigureChange  densityDpi = " + densityDpi + " , newConfig.densityDpi = " + newConfig.densityDpi)
        if (densityDpi != newConfig.densityDpi && DisplayUtils.currentDisplay().isDefaultDisplay()) {
            densityDpi = newConfig.densityDpi
            WaveViewUtil.clearAll()
        }

        Log.d(TAG, "onConfigurationChanged sLaunchedByProvider = $sLaunchedByProvider")
        if (sLaunchedByProvider.not()) {
            summaryApi?.loadSupportSummaryByCountry(newConfig.locale.country)
        }
    }

    //#endif /* COLOROS_EDIT */
    override fun onDestroyedRelease(activity: Activity) {
        if (activity.javaClass.name.equals(cloudTipManagerAction?.getCloudPermissionActivityName(), ignoreCase = true)) {
            DebugUtil.e(TAG, "onDestroyedRelease activity is CloudPermissionActivity")
            return
        }
        if (activity.javaClass.name.equals(cloudTipManagerAction?.getRecordCloudSettingActivityName(), ignoreCase = true)) {
            DebugUtil.e(TAG, "onDestroyedRelease activity is SettingRecordSyncActivity")
            return
        }
        if (activity.javaClass.name.equals(TransparentActivity::class.java.getName(), ignoreCase = true)) {
            DebugUtil.d(TAG, "onDestroyedRelease activity is TransparentActivity")
            return
        }
        val isTaskEmpty: Boolean = ActivityTaskUtils.isAllTaskEmpty()
        DebugUtil.i(TAG, "onActivityDestroyed isTaskEmpty is $isTaskEmpty")
        if (isTaskEmpty) {
            if (false == recorderViewModelApi?.hasInitRecorderService()) {
                //侧边栏启动时没有task栈没有activity,此时取消notification有问题
                notificationApi?.cancelAllNotification()
            }
            //退出应用时
            SpeakerReceiver.release()
            MultiFileObserver.getInstance().stopWatching(true)
            CenterFileChangeObserver.release()
            cloudKitApi?.release()
            playbackApi?.stopConvertService(this)
            smartNameAction?.releaseAllTask()
            LoadAmplitudeHelper.clearAllAmplitudes()
            //退出界面flush日志到xlog中,有读权限+已同意用户须知
            if (PermissionUtils.hasReadAudioPermission(this) && (PermissionUtils.getNextAction() != PermissionUtils.SHOULD_SHOW_USER_NOTICE)) {
                DebugUtil.aSyncPrintDbAndFlushLog()
                //RecorderLogger.INSTANCE.flushLog(false);
            }
        }
    }

    private fun moveDataToCredentialStorage(context: Context) {
        kotlin.runCatching {
            val deviceProtectedStorageContext = context.createDeviceProtectedStorageContext()
            if (deviceProtectedStorageContext != null) {

                val oldPath = deviceProtectedStorageContext.dataDir.absolutePath
                val oldDirectory = File(oldPath)
                val newPath = context.dataDir.absolutePath + "/databases/recorder.db"
                val newFile = File(newPath)

                val oldDbDirectory = File("$oldPath/databases")
                val oldDbFiles = oldDbDirectory.listFiles(FileFilter { file: File -> file.name.endsWith(".db") })
                var oldDbFilesWithoutExtension: List<String?>? = null
                if (oldDirectory.exists() && oldDirectory.isDirectory) {
                    oldDbFilesWithoutExtension = getFileNamesWithoutExtension(oldDbFiles, DB_EXTENSION_LENGTH)
                } else {
                    DebugUtil.d(TAG, "moveDataToCredentialStorage(): oldDirectory does not exist or is not a directory")
                }

                val oldSPDirectory = File("$oldPath/shared_prefs")
                val oldSPFiles = oldSPDirectory.listFiles(FileFilter { file: File -> file.name.endsWith(".xml") })
                var oldSPFilesWithoutExtension: List<String?>? = null
                if (oldSPDirectory.exists() && oldSPDirectory.isDirectory) {
                    oldSPFilesWithoutExtension = getFileNamesWithoutExtension(oldSPFiles, SP_EXTENSION_LENGTH)
                } else {
                    DebugUtil.d(TAG, "moveDataToCredentialStorage(): oldSPDirectory does not exist or is not a directory")
                }

                if (oldDirectory.exists() && !newFile.exists()) {
                    if (oldDbFilesWithoutExtension != null && !oldDbFilesWithoutExtension.isEmpty()) {
                        for (dbFile in oldDbFilesWithoutExtension) {
                            context.moveDatabaseFrom(deviceProtectedStorageContext, dbFile)
                        }
                    }
                    if (oldSPFilesWithoutExtension != null && !oldSPFilesWithoutExtension.isEmpty()) {
                        for (spFile in oldSPFilesWithoutExtension) {
                            context.moveSharedPreferencesFrom(deviceProtectedStorageContext, spFile)
                        }
                    }
                }
            } else {
                DebugUtil.d(TAG, "moveDataToCredentialStorage(): deviceProtectedStorageContext is null")
            }
        }.onFailure {
            DebugUtil.e(TAG, "moveDataToCredentialStorage() err :$it")
        }
    }

    fun getFileNamesWithoutExtension(files: Array<File>?, spiltIndex: Int): List<String?> {
        val fileNamesWithoutExtension: MutableList<String?> = ArrayList()
        if (files != null) {
            for (file in files) {
                val fileNameWithoutExtension = file.name.substring(0, file.name.length - spiltIndex)
                fileNamesWithoutExtension.add(fileNameWithoutExtension)
            }
        }
        return fileNamesWithoutExtension
    }

    companion object {
        const val TAG: String = "RecorderApplication"
        private const val DB_EXTENSION_LENGTH = 3
        private const val SP_EXTENSION_LENGTH = 4
    }
}