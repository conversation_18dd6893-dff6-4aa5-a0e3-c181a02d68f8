/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ShadowAppFeatureUtil
 Description:
 Version: 1.0
 Date: 2023/03/13
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 22023/03/13 1.0 create
 */

package oplus.multimedia.soundrecorder.shadows;

import android.content.Context;

import com.coui.appcompat.dialog.AppFeatureUtil;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(AppFeatureUtil.class)
public class ShadowAppFeatureUtil {

    @Implementation
    public static boolean isFoldDisplayFeature(Context context) {
        return false;
    }
}
