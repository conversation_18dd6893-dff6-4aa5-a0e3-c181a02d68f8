/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SeedlingSdkApiTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.api

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.pantanal.seedling.bean.SeedlingCard
import com.soundrecorder.common.card.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.*
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SeedlingSdkApiTest {
    private var mContext: Context? = null
    private var mockedStatic: MockedStatic<SeedlingSdkApi>? = null


    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        try {
            mockedStatic = Mockito.mockStatic(SeedlingSdkApi::class.java)
        } catch (e: Exception) {
            //do nothing
        }
    }

    @After
    fun tearDown() {
        mockedStatic?.close()
        mContext = null
    }
    @Test
    fun should_invoke_when_isSupportFluidCloud() {
        Mockito.`when`(SeedlingSdkApi.isSupportFluidCloud(any(Context::class.java)))
           .thenReturn(true, false, false)
    }

    @Test
    fun should_invoke_when_isSupportSystemSendIntent() {
        Mockito.`when`(SeedlingSdkApi.isSupportSystemSendIntent(any(Context::class.java)))
           .thenReturn(true, false)
    }

    @Test
    fun should_invoke_when_showSeedlingStatusBar() {
        SeedlingSdkApi.showSeedlingStatusBar()

        mockedStatic?.verify({
            SeedlingSdkApi.showSeedlingStatusBar()
        }, Mockito.times(1))
    }

    @Test
    fun should_invoke_when_unRegisterResultCallBack() {
        SeedlingSdkApi.unRegisterResultCallBack()
        mockedStatic?.verify({
            SeedlingSdkApi.unRegisterResultCallBack()
        }, Mockito.times(1))
    }

    @Test
    fun should_invoke_when_registerResultCallBack() {
        SeedlingSdkApi.registerResultCallBack()
        mockedStatic?.verify({
            SeedlingSdkApi.registerResultCallBack()
        }, Mockito.times(1))
    }

    @Test
    fun should_invoke_when_hideSeedlingStatusBar() {
        val callback: (Boolean) -> Unit = { _ ->
            //do nothing
        }
        SeedlingSdkApi.hideSeedlingStatusBar(callback)
        mockedStatic?.verify({
            SeedlingSdkApi.hideSeedlingStatusBar(callback)
        }, Mockito.times(1))
    }

    private fun <T> any(type: Class<T>): T = Mockito.any<T>(type)

    @Test
    fun should_invoke_when_updateCardData() {
        SeedlingSdkApi.updateCardData(any(SeedlingCard::class.java), any(String::class.java), any(Boolean::class.java))
    }
}