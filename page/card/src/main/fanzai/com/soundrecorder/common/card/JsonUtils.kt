/*
 Copyright (C), 2008-2024 OPLUS Mobile Comm Corp., Ltd.
 File: RecordSeedlingSmallCardWidgetProvider
 Description:
 Version: 1.0
 Date: 2024/9/26
 Author: <EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 80319089 2024/9/26 1.0 create
 */
package com.soundrecorder.common.card

import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.permission.PermissionUtils
import org.json.JSONObject

object JsonConstants {
    const val IMAGE_RECORD_BTN_PAUSE_ENABLE = "\$r('images.paused')"
    const val IMAGE_RECORD_BTN_PAUSE_ENABLE_NEW = "{{\$r('images.paused')}}"
    const val IMAGE_RECORD_BTN_PLAY_ENABLE = "\$r('images.recording')"
    const val IMAGE_RECORD_BTN_PLAY_ENABLE_NEW = "{{\$r('images.recording')}}"
    const val IMAGE_RECORD_BTN_DEFAULT = "{{\$r('images.default')}}"
    const val IMAGE_RECORD_BTN_PLAY_DISABLE = "\$r('images.disable_recording')"
    const val IMAGE_RECORD_BTN_PLAY_DISABLE_NEW = "{{\$r('images.disable_recording')}}"
    const val IMAGE_RECORD_BTN_PAUSE_DISABLE = "\$r('images.disable_paused')"
    const val IMAGE_RECORD_BTN_PAUSE_DISABLE_NEW = "{{\$r('images.disable_paused')}}"
    const val RECORD_STATE_RECORDING = "{{\$t('strings.recording')}}"
    const val RECORD_STATE_DEFAULT = "{{\$t('strings.app_name_main')}}"
    const val PAUSE_BTN_CLASS_RESUME = "pause2resume"
    const val PAUSE_BTN_CLASS_DONE = "pause2done"
    const val RECORD_BTN_CLASS_PAUSE = "recordBtn2pause"
    const val RECORD_BTN_CLASS_DONE = "recordBtn2done"
    const val DONE_BTN_CLASS_RESUME = "doneBtn2resume"
    const val DONE_BTN_CLASS_DONE = "doneBtn2done"
    const val TIME_TXT_CLASS_PAUSE = "timeTxt2pause"
    const val TIME_TXT_CLASS_DONE = "timeTxt2done"
    const val TIME_TXT_CLASS_PAUSE_L = "timeTxt2pauseLong"
    const val TIME_TXT_CLASS_DONE_L = "timeTxt2doneLong"
    const val TIME_TXT_DEFAULT = "00:00"
    const val INDEX = "index"
    const val TIME_TEXT = "timeText"
    const val RECORD_STATE_ICON = "recordStateIcon"
    const val RECORD_STATE_TXT = "recordStateTxt"
    const val TIME_TXT_CLASS = "timeTxtClass"
    const val DONE_DISABLE_1 = "doneDisable1"
    const val DONE_DISABLE_2 = "doneDisable2"
    const val RECORD_BTN_CLASS = "recordBtnClass"
    const val PAUSE_BTN_CLASS = "pauseBtnClass"
    const val DONE_BTN_CLASS = "doneBtnClass"
    const val NO_PERMISSION = "nopermission"
    const val KEY_SMALL_CARD_SAVE_LOTTIE = "saveSmallCardLottie"
    const val SMALL_CARD_SAVING = "small_card_saving"
    const val KEY_CARD_CLICK_URI = "cardClickUri"
    const val CARD_CLICK_URI = "nativeapp://oplus.intent.action.com.soundrecorder.SMALL_SEEDLING_CARD"
    const val KEY_UI_EVENT_TYPE = "uiEventType"
    const val UI_EVENT_TYPE_MESSAGE = "message"
    const val UI_EVENT_TYPE_DEEPLINK = "deeplink"
    const val KEY_UI_EVENT_URI = "uiEventUri"
    const val UI_EVENT_URI_MESSAGE = "com.oneplus.soundrecorder.card.event.provider"
    const val UI_EVENT_URI_DEEPLINK = "nativeapp://oplus.intent.action.provider.start_transparent_from_smallcard"
}

object JsonUtils {
    @JvmStatic
    fun updateJsonData(jsonData: JSONObject?, curStatus: Int) {
        when (jsonData?.getString(JsonConstants.RECORD_STATE_ICON)) {
            JsonConstants.IMAGE_RECORD_BTN_PAUSE_ENABLE -> {
                jsonData.put(JsonConstants.RECORD_STATE_ICON, JsonConstants.IMAGE_RECORD_BTN_PAUSE_ENABLE_NEW)
            }
            JsonConstants.IMAGE_RECORD_BTN_PLAY_ENABLE -> {
                jsonData.put(JsonConstants.RECORD_STATE_ICON, JsonConstants.IMAGE_RECORD_BTN_PLAY_ENABLE_NEW)
                jsonData.put(JsonConstants.RECORD_STATE_TXT, JsonConstants.RECORD_STATE_RECORDING)
            }
            JsonConstants.IMAGE_RECORD_BTN_PLAY_DISABLE -> {
                jsonData.put(JsonConstants.RECORD_STATE_ICON, JsonConstants.IMAGE_RECORD_BTN_PLAY_DISABLE_NEW)
            }
            JsonConstants.IMAGE_RECORD_BTN_PAUSE_DISABLE -> {
                jsonData.put(JsonConstants.RECORD_STATE_ICON, JsonConstants.IMAGE_RECORD_BTN_PAUSE_DISABLE_NEW)
            }
        }

        if (curStatus == -1) {
            jsonData?.put(JsonConstants.RECORD_STATE_ICON, JsonConstants.IMAGE_RECORD_BTN_DEFAULT)
        }
        val hasAllPermission = PermissionUtils.hasReadAudioPermission() && PermissionUtils.hasRecordAudioPermission()
        jsonData?.put(JsonConstants.NO_PERMISSION, !hasAllPermission)
    }
    @JvmStatic
    fun parseTimeTxtClass(jsonData: JSONObject?) {
        val timeLength = jsonData?.getString(JsonConstants.TIME_TEXT)?.length
        if (timeLength != null && timeLength > JsonConstants.TIME_TXT_DEFAULT.length) {
            jsonData.put(JsonConstants.TIME_TXT_CLASS, JsonConstants.TIME_TXT_CLASS_PAUSE_L)
        }
    }
    @JvmStatic
    fun initializeJsonData(jsonData: JSONObject, hasAllPermission: Boolean) {
        jsonData.putAll(
            JsonConstants.INDEX to 0, JsonConstants.RECORD_STATE_ICON to JsonConstants.IMAGE_RECORD_BTN_DEFAULT,
            JsonConstants.RECORD_STATE_TXT to JsonConstants.RECORD_STATE_DEFAULT,
            JsonConstants.TIME_TXT_CLASS to JsonConstants.TIME_TXT_CLASS_PAUSE, JsonConstants.TIME_TEXT to TimeUtils.getFormatTimeExclusiveMill(0),
            JsonConstants.RECORD_BTN_CLASS to JsonConstants.RECORD_BTN_CLASS_DONE,
            JsonConstants.DONE_DISABLE_2 to true, JsonConstants.DONE_DISABLE_1 to true,
            JsonConstants.PAUSE_BTN_CLASS to JsonConstants.PAUSE_BTN_CLASS_DONE, JsonConstants.DONE_BTN_CLASS to JsonConstants.DONE_BTN_CLASS_RESUME,
            JsonConstants.KEY_CARD_CLICK_URI to JsonConstants.CARD_CLICK_URI,
            JsonConstants.KEY_UI_EVENT_TYPE to JsonConstants.UI_EVENT_TYPE_DEEPLINK,
            JsonConstants.KEY_UI_EVENT_URI to JsonConstants.UI_EVENT_URI_DEEPLINK,
        )
        if (!hasAllPermission) {
            jsonData.put(JsonConstants.NO_PERMISSION, !hasAllPermission)
        }
    }
    @JvmStatic
    fun updateJsonDataForPauseStateInReset(jsonData: JSONObject) {
        jsonData.putAll(
            JsonConstants.INDEX to 1, JsonConstants.DONE_DISABLE_1 to false,
            JsonConstants.RECORD_BTN_CLASS to JsonConstants.RECORD_BTN_CLASS_PAUSE,
            JsonConstants.TIME_TXT_CLASS to JsonConstants.TIME_TXT_CLASS_PAUSE,
            JsonConstants.RECORD_STATE_ICON to JsonConstants.IMAGE_RECORD_BTN_PAUSE_ENABLE_NEW,
            JsonConstants.DONE_BTN_CLASS to JsonConstants.DONE_BTN_CLASS_RESUME,
            JsonConstants.KEY_CARD_CLICK_URI to JsonConstants.CARD_CLICK_URI,
            JsonConstants.KEY_UI_EVENT_TYPE to JsonConstants.UI_EVENT_TYPE_MESSAGE,
            JsonConstants.KEY_UI_EVENT_URI to JsonConstants.UI_EVENT_URI_MESSAGE,
        )
    }
    @JvmStatic
    fun updateJsonDataForPauseState(jsonData: JSONObject?) {
        jsonData?.putAll(
            JsonConstants.INDEX to 1, JsonConstants.DONE_DISABLE_1 to false,
            JsonConstants.RECORD_BTN_CLASS to JsonConstants.RECORD_BTN_CLASS_PAUSE,
            JsonConstants.TIME_TXT_CLASS to JsonConstants.TIME_TXT_CLASS_PAUSE,
            JsonConstants.KEY_CARD_CLICK_URI to JsonConstants.CARD_CLICK_URI,
            JsonConstants.KEY_UI_EVENT_TYPE to JsonConstants.UI_EVENT_TYPE_MESSAGE,
            JsonConstants.KEY_UI_EVENT_URI to JsonConstants.UI_EVENT_URI_MESSAGE,
        )
    }
    @JvmStatic
    fun updateJsonDataForDoneState(jsonData: JSONObject?) {
        jsonData?.putAll(
            JsonConstants.INDEX to 2,
            JsonConstants.TIME_TEXT to TimeUtils.getFormatTimeExclusiveMill(0),
            JsonConstants.DONE_DISABLE_2 to false,
            JsonConstants.PAUSE_BTN_CLASS to JsonConstants.PAUSE_BTN_CLASS_DONE,
            JsonConstants.DONE_BTN_CLASS to JsonConstants.DONE_BTN_CLASS_DONE,
            JsonConstants.KEY_CARD_CLICK_URI to "NA",
            JsonConstants.KEY_UI_EVENT_TYPE to JsonConstants.UI_EVENT_TYPE_MESSAGE,
            JsonConstants.KEY_UI_EVENT_URI to JsonConstants.UI_EVENT_URI_MESSAGE,
        )
    }
    @JvmStatic
    fun updateJsonDataForInitState(jsonData: JSONObject?) {
        jsonData?.putAll(
            JsonConstants.INDEX to 0,
            JsonConstants.TIME_TEXT to TimeUtils.getFormatTimeExclusiveMill(0),
            JsonConstants.RECORD_STATE_ICON to JsonConstants.IMAGE_RECORD_BTN_DEFAULT,
            JsonConstants.RECORD_STATE_TXT to JsonConstants.RECORD_STATE_DEFAULT,
            JsonConstants.TIME_TXT_CLASS to JsonConstants.TIME_TXT_CLASS_DONE,
            JsonConstants.RECORD_BTN_CLASS to JsonConstants.RECORD_BTN_CLASS_DONE,
            JsonConstants.DONE_DISABLE_2 to true, JsonConstants.DONE_DISABLE_1 to true,
            JsonConstants.PAUSE_BTN_CLASS to JsonConstants.PAUSE_BTN_CLASS_DONE,
            JsonConstants.DONE_BTN_CLASS to JsonConstants.DONE_BTN_CLASS_RESUME,
            JsonConstants.KEY_CARD_CLICK_URI to JsonConstants.CARD_CLICK_URI,
            JsonConstants.KEY_UI_EVENT_TYPE to JsonConstants.UI_EVENT_TYPE_DEEPLINK,
            JsonConstants.KEY_UI_EVENT_URI to JsonConstants.UI_EVENT_URI_DEEPLINK,
        )
    }
    @JvmStatic
    fun updateJsonDataToResumeState(jsonData: JSONObject?) {
        jsonData?.putAll(
            JsonConstants.INDEX to 0,
            JsonConstants.PAUSE_BTN_CLASS to JsonConstants.PAUSE_BTN_CLASS_RESUME,
            JsonConstants.DONE_BTN_CLASS to JsonConstants.DONE_BTN_CLASS_RESUME,
            JsonConstants.TIME_TXT_CLASS to JsonConstants.TIME_TXT_CLASS_PAUSE,
            JsonConstants.RECORD_BTN_CLASS to JsonConstants.RECORD_BTN_CLASS_PAUSE,
            JsonConstants.DONE_DISABLE_2 to true, JsonConstants.DONE_DISABLE_1 to true,
            JsonConstants.KEY_CARD_CLICK_URI to JsonConstants.CARD_CLICK_URI,
            JsonConstants.KEY_UI_EVENT_TYPE to JsonConstants.UI_EVENT_TYPE_MESSAGE,
            JsonConstants.KEY_UI_EVENT_URI to JsonConstants.UI_EVENT_URI_MESSAGE,
        )
    }
    @JvmStatic
    fun updateJsonDataForRecordBtn(jsonData: JSONObject?) {
        jsonData?.putAll(
            JsonConstants.TIME_TEXT to TimeUtils.getFormatTimeExclusiveMill(0),
            JsonConstants.RECORD_STATE_ICON to JsonConstants.IMAGE_RECORD_BTN_PLAY_ENABLE_NEW,
            JsonConstants.RECORD_STATE_TXT to JsonConstants.RECORD_STATE_RECORDING
        )
    }
}

fun JSONObject.putAll(vararg pairs: Pair<String, Any>) {
    for ((key, value) in pairs) {
        this.put(key, value)
    }
}
