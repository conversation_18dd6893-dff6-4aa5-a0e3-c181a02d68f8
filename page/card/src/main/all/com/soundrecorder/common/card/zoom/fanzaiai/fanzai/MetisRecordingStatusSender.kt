/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MetisRecordingStatusSender.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/18
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.zoom.fanzaiai.fanzai

import android.content.Context
import com.oplus.pantanal.seedling.intelligent.IntelligentData
import com.oplus.pantanal.seedling.util.SeedlingTool
import com.soundrecorder.base.utils.DebugUtil
import org.json.JSONObject

object MetisRecordingStatusSender {

    private const val TAG = "MetisRecordingStatusSender"

    private const val KEY_STATE = "state"
    private const val KEY_INSTANCE_ID = "instanceId"

    private const val RECORDING_STATE = "RECORDING_STATE"
    private const val EVENT_CODE = 12201

    /**
     * 当录音文件有更新的时候发送：保存新的录音，删除了一条录音
     */
    private const val STATUS_ON_UPDATE_RECORD = 1

    /**
     * 当全部的录音文件删除的时候
     */
    private const val STATUS_ON_DELETE_ALL_RECORD = 5

    /**
     * 最新的录音文件有更新
     */
    fun sendUpdateRecordEvent(context: Context, mediaId: Long) {
        val data = JSONObject().apply {
            put(KEY_STATE, STATUS_ON_UPDATE_RECORD)
            put(KEY_INSTANCE_ID, mediaId)
        }
        sendDataToMetis(context, data)
    }

    /**
     * 删除所有录音
     */
    fun sendDeleteAllRecordEvent(context: Context) {
        val data = JSONObject().apply {
            /**
             * 文件全部删除的情况instanceId传0
             */
            put(KEY_INSTANCE_ID, 0L)
            put(KEY_STATE, STATUS_ON_DELETE_ALL_RECORD)
        }
        sendDataToMetis(context, data)
    }

    /**
     * 向Metis 更新数据
     */
    private fun sendDataToMetis(context: Context, data: JSONObject) {
        runCatching {
            val intelligentData = IntelligentData(
                    System.currentTimeMillis(),
                    EVENT_CODE,
                    RECORDING_STATE,
                    data,
                    null,
                    null
            )
            SeedlingTool.updateIntelligentData(context, intelligentData)
            DebugUtil.d(TAG, "sendDataToMetis: data = $data")
        }.onFailure {
            DebugUtil.e(TAG, "sendDataToMetis error $it")
        }
    }
}