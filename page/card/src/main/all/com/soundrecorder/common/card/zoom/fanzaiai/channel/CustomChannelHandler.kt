/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CustomChannelHandler.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/18
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.card.zoom.fanzaiai.channel

import android.content.Context
import com.soundrecorder.base.utils.DebugUtil
import org.hapjs.features.channel.ChannelMessage
import org.hapjs.features.channel.HapChannelManager
import org.hapjs.features.channel.IHapChannel
import org.hapjs.features.channel.appinfo.HapApplication

class CustomChannelHandler(private val applicationContext: Context) :
    HapChannelManager.ChannelHandler {
    companion object {
        const val TAG = "CustomChannelHandler"

        //通道已经被打开
        const val CHANNEL_STATUS_OPEN = 2
    }


    override fun accept(hapApplication: HapApplication): Boolean {
        DebugUtil.d(TAG, "pkg = " + hapApplication.mPkgName + ", " + hapApplication.mSignature)
        //在这⾥检查快应⽤的包名和签名是否符合预期，是则返回true，否则返回false
        return true
    }

    override fun onOpen(iHapChannel: IHapChannel) {
        AppCardMessageSender.updateChannel(iHapChannel)
        DebugUtil.i(
            TAG,
            "onOpen pkg=${iHapChannel.hapApplication.mPkgName} status=${iHapChannel.status}"
        )
        if (iHapChannel.status == CHANNEL_STATUS_OPEN) {
            AppCardAudioFileObserver.onOpenSendData()
        }
    }

    override fun onReceiveMessage(channel: IHapChannel, message: ChannelMessage) {
        DebugUtil.i(TAG, "onReceiveMessage: " + message.code + " " + message.data)
        AppCardMessageSender.updateChannel(channel)
        val eventProcessor = AppCardEventProcessor()
        eventProcessor.onMessage(applicationContext, message)
    }

    override fun onClose(iHapChannel: IHapChannel, code: Int, s: String) {
        DebugUtil.d(TAG, "onClose code $code  $s")
        AppCardMessageSender.onChannelClosed()
    }

    override fun onError(channel: IHapChannel, errorCode: Int, errorMessage: String) {
        DebugUtil.i(
            TAG, "onError: " + channel.hapApplication.mPkgName
                    + " error, " + "errorCode " + errorCode + ", errorMessage:" +
                    errorMessage
        )
    }
}
