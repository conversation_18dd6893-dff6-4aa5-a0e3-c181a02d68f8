/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: SmallCardWidgetProvider2.kt
 ** Description: Copy from SmallCardWidgetProvider to be the recommended provider for fast applications
 ** Version: 1.0
 ** Date : 2025/04/04
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  ******** 2025/04/04      1.0     create file
 ****************************************************************/

package com.soundrecorder.common.card

import android.content.Context
import android.os.Bundle
import com.oplus.cardwidget.serviceLayer.AppCardWidgetProvider
import com.oplus.cardwidget.util.getCardType
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.CardPointStaticsUtil
import com.soundrecorder.modulerouter.AppCardInterface
import com.soundrecorder.modulerouter.CARD_TYPE_FOR_RECOMMEND_SMALL_CARD
import com.soundrecorder.modulerouter.utils.Injector

open class SmallCardWidgetProvider2 : AppCardWidgetProvider() {

    companion object {
        const val CARD_TAG = "SmallCardWidgetProvider2"
    }

    private val appCardApi by lazy {
        Injector.injectFactory<AppCardInterface>()
    }

    override fun onCardsObserve(context: Context, widgetCodes: List<String>) {
        super.onCardsObserve(context, widgetCodes)
        DebugUtil.i(CARD_TAG, "onCardsObserve,widgetCodes = ${widgetCodes.toTypedArray()}")
        widgetCodes.forEach {
            appCardApi?.addWidgetCodes(it)
        }
    }

    override fun subscribed(context: Context, widgetCode: String) {
        DebugUtil.i(CARD_TAG, "subscribed,widgetCode = $widgetCode")
        super.subscribed(context, widgetCode)
        CardPointStaticsUtil.addSmallCardEvent()
        appCardApi?.addWidgetCodesOnResume(widgetCode)
    }

    override fun unSubscribed(context: Context, widgetCode: String) {
        DebugUtil.i(CARD_TAG, "unSubscribed,widgetCode = $widgetCode")
        super.unSubscribed(context, widgetCode)
        CardPointStaticsUtil.removeSmallCardEvent()
        appCardApi?.removeWidgetCodeOnPause(widgetCode)
    }

    override fun onCardCreate(context: Context, widgetCode: String) {
        super.onCardCreate(context, widgetCode)
        DebugUtil.i(CARD_TAG, "onCardCreate,widgetCode = $widgetCode")
        appCardApi?.addWidgetCodes(widgetCode)
    }

    override fun getCardLayoutName(widgetCode: String): String {
        DebugUtil.i(CARD_TAG, "getCardLayoutName,widgetCode = $widgetCode")
        val cardType = widgetCode.getCardType()
        if (cardType == CARD_TYPE_FOR_RECOMMEND_SMALL_CARD) {
            return "small_card.json"
        }
        return ""
    }

    override fun onResume(context: Context, widgetCode: String) {
        DebugUtil.i(CARD_TAG, "onResume,widgetCode = $widgetCode")
        appCardApi?.addWidgetCodesOnResume(widgetCode)
    }

    override fun onPause(context: Context, widgetCode: String) {
        DebugUtil.i(CARD_TAG, "onPause,widgetCode = $widgetCode")
        super.onPause(context, widgetCode)
//        RecorderAction.removeWidgetCodeOnPause(widgetCode)
    }

    override fun call(method: String, arg: String?, extras: Bundle?): Bundle? {
        DebugUtil.i(CARD_TAG, "method = $method, extras = $extras")
        if (!checkCallPermission()) return null
        val code = extras?.getString("key_widget_code") ?: ""
        val bundle = appCardApi?.callFromSmallCard(method, code, true)
        if (bundle != null) {
            return bundle
        }
        return super.call(method, arg, extras)
    }
}