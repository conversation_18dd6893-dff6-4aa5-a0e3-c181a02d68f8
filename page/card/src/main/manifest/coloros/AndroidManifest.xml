<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.soundrecorder.common.card">

    <queries>
        <package android:name="com.oplus.smartengine" />
    </queries>
    <application>
        <meta-data
            android:name="com.soundRecorder.smallCard.versionCode"
            android:value="3" />

        <!--蜻蜓副屏-负一屏-->
        <meta-data
            android:name="com.oplus.ocs.card.AUTH_CODE"
            android:value="ADBEAiB3bk/zwneHdEgJDVuNybPFZaChVAefNmiWmjdmTloWBQIgEgQnUv0oS9NElv9xe3uhR9/W5pszoaElO2uTCt7ZjaZsbSRf" />

        <!--速览内销ColorOS-->
        <provider
            android:name="com.soundrecorder.common.card.SmallCardWidgetProvider"
            android:authorities="com.soundrecorder.common.provider.recorder.smallcard"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="android.appcard.action.APPCARD_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.card.provider"
                android:resource="@xml/small_card_app_widget" />
        </provider>

        <!--速览卡上推荐-->
        <provider
            android:name="com.soundrecorder.common.card.SmallCardWidgetProvider2"
            android:authorities="com.soundrecorder.common.provider.recorder.smallcard2"
            android:enabled="true"
            android:exported="true"
            android:permission="com.oplus.permission.safe.ASSISTANT">
            <intent-filter>
                <action android:name="android.appcard.action.APPCARD_UPDATE" />
            </intent-filter>
            <meta-data
                android:name="android.card.provider"
                android:resource="@xml/small_card_app_widget_recommend" />
        </provider>
    </application>
</manifest>
