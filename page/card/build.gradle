apply from: "../../common_flavor_build.gradle"
apply plugin: 'com.google.protobuf'

android {
    sourceSets {
        main {
            res.srcDirs += ['res', '../../res-strings', 'src/main/all/res']
            assets.srcDirs += ['src/main/all/assets', 'src/main/fanzai/assets']
            java.srcDirs += ['src/main/all', 'src/main/fanzai']
            manifest.srcFile 'src/main/all/AndroidManifest.xml'
        }

        oppoPallDomesticAall {
            res.srcDirs += ['src/main/oplus/res']
            assets.srcDirs += ['src/main/oplus/assets']
            java.srcDirs += ['src/main/oplus']
            manifest.srcFile 'src/main/manifest/coloros/AndroidManifest.xml'
        }
        oppoPallExportAall {
            res.srcDirs += ['src/main/oplus/res']
            assets.srcDirs += ['src/main/oplus/assets']
            java.srcDirs += ['src/main/oplus']
            manifest.srcFile 'src/main/manifest/coloros/AndroidManifest.xml'
        }
        oppoPallGdprAall {
            res.srcDirs += ['src/main/oplus/res']
            assets.srcDirs += ['src/main/oplus/assets']
            java.srcDirs += ['src/main/oplus']
            manifest.srcFile 'src/main/manifest/coloros/AndroidManifest.xml'
        }
        oneplusPallDomesticAall {
            res.srcDirs += ['src/main/oplus/res']
            assets.srcDirs += ['src/main/oplus/assets']
            java.srcDirs += ['src/main/oplus']
            manifest.srcFile 'src/main/manifest/coloros/AndroidManifest.xml'
        }
        oneplusPallExportAall {
            res.srcDirs += ['src/main/oneplus/res']
            assets.srcDirs += ['src/main/oneplus/assets']
            java.srcDirs += ['src/main/oneplus']
            manifest.srcFile 'src/main/manifest/oneplus/export/AndroidManifest.xml'
        }
        oneplusPallGdprAall {
            res.srcDirs += ['src/main/oneplus/res']
            assets.srcDirs += ['src/main/oneplus/assets']
            java.srcDirs += ['src/main/oneplus']
            manifest.srcFile 'src/main/manifest/oneplus/export/AndroidManifest.xml'
        }
    }
    namespace "com.soundrecorder.common.card"
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation libs.org.kotlin.stdlib
    implementation project(':common:libbase')
    implementation project(':common:libcommon')

    // Koin for Android
    implementation(libs.koin)
    implementation project(':common:modulerouter')
    //泛在SDK 包括速览sdk
    implementation libs.oplus.pantanal.card
    //接入channel 应用sdk 工号 ******** https://open.oppomobile.com/new/developmentDoc/info?id=13012
    implementation libs.oplus.channel.sdk
    implementation libs.keyguardstyle
    compileOnly libs.gson

    implementation libs.protobuf.java
    implementation(libs.square.retrofit.converter.protobuf) {
        exclude group: 'com.google.protobuf', module: 'protobuf-java'
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    implementation(libs.oplus.storage.sdk)
}

protobuf {
    protoc {
        artifact = 'com.google.protobuf:protoc:3.25.8'
    }
    generateProtoTasks {
        all().each { task ->
            task.builtins {
                remove java
            }
            task.builtins {
                java {}
            }
        }
    }
}