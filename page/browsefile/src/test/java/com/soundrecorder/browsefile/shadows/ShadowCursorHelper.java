/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : ShadowCursorHelper.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/10/16
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/10/16, LI Kun, create
 ************************************************************/

package com.soundrecorder.browsefile.shadows;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

import android.content.Context;
import android.database.Cursor;
import android.provider.MediaStore;

import com.soundrecorder.common.db.CursorHelper;

import org.mockito.Mockito;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(CursorHelper.class)
public class ShadowCursorHelper {

    @Implementation
    public static Cursor querryAudioFiles(Context c, int recordingType) {
        Cursor mockCursor = Mockito.mock(Cursor.class);
        when(mockCursor.getCount()).thenReturn(1);
        when(mockCursor.moveToPosition(anyInt())).thenReturn(true);
        when(mockCursor.getColumnIndex(MediaStore.Files.FileColumns._ID)).thenReturn(1);
        when(mockCursor.getLong(1)).thenReturn(1234567L);
        when(mockCursor.getColumnIndex(MediaStore.Files.FileColumns.DATA)).thenReturn(2);
        when(mockCursor.getString(2)).thenReturn("2019/10/10");
        when(mockCursor.getColumnIndex(MediaStore.Files.FileColumns.DISPLAY_NAME)).thenReturn(4);
        when(mockCursor.getString(4)).thenReturn("test.mp3");
        doNothing().when(mockCursor).close();
        return mockCursor;
    }

}
