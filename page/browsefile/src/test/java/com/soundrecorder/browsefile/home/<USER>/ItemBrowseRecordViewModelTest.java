package com.soundrecorder.browsefile.home.item;

import static org.mockito.Mockito.spy;
import static org.robolectric.Shadows.shadowOf;

import android.content.Context;
import android.content.Intent;
import android.os.Build;

import androidx.lifecycle.MutableLiveData;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.browsefile.BrowseFile;
import com.soundrecorder.browsefile.shadows.ShadowCursorHelper;
import com.soundrecorder.browsefile.shadows.ShadowFeatureOption;
import com.soundrecorder.browsefile.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.player.status.PlayStatus;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.Robolectric;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ItemBrowseRecordViewModelTest {
    private ActivityController<BrowseFile> mController;
    private BrowseFile mActivity;
    private ShadowApplication mShadowApplication;
    private Context mContext;

    @Before
    public void setUp() {
        mController = Robolectric.buildActivity(BrowseFile.class);
        mContext = ApplicationProvider.getApplicationContext();
        mShadowApplication = ShadowApplication.getInstance();
    }

    @After
    public void tearDown() {
        mController = null;
        mContext = null;
        mShadowApplication = null;
    }

    @Test
    public void verify_value_when_onClickCheckBox() {
        ItemBrowseRecordViewModel browseRecordViewModel = new ItemBrowseRecordViewModel();
        browseRecordViewModel.onClickCheckBox();
        Assert.assertNotNull(ItemBrowseRecordViewModel.Companion.getLiveSelectedMap());
    }

    @Test
    @Config(shadows = ShadowCursorHelper.class)
    public void should_startActivity_when_startItemPlay() {
        mActivity = spy(mController.get());
        ItemBrowseRecordViewModel browseRecordViewModel = new ItemBrowseRecordViewModel();
        browseRecordViewModel.startItemPlay(mActivity);
        Intent intent = shadowOf(RuntimeEnvironment.application).getNextStartedActivity();
        Assert.assertNotNull(intent);
    }

    @Test
    @Config(shadows = ShadowCursorHelper.class)
    public void should_returnTrue_when_onLongClick() {
        ItemBrowseRecordViewModel browseRecordViewModel = new ItemBrowseRecordViewModel();
        ItemBrowseRecordViewModel.Companion.getLiveEditMode().put(browseRecordViewModel.getTaskId(), new MutableLiveData<>(false));
        browseRecordViewModel.onLongClick();
        //Assert.assertTrue(ItemBrowseRecordViewModel.Companion.getEditModeChangeAnim());
    }

    @Test
    public void should_returnTrue_when_getRecord() {
        ItemBrowseRecordViewModel browseRecordViewModel = new ItemBrowseRecordViewModel();
        browseRecordViewModel.setMediaId(123);
        Record record = browseRecordViewModel.getRecord();
        Assert.assertEquals(123, record.getId());
    }


    @Test
    public void should_returnText_when_durationText() {
        ItemBrowseRecordViewModel browseRecordViewModel = new ItemBrowseRecordViewModel();
        browseRecordViewModel.setMDuration(1000);
        String text = browseRecordViewModel.durationText();
        Assert.assertTrue(text.startsWith("00:"));
    }

    @Test
    public void should_returnText_when_dateModifiedText() {
        ItemBrowseRecordViewModel browseRecordViewModel = new ItemBrowseRecordViewModel();
        browseRecordViewModel.setDateModified(System.currentTimeMillis() / 1000);
        String text = browseRecordViewModel.dateModifiedText();
        Assert.assertNotEquals("", text);
    }

    @Test
    public void should_equals_when_itemCheckBoxContentDescriptionText() {
        ItemBrowseRecordViewModel browseRecordViewModel = new ItemBrowseRecordViewModel();
        browseRecordViewModel.setTitle(null);
        Assert.assertEquals("", browseRecordViewModel.itemCheckBoxContentDescriptionText());

        browseRecordViewModel.setTitle("hahaha");
        Assert.assertEquals("hahaha", browseRecordViewModel.itemCheckBoxContentDescriptionText());
    }

    @Test
    public void should_correct_when_isNeedShowSeekBarArea() {
        ItemBrowseRecordViewModel browseRecordViewModel = new ItemBrowseRecordViewModel();
        Assert.assertTrue(browseRecordViewModel.isNeedShowSeekBarArea(PlayStatus.PLAYER_STATE_PAUSE));
        Assert.assertTrue(browseRecordViewModel.isNeedShowSeekBarArea(PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE));
        Assert.assertTrue(browseRecordViewModel.isNeedShowSeekBarArea(PlayStatus.PLAYER_STATE_PLAYING));
        Assert.assertTrue(browseRecordViewModel.isNeedShowSeekBarArea(PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING));
        Assert.assertTrue(browseRecordViewModel.isNeedShowSeekBarArea(ItemBrowseRecordViewModel.PLAYER_STATE_AUTO_MARK));
        Assert.assertFalse(browseRecordViewModel.isNeedShowSeekBarArea(PlayStatus.PLAYER_STATE_INIT));
    }

    @Test
    public void should_notNull_when_durationContentDescriptionText() {
        ItemBrowseRecordViewModel browseRecordViewModel = new ItemBrowseRecordViewModel();
        browseRecordViewModel.setMDuration(1000);
        Assert.assertNotNull(browseRecordViewModel.durationContentDescriptionText());
    }

    @Test
    public void should_not_null_when_toString() {
        ItemBrowseRecordViewModel browseRecordViewModel = new ItemBrowseRecordViewModel();
        Assert.assertNotNull(browseRecordViewModel.toString());
    }
}
