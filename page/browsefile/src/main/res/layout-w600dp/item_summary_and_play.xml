<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <variable
            name="itemRecord"
            type="com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/summary_and_play"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/item_play_area"
            layout="@layout/item_play_area"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp12"
            android:visibility="gone"
            app:itemRecord="@{itemRecord}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>