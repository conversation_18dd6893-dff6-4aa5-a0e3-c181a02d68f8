<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="ConvertLoadingView">
        <attr name="minWidth" format="dimension" />
        <attr name="maxWidth" format="dimension" />
        <attr name="minHeight" format="dimension" />
        <attr name="maxHeight" format="dimension" />
        <attr name="indicatorColor" format="color" />
    </declare-styleable>

    <declare-styleable name="MyListItemView">
        <attr name="state_list_checked" format="boolean" />
    </declare-styleable>

    <declare-styleable name="AmplitudeSeekBar">
        <attr name="point_color" format="color" />
        <attr name="amplitude_color" format="color" />
        <attr name="amplitude_unselect_color" format="color" />
        <attr name="wave_padding" format="dimension" />
    </declare-styleable>
</resources>