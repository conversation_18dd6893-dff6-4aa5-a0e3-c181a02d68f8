<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="280dp"
    android:height="200dp"
    android:viewportWidth="280"
    android:viewportHeight="200">
  <path
      android:pathData="M104.02,56.47L99.11,53.47C95.31,56.8 90.42,58.64 85.36,58.63C80.3,58.63 75.42,56.78 71.63,53.44C67.83,50.09 65.38,45.48 64.74,40.46C64.1,35.45 65.3,30.37 68.13,26.17C70.96,21.98 75.22,18.96 80.11,17.68C85.01,16.4 90.2,16.94 94.72,19.21C99.24,21.48 102.78,25.31 104.68,30C106.58,34.69 106.71,39.91 105.04,44.68C104.42,46.47 103.56,48.16 102.48,49.71L105.61,54.86C105.73,55.08 105.77,55.33 105.74,55.58C105.7,55.83 105.58,56.07 105.41,56.25C105.23,56.43 105,56.54 104.75,56.58C104.5,56.62 104.24,56.58 104.02,56.47Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.6"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M88.5,47.46C88.5,48 88.35,48.53 88.05,48.98C87.75,49.43 87.32,49.78 86.83,49.99C86.33,50.19 85.78,50.25 85.25,50.15C84.72,50.04 84.23,49.78 83.85,49.4C83.47,49.02 83.21,48.54 83.1,48.01C83,47.48 83.05,46.93 83.26,46.43C83.46,45.93 83.81,45.5 84.26,45.2C84.71,44.9 85.24,44.74 85.78,44.74C86.5,44.74 87.19,45.03 87.7,45.54C88.21,46.05 88.5,46.74 88.5,47.46Z"
      android:fillColor="#5680BF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M92.81,33.32C92.68,34.57 92.22,35.78 91.47,36.79C90.72,37.81 89.7,38.6 88.54,39.09C88.35,39.18 88.2,39.32 88.09,39.49C87.98,39.67 87.92,39.87 87.92,40.07V40.8C87.94,41.09 87.9,41.38 87.81,41.65C87.71,41.93 87.56,42.18 87.36,42.39C87.17,42.6 86.93,42.77 86.66,42.89C86.39,43.01 86.11,43.07 85.82,43.07C85.53,43.07 85.24,43.01 84.97,42.89C84.71,42.77 84.47,42.6 84.27,42.39C84.07,42.18 83.92,41.93 83.83,41.65C83.73,41.38 83.69,41.09 83.71,40.8V40.12C83.69,39.1 83.97,38.09 84.52,37.23C85.07,36.35 85.87,35.65 86.82,35.23C87.3,35.05 87.72,34.73 88,34.3C88.32,33.9 88.51,33.42 88.56,32.91C88.61,32.41 88.52,31.9 88.3,31.44C88.09,30.99 87.76,30.59 87.34,30.31C86.92,30.03 86.43,29.87 85.93,29.84C85.43,29.81 84.92,29.92 84.48,30.18C84.03,30.41 83.65,30.76 83.38,31.19C83.13,31.63 82.99,32.13 82.99,32.64C82.99,33.2 82.77,33.74 82.37,34.13C81.98,34.53 81.44,34.75 80.88,34.75C80.32,34.75 79.78,34.53 79.39,34.13C78.99,33.74 78.77,33.2 78.77,32.64C78.78,31.39 79.12,30.16 79.78,29.08C80.42,27.99 81.36,27.09 82.49,26.5C83.61,25.9 84.87,25.61 86.14,25.67C87.41,25.74 88.64,26.15 89.7,26.86C90.76,27.57 91.61,28.55 92.15,29.71C92.69,30.83 92.92,32.08 92.81,33.32Z"
      android:fillColor="#5680BF"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M122.88,60.34C123.4,60.14 123.39,59.38 123.26,58.67L123.19,58.68C122.35,56.82 121.89,54.81 121.85,52.78C121.85,52.49 121.85,52.2 121.86,51.92C124.34,53.01 124.76,55.65 123.96,58.6C123.08,61.85 123.17,65.61 123.26,69.31C123.32,72.1 123.39,74.86 123.04,77.35C121.97,84.98 118.44,89.31 116.34,91.21C116.28,91.26 116.2,91.3 116.12,91.31C116.04,91.32 115.96,91.31 115.89,91.27C115.82,91.24 115.76,91.18 115.71,91.12C115.67,91.05 115.65,90.97 115.65,90.89L115.76,87.17C115.42,87.35 115.1,87.56 114.8,87.79C113.84,88.53 113.05,89.46 112.47,90.52C111.88,91.58 111.52,92.74 111.41,93.95C111.4,94.06 111.37,94.17 111.3,94.26C111.23,94.35 111.14,94.42 111.03,94.46C110.93,94.5 110.81,94.5 110.7,94.47C110.59,94.44 110.5,94.38 110.42,94.29C108.53,92.51 107.33,90.12 107.03,87.55L107.03,87.52C106.66,84.89 106,80.19 109.04,72.25C109.57,70.89 110.19,69.52 110.81,68.14C112.41,64.59 114.05,60.97 114.37,57.14C114.57,54.61 115.55,52.49 117.53,51.72C117.81,51.94 118.14,52.13 118.47,52.32C119.2,52.73 119.93,53.14 120.18,53.94C120.43,54.74 120.06,55.4 119.7,56.06C119.32,56.77 118.94,57.47 119.33,58.31C119.98,59.67 122.09,60.66 122.88,60.34Z"
      android:fillColor="#CC8A52"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M123.26,58.67C123.39,59.38 123.4,60.14 122.88,60.34C122.09,60.66 119.98,59.67 119.33,58.31C118.58,56.69 120.69,55.6 120.18,53.94C119.67,52.28 117.03,52.28 116.77,50.51C116.7,50.09 116.76,49.65 116.93,49.26C117.11,48.87 117.38,48.53 117.74,48.29C118.1,48.11 118.52,48.04 118.92,48.1C118.84,47.84 118.81,47.56 118.83,47.28C118.86,47.01 118.93,46.74 119.06,46.49C119.19,46.24 119.36,46.03 119.58,45.85C119.79,45.67 120.03,45.53 120.3,45.45C120.56,45.36 120.84,45.33 121.12,45.36C121.39,45.38 121.66,45.46 121.91,45.59C122.16,45.71 122.38,45.89 122.56,46.1C122.74,46.31 122.87,46.56 122.95,46.82C122.19,48.71 121.81,50.74 121.85,52.77C121.89,54.81 122.35,56.82 123.19,58.68L123.26,58.67Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.03,98.84C83.93,98.89 83.89,99 83.93,99.1C83.98,99.2 84.1,99.25 84.2,99.2L84.03,98.84ZM92.37,100.67L92.2,100.77L92.2,100.78L92.37,100.67ZM95.27,109.67L95.07,109.68C95.07,109.69 95.08,109.7 95.08,109.71L95.27,109.67ZM95.57,115.27L95.72,115.41C95.75,115.38 95.76,115.34 95.77,115.3L95.57,115.27ZM91.34,116.34C91.23,116.32 91.13,116.39 91.11,116.5C91.09,116.61 91.16,116.71 91.27,116.73L91.34,116.34ZM84.2,99.2C85.22,98.75 86.69,98.25 88.17,98.32C89.64,98.38 91.12,98.99 92.2,100.77L92.54,100.56C91.39,98.66 89.77,97.99 88.19,97.92C86.62,97.85 85.08,98.37 84.03,98.84L84.2,99.2ZM92.2,100.78C93.92,103.44 94.91,106.51 95.07,109.68L95.47,109.65C95.31,106.42 94.3,103.28 92.54,100.56L92.2,100.78ZM95.08,109.71C95.51,111.52 95.61,113.4 95.37,115.25L95.77,115.3C96.01,113.4 95.91,111.48 95.47,109.62L95.08,109.71ZM95.42,115.14C94.49,116.17 93.02,116.64 91.34,116.34L91.27,116.73C93.06,117.05 94.68,116.56 95.72,115.41L95.42,115.14Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M142.57,129.33C142.77,130.3 143.39,130.89 144.26,131.2M142.55,129.33C142.55,129.33 141.48,124.55 143.85,121.15C146.74,117.09 151.17,116.47 155.71,116.88C158.12,117.1 160.18,119.43 160.01,122.25"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M144.9,133.67C142.43,127.34 142.96,117.4 142.96,117.4L157.27,115.26L158.26,123.33"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M180.62,99.73L181.46,98.44C181.67,98.12 181.93,97.76 182.32,97.73C183.3,97.67 183.13,99.15 182.87,100.19"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M144.6,132.22C144.6,132.22 144.96,137.54 125.56,134.66C98.81,130.69 95.58,123.3 95.43,121.54C95.43,121.01 95.32,120.49 95.76,120.17C99.32,117.7 103.35,115.98 107.6,115.13C111.85,114.27 116.23,114.29 120.48,115.19C142.48,119.9 144.6,132.22 144.6,132.22Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.16,117.84C94.82,112.14 101.44,103.62 101.44,103.62L88.2,97.54L85.24,101.34"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136.4,152.28L136.44,152.49L136.47,152.27L136.4,152.28Z"
      android:fillColor="#7E73E5"/>
  <path
      android:pathData="M131.2,48.24C135.13,44.88 141.05,43.65 146.35,45.74C154.64,49.01 151.98,57.12 150.25,61.74C154.17,59.52 156.82,55.31 156.82,50.49C156.82,43.36 151.04,37.58 143.91,37.58C137.55,37.58 132.26,42.18 131.2,48.24Z"
      android:fillColor="#CC8A52"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M141.95,66.32C141.25,66.07 140.66,65.6 140.26,64.98"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M148.16,61.83C147.9,62.48 147.33,62.87 146.88,62.7C146.43,62.52 146.27,61.84 146.53,61.19C146.79,60.54 147.36,60.14 147.81,60.31C148.26,60.49 148.42,61.17 148.16,61.83Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M139.26,59.57C139.06,60.29 138.46,60.75 137.93,60.59C137.78,60.52 137.65,60.43 137.55,60.32C137.44,60.2 137.36,60.07 137.3,59.92C137.24,59.78 137.22,59.62 137.23,59.46C137.23,59.31 137.27,59.15 137.33,59.01C137.53,58.3 138.14,57.82 138.67,57.99C138.82,58.05 138.94,58.15 139.05,58.26C139.16,58.37 139.24,58.51 139.3,58.65C139.35,58.8 139.38,58.96 139.37,59.11C139.37,59.27 139.33,59.42 139.26,59.57Z"
      android:fillColor="#323739"/>
  <path
      android:pathData="M150.27,59.76C150.04,59.46 149.74,59.22 149.41,59.06C149.07,58.89 148.7,58.81 148.32,58.82C147.99,58.85 147.66,58.95 147.36,59.12C147.06,59.28 146.79,59.49 146.58,59.76"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M136,56.99C136.32,56.75 136.68,56.57 137.06,56.48C137.45,56.38 137.85,56.37 138.24,56.44C138.63,56.51 139,56.66 139.33,56.89C139.66,57.11 139.93,57.4 140.14,57.74"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M125.63,58.93C125.81,58.74 126.04,58.6 126.29,58.52C126.54,58.44 126.8,58.43 127.05,58.49C127.57,58.59 128.03,58.86 128.38,59.25"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M150.08,47.75C148.39,50.17 145.9,51.92 143.05,52.69C141.15,53.11 139.22,53.39 137.29,53.56C135.35,53.71 133.49,54.34 131.87,55.4C131.21,55.85 130.68,56.44 130.29,57.13C129.9,57.83 129.68,58.6 129.64,59.39C129.64,59.44 129.62,59.49 129.59,59.52C129.56,59.56 129.52,59.59 129.48,59.6C129.43,59.62 129.38,59.62 129.33,59.6C129.29,59.58 129.25,59.55 129.22,59.51C128.04,57.8 127.14,55.9 126.58,53.9C126.3,52.98 126.22,52 126.35,51.04C126.47,50.09 126.8,49.16 127.3,48.34C129.45,45.5 130.13,44.04 134.69,42.59C140.2,40.85 143.09,43.11 146.54,44.81C147.94,45.48 149.16,46.49 150.08,47.75Z"
      android:fillColor="#CC8A52"/>
  <path
      android:pathData="M141.89,43.95C144.8,45.1 147.3,47.08 149.09,49.65C150.87,52.22 151.85,55.27 151.9,58.39C151.9,58.39 154.39,51.92 150.99,47.72C147.59,43.53 144.13,42.89 144.13,42.89L141.89,43.95Z"
      android:fillColor="#CC8A52"/>
  <path
      android:pathData="M136.4,152.28L136.44,152.49L136.47,152.27L136.4,152.28Z"
      android:fillColor="#7E73E5"/>
  <path
      android:pathData="M147.42,108.72C143.56,116.26 138.91,125.91 136.44,136.89C136.46,138.63 136.62,140.37 136.94,142.08C136.66,141.65 102.56,135.4 102.56,135.4C102.34,123.54 103.18,111.68 105.07,99.96C107.5,85.04 118.52,75.45 122.35,77.77C125.5,84.28 137.5,87.93 140.58,87.14C143.9,86.28 143.7,83.58 143.7,83.58C143.7,83.58 148.16,85.77 149.88,93.07C150.78,96.91 149.1,105.44 147.42,108.72Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M140.45,81.85L137.73,80.47L138.57,75.74"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M138.57,75.74L137.87,78.5C136.78,78.33 135.8,77.77 135.12,76.91C133.63,75.36 132.3,73.67 131.15,71.86C131.15,71.86 134.18,74.76 136.56,75.31C137.21,75.55 137.88,75.69 138.57,75.74Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M212.23,150.87C211.51,145.93 212.28,140.89 214.46,136.4C216.64,131.95 221.39,127.56 225.72,125.13C226.54,126.32 223.61,132.02 222.16,132.4C223.59,132.91 224.04,134.83 223.49,136.24C222.8,137.6 221.76,138.76 220.47,139.58C219.89,140.13 219.17,140.5 218.4,140.66C218.01,140.73 217.62,140.64 217.29,140.42C216.97,140.2 216.75,139.86 216.68,139.48C216.69,139.29 216.74,139.11 216.83,138.95C216.92,138.79 217.05,138.65 217.19,138.53C217.34,138.42 217.51,138.34 217.69,138.3C217.87,138.26 218.06,138.26 218.24,138.29C218.6,138.39 218.92,138.6 219.17,138.88C219.41,139.16 219.57,139.51 219.62,139.88C219.71,140.63 219.56,141.38 219.2,142.04C218.76,142.94 218.12,143.74 217.34,144.37C216.57,145.01 215.66,145.47 214.68,145.72C215.02,146.73 214.96,147.83 214.5,148.79C214.04,149.75 213.23,150.5 212.23,150.87Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M210.89,145.06C210.25,139.9 209.06,134.81 207.33,129.9C205.44,125.06 202.35,120.52 197.92,117.82C197.93,122.06 199.07,126.22 201.21,129.88C201.46,130.34 201.8,130.74 202.21,131.06C202.43,131.23 202.69,131.33 202.96,131.35C203.23,131.38 203.5,131.33 203.74,131.22C203.86,131.15 203.96,131.06 204.03,130.95C204.11,130.84 204.16,130.72 204.18,130.59C204.21,130.46 204.2,130.32 204.17,130.19C204.13,130.06 204.07,129.94 203.99,129.84C203.87,129.76 203.74,129.71 203.6,129.69C203.46,129.66 203.31,129.67 203.17,129.71C203.04,129.74 202.91,129.81 202.8,129.9C202.69,129.99 202.6,130.1 202.53,130.23C202.32,130.78 202.32,131.38 202.53,131.93C203.12,134.12 204.5,136.01 206.41,137.23C206.02,137.3 205.67,137.51 205.41,137.81C205.15,138.1 205,138.48 204.98,138.87C204.94,139.67 205.17,140.46 205.64,141.1C206.79,142.99 209.07,143.91 210.89,145.06Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M212.03,137.93C212.39,129.27 215.33,120.92 220.47,113.94"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M211.4,131.95C211.06,128.54 210.12,125.22 208.61,122.15"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M208.53,123.78C209.76,123.78 210.75,122.79 210.75,121.57C210.75,120.34 209.76,119.35 208.53,119.35C207.31,119.35 206.31,120.34 206.31,121.57C206.31,122.79 207.31,123.78 208.53,123.78Z"
      android:fillColor="#928EBA"/>
  <path
      android:pathData="M219.63,118.41C221.03,118.41 222.16,117.28 222.16,115.88C222.16,114.47 221.03,113.34 219.63,113.34C218.23,113.34 217.09,114.47 217.09,115.88C217.09,117.28 218.23,118.41 219.63,118.41Z"
      android:fillColor="#928EBA"/>
  <path
      android:pathData="M217.97,126.07C218.87,126.07 219.59,125.35 219.59,124.45C219.59,123.55 218.87,122.82 217.97,122.82C217.07,122.82 216.34,123.55 216.34,124.45C216.34,125.35 217.07,126.07 217.97,126.07Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M217.63,178.14C217.68,178.08 217.73,178.02 217.77,177.96C217.98,177.68 218.07,177.32 218.02,176.97C216.97,169.74 215.91,162.32 214.84,154.82C215.96,151.49 217.02,152.86 218.21,149.61H205.47C206.47,152.86 207.45,151.47 208.45,154.81C208.24,156.5 208.03,158.19 207.81,159.88M205.89,174.98L205.88,175.06M205.89,174.98C205.88,175.01 205.88,175.03 205.88,175.06M205.89,174.98C206.08,173.46 206.28,171.94 206.48,170.41M205.88,175.06C205.87,175.09 205.87,175.11 205.87,175.14L205.88,175.06Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M229.51,186.28H168V181.19C168,180.8 168.08,180.4 168.23,180.04C168.38,179.67 168.61,179.34 168.89,179.06C169.17,178.77 169.5,178.55 169.87,178.4C170.23,178.25 170.63,178.17 171.02,178.17H226.49C227.29,178.17 228.06,178.49 228.63,179.06C229.2,179.62 229.51,180.39 229.51,181.19V186.28Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M90.16,117.85C85.85,122.95 77.19,121.14 74.1,108.1C72.29,100.55 69.58,89 66.56,80.09C66.56,80.09 58.61,75.67 58.57,75.61L53.44,69.68C53.44,69.61 50.1,62.26 50.1,62.26C50.04,62.14 50.01,62.01 50,61.88C50,61.75 50.02,61.62 50.06,61.5C50.11,61.38 50.18,61.27 50.27,61.17C50.36,61.08 50.47,61 50.59,60.95C50.81,60.85 51.06,60.84 51.3,60.91C51.53,60.98 51.73,61.14 51.85,61.35L55.69,67.9L57.27,69.38L56.79,67.65L55.37,59.66C55.31,59.38 55.36,59.09 55.52,58.85C55.67,58.62 55.92,58.45 56.19,58.39C56.47,58.33 56.76,58.38 57,58.54C57.24,58.69 57.41,58.93 57.47,59.21L57.95,60.99L57.71,59.63C57.67,59.5 57.66,59.36 57.68,59.23C57.7,59.1 57.75,58.97 57.83,58.86C57.9,58.75 57.99,58.65 58.11,58.58C58.22,58.51 58.34,58.46 58.48,58.44C58.71,58.4 58.96,58.45 59.16,58.57C59.36,58.7 59.51,58.9 59.58,59.13L61.7,66.34L62.08,62.09C62.09,61.84 62.2,61.61 62.39,61.44C62.57,61.28 62.82,61.19 63.06,61.2C63.31,61.22 63.55,61.33 63.71,61.51C63.88,61.7 63.97,61.94 63.95,62.19L63.85,66.82L65.67,70.09C67.84,70.92 69.88,72.05 71.74,73.44C72.99,74.29 73.97,75.49 74.56,76.88C76.57,80.55 81.69,93.79 87.7,107.21"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M84.45,99.81C85.5,100.11 87.28,101.03 89.93,103.65C91.56,105.28 92.78,107.26 93.5,109.45C94.22,111.63 94.41,113.96 94.06,116.23C94.06,116.23 99.24,115.41 109.47,98.13L116.37,95.46L120.5,76.51C118.23,75.84 116,75.05 113.81,74.14C110.43,72.45 101.44,71.06 93.17,85.62C90.41,90.2 87.35,94.6 84.01,98.79C84.16,99.13 84.31,99.47 84.45,99.81Z"
      android:fillColor="#64589E"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M143.41,82.92C143.41,82.92 154.64,81.26 156.1,92.85C157.64,105.31 159.81,120.57 159.81,120.57C159.81,120.57 158.29,117.75 154.24,119.15C147.98,121.3 143.97,129.47 143.97,129.47C143.97,129.47 140.71,114.69 141.32,104.26C142.3,87.22 143.41,82.92 143.41,82.92Z"
      android:fillColor="#64589E"/>
  <path
      android:pathData="M135.23,55.07C137.11,54.37 140.28,54.09 141.88,55.14"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.95,60.96L58.88,66.1L60.87,71.52"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M61.7,66.4L63.09,70.46"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M57.28,69.38L58.18,72"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M206.37,176.66C205.91,175.69 205.67,174.62 205.67,173.53C205.67,172.43 205.91,171.35 206.39,170.36"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M206.75,178.07H174.24C174.12,178.07 174,178.04 173.9,177.98C173.8,177.93 173.72,177.84 173.65,177.74C172.87,176.48 172.46,175.02 172.46,173.53C172.46,172.05 172.87,170.59 173.65,169.33C173.72,169.23 173.8,169.14 173.9,169.09C174,169.03 174.12,168.99 174.24,168.99H206.78C206.87,168.99 206.96,169.01 207.04,169.05C207.12,169.08 207.2,169.13 207.26,169.2C207.33,169.26 207.38,169.34 207.41,169.42C207.45,169.5 207.46,169.59 207.46,169.68C207.46,169.77 207.45,169.86 207.41,169.94C207.38,170.03 207.33,170.1 207.26,170.16C207.2,170.23 207.12,170.28 207.04,170.31C206.96,170.34 206.87,170.36 206.78,170.36H174.6C174.08,171.33 173.8,172.43 173.8,173.53C173.8,174.64 174.08,175.74 174.6,176.71H206.75C206.84,176.71 206.93,176.73 207.02,176.76C207.1,176.79 207.18,176.84 207.24,176.9C207.3,176.97 207.35,177.04 207.39,177.13C207.42,177.21 207.44,177.3 207.44,177.39C207.44,177.57 207.37,177.74 207.24,177.87C207.11,178 206.94,178.07 206.75,178.07Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M207.92,169H182.54C182.45,169 182.36,168.98 182.27,168.94C182.19,168.91 182.12,168.86 182.05,168.79C181.99,168.73 181.94,168.65 181.91,168.57C181.87,168.49 181.86,168.4 181.86,168.31C181.86,168.22 181.87,168.13 181.91,168.05C181.94,167.96 181.99,167.89 182.05,167.83C182.12,167.76 182.19,167.71 182.27,167.68C182.36,167.65 182.45,167.63 182.54,167.63H207.58C208.37,166.76 208.81,165.63 208.81,164.46C208.81,163.28 208.37,162.15 207.58,161.28H182.54C182.37,161.26 182.22,161.18 182.11,161.05C182,160.93 181.94,160.76 181.94,160.6C181.94,160.43 182,160.27 182.11,160.14C182.22,160.02 182.37,159.94 182.54,159.91H207.87C207.96,159.91 208.05,159.93 208.13,159.97C208.21,160 208.29,160.05 208.35,160.12C208.93,160.69 209.38,161.36 209.69,162.11C210,162.85 210.15,163.65 210.15,164.46C210.15,165.26 210,166.06 209.69,166.8C209.38,167.55 208.93,168.23 208.35,168.79C208.24,168.91 208.08,168.99 207.92,169Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M183.16,161.29C183.82,162.21 184.18,163.32 184.18,164.46C184.18,165.59 183.82,166.7 183.16,167.62"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M126.59,113.66L133.02,115.07"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.9,127.53C105.78,130.23 94.22,122.66 95.4,119.76C100.31,107.91 105.41,91.5 107.12,87.94C110.32,81.26 104.85,72.39 119.64,76.08C120.6,76.07 121.55,75.85 122.42,75.44C122.79,75.3 123.19,75.3 123.55,75.46C123.91,75.61 124.2,75.9 124.35,76.26C125.02,77.91 126.87,80.62 132.08,82.77C136.45,84.56 138.97,83.15 139.51,81.49C139.58,81.31 139.68,81.16 139.82,81.03C139.95,80.9 140.11,80.8 140.28,80.74C140.46,80.68 140.65,80.65 140.83,80.67C141.01,80.69 141.19,80.75 141.35,80.85C142.57,81.58 143.67,82.5 144.6,83.57C148.64,85.79 151.11,94.09 149.86,100.06C148.46,106.82 146.02,119.85 146.02,119.85C145.46,122.58 145.3,125.37 145.55,128.15C146.14,131.85 144.04,133.13 144.04,133.13C144.04,133.13 141.9,125.81 127.9,127.53Z"
      android:fillColor="#64589E"/>
  <path
      android:pathData="M119.71,76.88C119.71,76.88 123.26,84.88 133.78,86.98C143.06,88.81 143.75,83.69 143.75,83.69"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M150.81,57.25C150.15,56.83 149.39,56.58 148.6,56.53C147.82,56.48 147.03,56.63 146.32,56.97"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M127.81,66.03C127.77,66.19 127.73,66.35 127.69,66.52C123.67,62.74 121.07,55.95 121.43,49.21C121.8,41.19 130.41,34.81 138.71,36.66C139.05,36.75 139.4,36.82 139.75,36.89C140.19,36.98 140.64,37.07 141.08,37.19C156.27,41.4 152.86,53.31 151.83,56.36C152.55,52.32 151.92,47.94 146.35,45.74C139.27,42.95 131.06,46.09 128,52.31C127.51,53.33 127.14,54.4 126.89,55.49L125.48,55.83L124.28,56.99L123.89,58.33L124.02,59.23L124.63,60.7L125.87,62.23L127.06,63.31C127.25,64.24 127.51,65.15 127.81,66.03Z"
      android:fillColor="#CC8A52"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M130.28,70.59C132.09,73.06 134.4,75.04 136.87,75.59C141.61,76.66 147.85,71.87 149.4,63.81C149.77,61.94 151.33,60.36 152.21,56.51"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M123.97,75.81L127.84,66.16L127.07,63.37C126.65,62.93 121.63,58.81 125.16,56.07C125.67,55.72 126.27,55.51 126.89,55.46"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M150.92,188.35H92.14C91.28,174.51 92.39,160.61 95.44,147.08C97.27,139.5 102.97,131.04 102.97,131.04L137.18,131.75C139.69,137.1 141.42,142.79 142.32,148.63C145.23,166.82 146.97,174.86 150.92,188.35Z"
      android:fillColor="#595050"/>
  <path
      android:pathData="M144.9,133.67C144.9,133.67 146.08,139.76 149.97,141.22C160.18,145.01 178.68,118.38 178.68,118.38L185.77,110.45M150.21,128.63L172.43,114.11C172.43,114.11 172.96,112.27 175.14,109.97"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M183.49,81.12C186.91,77.04 192.1,75.72 196.24,77.49C191.95,74.35 185.67,75.45 181.74,80.13C178.13,84.45 177.69,90.49 180.39,94.63L180.7,95.06C181.04,95.56 181.44,96.03 181.88,96.46C182.74,97.25 183.73,97.88 184.81,98.31C184.43,98.05 184.06,97.76 183.72,97.44C183.24,97.02 182.82,96.55 182.44,96.04C182.33,95.91 182.24,95.77 182.14,95.63C179.43,91.48 179.87,85.44 183.49,81.12ZM186.98,81.18C188.43,80 190.24,79.37 192.11,79.4C193.98,79.42 195.77,80.11 197.19,81.33C198,82.1 198.64,83.03 199.08,84.07C199.51,85.1 199.73,86.21 199.71,87.33C199.74,89.74 198.9,92.07 197.34,93.91C195.33,96.31 192.51,97.47 189.88,97.27C192.51,97.45 195.31,96.27 197.29,93.91C198.85,92.07 199.69,89.74 199.66,87.33C199.68,86.21 199.46,85.1 199.03,84.07C198.6,83.03 197.95,82.1 197.14,81.33C195.73,80.12 193.94,79.44 192.09,79.42C190.23,79.39 188.43,80.01 186.98,81.18C186.98,81.18 186.98,81.18 186.97,81.18C186.98,81.18 186.98,81.18 186.98,81.18Z"
      android:fillColor="#928EBA"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M103.07,131.84L137.09,132.65"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M183.49,81.12C179.87,85.44 179.43,91.48 182.14,95.63C182.24,95.77 182.33,95.91 182.44,96.04C182.82,96.55 183.24,97.02 183.72,97.44C184.75,98.4 185.98,99.11 187.32,99.53C188.66,99.96 190.08,100.08 191.47,99.89C194.48,99.48 197.22,97.93 199.13,95.57C201.15,93.18 202.25,90.15 202.23,87.02C202.24,85.57 201.95,84.13 201.38,82.79C200.82,81.45 199.98,80.24 198.93,79.23C194.62,75.25 187.7,76.09 183.49,81.12ZM186.98,81.18C188.43,80 190.24,79.37 192.11,79.4C193.98,79.42 195.77,80.11 197.19,81.33C198,82.1 198.64,83.03 199.08,84.07C199.51,85.1 199.73,86.21 199.71,87.33C199.74,89.74 198.9,92.07 197.34,93.91C194.1,97.77 188.78,98.42 185.49,95.35C182.19,92.28 182.11,86.64 185.34,82.78C185.82,82.18 186.37,81.64 186.98,81.18Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M186.98,81.18C186.36,81.64 185.79,82.18 185.3,82.78C182.06,86.64 182.12,92.26 185.44,95.36C188.76,98.45 194.09,97.73 197.29,93.91C198.85,92.07 199.69,89.74 199.66,87.33C199.68,86.21 199.46,85.1 199.03,84.07C198.6,83.03 197.95,82.1 197.14,81.33C195.73,80.12 193.94,79.45 192.09,79.42C190.23,79.39 188.42,80.01 186.98,81.18Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M186.78,96.04C188.48,96.64 190.33,96.69 192.06,96.17C193.79,95.66 195.31,94.62 196.41,93.19C197.97,91.35 198.81,89.01 198.79,86.61C198.79,84.86 198.24,83.16 197.23,81.74C196.95,81.34 196.62,80.98 196.26,80.64"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M176.57,116.36L176.1,116.11C175.76,115.93 175.5,115.61 175.39,115.24C175.28,114.87 175.32,114.46 175.51,114.12L177.78,109.59L179.83,109.13L181.18,110.18L181.25,110.19L178.52,115.66C178.43,115.84 178.32,115.99 178.18,116.12C178.04,116.25 177.87,116.35 177.69,116.42C177.51,116.48 177.32,116.51 177.13,116.5C176.93,116.49 176.74,116.44 176.57,116.36ZM185.72,101.26L186.88,98.94L183.89,97.41L182.52,100.14L185.72,101.26Z"
      android:fillColor="#595050"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M181.18,110.19L179.83,109.13L175.8,110.03C175.69,110.06 175.58,110.06 175.47,110.04C175.36,110.02 175.25,109.98 175.15,109.92C175.06,109.86 174.98,109.78 174.91,109.69C174.85,109.59 174.8,109.49 174.78,109.38C174.76,109.27 174.75,109.16 174.77,109.05C174.79,108.94 174.84,108.84 174.9,108.75C174.76,108.72 174.63,108.66 174.53,108.56C174.42,108.47 174.34,108.35 174.29,108.22C174.23,108.04 174.22,107.84 174.28,107.65C174.34,107.47 174.46,107.31 174.62,107.2C174.55,107.12 174.49,107.04 174.45,106.95C174.34,106.74 174.31,106.49 174.36,106.26C174.42,106.03 174.56,105.83 174.76,105.69L176.16,104.76C176.03,104.74 175.92,104.7 175.82,104.63C175.71,104.56 175.63,104.47 175.56,104.36C175.45,104.18 175.41,103.95 175.45,103.74C175.48,103.52 175.6,103.32 175.76,103.18L180.35,99.96C180.52,99.82 180.71,99.74 180.92,99.71C181.13,99.68 181.34,99.71 181.54,99.79L181.68,99.85L188.79,102.33C189,102.41 189.18,102.53 189.34,102.69C189.49,102.85 189.62,103.03 189.7,103.24C189.78,103.44 189.83,103.66 189.82,103.88C189.82,104.1 189.78,104.32 189.69,104.52C189.6,104.75 189.46,104.95 189.28,105.11C189.1,105.28 188.88,105.39 188.64,105.46C188.79,105.68 188.88,105.95 188.91,106.22C188.93,106.49 188.9,106.77 188.79,107.02C188.65,107.36 188.4,107.65 188.08,107.83C187.76,108.01 187.39,108.09 187.03,108.04C187.14,108.27 187.2,108.52 187.2,108.78C187.2,109.03 187.14,109.28 187.03,109.51C186.9,109.77 186.72,109.99 186.49,110.15C186.25,110.32 185.98,110.42 185.7,110.45"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M176.16,104.77L180.47,102.25C180.65,102.15 180.86,102.1 181.06,102.1C181.27,102.1 181.48,102.15 181.66,102.25L187.69,105.05"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M174.62,107.21L180.54,104.71C180.77,104.61 181.01,104.58 181.26,104.6C181.5,104.62 181.73,104.7 181.94,104.83L185.97,107.2"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M174.9,108.76L180.28,106.98C180.49,106.92 180.7,106.91 180.91,106.96C181.12,107 181.31,107.11 181.47,107.26L184.43,109.21"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M139.99,67.96C142.23,68.61 141.04,70.18 141.04,70.18C141.04,70.18 140.9,71.63 138.3,70.71C136.81,70.16 136.91,69.05 136.91,69.05C137.23,68.55 137.7,68.17 138.26,67.98C138.82,67.78 139.43,67.77 139.99,67.96ZM137.25,69.19L140.81,70.12C141.04,68.98 139.55,67.83 137.25,69.19Z"
      android:fillColor="#808080"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M137.25,69.19L140.81,70.12C141.04,68.98 139.55,67.83 137.25,69.19Z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#323739"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M99.68,136.85L139.09,137.82"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.13,137.32C107.13,137.32 106.15,141.04 104.93,149.5C102.81,164.15 104.55,188.35 104.55,188.35"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#808080"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M107.34,172.41C108.32,172.41 109.11,171.62 109.11,170.64C109.11,169.67 108.32,168.88 107.34,168.88C106.36,168.88 105.57,169.67 105.57,170.64C105.57,171.62 106.36,172.41 107.34,172.41Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M107.83,179.04C108.8,179.04 109.59,178.25 109.59,177.27C109.59,176.3 108.8,175.51 107.83,175.51C106.85,175.51 106.06,176.3 106.06,177.27C106.06,178.25 106.85,179.04 107.83,179.04Z"
      android:fillColor="#808080"/>
  <path
      android:pathData="M108.19,185.66C109.17,185.66 109.96,184.87 109.96,183.9C109.96,182.92 109.17,182.13 108.19,182.13C107.22,182.13 106.43,182.92 106.43,183.9C106.43,184.87 107.22,185.66 108.19,185.66Z"
      android:fillColor="#808080"/>
  <group>
    <clip-path
        android:pathData="M159.8,120.54C159.65,120.3 158.09,117.81 154.24,119.13C150.38,120.46 147.13,124.43 145.42,127.02C145.42,127.4 145.49,127.78 145.55,128.13C146.14,131.84 144.04,133.12 144.04,133.12C144.04,133.12 141.9,125.81 127.9,127.51C105.79,130.22 94.22,122.64 95.42,119.76C96.2,117.88 96.98,115.87 97.79,113.83C95.33,116.03 94.11,116.21 94.11,116.21C94.46,113.93 94.26,111.61 93.54,109.43C92.82,107.24 91.59,105.26 89.96,103.64C85.22,98.96 82.55,99.37 82.55,99.37C82.55,99.37 89.59,92 93.22,85.6C101.44,71.06 110.43,72.45 113.78,74.15C115.21,74.79 116.68,75.34 118.19,75.81C118.65,75.9 119.12,76.01 119.63,76.14C120.6,76.13 121.55,75.92 122.42,75.51C122.79,75.36 123.19,75.37 123.55,75.52C123.92,75.67 124.2,75.96 124.35,76.33C125.02,77.97 126.86,80.69 132.08,82.82C136.44,84.61 138.97,83.2 139.51,81.54C139.58,81.37 139.69,81.22 139.82,81.09C139.95,80.96 140.11,80.87 140.29,80.81C140.46,80.75 140.65,80.73 140.83,80.74C141.01,80.76 141.19,80.82 141.35,80.91C142.3,81.41 143.15,82.06 143.87,82.86C146,82.69 154.79,82.67 156.07,92.84C157.64,104.99 159.7,119.82 159.8,120.54Z"/>
    <path
        android:pathData="M167.19,101.29L162.84,86.83L159.29,86.68C147.43,93.16 132.59,91.56 120.36,87.67C120.36,87.67 119.07,76.43 118.29,71.41L115.41,71.06C115.41,71.99 115.51,73.33 115.51,74.25C115.54,78.03 115.21,81.79 114.52,85.5C111.79,84.55 109.15,83.36 106.63,81.95C103.86,80.2 98.66,76.17 98.66,76.17L95.1,80.23L98.01,82.92C102.25,87.34 107.03,91.2 112.24,94.43C112.24,94.43 103.69,116.11 94.23,126.24C92.44,128.11 99.41,135.08 99.41,135.08L109.09,128.57C114.74,120.76 120.01,98.59 120.01,98.59C130.02,103.29 140.96,105.68 152.02,105.61C157.29,105.45 162.45,104.1 167.12,101.65"
        android:fillColor="#928EBA"/>
  </group>
  <path
      android:pathData="M108.57,87.57L98.26,113.41"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M145.23,90.35C147.42,95.67 148.03,101.51 146.97,107.16C145.7,114.08 145.23,121.12 145.56,128.14"
      android:strokeLineJoin="round"
      android:strokeWidth="0.4"
      android:fillColor="#00000000"
      android:strokeColor="#595050"
      android:strokeLineCap="round"/>
</vector>
