<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/seek_bar_area"
    android:layout_width="match_parent"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:visibility="visible"
    tools:visibility="visible">

    <Space
        android:id="@+id/space_play_info"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp12"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <FrameLayout
        android:id="@+id/seek_bar_content"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/space_play_info"
        app:layout_constraintStart_toEndOf="@+id/playProgress"
        app:layout_constraintEnd_toStartOf="@+id/play_total_duration"
        android:clipChildren="false"
        android:clipToPadding="false">

        <com.oplus.anim.EffectiveAnimationView
            android:id="@+id/amp_loading"
            android:layout_width="@dimen/dp28"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:forceDarkAllowed="false"
            android:layout_marginStart="@dimen/dp10"
            android:visibility="gone"
            app:anim_loop="true"
            app:layout_constraintTop_toTopOf="@id/record_title"
            app:layout_constraintBottom_toBottomOf="@id/record_title"
            app:layout_constraintStart_toStartOf="parent"/>

        <com.soundrecorder.browsefile.view.AmplitudeSeekBar
            android:id="@+id/playAmplitudeBar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp24"
            android:layout_gravity="center_vertical"
            android:forceDarkAllowed="false"
            app:amplitude_color="?attr/couiColorLabelPrimary"
            app:amplitude_unselect_color="?attr/couiColorContainer16"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginStart="@dimen/dp10"
            android:layout_marginEnd="@dimen/dp12"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:point_color="?attr/couiColorRed" />

    </FrameLayout>

    <TextView
        android:id="@+id/playProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        app:layout_constraintTop_toBottomOf="@id/space_play_info"
        app:layout_constraintStart_toStartOf="parent"
        android:textColor="@color/item_record_extra_info_color"
        app:layout_constraintTop_toTopOf="@+id/seek_bar_content"
        app:layout_constraintBottom_toBottomOf="@+id/seek_bar_content"
        style="@style/couiTextAppearanceArticleBody"
        android:textSize="@dimen/text_item_play_progress_size"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        android:textFontWeight="500"
        android:lineHeight="@dimen/sp16"
        tools:text="00:00" />

    <TextView
        android:id="@+id/play_total_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/space_play_info"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/seek_bar_content"
        app:layout_constraintTop_toTopOf="@+id/seek_bar_content"
        app:layout_constraintBottom_toBottomOf="@+id/seek_bar_content"
        android:textSize="@dimen/text_item_play_progress_size"
        android:text="@{itemRecord.durationText()}"
        android:textColor="@color/item_record_extra_info_color"
        android:lineHeight="@dimen/sp16"
        style="@style/couiTextAppearanceArticleBody"
        android:fontFamily="sys-sans-en"
        android:textFontWeight="500"
        android:fontFeatureSettings="tnum"
        tools:text="00:00" />
</merge>