<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#00000000"
    android:layoutDirection="locale">

    <com.soundrecorder.browsefile.drag.view.ShadowWrapperView
        android:id="@+id/shadow_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/drag_shadow_item_layout_margin_top"
        android:layout_marginEnd="@dimen/drag_shadow_item_layout_margin_end"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/drag_shadow_item_layout_padding" />

    <com.soundrecorder.browsefile.drag.view.ShadowRedDotView
        android:id="@+id/dragshadow_item_count"
        style="?attr/couiHintRedDotStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/drag_shadow_item_layout_padding"
        android:layout_marginEnd="@dimen/drag_shadow_item_layout_padding"
        app:couiHeight="@dimen/drag_shadow_dot_height"
        app:couiCornerRadius="@dimen/drag_shadow_dot_radius"
        app:couiSmallWidth="@dimen/drag_shadow_dot_height"
        app:couiMediumWidth="@dimen/drag_shadow_dot_m_width"
        app:couiLargeWidth="@dimen/drag_shadow_dot_l_width"
        app:couiHintRedDotColor="#00000000"
        android:layout_gravity="end|top" />

</FrameLayout>