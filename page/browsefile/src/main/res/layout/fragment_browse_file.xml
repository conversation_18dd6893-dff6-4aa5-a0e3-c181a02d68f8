<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewModel"
            type="com.soundrecorder.browsefile.home.load.BrowseViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/root_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/common_background_color"
        android:tag="BrowseFragment">

        <FrameLayout
            android:id="@+id/mFootView"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.soundrecorder.base.refresh.BounceLayout
                android:id="@+id/bounce_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:importantForAccessibility="no">

                <androidx.recyclerview.widget.COUIRecyclerView
                    android:id="@+id/mListView"
                    android:layout_marginTop="@dimen/dp2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp16"
                    android:divider="@null"
                    android:importantForAccessibility="no"
                    android:requiresFadingEdge="vertical"
                    android:scrollbars="none"
                    tools:listitem="@layout/item_browse" />

            </com.soundrecorder.base.refresh.BounceLayout>

            <FrameLayout
                android:id="@+id/pull_down_refresh_root"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <ViewStub
                android:id="@+id/mEmptyViewStub"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:inflatedId="@+id/empty_recorder_layout"
                android:layout="@layout/layout_empty_recorder"
                android:visibility="gone"
                tools:visibility="visible" />

            <ViewStub
                android:id="@+id/mLoadingStub"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout="@layout/loading_view"
                android:visibility="gone" />

            <ViewStub
                android:id="@+id/mPermissionDeniedStub"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:inflatedId="@+id/permission_denied_layout"
                android:layout="@layout/layout_permission_denied"
                android:visibility="gone"
                tools:visibility="gone" />

            <ViewStub
                android:id="@+id/mViewStub"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:inflatedId="@+id/navi_layout"
                android:layout="@layout/navigation_view"
                android:visibility="gone" />

<!--            <com.coui.appcompat.bottomnavigation.COUINavigationView-->
<!--                android:id="@+id/navi_menu_tool"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_gravity="bottom"-->
<!--                app:couiNaviMenu="@menu/bottom_navigation_menu"-->
<!--                app:navigationType="tool"-->
<!--                app:couiNaviTextColor="@color/selector_navigation_text_color" />-->

        </FrameLayout>

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/abl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/common_background_color"
            app:elevation="0dp">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/browse_toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/recorder_toolbar_height"
                app:supportTitle="@string/app_name_main"
                app:supportTitleTextAppearance="@style/Browse_Title_Semibold"
                app:titleCenter="false" />

            <LinearLayout
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/main_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingStart="@dimen/dp16"
                    android:paddingEnd="@dimen/dp16">

                    <TextView
                        android:id="@+id/toolbar_title"
                        style="@style/Browse_Title_OS14"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/toolbar_title_init_height"
                        android:layout_weight="9"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:text="@string/app_name_main"
                        tools:visibility="visible" />

                    <com.coui.appcompat.rotateview.COUIRotateView
                        android:id="@+id/folder_name_rotate_view"
                        android:layout_width="@dimen/dp24"
                        android:layout_height="@dimen/dp24"
                        android:layout_weight="1"
                        android:clickable="false"
                        android:contentDescription="@string/recording_group"
                        android:focusable="false"
                        app:supportExpanded="true"
                        android:src="@drawable/change_folder_expander_anim"
                        android:visibility="gone"
                        tools:visibility="visible" />
                </LinearLayout>

                <com.soundrecorder.browsefile.home.view.SubTitleLayout
                    android:id="@+id/toolbar_subtitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp16"
                    android:layout_marginEnd="@dimen/dp16"
                    android:layout_marginBottom="@dimen/sub_title_margin_bottom"
                    android:visibility="visible">

                    <TextView
                        android:id="@+id/subtitle_desc"
                        style="@style/SubtitleStyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:clickable="false"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:singleLine="true"
                        app:layout_constraintEnd_toStartOf="@+id/subtitle_event"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        tools:text="同步暂停，同步暂停同步暂停，同步暂停，同步暂停同步暂停，同步暂停同步暂停，" />

                    <TextView
                        android:id="@+id/tv_recycle_subtitle_desc"
                        style="@style/SubtitleStyle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:clickable="false"
                        android:ellipsize="end"
                        android:gravity="center"
                        android:singleLine="true"
                        app:layout_constraintEnd_toStartOf="@+id/subtitle_event"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        android:visibility="gone"
                        tools:text="1个录音" />

                    <TextView
                        android:id="@+id/subtitle_event"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toTopOf="@id/subtitle_desc"
                        app:layout_constraintBottom_toBottomOf="@id/subtitle_desc"
                        android:gravity="center"
                        style="@style/couiTextAppearanceBody"
                        android:textColor="?attr/couiColorLink"
                        android:singleLine="true"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/subtitle_desc"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        tools:text="点击" />

                </com.soundrecorder.browsefile.home.view.SubTitleLayout>

                <!--label-->
                <include
                    android:id="@+id/group_label_container"
                    layout="@layout/layout_group_label"/>

                <View
                    android:id="@+id/divider_line"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/divider_background_height"
                    android:layout_gravity="bottom"
                    android:layout_marginLeft="@dimen/common_margin"
                    android:layout_marginRight="@dimen/common_margin"
                    android:alpha="0"
                    android:background="?attr/couiColorDivider"
                    android:forceDarkAllowed="false" />
            </LinearLayout>

        </com.google.android.material.appbar.AppBarLayout>

        <View
            android:id="@+id/v_delay_jump_mask"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:visibility="gone" />

        <FrameLayout
            android:id="@+id/gradientBackground"
            android:layout_width="match_parent"
            android:layout_height="@dimen/recorder_height"
            android:layout_gravity="bottom"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:background="@color/color_transparent"
            android:transitionName="sharedView"
            tools:visibility="visible">

            <com.soundrecorder.common.widget.glow.ShadowImageView
                android:layout_width="@dimen/circle_record_button_diam"
                android:layout_height="@dimen/circle_record_button_diam"
                android:background="@color/color_transparent"
                android:layout_marginBottom="@dimen/dp40"
                android:layout_gravity="bottom|center_horizontal"
                app:shadow_blur_radius="@dimen/dp28"
                app:shadow_offsetY="@dimen/dp14"
                app:shadow_offsetX="@dimen/dp0"
                app:shadow_reference="@id/red_circle_icon"
                app:shadow_color="@color/record_btn_shadow_red_color"/>


            <FrameLayout
                android:id="@+id/middle_control"
                android:layout_width="@dimen/circle_record_button_diam"
                android:layout_height="@dimen/circle_record_button_diam"
                android:layout_gravity="bottom|center_horizontal"
                android:elevation="@dimen/dp8.5"
                android:layout_marginBottom="@dimen/dp40"
                android:contentDescription="@string/recording_start"
                android:transitionName="sharedControlView"
                android:clipChildren="false"
                android:clipToPadding="false">

                <com.soundrecorder.common.widget.AnimatedCircleButton
                    android:id="@+id/red_circle_icon"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:contentDescription="@string/recording_start"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_record_icon"
                    android:layout_gravity="center"
                    android:translationZ="@dimen/dp0"
                    app:glow_enable="true"
                    app:reference_id="@id/red_circle_icon_reference"
                    app:circle_color="@color/coui_color_container_theme_red"
                    app:circle_radius="@dimen/circle_record_button_radius"
                    app:state_change="false"
                    app:vibrate_toggle="true"
                    app:circle_gradient_start_color="@color/record_btn_gradient_start_color"
                    app:circle_gradient_end_color="@color/record_btn_gradient_end_color" />

                <ImageButton
                    android:id="@+id/red_circle_icon_reference"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clickable="false"
                    android:background="@color/color_transparent"
                    android:layout_gravity="center"
                    android:translationZ="@dimen/dp1"/>
            </FrameLayout>
            <View
                android:id="@+id/divider_line_record_panel"
                android:layout_width="match_parent"
                android:layout_height="@dimen/toolbar_divider_height"
                android:background="?attr/couiColorDivider"
                android:forceDarkAllowed="false"
                android:visibility="gone"/>
        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_search_box"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"/>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>