<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_marginBottom="@dimen/dp4">

    <com.soundrecorder.common.widget.CircleTextImage
        android:id="@+id/civ_avatar"
        android:layout_width="86dp"
        android:layout_height="86dp"
        android:layout_gravity="center_horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:circle_text_color="@color/White"
        app:sub_first_character="true"
        app:use_random_color="true"
        app:circle_text_size="28sp"/>

    <TextView
        android:id="@+id/tv_caller_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="老妈"
        android:minHeight="40dp"
        android:layout_marginTop="@dimen/dp12"
        android:textSize="28sp"
        android:gravity="center"
        android:textFontWeight="500"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/civ_avatar"
        android:fontFamily="sans-serif-medium"
        android:textColor="@color/coui_color_label_primary"/>
    <TextView
        android:id="@+id/tv_record_count"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="1个录音"
        android:minHeight="20dp"
        android:layout_marginTop="@dimen/dp12"
        android:textSize="@dimen/sp14"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_caller_name"
        android:fontFamily="sans-serif-regular"
        android:textColor="@color/coui_color_label_primary"/>

</androidx.constraintlayout.widget.ConstraintLayout>