<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/root_view">

        <FrameLayout
            android:id="@+id/background_mask_container"
            android:background="@color/common_background_color"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/background_mask"
                android:paddingTop="@dimen/dp8"
                android:visibility="gone"
                android:paddingHorizontal="@dimen/dp16"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <TextView
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/clear_history"
                    app:layout_constraintBottom_toBottomOf="@+id/clear_history"
                    android:textAppearance="?attr/couiTextButtonM"
                    android:textColor="?attr/couiColorLabelPrimary"
                    android:text="@string/search_history_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/clear_history"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:src="@drawable/icon_clear_search_history"
                    android:layout_width="@dimen/dp32"
                    android:layout_height="@dimen/dp32"/>

                <ScrollView
                    app:layout_constraintTop_toBottomOf="@id/clear_history"
                    app:layout_constraintBottom_toBottomOf="parent"
                    android:layout_width="match_parent"
                    android:layout_height="0dp">
                    <com.coui.appcompat.chip.COUIChipGroup
                        android:id="@+id/search_history"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp4"
                        android:paddingBottom="24dp"
                        app:singleLine="false"
                        app:isCollapsable="true"
                        app:isCollapsed="true"
                        app:collapsedMaxRows="2">
                    </com.coui.appcompat.chip.COUIChipGroup>
                </ScrollView>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>

        <include
            android:id="@+id/include"
            layout="@layout/search_result_panel" />

    </FrameLayout>
</layout>
