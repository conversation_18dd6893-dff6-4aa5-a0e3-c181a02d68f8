<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/btn_all_recorder"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/dp44"
    android:paddingStart="@dimen/dp16"
    android:paddingEnd="@dimen/dp16">

    <ImageView
        android:id="@+id/mImage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:src="@drawable/ic_all_recorder"
        android:layout_marginEnd="@dimen/dp16"/>

    <TextView
        android:id="@+id/tv_recorder"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/dp36"
        android:lines="1"
        android:ellipsize="end"
        android:singleLine="true"
        android:text="@string/all_the_recordings"
        android:layout_centerVertical="true"
        android:layout_toEndOf="@+id/mImage"
        android:textColor="@color/coui_color_primary_neutral"
        android:fontFamily="sans-serif-medium"
        android:textSize="@dimen/dp16" />

    <RadioButton
        android:id="@+id/radio_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:button="@drawable/coui_btn_check_mark"
        android:clickable="false"
        android:focusable="false"
        android:forceDarkAllowed="false" />

</RelativeLayout>