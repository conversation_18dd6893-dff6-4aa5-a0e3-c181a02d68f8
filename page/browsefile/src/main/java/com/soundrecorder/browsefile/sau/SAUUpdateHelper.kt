/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SAUHelper
 * Description:
 * Version: 1.0
 * Date: 2022/10/19
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/10/19 1.0 create
 */

package com.soundrecorder.browsefile.sau

import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import com.oplusos.sauaar.client.SauSelfUpdateAgent
import com.soundrecorder.base.utils.ActivityRunnable
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.common.permission.PermissionUtils

class SAUUpdateHelper {
    private val mLogTag = "SAUHelper"
    private var mMainHandler = Handler(Looper.getMainLooper())

    fun runSauUpdate(activity: Activity, delayMill: Long) {
        mMainHandler.postDelayed(object : ActivityRunnable<Activity?>("sau check", activity) {
            override fun run(act: Activity?) {
                DebugUtil.i(mLogTag, "delayOnCreate sau check")
                act?.let {
                    sauCheckUpdate(it)
                }
            }
        }, delayMill)
    }

   private fun sauCheckUpdate(context: Context) {
        try {
            if (PermissionUtils.hasReadAudioPermission()) {
                DebugUtil.i(mLogTag, "sauCheckUpdate begin")
                val sauSelfUpdateAgent = SauSelfUpdateAgent.SauSelfUpdateBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Center)
                    .setTextColorId(context.getColor(com.support.dialog.R.color.coui_alert_dialog_content_text_color))
                    .build()
                sauSelfUpdateAgent.sauCheckSelfUpdate()
                DebugUtil.i(mLogTag, "sauCheckUpdate end")
            }
        } catch (ignore: Throwable) {
            DebugUtil.e(mLogTag, "exception in sauCheckUpdate $ignore")
        }
    }

    fun release() {
        mMainHandler.removeCallbacksAndMessages(null)
    }
}