/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertInfo
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/19
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.load

import com.soundrecorder.base.utils.DebugUtil
import java.util.concurrent.ConcurrentHashMap

object ConvertingInfo {
    private const val TAG = "ConvertingInfo"

    private val cacheConvertingIds = mutableListOf<Long>()
    private var cacheSmartNamingIds = mutableListOf<Long>()
    private var cacheSmartNameResult = ConcurrentHashMap<Long, String>()

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun onConvertStatusChanged(mediaId: Long, status: Boolean) {
        DebugUtil.d(TAG, "onConvertStatusChanged mediaId:$mediaId, status:$status")
        if (status) {
            if (!cacheConvertingIds.contains(mediaId)) {
                cacheConvertingIds.add(mediaId)
            }
        } else {
            cacheConvertingIds.remove(mediaId)
        }
    }

    fun isConverting(mediaId: Long): Boolean {
        return cacheConvertingIds.contains(mediaId)
    }

    fun removeConvertMediaId(mediaId: Long) {
        if (cacheConvertingIds.contains(mediaId)) {
            DebugUtil.d(TAG, "removeConvertMediaId, mediaId:$mediaId")
            cacheConvertingIds.remove(mediaId)
        }
    }

    @JvmStatic
    fun clearConvertingInfo() {
        cacheConvertingIds.clear()
        cacheSmartNamingIds.clear()
        cacheSmartNameResult.clear()
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun onSmartNameStatusChanged(mediaId: Long, status: Boolean) {
        removeConvertMediaId(mediaId)
        if (status) {
            if (!cacheSmartNamingIds.contains(mediaId)) {
                cacheSmartNamingIds.add(mediaId)
            }
        } else {
            cacheSmartNamingIds.remove(mediaId)
        }
    }

    fun isSmartNaming(mediaId: Long): Boolean {
        return cacheSmartNamingIds.contains(mediaId)
    }

    fun removeSmartNameMediaId(recordId: Long) {
        if (cacheSmartNamingIds.contains(recordId)) {
            DebugUtil.d(TAG, "removeSmartNameMediaId:$recordId")
            cacheSmartNamingIds.remove(recordId)
        }
    }

    fun addSmartNameResult(mediaId: Long, resultName: String) {
        if (!cacheSmartNameResult.containsKey(mediaId)) {
            DebugUtil.d(TAG, "addSmartNameResult, mediaId:$mediaId, resultName:$resultName")
            cacheSmartNameResult[mediaId] = resultName
            //cacheSmartNameResult.remove(mediaId)
        }
    }

    fun removeSmartNameResult(mediaId: Long) {
        if (cacheSmartNameResult.containsKey(mediaId)) {
            DebugUtil.d(TAG, "removeSmartNameResult:$mediaId")
            cacheSmartNameResult.remove(mediaId)
        }
    }

    fun isContainsSmartNameResult(mediaId: Long): Boolean {
        return cacheSmartNameResult.containsKey(mediaId)
    }

    fun getSmartNameResult(mediaId: Long): String? {
        if (isContainsSmartNameResult(mediaId)) {
            return cacheSmartNameResult[mediaId]
        }
        return null
    }
}