/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: QuestionnaireGuideTipView
 Description:
 Version: 1.0
 Date: 2023/05/30 1.0
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2023/05/30 1.0 create
 */

package com.soundrecorder.browsefile.home.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import androidx.cardview.widget.CardView
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.LifecycleCoroutineScope
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.modulerouter.FeedBackInterface
import com.soundrecorder.modulerouter.questionnaire.QuestionCDPAnimCallback
import com.soundrecorder.modulerouter.questionnaire.QuestionCDPCallback
import com.soundrecorder.modulerouter.questionnaire.QuestionnaireInterface
import com.soundrecorder.modulerouter.utils.Injector

@SuppressLint("ViewConstructor")
class QuestionnaireGuideTipView(
    context: Context,
    private val scope: LifecycleCoroutineScope,
    val callback: QuestionCDPCallback?,
    private val animCallBack: QuestionCDPAnimCallback?
) : CardView(context) {

    private var cdpView: View? = null
    private val feedBackApi by lazy {
        Injector.injectFactory<FeedBackInterface>()
    }

    private val questionApi by lazy {
        Injector.injectFactory<QuestionnaireInterface>()
    }

    init {
        //需要显示卡片的时候设置主题色
        feedBackApi?.setFeedbackThemeColor(
            COUIContextUtil.getAttrColor(
                context,
                com.support.appcompat.R.attr.couiColorPrimary
            )
        )
        cdpView = questionApi?.initView(context, this)
        cdpView?.let {
            elevation = 0f
            setBackgroundColor(Color.TRANSPARENT)
            radius = context.resources.getDimension(com.soundrecorder.common.R.dimen.dp12)
            addView(it)
            layoutParams = LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            updateLayoutParams<LayoutParams> {
                setMargins(
                    0,
                    context.resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp8),
                    0,
                    context.resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp4)
                )
            }
            questionApi?.setCDPCallBack(it, callback, animCallBack)
            refreshViewData()
        }
    }

    /**
     * 获取问卷数据
     */
    fun refreshViewData() {
        val cdpView = cdpView ?: return
        questionApi?.updateSpace(scope, cdpView)
    }

    fun release() {
        questionApi?.releaseSpace(cdpView)
        cdpView = null
    }
}