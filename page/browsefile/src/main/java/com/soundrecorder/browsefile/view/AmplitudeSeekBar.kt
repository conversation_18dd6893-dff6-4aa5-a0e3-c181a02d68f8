/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AmplitudeSeekBar.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/07/08
 ** Author      : W9035969(<EMAIL>)
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9035969     2025/07/08     1.0      create
 ***********************************************************************/
package com.soundrecorder.browsefile.view

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.common.constant.Constants
import kotlinx.coroutines.Runnable
import java.lang.Float.isNaN
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.Collections
import kotlin.math.abs
import kotlin.math.ceil
import kotlin.math.min
import kotlin.math.sqrt
import androidx.core.graphics.createBitmap
import com.coui.appcompat.animation.COUILinearInterpolator
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.common.utils.amplitude.AmpSeekbarUtil
import com.soundrecorder.common.utils.amplitude.AmpSeekbarUtil.PERCENT_80

class AmplitudeSeekBar : View {

    companion object {
        private const val TAG: String = "AmplitudeSeekBar"
        private const val NATURE_UNIT_TIME: Int = 70
        /**
         * 波形刻度数量
         */
        private const val NATURE_COUNT: Float = 72f
        /**
         * 波形间隔数量
         */
        private const val NATURE_SPACE_COUNT: Float = NATURE_COUNT - 1
        /**
         * 48 + 47 = 95
         */
        private const val TOTAL_COUNT: Float = NATURE_COUNT + NATURE_SPACE_COUNT
        /**
         * 95 * 70 = 6650
         * the view width could signify time,when mScaleValue equal 1
         */
        private const val NATURE_TIME: Long = (NATURE_COUNT * NATURE_UNIT_TIME).toLong()

        private const val HALF_VALUE: Float = 0.5f

        private const val DRAW_POINT_FRAME_TIME: Int = 18
        private const val DRAW_POINT_FRAME_THRESHOLD: Int = 300
        private const val PRECISION: Int = 9
        private const val CURRENT_LINE_PADDING: Int = 10
        private const val NUM_TWO: Int = 2
        private const val NUMBER_TWO_FLOAT: Float = 2f
        private const val DEFAULT_AMPLITUDE_RADIUS: Float = 16f
        const val ALPHA_255 = 255
        const val DRAG_BAR_ALPHA_START_DURATION = 250L
        const val DRAG_BAR_ALPHA_EXIT_DURATION = 217L

        private val INTERPOLATOR = COUILinearInterpolator()
    }

    private var mAmplitudes: MutableList<Int>? = null

    private var mPointPaint: Paint? = null
    private var mAmplitudePaint: Paint? = null
    private var mUnSelectedPaint: Paint? = null

    private var mPointTime: Long = 0

    private val mListeners: MutableList<PointMoveListener> = ArrayList<PointMoveListener>()
    private var isDragging = false
    private var mWavePadding = 0f
    private var mTotalTime: Long = 0
    private var mPreTime: Long = -1
    private var mReverseLayout = false

    private var mPointX = 0f

    private var mDownX: Float = 0f
    private var mDownY: Float = 0f
    /** 按下时的播放位置*/
    private var downPointX = 0f

    private var mNatureCount = NATURE_COUNT
    private var mTotalCount = TOTAL_COUNT

    private var mDirection: Int = -1
    private var mPreDirection: Int = -1

    private val touchSlop = ViewConfiguration.get(context).scaledTouchSlop

    private var mMinAmpWith: Float = 0f
    private var mAmpWith: Float = 0f
    private var mGap = 0f

    private var mDragStartColor: Int = COUIContextUtil.getColor(context, com.soundrecorder.common.R.color.amp_drag_start_color_half)
    private var mDragEndColor: Int = COUIContextUtil.getColor(context, com.soundrecorder.common.R.color.amp_drag_end_color)

    private val dragRectWidth = context.resources.getDimension(com.soundrecorder.common.R.dimen.dp38)

    private var leftBitmap: Bitmap? = null
    private var rightBitmap: Bitmap? = null
    private var leftCanvas: Canvas? = null
    private var rightCanvas: Canvas? = null
    private var bitmapRect = Rect()
    private var drawBitmapRectF = RectF()

    private var dragAnimStartX = 0f

    private val dragBitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG)
    private val dragAnimPaint = Paint()

    private val colors = intArrayOf(mDragStartColor, mDragEndColor)
    private var gradientRight = LinearGradient(0f, 0f, dragRectWidth, 0f, colors, null, Shader.TileMode.CLAMP)
    private var gradientLeft = LinearGradient(dragRectWidth, 0f, 0f, 0f, colors, null, Shader.TileMode.CLAMP)

    private var lastDragX = -1f
    private var isFirstTime = false

    private var verticalPadding = 0f

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attributeSet: AttributeSet?) : this(context, attributeSet, 0)

    constructor(context: Context, attributeSet: AttributeSet?, defStyleAttr: Int) : super(context, attributeSet, defStyleAttr) {
        initParams(context, attributeSet)
        setWillNotDraw(false)
    }

    private fun initParams(context: Context, attr: AttributeSet?) {
        val array = context.obtainStyledAttributes(attr, R.styleable.AmplitudeSeekBar)
        val pointColor = array.getColor(R.styleable.AmplitudeSeekBar_point_color, Color.BLACK)
        val amplitudeColor = array.getColor(R.styleable.AmplitudeSeekBar_amplitude_color,
            context.getColor(com.support.appcompat.R.color.coui_color_label_primary))
        val amplitudeUnSelectColor = array.getColor(R.styleable.AmplitudeSeekBar_amplitude_unselect_color,
            context.getColor(com.support.appcompat.R.color.coui_color_container16))
        array.recycle()

        mPointPaint = Paint()
        mPointPaint?.color = pointColor
        mPointPaint?.isAntiAlias = true
        mPointPaint?.isDither = true

        mAmplitudePaint = Paint()
        mAmplitudePaint?.color = amplitudeColor
        mAmplitudePaint?.isAntiAlias = true
        mAmplitudePaint?.isDither = true

        mUnSelectedPaint = Paint()
        mUnSelectedPaint?.color = amplitudeUnSelectColor
        mUnSelectedPaint?.isAntiAlias = true
        mUnSelectedPaint?.isDither = true

        mMinAmpWith = context.resources.getDimension(com.soundrecorder.common.R.dimen.dp1)
        mGap = context.resources.getDimension(com.soundrecorder.common.R.dimen.dp0_67)
    }

    fun setReverseLayout(reverseLayout: Boolean) {
        if (reverseLayout == mReverseLayout) {
            return
        }
        mReverseLayout = reverseLayout
        requestLayout()
    }

    fun getReverseLayout(): Boolean {
        return mReverseLayout
    }

    fun isDragging(): Boolean {
        return isDragging
    }

    fun setListenter(listener: PointMoveListener?) {
        if (listener != null) {
            mListeners.add(listener)
        }
    }

    private fun onMoveOnRedBar() {
        if (mListeners.isNullOrEmpty()) {
            return
        }
        for (listener in mListeners) {
            listener.onMoveOnMiddleBar(mPointTime)
        }
    }


    // 淡入动画
    private var fadeInAnimator = ValueAnimator.ofInt(0, ALPHA_255).apply {
        duration = DRAG_BAR_ALPHA_START_DURATION
        interpolator = INTERPOLATOR
        addUpdateListener {
            val alpha = it.animatedValue as Int
            dragAnimPaint.alpha = alpha
            postInvalidateOnAnimation()
        }
        doOnEnd {
            DebugUtil.d(TAG, "fadeInAnimator donEnd")
            dragAnimPaint.alpha = ALPHA_255
            postInvalidateOnAnimation()
        }
        doOnCancel {
            DebugUtil.d(TAG, "fadeInAnimator donCancel")
            dragAnimPaint.alpha = ALPHA_255
            postInvalidateOnAnimation()
        }
    }

    // 淡出动画
    private var fadeOutAnimator = ValueAnimator.ofInt(ALPHA_255, 0).apply {
        duration = DRAG_BAR_ALPHA_EXIT_DURATION
        interpolator = INTERPOLATOR
        addUpdateListener {
            val alpha = it.animatedValue as Int
            dragAnimPaint.alpha = alpha
            postInvalidateOnAnimation()
        }
        doOnEnd {
            DebugUtil.i(TAG, "fadeOutAnimator donEnd")
            dragAnimPaint.alpha = 0
            isDragging = false
            postInvalidateOnAnimation()
        }
        doOnCancel {
            DebugUtil.i(TAG, "fadeOutAnimator donCancel")
            dragAnimPaint.alpha = 0
            isDragging = false
            postInvalidateOnAnimation()
        }
    }

    fun startFadeAnimation() {
        DebugUtil.d(TAG, "startFadeAnimation")
        fadeOutAnimator.cancel()
        fadeInAnimator.cancel()
        fadeInAnimator.start()
    }

    fun exitFadeAnimation() {
        DebugUtil.d(TAG, "exitFadeAnimation")
        fadeInAnimator.cancel()
        fadeOutAnimator.cancel()
        fadeOutAnimator.start()
    }

    fun getPercentDrawHeight(): Float {
        return height * PERCENT_80
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (w > 0 && h > 0) {
            verticalPadding = (h - getPercentDrawHeight()) / NUMBER_TWO_FLOAT
            val viewWidth =  getAvailableWidth()
            val ampAndGapWidth = AmpSeekbarUtil.calculateAmpWidthAndGapWidthEqualProportion(viewWidth.toFloat(), getNatureCount().toInt())
            mAmpWith = ampAndGapWidth.first
            mGap = ampAndGapWidth.second
            if (mAmpWith < mMinAmpWith) {
                mAmpWith = mMinAmpWith
            }
            val minGap = context.resources.getDimension(com.soundrecorder.common.R.dimen.dp0_67)
            if (mGap < minGap) {
                mGap = minGap
            }
            DebugUtil.d(TAG, "onSizeChanged, verticalPadding=$verticalPadding drawWidth=$viewWidth ampWidth=$mAmpWith ampGapWidth=$mGap")
        }
    }

    override fun onDraw(canvas: Canvas) {
        drawAmplitude(canvas)
        drawPoint(canvas)
        if (isDragging) {
            drawDragAnimation(canvas)
        }
        super.onDraw(canvas)
    }

    private fun drawDragAnimation(canvas: Canvas) {
        checkIfNeedCreateDragBitmap()
        if (mDirection > 0) {
            leftBitmap?.let {
                if (mPointX == dragAnimStartX) {
                    return
                }
                // 向右拖动
                if (mPointX - dragAnimStartX < dragRectWidth) {
                    val rectW = mPointX - dragAnimStartX
                    if (rectW < 0) {
                        DebugUtil.w(TAG, "drawDragAnimation: leftBitmap rectW < 0, not crop bitmap")
                        canvas.drawBitmap(it, mPointX - dragRectWidth, 0f, dragAnimPaint)
                        return
                    }
                    bitmapRect.set((dragRectWidth - rectW).toInt(), 0, dragRectWidth.toInt(), height)
                    drawBitmapRectF.set(mPointX - rectW, 0f, mPointX, height.toFloat())
                    canvas.drawBitmap(it, bitmapRect, drawBitmapRectF, dragAnimPaint)
                } else {
                    canvas.drawBitmap(it, mPointX - dragRectWidth, 0f, dragAnimPaint)
                }
            }
        } else {
            rightBitmap?.let {
                // 向左拖动
                if (mPointX == dragAnimStartX) {
                    return
                }
                if (mPointX + dragRectWidth > dragAnimStartX) {
                    val rectW = (dragAnimStartX - mPointX).toInt()
                    if (rectW < 0) {
                        DebugUtil.w(TAG, "drawDragAnimation: rightBitmap rectW < 0, not crop bitmap")
                        canvas.drawBitmap(it, mPointX, 0f, dragAnimPaint)
                        return
                    }
                    bitmapRect.set(0, 0, rectW, height)
                    drawBitmapRectF.set(mPointX, 0f, mPointX + rectW, height.toFloat())
                    canvas.drawBitmap(it, bitmapRect, drawBitmapRectF, dragAnimPaint)
                } else {
                    canvas.drawBitmap(it, mPointX, 0f, dragAnimPaint)
                }
            }
        }
    }

    private fun checkIfNeedCreateDragBitmap() {
        if (leftBitmap == null || rightBitmap == null) {
            DebugUtil.d(
                TAG, "checkIfNeedCreateDragBitmap, bitmap is null, " +
                    "need to create new bitmap dragRectWidth = $dragRectWidth height = $height")
            recycleBitmap()
            leftBitmap = createBitmap(dragRectWidth.toInt(), height)
            rightBitmap = createBitmap(dragRectWidth.toInt(), height)
            leftBitmap?.let {
                dragBitmapPaint.shader = gradientLeft
                if (leftCanvas == null) {
                    leftCanvas = Canvas(it)
                }
                leftCanvas?.drawRect(0f, 0f, dragRectWidth, height.toFloat(), dragBitmapPaint)
            }
            rightBitmap?.let {
                dragBitmapPaint.shader = gradientRight
                if (rightCanvas == null) {
                    rightCanvas = Canvas(it)
                }
                rightCanvas?.drawRect(0f, 0f, dragRectWidth, height.toFloat(), dragBitmapPaint)
            }
        }
    }

    private fun recycleBitmap() {
        leftCanvas = null
        rightCanvas = null
        runCatching {
            leftBitmap?.let {
                if (it.isRecycled.not()) {
                    it.recycle()
                }
            }
            leftBitmap = null
            DebugUtil.d(TAG, "recycleBitmap leftBitmap success")
        }.onFailure {
            leftBitmap = null
            DebugUtil.e(TAG, "recycleBitmap leftBitmap error", it)
        }

        runCatching {
            rightBitmap?.let {
                if (it.isRecycled.not()) {
                    it.recycle()
                }
            }
            rightBitmap = null
            DebugUtil.d(TAG, "recycleBitmap rightBitmap success")
        }.onFailure {
            rightBitmap = null
            DebugUtil.e(TAG, "recycleBitmap rightBitmap error", it)
        }
    }

    override fun onDetachedFromWindow() {
        fadeInAnimator.cancel()
        fadeOutAnimator.cancel()
        recycleBitmap()
        super.onDetachedFromWindow()
    }

    private fun drawAmplitude(canvas: Canvas) {
        val amplitudes: MutableList<Int>? = getAmplitudes()
        if ((amplitudes == null) || (amplitudes.isEmpty())) {
            return
        }
        var maxAmp = Collections.max<Int>(amplitudes)
        val minAmplitudeHeight = mAmpWith.toInt()
        maxAmp = if (maxAmp == 0) minAmplitudeHeight else maxAmp
        val oneLineHeightScale = getPercentDrawHeight() / maxAmp.toFloat()
        val amplitudeSize = amplitudes.size
        val viewWidth = getAvailableWidth()
        calculatePointX(viewWidth)
        val calculateAmpWidth = calculateTotalAmpWidth()
        if (amplitudeSize < getNatureCount() || calculateAmpWidth < viewWidth) {
            drawLessOneScreenAmp(amplitudeSize, amplitudes, oneLineHeightScale, viewWidth, canvas)
        } else {
            drawNormalAmp(amplitudes, oneLineHeightScale, viewWidth, canvas)
        }
    }

    private fun drawLessOneScreenAmp(
        amplitudeSize: Int,
        amplitudes: MutableList<Int>,
        oneLineHeightScale: Float,
        viewWidth: Int,
        canvas: Canvas
    ) {
        val isRtl: Boolean = getReverseLayout()
        for (i in 0 until amplitudeSize) {
            val amplitude = amplitudes.get(i)
            var startY: Float = getStartYByHeight(amplitude * oneLineHeightScale) + mWavePadding
            var endY: Float = getEndYByHeight(amplitude * oneLineHeightScale) - mWavePadding
            var startX: Float = mAmpWith * i + mGap * i
            if (startX <= viewWidth) {
                var endX: Float = startX + mAmpWith
                if (endY - startY < mAmpWith) {
                    startY -= mAmpWith / NUMBER_TWO_FLOAT
                    endY += mAmpWith / NUMBER_TWO_FLOAT
                }

                // RTL模式下,波形图支持从右往左滚动
                if (isRtl) {
                    startX = getWidth() - startX
                    endX = getWidth() - endX
                }
                //DebugUtil.d(TAG, "drawLessOneScreenAmp, startX:$startX, endX:$endX, startY:$startY, endY:$endY")
                val isSelect = if (isRtl) {
                    startX >= mPointX
                } else {
                    startX <= mPointX
                }
                if (isSelect) {
                    mAmplitudePaint?.let { canvas.drawRoundRect(startX, startY, endX, endY, mAmpWith, mAmpWith, it) }
                } else {
                    mUnSelectedPaint?.let { canvas.drawRoundRect(startX, startY, endX, endY, mAmpWith, mAmpWith, it) }
                }
                canvas.save()
            }
        }
    }

    private fun drawNormalAmp(
        amplitudes: MutableList<Int>,
        oneLineHeightScale: Float,
        viewWidth: Int,
        canvas: Canvas
    ) {
        var count = 0
        var index = 0
        var preIndex = 0
        val isRtl: Boolean = getReverseLayout()
        val onePreviewBarAmpCount: Float = amplitudes.size / getNatureCount()
        var cumulativeIndex = 0f
        while (index <= amplitudes.size) {
            if ((preIndex > 0) && (preIndex >= index)) {
                DebugUtil.w(TAG, "preIndex = index don't draw amplitude.")
                break
            }
            val amplitude = if (preIndex == 0) amplitudes.get(0) else harmonicAverageAlgorithm(amplitudes, preIndex, index)
            preIndex = index
            cumulativeIndex += onePreviewBarAmpCount
            index = Math.round(cumulativeIndex)
            var startX: Float = mAmpWith * count + mGap * count
            var startY: Float = getStartYByHeight(amplitude * oneLineHeightScale) + mWavePadding
            var endY: Float = getEndYByHeight(amplitude * oneLineHeightScale) - mWavePadding
            if (startX <= viewWidth) {
                var endX: Float = startX + mAmpWith
                if (endY - startY < mAmpWith) {
                    startY -= mAmpWith / NUMBER_TWO_FLOAT
                    endY += mAmpWith / NUMBER_TWO_FLOAT
                }

                // RTL模式下,波形图支持从右往左滚动
                if (isRtl) {
                    startX = getWidth() - startX
                    endX = getWidth() - endX
                }
                //DebugUtil.d(TAG, "drawNormalAmp, startX:$startX, endX:$endX, startY:$startY, endY:$endY")
                val isSelect = if (isRtl) {
                    startX >= mPointX
                } else {
                    startX <= mPointX
                }
                if (isSelect) {
                    mAmplitudePaint?.let { canvas.drawRoundRect(startX, startY, endX, endY, mAmpWith, mAmpWith, it) }
                } else {
                    mUnSelectedPaint?.let { canvas.drawRoundRect(startX, startY, endX, endY, mAmpWith, mAmpWith, it) }
                }
                canvas.save()
                count++
            }
        }
    }

    /**
     * The square average algorithm is used to calculate the average value of a wave corresponding to a wave in the global preview bar.
     *
     * @param data
     * @return
     */
    private fun harmonicAverageAlgorithm(amplitudes: MutableList<Int>, preIndex: Int, index: Int): Int {
        var result = 0
        if (preIndex <= 0 && index <= 0) {
            return result
        }
        if (preIndex >= index || index > amplitudes.size) {
            return result
        }
        val subList: MutableList<Int> = amplitudes.subList(preIndex, index)
        if (subList.isNullOrEmpty()) {
            return result
        }
        val n = subList.size
        var sum = 0.0
        for (x in subList) {
            sum += (x * x).toDouble()
        }
        sum /= n.toDouble()
        result = sqrt(sum).toInt()
        return result
    }

    /**
     * 绘制当前进度条
     */
    private fun drawPoint(canvas: Canvas) {
        mPointPaint?.let {
            it.strokeWidth = mAmpWith
            canvas.drawLine(mPointX, 0f, mPointX, height.toFloat(), it)
        }
        canvas.save()
    }

    private fun calculatePointX(viewWidth: Int) {
        if ((mAmplitudes == null) || mAmplitudes?.isEmpty() == true) {
            mPointTime = 0
        }
        var pointX = 0f
        if (mTotalTime != 0L) {
            pointX = (mPointTime / mTotalTime.toFloat()) * getAvailableWidth() + getPaddingStart()
        }

        mPointX = pointX - getHalfPointWidth()
        var endX = pointX + getHalfPointWidth()
        if (mReverseLayout) {
            mPointX = width - mPointX
            endX = width - endX
        }
    }

    fun getXByTime(time: Long): Float {
        if (mTotalTime == 0L) {
            return 0f
        }
        return (time / mTotalTime) * mTotalCount
    }

    fun getPointX(): Float {
        return mPointX
    }

    private fun getStartYByHeight(amplitude: Float): Float {
        val drawHeight = getPercentDrawHeight()
        return if (amplitude > drawHeight) {
            verticalPadding
        } else {
            val halfAmplitude: Float = amplitude / NUM_TWO
            verticalPadding + if (halfAmplitude == 0f) {
                drawHeight / NUMBER_TWO_FLOAT - mAmpWith / NUMBER_TWO_FLOAT
            } else {
                drawHeight / NUMBER_TWO_FLOAT - halfAmplitude
            }
        }
    }

    private fun getEndYByHeight(amplitude: Float): Float {
        val drawHeight = getPercentDrawHeight()
        return if (amplitude > drawHeight) {
            drawHeight - verticalPadding
        } else {
            val halfAmplitude: Float = amplitude / 2
            verticalPadding + if (halfAmplitude == 0f) {
                drawHeight / NUMBER_TWO_FLOAT + mAmpWith / NUMBER_TWO_FLOAT
            } else {
                drawHeight / NUMBER_TWO_FLOAT + halfAmplitude
            }
        }
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        mPreTime = -1
        when (event?.getAction()) {
            MotionEvent.ACTION_DOWN -> {
                //请求父View不拦截触摸事件
                parent?.requestDisallowInterceptTouchEvent(true)
                fadeInAnimator?.cancel()
                fadeOutAnimator?.cancel()
                mDownX = event.x

                // 初始化lastX, 用于计算拖拽动画
                lastDragX = mDownX
                // 开始拖拽前，初始化动画上一次方向为-1，表示未开始拖动
                mPreDirection = -1

                downPointX = mDownX
                correctPointTime(event.x)
                for (listener in mListeners) {
                    listener.onTouchDownMiddleBar(mPointTime)
                }

                isDragging = true
                dragAnimStartX = downPointX
                isFirstTime = true
                DebugUtil.d(TAG, "onTouchEvent, ACTION_DOWN pointX=$mPointX downPointX=$downPointX")
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                //请求父View不拦截触摸事件
                parent?.requestDisallowInterceptTouchEvent(true)
                val curX = event.x
                val dx = curX - mDownX
                if (!isDragging && abs(dx) > touchSlop) {
                    isDragging = true
                }

                if (isDragging) {
                    correctPointTime(event.x)
                    // 判定是否需要更新拖动x坐标
                    val dragDx = curX - lastDragX
                    if (abs(dragDx) > touchSlop) {
                        lastDragX = curX
                    }
                    // 判定拖拽方向
                    mDirection = if (dragDx > 0) 1 else 0
                    if (mDirection != mPreDirection) {
                        mPreDirection = mDirection
                        dragAnimStartX = if (isFirstTime) {
                            isFirstTime = false
                            downPointX
                        } else {
                            mPointX
                        }
                        DebugUtil.d(TAG, "onTouchEvent, ACTION_MOVE direction change, pointX=$mPointX, downPointX=$downPointX," +
                                " dragAnimStartX=$dragAnimStartX")
                        startFadeAnimation()
                    }
                    onMoveOnRedBar()
                }
                return true
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                run {
                    correctPointTime(event.x)
                    DebugUtil.d(TAG, "onTouchEvent (${event.action}) isDragging = $isDragging")
                    exitFadeAnimation()
                    for (listener in mListeners) {
                        listener.onTouchUpMiddleBar(mPointTime)
                    }
                }
                return true
            }

            else -> return super.onTouchEvent(event)
        }
    }

    private fun correctPointTime(x: Float) {
        val timeByX: Long = getTimeByX(x, false)
        // 矫正时间：0-总时长之间
        if (timeByX < 0) {
            mPointTime = 0
        } else {
            mPointTime = min(timeByX.toDouble(), mTotalTime.toDouble()).toLong()
        }
        DebugUtil.d(TAG, "correctPointTime, timeByX:$timeByX, pointTime:$mPointTime")
    }

    /**
     * 根据触摸点坐标，计算触摸点的时间
     *
     * @param x 触摸点坐标
     * @return 触摸点的时间
     */
    fun getTimeByX(x: Float, isChild: Boolean): Long {
        //rtl
        var xValue = x
        if (mReverseLayout && !isChild) {
            xValue = getWidth() - xValue
        }

        val totalAmpWidth: Int = getTotalAmpWidth()
        if (totalAmpWidth == 0) {
            DebugUtil.i(TAG, "getTimeByX, totalAmpWidth is 0 return")
            return 0
        }
        if (isNaN(xValue)) {
            DebugUtil.i(TAG, "getTimeByX, preViewBarLength is NAN return")
        }
        val bigPercent = BigDecimal(xValue.toDouble()).divide(BigDecimal(totalAmpWidth), PRECISION, RoundingMode.HALF_UP)
        return BigDecimal(mTotalTime).multiply(bigPercent).toLong()
    }

    /**
     * Update playback time/progress
     */
    fun setPointTime(currentTime: Long, isRightDrag: Boolean) {
        if (isDragging) {
            return
        }
        if (!isRightDrag) {
            mPreTime = -1
        }
        if (mPreTime >= currentTime) {
            return
        }
        /**
         * When playing a recording with a playback time less than 1 minute,the global preview bar jitters.
         * Increase the number of times the global preview bar is drawn to make it move smoothly.
         */
        if ((mTotalTime >= Constants.TIME_ONE_MINUTE) || (currentTime - mPointTime > DRAW_POINT_FRAME_THRESHOLD)) {
            mPointTime = currentTime
            mPreTime = mPointTime
            postInvalidateOnAnimation()
        } else {
            mPreTime = currentTime
            refreshPoint(DRAW_POINT_FRAME_TIME.toLong())
        }
    }

    private fun refreshPoint(delayTime: Long) {
        if (mPreTime == -1L) {
            return
        }
        this.mPointTime += delayTime
        if ((mPointTime != 0L) && (mPointTime < mPreTime)) {
            postDelayed(Runnable { refreshPoint(delayTime) }, delayTime)
        } else {
            mPointTime = mPreTime
        }
        postInvalidateOnAnimation()
    }

    fun resetPreTime() {
        mPreTime = -1
    }

    fun resetStatus() {
        if (mAmplitudes != null) {
            mAmplitudes?.clear()
        }
    }

    fun setSelectTime(currentTime: Long) {
        mPointTime = currentTime
        mPreTime = currentTime
        postInvalidateOnAnimation()
    }

    fun setTotalTime(mTotalTime: Long) {
        this.mTotalTime = mTotalTime
        DebugUtil.d(TAG, "setTotalTime, totalTime:$mTotalTime")
    }

    fun getTotalTime(): Long {
        return mTotalTime
    }

    fun setAmplitudes(amplitudes: MutableList<Int>) {
        this.mAmplitudes = amplitudes
        setTotalCount(amplitudes.size.toFloat())
        DebugUtil.d(TAG, "setAmplitudes, amplitudes:${amplitudes.size}")
        postInvalidateOnAnimation()
    }

    fun setTotalCount(ampCount: Float) {
        if (ampCount > 0f) {
            mNatureCount = ampCount
            mTotalCount = ampCount + ampCount - 1
            DebugUtil.d(TAG, "setTotalCount, totalCount:$mTotalCount, natureCount:$mNatureCount")
        }
    }

    fun getAmplitudes(): MutableList<Int>? {
        return mAmplitudes
    }

    fun getHalfPointWidth(): Int {
        return (ceil((mAmpWith * HALF_VALUE).toDouble())).toInt()
    }

    fun getNatureTime(): Long {
        return (mNatureCount * NATURE_UNIT_TIME).toLong()
    }

    fun getNatureCount(): Float {
        return mNatureCount
    }

    fun getTotalCount(): Float {
        return mTotalCount
    }

    fun getAmpWidth(): Float {
        return mAmpWith
    }

    fun getGapWidth(): Float {
        return mGap
    }

    /**
     * 根据波形宽度和间距计算显示所需要占用的宽度
     */
    fun calculateTotalAmpWidth(): Int {
        if (mAmplitudes == null || mAmplitudes?.isEmpty() == true) {
            return 0
        }
        val calAmpWidth = getNatureCount() * mAmpWith + (getNatureCount() - 1) * mGap
        return calAmpWidth.toInt()
    }

    /**
     * 波形总宽度
     */
    fun getTotalAmpWidth(): Int {
        if (mAmplitudes == null || mAmplitudes?.isEmpty() == true) {
            return 0
        }
        var totalAmpWidth = calculateTotalAmpWidth()
        if (totalAmpWidth > getAvailableWidth()) {
            totalAmpWidth = getAvailableWidth()
        }
        return totalAmpWidth
    }

    fun getAvailableWidth(): Int {
        return width - paddingStart - paddingEnd
    }

    interface PointMoveListener {

        /**
         * ACTION_DOWN 红线
         */
        fun onTouchDownMiddleBar(time: Long)

        /**
         * ACTION_MOVE 中间红线
         *
         * @param time
         */
        fun onMoveOnMiddleBar(time: Long)

        /**
         * ACTION_UP/ACTION_CANCEL
         *
         * @param time
         */
        fun onTouchUpMiddleBar(time: Long)

        /**
         * Media Player is tracking touch
         */
        fun getTrackingTouch(): Boolean
    }
}