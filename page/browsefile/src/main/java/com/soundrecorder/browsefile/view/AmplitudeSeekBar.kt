/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AmplitudeSeekBar.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/07/08
 ** Author      : W9035969(<EMAIL>)
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9035969     2025/07/08     1.0      create
 ***********************************************************************/
package com.soundrecorder.browsefile.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import android.view.Gravity
import android.view.MotionEvent
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.widget.FrameLayout
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.dp2px
import com.soundrecorder.browsefile.R
import com.soundrecorder.common.constant.Constants
import kotlinx.coroutines.Runnable
import java.lang.Float.isNaN
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.Collections
import kotlin.math.abs
import kotlin.math.ceil
import kotlin.math.min
import kotlin.math.sqrt

class AmplitudeSeekBar : FrameLayout {

    companion object {
        private const val TAG: String = "AmplitudeSeekBar"
        private const val NATURE_UNIT_TIME: Int = 70
        /**
         * 波形刻度数量
         */
        private const val NATURE_COUNT: Float = 90f
        /**
         * 波形间隔数量
         */
        private const val NATURE_SPACE_COUNT: Float = NATURE_COUNT - 1
        /**
         * 48 + 47 = 95
         */
        private const val TOTAL_COUNT: Float = NATURE_COUNT + NATURE_SPACE_COUNT
        /**
         * 95 * 70 = 6650
         * the view width could signify time,when mScaleValue equal 1
         */
        private const val NATURE_TIME: Long = (NATURE_COUNT * NATURE_UNIT_TIME).toLong()

        private const val HALF_VALUE: Float = 0.5f

        private const val DRAW_POINT_FRAME_TIME: Int = 18
        private const val DRAW_POINT_FRAME_THRESHOLD: Int = 300
        private const val PRECISION: Int = 9
        private const val CURRENT_LINE_PADDING: Int = 10
        private const val NUM_TWO: Int = 2
        private const val NUMBER_TWO_FLOAT: Float = 2f
        private const val DEFAULT_AMPLITUDE_RADIUS: Float = 16f
    }

    private var mAmplitudes: MutableList<Int>? = null

    private var mPointPaint: Paint? = null
    private var mAmplitudePaint: Paint? = null
    private var mUnSelectedPaint: Paint? = null

    private var mPointTime: Long = 0

    private val mListeners: MutableList<PointMoveListener> = ArrayList<PointMoveListener>()
    private var mIsTouch = false
    private var mWavePadding = 0f
    private var mTotalTime: Long = 0
    private var mPreTime: Long = -1
    private var mReverseLayout = false
    private var mPointRealX = 0f
    private var mMinAmplitudeHeight = 2
    private var mAmplitudeRadius = DEFAULT_AMPLITUDE_RADIUS

    private var mDragBar: GradientDragBar? = null
    private var mDownX: Float = 0f
    private var mDownY: Float = 0f

    private var mNatureCount = NATURE_COUNT
    private var mTotalCount = TOTAL_COUNT

    private var mDirection: Int = 0
    private var mOffsetX: Float = 0f
    private val touchPadding = getContext().dp2px(CURRENT_LINE_PADDING)
    private var mMinAmpWith: Float = 0f
    private var mGap = 0f

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attributeSet: AttributeSet?) : this(context, attributeSet, 0)

    constructor(context: Context, attributeSet: AttributeSet?, defStyleAttr: Int) : super(context, attributeSet, defStyleAttr) {
        initParams(context, attributeSet)
        initDragBar()
        setWillNotDraw(false)
    }

    private fun initParams(context: Context, attr: AttributeSet?) {
        val array = context.obtainStyledAttributes(attr, R.styleable.AmplitudeSeekBar)
        val pointColor = array.getColor(R.styleable.AmplitudeSeekBar_point_color, Color.BLACK)
        val amplitudeColor = array.getColor(R.styleable.AmplitudeSeekBar_amplitude_color,
            context.getColor(com.support.appcompat.R.color.coui_color_label_primary))
        val amplitudeUnSelectColor = array.getColor(R.styleable.AmplitudeSeekBar_amplitude_unselect_color,
            context.getColor(com.support.appcompat.R.color.coui_color_container16))
        array.recycle()

        mPointPaint = Paint()
        mPointPaint?.color = pointColor
        mPointPaint?.isAntiAlias = true
        mPointPaint?.isDither = true

        mAmplitudePaint = Paint()
        mAmplitudePaint?.color = amplitudeColor
        mAmplitudePaint?.isAntiAlias = true
        mAmplitudePaint?.isDither = true

        mUnSelectedPaint = Paint()
        mUnSelectedPaint?.color = amplitudeUnSelectColor
        mUnSelectedPaint?.isAntiAlias = true
        mUnSelectedPaint?.isDither = true

        mMinAmplitudeHeight = context.getResources().getDimension(com.soundrecorder.common.R.dimen.dp2).toInt()
        mMinAmpWith = context.getResources().getDimension(com.soundrecorder.common.R.dimen.dp1_5)
        mGap = context.getResources().getDimension(com.soundrecorder.common.R.dimen.dp0_75)
        DebugUtil.d(TAG, "initParams, mineAmpHeight:$mMinAmplitudeHeight")
    }

    private fun initDragBar() {
        if (mDragBar == null) {
            mDragBar = GradientDragBar(context)
        }
    }

    fun setReverseLayout(reverseLayout: Boolean) {
        if (reverseLayout == mReverseLayout) {
            return
        }
        mReverseLayout = reverseLayout
        requestLayout()
    }

    fun getReverseLayout(): Boolean {
        return mReverseLayout
    }

    fun getIsTouch(): Boolean {
        return mIsTouch
    }

    fun setListenter(listener: PointMoveListener?) {
        if (listener != null) {
            mListeners.add(listener)
        }
    }

    private fun onMoveOnRedBar() {
        if (mListeners.isNullOrEmpty()) {
            return
        }
        for (listener in mListeners) {
            listener.onMoveOnMiddleBar(mPointTime)
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (mAmplitudes != null && (mAmplitudes?.size ?: 0) > 0) {
            drawPoint(canvas) //ok
            drawAmplitude(canvas)
        }
    }

    private fun drawAmplitude(canvas: Canvas) {
        val amplitudes: MutableList<Int>? = getAmplitudes()
        if ((amplitudes == null) || (amplitudes.isEmpty())) {
            return
        }
        var maxAmp = Collections.max<Int>(amplitudes)
        maxAmp = if (maxAmp == 0) mMinAmplitudeHeight else maxAmp
        val oneLineHeightScale = height / maxAmp.toFloat()
        mAmplitudeRadius = height / NUMBER_TWO_FLOAT
        val calculateAmpWidth = calculateTotalAmpWidth()
        val amplitudeSize = amplitudes.size
        val viewWidth = getAvailableWidth()
        if (amplitudeSize < getNatureCount() || calculateAmpWidth < viewWidth) {
            drawLessOneScreenAmp(amplitudeSize, amplitudes, oneLineHeightScale, viewWidth, canvas)
        } else {
            drawNormalAmp(amplitudes, oneLineHeightScale, viewWidth, canvas)
        }
    }

    private fun drawLessOneScreenAmp(
        amplitudeSize: Int,
        amplitudes: MutableList<Int>,
        oneLineHeightScale: Float,
        viewWidth: Int,
        canvas: Canvas
    ) {
        val isRtl: Boolean = getReverseLayout()
        for (i in 0 until amplitudeSize) {
            val amplitude = amplitudes.get(i)
            var startY: Float = getStartYByHeight(amplitude * oneLineHeightScale) + mWavePadding
            var endY: Float = getEndYByHeight(amplitude * oneLineHeightScale) - mWavePadding
            var startX: Float = getAmpWidth() * i + getGapWidth() * i
            if (startX <= viewWidth) {
                var endX: Float = startX + getAmpWidth()
                if (endY - startY < mMinAmplitudeHeight) {
                    startY -= mMinAmplitudeHeight / NUMBER_TWO_FLOAT
                    endY += mMinAmplitudeHeight / NUMBER_TWO_FLOAT
                }

                // RTL模式下,波形图支持从右往左滚动
                if (isRtl) {
                    startX = getWidth() - startX
                    endX = getWidth() - endX
                }
                //DebugUtil.d(TAG, "drawLessOneScreenAmp, startX:$startX, endX:$endX, startY:$startY, endY:$endY")
                val isSelect = if (isRtl) {
                    startX >= mPointRealX
                } else {
                    startX <= mPointRealX
                }
                if (isSelect) {
                    mAmplitudePaint?.let { canvas.drawRoundRect(startX, startY, endX, endY, mAmplitudeRadius, mAmplitudeRadius, it) }
                } else {
                    mUnSelectedPaint?.let { canvas.drawRoundRect(startX, startY, endX, endY, mAmplitudeRadius, mAmplitudeRadius, it) }
                }
                canvas.save()
            }
        }
    }

    private fun drawNormalAmp(
        amplitudes: MutableList<Int>,
        oneLineHeightScale: Float,
        viewWidth: Int,
        canvas: Canvas
    ) {
        var count = 0
        var index = 0
        var preIndex = 0
        val isRtl: Boolean = getReverseLayout()
        val onePreviewBarAmpCount: Float = amplitudes.size / getNatureCount()
        var cumulativeIndex = 0f
        while (index <= amplitudes.size) {
            if ((preIndex > 0) && (preIndex >= index)) {
                DebugUtil.w(TAG, "preIndex = index don't draw amplitude.")
                break
            }
            val amplitude = if (preIndex == 0) amplitudes.get(0) else harmonicAverageAlgorithm(amplitudes, preIndex, index)
            preIndex = index
            cumulativeIndex += onePreviewBarAmpCount
            index = Math.round(cumulativeIndex)
            var startX: Float = getAmpWidth() * count + getGapWidth() * count
            var startY: Float = getStartYByHeight(amplitude * oneLineHeightScale) + mWavePadding
            var endY: Float = getEndYByHeight(amplitude * oneLineHeightScale) - mWavePadding
            if (startX <= viewWidth) {
                var endX: Float = startX + getAmpWidth()
                if (endY - startY < mMinAmplitudeHeight) {
                    startY -= mMinAmplitudeHeight / NUMBER_TWO_FLOAT
                    endY += mMinAmplitudeHeight / NUMBER_TWO_FLOAT
                }

                // RTL模式下,波形图支持从右往左滚动
                if (isRtl) {
                    startX = getWidth() - startX
                    endX = getWidth() - endX
                }
                //DebugUtil.d(TAG, "drawNormalAmp, startX:$startX, endX:$endX, startY:$startY, endY:$endY")
                val isSelect = if (isRtl) {
                    startX >= mPointRealX
                } else {
                    startX <= mPointRealX
                }
                if (isSelect) {
                    mAmplitudePaint?.let { canvas.drawRoundRect(startX, startY, endX, endY, mAmplitudeRadius, mAmplitudeRadius, it) }
                } else {
                    mUnSelectedPaint?.let { canvas.drawRoundRect(startX, startY, endX, endY, mAmplitudeRadius, mAmplitudeRadius, it) }
                }
                canvas.save()
                count++
            }
        }
    }

    /**
     * The square average algorithm is used to calculate the average value of a wave corresponding to a wave in the global preview bar.
     *
     * @param data
     * @return
     */
    private fun harmonicAverageAlgorithm(amplitudes: MutableList<Int>, preIndex: Int, index: Int): Int {
        var result = 0
        if (preIndex <= 0 && index <= 0) {
            return result
        }
        if (preIndex >= index || index > amplitudes.size) {
            return result
        }
        val subList: MutableList<Int> = amplitudes.subList(preIndex, index)
        if (subList.isNullOrEmpty()) {
            return result
        }
        val n = subList.size
        var sum = 0.0
        for (x in subList) {
            sum += (x * x).toDouble()
        }
        sum /= n.toDouble()
        result = sqrt(sum).toInt()
        return result
    }

    /**
     * 绘制当前进度条
     */
    private fun drawPoint(canvas: Canvas) {
        if ((mAmplitudes == null) || mAmplitudes?.isEmpty() == true) {
            mPointTime = 0
        }
        var pointX = 0f
        if (mTotalTime != 0L) {
            pointX = (mPointTime / mTotalTime.toFloat()) * getAvailableWidth() + getPaddingStart()
        }
        mPointRealX = pointX - getHalfPointWidth()
        var endX: Float = pointX + getHalfPointWidth()
        if (mReverseLayout) {
            mPointRealX = getWidth() - mPointRealX
            endX = getWidth() - endX
        }
        if (mIsTouch) {
            mDragBar?.let {
                it.startDragMove(pointX, mOffsetX, mDirection)
                it.startFadeAnimation()
            }
        }
        //DebugUtil.d(TAG, "drawPoint, pointTime:$mPointTime, pointRealX:$mPointRealX")
        mPointPaint?.let { canvas.drawRect(mPointRealX, 0f, endX, getHeight().toFloat(), it) }
        canvas.save()
    }

    fun getXByTime(time: Long): Float {
        if (mTotalTime == 0L) {
            return 0f
        }
        return (time / mTotalTime) * mTotalCount
    }

    fun getPointX(): Float {
        return mPointRealX
    }

    fun getStartYByHeight(amplitude: Float): Float {
        val height = getHeight().toFloat()
        if (amplitude > height) {
            return 0f
        } else {
            return (height / NUM_TWO
                    - (if ((amplitude / NUM_TWO) == 0f) NUM_TWO.toFloat() else (amplitude / NUM_TWO)))
        }
    }

    fun getEndYByHeight(amplitude: Float): Float {
        val height = getHeight().toFloat()
        if (amplitude > height) {
            return height
        } else {
            return (height / NUM_TWO
                    + (if ((amplitude / NUM_TWO) == 0f) NUM_TWO.toFloat() else (amplitude / NUM_TWO)))
        }
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        mPreTime = -1
        when (event?.getAction()) {
            MotionEvent.ACTION_DOWN -> {
                mDownX = event.x
                mDownY = event.y
                val rectF = RectF(mPointRealX - touchPadding, 0f, mPointRealX + getPointWidth() + touchPadding, getHeight().toFloat())
                val contains = rectF.contains(mDownX, event.y)
                if (contains) {
                    //请求父View不拦截触摸事件
                    parent.requestDisallowInterceptTouchEvent(true)
                    addDragBar()
                    for (listener in mListeners) {
                        listener.onTouchDownMiddleBar()
                    }
                }
                return contains
            }

            MotionEvent.ACTION_MOVE -> {
                mIsTouch = true
                val timeByX: Long = getTimeByX(event.x, false)
                correctPointTime(timeByX)
                mOffsetX = event.x - mDownX
                DebugUtil.d(TAG, "onTouchEvent, eventX:${event.x} offsetX:$mOffsetX")
                //判断拖动方向（左或右）
                mDirection = if (mOffsetX > 0) GradientDragBar.DRAG_RIGHT else GradientDragBar.DRAG_LEFT
                mDragBar?.let {
                    it.setOffsetX(mOffsetX)
                    it.setDirection(mDirection)
                }
                onMoveOnRedBar()
                postInvalidateOnAnimation()

                val dx = abs(mOffsetX)
                val dy = abs(event.y - mDownY)
                //处理横向滑动
                if (dx > dy && dx > ViewConfiguration.get(context).scaledTouchSlop) {
                    return true
                } else {
                    //纵向滑动允许父View拦截
                    parent.requestDisallowInterceptTouchEvent(false)
                    return super.onTouchEvent(event)
                }
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                run {
                    mIsTouch = false
                    val timeByX: Long = getTimeByX(event.getX(), false)
                    correctPointTime(timeByX)
                    mDragBar?.exitFadeAnimation()
                    for (listener in mListeners) {
                        listener.onTouchUpMiddleBar(mPointTime)
                    }
                    postInvalidateOnAnimation()
                    parent.requestDisallowInterceptTouchEvent(false)
                }
                return true
            }

            else -> return super.onTouchEvent(event)
        }
    }

    private fun addDragBar() {
        initDragBar()
        removeDragBar()
        val layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT)
        layoutParams.gravity = Gravity.CENTER_VERTICAL
        mDragBar?.alpha = 0f
        addView(mDragBar, layoutParams)
    }

    private fun removeDragBar() {
        if (mDragBar?.getParent() != null) {
            (mDragBar?.getParent() as? ViewGroup)?.removeView(mDragBar)
        }
    }

    private fun correctPointTime(timeByX: Long) {
        // 矫正时间：0-总时长之间
        if (timeByX < 0) {
            mPointTime = 0
        } else {
            mPointTime = min(timeByX.toDouble(), mTotalTime.toDouble()).toLong()
        }
        DebugUtil.d(TAG, "correctPointTime, timeByX:$timeByX, pointTime:$mPointTime")
    }

    /**
     * 根据触摸点坐标，计算触摸点的时间
     *
     * @param x 触摸点坐标
     * @return 触摸点的时间
     */
    fun getTimeByX(x: Float, isChild: Boolean): Long {
        //rtl
        var xValue = x
        if (mReverseLayout && !isChild) {
            xValue = getWidth() - xValue
        }

        //以预览条起点为基准，手指触摸点在预览条上的长度
        var preViewBarLength = 0f
        if (isChild) {
            preViewBarLength = xValue
        } else {
            preViewBarLength = xValue - getPaddingStart()
        }

        val totalAmpWidth: Int = getTotalAmpWidth()
        if (totalAmpWidth == 0) {
            DebugUtil.i(TAG, "getTimeByX, totalAmpWidth is 0 return")
            return 0
        }
        if (isNaN(preViewBarLength)) {
            DebugUtil.i(TAG, "getTimeByX, preViewBarLength is NAN return")
        }
        val bigPercent = BigDecimal(preViewBarLength.toDouble()).divide(BigDecimal(totalAmpWidth), PRECISION, RoundingMode.HALF_UP)
        return BigDecimal(mTotalTime).multiply(bigPercent).toLong()
    }

    fun findChildXByTime(mPointTime: Long): Float {
        val totalAmpWidth: Int = getTotalAmpWidth()
        if (totalAmpWidth == 0) {
            DebugUtil.i(TAG, "findChildXByTime, totalAmpWidth is 0 return")
            return 0f
        }
        val inputTime = BigDecimal(mPointTime)
        if (mTotalTime == 0L) {
            DebugUtil.i(TAG, "findChildXByTime, mTotalTime is 0 return")
            return 0f
        }
        return inputTime.divide(BigDecimal(mTotalTime), PRECISION, RoundingMode.HALF_UP)
            .multiply(BigDecimal(totalAmpWidth))
            //.add(BigDecimal(getAmpMargin()))
            .toFloat()
    }

    /**
     * Update playback time/progress
     */
    fun setPointTime(currentTime: Long, isRightDrag: Boolean) {
        if (mIsTouch) {
            return
        }
        if (!isRightDrag) {
            mPreTime = -1
        }
        if (mPreTime >= currentTime) {
            return
        }
        /**
         * When playing a recording with a playback time less than 1 minute,the global preview bar jitters.
         * Increase the number of times the global preview bar is drawn to make it move smoothly.
         */
        if ((mTotalTime >= Constants.TIME_ONE_MINUTE) || (currentTime - mPointTime > DRAW_POINT_FRAME_THRESHOLD)) {
            mPointTime = currentTime
            mPreTime = mPointTime
            postInvalidateOnAnimation()
        } else {
            mPreTime = currentTime
            refreshPoint(DRAW_POINT_FRAME_TIME.toLong())
        }
    }

    private fun refreshPoint(delayTime: Long) {
        if (mPreTime == -1L) {
            return
        }
        this.mPointTime += delayTime
        if ((mPointTime != 0L) && (mPointTime < mPreTime)) {
            postDelayed(Runnable { refreshPoint(delayTime) }, delayTime)
        } else {
            mPointTime = mPreTime
        }
        postInvalidateOnAnimation()
    }

    fun resetPreTime() {
        mPreTime = -1
    }

    fun resetStatus() {
        if (mAmplitudes != null) {
            mAmplitudes?.clear()
        }
    }

    fun setSelectTime(currentTime: Long) {
        mPointTime = currentTime
        mPreTime = currentTime
        postInvalidateOnAnimation()
    }

    fun setTotalTime(mTotalTime: Long) {
        this.mTotalTime = mTotalTime
        DebugUtil.d(TAG, "setTotalTime, totalTime:$mTotalTime")
    }

    fun getTotalTime(): Long {
        return mTotalTime
    }

    fun setAmplitudes(amplitudes: MutableList<Int>) {
        this.mAmplitudes = amplitudes
        setTotalCount(amplitudes.size.toFloat())
        DebugUtil.d(TAG, "setAmplitudes, amplitudes:${amplitudes.size}")
        postInvalidateOnAnimation()
        //mAmplitudeView?.postInvalidate()
    }

    fun setTotalCount(ampCount: Float) {
        if (ampCount > 0f) {
            mNatureCount = ampCount
            mTotalCount = ampCount + ampCount - 1
            DebugUtil.d(TAG, "setTotalCount, totalCount:$mTotalCount, natureCount:$mNatureCount")
        }
    }

    fun getAmplitudes(): MutableList<Int>? {
        return mAmplitudes
    }

    fun getPointWidth(): Float {
        return getContext().getResources().getDimension(com.soundrecorder.common.R.dimen.dp2)
    }

    fun getHalfPointWidth(): Int {
        return (ceil(
            (getContext().getResources().getDimension(com.soundrecorder.common.R.dimen.dp2) * HALF_VALUE).toDouble()
        )).toInt()
    }

    private fun isLessNature(): Boolean {
        if (mAmplitudes.isNullOrEmpty()) {
            return true
        }
        return (mAmplitudes?.size ?: 0) < mNatureCount
    }

    fun isLessOneScreen(): Boolean {
        return isLessNature() || mTotalTime < getNatureTime()
    }

    fun getNatureTime(): Long {
        return (mNatureCount * NATURE_UNIT_TIME).toLong()
    }

    fun getNatureCount(): Float {
        return mNatureCount
    }

    fun getTotalCount(): Float {
        return mTotalCount
    }

    fun getAmpWidth(): Float {
        return mMinAmpWith
    }

    fun getGapWidth(): Float {
        return mGap
    }

    /**
     * 每个波形刻度的宽度
     */
    fun getAmplitudeWidth(): Float {
        val width: Int = getAvailableWidth()
        val viewWidth = BigDecimal(width)
        val sizeBig = BigDecimal(mTotalCount.toDouble())
        var amplitudeWidth = viewWidth.divide(sizeBig, PRECISION, RoundingMode.HALF_UP).toFloat()
        if (amplitudeWidth < mMinAmpWith) {
            amplitudeWidth = mMinAmpWith
        }
        return amplitudeWidth
    }

    /**
     * 根据波形宽度和间距计算显示所需要占用的宽度
     */
    fun calculateTotalAmpWidth(): Int {
        if (mAmplitudes == null || mAmplitudes?.isEmpty() == true) {
            return 0
        }
        val calAmpWidth = getNatureCount() * getAmpWidth() + (getNatureCount() - 1) * getGapWidth()
        return calAmpWidth.toInt()
    }

    /**
     * 波形总宽度
     */
    fun getTotalAmpWidth(): Int {
        if (mAmplitudes == null || mAmplitudes?.isEmpty() == true) {
            return 0
        }
        var totalAmpWidth = 0
        val calculateWidth = calculateTotalAmpWidth()
        if (calculateWidth > getAvailableWidth()) {
            totalAmpWidth = getAvailableWidth()
        }
        DebugUtil.i(TAG, "getTotalAmpWidth:" + totalAmpWidth)
        return totalAmpWidth
    }

    fun getAvailableWidth(): Int {
        return getWidth() - getPaddingStart() - getPaddingEnd()
    }

    interface PointMoveListener {

        /**
         * ACTION_DOWN 红线
         */
        fun onTouchDownMiddleBar()

        /**
         * ACTION_MOVE 中间红线
         *
         * @param time
         */
        fun onMoveOnMiddleBar(time: Long)

        /**
         * ACTION_UP/ACTION_CANCEL
         *
         * @param time
         */
        fun onTouchUpMiddleBar(time: Long)
    }
}