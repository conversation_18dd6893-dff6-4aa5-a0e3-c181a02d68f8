package com.soundrecorder.browsefile.search.load

import android.content.ContentProvider
import android.content.ContentResolver
import android.content.ContentValues
import android.content.UriMatcher
import android.database.Cursor
import android.database.MatrixCursor
import android.net.Uri
import android.os.Binder
import android.os.Bundle
import android.os.CancellationSignal
import android.os.ParcelFileDescriptor
import android.provider.MediaStore
import android.text.TextUtils
import androidx.core.database.getStringOrNull
import androidx.core.os.bundleOf
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import com.soundrecorder.base.ext.title
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.browsefile.search.load.center.CenterDbConstant
import com.soundrecorder.browsefile.search.load.center.CenterDbUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.db.CursorHelper
import com.soundrecorder.common.db.RecorderDatabaseHelper
import com.soundrecorder.browsefile.search.load.center.databean.SearchInsertExtendBean
import com.soundrecorder.browsefile.search.utils.ForNoteUtil
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import java.io.*
import java.lang.Exception
import java.util.*


class SearchCenterProvider : ContentProvider() {
    companion object {
        private const val TAG = "SearchCenterProvider"
        private const val CONVERT_TEXT_SPLIT = "/"
        private const val CONVERT_TEXT_POSITION = 2
        private const val RECORD_SUMMARIZED = 1
        private const val RECORD_NOT_SUMMARY = 0
    }

    private val URI_MATCHER = UriMatcher(UriMatcher.NO_MATCH)

    init {
        URI_MATCHER.addURI(CenterDbConstant.AUTHORITY_SEARCH_CENTER, null, 0)
    }

    private var mDatabaseHelper: RecorderDatabaseHelper? = null

    override fun onCreate(): Boolean {
        mDatabaseHelper = RecorderDatabaseHelper(context)
        return true
    }

    /**
     * 该接口实际无意义，系统调用该方法，内部实际还是调用的query（bundle）
     */
    override fun query(
        uri: Uri,
        projection: Array<String>?,
        selection: String?,
        selectionArgs: Array<String>?,
        sortOrder: String?
    ): Cursor? {
        DebugUtil.i(TAG, "query by selection")

        return query(
            uri,
            projection,
            bundleOf(
                ContentResolver.QUERY_ARG_SQL_SELECTION to selection,
                ContentResolver.QUERY_ARG_SQL_SELECTION_ARGS to selectionArgs
            ),
            null
        )
    }

    /**
     * 中子通过该接口批量获取录音及转文本数据
     * 按照约定好字段返回给中子
     */
    @Suppress("LongMethod")
    override fun query(
        uri: Uri,
        projection: Array<String>?,
        bundle: Bundle?,
        cancellationSignal: CancellationSignal?
    ): Cursor? {
        if (!PermissionUtils.hasReadAudioPermission(context)) {
            DebugUtil.d(TAG, "search center provider, no storage permission")
            return null
        }
        if (!checkPackageAuth()) {
            DebugUtil.i(TAG, "package name not match")
            throw RuntimeException("package name not match")
        }
        if (mDatabaseHelper == null) {
            DebugUtil.i(TAG, "query bundle helper is null")
            return null
        }
        val table = URI_MATCHER.match(uri)
        if (table == -1) {
            throw RuntimeException("uri is not match")
        }

        val queryBundle = Bundle().apply {
            val whereClause = CursorHelper.getAllRecordForFilterAndQueryWhereClause(context).run {
                var whereClause = this
                // 拼接调用方动态传入selection,目前仅支持媒体库对应字段,若后期要支持bucket、text_path、extend 需额外开发
                // 若调用方传入非以下字段，会sql error
                val selection = bundle?.getString(ContentResolver.QUERY_ARG_SQL_SELECTION)?.let {
                    it.replace(
                        CenterDbConstant.IndexProvider.COLUMN_NAME_MEDIA_ID,
                        MediaStore.Audio.Media._ID
                    )
                        .replace(
                            CenterDbConstant.IndexProvider.COLUMN_NAME_MEDIA_PATH,
                            MediaStore.Audio.Media.DATA
                        )
                        .replace(
                            CenterDbConstant.IndexProvider.COLUMN_NAME_DISPLAY_NAME,
                            MediaStore.Audio.Media.DISPLAY_NAME
                        )
                        .replace(
                            CenterDbConstant.IndexProvider.COLUMN_NAME_SIZE,
                            MediaStore.Audio.Media.SIZE
                        )
                        .replace(
                            CenterDbConstant.IndexProvider.COLUMN_NAME_DATE_MODIFIED,
                            MediaStore.Audio.Media.DATE_MODIFIED
                        )
                        .replace(
                            CenterDbConstant.IndexProvider.COLUMN_NAME_DURATION,
                            MediaStore.Audio.Media.DURATION
                        )
                }

                if (selection.isNullOrBlank().not()) {
                    whereClause = whereClause.plus(" AND ( $selection ) ")
                }
                whereClause
            }
            val selectionArgs = CursorHelper.getsAcceptableAudioTypes()?.run {
                val origin = this.toMutableList()
                // 拼接调用方动态传入selectionArgs
                val selectionArgs =
                    bundle?.getStringArray(ContentResolver.QUERY_ARG_SQL_SELECTION_ARGS)
                if (selectionArgs.isNullOrEmpty().not()) {
                    origin.addAll(selectionArgs!!.toList())
                }
                origin.toTypedArray()
            }
            this.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, whereClause)
            this.putStringArray(ContentResolver.QUERY_ARG_SQL_SELECTION_ARGS, selectionArgs)
            // Sort function
            this.putStringArray(
                ContentResolver.QUERY_ARG_SORT_COLUMNS,
                arrayOf(MediaStore.Audio.AudioColumns.DATE_MODIFIED)
            )
            this.putInt(
                ContentResolver.QUERY_ARG_SORT_DIRECTION,
                ContentResolver.QUERY_SORT_DIRECTION_DESCENDING
            )
            DebugUtil.i(TAG, "query bundle whereClause $whereClause  selectionArgs $selectionArgs")
        }
        DebugUtil.i(TAG, "query bundle limit ${bundle?.get("limit")}")
        (bundle?.get("limit") as? String?)?.split(",")?.let {
            // offset,limit
            if (it.size > 1) {
                val offset = it[0].toInt()
                val limit = it[1].toInt()
                queryBundle!!.putInt(ContentResolver.QUERY_ARG_OFFSET, offset)
                queryBundle.putInt(ContentResolver.QUERY_ARG_LIMIT, limit)
                DebugUtil.i(TAG, "limit $limit,offset $offset")
            }
        }

        try {
            val mediaCursor = queryMediaCursor(queryBundle)

            return convertCursor(
                mediaCursor,
                projection ?: CenterDbConstant.IndexProvider.CENTER_PROJECTION
            )
        } catch (e: Exception) {
            DebugUtil.e(TAG, "query error $e")
            throw e
        }
    }

    private fun queryMediaCursor(argBundle: Bundle): Cursor? {
        kotlin.runCatching {
            return context?.contentResolver?.query(
                MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                CenterDbConstant.RECODER_PROJECTION,
                argBundle,
                null
            )
        }.onFailure {
            DebugUtil.e(TAG, "queryMediaCursor1 error: $it")
            kotlin.runCatching {
                return context?.contentResolver?.query(
                    MediaStore.Audio.Media.getContentUri(MediaStore.VOLUME_EXTERNAL),
                    CenterDbConstant.RECODER_PROJECTION,
                    argBundle,
                    null
                )
            }
        }

        return null
    }

    /**
     * @param cursor 媒体库数据
     * @return newCursor 按照中子约定好字段+转文本内容
     */
    private fun convertCursor(cursor: Cursor?, projection: Array<String>): Cursor? {
        DebugUtil.i(TAG, "query, convertCursor start. ${cursor?.count}")
        if (cursor == null) {
            return null
        }
        cursor.use {
            val newCursor = MatrixCursor(projection)
            if (it.count == 0) {
                return newCursor
            }
            // 媒体库cursor的index
            val completes =
                if (projection.contains(CenterDbConstant.IndexProvider.COLUMN_NAME_CONVERT_PATH)) getConvertTableData() else emptyMap<Int, String>()
            val columnSize = projection.size
            val indexOfId = cursor.getColumnIndex(MediaStore.Audio.Media._ID)
            val indexOfPath = cursor.getColumnIndex(MediaStore.Audio.Media.RELATIVE_PATH)
            val indexOfData = cursor.getColumnIndex(MediaStore.Audio.Media.DATA)
            val indexOfName = cursor.getColumnIndex(MediaStore.Audio.Media.DISPLAY_NAME)
            val indexOfSize = cursor.getColumnIndex(MediaStore.Audio.Media.SIZE)
            val indexOfTime = cursor.getColumnIndex(MediaStore.Audio.Media.DATE_MODIFIED)
            val indexOfDuration = cursor.getColumnIndex(MediaStore.Audio.Media.DURATION)
            val indexOfMimeType = cursor.getColumnIndex(MediaStore.Audio.Media.MIME_TYPE)

            // 返回newCursor的index
            val newIndexOfBucket =
                projection.indexOf(CenterDbConstant.IndexProvider.COLUMN_NAME_BUCKET)
            val newIndexOfId =
                projection.indexOf(CenterDbConstant.IndexProvider.COLUMN_NAME_MEDIA_ID)
            val newIndexOfPath =
                projection.indexOf(CenterDbConstant.IndexProvider.COLUMN_NAME_MEDIA_PATH)
            val newIndexOfName =
                projection.indexOf(CenterDbConstant.IndexProvider.COLUMN_NAME_DISPLAY_NAME)
            val newIndexOfSize = projection.indexOf(CenterDbConstant.IndexProvider.COLUMN_NAME_SIZE)
            val newIndexOfModify =
                projection.indexOf(CenterDbConstant.IndexProvider.COLUMN_NAME_DATE_MODIFIED)
            val newIndexOfDuration =
                projection.indexOf(CenterDbConstant.IndexProvider.COLUMN_NAME_DURATION)
            val newIndexOfConvertPath =
                projection.indexOf(CenterDbConstant.IndexProvider.COLUMN_NAME_CONVERT_PATH)
            val newIndexOfExtend =
                projection.indexOf(CenterDbConstant.IndexProvider.COLUMN_NAME_EXTEND)
            loop@ while (cursor.moveToNext()) {
                val newRow = arrayOfNulls<Any>(columnSize)
                val relativePath = cursor.getString(indexOfPath)
                val bucket = CenterDbUtils.calBucketByRelativePath(relativePath)
                if (bucket.isNullOrEmpty()) {
                    continue@loop
                }
                if (newIndexOfBucket != -1) {
                    newRow[newIndexOfBucket] = bucket
                }

                //"bucket", "id", "media_path", "display_name", "size", "date_modified", "duration", "text_path"
                val mediaId = cursor.getLong(indexOfId)
                if (newIndexOfId != -1) {
                    newRow[newIndexOfId] = mediaId.toString()
                }
                if (newIndexOfPath != -1) {
                    newRow[newIndexOfPath] = cursor.getString(indexOfData)
                }
                if (newIndexOfName != -1) {
                    newRow[newIndexOfName] = cursor.getString(indexOfName).title() ?: ""
                }
                if (newIndexOfSize != -1) {
                    newRow[newIndexOfSize] = cursor.getLong(indexOfSize)
                }
                if (newIndexOfModify != -1) {
                    newRow[newIndexOfModify] = cursor.getLong(indexOfTime)
                }
                if (newIndexOfDuration != -1) {
                    newRow[newIndexOfDuration] = cursor.getLong(indexOfDuration)
                }
                if (newIndexOfConvertPath != -1) {
                    newRow[newIndexOfConvertPath] = completes[mediaId.toInt()] ?: ""
                }
                if (newIndexOfExtend != -1) {
                    newRow[newIndexOfExtend] =
                        genExtendJosnString(cursor.getString(indexOfMimeType))
                }
                val token = Binder.clearCallingIdentity()
                val noteData = NoteDbUtils.queryNoteByMediaId(mediaId.toString())
                Binder.restoreCallingIdentity(token)
                val isSupportNeutronVersion = ForNoteUtil.isSupportNeutronVersion()
                DebugUtil.i(
                    TAG,
                    "mediaId:$mediaId noteData:$noteData   ${noteData != null} isSupportNeutron:$isSupportNeutronVersion "
                )
                if (isSupportNeutronVersion && projection.contains(CenterDbConstant.IndexProvider.SUMMARY_TEXT_FLAG)) {
                    val summaryText =
                        projection.indexOf(CenterDbConstant.IndexProvider.SUMMARY_TEXT_FLAG)
                    if (noteData != null) {
                        //summary数据库表有该条media_id 标志位置位1
                        newRow[summaryText] = RECORD_SUMMARIZED
                    } else {
                        newRow[summaryText] = RECORD_NOT_SUMMARY
                    }
                }
                newCursor.addRow(newRow)
            }
            DebugUtil.i(TAG, "convertCursor end")
            return newCursor
        }
    }

    private fun genExtendJosnString(mimeType: String): String {
        val extendBean = SearchInsertExtendBean().apply {
            this.mimeType = mimeType
        }
        return extendBean.toJsonString()
    }

    /**
     * 查询转文本数据
     */
    private fun getConvertTableData(): HashMap<Int, String> {
        val convertMap = HashMap<Int, String>()
        kotlin.runCatching {
            mDatabaseHelper?.readableDatabase?.query(
                DatabaseConstant.TABLE_NAME_CONVERT,
                null, null, null, null, null, null
            )?.use {
                if (it.count > 0) {
                    val idIndex = it.getColumnIndex(DatabaseConstant.ConvertColumn.RECORD_ID)
                    val pathIndex = it.getColumnIndex(DatabaseConstant.ConvertColumn.MEDIA_PATH)
                    val textPathIndex = it.getColumnIndex(DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH)
                    while (it.moveToNext()) {
                        try {
                            var id = if (idIndex >= 0) it.getLong(idIndex) else -1L
                            val mediaPath = if (pathIndex >= 0) it.getString(pathIndex) else null
                            if (id == -1L) {
                                id = MediaDBUtils.queryIdByData(mediaPath)
                            }
                            it.getStringOrNull(textPathIndex)?.let { path ->
                                convertMap[id.toInt()] = path
                            }
                        } catch (e: Exception) {
                            DebugUtil.e(TAG, "getConvertTableData error", e)
                        }
                    }
                }
            }
        }.onFailure {
            DebugUtil.e(TAG, "getConvertTableData error: $it")
        }
        return convertMap
    }

    override fun getType(uri: Uri): String? {
        if (URI_MATCHER.match(uri) == 0) {
            return DatabaseConstant.PROVIDER_CONVERT_TYPE
        }
        throw IllegalStateException("Unknown URL: $uri")
    }

    override fun insert(uri: Uri, contentValues: ContentValues?): Uri? = null

    override fun delete(uri: Uri, s: String?, strings: Array<String>?): Int = 0

    override fun update(
        uri: Uri,
        contentValues: ContentValues?,
        s: String?,
        strings: Array<String>?
    ): Int = 0

    override fun call(authority: String, method: String, arg: String?, extras: Bundle?): Bundle? {
        if (!checkPackageAuth()) {
            throw RuntimeException("package name not match")
        }
        DebugUtil.i(TAG, "---callmethod $method")
        when (method) {
            // 供中台获取资源方当前版本支持的resourceVersion值（初始版本值：100，整型）
            // 录音明确不想接入dmp，可返回一个空的List（List对象不为空，内容为空）
            "getResourceVersionList" -> return Bundle().apply {
                DebugUtil.i(
                    TAG,
                    "---call-getResourceVersionList-- ${CenterDbConstant.Version.RECORDER_RESOURCE_VERSION_LIST.size}"
                )
                this.putIntegerArrayList(
                    "resourceVersionList",
                    CenterDbConstant.Version.RECORDER_RESOURCE_VERSION_LIST.toMutableList() as ArrayList<Int>
                )
            }
        }
        return super.call(authority, method, arg, extras)
    }


    /**
     * 实现openFIle接口
     * 中子通过转文本路径，从录音获取转文本内容
     * @return 录音需要搜索到转文本内容
     */
    override fun openFile(uri: Uri, mode: String): ParcelFileDescriptor? {
        return openFile(uri, mode, null)
    }

    override fun openFile(
        uri: Uri,
        mode: String,
        signal: CancellationSignal?
    ): ParcelFileDescriptor? {
        if (!checkPackageAuth()) {
            DebugUtil.i(TAG, "openFile package name not match")
            throw RuntimeException("package name not match")
        }
        DebugUtil.i(TAG, "---openFile--")
        // for solve the file name contains special char, getEncodePath() be Truncated, exm:#
        val convertPath = uri.toString().removePrefix("content://" + uri.authority)
        if (TextUtils.isEmpty(convertPath)) {
            DebugUtil.i(TAG, "---openFile-- convertPath is null")
            return null
        }

        return try {
            openFile(convertPath)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "openFile----error $convertPath $e")
            throw e
        }
    }

    private fun openFile(filePath: String): ParcelFileDescriptor? {
        DebugUtil.i(TAG, "openFile----start $filePath")
        var pipe = ParcelFileDescriptor.createPipe()

        var bufferedWriter =
            BufferedWriter(
                OutputStreamWriter(
                    ParcelFileDescriptor.AutoCloseOutputStream(pipe[1]),
                    Charsets.UTF_8
                )
            )
        val reader = BufferedReader(InputStreamReader(FileInputStream(filePath), Charsets.UTF_8))

        GlobalScope.launch(Dispatchers.IO) {
            try {
                bufferedWriter?.use {
                    reader?.use {
                        var lineContentStr: String = ""
                        while (reader.readLine()?.also { lineContentStr = it } != null) {
                            lineContentStr.split(CONVERT_TEXT_SPLIT)?.let {
                                if (!it.isNullOrEmpty() && it.size > CONVERT_TEXT_POSITION) {
                                    bufferedWriter.write("${it[CONVERT_TEXT_POSITION]}")
                                }
                            }
                        }
                        bufferedWriter.flush()
                    }
                }
                DebugUtil.i(TAG, "openFile----end")
            } catch (e: Exception) {
                DebugUtil.e(TAG, "openFile----thread error $filePath  $e")
            }
        }

        return pipe?.get(0)
    }

    private fun checkPackageAuth(): Boolean {
        return CenterDbConstant.CENTER_DMP_PKG_NAME == callingPackage
    }

    internal interface TABLESCODE {
        companion object {
            const val RECORDS = 1
        }
    }
}