/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GradientDragBar.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/07/08
 ** Author      : W9035969(<EMAIL>)
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9035969     2025/07/08     1.0      create
 ***********************************************************************/
package com.soundrecorder.browsefile.view

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Shader
import android.util.AttributeSet
import android.view.View
import androidx.core.animation.doOnEnd
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.PathInterpolatorHelper
import kotlin.math.abs

class GradientDragBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "GradientView"
        const val ALPHA_1F = 1f
        const val DRAG_BAR_ALPHA_START_DURATION = 250L
        const val DRAG_BAR_ALPHA_EXIT_DURATION = 217L
        const val DRAG_NONE = 0
        const val DRAG_RIGHT = 1
        const val DRAG_LEFT = 2
    }
    private var mPaint: Paint? = null
    private var mLinearGradient: LinearGradient? = null

    private var mDragStartColor: Int = -1
    private var mDragEndColor: Int = -1

    private var mStartAnimtion: ValueAnimator? = null
    private var mExitAnimation: ValueAnimator? = null
    //private var mDragStartTime: Long = 0L
    private var mDragX: Float = 0f

    private var mIsFadeAnimRunning: Boolean = false
    private var mDirection = DRAG_NONE
    private var mOffsetX: Float = 0f

    private var mPointTime: Long = 0

    init {
        mPaint = Paint()
        mPaint?.isAntiAlias = true

        mDragStartColor = context.resources.getColor(com.soundrecorder.common.R.color.amp_drag_start_color, context.theme)
        mDragEndColor = context.resources.getColor(com.soundrecorder.common.R.color.amp_drag_end_color, context.theme)
    }

    fun startDragMove(dragX: Float, offsetX: Float, direction: Int) {
        this.mDragX = dragX
        this.mOffsetX = abs(offsetX)
        this.mDirection = direction
        postInvalidateOnAnimation()
    }

    fun setPointTime(pointTime: Long) {
        this.mPointTime = pointTime
    }

    fun setDirection(direction: Int) {
        this.mDirection = direction
    }

    fun setOffsetX(offsetX: Float) {
        this.mOffsetX = abs(offsetX)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        val colors = intArrayOf(mDragStartColor, mDragEndColor)
        if (mDirection == DRAG_NONE || mDirection == DRAG_RIGHT) {
            //从右到左渐变
            mLinearGradient = LinearGradient(width.toFloat(), 0f, 0f, 0f, colors, null, Shader.TileMode.CLAMP)
        } else {
            //从左到右渐变
            mLinearGradient = LinearGradient(0f, 0f, width.toFloat(), 0f, colors, null, Shader.TileMode.CLAMP)
        }
        mPaint?.shader = mLinearGradient
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        val previewBar = getAmpSeekBar() ?: return
        var startX = 0f
        var endX = 0f
        val dragBarWidth = calculateDragBarWidth()
        if (mDirection == DRAG_NONE || mDirection == DRAG_RIGHT) {
            startX = mDragX - dragBarWidth
            endX = mDragX + previewBar.getHalfPointWidth()
        } else {
            startX = mDragX
            endX = startX + dragBarWidth
        }
        //DebugUtil.d(TAG, "onDraw, startX:$startX, endX:$endX")
        mPaint?.let { canvas.drawRect(startX, 0f, endX, height.toFloat(), it) }
    }

    private fun calculateDragBarWidth(): Float {
        var dragBarWidth = getDragWidth()
        if (mOffsetX < getDragWidth()) {
            dragBarWidth = mOffsetX
        }
        //DebugUtil.d(TAG, "calculateDragBarWidth, dragBarWidth:$dragBarWidth")
        return dragBarWidth
    }

    private fun getDragWidth(): Float {
        return getContext().getResources().getDimension(com.soundrecorder.common.R.dimen.dp30)
    }

    private fun isRtl(): Boolean {
        return getAmpSeekBar()?.getReverseLayout() ?: false
    }

    private fun getAmpSeekBar(): AmplitudeSeekBar? {
        return parent as? AmplitudeSeekBar
    }

    private fun findChildXByTime(time: Long): Float {
        val preView = getAmpSeekBar()
        var x = 0f
        if (preView != null) {
            x = if (isRtl()) {
                width - preView.findChildXByTime(time)
            } else {
                preView.findChildXByTime(time)
            }
        }
        return x
    }

    fun startFadeAnimation() {
        if (mStartAnimtion?.isRunning == true || mIsFadeAnimRunning) {
            DebugUtil.d(TAG, "startFadeAnimation, animation is running")
            return
        }
        DebugUtil.d(TAG, "startFadeAnimation, start")
        mStartAnimtion = ValueAnimator.ofFloat(0f, ALPHA_1F).apply {
            duration = DRAG_BAR_ALPHA_START_DURATION
            interpolator = PathInterpolatorHelper.couiEaseInterpolator
            addUpdateListener {
                val alpha = it.animatedValue as Float
                //mPaint?.alpha = alpha
                <EMAIL> = alpha
                postInvalidateOnAnimation()
            }
            doOnEnd {
                cancelStartAnimation()
            }
            mIsFadeAnimRunning = true
            start()
        }
    }

    private fun cancelStartAnimation() {
        mStartAnimtion?.cancel()
        mStartAnimtion = null
    }

    fun exitFadeAnimation() {
        if (mExitAnimation?.isRunning == true) {
            DebugUtil.d(TAG, "exitFadeAnimation, animation is running")
            return
        }
        DebugUtil.d(TAG, "exitFadeAnimation, start")
        mExitAnimation = ValueAnimator.ofFloat(ALPHA_1F, 0f).apply {
            duration = DRAG_BAR_ALPHA_EXIT_DURATION
            interpolator = PathInterpolatorHelper.couiEaseInterpolator
            addUpdateListener {
                val alpha = it.animatedValue as Float
                <EMAIL> = alpha
                postInvalidateOnAnimation()
            }
            doOnEnd {
                cancelExitAnimation()
            }
            start()
        }
    }

    private fun cancelExitAnimation() {
        mExitAnimation?.cancel()
        mExitAnimation = null
        mIsFadeAnimRunning = false
    }
}