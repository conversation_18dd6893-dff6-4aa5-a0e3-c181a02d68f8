/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:The home page for managing recording groups
 ** Version: 1.0
 ** Date : 2025/01/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  ******** 2025/01/20      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.view

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.ViewTreeObserver
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.widget.Toolbar
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.forEach
import androidx.core.view.updatePadding
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.commit
import androidx.fragment.app.viewModels
import androidx.recyclerview.widget.COUILinearLayoutManager
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.backpressed.OnBackPressedListener
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.splitwindow.BaseFragment
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.base.view.DeDuplicateInsetsCallback
import com.soundrecorder.base.view.RootViewPersistentInsetsCallback
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.databinding.FragmentGroupBinding
import com.soundrecorder.browsefile.home.dialog.navigation.NavigationViewManager
import com.soundrecorder.browsefile.home.view.group.GroupManageRecyclerAdapter
import com.soundrecorder.browsefile.home.view.group.entity.GroupFactory
import com.soundrecorder.browsefile.home.view.group.entity.GroupItem
import com.soundrecorder.browsefile.home.view.group.entity.GroupRecyclerItemListener
import com.soundrecorder.browsefile.home.view.group.util.GroupItemConverter
import com.soundrecorder.browsefile.home.view.group.util.GroupManageUtils
import com.soundrecorder.browsefile.home.view.group.util.GroupManageUtils.scrollToPosition
import com.soundrecorder.browsefile.home.view.group.util.GroupViewModel
import com.soundrecorder.browsefile.home.view.group.util.LocalEditingGroupChannel
import com.soundrecorder.browsefile.home.view.group.util.RecordGroupEditViewModel
import com.soundrecorder.browsefile.home.view.group.util.RecordGroupItemManipulator
import com.soundrecorder.browsefile.home.view.group.util.SimpleItemTouchHelperCallBack
import com.soundrecorder.browsefile.parentchild.BrowseFileActivityViewModel
import com.soundrecorder.common.buryingpoint.BuryingPoint.addClickGroup
import com.soundrecorder.common.buryingpoint.BuryingPoint.addCreateNewGroup
import com.soundrecorder.common.buryingpoint.BuryingPoint.addDeleteGroup
import com.soundrecorder.common.buryingpoint.BuryingPoint.addRenameGroup
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.GroupInfoDbUtil
import com.soundrecorder.common.db.GroupInfoManager
import com.soundrecorder.common.fileoperator.CheckOperate
import com.soundrecorder.common.utils.taskbar.TaskBarUtil.setNavigationColorOnSupportTaskBar
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.utils.Injector
import java.lang.ref.WeakReference

class GroupFragment : BaseFragment<FragmentGroupBinding>(),
    OnBackPressedListener {
    companion object {
        const val TAG = "GroupFragment"

        const val MAX_CUSTOM_GROUP_INFO_NUM = 50
        fun show(containerId: Int, fm: FragmentManager, stackName: String?) {
            fm.commit {
                setCustomAnimations(
                    R.anim.group_open_slide_enter,
                    com.support.appcompat.R.anim.coui_open_slide_exit,
                    com.support.appcompat.R.anim.coui_close_slide_enter,
                    R.anim.group_close_slide_exit
                )
                add(containerId, newInstance(), "GroupFragment")
                if (!stackName.isNullOrBlank()) {
                    addToBackStack(stackName)
                    setReorderingAllowed(true)
                }
            }
        }

        private fun newInstance(): GroupFragment = GroupFragment()
    }

    private val groupInfoManager =
        GroupInfoManager.getInstance(context ?: BaseApplication.getAppContext())

    private var isChangedGroupItem: Boolean = false
    private var isCustomGroupInfoListNone: Boolean = true
    private var adapter: GroupManageRecyclerAdapter? = null
    private var recordingGroupRv: RecyclerView? = null
    private var onBackPressedCallback: OnBackPressedCallback? = null
    private var naviContainer: View? = null
    private var navi: COUINavigationView? = null
    private var selectAllMenuItem: MenuItem? = null

    private val groupViewModel: GroupViewModel by activityViewModels()
    private val recordGroupEditViewModel by viewModels<RecordGroupEditViewModel>()
    private val mBrowseFileActivityViewModel: BrowseFileActivityViewModel by activityViewModels()

    private var mNavigationViewManager: NavigationViewManager? = null
    private var processorCallbackAfterDeleteGroup: ((Boolean) -> Unit)? = null
    private var isDragEndUpdate: Boolean = false
    private var naviContainerHeight = 0

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val recordOptionCompletedListener =
        object : NavigationViewManager.OnOptionCompletedListener {
            override fun onOptionCompleted(
                option: Int,
                selectedMediaIdList: ArrayList<String>?,
                deleteRecordList: ArrayList<Record>?,
                isDeleteAll: Boolean
            ): Long {
                DebugUtil.i(logTag, "onOptionCompleted > $option")
                val delayTime = 0L
                //分组删除涉及到的录音记录，被删除后的回调在这里执行，此时再刷新分组管理UI
                if (option == CheckOperate.OPERATE_DELETE) {
                    forceRefresh()

                    cloudKitApi?.trigBackupNow(BaseApplication.getAppContext())
                    processorCallbackAfterDeleteGroup?.invoke(true)
                }
                return delayTime
            }
        }
    private var naviContainerObserver: NavigateContainerObserver? = null

    override fun layoutId(): Int {
        return R.layout.fragment_group
    }

    override fun onViewCreated(savedInstanceState: Bundle?) {
        (activity as? BaseActivity)?.registerBackPressed()
    }

    override var logTag: String = "GroupFragment"
    private val recordGroupItemManipulator = RecordGroupItemManipulator(this)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mBinding = DataBindingUtil.inflate(inflater, layoutId(), container, false)
        initiateWindowInsets()
        configToolbar()
        initRecyclerView(mBinding)
        initNavigation(mBinding)
        initObservers()
        initScrollToPosition()
        if (naviContainerObserver == null) {
            naviContainerObserver = NavigateContainerObserver(this)
            mBinding.naviContainer.viewTreeObserver.addOnDrawListener(naviContainerObserver)
        }
        val callback: RootViewPersistentInsetsCallback =
            object : RootViewPersistentInsetsCallback() {
                override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                    super.onApplyInsets(v, insets)
                    DebugUtil.i(TAG, "onApplyInsets")
                    setNavigationColorOnSupportTaskBar(
                        insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars()).bottom,
                        activity,
                        com.soundrecorder.base.R.color.common_background_color
                    )
                }
            }
        ViewCompat.setOnApplyWindowInsetsListener(mBinding.root, callback)
        return mBinding.root
    }

    override fun onDestroyView() {
        if (mBrowseFileActivityViewModel.isFileChanged) {
            mBrowseFileActivityViewModel.isFileChanged = false
            if (!isChangedGroupItem) {
                //如果未切换组且需要刷新，退出当前页面时，则通知显示加载UI且强刷一下首页列表
                mBrowseFileActivityViewModel.isShowLoadingWithRefresh.postValueSafe(true)
            }
        }
        if (mBrowseFileActivityViewModel.isGroupListShow.value == true) {
            mBrowseFileActivityViewModel.isGroupListShow.value = false
        }
        super.onDestroyView()
        naviContainerObserver?.let {
            mBinding.naviContainer.viewTreeObserver.removeOnDrawListener(it)
            naviContainerObserver = null
        }
        (activity as? BaseActivity)?.unRegisterBackPressed()
        ViewCompat.setOnApplyWindowInsetsListener(mBinding.root, null)
    }

    private fun configToolbar() {
        isCustomGroupInfoListNone = groupViewModel.customGroupInfoList.value?.size == 0
        mBinding.toolbar.setNavigationIcon(com.support.snackbar.R.drawable.coui_menu_ic_cancel)
        mBinding.toolbar.setNavigationOnClickListener {
            onBackPressed()
        }
        mBinding.toolbar.setTitle(com.soundrecorder.base.R.string.recording_group)
        mBinding.toolbar.inflateMenu(R.menu.group_menu_main)
        mBinding.toolbar.setOnMenuItemClickListener { menu ->
            when (menu.itemId) {
                R.id.group_menu_check -> {
                    //一期分组需求因无私密分组，所以无自定义分组时，按编辑按钮不触发编辑模式。
                    if (!isCustomGroupInfoListNone) {
                        toggleCheckableState(null)
                    }
                }

                R.id.group_menu_cancel -> onBackPressed()
                R.id.group_menu_select_all -> refreshToolbarState()
            }
            return@setOnMenuItemClickListener true
        }
    }

    override fun onBackPressed(): Boolean {
        if (isRecyclerViewInCheckableState()) {
            toggleCheckableState(recordingGroupRv)
        } else {
            triggerCancel()
        }
        return true
    }

    private fun triggerCancel() {
        DebugUtil.d(TAG, "triggerCancel")
        mBrowseFileActivityViewModel.isGroupListShow.value = false
        parentFragmentManager.popBackStack()
    }

    private fun initiateWindowInsets() {
        val stableRVPaddingBottom =
            resources.getDimensionPixelSize(R.dimen.group_fragment_rv_bottom_padding)
        val callback = object : DeDuplicateInsetsCallback() {
            override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                //使用statusBars的高度    systemBars的高度不对
                val stableStatusBarInsets =
                    insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
                //底部面板bottom在onLayoutChange中动态设置高度nagivationHeight
                val bottom = stableStatusBarInsets.bottom
                // view 顶部statusBar高度
                mBinding.coordinator.updatePadding(
                    top = stableStatusBarInsets.top,
                    left = stableStatusBarInsets.left, right = stableStatusBarInsets.right
                )
                mBinding.naviContainer.updatePadding(bottom = bottom)
                (mBinding.rvGroupList.layoutParams as? ViewGroup.MarginLayoutParams)?.bottomMargin =
                    bottom
                mBinding.rvGroupList.updatePadding(bottom = stableRVPaddingBottom)
            }
        }
        ViewCompat.setOnApplyWindowInsetsListener(mBinding.root, callback)
    }

    override fun onResume() {
        super.onResume()
        onBackPressedCallback?.isEnabled = true
    }

    override fun onPause() {
        super.onPause()
        onBackPressedCallback?.isEnabled = false
    }

    private fun initRecyclerView(binding: FragmentGroupBinding) {
        recordingGroupRv = binding.rvGroupList
        recordingGroupRv?.layoutManager = COUILinearLayoutManager(context)
        adapter = context?.let { ctx ->
            GroupManageRecyclerAdapter(
                ctx,
                GroupRecyclerItemListener(onSelectedChangedListener = { target, adapterSelectedCallback ->
                    triggerSelectRecordingGroup(target, adapterSelectedCallback)
                },
                    onCheckedChangedListener = {
                        refreshNavigationItemViewState()
                        refreshToolbarState()
                    },
                    onItemMoveListener = {
                        refreshNavigationItemViewState(isDrag = true, it)
                    },
                    onCreateNewGroupClickListener = {
                        addCreateNewGroup()
                        if ((GroupInfoDbUtil.getCustomGroupsFromDB(context).size < MAX_CUSTOM_GROUP_INFO_NUM)) {
                            GroupEditFragment.show(childFragmentManager, null)
                        } else {
                            ToastManager.showShortToast(
                                context,
                                com.soundrecorder.common.R.string.maximum_custom_group_limit
                            )
                        }
                    },
                    onDragResultListener = { groupManageItems ->
                        recordGroupEditViewModel.updateByDrag(
                            groupManageItems.mapNotNull { it.groupInfo }, callback = {
                                isDragEndUpdate = true
                                forceRefresh()
                            })
                    },
                    onItemLongClickListener = { position ->
                        //一期分组需求因无私密分组，所以无自定义分组时，长按不触发编辑模式。
                        if (!isCustomGroupInfoListNone) {
                            toggleCheckableState(null)
                            if (position != null) {
                                adapter?.updateCheckedDataAndRefreshUI(position)
                            }
                        }
                    })
            )
        }
        recordingGroupRv?.adapter = adapter
        recordingGroupRv?.addItemDecoration(COUIRecyclerView.COUIRecyclerViewItemDecoration(context))

        val callback: ItemTouchHelper.Callback = SimpleItemTouchHelperCallBack(adapter)
        val helper = ItemTouchHelper(callback)
        helper.attachToRecyclerView(recordingGroupRv)
        adapter?.setItemTouchHelper(helper)
    }

    private fun triggerSelectRecordingGroup(
        groupItem: GroupItem?,
        adapterSelectedCallback: () -> Unit
    ) {
        val selected = adapter?.getCurrentValues()?.find { it.selected }
        val targets = groupItem?.let { listOf(it) } ?: emptyList()
        if (targets.isEmpty()) {
            triggerCancel()
            return
        }
        recordGroupItemManipulator.select(selected, targets) { success ->
            if (success) {
                selected?.selected = false
                adapterSelectedCallback.invoke()
                isChangedGroupItem = groupViewModel.updateCurrentGroup(
                    groupItem?.groupInfo,
                    mBrowseFileActivityViewModel
                )
                triggerCancel()
                addClickGroup(groupItem?.groupInfo)
            }
        }
    }

    private fun refreshNavigationItemViewState(
        isDrag: Boolean = false,
        isUnDisable: Boolean = false
    ) {
        val isCheckableState = isRecyclerViewInCheckableState()

        mBinding.naviContainer.apply {
            post {
                visibility = if (isCheckableState) View.VISIBLE else View.GONE
            }
        }

        val checkedValues = adapter?.getCurrentValues()?.filter { it.checked } ?: emptyList()

        val editable = checkedValues.size == 1 &&
                checkedValues.find {
                    GroupFactory.isEmbedGroup(it.groupInfo)
                } == null
        val deletable = checkedValues.isNotEmpty() && checkedValues.find {
            GroupFactory.isEmbedGroup(it.groupInfo)
        } == null
        mBinding.groupNavi.menu.forEach { menuItem ->
            when (menuItem.itemId) {
                R.id.recording_group_navi_edit -> {
                    if (isDrag) {
                        menuItem.isEnabled = editable && isUnDisable
                    } else {
                        menuItem.isEnabled = editable
                    }
                }

                /*   R.id.recording_group_navi_encrypt -> {

                   }
   */
                R.id.recording_group_navi_delete -> {
                    if (isDrag) {
                        menuItem.isEnabled = deletable && isUnDisable
                    } else {
                        menuItem.isEnabled = deletable
                    }
                }
            }
        }
        refreshRVBottom(isCheckableState)
    }

    private fun refreshRVBottom(isCheckableState: Boolean) {
        mBinding.rvGroupList.apply {
            post {
                (layoutParams as MarginLayoutParams).bottomMargin =
                    if (isCheckableState) naviContainerHeight else 0
            }
        }
    }

    private fun refreshToolbarState() {
        val isCheckableState = isRecyclerViewInCheckableState()

        val toolbar = mBinding.toolbar
        val allCount =
            adapter?.getCurrentValues()
                ?.count { it.groupInfo != null && !GroupFactory.isDefaultGroup(it.groupInfo) }
                ?: 0
        var checkCount = adapter?.getCurrentValues()?.count { it.checked } ?: 0
        toolbar.apply {
            isTitleCenterStyle = isCheckableState
            clearMenu()
            inflateMenu(if (isCheckableState) R.menu.group_menu_main_edit else R.menu.group_menu_main)
            selectAllMenuItem = menu.findItem(R.id.group_menu_select_all)
            selectAllMenuItem?.setOnMenuItemClickListener {
                when (checkCount) {
                    allCount -> {
                        //取消全选
                        adapter?.apply {
                            values.forEach {
                                //"我的分组"标题item不需要设置。
                                if (it.groupInfo != null) {
                                    it.checked = false
                                }
                            }
                        }
                    }

                    else -> {
                        //全选
                        adapter?.apply {
                            values.forEach {
                                //"我的分组"标题item不需要设置。
                                if (it.groupInfo != null && !GroupFactory.isDefaultGroup(it.groupInfo)) {
                                    it.checked = true
                                }
                            }
                        }
                    }
                }
                adapter?.notifyDataSetChanged()
                checkCount = adapter?.getCurrentValues()?.count { it.checked } ?: 0
                refreshToolbarTitle(toolbar, isCheckableState, checkCount, allCount)
                refreshNavigationItemViewState()
                return@setOnMenuItemClickListener true
            }
            refreshToolbarTitle(toolbar, isCheckableState, checkCount, allCount)
        }
    }

    private fun refreshToolbarTitle(
        toolbar: Toolbar,
        isCheckableState: Boolean,
        checkCount: Int,
        allCount: Int
    ) {
        if (isCheckableState) {
            toolbar.navigationIcon = null
            when (checkCount) {
                0 -> {
                    toolbar.setTitle(com.soundrecorder.common.R.string.choose_item)
                    selectAllMenuItem?.title =
                        getString(com.soundrecorder.common.R.string.select_all)
                }

                allCount -> {
                    toolbar.setTitle(com.soundrecorder.common.R.string.selected_all_item)
                    selectAllMenuItem?.title =
                        getString(com.soundrecorder.common.R.string.record_delete_all_cancel)
                }

                else -> toolbar.title = getString(com.soundrecorder.common.R.string.item_select, checkCount)
            }
        } else {
            toolbar.setNavigationIcon(com.support.snackbar.R.drawable.coui_menu_ic_cancel)

            toolbar.setTitle(com.soundrecorder.common.R.string.recording_group)
        }
    }

    private fun isRecyclerViewInCheckableState(): Boolean {
        return adapter?.isCheckableState() ?: false
    }

    private val editingGroupReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            forceRefresh()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LocalEditingGroupChannel.register(context, editingGroupReceiver)
    }

    override fun onDestroy() {
        super.onDestroy()
        LocalEditingGroupChannel.unregister(context, editingGroupReceiver)
    }

    override fun onDetach() {
        super.onDetach()
        processorCallbackAfterDeleteGroup = null
    }

    private fun toggleCheckableState(recyclerView: RecyclerView?) {
        DebugUtil.d(TAG, "toggleCheckableState")
        // firstly,update list adapter check state.
        if (recyclerView != null) {
            adapter?.toggleCheckableState(recyclerView)
        } else {
            adapter?.toggleCheckableState(null)
        }

        // navi
        refreshNavigationItemViewState()
        // toolbar
        refreshToolbarState()
    }

    private fun initObservers() {
        groupViewModel.groupsInDb.observe(viewLifecycleOwner) { groupInfos ->
            if (isDragEndUpdate) { // 拖拽排序后第一次数据变化不处理。
                isDragEndUpdate = false
                return@observe
            }
            if (groupInfos != null) {
                adapter?.refresh(
                    mBinding.rvGroupList,
                    GroupItemConverter.groupInfo2GroupItem(
                        groupViewModel,
                        groupInfos
                    )
                )
            } else {
                DebugUtil.e(TAG, " initObservers groupInfos is null!")
            }
        }

        /*初次进来先初始化数据。*/
        // groupViewModel.groupsInDb.value = groupInfoManager.getAllGroupInfoList()
        forceRefresh()

        /**
         * 编辑item图标可见性更新。
         */
        groupViewModel.customGroupInfoList.observe(viewLifecycleOwner) {
            isCustomGroupInfoListNone = it.isEmpty()
            mBinding.toolbar.apply {
                post {
                    menu.findItem(R.id.group_menu_check)?.isVisible =
                        !isCustomGroupInfoListNone
                }
            }
        }
    }

    private fun initScrollToPosition() {
        recordingGroupRv?.let {
            GroupManageUtils.OneShotPostDrawListener.add(
                it,
                scrollToPosition(it, groupViewModel.currentGroup.value, adapter)
            )
        } ?: run {
            DebugUtil.w(TAG, "initScrollToPosition recyclerView is null! Don't scroll to position!")
        }
    }

    private fun initNavigation(binding: FragmentGroupBinding) {
        naviContainer = binding.naviContainer
        navi = binding.groupNavi
        navi?.setOnItemSelectedListener { menuItem ->
            when (menuItem.itemId) {
                R.id.recording_group_navi_edit -> triggerEditGroup()
                R.id.recording_group_navi_delete -> triggerDeleteGroup()
            }
            return@setOnItemSelectedListener true
        }
        //为了沿用原来删除录音记录的逻辑，复用NavigationViewManager，仅限删除分组功能使用
        if (mNavigationViewManager == null) {
            mNavigationViewManager = NavigationViewManager(activity, navi)
        }
        mNavigationViewManager?.onOptionCompletedListener = recordOptionCompletedListener
    }

    private fun triggerDeleteGroup() {
        val selected = adapter?.getCurrentValues()?.find { it.selected }
        val checkedValues = adapter?.getCurrentValues()?.filter { it.checked } ?: emptyList()
        val isAllChecked = adapter?.values?.map { it.groupInfo }
            ?.filter { !GroupFactory.isDefaultGroup(it) && it != null }?.size == checkedValues.size
        //获取删除录音记录的回调
        recordGroupItemManipulator.mRecordDeleteListener =
            mNavigationViewManager?.getDeleteListener()
        //构造一个删除分组后的回调，在recordOptionCompletedListener里调用
        processorCallbackAfterDeleteGroup = object : ((Boolean) -> Unit) {
            override fun invoke(isSuccess: Boolean) {
                if (isSuccess) {
                    toggleCheckableState(recordingGroupRv)
                    mBrowseFileActivityViewModel.liveDataGroupList.value =
                        groupInfoManager.getAllGroupInfoList()
                    //如果被删除的自定义分组之前是被选中为当前使用的分组，则重新设置”全部录音“分组为当前使用的分组
                    if (checkedValues.contains(selected)) {
                        adapter?.setDefaultSelectedGroup(recordingGroupRv)
                        val defaultCur = groupViewModel.findAllGroup()
                        isChangedGroupItem = groupViewModel.updateCurrentGroup(defaultCur, mBrowseFileActivityViewModel)
                    }
                }
            }
        }

        recordGroupItemManipulator.delete(
            selected,
            checkedValues,
            isAllChecked,
            groupViewModel.isCloudEnable,
            null
        )
        addDeleteGroup()
    }

    private fun triggerEditGroup() {
        val checked = adapter?.getCurrentValues()?.find { it.checked }
        GroupEditFragment.show(childFragmentManager, checked?.groupInfo)
        addRenameGroup()
    }

    private fun forceRefresh() {
        val list = GroupInfoManager.getInstance(BaseApplication.getAppContext()).getAllGroupInfoList()
        var total = 0
        var groupCount = 0
        list.forEach {
            if (it.isCustomGroup() || it.isRecentlyDeleteGroup()) {
                val size = groupViewModel.getRecordSizeFromDbByGroup(it)
                it.mGroupCount = size
                if (it.isCustomGroup()) {
                    groupCount += size
                    total += size
                    DebugUtil.d(TAG, "ShowCount ${it.mGroupName} size= $size")
                }
            } else if (it.isCommonGroup() || it.isCallingGroup()) {
                total += it.mGroupCount
            }
        }
        //更新一下总数，避免整组删除后总数无变化
        list.find { it.isAllGroup() }?.mGroupCount = total
        DebugUtil.d(TAG, "ShowCount forceRefresh groupCount size = $groupCount")
        groupViewModel.groupsInDb.postValueSafe(list)
        mBrowseFileActivityViewModel.liveDataGroupList.postValueSafe(list)
    }

    private class NavigateContainerObserver(groupFragment: GroupFragment) : ViewTreeObserver.OnDrawListener {
        private val weakFragment = WeakReference(groupFragment)
        override fun onDraw() {
            val fragment = weakFragment.get() ?: return
            if (fragment.isDetached) return
            fragment.naviContainerHeight = fragment.mBinding.naviContainer.height
            fragment.refreshRVBottom(fragment.isRecyclerViewInCheckableState())
        }
    }
}