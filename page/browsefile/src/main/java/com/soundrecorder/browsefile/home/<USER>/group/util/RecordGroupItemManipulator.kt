/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2025/01/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9017070 2025/01/20      1.0     create file
 ****************************************************************/

package com.soundrecorder.browsefile.home.view.group.util

import android.app.Activity
import android.content.Context
import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultCaller
import androidx.activity.viewModels
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.browsefile.home.view.group.entity.GroupItem
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.fileoperator.delete.OnFileDeleteListener

internal class RecordGroupItemManipulator<T>(private val host: T) where T : LifecycleOwner, T : ActivityResultCaller {

    companion object {
        private const val MODE_UNKNOWN = -1
    }

    private val _context: Context?
        get() = when (host) {
            is Fragment -> host.context
            is ComponentActivity -> host
            else -> null
        }

    private var processorCallback: ((Boolean) -> Unit)? = null
    var mRecordDeleteListener: OnFileDeleteListener? = null

    private val recordGroupEditViewModel by when (host) {
        is Fragment -> host.viewModels<RecordGroupEditViewModel>({ host.requireActivity() })
        is ComponentActivity -> host.viewModels<RecordGroupEditViewModel>()
        else -> lazy { null }
    }

    fun delete(
        selected: GroupItem?,
        items: List<GroupItem>,
        isAllChecked: Boolean,
        isCloudEnable: () -> Boolean,
        onClickCallback: ((Boolean) -> Unit)?
    ) {
        val context = _context
        if (context == null || selected == null || items.isEmpty()) {
            return
        }

        val title = if (isAllChecked) {
            context.getString(com.soundrecorder.common.R.string.title_delete_all_recording_group)
        } else if (items.size > 1) {
            context.resources.getQuantityString(com.soundrecorder.common.R.plurals.title_delete_recording_group_multi, items.size, items.size)
        } else {
            context.getString(com.soundrecorder.common.R.string.title_delete_recording_group)
        }
        val message = if (isCloudEnable.invoke()) {
            if (isAllChecked) {
                context.getString(com.soundrecorder.common.R.string.recording_group_delete_cloud_all)
            } else if (items.size > 1) {
                context.resources.getQuantityString(
                    com.soundrecorder.common.R.plurals.recording_group_delete_local_multi_cloud,
                    items.size,
                    items.size
                )
            } else {
                context.getString(com.soundrecorder.common.R.string.recording_group_delete_cloud)
            }
        } else {
            if (isAllChecked) {
                context.getString(com.soundrecorder.common.R.string.recording_group_delete_local_all)
            } else if (items.size > 1) {
                context.resources.getQuantityString(
                    com.soundrecorder.common.R.plurals.recording_group_delete_local_multi_local,
                    items.size,
                    items.size
                )
            } else {
                context.getString(com.soundrecorder.common.R.string.recording_group_delete_local)
            }
        }

        COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setBlurBackgroundDrawable(true)
            .setTitle(title)
            .setMessage(message)
            .setNeutralButton(com.soundrecorder.common.R.string.delete) { _, _ ->
                onClickCallback?.invoke(true)
                deleteInternal(items)
            }
            .setNegativeButton(com.soundrecorder.common.R.string.cancel) { _, _ ->
                onClickCallback?.invoke(false)
            }
            .setWindowAnimStyle(com.support.dialog.R.style.Animation_COUI_Dialog)
            .show()
    }

    private fun deleteInternal(items: List<GroupItem>) {
        if (_context != null) {
            val groupInfos = mutableListOf<GroupInfo>()
            items.forEach {
                if (it.groupInfo != null) {
                    groupInfos.add(it.groupInfo)
                }
            }
            recordGroupEditViewModel?.deleteGroupInfos(_context as Activity, groupInfos, mRecordDeleteListener)
        }
    }

    /**
     * @param selected 当前选中组
     * @param target 新选中组
     * @param skipCheck 是否跳过私密密码验证，加密组间切换不需要验证。
     */
    fun select(selected: GroupItem?, target: List<GroupItem>, skipCheck: Boolean = false, uiCallback: ((Boolean) -> Unit)? = null) {
        if (target.isEmpty()) {
            return
        }
        processorCallback = uiCallback
        selectInternal(target)
    }

    private fun selectInternal(target: List<GroupItem>) {
        target.forEach { it.selected = true }
        processorCallback?.invoke(true)
    }
}