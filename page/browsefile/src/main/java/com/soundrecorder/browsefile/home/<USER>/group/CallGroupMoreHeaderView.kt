/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CallGroupMoreHeaderView
 * Description:
 * Version: 1.0
 * Date: 2024/10/20
 * Author: W9035969(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9035969 2024/10/20 1.0 create
 */
package com.soundrecorder.browsefile.home.view.group

import android.content.Context
import android.widget.FrameLayout
import android.widget.TextView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.common.widget.CircleTextImage

class CallGroupMoreHeaderView(context: Context) : FrameLayout(context) {

    companion object {
        private const val TAG = "CallGroupMoreHeaderView"
    }
    private var mAvatar: CircleTextImage? = null
    private var mTvCallerName: TextView? = null
    private var mTvCount: TextView? = null

    init {
        inflate(context, R.layout.layout_call_record_more_header, this)

        mAvatar = findViewById(R.id.civ_avatar)
        mTvCallerName = findViewById(R.id.tv_caller_name)
        mTvCount = findViewById(R.id.tv_record_count)
    }

    fun setAvatar(resId: Int) {
        mAvatar?.setImageResource(resId)
    }

    fun setCallerName(callerName: String?, color: String?) {
        DebugUtil.d(TAG, "setCallerName, callerName:$callerName, color:$color")
        mTvCallerName?.setText(callerName)
        mAvatar?.setCircleImageText(callerName)
        color?.let {
            mAvatar?.setCircleImageColor(it)
        }
    }

    fun setCount(descCount: String) {
        mTvCount?.setText(descCount)
    }
}