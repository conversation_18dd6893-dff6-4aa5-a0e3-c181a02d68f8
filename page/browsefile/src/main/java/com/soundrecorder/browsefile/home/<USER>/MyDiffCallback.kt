/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MyDiffCallback.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/07/08
 ** Author      : ********(<EMAIL>)
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ********     2025/07/08     1.0      create
 ***********************************************************************/
package com.soundrecorder.browsefile.home.load

import androidx.recyclerview.widget.DiffUtil
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel

class MyDiffCallback : DiffUtil.Callback {

    private var oldList: MutableList<ItemBrowseRecordViewModel>? = null
    private var newList: MutableList<ItemBrowseRecordViewModel>? = null

    constructor(oldList: MutableList<ItemBrowseRecordViewModel>?, newList: MutableList<ItemBrowseRecordViewModel>?) {
        this.oldList = oldList
        this.newList = newList
    }

    override fun getOldListSize(): Int {
        return oldList?.size ?: 0
    }

    override fun getNewListSize(): Int {
        return newList?.size ?: 0
    }

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList?.get(oldItemPosition)?.mediaId == newList?.get(newItemPosition)?.mediaId
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        return oldList?.get(oldItemPosition)?.equals(newList?.get(newItemPosition)) == true
    }

    override fun getChangePayload(oldItemPosition: Int, newItemPosition: Int): Any? {
        //可选：返回变化的部分，用于部分更新
        return super.getChangePayload(oldItemPosition, newItemPosition)
    }
}