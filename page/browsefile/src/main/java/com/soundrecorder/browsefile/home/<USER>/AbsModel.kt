/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : AbsModel.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.home.load

import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.ext.TAG
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.home.item.BaseItemRecordViewModel
import kotlinx.coroutines.Job
import java.lang.ref.WeakReference

abstract class AbsModel<D : BaseItemRecordViewModel, T> {
    val liveViewStatus = MutableLiveData<ViewStatus>()
    private var mQuerying: Boolean = false
    private var mRefListener: WeakReference<OnDataReadyCompletedListener<D, T>>? = null
    open val logTag: String = "AbsModel"

    /**
     * 查询录音列表
     */
    abstract fun query(args: T, isGroupingByContact: Boolean, name: String? = null, job: Job?)

    /**
     * 加载录音列表波形数据
     */
    abstract fun queryAmplitudes(dataList: List<D>, group: T, isGroupingByContact: Boolean, name: String? = null)

    fun loadAmplitudes(dataList: List<D>, group: T, isGroupingByContact: Boolean, name: String? = null) {
        //liveViewStatus.postValueSafe(ViewStatus.QUERYING)
        DebugUtil.d(logTag, "loadAmplitudes, start")
        queryAmplitudes(dataList, group, isGroupingByContact, name)
    }

    fun refresh(args: T, isGroupingByContact: Boolean, name: String? = null, job: Job?) {
        mQuerying = true
        liveViewStatus.postValueSafe(ViewStatus.QUERYING)
        DebugUtil.d(TAG, "mQuerying start = ${System.currentTimeMillis()}")
        query(args, isGroupingByContact, name, job)
    }


    fun register(listener: OnDataReadyCompletedListener<D, T>) {
        if (mRefListener == null) {
            mRefListener = WeakReference(listener)
        }
    }

    protected fun notifyDataReadyCompleted(
        data: List<D>,
        args: T,
        name: String? = null,
        job: Job? = null
    ) {
        mRefListener?.get()?.onDataReadyCompleted(this, data, args, name, job)
        mQuerying = false
        /*判断是否因刷新任务取消，如果非取消状态，data为空才显示空数据图标的动画，
        否则在快速切换Tab过程中，因任务取消会闪现空数据图标动画*/
        val isJobNotCancelled = job?.isCancelled == false || job?.isCancelled == null
        if (data.isEmpty() && isJobNotCancelled) {
            liveViewStatus.postValueSafe(ViewStatus.EMPTY)
        } else {
            liveViewStatus.postValueSafe(ViewStatus.SHOW_CONTENT)
        }
    }

    protected fun notifyDataAmpLoadCompleted(dataList: MutableList<D>, group: T, name: String? = null) {
        mRefListener?.get()?.onDataAmpLoadCompleted(this, dataList, group, name)
    }
}