/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  HorizontalNestedRecyclerView.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/1/15
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import androidx.recyclerview.widget.COUIRecyclerView
import java.util.Locale

class HorizontalNestedRecyclerView : COUIRecyclerView {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context, attrs, defStyleAttr
    )

    override fun startNestedScroll(axes: Int, type: Int): Boolean {
        return false
    }

    override fun isNestedScrollingEnabled(): Boolean {
        return false
    }

    override fun getRightFadingEdgeStrength(): Float {
        return if (TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LAYOUT_DIRECTION_RTL) {
            super.getRightFadingEdgeStrength()
        } else {
            0f
        }
    }

    override fun getLeftFadingEdgeStrength(): Float {
        return if (TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == LAYOUT_DIRECTION_RTL) {
            0f
        } else {
            super.getLeftFadingEdgeStrength()
        }
    }
}