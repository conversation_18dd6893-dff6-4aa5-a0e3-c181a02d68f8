package com.soundrecorder.browsefile.search.load.center.filechange

import android.database.Cursor
import android.provider.MediaStore
import android.text.TextUtils
import android.util.ArrayMap
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.db.CursorHelper
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.search.load.center.CenterDbConstant.RECODER_PROJECTION
import com.soundrecorder.browsefile.search.load.center.databean.SearchInsertBean
import com.soundrecorder.common.db.MediaDBUtils
import java.lang.Exception


class CenterFileChangeDataFactory {
    private val TAG = "CenterFileChangeDataFactory"

    /**
     * query insert data from media-lib by media-id
     * @return pair-first: 没有查询到的文件idList
     * pair-second: 变更文件封装类List
     */
    fun queryUpdatedIdData(updateData: MutableList<Long>): Pair<MutableList<Long>, MutableList<SearchInsertBean>>? {
        if (updateData.isNullOrEmpty()) {
            return null
        }
        try {
            /*延迟300毫秒，避免媒体库更新不及时*/
            Thread.sleep(300)
        } catch (e: Exception) {
            DebugUtil.e(TAG, " queryUpdatedIdData sleep error $e")
        }
        /*ensure some not exist data sync to center-dmp*/
        val notExistIdList = mutableListOf<Long>()

        // true：过滤录音不支持的mimeType
        val updateDatatList = queryInsertBeanByIds(updateData, true)
        try {
            /*the all update data is not exist*/
            if (updateDatatList.isNullOrEmpty()) {
                notExistIdList.addAll(updateData)
                return Pair(notExistIdList, updateDatatList)
            }

            //part of update data is not exist
            //query convert info
            val map = mutableMapOf<Long, String?>()
            ConvertDbUtil.selectByIds(updateData)?.forEach {
                map[it.recordId] = it.convertTextfilePath
            }
            val existIdList = mutableListOf<Long>()
            updateDatatList.forEach {
                existIdList.add(it.id)
                if (!map[it.id].isNullOrBlank()) {
                    it.text_path = map.get(it.id) ?: ""
                }
            }
            if (updateData.size != existIdList.size) {
                notExistIdList.addAll(updateData.subtract(existIdList))
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "packageUpdateData error ")
        }

        return Pair(notExistIdList, updateDatatList)
    }


    fun genLocalDataList(cursor: Cursor?): Pair<List<SearchInsertBean>, HashMap<String, Long>> {
        val compareList = mutableListOf<SearchInsertBean>()
        val mediaMap: HashMap<String, Long> = hashMapOf()
        try {
            DebugUtil.i(TAG, "genLocalDataList cursorCount is: ${cursor?.count}")
            cursor?.use {
                if (cursor.count > 0) {
                    val convertPathList = getConvertPathMap()
                    var storageBean: SearchInsertBean?
                    while (cursor.moveToNext()) {
                        val mediaId = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Audio.AudioColumns._ID))
                        val allPath = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Audio.AudioColumns.DATA))
                        if (mediaId > 0) {
                            mediaMap[allPath.toLowerCase()] = mediaId
                            storageBean = SearchInsertBean(cursor).apply {
                                this.text_path = convertPathList?.get(mediaId) ?: ""
                            }
                            compareList.add(storageBean)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "genLocalDataList error", e)
        }

        return Pair(compareList, mediaMap)
    }

    private fun getConvertPathMap(): ArrayMap<Long, String> {
        val completeConvertPath: ArrayMap<Long, String> = ArrayMap()
        val convertVads: MutableList<ConvertRecord> = ConvertDbUtil.selectAll()
        for (record: ConvertRecord in convertVads) {
            var id: Long = record.getRecordId()
            if (id == -1L) {
                id = MediaDBUtils.queryIdByData(record.getMediaPath())
                ConvertDbUtil.updateRecordIdByMediaPath(record.getMediaPath(), id)
            }
            if (!TextUtils.isEmpty(record.getConvertTextfilePath())) {
                completeConvertPath.put(id, record.getConvertTextfilePath())
            }
        }
        return completeConvertPath
    }

    fun queryInsertBeanByIds(rowIds: List<Long?>?, limitMimeType: Boolean): MutableList<SearchInsertBean> {
        val searchInsertList: MutableList<SearchInsertBean> = ArrayList()
        var cursor: Cursor? = null
        var selection =
            MediaStore.Audio.Media.SIZE + "!=0 AND " + MediaStore.Audio.Media._ID + " in (" + TextUtils.join(",",
                rowIds!!) + ")"
        var selectionArgs: Array<String?>? = null
        // 限制录音指定mimeType
        if (limitMimeType) {
            selection += " AND " + MediaStore.Audio.Media.MIME_TYPE + CursorHelper.getsAcceptableAudioTypesSQL()
            selectionArgs = CursorHelper.getsAcceptableAudioTypes()
        }
        var insertBean: SearchInsertBean
        try {
            cursor = BaseApplication.getAppContext().contentResolver
                .query(MediaDBUtils.BASE_URI, RECODER_PROJECTION, selection, selectionArgs, null)
            if (cursor != null && cursor.count > 0) {
                while (cursor.moveToNext()) {
                    insertBean = SearchInsertBean(cursor)
                    searchInsertList.add(insertBean)
                }
            }
        } catch (e: Throwable) {
            DebugUtil.e(TAG, "query media data error", e)
        } finally {
            cursor?.close()
        }
        return searchInsertList
    }
}