package com.soundrecorder.browsefile.home.view.cloudtip

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.tips.def.COUIDefaultTopTips
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.home.item.BrowseAdapter
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.modulerouter.cloudkit.tipstatus.CloudGuideState

class CloudGuideTipView(context: Context) : FrameLayout(context) {

    private var mAdapter: BrowseAdapter? = null
    private var tipView: COUIDefaultTopTips? = null
    private var onCloudGuideTipListener: OnCloudGuideTipListener? = null
    init {
        tipView = COUIDefaultTopTips(context).apply {
            iniView(this)
        }
        addView(tipView)
        layoutParams = LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        updateLayoutParams<LayoutParams> {
            setMargins(
                0,
                context.resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp8),
                0,
                context.resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp4)
            )
        }
    }

    fun bindAdapter(adapter: BrowseAdapter?) {
        this.mAdapter = adapter
    }

    private fun iniView(tipView: COUIDefaultTopTips) {
        tipView.setStartIcon(
            ContextCompat.getDrawable(tipView.context, R.drawable.ic_cloudkit_guide_tips),
        )
        tipView.setTipsText(context.getString(com.soundrecorder.common.R.string.cloudkit_guide_tips))
        tipView.setNegativeButton(context.getString(com.soundrecorder.common.R.string.cloud_ignore))
        tipView.setPositiveButton(context.getString(com.soundrecorder.common.R.string.permission_open_dialog))

        tipView.setNegativeButtonListener {
            onIgnoreButtonClick()
        }
        tipView.setPositiveButtonListener {
            onCloudGuideTipListener?.onCloudTipOpen(it)
        }
    }

    private fun onIgnoreButtonClick() {
        updateSPValueWhenClickIgnore()
        mAdapter?.hideCloudHeader()
        CloudStaticsUtil.addCloudTipsClickIgnoreEvent()
    }

    private fun updateSPValueWhenClickIgnore() {
        when (PrefUtil.getInt(
            BaseApplication.getAppContext(),
            PrefUtil.KEY_GUIDE_STATE,
            CloudGuideState.GUIDE_STATE_DEFAULT
        )) {
            CloudGuideState.GUIDE_STATE_DEFAULT -> {
                PrefUtil.putInt(
                    context,
                    PrefUtil.KEY_GUIDE_STATE,
                    CloudGuideState.GUIDE_STATE_IGNORE
                )
                PrefUtil.putLong(
                    context,
                    PrefUtil.KEY_GUIDE_IGNORE_TIME,
                    System.currentTimeMillis()
                )
            }

            CloudGuideState.GUIDE_STATE_IGNORE -> {
                PrefUtil.putInt(
                    context,
                    PrefUtil.KEY_GUIDE_STATE,
                    CloudGuideState.GUIDE_STATE_IGNORE_AGAIN
                )
            }
        }
    }

    fun release() {
        tipView = null
        mAdapter = null
    }

    fun setOnCloudGuideTipListener(listener: OnCloudGuideTipListener?) {
        this.onCloudGuideTipListener = listener
    }

    interface OnCloudGuideTipListener {
        fun onCloudTipOpen(view: View)
    }
}
