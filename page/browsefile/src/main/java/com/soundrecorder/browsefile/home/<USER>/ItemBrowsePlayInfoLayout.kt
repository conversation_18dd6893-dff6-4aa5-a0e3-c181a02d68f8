/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/3/2
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.view

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.animation.PathInterpolator
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.currentHasHourFormatTimeExclusive
import com.soundrecorder.base.ext.durationHasHourFormatTimeExclusive
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.view.AmplitudeSeekBar
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.utils.cancelAnimationExt
import com.soundrecorder.common.utils.invisible
import com.soundrecorder.common.utils.playAnimationExt
import com.soundrecorder.common.utils.visible

/**
 * 首页快捷播放布局，包括播放进度、当前时间和总时间
 */
class ItemBrowsePlayInfoLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        const val TAG = "ItemBrowsePlayInfoLayout"

        const val PROGRESS_MAX = 1000
        const val MS_100 = 100
    }

    private var playAmpSeekBar: AmplitudeSeekBar? = null
    private var playProgress: TextView? = null
    private var playTotalDuration: TextView? = null
    private var ampLoading: EffectiveAnimationView? = null

    var pointMoveListener: AmplitudeSeekBar.PointMoveListener? = null

    var duration: Long = 0
        set(value) {
            field = value
            playTotalDuration?.text = value.durationHasHourFormatTimeExclusive(true)
            playAmpSeekBar?.setTotalTime(value)
        }

    private var enterAnim: Animator? = null
    private var exitAnim: Animator? = null

    init {
        LayoutInflater.from(context).inflate(
            R.layout.item_play_info,
            this,
            true
        )

        playTotalDuration = findViewById(R.id.play_total_duration)
        playProgress = findViewById(R.id.playProgress)
        playAmpSeekBar = findViewById(R.id.playAmplitudeBar)
        ampLoading = findViewById(R.id.amp_loading)
        //initPlayAmplitudeBar()
    }

    fun startAmpLoadingAnimation() {
        DebugUtil.d(TAG, "startAmpLoadingAnimation")
        ampLoading?.playAnimationExt()
        playAmpSeekBar?.invisible()
    }

    fun cancelAmpLoadingAnimation() {
        DebugUtil.d(TAG, "cancelAmpLoadingAnimation")
        ampLoading?.cancelAnimationExt()
        playAmpSeekBar?.visible()
    }

    fun initPlayAmpSeekBar() {
        if (BaseApplication.sIsRTLanguage) {
            playAmpSeekBar?.setReverseLayout(true)
        }
        playAmpSeekBar?.setListenter(pointMoveListener)
    }

    fun setAmplitudes(amplitudes: MutableList<Int>) {
        setTotalTime(duration)
        if (amplitudes.isNotEmpty()) {
            initPlayAmpSeekBar()
            playAmpSeekBar?.setAmplitudes(amplitudes)
        }
    }

    fun isEmptyAmps(): Boolean {
        return playAmpSeekBar?.getAmplitudes().isNullOrEmpty()
    }

    fun setSelectTime(time: Long?) {
        playAmpSeekBar?.setSelectTime(time ?: 0)
    }

    fun setTotalTime(duration: Long) {
        playAmpSeekBar?.setTotalTime(duration)
    }

    fun updatePlayProgress(curTime: Long?): Boolean {
        if (pointMoveListener?.getTrackingTouch() == true) {
            //When the finger drags the seek bar, don't update progress.
            DebugUtil.i(TAG, "When the finger drags the seek bar, no update is progress.")
            return false
        }
        playAmpSeekBar?.setPointTime(curTime ?: 0, false)
        updatePlayProgressText(curTime ?: 0)
        return true
    }

    fun updatePlayProgressText(curTime: Long) {
        playProgress?.text = curTime.currentHasHourFormatTimeExclusive(duration)
    }

    private fun durationToSeekBarProgress(timeInMs: Long): Int {
        return if ((duration > 0) && (duration - timeInMs >= MS_100)) {
            (timeInMs / duration.toFloat() * PROGRESS_MAX).toInt()
        } else {
            PROGRESS_MAX
        }
    }

    private fun seekBarProgressToDuration(progress: Int): Long {
        return (progress / PROGRESS_MAX.toFloat() * duration).toLong()
    }

    fun isNotCompleteVisible(): Boolean {
        return !isVisible || layoutParams.height < getLayoutHeight()
    }

    private fun getLayoutHeight(): Int {
        val resources = BaseApplication.getApplication().resources
        val defaultHeight = resources.getDimensionPixelSize(R.dimen.seekbar_layout_height) + resources.getDimensionPixelSize(
            com.soundrecorder.common.R.dimen.dp12
        )

        /**
         * offset 字体大小切换显示不全容错值 16sp和16dp不对等
         */
        val offset = resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.sp16) - resources.getDimensionPixelSize(
            com.soundrecorder.common.R.dimen.dp16
        )
        DebugUtil.d(TAG, "getLayoutHeight, defaultHeight:$defaultHeight, offset:$offset")
        return defaultHeight + (if (offset < 0) 0 else offset)
    }

    /**
     * 展开动效
     * 如果正在展开，不处理
     * 如果在关闭，取消掉关闭，并从当前状态展开
     */
    fun startOrContinueEnterAnim(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        if (enterAnim?.isRunning == true) {
            DebugUtil.i(TAG, "startOrContinueEnterAnim, enter anim is running")
            return
        }

        cancelExitAnimation()
        continueEnterAnim(extraAnimator, animatorListener)
    }

    private fun continueEnterAnim(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        //从当前高度执行
        DebugUtil.i(TAG, "continueEnterAnim")
        val cardHeightAnim = ValueAnimator.ofInt(layoutParams.height, getLayoutHeight()).apply {
            duration = 367
            interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
            addUpdateListener {
                setItemHeight(it.animatedValue as Int)
            }
        }

        val textAlphaAnim = ValueAnimator.ofFloat((playProgress?.alpha ?: 0f), 1f).apply {
            duration = 250
            startDelay = 117
            interpolator = PathInterpolator(0.5f, 0f, 0.67f, 1f)
            addUpdateListener {
                playProgress?.alpha = it.animatedValue as Float
                playTotalDuration?.alpha = it.animatedValue as Float
            }
        }

        val seekBarAlphaAnim = ObjectAnimator.ofFloat(playAmpSeekBar, "alpha", (playAmpSeekBar?.alpha ?: 0f), 1f).apply {
            duration = 317
            startDelay = 50
            interpolator = PathInterpolator(0.5f, 0f, 0.7f, 1f)
        }

        val animatorList = mutableListOf<Animator>().also {
            if (!extraAnimator.isNullOrEmpty()) {
                it.addAll(extraAnimator)
            }
            it.add(cardHeightAnim)
            it.add(textAlphaAnim)
            it.add(seekBarAlphaAnim)
        }
        enterAnim = AnimatorSet().also {
            it.playTogether(animatorList)
            it.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    animatorListener?.onAnimationStart(animation)
                    visibility = View.VISIBLE
                }

                override fun onAnimationEnd(animation: Animator) {
                    animatorListener?.onAnimationEnd(animation)
                    visibility = View.VISIBLE
                    animation.removeListener(this)
                    enterAnim = null
                }
            })
            it.start()
        }
    }

    /**
     * 关闭动效
     * 如果正在关闭，不处理
     * 如果在展开，取消掉展开，并从当前状态关闭
     */
    fun startOrContinueExitAnim(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        if (exitAnim?.isRunning == true) {
            DebugUtil.i(TAG, "startOrContinueEnterAnim, exit anim is running")
            return
        }

        cancelEnterAnimation()
        continueExitAnim(extraAnimator, animatorListener)
    }

    private fun continueExitAnim(
        extraAnimator: Array<Animator>? = null,
        animatorListener: AnimatorListenerAdapter? = null
    ) {
        DebugUtil.i(TAG, "continueExitAnim")
        val cardHeightAnim = ValueAnimator.ofInt(layoutParams.height, 0).apply {
            duration = 367
            interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
            addUpdateListener {
                setItemHeight(it.animatedValue as Int)
            }
        }
        val textAlphaAnim = ValueAnimator.ofFloat((playProgress?.alpha ?: 0f), 0f).apply {
            duration = 100
            interpolator = PathInterpolator(0.2f, 0f, 1f, 1f)
            addUpdateListener {
                playProgress?.alpha = it.animatedValue as Float
                playTotalDuration?.alpha = it.animatedValue as Float
            }
        }

        val seekBarAlphaAnim = ObjectAnimator.ofFloat(playAmpSeekBar, "alpha", (playAmpSeekBar?.alpha ?: 0f), 0f).apply {
            duration = 133
            interpolator = PathInterpolator(0.33f, 0f, 0.7f, 1f)
        }

        val animatorList = mutableListOf<Animator>().also {
            if (!extraAnimator.isNullOrEmpty()) {
                it.addAll(extraAnimator)
            }
            it.add(cardHeightAnim)
            it.add(textAlphaAnim)
            it.add(seekBarAlphaAnim)
        }
        exitAnim = AnimatorSet().also {
            it.playTogether(animatorList)
            it.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animation: Animator) {
                    DebugUtil.d(TAG, "onAnimationStart")
                    animatorListener?.onAnimationStart(animation)
                    visibility = View.VISIBLE
                }

                override fun onAnimationEnd(animation: Animator) {
                    DebugUtil.d(TAG, "onAnimationEnd")
                    animatorListener?.onAnimationEnd(animation)
                    visibility = View.GONE
                    /*停止fling等滚动，避免播放A，拖动seekbar快速切B-A，A才执行stopTracking，导致又从上次位置继续播放*/
                    //seekBar.stopPhysicsMove()

                    animation.removeListener(this)
                    exitAnim = null
                }
            })
            it.start()
        }
    }

    private fun setItemHeight(height: Int) {
        val tempLayoutParams = layoutParams
        if (tempLayoutParams.height != height) {
            tempLayoutParams.height = height
            layoutParams = tempLayoutParams
        }
    }

    fun setExpandState() {
        playProgress?.alpha = 1f
        playTotalDuration?.alpha = 1f
        playAmpSeekBar?.alpha = 1f
        setItemHeight(getLayoutHeight())
        visibility = View.VISIBLE
    }

    fun setShrinkState() {
        playProgress?.alpha = 0f
        playTotalDuration?.alpha = 0f
        playAmpSeekBar?.alpha = 0f
        setItemHeight(0)
        visibility = View.GONE
    }

    fun getPlayProgress(): TextView? {
        return playProgress
    }

    fun getPlayTotalDuration(): TextView? {
        return playTotalDuration
    }

    fun cancelAnimation() {
        cancelEnterAnimation()
        cancelExitAnimation()
    }

    private fun cancelEnterAnimation() {
        enterAnim?.cancel()
        enterAnim?.removeAllListeners()
        enterAnim = null
    }

    private fun cancelExitAnimation() {
        exitAnim?.cancel()
        exitAnim?.removeAllListeners()
        exitAnim = null
    }

    fun isAnimating(): Boolean {
        return !(enterAnim == null && exitAnim == null)
    }
}

interface OnSeekBarChangeListener {
    fun onStartTrackingTouch()
    fun onSeekToTime(curTime: Long)
    fun updateTouchSeekbar(touch: Boolean)
    fun getTrackingTouch(): Boolean
}