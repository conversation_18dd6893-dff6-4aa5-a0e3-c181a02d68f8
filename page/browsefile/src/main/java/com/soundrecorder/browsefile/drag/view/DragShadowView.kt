/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DragShadowView
 * Description:
 * Version: 1.0
 * Date: 2023/12/20
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/12/20 1.0 create
 */

package com.soundrecorder.browsefile.drag.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Point
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.reddot.COUIHintRedDot
import com.coui.appcompat.roundRect.COUIRoundDrawable
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R
import java.lang.ref.WeakReference

class DragShadowView(
    view: View,
    private val dragDropCount: Int,
    hasRadius: Boolean,
    shadowRadius: Float? = null,
    parentScale: Float = 1.0F,
    bgColor: Int? = null
) : View.DragShadowBuilder(view) {

    private val scale: Float
    private val width: Int
    private val height: Int
    private var layoutPadding: Float
    private val layoutMarginEnd: Float
    private val layoutMarginTop: Int

    private val mShadowView: WeakReference<View>

    companion object {
        private const val TAG = "NoteCommonDragShadow"
    }


    init {
        // 计算shadow大小
        val needCut = ShadowContentHelper.getScale(view.context, (view.width * parentScale).toInt(),
                (view.height * parentScale).toInt()).also {
            scale = parentScale * it.first
        }.second
        layoutPadding =
                view.context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_layout_padding)
                        .toFloat()
        if (scale > 1.0F) {
            layoutPadding *= scale
        }
        layoutMarginEnd =
                view.context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_layout_margin_end)
                        .toFloat()
        layoutMarginTop =
                view.context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_item_layout_margin_top)

        val rootView =
                LayoutInflater.from(view.context).inflate(R.layout.drawshadow_item_layout, null)
        rootView.findViewById<ShadowWrapperView>(R.id.shadow_content).run {
            val shadowContent = if (hasRadius && (!needCut)) {
                ViewDefaultShadowContent(view, scale, dragDropCount)
            } else {
                val contentBitmap = ShadowContentHelper.getBimapFromViewWithRadius(
                        view, scale, ShadowContentHelper.getShowRect(view, parentScale),
                        shadowRadius
                                ?: view.context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_blur_radius)
                                        .toFloat()
                )
                BitmapDefaultShadowContent(view.context, contentBitmap, dragDropCount)
            }
            if (bgColor != null) {
                shadowContent.bgColor = bgColor
            }
            if (shadowRadius != null) {
                shadowContent.contentShadowRadius = shadowRadius
            }
            updateShadowContent(shadowContent)
            <EMAIL> =
                    ((shadowContent.getScale() * shadowContent.getContentWidth())
                            + (2 * layoutPadding)
                            + layoutMarginEnd).toInt()
            <EMAIL> =
                    ((shadowContent.getScale() * shadowContent.getContentHeight())
                            + (2 * layoutPadding)
                            + layoutMarginTop + getItemsExtraHeight()).toInt()
        }

        mShadowView = WeakReference(rootView)
        initCountView(rootView)
        // Important for certain APIs
        rootView.setLayerType(View.LAYER_TYPE_HARDWARE, null)

        DebugUtil.d(TAG, "init w=$width h=$height s=$scale vw=${view.width} vh=${view.height} oriScale=$parentScale")
    }

    override fun onProvideShadowMetrics(outShadowSize: Point, outShadowTouchPoint: Point) {
        super.onProvideShadowMetrics(outShadowSize, outShadowTouchPoint)
        outShadowSize.set(width, height)
        DebugUtil.d(TAG, "onProvideShadowMetrics w=$width h=$height")
        outShadowTouchPoint.set(width / 2, height / 2)
    }

    override fun onDrawShadow(canvas: Canvas) {
        val r = canvas.clipBounds
        DebugUtil.d(TAG, "onDrawShadow $r")
        val shadowView = mShadowView.get() ?: return

        // Calling measure is necessary in order for all child views to get correctly laid out.
        shadowView.measure(
                View.MeasureSpec.makeMeasureSpec(r.right - r.left, View.MeasureSpec.EXACTLY),
                View.MeasureSpec.makeMeasureSpec(r.bottom - r.top, View.MeasureSpec.EXACTLY)
        )
        shadowView.findViewById<ShadowWrapperView>(R.id.shadow_content).let {
            it.measureShadowBg()
            shadowView.layout(r.left, r.top, r.right, r.bottom)
            shadowView.draw(canvas)
            it.shadowContent?.release()
        }
    }

    private fun initCountView(shadowView: View) {
        val countView: COUIHintRedDot =
                shadowView.findViewById(R.id.dragshadow_item_count) ?: return
        countView.pointMode = COUIHintRedDot.POINT_WITH_NUM_MODE
        if (dragDropCount > 1) {
            countView.pointNumber = dragDropCount
            countView.visibility = View.VISIBLE
        } else {
            countView.visibility = View.GONE
        }
    }
}

abstract class DefaultShadowContent(
    private val contentContext: Context,
    private val contentScale: Float,
    private val contentItemCount: Int,
) : ShadowWrapperView.ShadowContent {
    var contentShadowRadius: Float = contentContext.resources
            .getDimensionPixelOffset(R.dimen.drag_shadow_layout_background_radius).toFloat()
    var contentShadowColor: Int = contentContext.getColor(R.color.drag_shadow_item_shadow_color)
    var contentShadowBlur: Float = contentContext.resources.getDimensionPixelOffset(R.dimen.drag_shadow_blur_radius).toFloat()
    var bgColor: Int = contentContext.getColor(R.color.drag_shadow_bg_color)
    var drawContent: Boolean = false
    override fun getShadowRadius() = contentShadowRadius

    override fun getShadowColor() = contentShadowColor

    override fun getScale() = contentScale

    override fun getContext() = contentContext

    override fun getItemCount() = contentItemCount

    override fun getShadowBlur() = contentShadowBlur
    override fun getContentBgColor() = bgColor
    override fun isDrawContentBg() = drawContent
}

class ViewDefaultShadowContent(
    private val vw: View,
    scale: Float,
    itemCount: Int,
) : DefaultShadowContent(
        vw.context, scale, itemCount
) {
    init {
        drawContent = (vw as? ViewGroup)?.getChildAt(0)?.background !is COUIRoundDrawable
    }

    override fun drawContent(canvas: Canvas) {
        vw.draw(canvas)
    }

    override fun getContentWidth() = vw.width.toFloat()
    override fun getContentHeight() = vw.height.toFloat()
}

class BitmapDefaultShadowContent(context: Context, private val bitmap: Bitmap, itemCount: Int) :
        DefaultShadowContent(context, 1.0F, itemCount) {
    override fun drawContent(canvas: Canvas) {
        canvas.drawBitmap(bitmap, 0F, 0F, null)
    }

    override fun getContentWidth() = bitmap.width.toFloat()
    override fun getContentHeight() = bitmap.height.toFloat()

    override fun release() {
        bitmap.recycle()
    }
}