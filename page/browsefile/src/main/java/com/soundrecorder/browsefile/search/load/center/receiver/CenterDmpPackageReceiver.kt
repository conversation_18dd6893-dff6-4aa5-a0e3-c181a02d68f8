package com.soundrecorder.browsefile.search.load.center.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.soundrecorder.base.ext.getStringExtraSecure
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import com.soundrecorder.browsefile.search.load.center.CenterDbConstant
import com.soundrecorder.browsefile.search.load.center.CenterDbManager
import com.soundrecorder.browsefile.search.load.center.CenterDbUtils
import com.soundrecorder.base.utils.DebugUtil

private const val ACTION_CENTER_DMP_ENABLE_STATUS = "com.oplus.dmp.action.STATUS"

/**
 * 接收中子发送广播以及中子清除数据广播
 * 中子接口文档：https://hio.oppo.com/app/ozone/neutron/gotoOzoneCircleKbItemDetail?source=index&cataLog_id=339667&enc_kbi_id=158086471_157605808&page=ozone&is_more_document=0&folder_id=339657&type=ARTICLE
 */
class CenterDmpPackageReceiver : BroadcastReceiver() {
    companion object {
        const val TAG = "CenterDmpReceiver"
        private const val DEFAULT_INDEX_STATUS = -2
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        intent?.let {
            val action = intent.action
            DebugUtil.i(TAG, "onReceive action: $action")
            when (action) {
                Intent.ACTION_PACKAGE_DATA_CLEARED -> {
                    val uri = intent.data
                    if (uri != null) {
                        val pkgName = uri.schemeSpecificPart
                        if (CenterDbConstant.CENTER_DMP_PKG_NAME.equals(pkgName, ignoreCase = true)) {
                            DebugUtil.i(TAG, "center dmp is removed or cleared, pkgName = $pkgName")
                            notifyDmpWhenDmpClearAppData()
                        }
                    }
                }
                /** key: "status"
                 * value:  “fatal”：unenable
                 * “available”：enable
                 * */
                ACTION_CENTER_DMP_ENABLE_STATUS -> {
                    // “fatal”：不可工作; “available”：可工作
                    val enableStatus = it.getStringExtraSecure("status")
                    // “dmp”：dmp的status “recorder”：录音业务的status
                    val resource = it.getStringExtraSecure("resource")
                    // 初始索引状态：-1 未建立； 0建立成功（建立成功后会发送广播）； 1建立失败；2已创建建索引任务，但未执行；3 正在建立
                    val indexStatus = it.getIntExtra("indexStatus", DEFAULT_INDEX_STATUS)
                    DebugUtil.i(TAG, "center dmp enable status $enableStatus  resource=$resource indexStatus=$indexStatus")
                    if (enableStatus.isNullOrBlank().not()) {
                        CenterDbUtils.saveDmpEnableStatus(enableStatus)
                    }
                }
            }
        }
    }

    /**
     * 调用中子方法，唤起中子应用
     * initSearchProvider() 内部含触发中子兜底逻辑-（中子检测本地数据库无录音数据）
     * 初始化兜底，只会兜底 500条数据，全量兜底还是需要调用中子兜底方法
     */
    private fun notifyDmpWhenDmpClearAppData() {
        GlobalScope.launch(Dispatchers.IO) {
            // 有执行时序，需先调用初始化搜索服务
            CenterDbManager.getDmpConfigInfo()
            CenterDbManager.notifyDmpAllSync()
        }
    }
}