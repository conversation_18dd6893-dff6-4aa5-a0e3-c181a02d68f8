/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FastPlayHelper
 * Description:
 * Version: 1.0
 * Date: 2022/11/15
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/11/15 1.0 create
 */

package com.soundrecorder.browsefile.home.item

import androidx.core.net.toUri
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.home.item.item.BrowseWavePlayController
import com.soundrecorder.common.base.PlayerHelperCallback
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.task.ActivityTaskUtils
import com.soundrecorder.player.status.PlayStatus

class FastPlayHelper() : PlayerHelperCallback {
    private val mLogTag = "FastPlayHelper"
    var taskId: Int? = null
    val mediaPlayerManager by lazy {
        BrowseWavePlayController(this)
    }
    var displayNameLiveData: MutableLiveData<String> = MutableLiveData()
    var itemBrowseRecordViewModel: ItemBrowseRecordViewModel? = null
        private set

    fun reset() {
        if (itemBrowseRecordViewModel != null) {
            DebugUtil.d(mLogTag, "reset")
            mediaPlayerManager.fastPlayMediaUri = null
            mediaPlayerManager.releasePlay()
            mediaPlayerManager.onRelease()
            itemBrowseRecordViewModel?.mPlayState = PlayStatus.PLAYER_STATE_INIT
            itemBrowseRecordViewModel = null
            displayNameLiveData.value = ""
        }
    }

    fun setPlayRecordItem(recordViewModel: ItemBrowseRecordViewModel) {
        if (recordViewModel.mediaId == itemBrowseRecordViewModel?.mediaId) {
            return
        }
        DebugUtil.i(mLogTag, "setPlayRecordItem ${recordViewModel.displayName}")
        itemBrowseRecordViewModel?.let {
            mediaPlayerManager.releasePlay()
            it.mPlayState = PlayStatus.PLAYER_STATE_INIT
        }
        itemBrowseRecordViewModel = recordViewModel
        displayNameLiveData.value = recordViewModel.displayName ?: ""
        if (recordViewModel.isRecycle) {
            val recyclePath =  recordViewModel.recyclePath ?: recordViewModel.data
            recyclePath?.let {
                mediaPlayerManager.fastPlayMediaUri = it.toUri()
            }
        } else {
            mediaPlayerManager.fastPlayMediaUri = MediaDBUtils.genUri(recordViewModel.mediaId)
        }
        mediaPlayerManager.mDuration.value = recordViewModel.mDuration
        // 调整UI展开逻辑，跟播放状态无关
        changePlayerState(ItemBrowseRecordViewModel.PLAYER_STATE_AUTO_MARK)
    }

    fun onStopSeekBar(timeMill: Long) {
        if (mediaPlayerManager.hasInit) {
            mediaPlayerManager.seekTime(timeMill)
        } else {
            //若没有初始化，thread没有跑起来，没法发送message更新currentTimeMill
            mediaPlayerManager.doSeekTime(timeMill)
        }
        mediaPlayerManager.onResetPlayState()
    }


    override fun hasPaused(): Boolean {
        return mediaPlayerManager.hasPaused()
    }

    override fun getDuration(): Long {
        return itemBrowseRecordViewModel?.mDuration ?: 0
    }

    override fun getCurrentPlayerTime(): Long {
        return mediaPlayerManager.getCurrentPlayerTime()
    }

    override fun getPlayerName(): MutableLiveData<String> {
        return displayNameLiveData
    }

    override fun getPlayerMimeType(): String? {
        return itemBrowseRecordViewModel?.mimeType
    }

    override fun playBtnClick() {
        if (mediaPlayerManager.mIsTouchSeekbar.value == true) {
            DebugUtil.i(mLogTag, "playBtnClick  mIsTouchSeekbar = true")
            return
        }
        DebugUtil.i(mLogTag, "playBtnClick,init=${mediaPlayerManager.hasInit}")
        PermissionUtils.checkNotificationPermission(ActivityTaskUtils.topActivityOnFrontTask())
        ensurePlayerCreated()
        mediaPlayerManager.playBtnClick()
    }

    private fun ensurePlayerCreated() {
        if (!mediaPlayerManager.hasInit) {
            mediaPlayerManager.mTaskId = taskId
            mediaPlayerManager.init()
        }
    }

    fun getPlayerState(): Int = mediaPlayerManager.playerState.value ?: PlayStatus.PLAYER_STATE_INIT

    fun hasPlayingRecord(): Boolean {
        return itemBrowseRecordViewModel != null
                && getPlayerState().run { this != PlayStatus.PLAYER_STATE_INIT && this != PlayStatus.PLAYER_STATE_HALTON }
    }

    fun changePlayerState(state: Int) {
        mediaPlayerManager.setPlayerStatus(state)
    }
}