package com.soundrecorder.browsefile.search.load.center.databean

import androidx.annotation.Keep
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.search.load.center.CenterDbConstant
import com.soundrecorder.browsefile.search.load.center.CenterDbUtils
import com.soundrecorder.browsefile.search.utils.ForNoteUtil

@Keep
data class SearchRequestBean(
    var keyword: String,
    var pageNum: Int = 1,
    var pageSize: Int = 100,
    @Transient val isFromOtherApp: Boolean
) {
    /*中子返回转文本内容，匹配关键字前后截断字数配置*/
    @Transient
    val SEARCH_RESULT_CHAR_LENGTH_BEFORE_KEYWORD =
        BaseApplication.getAppContext().resources.getInteger(R.integer.search_result_text_length_before_keywords)

    @Transient
    val SEARCH_RESULT_CHAR_LENGTH_AFTER_KEYWORD =
        BaseApplication.getAppContext().resources.getInteger(R.integer.search_result_text_length_after_keywords)

    var index: String = CenterDbConstant.PROVIDER_INDEX

    /*筛选搜索条件，目前只针对 recorder的bucket*/
    var filterBy: FilterBean? = null

    /**
     * 需要增加taskID、useSnapshot两个字段用于搜索分页加载更多数据使用缓存数据，而不是实时查(用户删除新增音频后，分页index错误导致count错误)
     * taskID需要全局唯一，例如uuid,搜索关键词变更就需要变更
     * useSnapshot为bool类型，设置为true即为使用快照模式*/
    var taskID: String = ""
    var useSnapshot = true

    /*搜索匹配策略：Recording search strategy*/
    var searchStrategy: SearchStrategy = SearchStrategy().apply {
        // match file name
        val strategyName =
            SearchFields("RecorderSplitAndExtendsPinYin", "display_name_search", 1000.0)
        val strategyNameChar =
            SearchFields("RecorderSubStringAndIntersection", "display_name_subString", 1000.0)
        // match convert text content
        val strategyText = SearchFields("RecorderSplitAndIntersection", "text_content", 0.6)
        val strategyTextSingleChar =
            SearchFields("RecorderSingleCharSplitAndIntersection", "text_content_character", 0.5)
        val strategyTextDoubleChar =
            SearchFields("DoubleCharAndIntersectionStrategy", "text_content_double_character", 0.6)


        this.searchFields.add(strategyName)
        this.searchFields.add(strategyNameChar)
        /*多任务进来，不匹配转文本内容*/
        if (!isFromOtherApp) {
            this.searchFields.add(strategyText)
            this.searchFields.add(strategyTextSingleChar)
            // 该策略在中子 V1.0.5上增加，未调整策略版本，为兼容1.0.5下中子版本，该处动态判断一下，避免后端提bug
            // 后期正式发布后，可删除此逻辑判断
            if (CenterDbUtils.isSupportDoubleCharStrategy()) {
                this.searchFields.add(strategyTextDoubleChar)
            }
        }
    }

    /*高亮策略配置：can't be change*/
    var highlight: MutableList<Highlight> = mutableListOf<Highlight>().apply {
        this.add(Highlight(highlighter = "RecorderHighlighter", field = "display_name"))
        /*extras: char-length before and after the matching result*/
        this.add(
            Highlight(
                highlighter = "RecorderHighlighter", field = "highlight",
                extras = "$SEARCH_RESULT_CHAR_LENGTH_BEFORE_KEYWORD,$SEARCH_RESULT_CHAR_LENGTH_AFTER_KEYWORD"
            )
        )
    }


    /*排序策略*/
    var sortBy: List<SortRule> = mutableListOf<SortRule>().apply {
        if (ForNoteUtil.isSupportNeutronVersion()) {
            this.add(SortRule("CommonBriefSorter", "summary_text_flag", "desc"))
        }
        /*Do not modify the list-child order,Affect the sorting rules: priority first > second*/
        this.add(SortRule("BasicScoreSorter", "domain", "desc"))
        this.add(SortRule())
    }

    /*返回结果含dbDocCount信息数据配置*/
//    var statistics: List<StatisticsItem> = mutableListOf<StatisticsItem>().apply {
//        this.add(StatisticsItem("DocCountStatistic", "recorder"))
//    }

    data class SearchStrategy(
        val searchFields: MutableList<SearchFields> = mutableListOf(),
        val fieldRelation: String = "UnionFieldStrategy"
    )

    data class SearchFields(val strategy: String, val fieldName: String, val fieldBoost: Double)

    data class Highlight(val highlighter: String, val field: String, val extras: String? = null)

    data class FilterBean(
        val field: String = "bucket_filter",
        val method: String = "Equal",
        var value: String
    )

    data class SortRule(
        val sorter: String = "TimestampPayloadSorter",
        val field: String = "payload",
        var type: String = "desc"
    )

    data class StatisticsItem(val stat: String, val field: String)

    override fun toString(): String {
        return "SearchRequestBean(keyword='$keyword', pageNum=$pageNum, pageSize=$pageSize, index='$index', filterBy=$filterBy, searchStrategy=$searchStrategy, highlight=$highlight, sortBy=$sortBy)"
    }
}