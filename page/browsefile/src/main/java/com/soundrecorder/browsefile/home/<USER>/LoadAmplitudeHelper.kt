/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LoadAmplitudeHelper.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/07/08
 ** Author      : W9035969(<EMAIL>)
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9035969     2025/07/08     1.0      create
 ***********************************************************************/
package com.soundrecorder.browsefile.home.load

import android.net.Uri
import androidx.core.net.toUri
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.modulerouter.waveMark.WaveConstant
import com.soundrecorder.modulerouter.waveMark.WaveMarkInterface
import java.util.concurrent.ConcurrentHashMap

object LoadAmplitudeHelper {

    private const val TAG = "LoadAmplitudeHelper"
    /*已获取过的波形数据*/
    private val cacheAmplitudes = ConcurrentHashMap<Long, ItemBrowseRecordViewModel>()
    //正在保存的录音波形
    private val savingAmplitudes = ConcurrentHashMap<String, MutableList<Int>>()

    private val waveMarkApi by lazy {
        Injector.injectFactory<WaveMarkInterface>()
    }

    fun addSavingAmplitudes(path: String, ampList: MutableList<Int>) {
        DebugUtil.d(TAG, "addSavingAmplitudes, path:$path, ampList:${ampList.size}")
        savingAmplitudes.put(path, ampList)
    }

    fun getSavingAmplitudes(item: ItemBrowseRecordViewModel): MutableList<Int>? {
        if (savingAmplitudes.isNullOrEmpty()) {
            return null
        }
        if (item.data.isNullOrEmpty()) {
            return null
        }
        var ampList: MutableList<Int>? = null
        if (savingAmplitudes.containsKey(item.data)) {
            ampList = savingAmplitudes[item.data]
        }
        if (!ampList.isNullOrEmpty()) {
            item.ampList = ampList
            cacheAmplitudes[item.mediaId] = item
        }
        return ampList
    }

    fun clearSavingAmplitudes() {
        savingAmplitudes.clear()
    }

    /**
     * 获取单个录音的波形数据
     */
    fun loadSingleAmplitude(
        item: ItemBrowseRecordViewModel,
        callBack: ((ampList: MutableList<Int>?) -> Unit)? = null,
        decodeFinishCallback: ((ampList: MutableList<Int>?) -> Unit)? = null
    ) {
        var uri: Uri? = null
        var path: String? = null
        if (item.isRecycle) {
            path = item.recyclePath
            uri = item.recyclePath?.toUri()
        } else {
            path = item.data
            uri = MediaDBUtils.genUri(item.mediaId)
        }
        if (path.isNullOrEmpty() || uri == null) {
            return
        }
        var ampReadyFinish = false
        /*1.外录文件先解析部分波形回调 ampReadyCallback，然后返回真实波形数据回调decodeFinishCallback
        2.内部文件直接回调 decodeFinishCallback*/
        waveMarkApi?.getAmplitudeByType(path, uri, item.isRecycle, WaveConstant.COMPRESS_90_AMP_TYPE, false,
            ampReadyCallback = { ampReadyList ->
                DebugUtil.d(TAG, "loadSingleAmplitude, ampReadyList.size:${ampReadyList?.size}")
                ampReadyFinish = true
                if (!ampReadyList.isNullOrEmpty()) {
                    addCacheAmpList(item, ampReadyList)
                }
                refreshAmpUi(ampReadyList, item, callBack)
            },
            decodeFinishCallback = { ampSegmentsList ->
                DebugUtil.d(TAG, "loadSingleAmplitude, ampSegmentsList:${ampSegmentsList?.size}")
                decodeAmpFinish(ampSegmentsList, item, decodeFinishCallback)
                if (!ampReadyFinish) {
                    refreshAmpUi(ampSegmentsList, item, callBack)
                }
            })
    }

    private fun decodeAmpFinish(
        ampSegmentsList: List<Int>?,
        item: ItemBrowseRecordViewModel,
        decodeFinishCallback: ((MutableList<Int>?) -> Unit)?
    ) {
        if (!ampSegmentsList.isNullOrEmpty()) {
            addCacheAmpList(item, ampSegmentsList)
            decodeFinishCallback?.invoke(ampSegmentsList.toMutableList())
        } else {
            decodeFinishCallback?.invoke(null)
        }
    }

    private fun addCacheAmpList(item: ItemBrowseRecordViewModel, ampList: List<Int>) {
        item.ampList = ampList.toMutableList()
        cacheAmplitudes[item.mediaId] = item
    }

    private fun refreshAmpUi(
        ampReadyList: List<Int>?,
        item: ItemBrowseRecordViewModel,
        callBack: ((MutableList<Int>?) -> Unit)?
    ) {
        if (!ampReadyList.isNullOrEmpty()) {
            item.ampList = ampReadyList.toMutableList()
            callBack?.invoke(ampReadyList.toMutableList())
        } else {
            callBack?.invoke(null)
        }
    }

    /**
     * 获取指定item的波形数据
     */
    fun getAmpList(item: ItemBrowseRecordViewModel?): MutableList<Int>? {
        if (cacheAmplitudes.isNullOrEmpty() || item == null) {
            return null
        }
        val ampModel = if (cacheAmplitudes.containsKey(item.mediaId)) {
            cacheAmplitudes[item.mediaId]
        } else {
            null
        }
        return ampModel?.ampList
    }

    fun clearAllAmplitudes() {
        DebugUtil.d(TAG, "clearAllAmplitudes")
        cacheAmplitudes.clear()
        savingAmplitudes.clear()
    }
}