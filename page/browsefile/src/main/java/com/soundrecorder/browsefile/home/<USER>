/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ItemAnimationUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.view.animation.PathInterpolator
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.browsefile.R
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.isGone
import com.soundrecorder.common.utils.isVisible
import com.soundrecorder.common.utils.visible

object ItemAnimationUtil {
    const val TAG = "ItemAnimationUtil"

    const val DURATION_240 = 240L
    const val DURATION_400 = 400L
    const val DURATION_183 = 183L
    const val DURATION_120 = 120L
    const val DURATION_130 = 130L
    const val FLOAT_PERCENT_0 = 0F
    const val FLOAT_PERCENT_1 = 1F
    const val FLOAT_PERCENT_20 = 0.2f
    const val FLOAT_PERCENT_58 = 0.58F

    @SuppressLint("ObjectAnimatorBinding")
    @JvmStatic
    fun animWithListener(
        view: View,
        startHeight: Int,
        endHeight: Int,
        duration: Long,
        listener: Animator.AnimatorListener?
    ): ObjectAnimator {
        val anim = ObjectAnimator.ofInt(view, "view", startHeight, endHeight).setDuration(duration)
        anim.start()
        anim.addUpdateListener { valueAnimator ->
            var cVal = 0
            val animatValue = valueAnimator.animatedValue
            if (animatValue != null) {
                cVal = animatValue as Int
            }
            val lp = view.layoutParams as? RecyclerView.LayoutParams
            if (lp != null && cVal > 0) {
                lp.height = cVal
                view.layoutParams = lp
            }
        }
        anim.addListener(object : AnimatorListenerAdapter() {
            private var mIsCancel = false

            override fun onAnimationCancel(animation: Animator) {
                mIsCancel = true
                super.onAnimationCancel(animation)
            }

            override fun onAnimationEnd(animator: Animator) {
                super.onAnimationEnd(animator)
                val lp = view.layoutParams as RecyclerView.LayoutParams
                lp.height = RecyclerView.LayoutParams.WRAP_CONTENT
                view.layoutParams = lp
                if (!mIsCancel) {
                    listener?.onAnimationEnd(animator)
                }
            }
        })

        return anim
    }

    @JvmStatic
    fun transitionAnimator(view: View?, transitionY: Int, listener: Animator.AnimatorListener?): AnimatorSet {
        val animatorSet = AnimatorSet()
        val viewTransAnimator =
            ObjectAnimator.ofFloat(view, "translationY", -transitionY.toFloat(), 0f)
        val viewAlphaAnimator = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        animatorSet.duration = NumberConstant.NUM_240.toLong()
        animatorSet.interpolator = PathInterpolator(
            FLOAT_PERCENT_0,
            FLOAT_PERCENT_0,
            FLOAT_PERCENT_58,
            FLOAT_PERCENT_1
        )
        animatorSet.playTogether(viewTransAnimator, viewAlphaAnimator)
        animatorSet.addListener(object : AnimatorListenerAdapter() {
            private var mIsCancel = false

            override fun onAnimationCancel(animation: Animator) {
                mIsCancel = true
                super.onAnimationCancel(animation)
                view?.alpha = FLOAT_PERCENT_1
                view?.translationY = FLOAT_PERCENT_0
            }

            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                if (!mIsCancel) {
                    listener?.onAnimationEnd(animation)
                }
            }
        })
        animatorSet.start()

        return animatorSet
    }

    @SuppressLint("ObjectAnimatorBinding")
    @JvmStatic
    fun transAlphaTopHeaderAnimator(
        view: View?,
        startHeight: Int,
        endHeight: Int,
        listener: Animator.AnimatorListener?
    ) {
        if (view == null) {
            DebugUtil.i(TAG, "transAlphaTopHeaderAnimator inputView empty, return ")
            return
        }
        val animatorSet = AnimatorSet()
        val transAnim = ObjectAnimator.ofInt(view, "view", startHeight, endHeight)
        val content = view.findViewById<TextView>(R.id.content)
        val action = view.findViewById<TextView>(com.support.tips.R.id.action)
        val ignore = view.findViewById<TextView>(com.google.android.material.R.id.ignore)
        val icon = view.findViewById<ImageView>(androidx.core.R.id.icon)
        val alphaAnimator1 = ObjectAnimator.ofFloat(content, "alpha", 1f, 0f)
        val alphaAnimator2 = ObjectAnimator.ofFloat(action, "alpha", 1f, 0f)
        val alphaAnimator3 = ObjectAnimator.ofFloat(ignore, "alpha", 1f, 0f)
        transAnim.duration = DURATION_400
        alphaAnimator1.duration = DURATION_183
        alphaAnimator2.duration = DURATION_183
        alphaAnimator3.duration = DURATION_183
        if (icon != null) {
            val alphaAnimator4 = ObjectAnimator.ofFloat(icon, "alpha", 1f, 0f)
            alphaAnimator4.duration = DURATION_183
            animatorSet.playTogether(
                transAnim,
                alphaAnimator1,
                alphaAnimator2,
                alphaAnimator3,
                alphaAnimator4
            )
        } else {
            animatorSet.playTogether(transAnim, alphaAnimator1, alphaAnimator2, alphaAnimator3)
        }
        transAnim.addUpdateListener { valueAnimator ->
            var cVal = 0
            val animatValue = valueAnimator.animatedValue
            if (animatValue != null) {
                cVal = animatValue as Int
            }
            val lp =
                view.layoutParams as? RecyclerView.LayoutParams
            if (lp != null && cVal > 0) {
                lp.height = cVal
                view.layoutParams = lp
            }
        }
        transAnim.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                super.onAnimationEnd(animation)
                val lp = view.layoutParams as? RecyclerView.LayoutParams
                if (lp != null) {
                    lp.height = RecyclerView.LayoutParams.WRAP_CONTENT
                    view.layoutParams = lp
                }
                listener?.onAnimationEnd(animation)
            }
        })
        animatorSet.start()
    }

    fun recordLayoutDismissAnimator(
        recorderLayout: ViewGroup?,
        recordGradienImg: View?,
        context: Context
    ): AnimatorSet {
        val animatorSet = AnimatorSet()
        val gradientViewTransAnimator = ObjectAnimator.ofFloat(
            recorderLayout,
            View.TRANSLATION_Y,
            0f,
            context.resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp10).toFloat()
        )
        val gradientViewAlpahAnimator = ObjectAnimator.ofFloat(recorderLayout, View.ALPHA, 1f, 0f)
        gradientViewTransAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animator: Animator) {
                animator.removeListener(this)
                if (!recorderLayout.isGone()) {
                    recorderLayout.gone()
                }
                if (!recordGradienImg.isGone()) {
                    recordGradienImg.gone()
                }
            }
        })
        val gradientViewTransAnimator1 = ObjectAnimator.ofFloat(
            recordGradienImg,
            View.TRANSLATION_Y,
            0f,
            context.resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp10).toFloat()
        )
        animatorSet.duration = DURATION_130
        animatorSet.interpolator =
            PathInterpolator(FLOAT_PERCENT_20, FLOAT_PERCENT_0, FLOAT_PERCENT_1, FLOAT_PERCENT_1)
        animatorSet.duration = DURATION_130
        animatorSet.interpolator =
            PathInterpolator(FLOAT_PERCENT_20, FLOAT_PERCENT_0, FLOAT_PERCENT_1, FLOAT_PERCENT_1)
        animatorSet.playTogether(
            gradientViewTransAnimator, gradientViewTransAnimator1,
            gradientViewAlpahAnimator
        )
        animatorSet.start()
        return animatorSet
    }

    fun recordLayoutAppearAnimator(
        recorderLayout: ViewGroup?,
        recordGradienImg: View?,
        context: Context
    ): AnimatorSet {
        val animatorSet = AnimatorSet()
        val gradientViewTransAnimator = ObjectAnimator.ofFloat(
            recorderLayout, View.TRANSLATION_Y,
            context.resources.getDimensionPixelOffset(com.soundrecorder.base.R.dimen.dp10).toFloat(), 0f
        )
        val gradientViewAlpahAnimator = ObjectAnimator.ofFloat(recorderLayout, View.ALPHA, 0f, 1f)
        gradientViewTransAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animator: Animator) {
                animator.removeListener(this)
                if (!recorderLayout.isVisible()) {
                    recorderLayout.visible()
                }
                if (!recordGradienImg.isVisible()) {
                    recordGradienImg.visible()
                }
            }
        })
        val gradientViewTransAnimator1 = ObjectAnimator.ofFloat(
            recordGradienImg,
            View.TRANSLATION_Y,
            context.resources.getDimensionPixelOffset(com.soundrecorder.base.R.dimen.dp10).toFloat(),
            FLOAT_PERCENT_1
        )
        animatorSet.duration = DURATION_120
        animatorSet.interpolator = PathInterpolator(
            FLOAT_PERCENT_20,
            FLOAT_PERCENT_0,
            FLOAT_PERCENT_1,
            FLOAT_PERCENT_1
        )
        animatorSet.duration = DURATION_120
        animatorSet.interpolator = PathInterpolator(
            FLOAT_PERCENT_20,
            FLOAT_PERCENT_0,
            FLOAT_PERCENT_1,
            FLOAT_PERCENT_1
        )
        animatorSet.playTogether(
            gradientViewTransAnimator, gradientViewTransAnimator1,
            gradientViewAlpahAnimator
        )
        animatorSet.start()
        return animatorSet
    }
}