package com.soundrecorder.browsefile.home

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.soundrecorder.base.ext.registerReceiverCompat
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.RecordFileChangeNotify
import com.soundrecorder.modulerouter.playback.NOTIFY_CONVERT_STATUS_UPDATE
import com.soundrecorder.modulerouter.playback.NOTIFY_SMART_NAME_STATUS_UPDATE
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.summary.ACTION_SUMMARY_STATE_CHANGED
import com.soundrecorder.player.speaker.SpeakerReceiver

class ReceiverUtils(val mContext: Context, val isFromOtherProcess: Boolean) {
    private var mOnReceive: IReceiverOnReceive? = null

    companion object {
        const val TAG = "ReceiverUtils"

        @JvmStatic
        private var mIsUnRegisterSpeakerReceiver: Boolean = false
    }

    fun setOnReceive(onReceive: IReceiverOnReceive) {
        mOnReceive = onReceive
    }

    private val mCustomLocalReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            mOnReceive?.onReceive(intent)
        }
    }

    private val mMountEventReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            mOnReceive?.onReceive(intent)
        }
    }

    private val mUserChangeReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            DebugUtil.i(TAG, "onReceive ${intent?.action}")
            mOnReceive?.onReceive(intent)
        }
    }

    private val mLocalConvertBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            mOnReceive?.onReceive(intent)
        }
    }

    private val mLocalBrenoBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            mOnReceive?.onReceive(intent)
        }
    }

    private val mLocalSummaryBroadcastReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            mOnReceive?.onReceive(intent)
        }
    }

    private val mFilePermissionReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent?) {
            mOnReceive?.onReceive(intent)
        }
    }

    private val mSpeakerReceiver = SpeakerReceiver.getInstance()
    fun registerAllBrowseFileReceiver() {
        registerStorageReceiver()
        registerCustomReceiver()
        registerConvertReceiver()
        registerUserReceiver()
        registerBrenoToRecordReceiver()
        registerSpeakerReceiver()
        registerSummaryReceiver()
        registerFilePermissionReceiver()
    }

    private fun registerFilePermissionReceiver() {
        val filter = IntentFilter()
        filter.addAction(PermissionUtils.ACTION_FILE_PERMISSION)
        mContext.registerReceiver(mFilePermissionReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
    }

    private fun registerUserReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(Intent.ACTION_USER_BACKGROUND)
        mContext.registerReceiver(mUserChangeReceiver, intentFilter)
    }

    private fun registerStorageReceiver() {
        val aIntentFilter = IntentFilter()
        aIntentFilter.addAction(Intent.ACTION_MEDIA_EJECT)
        aIntentFilter.addAction(Intent.ACTION_MEDIA_MOUNTED)
        aIntentFilter.addAction(Intent.ACTION_MEDIA_SCANNER_FINISHED)
        aIntentFilter.addDataScheme("file")
        mContext.registerReceiverCompat(mMountEventReceiver, aIntentFilter, Constants.PERMISSION_OPPO_COMPONENT_SAFE, null)
    }

    private fun registerConvertReceiver() {
        val convertFilter = IntentFilter()
        convertFilter.addAction(NOTIFY_CONVERT_STATUS_UPDATE)
        convertFilter.addAction(NOTIFY_SMART_NAME_STATUS_UPDATE)
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mLocalConvertBroadcastReceiver, convertFilter)
    }

    private fun registerCustomReceiver() {
        val filter = IntentFilter()
        filter.addAction(RecordFileChangeNotify.FILE_UPDATE_ACTION)
        filter.addAction(RecordFileChangeNotify.FILE_CUT_NEW_RECORD_ACTION)
        filter.addAction(RecorderDataConstant.ACTION_UPDATE_CALLER_NAME_AVATAR_COLOR)

        filter.addAction(RecorderDataConstant.SMART_NAME_FILE_PERMISSION_ACTION)
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mCustomLocalReceiver, filter)
    }

    private fun registerBrenoToRecordReceiver() {
        DebugUtil.i(TAG, "registerBrenoToRecordReceiver")
        val filter = IntentFilter()
        filter.addAction(RecordFileChangeNotify.BRENO_FRONT_TO_RECORD)
        filter.addAction(RecordFileChangeNotify.CUBE_CLEAR_PLAY_RECORD_DATA)
        LocalBroadcastManager.getInstance(mContext)
            .registerReceiver(mLocalBrenoBroadcastReceiver, filter)
    }

    private fun registerSpeakerReceiver() {
        mIsUnRegisterSpeakerReceiver = false
        mContext.registerReceiver(mSpeakerReceiver, SpeakerReceiver.getIntentFilter())
    }

    private fun unRegisterSpeakerReceiver() {
        DebugUtil.d(TAG, "unRegisterSpeakerReceiver is in")
        if (!mIsUnRegisterSpeakerReceiver) {
            mContext.unregisterReceiver(mSpeakerReceiver)
            mIsUnRegisterSpeakerReceiver = true
        }
        SpeakerReceiver.getInstance().setToDoInSpeakerReceiver(null)
    }

    fun unRegisterAllBrowseFileReceiver() {
        try {
            mContext.unregisterReceiver(mMountEventReceiver)
            mContext.unregisterReceiver(mUserChangeReceiver)
            LocalBroadcastManager.getInstance(mContext)
                    .unregisterReceiver(mLocalConvertBroadcastReceiver)
            LocalBroadcastManager.getInstance(mContext).unregisterReceiver(mCustomLocalReceiver)
            LocalBroadcastManager.getInstance(mContext)
                    .unregisterReceiver(mLocalBrenoBroadcastReceiver)
            unRegisterSpeakerReceiver()
            LocalBroadcastManager.getInstance(mContext)
                    .unregisterReceiver(mLocalSummaryBroadcastReceiver)
            mContext.unregisterReceiver(mFilePermissionReceiver)
            DebugUtil.i(TAG, "unRegisterAllBrowseFileReceiver")
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "unRegisterAllBrowseFileReceiver:${e.message}")
        }
    }

    private fun registerSummaryReceiver() {
        val summaryFilter = IntentFilter()
        summaryFilter.addAction(ACTION_SUMMARY_STATE_CHANGED)
        LocalBroadcastManager.getInstance(mContext).registerReceiver(mLocalSummaryBroadcastReceiver, summaryFilter)
    }

    interface IReceiverOnReceive {
        fun onReceive(intent: Intent?)
    }
}