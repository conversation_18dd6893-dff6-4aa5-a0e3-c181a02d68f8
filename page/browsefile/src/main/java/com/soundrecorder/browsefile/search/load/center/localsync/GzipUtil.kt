/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/9/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load.center.localsync

import android.util.Base64
import com.soundrecorder.base.utils.DebugUtil
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream

@Suppress("TooGenericExceptionCaught")
object GzipUtil {

    private const val TAG = "ZipContentUtil"
    private const val SIZE_256 = 256

    @JvmStatic
    fun compress(content: String?): String? {
        return try {
            if (content.isNullOrEmpty()) {
                null
            } else {
                val out = ByteArrayOutputStream()
                GZIPOutputStream(out).use {
                    it.write(content.toByteArray(Charsets.UTF_8))
                }

                Base64.encodeToString(out.toByteArray(), Base64.NO_PADDING)
            }
        } catch (e: Exception) {
            DebugUtil.d(TAG, "compress error is ${e.message}")
            null
        }
    }

    @JvmStatic
    fun uncompress(content: String?): String? {
        try {
            if (!content.isNullOrEmpty()) {
                val decode = Base64.decode(content, Base64.NO_PADDING)
                val buffer = ByteArray(SIZE_256)
                ByteArrayOutputStream().use { out ->
                    val `in` = ByteArrayInputStream(decode)
                    GZIPInputStream(`in`).use { gzipStream ->
                        var n: Int
                        while (gzipStream.read(buffer).also { n = it } >= 0) {
                            out.write(buffer, 0, n)
                        }
                    }
                    return String(out.toByteArray(), Charsets.UTF_8)
                }
            }
        } catch (e: Exception) {
            DebugUtil.d(TAG, "uncompress error is ${e.message}")
        }

        return null
    }
}