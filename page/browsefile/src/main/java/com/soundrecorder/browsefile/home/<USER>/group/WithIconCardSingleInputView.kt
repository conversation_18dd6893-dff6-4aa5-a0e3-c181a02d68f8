/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description: Edit the input box for the recording group name.
 ** Version: 1.0
 ** Date : 2025/01/24
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9017070 2025/01/20      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group

import android.content.Context
import android.util.AttributeSet
import android.widget.ImageView
import android.widget.LinearLayout
import com.coui.appcompat.edittext.COUICardSingleInputView
import com.coui.component.responsiveui.unit.dp

internal class WithIconCardSingleInputView : COUICardSingleInputView {

    private var imageView: ImageView? = null

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs) {
        val headContainer = findViewById<LinearLayout>(com.support.input.R.id.header_container)
        headContainer.visibility = VISIBLE
        imageView = ImageView(context)
        imageView?.scaleType = ImageView.ScaleType.FIT_XY
        val size = context?.let { 24.dp.toPixel(it).toInt() } ?: LayoutParams.WRAP_CONTENT
        headContainer.addView(imageView, LayoutParams(size, size))
    }

    fun setImgRes(imgRes: Int) {
        imageView?.setImageResource(imgRes)
    }
}
