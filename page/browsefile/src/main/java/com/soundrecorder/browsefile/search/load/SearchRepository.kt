/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/8/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.search.load

import android.os.Handler
import android.os.Looper
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel.Companion.SUMMARY_SOURCE_INNER
import com.soundrecorder.browsefile.search.load.NoteSearchRepository.findKeywordSubstring
import com.soundrecorder.browsefile.search.load.center.CenterDbManager
import com.soundrecorder.browsefile.search.load.center.CenterDbUtils
import com.soundrecorder.browsefile.search.load.mediadb.SearchRecordManager
import com.soundrecorder.browsefile.search.utils.ForNoteUtil
import com.soundrecorder.summary.data.SummaryDataParser
import com.soundrecorder.summary.request.database.SummaryCacheDBHelper
import com.soundrecorder.summary.request.database.SummaryCacheEntity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import java.util.UUID

class SearchRepository {

    companion object {
        private const val TAG = "SearchRepository"
        const val KEY_SEARCH_WORD = "searchWord"
        const val KEY_SUPPORT_FILTER = "supportFilter"
        const val KEY_FROM_OTHER_APP = "isFromOtherApp"
        const val PAGE_START_INDEX = 1

        const val RESULT_FROM_SELF = 0
        const val RESULT_FROM_DMP = 1

        private const val TIME_OUT_TIME = 500L
        private const val TIME_OUT_MSG = 0x11
    }

    var resultSource: Int = -1
    val totalCount = MutableLiveData<Int>()
    val queryTimeOut = MutableLiveData<Boolean>(false)
    var dmpSearchTaskId: String = ""

    private var queryTimeHandler: Handler? = null

    init {
        queryTimeHandler = Handler(Looper.getMainLooper()) {
            DebugUtil.d(
                TAG, "--------it.what==TIME_OUT_MSG:${it.what == TIME_OUT_MSG}--------------"
            )
            when (it.what) {
                TIME_OUT_MSG -> {
                    queryTimeOut.postValue(true)
                }
            }
            false
        }
    }

    fun startQueryTimeOut() {
        queryTimeOut.postValue(false)
        queryTimeHandler?.let {
            it.removeMessages(TIME_OUT_MSG)
            it.sendEmptyMessageDelayed(TIME_OUT_MSG, TIME_OUT_TIME)
        }
    }

    fun release() {
        queryTimeHandler?.removeCallbacksAndMessages(null)
        queryTimeHandler = null
    }

    fun cancelQueryTimeOut() {
        //notify search finished
        queryTimeOut.postValue(false)
        queryTimeHandler?.removeMessages(TIME_OUT_MSG)
    }

    fun postTotalCount(resultWrapper: SearchResultWrapper) {
        totalCount.postValue(resultWrapper.totalCount)
    }

    /**
     * query item by paging with the keyword
     * @param pageSize the count of one page
     * @param pageNo the num of cur page in searching
     */
    suspend fun query(
        searchValues: MutableMap<String, String>?,
        pageSize: Int,
        pageNo: Int = PAGE_START_INDEX
    ): SearchResultWrapper {
        return queryInIOThread(CoroutineScope(Dispatchers.IO), searchValues, pageSize, pageNo)
    }

    private suspend fun queryInIOThread(
        coroutineScope: CoroutineScope,
        searchValues: MutableMap<String, String>?,
        pageSize: Int,
        pageNo: Int = PAGE_START_INDEX
    ): SearchResultWrapper {
        DebugUtil.e(TAG, "queryInIOThread Page No:$pageNo.")
        return coroutineScope.async {
            DebugUtil.e(TAG, "queryInIOThread enter.")
            //中子或者媒体库原始数据
            if (searchValues.isNullOrEmpty()) {
                SearchResultWrapper()
            } else {
                queryInner(searchValues, pageSize, pageNo).let { localList ->
                    // 不支持摘要，走原逻辑
                    if (!FeatureOption.supportInnerRecordSummaryFunction()) {
                        mergeMediaSearchAndSummaryBeforeOS16(localList, pageNo, searchValues)
                    } else {
                        mergeMediaSearchAndSummaryAfterOS16(localList, pageNo, searchValues)
                    }
                }
            }
        }.await()
    }

    private fun mergeMediaSearchAndSummaryBeforeOS16(
        mediaList: SearchResultWrapper,
        pageNo: Int,
        searchValues: MutableMap<String, String>
    ): SearchResultWrapper {
        if (!FeatureOption.supportRecordSummaryFunction()) {
            DebugUtil.e(TAG, "Not support old summary function return media result.")
            return mediaList
        } else if (pageNo > PAGE_START_INDEX) {
            DebugUtil.e(TAG, "Search result from inner db page no:$pageNo.")
            return mediaList
        } else {
            DebugUtil.e(TAG, "Return inner result merge with note app result.")/*
             *  1.中子返回的结果为0，将便签返回的结果直接展示在列表中
             */
            val noteSearchResult = NoteSearchRepository.query(searchValues)/*
             * 将中子的结果和便签的结果合并起来
             */
            val finalList = ForNoteUtil.mergeSummaryAndNeutronList(
                noteSearchResult.pageData, mediaList.pageData
            )
            if (mediaList.totalCount <= 0) {
                mediaList.totalCount = 0
            }
            val total = mediaList.totalCount - mediaList.pageData.size + finalList.size
            return SearchResultWrapper(
                finalList, totalCount = total, nextPageNo = mediaList.nextPageNo
            )
        }
    }

    private fun mergeMediaSearchAndSummaryAfterOS16(
        mediaList: SearchResultWrapper,
        pageNo: Int,
        searchValues: MutableMap<String, String>
    ): SearchResultWrapper {
        if (pageNo > PAGE_START_INDEX) {
            DebugUtil.e(TAG, "mergeMediaSearchAndSummaryAfterOS16 search result from media db page no:$pageNo.")
            return mediaList
        } else {
            val summarySearchResult = searchSummaryInner(searchValues[KEY_SEARCH_WORD])
            val noteSearchResult = NoteSearchRepository.query(searchValues)
            val mergedSearchResult =
                ForNoteUtil.mergeNoteAndInnerSummary(summarySearchResult, noteSearchResult.pageData)
            DebugUtil.w(TAG, "mergeMediaSearchAndSummaryAfterOS16 keyword:${searchValues[KEY_SEARCH_WORD]}")
            DebugUtil.w(TAG, "mergeMediaSearchAndSummaryAfterOS16 inner db summary item count:${summarySearchResult.size}")
            DebugUtil.w(TAG, "mergeMediaSearchAndSummaryAfterOS16 note db summary item count:${noteSearchResult.pageData.size}")
            DebugUtil.w(TAG, "mergeMediaSearchAndSummaryAfterOS16 media db summary item count:${mediaList.pageData.size}")
            DebugUtil.w(TAG, "mergeMediaSearchAndSummaryAfterOS16 merged summary item count:${mergedSearchResult.size}")
            val finalList = ForNoteUtil.mergeSummaryAndNeutronList(
                mergedSearchResult, mediaList.pageData
            )
            DebugUtil.w(TAG, "mergeMediaSearchAndSummaryAfterOS16 final summary item count:${finalList.size}")
            if (mediaList.totalCount <= 0) {
                mediaList.totalCount = 0
            }
            val total = mediaList.totalCount - mediaList.pageData.size + finalList.size
            return SearchResultWrapper(
                finalList, totalCount = total, nextPageNo = mediaList.nextPageNo
            )
        }
    }

    fun searchSummaryInner(keyWord: String?): MutableList<ItemSearchViewModel> {
        DebugUtil.e(TAG, "searchSummaryInner keyword:$keyWord")
        if (keyWord.isNullOrEmpty()) return mutableListOf()
        val summaryEntityList = SummaryCacheDBHelper.querySummaryListByKeyword(keyWord)
        DebugUtil.e(TAG, "Summary Cache DB Item:${summaryEntityList.size}")
        if (summaryEntityList.isEmpty()) return mutableListOf()
        val summarySearchItemList = mutableListOf<ItemSearchViewModel>()
        for (summaryEntity in summaryEntityList) {
            val noteDataNode = summaryEntity.convertToItemBrowseRecordViewModel(keyWord)
            summarySearchItemList.add(noteDataNode)
        }
        return summarySearchItemList
    }

    /**
     * the real search logic
     */
    private fun queryInner(
        searchValues: MutableMap<String, String>,
        pageSize: Int,
        pageNo: Int = PAGE_START_INDEX
    ): SearchResultWrapper {
        return if (CenterDbUtils.isCenterSearchUsable()) {
            queryInnerByCenter(searchValues, pageSize, pageNo)
        } else {
            queryInnerByRecord(searchValues, pageSize, pageNo)
        }
    }

    /**
     * search items in record meida db
     */
    private fun queryInnerByRecord(
        searchValues: MutableMap<String, String>,
        pageSize: Int,
        pageNo: Int = PAGE_START_INDEX
    ): SearchResultWrapper {
        return if (pageNo <= PAGE_START_INDEX) {
            SearchRecordManager.startQueryWithSearch(searchValues).also {
                resultSource = RESULT_FROM_SELF
            }
        } else {
            DebugUtil.d(TAG, "cannot search with paging from record db")
            throw IllegalArgumentException(
                BaseApplication.getAppContext()
                    .getString(com.soundrecorder.common.R.string.search_result_load_error)
            )
        }
    }

    /**
     * search items in center search app by contentProvider
     */
    private fun queryInnerByCenter(
        searchValues: MutableMap<String, String>,
        pageSize: Int,
        pageNo: Int = PAGE_START_INDEX
    ): SearchResultWrapper {
        DebugUtil.d(TAG, "queryInnerByCenter searchValue is $searchValues, pageNo is $pageNo")
        return try {
            if (pageNo == PAGE_START_INDEX) {
                // 搜索词变化，taskId变化，是针对某个关键词搜索的唯一标识
                dmpSearchTaskId = UUID.randomUUID().toString()
            }
            CenterDbManager.doSearch(searchValues, pageNo, pageSize, dmpSearchTaskId).also {
                resultSource = RESULT_FROM_DMP
            }
        } catch (e: Exception) {
            DebugUtil.d(TAG, "queryInnerByCenter, there is an exception")
            //center app has occured an exception, just search in record media db
            if (pageNo == PAGE_START_INDEX) {
                queryInnerByRecord(searchValues, pageSize, pageNo)
            } else {
                throw IllegalArgumentException(
                    BaseApplication.getAppContext()
                        .getString(com.soundrecorder.common.R.string.search_result_load_error),
                )
            }
        }
    }
}

/**
 * convertToItemBrowseRecordViewModel
 * 扩展函数,将内部便签数据库查询结果转化为搜索结果
 *
 * @param keyWord 关键词
 * @return ItemSearchViewModel 返回的Item
 */
fun SummaryCacheEntity.convertToItemBrowseRecordViewModel(keyWord: String): ItemSearchViewModel {
    val itemSearchViewModel = ItemSearchViewModel()
    itemSearchViewModel.searchValue = keyWord
    itemSearchViewModel.summarySource = SUMMARY_SOURCE_INNER
    itemSearchViewModel.noteId = this.id.toString()
    itemSearchViewModel.recordUUID = this.timeStamp.toString()
    itemSearchViewModel.mediaId = this.mediaId
    itemSearchViewModel.isMatchSummary = true
    itemSearchViewModel.summaryText = this.summaryContent?.let {
        SummaryDataParser.parseSummaryContentText(it)
    }

    if (itemSearchViewModel.summaryText.isNullOrEmpty()) {
        return itemSearchViewModel
    }

    val keyWordIndexList = itemSearchViewModel.summaryText?.let { summaryText ->
        findKeywordSubstring(summaryText, keyWord)
    } as Pair

    if (keyWordIndexList.first.isNotEmpty() && keyWordIndexList.second.isNotEmpty()) {
        itemSearchViewModel.summaryText = keyWordIndexList.first
        keyWordIndexList.second.forEach { startIndex ->
            itemSearchViewModel.summaryColorIndex.add(startIndex)
            itemSearchViewModel.summaryColorIndex.add(startIndex + keyWord.length)
        }
    }
    DebugUtil.e("SummaryCacheEntity", "${itemSearchViewModel.summaryText}")
    return itemSearchViewModel
}

