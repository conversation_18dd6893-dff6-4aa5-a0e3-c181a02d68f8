package com.soundrecorder.browsefile.search.load.center

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.db.CursorHelper
import com.soundrecorder.common.utils.EnableAppUtil
import com.soundrecorder.common.utils.RecordModeUtil.cleanRelativePath

object CenterDbUtils {

    private const val DMP_SYNCED_MEDIA_INFO = "dmp_synced_media_info"

    /*center dmp isEnable ,the value cant be change, which be agreed by both*/
    const val DMP_ENABLE_STATUS_AVAILABLE = "available"
    const val DMP_ENABLE_STATUS_FATAL = "fatal"

    private var dmpEnableStatus: String? = DMP_ENABLE_STATUS_AVAILABLE
    private var recorderBaseDir: String? = null
    // 中子在V1.0.5版本上增加了转文本内容精确匹配排序规则，未更改策略版本，导致不兼容老版本中子，此处增加临时变量判断下
    // -1:为默认值，未获取判断 1：V1.0.5版本及以上  0：V1.0.5以下
    private var mTempSupportDoubleCharStrategy: Int = -1


    @JvmStatic
    fun saveDmpEnableStatus(status: String?) {
        dmpEnableStatus = status
    }

    @JvmStatic
    fun getDmpEnableStatus(): Boolean {
        return DMP_ENABLE_STATUS_AVAILABLE == dmpEnableStatus
    }

    /**
     * 外销无中子搜索
     * os12.1.1超级录音需求开关
     * 已安装中子，中子内部是否支持录音or内部出错，可拦截一部分手机上有中子但是未放开录音搜索的情况
     */
    @JvmStatic
    fun isCenterSearchUsableByFeature(): Boolean = !FeatureOption.OPLUS_VERSION_EXP && checkRecorderAppEnable()
            && getDmpEnableStatus()

    /**
     * check whether center search app is usable
     * 1、not the export version
     * 2、has installed the the app and the version is above 1.0.2
     * 3、center search app is usable
     * 4、center app is not disabled
     * 5、recorder app support center search - local-switch:part-version not support
     */
    @JvmStatic
    fun isCenterSearchUsable(): Boolean {
        return isCenterSearchUsableByFeature()
                && checkCenterAppEnable()
    }

    @JvmStatic
    fun isSupportDoubleCharStrategy(): Boolean {
        if (mTempSupportDoubleCharStrategy == -1) {
            mTempSupportDoubleCharStrategy =
                if (isVersionNameAbove("1.0.5", CenterDbConstant.CENTER_DMP_PKG_NAME)) 1 else 0
        }

        return mTempSupportDoubleCharStrategy == 1
    }

    @JvmStatic
    fun isVersionNameAbove(version: String, packageName: String): Boolean {
        val versionName = AppUtil.getAppVersion(packageName)
        return if (versionName.isNullOrEmpty()) {
            false
        } else {
            versionName >= version
        }
    }


    /**
     * local-switch:part-version not support
     */
    @JvmStatic
   private fun checkRecorderAppEnable(): Boolean {
        return OS12FeatureUtil.isSuperSoundRecorderEpicEffective()
    }

    @JvmStatic
    private fun checkCenterAppEnable(): Boolean {
        return EnableAppUtil.isAppInstallEnabled(
            BaseApplication.getAppContext(),
            CenterDbConstant.CENTER_DMP_PKG_NAME
        ) == EnableAppUtil.APP_IS_ENABLE
    }

    @JvmStatic
    fun saveSyncedMediaInfo(mediaInfo: String?) {
        StorageManager.setStringPref(
            BaseApplication.getAppContext(),
            DMP_SYNCED_MEDIA_INFO,
            mediaInfo
        )
    }

    @JvmStatic
    open fun getSyncedMediaInfo(): String? {
        return StorageManager.getStringPref(
            BaseApplication.getAppContext(),
            DMP_SYNCED_MEDIA_INFO,
            null
        )
    }

    @JvmStatic
    fun calBucketByRelativePath(relativePath: String): String? {
        return when (relativePath.cleanRelativePath()) {
            RecordModeConstant.RELATIVE_PATH_STANDARD, RecordModeConstant.RELATIVE_PATH_BASE -> RecordModeConstant.BUCKET_VALUE_STANDARD.toString()
            RecordModeConstant.RELATIVE_PATH_MEETING -> RecordModeConstant.BUCKET_VALUE_CONFERENCE.toString()
            RecordModeConstant.RELATIVE_PATH_INTERVIEW -> RecordModeConstant.BUCKET_VALUE_INTERVIEW.toString()
            RecordModeConstant.RELATIVE_PATH_CALL -> RecordModeConstant.BUCKET_VALUE_CALL.toString()
            else -> null
        }
    }

    /**
     * @param data /storage/0/Music/Recordings/Standard Recordings/1.mp3
     * @return Music/Recordings/Standard Recordings
     */
    @JvmStatic
    fun getRelativePathFromData(data: String?): String {
        if (data.isNullOrBlank()) {
            return ""
        }
        if (recorderBaseDir.isNullOrBlank()) {
            recorderBaseDir = BaseUtil.getPhoneStorageDir(BaseApplication.getAppContext())
            if (recorderBaseDir == null) {
                recorderBaseDir = CursorHelper.DEFAULT_DIR
            }
        }
        var newValue = data.replace(recorderBaseDir!!, "")
        if (newValue.startsWith("/")) {
            newValue = newValue.replaceFirst("/", "")
        }
        val endIndex = newValue.lastIndexOf("/")

        return if (endIndex != -1) newValue.substring(0, endIndex) else data
    }
}