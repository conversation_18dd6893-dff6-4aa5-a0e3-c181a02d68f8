package com.soundrecorder.browsefile.search

import android.view.View
import com.coui.appcompat.searchview.COUISearchBar
import com.coui.appcompat.toolbar.COUIToolbar
import com.google.android.material.appbar.AppBarLayout

class SearchCacheHolder {
    var mSearchAnimView: COUISearchBar? = null
    var toolbar: COUIToolbar? = null
    var appBarLayout: AppBarLayout? = null
    var searchBox: View? = null
    var subTitleView: View? = null
    var mainTitleLayout: View? = null
    var subTitleLayout: View? = null
    var rotateView: View? = null
    var browseFileList: View? = null
    var subTitleViewMaxHeight = 0
}