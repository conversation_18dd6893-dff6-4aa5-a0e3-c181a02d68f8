/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : ItemBrowseView.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.home.item

import android.animation.ArgbEvaluator
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import android.view.accessibility.AccessibilityNodeInfo
import android.widget.Button
import android.widget.TextView
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.databinding.ViewDataBinding
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.base.view.IRecyclerAdapterData
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.databinding.ItemPlayAreaBinding
import com.soundrecorder.browsefile.databinding.ItemRecordInfoBinding
import com.soundrecorder.browsefile.databinding.ItemSummaryAndPlayBinding
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel.Companion.PLAYER_STATE_AUTO_MARK
import com.soundrecorder.browsefile.home.load.LoadAmplitudeHelper
import com.soundrecorder.browsefile.home.view.ItemBrowsePlayInfoLayout
import com.soundrecorder.browsefile.search.item.SearchAdapter
import com.soundrecorder.browsefile.view.AmplitudeSeekBar
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.RecorderUserActionKt
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.player.status.PlayStatus
import java.util.concurrent.ConcurrentHashMap

abstract class AbsItemBrowseViewHolder<V : ViewDataBinding, D : ItemBrowseRecordViewModel>(
    viewBinding: V,
    val browseViewListener: IBrowseViewHolderListener?,
    val isGroupingByContact: Boolean
) : AbsItemViewHolder<V, D>(viewBinding) {

    protected var taskId: Int = 0
    protected var mIsRtl = false

    protected var mEditStartX = -120
    protected var mEditEndX = 100

    var adapterData: IRecyclerAdapterData? = null

    protected lateinit var mItemRecordInfo: ItemRecordInfoBinding
    protected lateinit var mItemPlayArea: ItemPlayAreaBinding
    protected lateinit var itemSummaryAndPlay: ItemSummaryAndPlayBinding
    protected lateinit var mItemPlayInfo: ItemBrowsePlayInfoLayout
    protected var mCardView: ViewGroup? = null

    protected open var logTag: String = "ItemBrowseView"

    companion object {
        const val NUM_20 = 20
        const val ANIM_TRANSLATION_X: Int = 18
        const val ANIM_DURATION_180 = 180L
        const val ANIM_DURATION_367 = 367L
        const val ANIM_DURATION_START_DELAY = 67L
    }

    private val playViewRunnable = {
        updatePlayButton(mItemViewModel.mPlayState)
        updateSeekBarArea(true)
    }

    private val mPlayStatus by lazy {
        Observer<Int> {
            val playerState = getPlayerState(it)
            if (mItemViewModel.mPlayState == playerState) {
                return@Observer
            }
            val originalModelState = mItemViewModel.mPlayState
            mItemViewModel.mPlayState = playerState
            if (!mItemPlayInfo.isAnimating() || (playerState == PlayStatus.PLAYER_STATE_PLAYING)
                || originalModelState == PlayStatus.PLAYER_STATE_PLAYING) {
                /*post 避免播放A过程中，快速点击B-A，A在退出动画(执行到0.5)执行未结束接着当前位置展开(0.5到1)了*/
                itemView.post(playViewRunnable)
            }
        }
    }
    private val mPlayProgressDuration by lazy {
        Observer { playProgress: Long? ->
            //halton状态下已经开始动画折叠itemPlayInfoLayout了，此处为规避AB来回切，reset后设置进度为0时进度条闪到0的问题
            browseViewListener?.let {
                if (getPlayerState() != PlayStatus.PLAYER_STATE_HALTON) {
                    val realProgress = getPlayerProgress(playProgress)
                    if ((mItemViewModel.mPlayProgress != realProgress) && (mItemPlayInfo.updatePlayProgress(
                            realProgress
                        ))
                    ) {
                        mItemViewModel.mPlayProgress = realProgress
                    }
                }
            }
        }
    }
    private val mWindowTypeObserver by lazy {
        Observer { windowType: WindowType ->
            when (windowType) {
                WindowType.SMALL -> {
                    mItemPlayArea.playButtonArea.isInvisible = false
                    mItemPlayArea.playButton.isVisible = true
                }

                WindowType.MIDDLE,
                WindowType.LARGE -> {
                    mItemPlayArea.playButtonArea.isInvisible = true
                    mItemPlayArea.playButton.isVisible = false
                    mItemPlayInfo.setShrinkState()
                }
            }
            updateItemSelectBg(windowType = windowType)
        }
    }
    private val mPlayModelObserver by lazy {
        Observer { playModel: StartPlayModel? ->
            updateItemSelectBg(playModel = playModel)
        }
    }

    private val mLiveAmpStatus by lazy {
        Observer<ConcurrentHashMap<Long, LoadAmpStatus>> {
            updateAmpStatus(it)
        }
    }

    private fun updateAmpStatus(map: ConcurrentHashMap<Long, LoadAmpStatus>) {
        if (map.isNullOrEmpty()) {
            return
        }
        val loadAmpStatus = map.get(mItemViewModel.mediaId)
        if (loadAmpStatus != null) {
            DebugUtil.d(logTag, "updateAmpStatus, ampList:${loadAmpStatus.ampList}")
            mItemPlayInfo.cancelAmpLoadingAnimation()
            if (!loadAmpStatus.ampList.isNullOrEmpty()) {
                loadAmplitudeList(loadAmpStatus.ampList, true)
            }
        }
    }

    init {
        mIsRtl = (context.resources.configuration.layoutDirection == View.LAYOUT_DIRECTION_RTL)
        mEditEndX = context.resources.getDimensionPixelOffset(R.dimen.mark_animal_offset)
        mEditStartX = (mEditEndX + NUM_20) * -1 //checkbox
    }

    protected open fun initBasicView() {
        initPlayButton()
    }

    override fun onRootViewClick(v: View) {
        DebugUtil.i(logTag, "onRootViewClick")
        if (checkAudioInvalid()) {
            DebugUtil.i(logTag, "onClickPlay  file is not exit")
            return
        }
        browseViewListener?.onClickItem(mItemViewModel)
    }

    override fun onRootViewLongClick(v: View): Boolean {
        DebugUtil.i(logTag, "onRootViewLongClick")
        return false
    }

    override fun onViewRecycled() {
        super.onViewRecycled()
        DebugUtil.d(logTag, "onViewRecycled itemName is ${mItemViewModel.displayName}")
        browseViewListener?.getPlayerController()?.mediaPlayerManager?.let {
            it.playerState.removeObserver(mPlayStatus)
            it.currentTimeMillis.removeObserver(mPlayProgressDuration)
        }
        browseViewListener?.getWindowType()?.removeObserver(mWindowTypeObserver)
        browseViewListener?.getPlayLiveData()?.removeObserver(mPlayModelObserver)
        mItemPlayInfo.cancelAnimation()
        itemView.removeCallbacks(playViewRunnable)
        ItemBrowseRecordViewModel.liveAmpStatus[taskId]?.removeObserver(mLiveAmpStatus)
    }

    override fun observeData(owner: LifecycleOwner) {
        browseViewListener?.getPlayerController()?.mediaPlayerManager?.let {
            it.playerState.observe(owner, mPlayStatus)
            it.currentTimeMillis.observe(owner, mPlayProgressDuration)
        }
        browseViewListener?.getWindowType()?.observe(owner, mWindowTypeObserver)
        browseViewListener?.getPlayLiveData()?.observe(owner, mPlayModelObserver)
        ItemBrowseRecordViewModel.liveAmpStatus[taskId]?.observe(owner, mLiveAmpStatus)
    }

    override fun bindView(isLastPosition: Boolean?) {
        updateItemInfo()
        updatePlayStatus()
        initPlayInfo()
    }

    protected abstract fun updateItemInfo()

    private fun initPlayButton() {
        mItemPlayArea.playButton.setOnClickListener {
            if (ClickUtils.isQuickClick()) {
                DebugUtil.i(logTag, "onClickPlay isQuickClick")
                return@setOnClickListener
            }
            if (checkAudioInvalid()) {
                DebugUtil.i(logTag, "onClickPlay  file is not exit")
                return@setOnClickListener
            }
            if (mItemPlayInfo.isEmptyAmps()) {
                loadAmplitudeList(mItemViewModel.ampList ?: LoadAmplitudeHelper.getAmpList(mItemViewModel))
            }
            if (mItemViewModel.ampList.isNullOrEmpty() || mItemPlayInfo.isEmptyAmps()) {
                DebugUtil.i(logTag, "onClickPlay need load amplitudes")
                updateSeekBarArea(true, true)
                mItemPlayInfo.startAmpLoadingAnimation()
                browseViewListener?.onLoadAmplitudes(it, mItemViewModel)
                return@setOnClickListener
            }
            startPlayButtonClick()
        }
        mItemPlayArea.playButton.accessibilityDelegate = object : View.AccessibilityDelegate() {
            override fun onInitializeAccessibilityNodeInfo(
                host: View,
                info: AccessibilityNodeInfo
            ) {
                super.onInitializeAccessibilityNodeInfo(host, info)
                info.contentDescription = playButtonContentDescriptionText()
                info.className = Button::class.java.name
            }
        }
    }

    private fun startPlayButtonClick() {
        if (mItemViewModel.isRecycle && !PermissionUtils.hasAllFilePermission()) {
            //点击播放按钮， 发送广播通知Activity页面弹出授权弹框
            PermissionUtils.sendFilePermissionBroadcast(context)
            DebugUtil.d(logTag, "initPlayButton sendFilePermissionBroadcast")
            return
        }
        if (adapterData is SearchAdapter) {
            BuryingPoint.addSearch(RecorderUserAction.VALUE_SEARCH_PLAY_BROWSEFILE)
            /*快捷播放按钮 次数 累加*/
            RecorderUserActionKt.sPlayCount++
        }
        browseViewListener?.getPlayerController()?.let {
            it.setPlayRecordItem(mItemViewModel)
            if ((mItemViewModel.mPlayState == PlayStatus.PLAYER_STATE_INIT) || (mItemViewModel.mPlayState == PlayStatus.PLAYER_STATE_HALTON)) {
                // 增加埋点，每次真正点击展开播放才计算一次(第一次播放是init，后面播放后是halton)
                BuryingPoint.addPlayFromAndDuration(
                    RecorderUserAction.VALUE_PLAY_FROM_BROWSEFILE,
                    mItemViewModel.recordType().toString(),
                    mItemViewModel.mDuration
                )
            }
            val ampMap = ItemBrowseRecordViewModel.liveAmpStatus[taskId]?.value
            if (!ampMap.isNullOrEmpty() && ampMap.containsKey(mItemViewModel.mediaId)) {
                ampMap.remove(mItemViewModel.mediaId)
                DebugUtil.d(logTag, "startPlayButtonClick remove ampStatus")
                ItemBrowseRecordViewModel.liveAmpStatus[taskId]?.postValueSafe(ampMap)
            }
            it.playBtnClick()
        }
    }

    /**
     * 校验该item对应音频文件是否无效(不存在)
     */
    protected open fun checkAudioInvalid(): Boolean = !mItemViewModel.fileIsExists()

    private fun playButtonContentDescriptionText(): String {
        val context = BaseApplication.getAppContext()
        val resId = when (getPlayerState()) {
            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE -> com.soundrecorder.common.R.string.talkback_play

            PlayStatus.PLAYER_STATE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> com.soundrecorder.common.R.string.talkback_pause

            else -> com.soundrecorder.common.R.string.talkback_play
        }
        return context.getString(resId)
    }

    private fun initPlayInfo() {
        mItemPlayInfo.duration = mItemViewModel.mDuration
        mItemPlayInfo.pointMoveListener = object : AmplitudeSeekBar.PointMoveListener {
            /**
             * 进度红线ACTION_DOWN
             */
            override fun onTouchDownMiddleBar() {
                if (isCurrentItemFastPlay()) {
                    browseViewListener?.getPlayerController()?.mediaPlayerManager?.let {
                        it.onStartTouchSeekBar()
                    }
                }
            }

            /**
             * 拖动进度红线
             */
            override fun onMoveOnMiddleBar(time: Long) {
                mItemPlayInfo.updatePlayProgressText(time)
                browseViewListener?.getPlayerController()?.let {
                    if (isCurrentItemFastPlay()) {
                        it.onStopSeekBar(time)
                    }
                }
            }

            /**
             * 进度红线ACTION_UP
             */
            override fun onTouchUpMiddleBar(time: Long) {
                if (isCurrentItemFastPlay()) {
                    mItemPlayInfo.updatePlayProgressText(time)
                    browseViewListener?.getPlayerController()?.mediaPlayerManager?.let {
                        it.onStopTouchSeekBar()
                    }
                }
            }
        }
        val curTime = getPlayerProgress(browseViewListener?.getPlayerController()?.mediaPlayerManager?.getCurrentPlayerTime())
        mItemPlayInfo.updatePlayProgressText(curTime ?: 0)
        mItemPlayInfo.setSelectTime(curTime)
        loadAmplitudeList(mItemViewModel.ampList)
        updateSeekBarArea()
    }

    fun loadAmplitudeList(ampList: MutableList<Int>?, startPlayingNow: Boolean = false) {
        if (!ampList.isNullOrEmpty()) {
            DebugUtil.d(logTag, "loadAmplitudeList, ampList:${ampList.size}")
            mItemViewModel.ampList = ampList.toMutableList()
            mItemPlayInfo.setAmplitudes(ampList)
            if (startPlayingNow) {
                startPlayButtonClick()
            }
        }
    }

    /**
     * 更新进度条区域的显示和隐藏
     * @param needAnim 是否需要动画
     */
    private fun updateSeekBarArea(needAnim: Boolean = false, preVisible: Boolean = false) {
        DebugUtil.i(
            logTag,
            "updateSeekBarArea needAnim=$needAnim, visible=${mItemPlayInfo.isVisible}" +
                    ", isCurrentPlay = ${browseViewListener?.isCurrentPlay(mItemViewModel)}" +
                    ", height = ${mItemPlayInfo.layoutParams.height}" +
                    ", displayName=${mItemViewModel.displayName}"
        )
        val needVisible = if (preVisible) {
            !mItemPlayInfo.isVisible
        } else {
            val playStatus = getPlayerState()
            mItemViewModel.isNeedShowSeekBarArea(playStatus)
        }
        if (needVisible) {
            //需要显示seekBar时，只有需要动画并且之前没显示才执行动画,否则直接执行结果
            if (needAnim && mItemPlayInfo.isNotCompleteVisible()) {
                mItemPlayInfo.startOrContinueEnterAnim(
                    arrayOf(getTitleColorEnterAnimator()))
                onSeekBarAnimChanged(true)
            } else {
                setPlayInfoStateImmediately(true)
            }
        } else {
            //需要隐藏seekBar时，只有需要动画并且之前显示才执行动画,否则直接执行结果
            if (needAnim && mItemPlayInfo.isVisible) {
                mItemPlayInfo.startOrContinueExitAnim(
                    arrayOf(getTitleColorExitAnimator())
                )
                onSeekBarAnimChanged(false)
            } else {
                setPlayInfoStateImmediately(false)
            }
        }
    }

    protected open fun onSeekBarAnimChanged(inAnim: Boolean): Unit = Unit

    private fun getTitleColorEnterAnimator(): ValueAnimator {
        val colorFocus = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary, 0)
        return ObjectAnimator.ofInt(
            mItemRecordInfo.recordTitle,
            "textColor",
            mItemRecordInfo.recordTitle.currentTextColor,
            colorFocus
        ).apply {
            duration = 367
            interpolator = PathInterpolatorHelper.couiEaseInterpolator
            setEvaluator(ArgbEvaluator())
        }
    }

    private fun getTitleColorExitAnimator(): ValueAnimator {
        val colorIdle = context.getColor(R.color.item_record_title_color)
        return ObjectAnimator.ofInt(
            mItemRecordInfo.recordTitle,
            "textColor",
            mItemRecordInfo.recordTitle.currentTextColor,
            colorIdle
        ).apply {
            duration = 333
            interpolator = PathInterpolatorHelper.couiEaseInterpolator
            setEvaluator(ArgbEvaluator())
        }
    }

    private fun setPlayInfoStateImmediately(expand: Boolean) {
        DebugUtil.i(
            logTag,
            "setPlayInfoStateImmediately expand=$expand" +
                    ", height = ${mItemPlayInfo.layoutParams.height}" +
                    ",  playStatus=${getPlayerState()}" +
                    ", displayName=${mItemViewModel.displayName}"
        )
        if (expand) {
            mItemPlayInfo.cancelAnimation()
            mItemPlayInfo.setExpandState()
        } else {
            mItemPlayInfo.cancelAnimation()
            mItemPlayInfo.setShrinkState()
        }
        updatePlayingTitleColor(getPlayerState())
    }

    /**
     * 设置item的背景
     * 中大屏：若选中某音频详情，该item背景高亮
     * 小屏、中大屏未选中音频，该item为默认背景
     */
    private fun updateItemSelectBg(
        windowType: WindowType? = browseViewListener?.getWindowType()?.value,
        playModel: StartPlayModel? = browseViewListener?.getPlayLiveData()?.value
    ) {
        val isSelected = ((mItemViewModel.mediaId == playModel?.mediaId)
                && (windowType != WindowType.SMALL))
        if (mCardView?.isSelected != isSelected) {
            mCardView?.isSelected = isSelected
        }
    }

    private fun updatePlayStatus() {
        updatePlayButton(getPlayerState())
    }

    private fun updatePlayButton(status: Int?) {
        val playResId = when (status) {
            PlayStatus.PLAYER_STATE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> R.drawable.ic_list_pause

            else -> R.drawable.ic_list_play
        }
        mItemPlayArea.playButton.setImageResource(playResId)
    }

    private fun updatePlayingTitleColor(status: Int?) {
        val titleColor = when (status) {
            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
            PLAYER_STATE_AUTO_MARK -> {
                COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary, 0)
            }

            else -> {
                context.getColor(R.color.item_record_title_color)
            }
        }
        mItemRecordInfo.recordTitle.setTextColor(titleColor)
    }

    fun setTextToMaxH3(text: TextView?, sizeInDpPx: Float) {
        val fontScale =
            context.resources.configuration.fontScale.coerceAtMost(COUIChangeTextUtil.H3)
        val textSize = sizeInDpPx * fontScale
        text?.setTextSize(
            TypedValue.COMPLEX_UNIT_PX,
            textSize
        )
    }

    fun getPlayerState(currentState: Int? = null): Int = if (isCurrentItemFastPlay()) {
        currentState ?: browseViewListener?.getPlayerController()?.getPlayerState()
        ?: PlayStatus.PLAYER_STATE_INIT
    } else {
        mItemViewModel.mPlayState ?: PlayStatus.PLAYER_STATE_INIT
    }

    fun getPlayerProgress(playProgress: Long?): Long? = if (isCurrentItemFastPlay()) {
        playProgress
    } else {
        0
    }

    fun isCurrentItemFastPlay(): Boolean = browseViewListener?.isCurrentPlay(mItemViewModel) == true
}

interface IBrowseViewHolderListener {
    /**
     * 获取快捷播放器
     */
    fun getPlayerController(): FastPlayHelper? = null

    /**
     * 列表点击事件
     * @param data 被点击项数据
     */
    fun onClickItem(data: ItemBrowseRecordViewModel) {}

    fun onLongClickItem(view: View, data: ItemBrowseRecordViewModel) {
        // default empty impl
    }

    /**
     * 获取当前项是否为当前快捷播放项
     * @return true：是 false：否
     */
    fun isCurrentPlay(data: ItemBrowseRecordViewModel): Boolean = false

    /**
     * 获取窗口屏幕类型
     */
    fun getWindowType(): MutableLiveData<WindowType>

    /**
     * 获取播放详情加载项
     */
    fun getPlayLiveData(): MutableLiveData<StartPlayModel?>

    /**
     * 是否执行动画展开新增音频快捷播放进度条
     */
    fun canShowAddAnimator(data: ItemBrowseRecordViewModel): Boolean = true

    /**
     * 摘要图标点击事件
     * @param data 被点击项数据
     */
    fun onClickSummaryIcon(data: ItemBrowseRecordViewModel) {}

    fun onClickSummaryItem(data: ItemBrowseRecordViewModel) {}

    fun onClickConvertItem(data: ItemBrowseRecordViewModel) {}

    fun showSummaryTip(anchorView: View?) {}

    fun onCallGroupMoreClick(view: View, data: ItemBrowseRecordViewModel) {}

    fun onGroupRecordDeleteSuccess(delay: Long, needRefresh: Boolean) {}

    fun onLoadAmplitudes(view: View, data: ItemBrowseRecordViewModel) {}
}