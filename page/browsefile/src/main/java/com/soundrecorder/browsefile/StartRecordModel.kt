package com.soundrecorder.browsefile

import android.content.Intent
import android.provider.MediaStore
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction

class StartRecordModel {
    companion object {
        private const val TAG = "StartRecordModel"
    }
    var isFromGesture: Boolean = false
    var isFromCall: Boolean = false
    var isNeedResult: Boolean = false
    var limitSize: Long = 0
    var limitDuration: Int = 0
    var noEnterAnimation: Boolean = false

    @Suppress("TooGenericExceptionCaught")
    fun setStartRecordModel(intent: Intent) {
        val incomingAction: String? = intent.action
        if (Intent.ACTION_GET_CONTENT == incomingAction ||
                Intent.ACTION_PICK == incomingAction ||
                MediaStore.Audio.Media.RECORD_SOUND_ACTION == incomingAction) {
            isNeedResult = true
            BuryingPoint.addRecordStartType(RecorderUserAction.VALUE_START_RECORD_FROM_MESSAGE)
        } else {
            isNeedResult = false
            BuryingPoint.addRecordStartType(RecorderUserAction.VALUE_START_RECORD_FROM_LAUNCHER)
        }

        try {
            limitSize = intent.getLongExtra("MAX_SIZE", 0)
            limitDuration = intent.getIntExtra(MediaStore.Audio.Media.DURATION, 0)
            isFromGesture = intent.getBooleanExtra("fromGesture", false)
        } catch (e: Exception) {
            DebugUtil.e(TAG, "Max_size long get error", e)
        }
        DebugUtil.log(
            TAG, "onCreate, the mSize is $limitSize, "
                + ", this incomingAction is $incomingAction ,mDurationLimitTime = $limitDuration")
    }
}