/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - GroupDraggableViewHolder.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2025/01/20
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9017070 2025/01/20      1.0     create file
 ****************************************************************/
package com.soundrecorder.browsefile.home.view.group.util

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.util.TypedValue
import android.view.MotionEvent
import android.view.View
import androidx.core.view.animation.PathInterpolatorCompat
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.hapticfeedback.COUIHapticFeedbackConstants
import com.soundrecorder.browsefile.R

abstract class GroupDraggableViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {

    companion object {
        private const val MAX_ELEVATION = 16f
        private const val SELECT_ANIMATOR_DURATION = 300L
        private const val DROP_ANIMATOR_DURATION = 400L
        private const val ELEVATION_ANIMATION_CONTROL_X = 0.4f
        private const val ELEVATION_ANIMATION_CONTROL_X_2 = 0.2f
        private const val ANIMATION_CONTROL_X_1 = 0.15f
        private const val ANIMATION_CONTROL_Y_2 = 1f
    }

    private var mSelectedAnimator: AnimatorSet? = null
    private var mDropAnimator: AnimatorSet? = null
    private val mScale: Float

    init {
        val outValue = TypedValue()
        itemView.resources.getValue(R.dimen.my_view_item_scale, outValue, true)
        mScale = outValue.float
        initAnimators()
    }

    private fun initAnimators() {
        initSelectAnimator()
        initDropAnimator()
    }

    private fun initSelectAnimator() {
        val elevationAnimation = ObjectAnimator.ofFloat(
            itemView,
            "elevation",
            0f,
            MAX_ELEVATION
        ).apply {
            duration = SELECT_ANIMATOR_DURATION
            interpolator = PathInterpolatorCompat.create(
                ELEVATION_ANIMATION_CONTROL_X,
                0f,
                ELEVATION_ANIMATION_CONTROL_X_2,
                ANIMATION_CONTROL_Y_2
            )
        }

        val scaleXAnimator = ObjectAnimator.ofFloat(
            itemView,
            "scaleX",
            1f,
            mScale
        ).apply {
            duration = SELECT_ANIMATOR_DURATION
            interpolator =
                PathInterpolatorCompat.create(ANIMATION_CONTROL_X_1, 0f, 0f, ANIMATION_CONTROL_Y_2)
        }

        val scaleYAnimator = ObjectAnimator.ofFloat(
            itemView,
            "scaleY",
            1f,
            mScale
        ).apply {
            duration = SELECT_ANIMATOR_DURATION
            interpolator =
                PathInterpolatorCompat.create(ANIMATION_CONTROL_X_1, 0f, 0f, ANIMATION_CONTROL_Y_2)
        }

        mSelectedAnimator = AnimatorSet()
        mSelectedAnimator?.play(scaleXAnimator)?.with(scaleYAnimator)?.with(elevationAnimation)
    }

    private fun initDropAnimator() {
        val scaleXAnimator =
            ObjectAnimator.ofFloat(itemView, "scaleX", mScale, ANIMATION_CONTROL_Y_2)
        val scaleYAnimator =
            ObjectAnimator.ofFloat(itemView, "scaleY", mScale, ANIMATION_CONTROL_Y_2)
        val elevationAnimation = ObjectAnimator.ofFloat(itemView, "elevation", MAX_ELEVATION, 0f)
        mDropAnimator = AnimatorSet().apply {
            duration = DROP_ANIMATOR_DURATION
            interpolator =
                PathInterpolatorCompat.create(ANIMATION_CONTROL_X_1, 0f, 0f, ANIMATION_CONTROL_Y_2)
            play(scaleXAnimator)?.with(scaleYAnimator)?.with(elevationAnimation)
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    itemView.performHapticFeedback(
                        COUIHapticFeedbackConstants.GRANULAR_SHORT_VIBRATE,
                        0
                    )
                }
            })
        }
    }

    open fun onItemSelected() {
        if (mDropAnimator?.isRunning == true) {
            mDropAnimator?.end()
        }
        mSelectedAnimator?.start()
    }

    open fun onItemDrop() {
        if (mSelectedAnimator?.isRunning == true) {
            mSelectedAnimator?.end()
        }
        mDropAnimator?.start()
    }

    /**
     * 获取把手控件
     */
    abstract fun getDragView(): View?

    /**
     * 默认支持拖拽操作
     */
    open fun isDraggable(): Boolean = true

    fun onBindViewHolder(itemTouchHelper: ItemTouchHelper?) {
        if (isDraggable()) {
            getDragView()?.setOnTouchListener { _, event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> itemTouchHelper?.startDrag(this)
                    else -> {
                    }
                }
                true
            }
        } else {
            getDragView()?.setOnTouchListener(null)
        }
    }
}