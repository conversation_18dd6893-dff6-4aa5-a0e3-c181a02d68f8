/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : OnDataReadyCompletedListener.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.home.load

import com.soundrecorder.browsefile.home.item.BaseItemRecordViewModel
import kotlinx.coroutines.Job

interface OnDataReadyCompletedListener<D : BaseItemRecordViewModel, T> {
    fun onDataReadyCompleted(model: AbsModel<D, T>, data: List<D>, args: T, callerName: String?, job: Job? = null)
    fun onDataAmpLoadCompleted(model: AbsModel<D, T>, dataList: MutableList<D>, group: T, callerName: String?)
}