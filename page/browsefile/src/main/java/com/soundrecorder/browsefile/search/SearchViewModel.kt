/************************************************************
 * Copyright 2000-2021 OPlus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : BrowseViewModel.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2021.06.04
 * History       :(ID,  2021.06.04, tianjun, Description)
 */
package com.soundrecorder.browsefile.search

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.asFlow
import androidx.lifecycle.viewModelScope
import androidx.paging.Pager
import androidx.paging.PagingConfig
import androidx.paging.PagingData
import androidx.paging.cachedIn
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.browsefile.home.load.BrowseViewModel
import com.soundrecorder.browsefile.search.load.ItemSearchViewModel
import com.soundrecorder.browsefile.search.load.SearchPagingSource
import com.soundrecorder.browsefile.search.load.SearchRepository
import com.soundrecorder.browsefile.search.utils.ForNoteUtil.isSupportSummarySearch
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.db.NoteDbUtils.queryAllNotes
import com.soundrecorder.common.db.SearchHistoryDBUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flattenMerge
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

class SearchViewModel(
    val searchRepository: SearchRepository,
    private val savedStateHandle: SavedStateHandle
) : ViewModel() {
    var isFromOtherApp: Boolean? = null

    companion object {
        const val TOP_MARGIN_ANIM_INIT = -1
        const val TOP_MARGIN_ANIM_DO = 0
        const val TOP_MARGIN_ANIM_NOT = 1

        private const val PAGE_SIZE: Int = 12
    }

    var isDoTopMarginAnim = TOP_MARGIN_ANIM_INIT
    var browseViewModel: BrowseViewModel? = null

    private val clearListCh = Channel<Unit>(Channel.CONFLATED)
    private val isSupportSummarySearch: Boolean by lazy {
        isSupportSummarySearch()
    }

    val searchHistory = MutableLiveData<MutableList<String>>()

    init {
        getSearchHistory()
    }

    val posts = flowOf(
        clearListCh.receiveAsFlow().map { PagingData.empty<ItemSearchViewModel>() },
        savedStateHandle.getLiveData<String>(SearchRepository.KEY_SEARCH_WORD)
            .asFlow()
            .flatMapLatest {
                createPagingData(it)
            }
            .cachedIn(viewModelScope)
    ).flattenMerge(2)

    private fun getSearchHistory() {
        viewModelScope.launch(Dispatchers.IO) {
            val result = SearchHistoryDBUtils.querySearchHistoryInSevenDays(BaseApplication.getAppContext())
            searchHistory.postValue(result.map { it.content }.toMutableList())
        }
    }

    fun insertSearchHistory(content: String) {
        viewModelScope.launch(Dispatchers.IO) {
            SearchHistoryDBUtils.insertSearchHistory(BaseApplication.getAppContext(), content, System.currentTimeMillis())
            if (searchHistory.value?.contains(content) == false && content.isNotEmpty()) {
                searchHistory.value?.add(0, content)
                searchHistory.postValue(searchHistory.value)
            }
        }
    }

    fun updateSearchHistory(content: String) {
        if (content.isEmpty()) return
        viewModelScope.launch(Dispatchers.IO) {
            SearchHistoryDBUtils.updateSearchHistory(BaseApplication.getAppContext(), content, System.currentTimeMillis())
            val list = searchHistory.value ?: mutableListOf()
            list.remove(content)
            list.add(0, content)
            searchHistory.postValue(list)
        }
    }

    fun deleteSearchHistory(content: String) {
        viewModelScope.launch(Dispatchers.IO) {
            SearchHistoryDBUtils.deleteSearchHistory(BaseApplication.getAppContext(), content)
            searchHistory.value?.remove(content)
        }
    }

    fun clearSearchHistory() {
        viewModelScope.launch(Dispatchers.IO) {
            SearchHistoryDBUtils.clearHistory(BaseApplication.getAppContext())
            searchHistory.postValue(mutableListOf())
        }
    }

    private fun createPagingData(
        searchValue: String,
        groupType: Int? = null
    ): Flow<PagingData<ItemSearchViewModel>> =
        Pager(
            config = PagingConfig(
                pageSize = if (isSupportSummarySearch) {
                    queryAllNotes().size.coerceAtLeast(PAGE_SIZE)
                } else {
                    PAGE_SIZE
                },
                initialLoadSize = if (isSupportSummarySearch) {
                    queryAllNotes().size.coerceAtLeast(PAGE_SIZE)
                } else {
                    PAGE_SIZE
                },
                prefetchDistance = 2
            ),
            pagingSourceFactory = {
                SearchPagingSource(
                    searchRepository,
                    mutableMapOf(
                        Pair(
                            SearchRepository.KEY_FROM_OTHER_APP,
                            if (isFromOtherApp == true) "1" else "0"
                        ),
                        Pair(SearchRepository.KEY_SEARCH_WORD, searchValue),
                        Pair(
                            SearchRepository.KEY_SUPPORT_FILTER,
                            (groupType ?: RecordModeConstant.BUCKET_VALUE_ALL).toString()
                        )
                    )
                )
            }
        ).flow

    fun showSearchData(searchValue: String?) {
        clearListCh.trySend(Unit).isSuccess
        savedStateHandle.set(SearchRepository.KEY_SEARCH_WORD, searchValue)
        searchRepository.startQueryTimeOut()
    }

    fun clearPageData() {
        clearListCh.trySend(Unit).isSuccess
    }

    fun releasePlayer() {
        browseViewModel?.releasePlayer()
        searchRepository.release()
    }
}