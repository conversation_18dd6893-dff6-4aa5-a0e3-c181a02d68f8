package com.soundrecorder.browsefile.search

import android.content.Context
import android.content.res.Configuration
import android.graphics.Rect
import android.os.Bundle
import android.text.InputFilter
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.appcompat.app.AlertDialog
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Guideline
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.AbstractSavedStateViewModelFactory
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.paging.LoadState
import androidx.recyclerview.widget.LinearLayoutManager
import com.coui.appcompat.chip.COUIChip
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.backpressed.OnBackPressedListener
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.ext.removeFragment
import com.soundrecorder.base.splitwindow.BaseFragment
import com.soundrecorder.base.splitwindow.WindowLayoutChangeListener
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.base.view.DeDuplicateInsetsCallback
import com.soundrecorder.browsefile.R
import com.soundrecorder.browsefile.databinding.FragmentSearchBinding
import com.soundrecorder.browsefile.home.BrowseFragment
import com.soundrecorder.browsefile.home.item.FastPlayHelper
import com.soundrecorder.browsefile.home.item.IBrowseViewHolderListener
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel
import com.soundrecorder.browsefile.home.item.ItemBrowseRecordViewModel.Companion.SUMMARY_SOURCE_NOTE
import com.soundrecorder.browsefile.home.load.BrowseViewModel
import com.soundrecorder.browsefile.home.load.ViewStatus
import com.soundrecorder.browsefile.parentchild.BrowseFileActivityViewModel
import com.soundrecorder.browsefile.search.item.SearchAdapter
import com.soundrecorder.browsefile.search.item.head.SearchHeaderAdapter
import com.soundrecorder.browsefile.search.item.head.SearchLoadStateAdapter
import com.soundrecorder.browsefile.search.load.ItemSearchViewModel
import com.soundrecorder.browsefile.search.load.SearchRepository
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.RecorderUserActionKt
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.utils.RecordModeUtil.isFromOther
import com.soundrecorder.common.utils.ViewUtils.addItemDecoration
import com.soundrecorder.common.widget.OSImageView
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.flow.collectLatest

class SearchFragment : BaseFragment<FragmentSearchBinding>(), OnBackPressedListener {
    companion object {
        const val TAG = "SearchFragment"
        const val JUMP_PLAY_BACK_TEXT = 0
        const val JUMP_PLAY_BACK_SUMMARY = 1
        const val SMALL_SCREEN_PERCENT = 0.28f
    }

    private var mItemAdapter: SearchAdapter? = null
    private var mHeaderAdapter: SearchHeaderAdapter? = null
    private var mFooterAdapter: SearchLoadStateAdapter? = null
    private var searchAnim: SearchAnim? = null
    private var isQueryClear = false
    private var taskId: Int = 0
    var searchCacheHolder: SearchCacheHolder? = null
    var browseViewModel: BrowseViewModel? = null
    private var mEmptyImageView: OSImageView? = null
    private val mBrowseFileActivityViewModel: BrowseFileActivityViewModel by activityViewModels<BrowseFileActivityViewModel>()
    private var disableDialog: AlertDialog? = null

    /*记录用户点击过搜索结果进入播放详情*/
    private var mClickToPlay: Boolean = false

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    val mViewModel by lazy {
        ViewModelProvider(
            this,
            object : AbstractSavedStateViewModelFactory(this@SearchFragment, null) {
                override fun <T : ViewModel> create(
                    key: String,
                    modelClass: Class<T>,
                    handle: SavedStateHandle
                ): T {
                    val repository = SearchRepository()
                    @Suppress("UNCHECKED_CAST")
                    return SearchViewModel(repository, handle) as T
                }
            }).get(SearchViewModel::class.java)
    }

    override var logTag: String
        get() = TAG
        set(value) {}

    override fun layoutId(): Int = R.layout.fragment_search

    override fun onViewCreated(savedInstanceState: Bundle?) {
        if (savedInstanceState == null) {
            resetData()
        }
        initView()
        initiateWindowInsets()
        initViewModel()
        initListAdapter()
        initAnim()
        initLayoutChangeListener()
        initChipGroup()
        (activity as? BaseActivity)?.registerBackPressed()
    }

    private fun initChipGroup() {
        mViewModel.searchHistory.observe(viewLifecycleOwner) { item ->
            mBinding.searchHistory.removeAllChips()
            setHistoryLayoutVisibility()
            item.forEach { content ->
                createHistoryChip(content)
            }
        }
        mBinding.clearHistory.setOnClickListener {
            mViewModel.clearSearchHistory()
        }
    }

    private fun setHistoryLayoutVisibility() {
        if (mViewModel.searchHistory.value?.isEmpty() == true) {
            mBinding.backgroundMask.visibility = View.GONE
        } else {
            mBinding.backgroundMask.visibility = View.VISIBLE
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        setHistoryLayoutVisibility()
    }

    private fun createHistoryChip(chipText: String, addToFont: Boolean = false, animate: Boolean = false): COUIChip {
        return (layoutInflater.inflate(
            R.layout.item_chip_search_suggestion,
            mBinding.searchHistory, false
        ) as COUIChip).apply {
            text = chipText
            setOnLongClickListener {
                isCloseIconVisible = !isCloseIconVisible
                true
            }
            setOnCloseIconClickListener {
                mViewModel.deleteSearchHistory(chipText)
                mBinding.searchHistory.removeChip(this)
            }
            setOnClickListener {
                (parentFragment as? BrowseFragment)?.onSearchHistoryClick(chipText)
                mViewModel.updateSearchHistory(chipText)
            }
            mBinding.searchHistory.addChip(
                this,
                if (addToFont) 0 else -1, animate
            )
        }
    }

    fun addSearchHistory(content: String) {
        mViewModel.insertSearchHistory(content)
    }

    private fun initiateWindowInsets() {
        if (mBinding.root != null) {
            val callback = object : DeDuplicateInsetsCallback() {
                override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                    val stableStatusBarInsets =
                        insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
                    mBinding.include.resultContainer.updatePadding(
                        bottom = stableStatusBarInsets.bottom,
                        left = stableStatusBarInsets.left, right = stableStatusBarInsets.right
                    )
                    mBinding.backgroundMask.updatePadding(bottom = stableStatusBarInsets.bottom)
                }
            }
            ViewCompat.setOnApplyWindowInsetsListener(mBinding.root, callback)
        }
    }

    private fun initLayoutChangeListener() {
        mBinding.rootView.addOnLayoutChangeListener(mLayoutChangeListener)
    }

    private val mLayoutChangeListener: WindowLayoutChangeListener =
        object : WindowLayoutChangeListener() {
            override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
                DebugUtil.i(TAG, "mLayoutChangeListener")
                if (mBinding.include.emptySearch.emptyContainer.isVisible) {
                    DebugUtil.i(TAG, "mLayoutChangeListener isVisible = $isVisible")
                    mEmptyImageView?.let {
                        it.post {
                            it.setScaleByEmptySize(
                                context?.px2dp(rect.width())?.toInt() ?: 0,
                                context?.px2dp(rect.height())?.toInt() ?: 0,
                                "SearchFragment"
                            )
                        }
                    }
                }
            }
        }

    override fun onDestroyView() {
        (mBinding.include.emptySearch.img as? EffectiveAnimationView)?.apply {
            if (isAnimating) {
                cancelAnimation()
            }
            setImageDrawable(null)
        }
        mBinding.rootView.removeOnLayoutChangeListener(mLayoutChangeListener)
        disableDialog.dismissWhenShowing()
        disableDialog = null
        (activity as? BaseActivity)?.unRegisterBackPressed()
        super.onDestroyView()
        buryingPointSetting()
    }

    private fun buryingPointSetting() {
        /*搜索页埋点事件，并清空 快捷播放次数 详情页播放次数*/
        BuryingPoint.addRecordSearchDurRecords(RecorderUserAction.VALUE_SEARCH_FRONT_PAGE, RecorderUserActionKt.sPlayCount, 0)
        BuryingPoint.addRecordSearchDurRecords(RecorderUserAction.VALUE_SEARCH_FRONT_PAGE, 0, RecorderUserActionKt.sPlayCount)
        RecorderUserActionKt.sPlayCount = 0
        RecorderUserActionKt.sInfoCount = 0
    }

    private fun initView() {
        mBinding.backgroundMaskContainer.setOnClickListener { onClickCancel() }
        mEmptyImageView = mBinding.include.emptySearch.img
        mBinding.include.resultList.addItemDecoration(com.soundrecorder.common.R.dimen.dp12, 1)
    }

    /**
     * browseViewModel comes from browseFragment, used for fileObserver, searchbox's animation
     * it will be lost when fragment is rebuilded. e.g change dark mode
     * so need to save it in SearchFragment's ViewModel, but it is not passed by bundle,
     * this variable is passed directly outside, and the fragment has not been add to activity, so no ViewMode can created
     *
     * just choose time to build and rebuild
     */
    private fun initBrowseViewModel() {
        if (browseViewModel != null) {
            //save the value to mViewModel
            mViewModel.browseViewModel = browseViewModel
        } else {
            //fragment rebuild, browseViewModel= null, so assign the value from mViewModel to this
            browseViewModel = mViewModel.browseViewModel
        }
    }

    private fun initViewModel() {
        taskId = activity?.taskId ?: 0
        initBrowseViewModel()
        mViewModel.isFromOtherApp = activity?.intent?.isFromOther()
        //observe the search value
        Log.d(TAG, "initViewModel: browseViewModel null is ${browseViewModel == null}")
        browseViewModel?.fileChange?.observe(viewLifecycleOwner, Observer { path ->
            // if cur search result is from dmp, no need to response to file changing, because update delay
            Log.d(
                TAG,
                "initViewModel fileChange: search value is ${browseViewModel?.searchValue?.value}, " +
                        "cur source is ${mViewModel.searchRepository.resultSource}"
            )
            if (mViewModel.searchRepository.resultSource == SearchRepository.RESULT_FROM_SELF) {
                val searchValue = browseViewModel?.searchValue?.value ?: ""
                if (!TextUtils.isEmpty(path) && !TextUtils.isEmpty(searchValue)) {
                    onQueryTextChange(searchValue)
                }
            }
        })
        //loadingBar
        mViewModel.searchRepository.queryTimeOut.observe(viewLifecycleOwner, Observer { timeOut ->
            Log.d(TAG, "queryTimeout is $timeOut")
            // 更改交互，当超时显示loading，隐藏上一次搜索结果view
            if (timeOut) {
                mBinding.include.resultList.visibility = View.GONE
                mBinding.include.emptySearch.emptyContainer.visibility = View.GONE
            }
            mBinding.include.colorSearchLoadView.visibility =
                if (timeOut) View.VISIBLE else View.GONE
        })
        //header of adapter, show the total count of items
        mViewModel.searchRepository.totalCount.observe(viewLifecycleOwner, Observer {
            Log.d(TAG, "totalCount is $it")
            if (it <= 0) {
                mBinding.include.resultList.visibility = View.GONE
                mBinding.include.emptySearch.emptyContainer.visibility = View.VISIBLE
                mHeaderAdapter?.totalCount = it
                initEmptyView()
            } else {
                mBinding.include.emptySearch.emptyContainer.visibility = View.GONE
                mBinding.include.resultList.visibility = View.VISIBLE
                mHeaderAdapter?.totalCount = it
            }
        })
        //real data
        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            mViewModel.posts.collectLatest {
                mItemAdapter?.submitData(it)
            }
        }
        viewLifecycleOwner.lifecycleScope.launchWhenCreated {
            mItemAdapter?.loadStateFlow?.collect {
                when {
                    it.refresh is LoadState.Loading -> {
                        mBinding.include.resultList.scrollToPosition(0)
                    }

                    it.append is LoadState.Error -> {
                        ToastManager.showShortToast(
                            activity,
                            (it.append as LoadState.Error).error.localizedMessage
                        )
                    }

                    else -> {
                        Log.d(TAG, "loadState append state is ${it.append}")
                        mFooterAdapter?.loadState = it.append
                    }
                }
            }
        }
    }

    private fun initEmptyView() {
        Log.i(TAG, "initEmptyView")
        initGuidLinePercent()
        //显示空页面的时候不一定会调用mLayoutChangeListener，所以还是需要设置一下高度
        val emptyContainer = mBinding.include.emptySearch.emptyContainer
        emptyContainer.post {
            context?.let {
                val width = it.px2dp(emptyContainer.width)
                val height = it.px2dp(emptyContainer.height)
                mEmptyImageView?.initView(width.toInt(), height.toInt(), TAG)
            }
        }
    }

    private fun initGuidLinePercent() {
        kotlin.runCatching {
            if (ScreenUtil.isSmallScreen(context)) {
                val guideline: Guideline = mBinding.include.emptySearch.guideEmptySearch
                val params = guideline.layoutParams as ConstraintLayout.LayoutParams
                if (params.guidePercent != SMALL_SCREEN_PERCENT) {
                    params.guidePercent = SMALL_SCREEN_PERCENT
                    guideline.layoutParams = params
                    DebugUtil.d(TAG, "Guideline for small screen set")
                }
            }
        }
    }

    private fun initListAdapter() {
        mItemAdapter = SearchAdapter(context, this,
            object : IBrowseViewHolderListener {
                override fun getPlayerController(): FastPlayHelper? {
                    return browseViewModel?.fastPlayHelper
                }

                override fun onClickItem(data: ItemBrowseRecordViewModel) {
                    DebugUtil.e(TAG, "onClickItem click the item parent.")
                    onClickSearchItem(data, JUMP_PLAY_BACK_TEXT)
                }

                override fun isCurrentPlay(data: ItemBrowseRecordViewModel): Boolean {
                    return getPlayerController()?.itemBrowseRecordViewModel?.mediaId == data.mediaId
                }

                override fun getWindowType(): MutableLiveData<WindowType> {
                    return mBrowseFileActivityViewModel.windowType
                }

                override fun getPlayLiveData(): MutableLiveData<StartPlayModel?> {
                    return mBrowseFileActivityViewModel.mCurrentPlayRecordData
                }

                override fun onClickSummaryIcon(data: ItemBrowseRecordViewModel) {
                    DebugUtil.w(TAG, "onClickSummaryIcon")
                    jumpSummaryPage(data)
                }

                override fun onClickSummaryItem(data: ItemBrowseRecordViewModel) {
                    DebugUtil.w(TAG, "onClickSummaryItem")
                    jumpSummaryPage(data)
                }

                override fun onClickConvertItem(data: ItemBrowseRecordViewModel) {
                    DebugUtil.w(TAG, "onClickConvertItem")
                    onClickSearchItem(data, JUMP_PLAY_BACK_TEXT)
                }

                private fun jumpSummaryPage(clickSearchItem: ItemBrowseRecordViewModel) {
                    DebugUtil.w(TAG, "jumpSummaryPage summary source:${clickSearchItem.summarySource}")
                    val noteId = clickSearchItem.noteId ?: return
                    (parentFragment as? BrowseFragment)?.clickedItemSummaryNoteId = noteId
                    if (clickSearchItem.summarySource == SUMMARY_SOURCE_NOTE) {
                        jumpToNotesSummaryActivity(clickSearchItem)
                    } else {
                        onClickSearchItem(clickSearchItem, JUMP_PLAY_BACK_SUMMARY)
                    }
                    SummaryStaticUtil.addClickViewSummaryEvent(SummaryStaticUtil.EVENT_FROM_SEARCH)
                }

                private fun onClickSearchItem(
                    data: ItemBrowseRecordViewModel,
                    targetIndex: Int
                ) {
                    DebugUtil.w(TAG, "onClickSearchItem target:$targetIndex")
                    hideInput()
                    val searchData = data as ItemSearchViewModel
                    if (data.mediaId != mBrowseFileActivityViewModel.mCurrentPlayRecordData.value?.mediaId) {
                        val currentIsPlay =
                            isCurrentPlay(data) && data.isNeedShowSeekBarArea(browseViewModel?.fastPlayHelper?.getPlayerState())
                        mClickToPlay = true
                        mBrowseFileActivityViewModel.mCurrentPlayRecordData.value =
                            searchData.toStartPlayModel(
                                isFromOtherApp = mViewModel.isFromOtherApp ?: false,
                                seekToMill = if (currentIsPlay) {
                                    browseViewModel?.fastPlayHelper?.getCurrentPlayerTime() ?: 0
                                } else {
                                    null
                                },
                                autoPlay = if (mBrowseFileActivityViewModel.isSmallWindow()) {
                                    true // 小屏同原有逻辑，自动开始播放
                                } else if (currentIsPlay) {
                                    browseViewModel?.fastPlayHelper?.mediaPlayerManager?.isPlaying()
                                        ?: false
                                } else {
                                    false
                                },
                                isRecycle = data.isRecycle
                            ).apply {
                                selectPosInPlayback = targetIndex
                                searchKeyWord = searchData.searchValue ?: ""
                            }
                        // 埋点
                        BuryingPoint.addPlayFromAndDuration(
                            RecorderUserAction.VALUE_PLAY_FROM_PLAYBACK,
                            data.recordType().toString(), data.mDuration
                        )
                        BuryingPoint.addSearch(RecorderUserAction.VALUE_SEARCH_PLAY_PLAYBACK)
                        /*详情页播放次数累加*/
                        RecorderUserActionKt.sInfoCount++
                        (parentFragment as? BrowseFragment)?.getSearchKey()?.let {
                            mViewModel.insertSearchHistory(it)
                        }
                    }
                    browseViewModel?.releasePlayer()
                }
            })
        mBinding.include.resultList.apply {
            adapter = mItemAdapter!!.withLoadStateHeaderAndFooter(
                header = SearchHeaderAdapter().also { mHeaderAdapter = it },
                footer = SearchLoadStateAdapter().also { mFooterAdapter = it }
            )
            layoutManager = LinearLayoutManager(context)
            itemAnimator = null

            setOnTouchListener { _, _ ->
                hideInput()
                false
            }
        }
    }

    fun jumpToNotesSummaryActivity(searchDataItem: ItemBrowseRecordViewModel) {
        DebugUtil.w(
            TAG,
            "jumpToNotesSummaryActivity noteId:${searchDataItem.noteId} recordUUID:${searchDataItem.recordUUID}"
        )
        val activity = activity ?: return
        val callId = searchDataItem.recordUUID ?: return
        val noteId = searchDataItem.noteId ?: return
        (parentFragment as? BrowseFragment)?.clickedItemSummaryNoteId = noteId
        summaryApi?.toNotesSummaryActivity(
            activity, callId, noteId, BrowseFragment.REQUEST_CODE_START_NOTE
        ) { clearSummary, disableDialog ->
            if (clearSummary) {
                browseViewModel?.clearAllSummary()
            } else {
                <EMAIL> = disableDialog
            }
        }
        SummaryStaticUtil.addClickViewSummaryEvent(SummaryStaticUtil.EVENT_FROM_SEARCH)
    }

    private fun hideInput() {
        if (searchCacheHolder != null && searchCacheHolder?.mSearchAnimView != null) {
            val imm = BaseApplication.getAppContext()
                .getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(searchCacheHolder?.mSearchAnimView?.windowToken, 0)
        }
    }

    private fun initAnim() {
        searchAnim = SearchAnim(this)
    }

    fun inSearch(useMaxHeiht: Boolean = false) {
        //将动画放慢后out动画未执行完就执行in动画会出现异常，但是这里没办法在拦截
        //原因：1、和outSearch时一样判断动画没结束不要执行下面代码时SearchFragment每次都重建，searchAnim都是新建的，isSubTitleInAnimation永远是false，更不能放到replaceFragment之前，因为bind依赖于add
        //2、也不能用cancelAnimation的方式，因为searchAnimView是coui动画，不能取消
        searchCacheHolder?.mSearchAnimView?.let {
            it.searchEditText.filters =
                arrayOf<InputFilter>(InputFilter.LengthFilter(50))
            it.searchEditText.hint = BaseApplication.getAppContext().resources.getString(com.soundrecorder.common.R.string.search_recorder)
            val searchValue = browseViewModel?.searchValue?.value ?: ""
            it.searchEditText.setText(searchValue)
            // 首页列表隐藏状态，就不要做显示搜索框动效，否则会导致小屏搜索进入播放详情，重建后播放详情弹出输入框
            if (parentFragment?.isHidden != true) {
                it.showInToolBar()
            }
            searchAnim?.animateSearchIn(useMaxHeiht)
        }
    }

    private fun resetData() {
        browseViewModel?.releasePlayer()
    }

    private fun outSearch() {
        if (searchAnim?.isSubTitleInAnimation() == true) {
            DebugUtil.v(TAG, "out search, but last searchAnim is running")
            return
        }
        BuryingPoint.addSearch(RecorderUserAction.VALUE_SEARCH_CANCEL_CLICK)
        browseViewModel?.searchValue?.value = ""
        val searchAnimView = searchCacheHolder?.mSearchAnimView
        searchAnimView?.hideInToolBar()
        /*1.中大屏下，用户当前进入搜索页面，点击了某项搜索结果加载其播放详情，退出搜索，列表自动定位并选中该条录音；若执行原有动画会出现明显跳动*/
        val noExitAnimUnSmallWindow =
            mClickToPlay && (!mBrowseFileActivityViewModel.isSmallWindow())
                    && (mBrowseFileActivityViewModel.mCurrentPlayRecordData.value?.isFromSearch == true)
        /*2.小屏下，播放数据不为null(播放fragment存在)，首页被播放页面覆盖，不能执行动画*/
        val noExitAnimSmallWindow =
            (ScreenUtil.isSmallScreen(context)) && (mBrowseFileActivityViewModel.hasPlayPageData())
        searchAnim?.animateSearchOut(!(noExitAnimUnSmallWindow || noExitAnimSmallWindow))
        resetData()
        parentFragment?.removeFragment(this)
        browseViewModel?.showSearch?.value = false
        if (noExitAnimUnSmallWindow) {
            (parentFragment as? BrowseFragment)?.scrollToPlayItemPosition()
        }
    }

    fun onClickCancel(): Boolean {
        mViewModel.releasePlayer()
        outSearch()
        return true
    }

    fun onQueryTextChange(newText: String?): Boolean {
        val searchValue = newText?.trim() ?: ""
        if (TextUtils.isEmpty(searchValue)) {
            //hide the total count, avoid to show the old item count after empty searchValue
            mHeaderAdapter?.totalCount = 0
            mBinding.include.resultContainer.visibility = View.GONE
            mBinding.include.emptySearch.emptyContainer.visibility = View.GONE
            mBinding.include.colorSearchLoadView.visibility = View.GONE
            isQueryClear = true
            val status = browseViewModel?.browseModel?.liveViewStatus?.value
            DebugUtil.d(TAG, "$status " + (ViewStatus.EMPTY == status))
            searchCacheHolder?.browseFileList?.visibility =
                if (status != ViewStatus.SHOW_CONTENT) View.GONE else View.VISIBLE
            mViewModel.clearPageData()
        } else {
            searchCacheHolder?.browseFileList?.visibility = View.GONE
            mBinding.include.resultContainer.visibility = View.VISIBLE
            if (isQueryClear) {
                isQueryClear = false
                mBinding.include.emptySearch.emptyContainer.visibility = View.GONE
            }
            mViewModel.showSearchData(searchValue)
        }

        return false
    }

    override fun onBackPressed(): Boolean {
        return onClickCancel()
    }
}