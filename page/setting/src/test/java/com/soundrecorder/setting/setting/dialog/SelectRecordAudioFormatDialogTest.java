/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SelectRecordAudioFormatDialogTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.setting.setting.dialog;

import static org.robolectric.Shadows.shadowOf;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.coui.appcompat.panel.COUIBottomSheetDialog;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;

import org.robolectric.RuntimeEnvironment;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowDialog;

import com.soundrecorder.base.utils.OS12FeatureUtil;
import com.soundrecorder.common.constant.RecorderConstant;
import com.soundrecorder.setting.setting.SettingRecorderActivity;
import com.soundrecorder.setting.setting.dialog.SelectRecordAudioFormatDialog;
import com.soundrecorder.setting.shadows.ShadowFeatureOption;
import com.soundrecorder.setting.shadows.ShadowOS12FeatureUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class SelectRecordAudioFormatDialogTest {
    private Context mContext;
    private ActivityController<SettingRecorderActivity> mController;

    @Before
    public void setUp() throws Exception {
        mContext = ApplicationProvider.getApplicationContext();
        mController = Robolectric.buildActivity(SettingRecorderActivity.class);
    }


    @After
    public void tearDown() {
        mContext = null;
        mController = null;
    }

    @Test
    public void should_notNull_whenCreate() {
        SelectRecordAudioFormatDialog dialog = new SelectRecordAudioFormatDialog(mContext);
        Assert.assertNotNull(dialog);
    }

    @Test
    public void should_showDialog_whenShow() {
        SettingRecorderActivity activity = mController.create().get();
        SelectRecordAudioFormatDialog dialog = new SelectRecordAudioFormatDialog(activity);
        dialog.showDialog(RecorderConstant.RECORDER_AUDIO_FORMAT_MP3);
        ShadowDialog realDialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNotNull(realDialog);

        dialog.release();
        Assert.assertNotNull(realDialog);

        COUIBottomSheetDialog mDialog = Whitebox.getInternalState(dialog, "mDialog");
        Assert.assertNull(mDialog);
    }

    @Test
    public void should_showDialog_whenRelease() {
        SettingRecorderActivity activity = mController.create().get();
        SelectRecordAudioFormatDialog dialog = new SelectRecordAudioFormatDialog(activity);
        dialog.release();

        dialog.showDialog(RecorderConstant.RECORDER_AUDIO_FORMAT_MP3);
        ShadowDialog realDialog = shadowOf(RuntimeEnvironment.application).getLatestDialog();
        Assert.assertNotNull(realDialog);

        dialog.release();
        COUIBottomSheetDialog mDialog = Whitebox.getInternalState(dialog, "mDialog");
        Assert.assertNull(mDialog);
    }

    @Test
    public void should_correct_whenGetItemPos() throws Exception {
        SelectRecordAudioFormatDialog dialog = new SelectRecordAudioFormatDialog(mContext);

        int pos = Whitebox.invokeMethod(dialog, "getItemPos", RecorderConstant.RECORDER_AUDIO_FORMAT_WAV);
        Assert.assertEquals(2, pos);

        pos = Whitebox.invokeMethod(dialog, "getItemPos", RecorderConstant.RECORDER_AUDIO_FORMAT_MP3);
        Assert.assertEquals(0, pos);

        pos = Whitebox.invokeMethod(dialog, "getItemPos", RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS);
        Assert.assertEquals(1, pos);

        pos = Whitebox.invokeMethod(dialog, "getItemPos", -1);
        Assert.assertEquals(0, pos);
    }

    @Test
    public void should_correct_whenCalAudioFormatByPos() throws Exception {
        SelectRecordAudioFormatDialog dialog = new SelectRecordAudioFormatDialog(mContext);

        int pos = Whitebox.invokeMethod(dialog, "calAudioFormatByPos", 1);
        Assert.assertEquals(RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS, pos);

        pos = Whitebox.invokeMethod(dialog, "calAudioFormatByPos", 0);
        Assert.assertEquals(RecorderConstant.RECORDER_AUDIO_FORMAT_MP3, pos);

        pos = Whitebox.invokeMethod(dialog, "calAudioFormatByPos", 2);
        Assert.assertEquals(RecorderConstant.RECORDER_AUDIO_FORMAT_WAV, pos);

        pos = Whitebox.invokeMethod(dialog, "calAudioFormatByPos", -1);
        Assert.assertEquals(RecorderConstant.RECORDER_AUDIO_FORMAT_MP3, pos);
    }

    @Test
    public void should_correct_when_isSupportWav() throws Exception {
        MockedStatic<OS12FeatureUtil> mockedStatic = Mockito.mockStatic(OS12FeatureUtil.class);
        mockedStatic.when(() -> OS12FeatureUtil.isColorOs12()).thenReturn(false, true, true);

        SettingRecorderActivity activity = mController.create().get();
        SelectRecordAudioFormatDialog dialog = new SelectRecordAudioFormatDialog(activity);
        Assert.assertTrue(Whitebox.invokeMethod(dialog, "isSupportWav"));
        Assert.assertFalse(Whitebox.invokeMethod(dialog, "isSupportWav"));

        dialog.showDialog(0);
        mockedStatic.close();
    }
}
