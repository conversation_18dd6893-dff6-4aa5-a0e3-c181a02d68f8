/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyCOUIPreferenceTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.setting.privacypolicy

import android.content.Context
import android.os.Build
import androidx.fragment.app.Fragment
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyInterface
import com.soundrecorder.setting.R
import com.soundrecorder.setting.shadows.ShadowFeatureOption
import com.soundrecorder.setting.shadows.ShadowOS12FeatureUtil
import io.mockk.every
import io.mockk.mockk
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.koin.core.context.startKoin
import org.koin.core.context.stopKoin
import org.koin.dsl.koinApplication
import org.koin.dsl.module
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLooper

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class PrivacyPolicyCOUIPreferenceTest {
    private lateinit var controller: ActivityController<PrivacyPolicyActivity>
    private lateinit var context: Context

    private val privacy = mockk<PrivacyPolicyInterface>()

    private val koinApp = koinApplication {
        modules(module {
            single { privacy }
        })
    }


    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        controller = Robolectric.buildActivity(PrivacyPolicyActivity::class.java)
        startKoin(koinApp)
        every { privacy.newPrivacyPolicyFragment() } returns Fragment()
    }

    @After
    fun tearDown() {
        stopKoin()
    }

    @Test
    fun check_onBindViewHolder() {
        val activity = controller.create().start().resume().get()
        ShadowLooper.runUiThreadTasksIncludingDelayedTasks()
        val policyFragmentTag = (com.soundrecorder.common.R.string.privacy_policy_settings_key).toString()
        val fragment = activity.supportFragmentManager.findFragmentByTag(policyFragmentTag)
        Assert.assertNotNull(fragment)
        fragment?.onCreateView(
            activity.layoutInflater,
            activity.findViewById(R.id.root_layout),
            null
        )
    }
}