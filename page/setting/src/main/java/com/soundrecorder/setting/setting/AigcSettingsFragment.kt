/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AigcSettingsFragment.kt
 ** Description : AigcSettingsFragment.kt
 ** Version     : 1.0
 ** Date        : 2025/07/05
 ** Author      : <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/07/05     1.0      create
 ***********************************************************************/
package com.soundrecorder.setting.setting

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.preference.PreferenceGroupAdapter
import androidx.preference.PreferenceScreen
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.NO_POSITION
import com.coui.appcompat.card.AnimDisplayInfo
import com.coui.appcompat.card.COUICardInstructionPreference
import com.coui.appcompat.preference.COUIPreferenceWithAppbarFragment
import com.soundrecorder.base.BaseApplication.getAppContext
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.base.utils.ScreenUtil.getWindowType
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity
import com.soundrecorder.common.permission.PermissionDialogUtils.PermissionDialogListener
import com.soundrecorder.common.permission.PermissionDialogUtils.showPermissionAllFileAccessDialog
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.task.AppTaskUtil.isSupportSmartName
import com.soundrecorder.modulerouter.convertService.ConvertSupportAction
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.PAGE_FROM_AIGC_SETTING
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_SMART_SHORTHAND
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_USER_NOTICE
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.setting.R
import com.soundrecorder.setting.widget.SmartNamePreference
import com.soundrecorder.summary.util.AIUnitApi
import java.lang.ref.WeakReference

class AigcSettingsFragment : COUIPreferenceWithAppbarFragment() {
    companion object {
        private const val TAG = "AigcSettingsFragment"
        private const val PREF_INSTRUCTION_PAGE = "pref_instruction_page"
        private const val PREF_SMART_GENERATION = "pref_setting_smart_generation"
        private const val CONVERT_TEXT_ANIM_INDEX = 0
        private const val RECORD_ASSISTANT_ANIM_INDEX = 1
        private const val TEXT_SUMMARY_ANIM_INDEX = 2
        private const val CONTENT_SHARE_ANIM_INDEX = 3
        private const val DEFAULT_CACHE_SIZE = 4

        private val CONVERT_TEXT_ANIM_RES = AnimJsonResWrap(
            DeviceAnimRes(R.raw.setting_aigc_anim_convert_text, R.raw.setting_aigc_anim_convert_text_dark),
            DeviceAnimRes(R.raw.setting_aigc_anim_convert_text_fold, R.raw.setting_aigc_anim_convert_text_fold_dark),
            DeviceAnimRes(R.raw.setting_aigc_anim_convert_text, R.raw.setting_aigc_anim_convert_text_dark)
        )

        private val RECORD_ASSISTANT_ANIM_RES = AnimJsonResWrap(
            DeviceAnimRes(R.raw.setting_aigc_anim_record_assistant, R.raw.setting_aigc_anim_record_assistant_dark),
            DeviceAnimRes(R.raw.setting_aigc_anim_record_assistant_fold, R.raw.setting_aigc_anim_record_assistant_fold_dark),
            DeviceAnimRes(R.raw.setting_aigc_anim_record_assistant, R.raw.setting_aigc_anim_record_assistant_dark),
        )

        private val TEXT_SUMMARY_ANIM_RES = AnimJsonResWrap(
            DeviceAnimRes(R.raw.setting_aigc_anim_text_summary, R.raw.setting_aigc_anim_text_summary_dark),
            DeviceAnimRes(R.raw.setting_aigc_anim_text_summary_fold, R.raw.setting_aigc_anim_text_summary_fold_dark),
            DeviceAnimRes(R.raw.setting_aigc_anim_text_summary, R.raw.setting_aigc_anim_text_summary_dark),
        )

        private val CONTENT_SHARE_ANIM_RES = AnimJsonResWrap(
            DeviceAnimRes(R.raw.setting_aigc_anim_content_share, R.raw.setting_aigc_anim_content_share_dark),
            DeviceAnimRes(R.raw.setting_aigc_anim_content_share_fold, R.raw.setting_aigc_anim_content_share_fold_dark),
            DeviceAnimRes(R.raw.setting_aigc_anim_content_share, R.raw.setting_aigc_anim_content_share_dark),
        )
    }

    private var instructionPref: COUICardInstructionPreference? = null
    private var smartNamePref: SmartNamePreference? = null

    private var mFilePermissionDialog: AlertDialog? = null

    private val itemDecoration by lazy { Decoration() }

    private val smartNameAction by lazy {
        Injector.injectFactory<SmartNameAction>()
    }

    private val convertAction by lazy {
        Injector.injectFactory<ConvertSupportAction>()
    }

    private val aiAsrManagerAction by lazy {
        Injector.injectFactory<AIAsrManagerAction>()
    }

    override fun getTitle(): String {
        return getAppContext().getString(com.soundrecorder.common.R.string.setting_aigc_title)
    }

    override fun onCreateRecyclerView(
        inflater: LayoutInflater?,
        parent: ViewGroup?,
        savedInstanceState: Bundle?
    ): RecyclerView {
        val rv = super.onCreateRecyclerView(inflater, parent, savedInstanceState)
        if (rv is COUIRecyclerView) {
            rv.setDispatchEventWhileScrolling(true)
        }
        return rv
    }

    @SuppressLint("RestrictedApi")
    override fun onCreateAdapter(preferenceScreen: PreferenceScreen?): RecyclerView.Adapter<*> {
        return PreferenceGroupAdapter(preferenceScreen).apply {
            itemDecoration.adapter = this
        }
    }

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        super.onCreatePreferences(savedInstanceState, rootKey)
        addPreferencesFromResource(R.xml.aigc_settings)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView()
        initPreference()
    }

    override fun onResume() {
        super.onResume()
        updatePreference()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        initPreference()
    }

    override fun onDestroy() {
        super.onDestroy()
        dismissFilePermissionDialog()
    }

    fun onPrivacyPolicySuccess(type: Int) {
        if (type == TYPE_USER_NOTICE) {
            smartNameAction?.setSmartNameSwitchStatus(getAppContext(), true, false)
            updatePreference()
        }
    }

    fun onPrivacyPolicyFail(type: Int) {
        if (type == TYPE_USER_NOTICE) {
            smartNameAction?.setSmartNameSwitchStatus(getAppContext(), false, false)
            updatePreference()
        }
    }

    private fun initView() {
        listView.apply {
            setItemViewCacheSize(DEFAULT_CACHE_SIZE)
            addItemDecoration(itemDecoration)
            setPadding(0, 0, 0, resources.getDimensionPixelOffset(com.support.card.R.dimen.coui_component_card_entrance_last_bottom_margin))
        }
    }

    private fun initPreference() {
        instructionPref = findPreference(PREF_INSTRUCTION_PAGE)
        instructionPref?.setDisplayInfos(getCardPageInfo())
        smartNamePref = findPreference(PREF_SMART_GENERATION)
        smartNamePref?.apply {
            updatePreference()
            setOnClick { switchSmartGeneration() }
        }
    }

    private fun updatePreference() {
        smartNamePref?.apply {
            val isOpen = smartNameAction?.isSmartNameSwitchOpen(getAppContext()) ?: false
            setChecked(isOpen)
            isVisible = isSupportSmartName || isOpen
        }
    }

    private fun switchSmartGeneration() {
        val isOpen = smartNameAction?.isSmartNameSwitchOpen(getAppContext()) ?: false
        smartNamePref?.setChecked(!isOpen)
        if (isOpen) {
            smartNameAction?.setSmartNameSwitchStatus(getAppContext(), false, true)
        } else {
            if (!PermissionUtils.hasFuncTypePermission(TYPE_PERMISSION_SMART_SHORTHAND)) {
                showSmartShortHandDialog()
                return
            }
            smartNameAction?.setSmartNameSwitchStatus(getAppContext(), true, true)
            continueSmartGenerationSetup()
        }
    }

    private fun showSmartShortHandDialog() {
        if (activity is PrivacyPolicyBaseActivity) {
            val privacyDelegate = (activity as? PrivacyPolicyBaseActivity)?.getPrivacyPolicyDelegate() ?: return
            privacyDelegate.resumeShowDialog(TYPE_PERMISSION_SMART_SHORTHAND, true, PAGE_FROM_AIGC_SETTING)
        }
    }

    private fun continueSmartGenerationSetup() {
        if (!PermissionUtils.hasAllFilePermission()) {
            showPermissionAllFileDialog()
            return
        }
        showSmartAiunitDialog()
    }

    private fun showSmartAiunitDialog() {
        val activity = activity ?: return
        val summaryManager = smartNameAction?.newUnifiedSummaryManager() ?: return
        summaryManager.showAiUnitPluginsDialog(activity, isOpenSwitch = true, isSummary = false)
    }

    private fun showPermissionAllFileDialog() {
        val activity = activity ?: return
        mFilePermissionDialog = showPermissionAllFileAccessDialog(activity, AllFilePermissionDialogListener(this))
    }

    private fun dismissFilePermissionDialog() {
        mFilePermissionDialog?.let {
            if (it.isShowing) {
                it.dismiss()
            }
            mFilePermissionDialog = null
        }
    }

    private fun getCardPageInfo(): ArrayList<AnimDisplayInfo> {
        val isSupportConvert = convertAction?.isSupportConvert(true) ?: false
        val isSupportAsr = aiAsrManagerAction?.loadSupportAIAsr(getAppContext(), true) ?: false
        val isSupportSmartName = smartNameAction?.checkSupportSmartName(getAppContext(), isSupportConvert) ?: false
        DebugUtil.d(TAG, "getCardPageInfo convert:$isSupportConvert, asr:$isSupportAsr, smartName:$isSupportSmartName")
        val screenType = getWindowType()
        val isNightMode = NightModeUtil.isNightMode(getAppContext())
        DebugUtil.d(TAG, "getCardPageInfo screenType:$screenType, isNightMode:$isNightMode")
        return arrayListOf<AnimDisplayInfo>().apply {
            if (isSupportAsr) {
                add(
                    createImageDisplayInfo(
                        CONVERT_TEXT_ANIM_INDEX,
                        com.soundrecorder.common.R.string.intro_convert_text_title,
                        com.soundrecorder.common.R.string.intro_convert_text_desc,
                        screenType,
                        isNightMode
                    )
                )
            }

            if (isSupportSmartName) {
                add(
                    createImageDisplayInfo(
                        RECORD_ASSISTANT_ANIM_INDEX,
                        com.soundrecorder.common.R.string.intro_record_assistant_title,
                        com.soundrecorder.common.R.string.intro_record_assistant_desc,
                        screenType,
                        isNightMode
                    )
                )
            }

            if (AIUnitApi.summaryAvailable(getAppContext())) {
                add(
                    createImageDisplayInfo(
                        TEXT_SUMMARY_ANIM_INDEX,
                        com.soundrecorder.common.R.string.intro_text_summary_title,
                        com.soundrecorder.common.R.string.intro_text_summary_desc,
                        screenType,
                        isNightMode
                    )
                )
            }
            add(
                createImageDisplayInfo(
                    CONTENT_SHARE_ANIM_INDEX,
                    com.soundrecorder.common.R.string.intro_content_share_title,
                    com.soundrecorder.common.R.string.intro_content_share_desc,
                    screenType,
                    isNightMode
                )
            )
        }
    }

    private fun createImageDisplayInfo(
        animIndex: Int,
        titleId: Int,
        descId: Int,
        screenType: WindowType,
        isNightMode: Boolean
    ): AnimDisplayInfo {
        val animResourceId = getAnimResByIndex(animIndex, screenType, isNightMode)
        val title = getAppContext().getString(titleId)
        val desc = getAppContext().getString(descId)
        return AnimDisplayInfo(title, desc).apply {
            animResources.add(animResourceId)
            animWidth = resources.getDimensionPixelOffset(R.dimen.instruction_card_width)
            animHeight = resources.getDimensionPixelOffset(R.dimen.instruction_card_height)
        }
    }

    private fun getAnimResByIndex(animIndex: Int, screenType: WindowType, isNightMode: Boolean): Int {
        val animResWrap = when (animIndex) {
            CONVERT_TEXT_ANIM_INDEX -> CONVERT_TEXT_ANIM_RES
            RECORD_ASSISTANT_ANIM_INDEX -> RECORD_ASSISTANT_ANIM_RES
            TEXT_SUMMARY_ANIM_INDEX -> TEXT_SUMMARY_ANIM_RES
            CONTENT_SHARE_ANIM_INDEX -> CONTENT_SHARE_ANIM_RES
            else -> CONVERT_TEXT_ANIM_RES
        }

        val screenAnimRes = when (screenType) {
            WindowType.LARGE -> animResWrap.tablet
            WindowType.MIDDLE -> animResWrap.fold
            else -> animResWrap.default
        }
        return if (isNightMode) screenAnimRes.night else screenAnimRes.light
    }


    @SuppressLint("RestrictedApi")
    class Decoration(var adapter: PreferenceGroupAdapter? = null) : RecyclerView.ItemDecoration() {

        override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
            val position = parent.getChildAdapterPosition(view)
            if (position == NO_POSITION) return
            val preference = adapter?.getItem(position)
            if (preference !is COUICardInstructionPreference) return
            val resource = view.context.resources
            val margin = resource.getDimensionPixelOffset(com.support.card.R.dimen.coui_component_card_entrance_large_horizontal_margin)
            outRect.apply {
                left = margin
                right = margin
            }
        }
    }

    class AllFilePermissionDialogListener(fragment: AigcSettingsFragment) : PermissionDialogListener {
        private val weakFragment = WeakReference(fragment)

        override fun dialogPermissionType(dialogPermissionType: Int) {
            DebugUtil.d(TAG, "dialogPermissionType $dialogPermissionType")
        }

        override fun onBackPress(alertType: Int) {
            DebugUtil.d(TAG, "onBackPress, alertType:$alertType")
        }

        override fun onClick(alertType: Int, isOk: Boolean, permissions: java.util.ArrayList<String>?) {
            val fragment = weakFragment.get() ?: return
            val activity = fragment.activity ?: return
            if (activity.isDestroyed) return

            if (isOk) {
                PermissionUtils.goToAppAllFileAccessConfigurePermissions(activity)
            }
            fragment.dismissFilePermissionDialog()
        }
    }

    data class AnimJsonResWrap(
        val default: DeviceAnimRes,
        val fold: DeviceAnimRes,
        val tablet: DeviceAnimRes,
    )

    data class DeviceAnimRes(val light: Int, val night: Int)
}