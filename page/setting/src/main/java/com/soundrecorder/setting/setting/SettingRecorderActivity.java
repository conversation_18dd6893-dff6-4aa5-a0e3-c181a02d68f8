/************************************************************
 * Copyright 2000-2009 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : SetRecorder.java
 * Version Number: 1.0
 * Description   :
 * Author        : zhanghr
 * Date          : 2009-12-1
 * History       :(ID,  2009-12-1, zhanghr, Description)
 ************************************************************/

package com.soundrecorder.setting.setting;

import android.app.Activity;
import android.os.Bundle;
import android.view.MenuItem;
import com.soundrecorder.base.BaseActivity;
import com.soundrecorder.base.utils.ClickUtils;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.flexible.FollowHandPanelUtils;
import com.soundrecorder.setting.R;

/**
 * Plan Settings
 */

final public class SettingRecorderActivity extends BaseActivity {

    private static final String TAG = "SettingRecorderActivity";
    private static final String FRAGMENT_TAG = "setting_fragment";
    /*用于识别,统计页面 勿改*/
    private static final String FUNCTION_NAME = "Setting";
    /*multi user*/

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        DebugUtil.e(TAG,"onCreate");
        setContentView(R.layout.activity_setting_recorder);
        getSupportFragmentManager().beginTransaction().replace(R.id.root_layout, getSettingFragment(), FRAGMENT_TAG).commit();
        FollowHandPanelUtils.checkActivityClickOutOfBounds(this, findViewById(R.id.root_layout));
    }

    private SettingFragment getSettingFragment() {
        SettingFragment settingFragment = (SettingFragment) getSupportFragmentManager().findFragmentByTag(FRAGMENT_TAG);
        if (settingFragment == null) {
            settingFragment = new SettingFragment();
        }
        return settingFragment;
    }


    @Override
    public void onTopResumedActivityChanged(boolean isTopResumedActivity) {
        SettingFragment settingFragment = (SettingFragment) getSupportFragmentManager().findFragmentByTag(FRAGMENT_TAG);
        if ((settingFragment != null) && isTopResumedActivity && isInMultiWindowMode()) {
            settingFragment.onActivityTopResumed();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        DebugUtil.d(TAG, "onOptionsItemSelected item:" + item.getItemId());
        if (ClickUtils.isFastDoubleClick(ClickUtils.DURATION_500)) {
            DebugUtil.d(TAG, "onOplusOptionsMenuItemSelected() isFastDoubleClick return");
            return false;
        }

        switch (item.getItemId()) {
            case android.R.id.home:
                setResult(Activity.RESULT_CANCELED);
                this.finish();
                break;
            default:
                break;
        }
        return super.onOptionsItemSelected(item);
    }
}
