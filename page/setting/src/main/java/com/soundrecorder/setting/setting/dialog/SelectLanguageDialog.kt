/**
 * Copyright (C), 2008-2025 OPLUS Mobile Comm Corp., Ltd.
 * File: SelectLanguageDialog
 * Description:
 * Version: 1.0
 * Date: 2025/6/4
 * Author: <EMAIL>
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 */
package com.soundrecorder.setting.setting.dialog

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.adapter.ChoiceListAdapter
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector

/**
 * 语言选择弹窗
 */
class SelectLanguageDialog(val activity: Activity, var isSpLanguageSetNullOrEmpty: Boolean) {

    companion object {
        private const val TAG = "SelectLanguageDialog"
    }
    private var mDialog: AlertDialog? = null
    var dialogItemListener: DialogItemClickListener? = null
    private var oldLanguageCodes: List<String>? = null
    private var oldSelectedLanguage: String = ""
    private val recorderViewModelAction by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    fun showDialog(selectedLanguage: String) {
        DebugUtil.d(TAG, "showDialog")
        // 首先判断sp是否有语种列表，若没有，优先拿本地语种配置
        if (isSpLanguageSetNullOrEmpty) {
            DebugUtil.d(TAG, "showDialog spLanguageList is null getSupportLanguageList from local")
            val regionSupportedLanguages = LanguageUtil.getRegionSupportedLanguages()
            if (regionSupportedLanguages.isNotEmpty()) {
                val sortLanguageList = LanguageUtil.sortLanguageList(activity, regionSupportedLanguages.toList())
                handleShow(sortLanguageList, selectedLanguage)
                return
            }
        }
        val languageCodes = recorderViewModelAction?.getLangListFromCatchOrSp()
        DebugUtil.d(TAG, "showDialog getSupportLanguageList from cache or sp languageCodes=$languageCodes")
        if (languageCodes.isNullOrEmpty()) {
            DebugUtil.d(TAG, "showDialog langList is null or empty")
            return
        }
        val sortLanguageList = LanguageUtil.sortLanguageList(activity, languageCodes)
        handleShow(sortLanguageList, selectedLanguage)
    }

    private fun handleShow(languageCodes: List<String>, selectedLanguage: String) {
        oldLanguageCodes = languageCodes
        oldSelectedLanguage = selectedLanguage
        val languageNames = LanguageUtil.getAsrLangMap(activity, languageCodes).values.toTypedArray()
        DebugUtil.d(TAG, "showDialog languageNames size=${languageNames.size}")
        val checkboxStates = BooleanArray(languageCodes.size) { false }
        val disableStatus = BooleanArray(languageCodes.size) { false }

        val selectedPos = languageCodes.indexOf(selectedLanguage).coerceAtLeast(0)
        DebugUtil.d(TAG, "showDialog selectedPos=$selectedPos")
        checkboxStates[selectedPos] = true

        val singleChoiceListAdapter = ChoiceListAdapter(
            activity,
            com.support.dialog.R.layout.coui_select_dialog_singlechoice,
            languageNames,
            null,
            checkboxStates,
            disableStatus,
            false
        )
        activity.runOnUiThread {
            if (activity.isFinishing || activity.isDestroyed) {
                return@runOnUiThread
            }
            mDialog?.let {
                if (it.isShowing) {
                    DebugUtil.d(TAG, "showDialog dialog is showing, dismiss it")
                    it.dismiss()
                }
            }
            mDialog = COUIAlertDialogBuilder(
                activity,
                com.support.dialog.R.style.COUIAlertDialog_BottomAssignment
            )
                .setBlurBackgroundDrawable(true)
                .setTitle(activity.getString(com.soundrecorder.common.R.string.speech_language))
                .setAdapter(singleChoiceListAdapter) { _, which ->
                    dialogItemListener?.click(languageCodes[which])
                    mDialog?.dismiss()
                    mDialog = null
                }
                .setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
                .show()
            ViewUtils.updateWindowLayoutParams(mDialog?.window)
        }
    }

    /**
     * 比较语言列表是否发生变化
     */
    fun compareLanguageListChange(newList: List<String>): Boolean {
        var isChange = false
        oldLanguageCodes?.let { oldList ->
            if (oldList.size != newList.size) {
                isChange = true
                return@let
            }
            oldList.forEach { oldItem ->
                if (!newList.contains(oldItem)) {
                    isChange = true
                    return@let
                }
            }
        }
        return isChange
    }

    /**
     * 比较选择的语言是否发生变化
     */
    fun compareSelectedLanguageChange(newSelectedLanguage: String): Boolean {
        return oldSelectedLanguage != newSelectedLanguage
    }

    fun isShowing(): Boolean {
        return mDialog?.isShowing == true
    }

    fun release() {
        mDialog?.dismiss()
        dialogItemListener = null
        mDialog = null
    }

    interface DialogItemClickListener {
        fun click(languageCode: String)
    }
}
