/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : LargeModelInfoActivity
 ** Description :
 ** Version     : 1.0
 ** Date        : 2025/06/19
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  keweiwei     2025/06/19       1.0      create
 ***********************************************************************/
package com.soundrecorder.setting.modelinfo

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.widget.TextView
import com.coui.appcompat.toolbar.COUIToolbar
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.StatusBarUtil
import com.soundrecorder.common.flexible.FollowHandPanelUtils
import com.soundrecorder.setting.R
import com.soundrecorder.setting.databinding.ActivityModelinfoBinding

class LargeModelInfoActivity : BaseActivity() {

    companion object {
        private const val TAG = "LargeModelInfoActivity"
        private const val MODEL_NAME = "Deepseek-V3"
        private const val RECORD_NUMBER = "网信算备110108970550101240011号"

        fun start(activity: Activity?) {
            if (activity == null) {
                DebugUtil.d(TAG, "start: activity = null return")
                return
            }
            val intent = Intent(activity, LargeModelInfoActivity::class.java)
            FollowHandPanelUtils.startActivity(activity, intent)
        }
    }

    private var viewBinding: ActivityModelinfoBinding? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewBinding = ActivityModelinfoBinding.inflate(layoutInflater).apply {
            setContentView(this.root)
        }
        initView()
    }

    private fun initView() {
        findViewById<TextView>(R.id.model_name)?.apply {
            text = String.format(resources.getString(com.soundrecorder.common.R.string.model_name), MODEL_NAME)
        }
        findViewById<TextView>(R.id.record_number)?.apply {
            text = String.format(resources.getString(com.soundrecorder.common.R.string.record_number), RECORD_NUMBER)
        }
        initActionBar()
    }

    private fun initActionBar() {
        val toolbar: COUIToolbar? = findViewById(R.id.toolbar)
        val appBarLayout: COUIDividerAppBarLayout? = findViewById(R.id.appbar)
        toolbar?.title = resources.getString(com.soundrecorder.common.R.string.setting_big_model_info)
        toolbar?.isTitleCenterStyle = false
        setSupportActionBar(toolbar)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        var appBarTopPadding =
            StatusBarUtil.getStatusBarHeight(this) + resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp10)
        if (FollowHandPanelUtils.isAddOnSupportFollowPanel() && ScreenUtil.isSmallScreen(this).not() && isInMultiWindowMode.not()) {
            appBarTopPadding = resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp16)
        }
        appBarLayout?.setPadding(0, appBarTopPadding, 0, 0)
        FollowHandPanelUtils.checkActivityClickOutOfBounds(this, viewBinding?.rootView)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (ClickUtils.isFastDoubleClick()) {
            DebugUtil.e(TAG, "onOptionsItemSelected() isFastDoubleClick return")
            return false
        }
        when (item.itemId) {
            android.R.id.home -> {
                setResult(RESULT_CANCELED)
                finish()
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onDestroy() {
        super.onDestroy()
        viewBinding = null
    }
}