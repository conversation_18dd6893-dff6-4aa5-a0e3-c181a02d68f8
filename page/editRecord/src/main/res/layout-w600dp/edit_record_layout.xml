<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="SpUsage,UnusedAttribute,ContentDescription">

    <data>

        <import type="android.view.View" />

        <variable
            name="editViewModel"
            type="com.soundrecorder.editrecord.ui.EditViewModel" />

        <variable
            name="recordControlOnClickListener"
            type="View.OnClickListener" />

    </data>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/body"
        android:clipChildren="false"
        android:clipToPadding="false"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/abl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/common_background_color"
            app:elevation="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/edit_recorder_toolbar_height"
                android:layout_marginTop="@dimen/edit_recorder_toolbar_margin_top" />

        </com.google.android.material.appbar.AppBarLayout>

        <RelativeLayout
            android:id="@+id/color_load_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/common_background_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/abl"
            tools:visibility="gone">

            <TextView
                android:id="@+id/loadingTip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:text="@string/oplus_loading_dialog_text_view" />

            <com.coui.appcompat.progressbar.COUILoadingView
                style="?attr/couiLoadingViewLargeStyle"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@+id/loadingTip"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="10dp" />
        </RelativeLayout>

        <com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout
            android:id="@+id/wave_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="@dimen/common_wave_view_margin_top"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:transitionName="recorderTop"
            app:backgroundWhole="@color/wave_recycler_background"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHeight_percent="@dimen/edit_wave_view_height_percent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/abl">

            <com.soundrecorder.editrecord.views.wave.EditWaveRecyclerView
                android:id="@+id/ruler_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:layoutDirection="ltr"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout>

        <View
            android:id="@+id/view_center_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/hover_state_center_height"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <LinearLayout
            android:id="@+id/anim_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/common_time_area_margin_horizontal"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="@dimen/edit_title_padding_vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/wave_layout">

            <TextView
                android:id="@+id/timerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="sys-sans-en"
                android:fontFeatureSettings="tnum"
                android:gravity="center"
                android:lines="1"
                android:padding="0dp"
                android:textAppearance="@style/couiTextAppearanceDisplayM"
                android:textColor="@color/coui_color_primary_neutral"
                android:textFontWeight="600"
                android:textSize="@dimen/common_max_time_text_size"
                tools:text="00:11:22" />

            <TextView
                android:id="@+id/record_name"
                style="@style/couiTextAppearanceHeadline6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:gravity="center"
                android:includeFontPadding="false"
                android:lines="1"
                android:textColor="@color/coui_color_primary_neutral"
                android:textSize="@dimen/common_name_text_size"
                tools:text="@string/standard_mode" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_marklist"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/common_mark_list_margin_horizontal_small_window"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/edit_mark_list_footer_divider"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/anim_title"
            app:layout_constraintWidth_percent="@dimen/screen_width_percent">

            <androidx.recyclerview.widget.COUIRecyclerView
                android:id="@+id/mark_listview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:divider="@null"
                tools:listitem="@layout/item_mark_list" />
        </LinearLayout>

        <View
            android:id="@+id/edit_mark_list_footer_divider"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="?attr/couiColorDivider"
            android:visibility="invisible"
            app:layout_constraintBottom_toTopOf="@id/preViewBar_and_cutLayout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/layout_marklist" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/preViewBar_and_cutLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/edit_preview_bar_padding_horizontal"
            android:paddingTop="@dimen/mark_area_padding_vertical"
            android:paddingBottom="@dimen/dp8"
            app:layout_constraintBottom_toTopOf="@id/middle_control"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintWidth_percent="@dimen/screen_width_percent">

            <com.soundrecorder.editrecord.views.preview.GloblePreViewBar
                android:id="@+id/preViewBar"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp44"
                android:forceDarkAllowed="false"
                app:amplitude_color="?attr/couiColorLabelPrimary"
                app:cut_line_color="?attr/couiColorAzureVariant"
                app:cut_zone_color="@color/edit_previewbar_cutzone_color"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:point_color="?attr/couiColorPrimary" />

            <View
                android:id="@+id/space_cut"
                android:layout_width="match_parent"
                android:layout_height="@dimen/edit_set_start_and_end_top_margin_preview_bar"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/preViewBar" />

            <TextView
                android:id="@+id/current_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="sys-sans-en"
                android:fontFeatureSettings="tnum"
                android:textColor="@color/coui_color_secondary_neutral"
                android:textFontWeight="500"
                android:textSize="@dimen/dp12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/space_cut"
                tools:text="00:00" />

            <TextView
                android:id="@+id/cut_total_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="sys-sans-en"
                android:fontFeatureSettings="tnum"
                android:textColor="@color/coui_color_secondary_neutral"
                android:textFontWeight="500"
                android:textSize="@dimen/dp12"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/space_cut"
                tools:text="00:00" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.soundrecorder.common.widget.glow.ShadowImageView
            android:layout_width="@dimen/circle_record_button_diam"
            android:layout_height="@dimen/circle_record_button_diam"
            android:background="@color/color_transparent"
            app:shadow_blur_radius="@dimen/dp28"
            app:layout_constraintTop_toTopOf="@id/middle_control"
            app:layout_constraintBottom_toBottomOf="@id/middle_control"
            app:layout_constraintStart_toStartOf="@id/middle_control"
            app:layout_constraintEnd_toEndOf="@id/middle_control"
            app:shadow_offsetY="@dimen/dp14"
            app:shadow_offsetX="@dimen/dp0"
            app:shadow_reference="@id/red_circle_icon"
            app:shadow_color="@color/record_btn_shadow_red_color"/>

        <RelativeLayout
            android:id="@+id/middle_control"
            android:layout_width="@dimen/circle_record_button_diam"
            android:layout_height="@dimen/circle_record_button_diam"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginBottom="@dimen/circle_record_button_margin_bottom"
            android:importantForAccessibility="no"
            android:transitionName="sharedControlView"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@id/preViewBar_and_cutLayout"
            app:layout_constraintStart_toStartOf="@id/preViewBar_and_cutLayout">

            <com.soundrecorder.common.widget.AnimatedCircleButton
                android:id="@+id/red_circle_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:onClick="@{recordControlOnClickListener}"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_play_icon"
                android:transitionName="sharedRedCircleView"
                android:forceDarkAllowed="false"
                android:translationZ="@dimen/dp0"
                app:vibrate_toggle="true"
                app:state_change="false"
                app:glow_enable="true"
                app:reference_id="@id/red_circle_icon_reference"
                app:circle_color="?attr/couiColorContainerTheme"
                app:circle_gradient_start_color="@color/record_btn_gradient_start_color"
                app:circle_gradient_end_color="@color/record_btn_gradient_end_color"
                app:circle_radius="@dimen/circle_record_button_radius" />

            <ImageButton
                android:id="@+id/red_circle_icon_reference"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:forceDarkAllowed="false"
                android:clickable="false"
                android:background="@color/color_transparent"
                android:translationZ="@dimen/dp1"/>

        </RelativeLayout>

        <LinearLayout
            android:id="@+id/remove"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_horizontal"
            android:layout_marginEnd="@dimen/edit_delete_extract_margin_start_margin_edn"
            android:layout_toStartOf="@id/middle_control"
            android:contentDescription="@string/edit_remove"
            android:gravity="center"
            android:onClick="@{editViewModel.editClickListener}"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/middle_control"
            app:layout_constraintEnd_toStartOf="@id/middle_control"
            app:layout_constraintTop_toTopOf="@id/middle_control">

            <ImageView
                android:id="@+id/remove_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp17"
                android:enabled="false"
                android:src="@drawable/selector_button_edit_remove"
                tools:ignore="ContentDescription" />

            <TextView
                android:id="@+id/remove_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:ellipsize="end"
                android:enabled="false"
                android:fontFamily="sans-serif-medium"
                android:gravity="top|center_horizontal"
                android:maxLines="1"
                android:paddingTop="@dimen/dp8"
                android:text="@string/edit_remove"
                android:textColor="@drawable/selector_color_edit"
                android:textSize="@dimen/dp12" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/extract"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="@dimen/edit_delete_extract_margin_start_margin_edn"
            android:layout_toEndOf="@id/middle_control"
            android:contentDescription="@string/extract"
            android:gravity="center"
            android:onClick="@{editViewModel.editClickListener}"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="@id/middle_control"
            app:layout_constraintStart_toEndOf="@id/middle_control"
            app:layout_constraintTop_toTopOf="@id/middle_control">

            <ImageView
                android:id="@+id/extract_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/dp17"
                android:enabled="false"
                android:src="@drawable/selector_button_edit_trim" />

            <TextView
                android:id="@+id/extract_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:ellipsize="end"
                android:enabled="false"
                android:fontFamily="sans-serif-medium"
                android:gravity="top|center_horizontal"
                android:maxLines="1"
                android:paddingTop="@dimen/dp8"
                android:text="@string/extract"
                android:textColor="@drawable/selector_color_edit"
                android:textSize="@dimen/dp12" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>