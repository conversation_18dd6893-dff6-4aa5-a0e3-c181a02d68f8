/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditRecordApi
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: <PERSON><PERSON>Ren
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord

import android.os.Bundle
import com.soundrecorder.base.ext.replaceFragment
import com.soundrecorder.common.permission.PermissionActivity
import com.soundrecorder.editrecord.ui.EditRecordFragment

class EditRecordActivity : PermissionActivity() {
    companion object {
        private const val TAG = "EditRecordActivity"

        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "Edit"
    }

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.layout_edit)

        if (savedInstanceState == null) {
            replaceFragment(R.id.fl_content, EditRecordFragment::class.java, this.intent.extras)
        }
    }

    override fun getFunctionName(): String {
        return FUNCTION_NAME
    }

    override fun userChange() {
        fragmentUserChange()
        super.userChange()
    }

    override fun navigationBarColor(): Int {
        return com.support.appcompat.R.color.coui_color_white
    }

    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.revert_exit_top, R.anim.revert_enter_bottom)
    }

    override fun onFoldStateChanged(state: Int) {
        super.onFoldStateChanged(state)
        fragmentStateChanged(state)
    }
}