/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForEditRecord.kt
 * * Description : AutoDiForEditRecord
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.editrecord.di

import com.soundrecorder.editrecord.EditRecordApi
import com.soundrecorder.modulerouter.EditRecordInterface
import org.koin.dsl.module

object AutoDiForEditRecord {
    val editRecordModule = module {
        single<EditRecordInterface>(createdAtStart = true) {
            EditRecordApi
        }
    }
}