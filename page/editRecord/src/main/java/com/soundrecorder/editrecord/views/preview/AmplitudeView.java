/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AmplitudeView
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: ********(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/2 1.0 create
 */

// OPLUS Java File Skip Rule:MethodLength
package com.soundrecorder.editrecord.views.preview;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.View;
import android.view.ViewParent;

import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.NumberConstant;

import java.util.Collections;
import java.util.List;

import static com.soundrecorder.common.utils.amplitude.AmpSeekbarUtil.PERCENT_80;
import static com.soundrecorder.editrecord.views.preview.GloblePreViewBar.NATURE_COUNT;

class AmplitudeView extends View {
    private static final String TAG = "AmplitudeView";
    private static final float NUMBER_TWO_FLOAT = 2F;

    private Paint mPaint;
    private Paint mUnSelectedPaint;
    private float mWaveDrawHeight;
    private float mVerticalPadding;

    public AmplitudeView(Context context) {
        super(context);
        try {
            if (BaseUtil.isAndroidQOrLater()) {
                boolean isForceDarkAllowed = isForceDarkAllowed();
                DebugUtil.i(TAG, "isForceDarkAllowed: " + isForceDarkAllowed);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "isForceDarkAllowed()", e);
        }
        mUnSelectedPaint = new Paint();
        mUnSelectedPaint.setColor(getResources().getColor(com.support.appcompat.R.color.coui_color_label_tertiary, context.getTheme()));
        mUnSelectedPaint.setAntiAlias(true);
        mUnSelectedPaint.setDither(true);
    }

    public void setListener() {
        GloblePreViewBar previewBar = getPreViewBar();
        if (previewBar != null) {
            previewBar.setListenter(new GloblePreViewBar.PointMoveListener() {
                @Override
                public void onTouchDownOnBar(int direction, long time) {
                    // Do nothing
                }
                @Override
                public void onMoveOnDragBar(int direction, long time) {
                    postInvalidateOnAnimation();
                }
                @Override
                public void onTouchUpOnBar(int direction, long time) {
                    // Do nothing
                }
                @Override
                public void onTouchDownMiddleBar() {
                    // Do nothing
                }
                @Override
                public void onMoveOnMiddleBar(long time) {
                    postInvalidateOnAnimation();
                }
                @Override
                public void onTouchUpMiddleBar(long time) {
                    // Do nothing
                }
            });
        }
    }

    public void setPaint(Paint paint) {
        this.mPaint = paint;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        mWaveDrawHeight = h * PERCENT_80;
        mVerticalPadding = (h - mWaveDrawHeight) / NUMBER_TWO_FLOAT;
        DebugUtil.d(TAG, "onSizeChanged, mWaveDrawHeight=" + mWaveDrawHeight + ", mVerticalPadding=" + mVerticalPadding);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        drawAmplitude(canvas);
        super.onDraw(canvas);
    }

    private GloblePreViewBar getPreViewBar() {
        ViewParent parent = getParent();
        GloblePreViewBar preViewBar = null;
        if (parent instanceof GloblePreViewBar) {
            preViewBar = (GloblePreViewBar) parent;
        }
        return preViewBar;
    }

    private void drawAmplitude(Canvas canvas) {
        GloblePreViewBar preViewBar = getPreViewBar();
        if (preViewBar == null) {
            return;
        }

        List<Integer> amplitudes = preViewBar.getAmplitudes();
        int size = 0;
        if ((amplitudes == null) || ((size = amplitudes.size()) != NATURE_COUNT)) {
            DebugUtil.e(TAG, "drawAmplitude amplitudes size is not correct, size=" + size);
            return;
        }
        float ampWidth = preViewBar.getAmplitudeWidth();
        int minAmplitudeHeight = (int) ampWidth;
        int maxAmp = Collections.max(amplitudes);
        maxAmp = (maxAmp == 0) ? minAmplitudeHeight : maxAmp;
        float oneLineHeightScale = mWaveDrawHeight / (float) maxAmp;
        int lenAmplitudes = amplitudes.size();
        boolean isRtl = preViewBar.getReverseLayout();

        for (int i = 0; i < lenAmplitudes; i++) {
            Integer preAmplitude = (i == 0) ? 0 : amplitudes.get(i - 1);
            Integer curAmplitude = amplitudes.get(i);
            int amplitude = (preAmplitude + curAmplitude) / NumberConstant.NUM_2;
            float startY = preViewBar.getStartYByHeight(amplitude * oneLineHeightScale);
            float endY = preViewBar.getEndYByHeight(amplitude * oneLineHeightScale);
            float startX = preViewBar.addFixWidth() + ampWidth * i * NumberConstant.NUM_2;
            float endX = startX + ampWidth;
            if (endY - startY < minAmplitudeHeight) {
                startY -= minAmplitudeHeight / NUMBER_TWO_FLOAT;
                endY += minAmplitudeHeight / NUMBER_TWO_FLOAT;
            }

            // RTL模式下,波形图支持从右往左滚动
            if (isRtl) {
                startX = getWidth() - startX;
                endX = getWidth() - endX;
            }

            if (startX >= preViewBar.getDragBarStart() && endX <= preViewBar.getDragBarEnd()) {
                canvas.drawRoundRect(startX, startY, endX, endY, ampWidth, ampWidth, mPaint);
            } else {
                canvas.drawRoundRect(startX, startY, endX, endY, ampWidth, ampWidth, mUnSelectedPaint);
            }
            canvas.save();
        }
    }

    public float getWaveDrawHeight() {
        return mWaveDrawHeight;
    }

    public float getVerticalPadding() {
        return mVerticalPadding;
    }
}
