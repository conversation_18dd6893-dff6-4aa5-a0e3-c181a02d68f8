/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: DialogDismissListener
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.ui

import android.content.DialogInterface
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.ext.suffix
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.utils.sound.DeleteSoundEffectManager
import com.soundrecorder.editrecord.ClipRecord
import com.soundrecorder.editrecord.ClipResultCode
import com.soundrecorder.editrecord.utils.EditViewUtils


class DialogDismissListener(private var mFragment: EditRecordFragment?) : DialogInterface.OnDismissListener {
    override fun onDismiss(dialogInterface: DialogInterface?) {
        if (mFragment == null) {
            DebugUtil.d(TAG, "DialogDissmissListener mActivity is null so return ")
            return
        }
        if (mFragment?.mEditViewModel?.mClickCancel == true) {
            DebugUtil.e(TAG, "onDismiss clickCancel")
            mFragment?.mEditViewModel?.mClickCancel = false
            return
        }

        val clipRecord = mFragment?.mEditViewModel?.clipTask?.clipRecord
        if (clipRecord == null) {
            DebugUtil.e(TAG, "getClipRecord is null ,clip failed.")
            return
        }

        val clipCode = clipRecord.clipCode
        DebugUtil.i(TAG, "clip result code is $clipCode")
        if (!ClipResultCode.isClipSuccess(clipCode)) {
            ToastManager.showShortToast(mFragment?.context, com.soundrecorder.common.R.string.clip_fail_tips)
            return
        }

        if (mFragment?.mEditViewModel?.clipTask?.opType == ClipRecord.OP_DELETE) {
            DeleteSoundEffectManager.getInstance().playDeleteSound()
        }
        if (MediaDBUtils.genUri(mFragment?.mEditViewModel?.recordId
                ?: -1) != (mFragment?.mEditViewModel?.playerController?.getPlayUri()
                ?: "")
        ) {
            MediaDBUtils.delete(mFragment?.mEditViewModel?.playerController?.getPlayUri())
        }
        DebugUtil.i(TAG, "onDismiss: mPlayUri: " + mFragment?.mEditViewModel?.playerController?.getPlayUri() + ", newPlayUri: " + clipRecord.clipUri)
        mFragment?.mEditViewModel?.playerController?.setPlayUri(clipRecord.clipUri)
        if (clipRecord.clipPath != null) {
            mFragment?.mEditViewModel?.playPath = clipRecord.clipPath
        }
        if (mFragment?.mEditViewModel?.playerController?.getPlayUri() == null) {
            DebugUtil.e(TAG, " getClipUri is null ,clip failed.")
            return
        }
        if (clipRecord.clipAmplitudes != null) {
            mFragment?.mEditViewModel?.ampList?.value = clipRecord.clipAmplitudes
        }
        mFragment?.mEditViewModel?.markDataList?.value = clipRecord.clipMarkList
        if (mFragment?.mEditViewModel?.playerController?.prepareToPlay(mFragment?.mEditViewModel?.playerController?.getPlayUri()) == true) {
            mFragment?.mEditViewModel?.correctAmplitudeList(
                mFragment?.mEditViewModel?.ampList?.value
                    ?: ArrayList()
            )
            mFragment?.mEditViewModel?.correctMarkData(mFragment?.mEditViewModel?.markDataList?.value)
            mFragment?.mEditViewModel?.playerController?.setOCurrentTimeMillis(
                EditViewUtils.clipLastTime(
                    mFragment?.mEditViewModel?.playerController?.lastPlayProgressTime ?: 0,
                    mFragment?.mEditViewModel?.clipTask?.opType == ClipRecord.OP_DELETE,
                    mFragment?.mEditViewModel,
                    mFragment?.dataBindUtil
                )
            )
            mFragment?.mEditViewModel?.playerController?.setLastPlayTime(
                EditViewUtils.clipLastTime(
                    mFragment?.mEditViewModel?.playerController?.lastPlayProgressTime ?: 0,
                    mFragment?.mEditViewModel?.clipTask?.opType == ClipRecord.OP_DELETE,
                    mFragment?.mEditViewModel,
                    mFragment?.dataBindUtil
                )
            )

            setTime()

            mFragment?.dataBindUtil?.preViewBar?.resetStatus()
            mFragment?.mEditViewModel?.playerController?.currentTimeMillis?.value = 0
            mFragment?.dataBindUtil?.rulerView?.let {
                it.setSelectTime(
                    mFragment?.mEditViewModel?.playerController?.currentTimeMillis?.value ?: 0
                )
                it.setMarkTimeList(mFragment?.mEditViewModel?.markDataList?.value)
                it.setAmplitudeList(
                    mFragment?.mEditViewModel?.ampList?.value
                        ?: ArrayList()
                )
            }
            mFragment?.mMarkListAdapter?.setData(mFragment?.mEditViewModel?.markDataList?.value)
            setClipTitle()
            mFragment?.dataBindUtil?.preViewBar?.let {
                it.resetPreTime()
                it.setPointTime(
                    mFragment?.mEditViewModel?.playerController?.currentTimeMillis?.value
                        ?: 0, true
                )
                // 裁剪后需重新计算90段振幅波形
                mFragment?.mEditViewModel?.calculateAmp90AfterClip()
            }
            mFragment?.mEditViewModel?.isClipped?.value = true
            mFragment?.mEditViewModel?.playerController?.seekRelease()
            mFragment?.setCurrentTime(
                mFragment?.mEditViewModel?.playerController?.currentTimeMillis?.value ?: 0
            )
        }
        EditViewUtils.setOpEnable(false, mFragment?.dataBindUtil, mFragment?.mEditViewModel)
    }

    private fun setTime() {
        val endTime = mFragment?.mEditViewModel?.playerController?.getDuration() ?: 0
        DebugUtil.i(TAG, "onDismiss, after cut, endTime:$endTime")
        mFragment?.mEditViewModel?.cutStartTime?.value = 0
        mFragment?.mEditViewModel?.cutEndTime?.value = endTime
        mFragment?.setTotalTime(endTime)
    }

    private fun setClipTitle() {
        val currentPlayName = mFragment?.mEditViewModel?.playName?.value
        var clipName: String? = null
        val mediaRecord = MediaDBUtils.queryRecordByUri(mFragment?.mEditViewModel?.playerController?.getPlayUri())
        if (mediaRecord != null) {
            clipName = EditViewUtils.getClipName(mediaRecord, currentPlayName)
            mFragment?.mEditViewModel?.playName?.postValueSafe(clipName + currentPlayName.suffix())
        } else {
            clipName = currentPlayName.title()
        }
        mFragment?.dataBindUtil?.recordName?.text = clipName
        doTitleAnimation()
    }

    private fun doTitleAnimation() {
        mFragment?.checkNeedHideOtherView(true)
    }

    fun release() {
        if (mFragment != null) {
            mFragment = null
        }
    }

    companion object {
        private const val TAG = "DialogDismissListener"
    }
}