/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditRecordApi
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord

import android.content.Context
import android.content.Intent
import com.soundrecorder.modulerouter.EditRecordInterface

object EditRecordApi : EditRecordInterface {

    override fun createEditRecordIntent(context: Context): Intent {
        val jumpIntent = Intent()
        jumpIntent.setClass(context, EditRecordActivity::class.java)
        return jumpIntent
    }

    override fun isEditRecordActivity(context: Context): Boolean = context is EditRecordActivity

    override fun getEditRecordActivityClass(): Class<*> {
        return EditRecordActivity::class.java
    }
}