/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AbsRegion
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.views.region;

import static com.soundrecorder.editrecord.views.wave.EditWaveItemView.LEFT;
import static com.soundrecorder.editrecord.views.wave.EditWaveItemView.RIGHT;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.View;
import android.view.ViewParent;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.editrecord.R;
import com.soundrecorder.wavemark.wave.WaveViewUtil;

import com.soundrecorder.editrecord.views.wave.EditWaveRecyclerView;
import com.soundrecorder.editrecord.views.wave.WaveItemHandler;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

public abstract class AbsRegion implements IRegionStrategy {

    protected static final float NUM_TWO_F = 2f;
    private static final String TAG = "AbsRegion";

    protected final int mPointBackGroundRadius;
    private final float mPxPerMs;
    private final int mReadColor;
    private final int mHandlerColor;

    protected View mUseView = null;
    protected WaveItemHandler mHandler = null;
    protected float mHalfHeight = 0;

    private Context mContext = null;
    private Paint mHandlerPaint = null;
    private float mCutLineTop = 0;
    private float mCutLineBottom;
    /**
     * 顶部标记区域的高度
     */
    private float mMarkViewHeight;
    /**
     * 波形的高度
     */
    private float mViewHeight;

    public AbsRegion(TypedArray array, View view, WaveItemHandler handler) {
        this.mUseView = view;
        this.mHandler = handler;
        this.mContext = mUseView.getContext();
        Resources r = mContext.getResources();
        mMarkViewHeight = r.getDimensionPixelSize(com.soundrecorder.wavemark.R.dimen.record_wave_view_mark_area_height);

        mPointBackGroundRadius = r.getDimensionPixelSize(R.dimen.ab_play_point_background_radius);
        int timeLineGap = (int) WaveViewUtil.getLargeWaveTimeLineGap(view.getContext());
        mPxPerMs = (float) timeLineGap / WaveViewUtil.DURATION_INTERVAL;
        mHalfHeight = mViewHeight / WaveViewUtil.NUM_TWO + mMarkViewHeight / 2;

        mCutLineTop = mMarkViewHeight;
        mCutLineBottom = mViewHeight;

        mHandlerPaint = new Paint();
        mHandlerColor = this.mContext.getColor(R.color.edit_wave_handler_color);
        mHandlerPaint.setColor(mHandlerColor);
        mHandlerPaint.setAntiAlias(true);
        mHandlerPaint.setStyle(Paint.Style.FILL);

        mReadColor = getResources().getColor(R.color.edit_red_line);
    }

    /**
     * 更新波形高度数据，因为某些分屏场景下，波形高度发生变化，但是不会走AbsRegion的构造方法，所以高度不会刷新
     */
    @Override
    public void updateViewHeight() {
        if (mContext != null) {
            mMarkViewHeight = mContext.getResources().getDimensionPixelSize(com.soundrecorder.wavemark.R.dimen.record_wave_view_mark_area_height);
            mHalfHeight = mViewHeight / WaveViewUtil.NUM_TWO + mMarkViewHeight / 2;
            mCutLineTop = mMarkViewHeight;
            mCutLineBottom = mViewHeight;
        }
    }

    @Override
    public void setItemViewHeight(float height) {
        if (mViewHeight != height) {
            mViewHeight = height;
            mHalfHeight = mViewHeight / WaveViewUtil.NUM_TWO + mMarkViewHeight / 2;
            mCutLineBottom = mViewHeight;
        }
    }

    protected float getXByTime(Long time, int viewIndex) {
        float v = (time - (viewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION) * mPxPerMs;
        return v;
    }


    Context getContext() {
        return mContext;
    }


    public Resources getResources() {
        return mUseView.getResources();
    }

    public ViewParent getParent() {
        return mUseView.getParent();
    }

    protected boolean isReverseLayout() {
        ViewParent parent = getParent();
        if (parent instanceof RecyclerView) {
            RecyclerView.LayoutManager layoutManager = ((RecyclerView) parent).getLayoutManager();
            if (layoutManager instanceof LinearLayoutManager) {
                return ((LinearLayoutManager) layoutManager).getReverseLayout();
            }
        }
        return false;
    }


    public ViewParent getSecondParent() {
        ViewParent parent = null;
        ViewParent tmp = getParent();
        if (tmp != null) {
            parent = tmp.getParent();
        } else {
            DebugUtil.e(TAG, "getSecondParent:tmp = null");
            return null;
        }
        if (parent == null) {
            DebugUtil.e(TAG, "getSecondParent:parent == null");
            return null;
        }
        return parent;
    }

    /**
     * Because when drawing a item view, if the graph is still drawn in the previous item view,
     * it will be transparent to cross over to the next view, so the offset value method is provided.
     * When drawing a larger graph, the offset value can be processed separately
     *
     * @return Offset value when drawing item view
     */
    public float timeOffset() {
        return 0;
    }


    @Override
    public void drawStart(Canvas canvas, int viewIndex) {
        ViewParent parent = getParent();
        if (parent == null) {
            return;
        }
        if (parent instanceof EditWaveRecyclerView) {
            Long startRecord = ((EditWaveRecyclerView) parent).getStartRecord();
            if (viewIndex == 0) {
                return;
            }
            long endTime = viewIndex * WaveViewUtil.ONE_WAVE_VIEW_DURATION; //ms
            long startTime = (viewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION; //ms
            if ((startRecord == null) || (startRecord < 0)) {
                return;
            }

            if (startRecord > endTime) {
                return;
            }
            if ((startRecord >= (startTime - timeOffset())) && (startRecord < endTime)) {
                boolean flag = false;
                if (((EditWaveRecyclerView) parent).getHitStatus() == LEFT) {
                    mHandlerPaint.setColor(mReadColor);
                    flag = true;
                } else {
                    mHandlerPaint.setColor(mHandlerColor);
                }
                onDrawStart(canvas, startRecord, flag, viewIndex, mHandlerPaint);
            }
        }
    }

    @Override
    public void drawEnd(Canvas canvas, int viewIndex) {
        ViewParent parent = getParent();
        if (parent == null) {
            return;
        }
        if (parent instanceof EditWaveRecyclerView) {
            EditWaveRecyclerView tempParent = (EditWaveRecyclerView) parent;
            Long endRecord = tempParent.getEndRecord();
            if ((endRecord == null) || (endRecord < 0)) {
                return;
            }
            if (viewIndex == 0) {
                return;
            }
            long startTime = (viewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION; //ms
            long endTime = viewIndex * WaveViewUtil.ONE_WAVE_VIEW_DURATION; //ms
            if ((endRecord > (startTime - timeOffset())) && (endRecord <= endTime)) {
                boolean flag = false;
                if (tempParent.getHitStatus() == RIGHT) {
                    flag = true;
                    mHandlerPaint.setColor(mReadColor);
                } else {
                    mHandlerPaint.setColor(mHandlerColor);
                }
                onDrawEnd(canvas, endRecord, flag, viewIndex, mHandlerPaint);

            }
        }

    }

    protected void drawLine(Canvas canvas, Long time, int viewIndex, Paint handlerPaint) {
        float dimension = getContext().getResources().getDimension(com.soundrecorder.common.R.dimen.dp1);
        float xByTime = getXByTime(time, viewIndex);
        float newXByTime = xByTime;
        if (isReverseLayout()) {
            newXByTime = mUseView.getWidth() - xByTime;
        }
        canvas.drawRect(newXByTime, mCutLineTop,
                newXByTime + dimension, mCutLineBottom, handlerPaint);
        canvas.save();
    }


    /**
     * draw high light zone of cut zone
     *
     * @param canvas canvas
     */
    public void drawZone(Canvas canvas, int viewIndex) {
        if (viewIndex == 0) {
            return;
        }
        ViewParent parent = getParent();
        if (parent == null) {
            return;
        }
        if (parent instanceof EditWaveRecyclerView) {
            EditWaveRecyclerView tempParent = (EditWaveRecyclerView) parent;
            Long startRecord = tempParent.getStartRecord();
            Long endRecord = tempParent.getEndRecord();
            long startTime = (viewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION; //ms
            long endTime = viewIndex * WaveViewUtil.ONE_WAVE_VIEW_DURATION; //ms
            long currentTimeMillis = tempParent.getSlideTime(TAG);
            mHandlerPaint.setColor(getContext().getResources().getColor(R.color.edit_waveview_cutzone_color));
            if (startRecord != null) {
                if ((endRecord != null) && (endRecord < startTime)) {
                    return;
                }
                if ((endRecord == null) && (currentTimeMillis < startTime)) {
                    return;
                }
                if (endRecord == null) {
                    return;
                }
                if (startRecord < endTime) {
                    float startX = (startRecord < startTime) ? getXByTime(startTime, viewIndex) : getXByTime(startRecord, viewIndex);
                    float endX = (endRecord > endTime) ? getXByTime(endTime, viewIndex) : getXByTime(endRecord, viewIndex);
                    if (isReverseLayout()) {
                        startX = canvas.getWidth() - startX;
                        endX = canvas.getWidth() - endX;
                    }
                    canvas.drawRect(startX, mCutLineTop,
                            endX, mCutLineBottom, mHandlerPaint);
                }
            }
            canvas.save();
        }
    }

    /**
     *onDrawStart
     * @param canvas 画布
     * @param time 时间
     * @param isAdd 是否添加
     * @param viewIndex index
     * @param handPaint 画笔
     */
    protected abstract void onDrawStart(Canvas canvas, Long time, boolean isAdd, int viewIndex, Paint handPaint);

    /**
     *onDrawEnd
     * @param canvas 画布
     * @param time 时间
     * @param isAdd 是否添加
     * @param viewIndex index
     * @param handPaint 画笔
     */
    protected abstract void onDrawEnd(Canvas canvas, Long time, boolean isAdd, int viewIndex, Paint handPaint);

}
