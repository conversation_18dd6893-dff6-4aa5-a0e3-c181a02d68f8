/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditRecordFragmentTest
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.ui;

import android.app.Dialog;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Build;

import androidx.appcompat.app.AlertDialog;
import androidx.lifecycle.MutableLiveData;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.utils.sound.DeleteSoundEffectManager;
import com.soundrecorder.editrecord.ClipRecord;
import com.soundrecorder.editrecord.ClipResultCode;
import com.soundrecorder.editrecord.ClipTask;
import com.soundrecorder.editrecord.EditRecordActivity;
import com.soundrecorder.editrecord.R;
import com.soundrecorder.editrecord.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption;
import com.soundrecorder.editrecord.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.editrecord.views.preview.GloblePreViewBar;

import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowDialog;

import java.util.ArrayList;
import java.util.List;

import static com.soundrecorder.common.utils.MarkSerializUtil.VERSION_NEW;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.robolectric.Shadows.shadowOf;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class,
        ShadowFeatureOption.class, ShadowCOUIVersionUtil.class})
public class EditRecordFragmentTest {

    private ActivityController<EditRecordActivity> mController;
    private EditRecordActivity mActivity;
    private EditRecordFragment mFragment;

    @Before
    public void setUp() {
        mController = Robolectric.buildActivity(EditRecordActivity.class);
        mActivity = mController.create().resume().get();
        mFragment = (EditRecordFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
    }

    @After
    public void tearDown() {
        mFragment = null;
        mActivity = null;
        mController = null;
    }

    @Test
    public void should_init_when_onCreateView() {
        assertNotNull(mFragment);
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_content), null);
        List<ShadowApplication.Wrapper> registeredReceivers = shadowOf(RuntimeEnvironment.application).getRegisteredReceivers();
        List<String> intentFilters = new ArrayList<>();
        for(ShadowApplication.Wrapper wrapper: registeredReceivers) {
            IntentFilter intentFilter = wrapper.getIntentFilter();
            for(int i = 0; i < intentFilter.countActions(); i++) {
                intentFilters.add(intentFilter.getAction(i));
            }
        }
//        assertTrue(intentFilters.contains(Intent.ACTION_USER_BACKGROUND));
//        assertTrue(intentFilters.contains(Intent.ACTION_DATE_CHANGED));
//        assertTrue(intentFilters.contains(Intent.ACTION_TIME_CHANGED));
    }

    @Test
    @Ignore
    public void should_release_when_onDestroy() {
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_content), null);
        mFragment.onDestroy();
        List<ShadowApplication.Wrapper> registeredReceivers = shadowOf(RuntimeEnvironment.application).getRegisteredReceivers();
        List<String> intentFilters = new ArrayList<>();
        for (ShadowApplication.Wrapper wrapper : registeredReceivers) {
            IntentFilter intentFilter = wrapper.getIntentFilter();
            for (int i = 0; i < intentFilter.countActions(); i++) {
                intentFilters.add(intentFilter.getAction(i));
            }
        }
        assertFalse(intentFilters.contains(Intent.ACTION_USER_BACKGROUND));
        assertFalse(intentFilters.contains(Intent.ACTION_DATE_CHANGED));
        assertFalse(intentFilters.contains(Intent.ACTION_TIME_CHANGED));
    }

    @Test
    public void should_doUserChange_when_UserChangeReceiver_receive() {
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_content), null);
        Intent broadcast = new Intent(Intent.ACTION_USER_BACKGROUND);
        mActivity.sendBroadcast(broadcast);
//        assertTrue(mActivity.isFinishing());
    }

    @Test
    public void should_refreshUI_when_onAmpLoadingFinish() {
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_content), null);
        EditViewModel editViewModel = mFragment.getMEditViewModel();
        assertNotNull(editViewModel);
        editViewModel.getPlayerController().setDuration(10000L);
        editViewModel.getPlayerController().setOCurrentTimeMillis(1000L);
//        Whitebox.invokeMethod(mFragment, "onAmpLoadingFinish");
//        assertEquals(10000L, mFragment.getMWaveAdapter().getTotalTime());
    }

    @Test
    public void should_showDialog_when_createCancelClipDialog() throws Exception {
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_content), null);
        EditViewModel editViewModel = mFragment.getMEditViewModel();
        assertNotNull(editViewModel);
        editViewModel.isClipped().postValue(true);
        editViewModel.setCacheSaveDialogName("cacheSaveName");
        ArrayList<MarkDataBean> markDataBeanList = new ArrayList<>();
        markDataBeanList.add(new MarkDataBean(1000L, VERSION_NEW));
        markDataBeanList.add(new MarkDataBean(5000L, VERSION_NEW));
        MutableLiveData<ArrayList<MarkDataBean>> markDataList = new MutableLiveData<>(markDataBeanList);
        editViewModel.setMarkDataList(markDataList);

        Whitebox.invokeMethod(mFragment, "createCancelClipDialog");
        Dialog latestDialog = ShadowDialog.getLatestDialog();
        assertNull(latestDialog);
        assertFalse(mActivity.isFinishing());

        editViewModel.getPlayName().setValue("111");
        editViewModel.isClipped().setValue(true);
        Whitebox.invokeMethod(mFragment, "createCancelClipDialog");
        latestDialog = ShadowDialog.getLatestDialog();
        assertNotNull(latestDialog);
        assertTrue(latestDialog instanceof AlertDialog);

        editViewModel.isClipped().postValue(false);
        Whitebox.invokeMethod(mFragment, "createCancelClipDialog");
        ShadowDialog.reset();
        latestDialog = ShadowDialog.getLatestDialog();
        assertNull(latestDialog);
    }

    @Test
    public void should_response_when_onDismiss_with_DialogDissmissListener() {
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_content), null);
        EditViewModel editViewModel = mFragment.getMEditViewModel();
        assertNotNull(editViewModel);
        DialogInterface dialogInterface = mock(DialogInterface.class);

        DialogDismissListener dialogDismissListener = new DialogDismissListener(null);
        dialogDismissListener.onDismiss(dialogInterface);
        assertNull(editViewModel.getPlayPath());

        dialogDismissListener = new DialogDismissListener(mFragment);
        dialogDismissListener.onDismiss(dialogInterface);
        assertNull(editViewModel.getPlayPath());

        ClipTask clipTask = mock(ClipTask.class);
        editViewModel.setClipTask(clipTask);
        ClipRecord clipRecord = mock(ClipRecord.class);
        Uri clipUri = mock(Uri.class);
        doReturn(clipUri).when(clipRecord).getClipUri();
        doReturn(clipRecord).when(clipTask).getClipRecord();
        doReturn(ClipResultCode.CLIP_SUCCESS).when(clipRecord).getClipCode();
        doReturn(ClipRecord.OP_DELETE).when(clipTask).getOpType();
        DeleteSoundEffectManager instance = mock(DeleteSoundEffectManager.class);
        MockedStatic<DeleteSoundEffectManager> mockedStatic = mockStatic(DeleteSoundEffectManager.class);
        mockedStatic.when(DeleteSoundEffectManager::getInstance).thenReturn(instance);
        dialogDismissListener.onDismiss(dialogInterface);
        verify(instance).playDeleteSound();

        doReturn("clipPath").when(clipRecord).getClipPath();
        List<Integer> amplitudes = new ArrayList<>();
        doReturn(amplitudes).when(clipRecord).getClipAmplitudes();
        ArrayList<MarkDataBean>  clipMarkList = new ArrayList<>();
        clipMarkList.add(new MarkDataBean(1000L, VERSION_NEW));
        doReturn(clipMarkList).when(clipRecord).getClipMarkList();
        dialogDismissListener.onDismiss(dialogInterface);
        assertEquals(clipUri, editViewModel.getPlayerController().getPlayUri());
        assertEquals("clipPath", editViewModel.getPlayPath());
        assertEquals(amplitudes, editViewModel.getAmpList().getValue());
        assertEquals(clipMarkList, editViewModel.getMarkDataList().getValue());
        mockedStatic.close();
    }

    @Test
    public void should_null_when_release_on_DialogDismissListener() {
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_content), null);
        DialogDismissListener dialogDismissListener = new DialogDismissListener(mFragment);
        dialogDismissListener.release();
        assertNull(Whitebox.getInternalState(dialogDismissListener, "mFragment"));
    }

    @Test
    public void should_correct_when_onTouchDownDragBar() {
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_content), null);
        EditViewModel editViewModel = mFragment.getMEditViewModel();
        assertNotNull(editViewModel);
        editViewModel.getCutStartTime().setValue(0L);
        editViewModel.getCutEndTime().setValue(3000L);
        GloblePreViewBar.PointMoveListener dragBarListener = Whitebox.getInternalState(mFragment, "mPointMoveListener");
        assertNotNull(dragBarListener);
        long cutEndTimeBeforeTouchDownDragBar = Whitebox.getInternalState(mFragment, "mCutEndTimeBeforeTouchDownDragBar");
        assertEquals(0L, cutEndTimeBeforeTouchDownDragBar);

        dragBarListener.onTouchDownOnBar(1, 0);
        assertTrue(Whitebox.getInternalState(mFragment, "isOnTouchDragBar"));
        cutEndTimeBeforeTouchDownDragBar = Whitebox.getInternalState(mFragment, "mCutEndTimeBeforeTouchDownDragBar");
        assertEquals(3000, cutEndTimeBeforeTouchDownDragBar);

        // start bar
        dragBarListener.onMoveOnDragBar(1, 300);
        long startTime = editViewModel.getCutStartTime().getValue();
        assertEquals(300, startTime);

        editViewModel.getPlayerController().doSetOCurrentTimeMillis(400);
        dragBarListener.onMoveOnDragBar(1, 500);
        dragBarListener.onTouchUpOnBar(1, 500);
        startTime = editViewModel.getCutStartTime().getValue();
        assertEquals(500, startTime);
        assertFalse(Whitebox.getInternalState(mFragment, "isOnTouchDragBar"));
        var currentMill = editViewModel.getPlayerController().getCurrentTimeMillis().getValue();
        assertTrue(currentMill == 500);

        // end bar
        dragBarListener.onTouchDownOnBar(2, 0);
        dragBarListener.onMoveOnDragBar(2, 4000);
        long endTime = editViewModel.getCutEndTime().getValue();
        assertEquals(4000, endTime);

        editViewModel.getPlayerController().setOCurrentTimeMillis(3700);
        dragBarListener.onMoveOnDragBar(2, 3500);
        dragBarListener.onTouchUpOnBar(2, 3500);
        endTime = editViewModel.getCutEndTime().getValue();
        assertEquals(3500, endTime);
        currentMill = editViewModel.getPlayerController().getCurrentTimeMillis().getValue();
        assertTrue(3500 == currentMill);
    }

    @Test
    public void should_correct_when_onTouchDownDragCenterLine() {
        mFragment.onCreateView(mActivity.getLayoutInflater(), mActivity.findViewById(R.id.fl_content), null);
        EditViewModel editViewModel = mFragment.getMEditViewModel();
        assertNotNull(editViewModel);
        editViewModel.getCutStartTime().setValue(100L);
        editViewModel.getCutEndTime().setValue(3000L);
        editViewModel.getPlayerController().doSetOCurrentTimeMillis(1000);
        GloblePreViewBar.PointMoveListener dragBarListener = Whitebox.getInternalState(mFragment, "mPointMoveListener");

        dragBarListener.onTouchDownMiddleBar();
        assertTrue(editViewModel.getPlayerController().getMIsTouchSeekbar().getValue());
        dragBarListener.onMoveOnMiddleBar(2000);
        assertTrue(editViewModel.getNeedSyncRulerView());

        dragBarListener.onTouchUpMiddleBar(2200);
        assertTrue(editViewModel.getNeedSyncRulerView());
    }
}