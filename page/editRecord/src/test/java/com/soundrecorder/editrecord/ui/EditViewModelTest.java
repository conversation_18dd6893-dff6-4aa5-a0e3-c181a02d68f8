/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: EditViewModelTest
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord.ui;

import android.content.Context;
import android.os.Build;
import android.view.LayoutInflater;

import androidx.lifecycle.MutableLiveData;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.editrecord.EditRecordActivity;
import com.soundrecorder.editrecord.R;
import com.soundrecorder.editrecord.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption;
import com.soundrecorder.editrecord.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.wavemark.model.AmpAndMarkModel;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class,
        ShadowFeatureOption.class, ShadowCOUIVersionUtil.class})
public class EditViewModelTest {

    private ActivityController<EditRecordActivity> mController;
    private EditRecordActivity mActivity;
    private EditRecordFragment mFragment;
    private Context mContext;
    private EditViewModel editViewModel;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        mController = Robolectric.buildActivity(EditRecordActivity.class);
        mActivity = mController.create().resume().get();
        mFragment = (EditRecordFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        mFragment.onCreateView(LayoutInflater.from(mActivity), mActivity.findViewById(R.id.fl_content), null);
        editViewModel = mFragment.getMEditViewModel();
    }

    @After
    public void tearDown() {
        mFragment = null;
        mActivity = null;
        mController = null;
        mContext = null;
        editViewModel = null;
    }

    @Test
    public void should_returnNotnull_when_readMarkTag() {
        editViewModel.readMarkTag();
        MutableLiveData<Boolean> data = Whitebox.getInternalState(editViewModel, "isPrepareAmplitudeAndMark");
        Assert.assertEquals(data.getValue().booleanValue(), false);
    }

    @Test
    public void should_returnNotnull_when_readAmpAndMark() throws Exception {
        AmpAndMarkModel ampAndMarkModel = Whitebox.invokeMethod(editViewModel, "readAmpAndMark");
        Assert.assertNotNull(ampAndMarkModel);
    }

    @Test
    public void should_returnNotnull_when_correctAmplitudeList() throws Exception {
        ArrayList<String> stringArrayList = Whitebox.invokeMethod(editViewModel, "correctAmplitudeList", 1000L);
        Assert.assertNotNull(stringArrayList);
    }

    @Test
    public void should_returnNotnull_when_correctAmplitudeList2() throws Exception {
        List<String> strings = new ArrayList<>();
        strings.add("111");
        Whitebox.setInternalState(editViewModel, "mNeedWaveLineCount", 3);
        Whitebox.invokeMethod(editViewModel, "correctAmplitudeList", strings);
        int mNeedWaveLineCount = Whitebox.getInternalState(editViewModel, "mNeedWaveLineCount");
        Assert.assertEquals(mNeedWaveLineCount, 0);
    }

    @Test
    public void should_returnNotnull_when_onCleared() {
        editViewModel.onCleared();
        Assert.assertNull(Whitebox.getInternalState(editViewModel, "clipTask"));
    }

    @Test
    public void should_returnNotnull_when_onActivityFinish() {
        editViewModel.onActivityFinish();
        Assert.assertNull(Whitebox.getInternalState(editViewModel, "readMarkTagJob"));
    }

    @Test
    public void should_when_onMarkClick() {
        EditViewModel editViewModel = new EditViewModel();
        editViewModel.getCutStartTime().setValue(1000L);
        editViewModel.getCutEndTime().setValue(5000L);
        editViewModel.onMarkClicked(new MarkDataBean(500L, 1));
        Assert.assertEquals(500, (long) editViewModel.getCutStartTime().getValue());

        editViewModel.onMarkClicked(new MarkDataBean(5500L, 1));
        Assert.assertEquals(5500, (long) editViewModel.getCutEndTime().getValue());
    }

    @Test
    public void should_when_playBtnClick() {
        EditViewModel editViewModel = new EditViewModel();
        editViewModel.getCutStartTime().setValue(1000L);
        editViewModel.getCutEndTime().setValue(5000L);

        editViewModel.getPlayerController().getCurrentTimeMillis().setValue(7000L);
        editViewModel.playBtnClick();
    }
}
