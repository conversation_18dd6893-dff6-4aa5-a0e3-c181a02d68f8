/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ClipRecordTest
 Description:
 Version: 1.0
 Date: 2022/12/2
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/2 1.0 create
 */

package com.soundrecorder.editrecord;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.doReturn;

import android.content.Context;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.net.Uri;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.editrecord.shadows.ShadowFeatureOption;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.io.FileDescriptor;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class ClipRecordTest {
    public static final String TEST_AUDIO_PATH = "emulated/0/Music/Recordings/Standard Recordings/1.mp3";
    public static final String TEST_AUDIO_NAME = "1.mp3";

    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_valueEqual_when_extract() {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        clipRecord.extract(2L, 1L, 1L);
        long mPosition = Whitebox.getInternalState(clipRecord, "mPosition");
        assertEquals(mPosition, 0);
        MockedStatic<BaseUtil> mockedStatic = Mockito.mockStatic(BaseUtil.class);
        mockedStatic.when(() -> BaseUtil.getPhoneStorageDir(mContext)).thenReturn("test");
        clipRecord.extract(1L, 2L, 1L);
        long mPosition2 = Whitebox.getInternalState(clipRecord, "mPosition");
        assertEquals(mPosition2, 0);
        mockedStatic.close();
    }

    @Test
    public void should_valueEqual_when_delete() {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        clipRecord.delete(2L, 1L, 1L);
        Uri mClipUri = Whitebox.getInternalState(clipRecord, "mClipUri");
        assertNull(mClipUri);
        MockedStatic<BaseUtil> mockedStatic = Mockito.mockStatic(BaseUtil.class);
        mockedStatic.when(() -> BaseUtil.getPhoneStorageDir(mContext)).thenReturn("test");
        clipRecord.delete(1L, 2L, 1L);
        Uri mClipUri2 = Whitebox.getInternalState(clipRecord, "mClipUri");
        assertNull(mClipUri2);
        mockedStatic.close();
    }

    @Test
    public void should_returnFalse_when_clip() throws Exception {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        clipRecord.delete(2L, 1L, 1L);
        Uri mClipUri = Whitebox.getInternalState(clipRecord, "mClipUri");
        assertNull(mClipUri);
        MockedStatic<BaseUtil> mockedStatic = Mockito.mockStatic(BaseUtil.class);
        mockedStatic.when(() -> BaseUtil.getPhoneStorageDir(mContext)).thenReturn("test");
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        MediaExtractor mediaExtractor = Mockito.mock(MediaExtractor.class);
        doReturn(2l).when(mediaExtractor).getSampleSize();
        doReturn(0).when(mediaExtractor).getSampleTrackIndex();
        boolean isflag = Whitebox.invokeMethod(clipRecord, "clip", mediaExtractor, fileChannel, 1l, 2l, 1l);
        assertFalse(isflag);
        mockedStatic.close();
    }

    @Test
    public void should_returnFalse_when_clipAAC() throws Exception {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        Uri mClipUri = Whitebox.getInternalState(clipRecord, "mClipUri");
        assertNull(mClipUri);
        MockedStatic<BaseUtil> mockedStatic = Mockito.mockStatic(BaseUtil.class);
        mockedStatic.when(() -> BaseUtil.getPhoneStorageDir(mContext)).thenReturn("test");
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        MediaExtractor mediaExtractor = Mockito.mock(MediaExtractor.class);
        doReturn(2l).when(mediaExtractor).getSampleSize();
        doReturn(0).when(mediaExtractor).getSampleTrackIndex();
        boolean isflag = Whitebox.invokeMethod(clipRecord, "clipAAC", mediaExtractor, fileChannel, 1l, 2l, 1l);
        assertFalse(isflag);
        mockedStatic.close();
    }

    @Test
    public void should_returnFalse_when_clipRevert() throws Exception {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        Uri mClipUri = Whitebox.getInternalState(clipRecord, "mClipUri");
        assertNull(mClipUri);
        MockedStatic<BaseUtil> mockedStatic = Mockito.mockStatic(BaseUtil.class);
        mockedStatic.when(() -> BaseUtil.getPhoneStorageDir(mContext)).thenReturn("test");
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        MediaExtractor mediaExtractor = Mockito.mock(MediaExtractor.class);
        doReturn(2l).when(mediaExtractor).getSampleSize();
        boolean isflag = Whitebox.invokeMethod(clipRecord, "clipRevert", mediaExtractor, fileChannel, 1l, 2l, 1l);
        assertFalse(isflag);
        mockedStatic.close();
    }

    @Test
    public void should_returnFalse_when_clipRevertAAC() throws Exception {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        Uri mClipUri = Whitebox.getInternalState(clipRecord, "mClipUri");
        assertNull(mClipUri);
        MockedStatic<BaseUtil> mockedStatic = Mockito.mockStatic(BaseUtil.class);
        mockedStatic.when(() -> BaseUtil.getPhoneStorageDir(mContext)).thenReturn("test");
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        MediaExtractor mediaExtractor = Mockito.mock(MediaExtractor.class);
        MediaFormat format = Mockito.mock(MediaFormat.class);
        doReturn("test").when(format).getString(MediaFormat.KEY_MIME);
        doReturn(1).when(mediaExtractor).getSampleTrackIndex();
        doReturn(format).when(mediaExtractor).getTrackFormat(1);
        doReturn(2l).when(mediaExtractor).getSampleSize();
        boolean isflag = Whitebox.invokeMethod(clipRecord, "clipRevertAAC", mediaExtractor, fileChannel, 1l, 2l, 1l);
        assertFalse(isflag);
        mockedStatic.close();
    }

    @Test
    public void should_returnSize_when_getAudioTrack() throws Exception {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        Uri mClipUri = Whitebox.getInternalState(clipRecord, "mClipUri");
        assertNull(mClipUri);
        MockedStatic<BaseUtil> mockedStatic = Mockito.mockStatic(BaseUtil.class);
        mockedStatic.when(() -> BaseUtil.getPhoneStorageDir(mContext)).thenReturn("test");
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        MediaExtractor mediaExtractor = Mockito.mock(MediaExtractor.class);
        MediaFormat format = Mockito.mock(MediaFormat.class);
        doReturn("test").when(format).getString(MediaFormat.KEY_MIME);
        doReturn(1).when(mediaExtractor).getTrackCount();
        doReturn(format).when(mediaExtractor).getTrackFormat(0);
        int size = Whitebox.invokeMethod(clipRecord, "getAudioTrack", mediaExtractor);
        assertEquals(size, -1);
        mockedStatic.close();
    }

    @Test
    public void should_returnFalse_when_getDurationByFD() {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        long length = clipRecord.getDurationByFD(new FileDescriptor());
        Assert.assertEquals(length, 0);
    }

    @Test
    public void should_returnFalse_when_clipAmplitudes() throws Exception {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        Whitebox.setInternalState(clipRecord, "mObjAmplitudes", new ArrayList<String>(), ClipRecord.class);
        Whitebox.invokeMethod(clipRecord, "clipAmplitudes", 1l, 2l, 1l);
        Assert.assertNull(clipRecord.getClipAmplitudes());
    }

    @Test
    public void should_returnNotnull_when_deleteAmplitudes() throws Exception {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        Whitebox.setInternalState(clipRecord, "mObjAmplitudes", new ArrayList<String>(), ClipRecord.class);
        Whitebox.invokeMethod(clipRecord, "deleteAmplitudes", 1l, 2l, 1l);
        Assert.assertNull(clipRecord.getClipAmplitudes());
    }

    @Test
    public void should_returnNotnull_when_clipMarkList() throws Exception {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        List<MarkDataBean> markDataBeans = new ArrayList<>();
        markDataBeans.add(new MarkDataBean(1L, 1));
        clipRecord.setMarkList(markDataBeans);
        Whitebox.invokeMethod(clipRecord, "clipMarkList", 1L, 2L, 2L);
        Assert.assertNotNull(clipRecord.getClipMarkList());
    }

    @Test
    public void should_returnNotnull_when_deleteMarkList() throws Exception {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        List<MarkDataBean> markDataBeans = new ArrayList<>();
        markDataBeans.add(new MarkDataBean(1L, 1));
        clipRecord.setMarkList(markDataBeans);
        Whitebox.invokeMethod(clipRecord, "deleteMarkList", 1L, 2L, 2L);
        Assert.assertNotNull(clipRecord.getClipMarkList());
    }

    @Test
    public void should_returnNotnull_when_getWaveHeader() throws Exception {
        Record record = getMockRecord();
        ClipRecord clipRecord = new ClipRecord(mContext, record);
        Uri mClipUri = Whitebox.getInternalState(clipRecord, "mClipUri");
        assertNull(mClipUri);
        MockedStatic<BaseUtil> mockedStatic = Mockito.mockStatic(BaseUtil.class);
        mockedStatic.when(() -> BaseUtil.getPhoneStorageDir(mContext)).thenReturn("test");
        FileChannel fileChannel = Mockito.mock(FileChannel.class);
        MediaExtractor mediaExtractor = Mockito.mock(MediaExtractor.class);
        MediaFormat format = Mockito.mock(MediaFormat.class);
        doReturn(1).when(format).getInteger(MediaFormat.KEY_CHANNEL_COUNT);
        doReturn(1).when(format).getInteger(MediaFormat.KEY_SAMPLE_RATE);
        doReturn(1).when(mediaExtractor).getSampleTrackIndex();
        doReturn(format).when(mediaExtractor).getTrackFormat(1);
        byte[] bytes = Whitebox.invokeMethod(clipRecord, "getWaveHeaderForExtract", mediaExtractor, 1000L, 2000L);
        assertNotNull(bytes);
        mockedStatic.close();
    }

    @Test
    public void should_corrent_when_cancel() {
        Record record = getMockRecord();

        ClipRecord clipRecord = new ClipRecord(mContext, record);
        boolean isCanceled = Whitebox.getInternalState(clipRecord, "mIsCanceled");
        Assert.assertFalse(isCanceled);

        clipRecord.setCanceled(true, false);
        isCanceled = Whitebox.getInternalState(clipRecord, "mIsCanceled");
        Assert.assertTrue(isCanceled);
    }

    private Record getMockRecord() {
        Record record = Mockito.mock(Record.class);
        doReturn(RecordConstant.MIMETYPE_MP3).when(record).getMimeType();
        doReturn(TEST_AUDIO_PATH).when(record).getRelativePath();
        doReturn(TEST_AUDIO_NAME).when(record).getDisplayName();
        return record;
    }
}
