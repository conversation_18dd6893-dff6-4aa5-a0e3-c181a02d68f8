apply from:"../../common_build.gradle"

android {
    namespace "com.soundrecorder.editrecord"
}

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.material
    implementation libs.androidx.appcompat
    implementation libs.androidx.lifecycle.livedata
    implementation libs.androidx.lifecycle.viewmodel
    implementation libs.androidx.lifecycle.extensions
    implementation libs.androidx.fragment.ktx
    compileOnly libs.oplus.addon
    testImplementation libs.oplus.addon
    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    // 以下子包应用可选使用，如有使用了如下子包的控件，则需要添加，未使用可以不引用
    implementation libs.oplus.coui.recyclerview
    implementation libs.oplus.coui.progressbar
    implementation libs.oplus.coui.scrollbar
    implementation libs.oplus.coui.dialog
    implementation libs.oplus.coui.toolbar
    implementation libs.oplus.coui.poplist
    implementation libs.oplus.coui.panel
    implementation libs.oplus.coui.responsiveui
    // Koin for Android
    implementation(libs.koin)
    kaptTest libs.androidx.databinding.compiler
    implementation project(':common:modulerouter')
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':component:player')
    implementation project(':component:wavemark')
    implementation project(':common:libimageload')
}
