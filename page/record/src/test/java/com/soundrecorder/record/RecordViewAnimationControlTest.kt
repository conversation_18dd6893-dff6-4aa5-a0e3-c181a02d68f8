/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordViewAnimationControlTest
 * Description:
 * Version: 1.0
 * Date: 2023/7/29
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/7/29 1.0 create
 */

package com.soundrecorder.record

import android.animation.AnimatorSet
import android.content.Context
import android.os.Build
import android.view.View
import androidx.core.view.marginTop
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.splitwindow.FoldingWindowObserver
import com.soundrecorder.record.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowCOUIVersionUtil::class]
)
@Ignore
class RecordViewAnimationControlTest {
    private var context: Context? = null
    private var activityControl: ActivityController<RecorderActivity>? = null
    private var activity: RecorderActivity? = null
    private var control: RecordViewAnimationControl? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        activityControl = Robolectric.buildActivity(RecorderActivity::class.java).create().start()
        activity = activityControl?.get()
        control =
            Whitebox.getInternalState<RecordViewAnimationControl>(activity, "mViewAnimateControl")
    }

    @After
    fun release() {
        context = null
        activityControl?.stop()
        activityControl = null
        activity = null
    }

    @Test
    fun should_correct_when_getWavePercent() {
        control?.foldWindowType = FoldingWindowObserver.SCREEN_VERTICAL_EXPAND

        var percent = control?.getWavePercent()
        Assert.assertEquals(Whitebox.getInternalState(control, "wavePercentDefault"), percent)

        control?.foldWindowType = FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER
        percent = control?.getWavePercent()
        Assert.assertEquals(Whitebox.getInternalState(control, "wavePercentHover"), percent)
    }

    @Test
    fun should_correct_when_isHoverState() {
        control?.foldWindowType = FoldingWindowObserver.SCREEN_VERTICAL_EXPAND
        var isHover = Whitebox.invokeMethod<Boolean>(control, "isHoverState")
        Assert.assertFalse(isHover)

        control?.foldWindowType = FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER
        isHover = Whitebox.invokeMethod(control, "isHoverState")
        Assert.assertTrue(isHover)
    }

    @Test
    fun should_showViewLocationTop_when_showRecordTopCenterOrTop() {
        control?.foldWindowType = FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER
        control?.showRecordTopCenterOrTop(false)
        val timerLayout = Whitebox.getInternalState<View>(control, "timerLayout")
        Assert.assertTrue(timerLayout.marginTop != 0)

        control?.foldWindowType = FoldingWindowObserver.SCREEN_VERTICAL_EXPAND
        control?.showRecordTopCenterOrTop(false)
        Assert.assertTrue(timerLayout.marginTop == 0)
    }

    @Test
    fun should_null_when_release() {
        Whitebox.setInternalState(control, "showMarkAnimation", AnimatorSet())
        Whitebox.setInternalState(control, "hideMarkAnimation", AnimatorSet())

        control?.release()
        Assert.assertNull(Whitebox.getInternalState<AnimatorSet>(control, "showMarkAnimation"))
        Assert.assertNull(Whitebox.getInternalState<AnimatorSet>(control, "hideMarkAnimation"))
    }
}