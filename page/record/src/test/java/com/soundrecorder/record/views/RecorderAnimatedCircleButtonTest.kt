/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecorderAnimatedCircleButtonTest
 * Description:
 * Version: 1.0
 * Date: 2024/2/5
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/2/5 1.0 create
 */

package com.soundrecorder.record.views

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.record.shadows.ShadowFeatureOption
import com.soundrecorder.record.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class RecorderAnimatedCircleButtonTest {
    var context: Context? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        context = null
    }

    @Test
    fun should_correct_when_init() {
        val context = context ?: return
        val view = RecorderAnimatedCircleButton(context)
        Assert.assertNull(Whitebox.getInternalState(view, "mNightDrawablePlay"))
    }

    @Test
    fun should_correct_when_switchCallNightPlayState() {
        val context = context ?: return
        val view = RecorderAnimatedCircleButton(context)
        view.switchCallNightPlayState(false)
        Assert.assertNotNull(Whitebox.getInternalState(view, "mLightDrawablePlay"))

        view.switchCallNightPlayState(true)
        Assert.assertNotNull(Whitebox.getInternalState(view, "mNightDrawablePlay"))

        Whitebox.invokeMethod<Unit>(view, "onDetachedFromWindow")
        Assert.assertNull(Whitebox.getInternalState(view, "mLightDrawablePlay"))
    }
}