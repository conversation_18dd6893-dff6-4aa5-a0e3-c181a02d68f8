<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="CustomButtonView">
        <attr name="button_light_img" format="reference" />
        <attr name="button_un_light_img" format="reference" />
        <attr name="button_light_text_color" format="reference" />
        <attr name="button_un_light_text_color" format="reference" />
        <attr name="button_enable" format="boolean" />
        <attr name="button_text" format="string" />

    </declare-styleable>


    <style name="PopViewLoading" parent="@style/AppNoTitleTheme">
        <!--是否悬浮在activity上-->
        <item name="android:windowIsFloating">true</item>
        <!--透明是否-->
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@drawable/coui_center_alert_dialog_bg</item>
        <item name="android:background">@null</item>
        <!--设置没有窗口标题、dialog标题等各种标题-->
        <item name="android:windowNoTitle">true</item>
        <item name="android:title">@null</item>
        <item name="android:dialogTitle">@null</item>

    </style>

    <array name="speakerColor">
        <item name="speaker_01">@color/coui_color_mint_variant</item>
        <item name="speaker_02">@color/coui_color_cyan_variant</item>
        <item name="speaker_03">@color/coui_color_green_variant</item>
        <item name="speaker_04">@color/coui_color_yellow_variant</item>
        <item name="speaker_05">@color/coui_color_orange_variant</item>
        <item name="speaker_06">@color/coui_color_red_variant</item>
        <item name="speaker_07">@color/coui_color_blue_variant</item>
        <item name="speaker_08">@color/coui_color_azure_variant</item>
        <item name="speaker_09">@color/coui_color_violet_variant</item>
        <item name="speaker_10">@color/coui_color_purple_variant</item>
    </array>
</resources>