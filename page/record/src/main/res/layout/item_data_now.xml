<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="@dimen/real_time_item_data_padding"
    android:paddingEnd="@dimen/real_time_item_data_padding"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/layout_data_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        tools:ignore="UselessParent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/data_speaker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:max_padding_end="@dimen/dp14">

            <LinearLayout
                android:id="@+id/ll_speaker"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <ImageView
                    android:id="@+id/speaker_portrait"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp5"
                    android:background="@drawable/speaker_portrait_picture" />

                <TextView
                    android:id="@+id/tv_speaker"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-medium"
                    android:includeFontPadding="false"
                    android:lines="1"
                    android:paddingStart="@dimen/dp2"
                    android:paddingTop="@dimen/dp5.5"
                    android:paddingEnd="@dimen/dp8"
                    android:paddingBottom="@dimen/dp5.5"
                    android:textColor="?attr/couiColorLabelPrimary"
                    android:textFontWeight="500"
                    android:textSize="@dimen/dp10"
                    tools:text="@string/convert_speaker" />

            </LinearLayout>

            <com.oplus.anim.EffectiveAnimationView
                android:id="@+id/speaker_background_animation"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/ll_speaker_animation"
                app:layout_constraintEnd_toEndOf="@id/ll_speaker_animation"
                app:layout_constraintStart_toStartOf="@id/ll_speaker_animation"
                app:layout_constraintTop_toTopOf="@id/ll_speaker_animation"
                />

            <LinearLayout
                android:id="@+id/ll_speaker_animation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                tools:ignore="MissingConstraints,UseCompoundDrawables">

                <com.oplus.anim.EffectiveAnimationView
                    android:id="@+id/speaker_portrait_animation"
                    android:layout_width="@dimen/dp12"
                    android:layout_height="@dimen/dp12"
                    android:layout_marginStart="@dimen/dp5"/>

                <TextView
                    android:id="@+id/tv_speaker_animation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif-medium"
                    android:includeFontPadding="false"
                    android:lines="1"
                    android:paddingStart="@dimen/dp2"
                    android:paddingTop="@dimen/dp5.5"
                    android:paddingEnd="@dimen/dp8"
                    android:paddingBottom="@dimen/dp5.5"
                    android:textColor="?attr/couiColorLabelPrimary"
                    android:textFontWeight="500"
                    android:textSize="@dimen/dp10"
                    android:layout_marginStart="@dimen/dp2"
                    tools:text="@string/recognizing_speaker" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/start_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minHeight="@dimen/dp16"
            android:layout_marginStart="@dimen/dp8"
            android:layout_marginEnd="@dimen/dp15"
            android:layout_weight="1"
            android:layout_gravity="end|center_vertical"
            android:gravity="end"
            android:ellipsize="end"
            android:fontFamily="sans-serif-medium"
            android:textFontWeight="500"
            android:fontFeatureSettings="tnum"
            android:lines="1"
            android:text="00:12"
            android:textColor="?attr/couiColorLabelSecondary"
            android:textSize="@dimen/sp12" />
    </LinearLayout>

    <!-- 设置的couiAnimateTextType： 1(ALPHA) + 2(GRADIENT COLOR) -->
    <com.soundrecorder.common.widget.COUIAnimateTextView
        android:id="@+id/item_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp2"
        android:layout_marginBottom="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp2"
        android:background="@null"
        android:textFontWeight="400"
        android:textSize="@dimen/sp16"
        android:fontFamily="sans-serif-medium"
        android:textColor="?attr/couiColorLabelPrimary"
        app:couiAnimateTextStartColor="@color/subtitle_font_color_start"
        app:couiAnimateTextEndColor="@color/subtitle_font_color_end"
        app:couiAnimateTextStableColor="?attr/couiColorLabelPrimary"
        app:couiAnimateStyle="1"
        app:couiAnimateTextDelay="150"
        app:couiAnimateTextDuration="500"
        app:couiAnimateTextOffset="10"
        app:couiAnimateTextType="3" />

</LinearLayout>