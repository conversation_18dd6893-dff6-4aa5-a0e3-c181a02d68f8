/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - SubtitleMarkInsertHelper.kt
 * Description:
 *     The helper class for inserting marks into subtitle text.
 *
 * Version: 1.0
 * Date: 2025-05-29
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * Bixia<PERSON>.<EMAIL>    2025-05-29   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record.subtitle

import android.os.Handler
import android.os.Looper
import androidx.annotation.MainThread
import androidx.annotation.WorkerThread
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.MarkDataSource
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.DatumPointUtil
import com.soundrecorder.record.subtitle.data.DisplayMark
import com.soundrecorder.record.subtitle.data.DisplaySubtitleEntry
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

/**
 * 此代码文件为AI生成产物，生成工具为Augment
 */
class SubtitleMarkInsertHelper {

    private companion object {
        private const val TAG = "SubtitleMarkInsertHelper"

        /**
         * 标记字符，用于插入到字幕文本当中
         */
        private const val ICON_MARK_FLAG = "\u2691"
        //识别中状态的上屏字幕的标记字符(响铃符)，不可打印(实际显示时看不到)，但能用于检索定位
        private const val ICON_INTERMEDIATE_MARK_FLAG = "\u0007"
        private const val FLOAT_ACCURACY = 0.00001f
    }

    /**
     * 上屏字幕条目列表
     * 使用线程安全的CopyOnWriteArrayList确保多线程访问安全
     */
    private val displaySubtitleEntries = CopyOnWriteArrayList<DisplaySubtitleEntry>()

    /**
     * 已有的标记信息列表
     * 使用线程安全的CopyOnWriteArrayList确保多线程访问安全
     */
    private val markDataBeans = CopyOnWriteArrayList<MarkDataBean>()

    /**
     * 用于异步执行的线程池
     */
    private val executorService: ExecutorService by lazy {
        Executors.newSingleThreadExecutor()
    }

    /**
     * 主线程Handler，用于回调
     */
    private val mainHandler by lazy { Handler(Looper.getMainLooper()) }

    /**
     * 获取上屏字幕条目列表
     *
     * @return 上屏字幕条目列表的只读副本
     */
    fun getDisplaySubtitleEntries(): List<DisplaySubtitleEntry> {
        return displaySubtitleEntries.toList()
    }

    /**
     * 获取标记数据列表
     *
     * @return 标记数据列表的只读副本
     */
    fun getMarkDataBeans(): List<MarkDataBean> {
        return markDataBeans.toList()
    }

    /**
     * 异步更新标记数据
     *
     * 在后台线程中执行updateMarkDataBeans，完成后在主线程回调IAfterUpdatedAction。
     *
     * @param newMarkDataBeans 新的标记数据列表
     * @param afterAction 更新完成后的回调
     */
    fun asyncUpdateMarkDataBeans(
        newMarkDataBeans: List<MarkDataBean>,
        afterAction: IAfterUpdatedAction
    ) {
        executorService.execute {
            runCatching {
                // 在工作线程中执行更新
                updateMarkDataBeans(newMarkDataBeans)

                // 获取更新后的字幕条目列表
                val updatedEntries = getDisplaySubtitleEntries()

                // 在主线程中回调
                mainHandler.post {
                    afterAction.onAfterUpdated(updatedEntries)
                }
            }.onFailure {
                // 异常处理：在主线程中回调空列表
                DebugUtil.e(TAG, "asyncUpdateMarkDataBeans error: ${it.message}")
            }
        }
    }

    /**
     * 更新整个MarkDataBean列表数据
     *
     * 更新标记数据列表，并重新分配所有标记到对应的字幕条目中。
     * 确保displaySubtitleEntries中的每个标记都能与MarkDataBean列表中的项目一一对应。
     *
     * @param newMarkDataBeans 新的标记数据列表
     */
    @WorkerThread
    fun updateMarkDataBeans(newMarkDataBeans: List<MarkDataBean>) {
        // 更新标记数据
        markDataBeans.clear()
        markDataBeans.addAll(newMarkDataBeans)

        // redistributeAllMarks方法会处理标记的清空和重新分配
        redistributeAllMarks()
    }

    /**
     * 异步更新字幕内容
     *
     * 在后台线程中执行updateSubtitleContent，完成后在主线程回调IAfterUpdatedAction。
     *
     * @param completedSubtitles 已经通过ASR识别完成的字幕列表（已按时间戳排序）
     * @param recognizingSubtitles ASR正在识别过程中逐字输出的字幕列表（通常至多一个）
     * @param afterAction 更新完成后的回调
     */
    fun asyncUpdateSubtitleContent(
        completedSubtitles: List<ConvertContentItem>,
        recognizingSubtitles: List<ConvertContentItem>,
        afterAction: IAfterUpdatedAction
    ) {
        executorService.execute {
            runCatching {
                // 在工作线程中执行更新
                updateSubtitleContent(completedSubtitles, recognizingSubtitles)

                // 获取更新后的字幕条目列表
                val updatedEntries = getDisplaySubtitleEntries()

                // 在主线程中回调
                mainHandler.post {
                    afterAction.onAfterUpdated(updatedEntries)
                }
            }.onFailure {
                DebugUtil.e(TAG, "asyncUpdateSubtitleContent error: ${it.message}")
            }
        }
    }

    /**
     * 异步同时更新字幕内容和标记数据
     *
     * 在后台线程中同时执行updateSubtitleContent和updateMarkDataBeans，
     * 在同一次execute中完成处理，最后统一回调一次IAfterUpdatedAction。
     * 这比分别调用asyncUpdateSubtitleContent和asyncUpdateMarkDataBeans更高效，
     * 避免了多次线程切换和重复的标记重新分配操作。
     *
     * @param completedSubtitles 已经通过ASR识别完成的字幕列表（已按时间戳排序）
     * @param recognizingSubtitles ASR正在识别过程中逐字输出的字幕列表（通常至多一个）
     * @param newMarkDataBeans 新的标记数据列表
     * @param afterAction 更新完成后的回调
     */
    fun asyncUpdate(
        completedSubtitles: List<ConvertContentItem>,
        recognizingSubtitles: List<ConvertContentItem>,
        newMarkDataBeans: List<MarkDataBean>,
        afterAction: IAfterUpdatedAction
    ) {
        executorService.execute {
            // 在工作线程中同时执行字幕内容和标记数据的更新
            runCatching {
                // 1. 更新标记数据
                markDataBeans.clear()
                markDataBeans.addAll(newMarkDataBeans)

                // 2. 更新字幕内容, 合并两个列表，先处理已完成的字幕，再处理正在识别的字幕
                val allSubtitles = completedSubtitles + recognizingSubtitles

                // 更新字幕条目列表
                allSubtitles.forEach { subtitle ->
                    val isFromCompleted = subtitle in completedSubtitles
                    addOrUpdateDisplaySubtitleEntry(subtitle, isFromCompleted)
                }

                // 移除不再存在的字幕条目（如果某些字幕被删除了）
                val currentStartTimes = allSubtitles.map { it.startTime }.toSet()
                val entriesToKeep = displaySubtitleEntries.filter { entry ->
                    entry.originContent.startTime in currentStartTimes
                }
                displaySubtitleEntries.clear()
                displaySubtitleEntries.addAll(entriesToKeep)

                // 3. 统一重新分配所有标记（只执行一次，避免重复操作）
                redistributeAllMarks()

                // 获取更新后的字幕条目列表
                val updatedEntries = getDisplaySubtitleEntries()

                // 在主线程中回调
                mainHandler.post {
                    afterAction.onAfterUpdated(updatedEntries)
                }
            }.onFailure {
                DebugUtil.e(TAG, "asyncUpdate error: ${it.message}")
            }
        }
    }

    /**
     * 更新字幕内容
     *
     * 根据ASR识别的两个阶段更新字幕内容：已完成识别的字幕和正在识别中的字幕。
     * 更新后会重新分配所有标记到对应的字幕条目中。
     *
     * @param completedSubtitles 已经通过ASR识别完成的字幕列表（已按时间戳排序）
     * @param recognizingSubtitles ASR正在识别过程中逐字输出的字幕列表（通常至多一个）
     */
    @WorkerThread
    fun updateSubtitleContent(
        completedSubtitles: List<ConvertContentItem>,
        recognizingSubtitles: List<ConvertContentItem>
    ) {
        // 合并两个列表，先处理已完成的字幕，再处理正在识别的字幕
        val allSubtitles = completedSubtitles + recognizingSubtitles

        // 更新字幕条目列表
        allSubtitles.forEach { subtitle ->
            val isFromCompleted = subtitle in completedSubtitles
            addOrUpdateDisplaySubtitleEntry(subtitle, isFromCompleted)
        }

        // 移除不再存在的字幕条目（如果某些字幕被删除了）
        val currentStartTimes = allSubtitles.map { it.startTime }.toSet()
        val entriesToKeep = displaySubtitleEntries.filter { entry ->
            entry.originContent.startTime in currentStartTimes
        }
        displaySubtitleEntries.clear()
        displaySubtitleEntries.addAll(entriesToKeep)

        // 重新分配所有标记到对应的字幕条目中
        redistributeAllMarks()
    }

    /**
     * 添加或更新字幕条目
     *
     * 如果传入的ConvertContentItem的startTime与已有条目相同，则更新已有条目的originContent；
     * 否则创建新的DisplaySubtitleEntry并添加到列表中。
     *
     * @param convertContentItem 要添加的字幕内容项
     * @param isFromCompletedSubtitles 是否来自已完成的字幕列表
     */
    private fun addOrUpdateDisplaySubtitleEntry(
        convertContentItem: ConvertContentItem,
        isFromCompletedSubtitles: Boolean
    ) {
        // 查找是否已存在相同startTime的条目
        val existingEntry = displaySubtitleEntries.find {
            it.originContent.startTime == convertContentItem.startTime
        }

        if (existingEntry != null) {
            // 使用lastContent检查内容是否有实际变化，避免引用对象被外部修改导致的检测失效
            val contentChanged = existingEntry.lastContent != convertContentItem.textContent ||
                    existingEntry.originContent.endTime != convertContentItem.endTime ||
                    existingEntry.isFromCompletedSubtitles != isFromCompletedSubtitles

            // 更新已有条目的originContent和来源标志
            existingEntry.originContent = convertContentItem
            existingEntry.isFromCompletedSubtitles = isFromCompletedSubtitles

            // 只有在内容有变化时才清空标记和重置displayContent
            if (contentChanged) {
                existingEntry.insertedMarks.clear()
                existingEntry.displayContent = convertContentItem.textContent
                existingEntry.lastContent = convertContentItem.textContent // 更新lastContent
            }
        } else {
            // 创建新的DisplaySubtitleEntry
            val newEntry = DisplaySubtitleEntry(
                originContent = convertContentItem,
                displayContent = convertContentItem.textContent,
                insertedMarks = mutableListOf(),
                isFromCompletedSubtitles = isFromCompletedSubtitles,
                lastContent = convertContentItem.textContent // 初始化lastContent
            )
            displaySubtitleEntries.add(newEntry)

            // 按startTime排序保持列表有序，CopyOnWriteArrayList不支持直接sortBy，需要重新创建排序后的列表
            val sortedEntries = displaySubtitleEntries.sortedBy { it.originContent.startTime }
            displaySubtitleEntries.clear()
            displaySubtitleEntries.addAll(sortedEntries)
        }
    }

    /**
     * 查找标记应该插入的目标字幕条目
     *
     * @param markTimeOffset 标记时间点
     * @return 目标字幕条目，如果找不到则返回null
     */
    private fun findTargetEntryForMark(markTimeOffset: Long): DisplaySubtitleEntry? {
        if (displaySubtitleEntries.isEmpty()) {
            return null
        }

        // 1. 如果标记时间点在第一个字幕之前，分配给第一个字幕
        val firstEntry = displaySubtitleEntries.first()
        if (markTimeOffset < firstEntry.originContent.startTime) {
            return firstEntry
        }

        // 2. 遍历所有字幕条目，找到合适的分配目标
        for (i in displaySubtitleEntries.indices) {
            val entry = displaySubtitleEntries[i]

            // 如果标记在当前字幕的时间范围内，直接分配给该字幕
            if (markTimeOffset >= entry.originContent.startTime &&
                markTimeOffset <= entry.originContent.endTime
            ) {
                return entry
            }

            // 如果标记在当前字幕之后，检查是否在下一个字幕之前
            if (markTimeOffset > entry.originContent.endTime) {
                val nextEntry = displaySubtitleEntries.getOrNull(i + 1)
                if (nextEntry == null || markTimeOffset < nextEntry.originContent.startTime) {
                    // 标记在当前字幕之后且在下一个字幕之前（或没有下一个字幕），分配给当前字幕
                    return entry
                }
            }
        }

        // 3. 如果没有找到合适的条目，分配给最后一个条目
        return displaySubtitleEntries.lastOrNull()
    }

    /**
     * 计算标记在指定字幕条目中的插入位置
     *
     * @param entry 目标字幕条目
     * @param markTimeOffset 标记时间点
     * @return 插入位置
     */
    private fun calculateInsertPositionForEntry(
        entry: DisplaySubtitleEntry,
        markTimeOffset: Long,
        from: MarkDataSource?
    ): Int {
        val originContent = entry.originContent

        // 如果标记不是转写页面打上的，就不插入
        if (from != MarkDataSource.Converting) {
            return -1
        }

        // 如果标记时间点在第一个ConvertContentItem的startTime之前
        if (markTimeOffset < originContent.startTime) {
            return 0
        }

        // 如果标记时间点在ConvertContentItem的endTime之后
        if (markTimeOffset > originContent.endTime) {
            return originContent.textContent.length
        }

        // 使用已有的计算方法
        return DatumPointUtil.calculateMarkFlagInsertPosition(
            originContent.textContent,
            originContent.startTime,
            originContent.endTime,
            markTimeOffset
        )
    }

    /**
     * 刷新字幕条目的displayContent
     *
     * 根据insertedMarks中的标记，将标记字符插入到textContent的对应位置，
     * 生成新的displayContent。
     *
     * @param entry 要刷新的字幕条目
     */
    private fun refreshDisplayContent(entry: DisplaySubtitleEntry) {
        val originalText = entry.originContent.textContent
        val marks = entry.insertedMarks.sortedBy { it.insertPosition }

        if (marks.isEmpty()) {
            entry.displayContent = originalText
            return
        }

        val result = StringBuilder()
        var currentPosition = 0

        // 按插入位置分组，处理相同位置的多个标记
        val marksByPosition = marks.groupBy { it.insertPosition }

        for (position in marksByPosition.keys.sorted()) {
            // 添加当前位置之前的文本
            if (position > currentPosition) {
                result.append(originalText.substring(currentPosition, position))
            }
            // 检查插入位置是否是标点符号前
            if (position < originalText.length && isPunctuationChar(originalText[position], originalText, position)) {
                // 添加该位置的所有标记字符
                val marksAtPosition = marksByPosition[position] ?: emptyList()
                repeat(marksAtPosition.size) {
                    result.append(ICON_MARK_FLAG)
                }
            }

            currentPosition = position
        }

        // 添加剩余的文本
        if (currentPosition < originalText.length) {
            result.append(originalText.substring(currentPosition))
        }

        entry.displayContent = result.toString()
        entry.isMarked = marks.isNotEmpty()
    }

    /**
     * 重新分配所有标记到对应的字幕条目中
     *
     * 优化：只对需要重新分配的标记进行处理，减少不必要的操作
     * 进一步优化：只有当originContent或insertedMarks有变化时才重新计算和刷新
     *
     *
     * 优化后的标记分配逻辑
     */
    private fun redistributeAllMarks() {
        // 1. 构建时间区间索引
        val timeRangeMap = displaySubtitleEntries.associate {
            it.originContent.startTime..it.originContent.endTime to it
        }

        // 2. 清空所有标记（保持原有逻辑）
        displaySubtitleEntries.forEach { it.insertedMarks.clear() }

        // 3. 批量处理标记分配
        markDataBeans
            .filter { it.markSource == MarkDataSource.Converting }
            .forEach { mark ->
                timeRangeMap.entries.firstOrNull { mark.timeInMills in it.key }?.value?.let { entry ->
                    DatumPointUtil.calculateMarkFlagInsertPosition(
                        entry.originContent.textContent,
                        entry.originContent.startTime,
                        entry.originContent.endTime,
                        mark.timeInMills
                    )?.takeIf { it != -1 }?.let { pos ->
                        entry.insertedMarks.add(DisplayMark(mark.timeInMills, pos))
                    }
                }
            }

        // 4. 全量刷新（保持原有逻辑）
        displaySubtitleEntries.forEach { refreshDisplayContent(it) }
    }

    /**
     * 释放资源
     *
     * 停止异步工作线程，释放相关资源。
     * 应在Activity销毁时调用此方法。
     */
    fun release() {
        runCatching {
            executorService.shutdown()
        }.onFailure {
            DebugUtil.e(TAG, "release error: ${it.message}")
        }
    }

    fun interface IAfterUpdatedAction {
        @MainThread
        fun onAfterUpdated(subtitles: List<DisplaySubtitleEntry>)
    }

    // 断句符号的正则表达式
    private val punctuationRegex = Regex("""[、，。：；！？,:;!?]""")

    /**
     * 判断字符是否为断句字符
     * @param c 要判断的字符
     * @param text 完整文本(用于上下文判断)
     * @param index 字符在文本中的索引
     * @return 是否为断句字符
     */
    fun isPunctuationChar(c: Char, text: String, index: Int): Boolean {
        // 如果不是断句符号直接返回false
        if (!c.toString().matches(punctuationRegex)) return false
        // 特殊处理英文句点(.)
        if (c == '.') {
            // 如果后面是数字，可能是小数点或IP地址
            if (index + 1 < text.length && text[index + 1].isDigit()) {
                return false
            }
            // 如果前面是数字，可能是小数点
            if (index > 0 && text[index - 1].isDigit()) {
                return false
            }
            // 如果后面没有字符或非数字，视为句号
            if (index + 1 >= text.length || text[index + 1].isDigit().not()) {
                return true
            }
        }
        return true
    }

    /**
     * 计算所有的高亮文本标记字符对应范围列表
     * @param markedText 已插入标记字符的ASR文本内容
     * @return List<Pair<Int, Int>> 每个标记字符对应的高亮范围列表
     */
    fun calculateHighlightRangesForMarks(markedText: String): List<Pair<Int, Int>> {
        if (markedText.isEmpty()) {
            return emptyList()
        }
        val highlightRanges = mutableListOf<Pair<Int, Int>>()
        var currentIndex = 0
        // 查找所有标记字符的位置
        while (currentIndex < markedText.length) {
            if (markedText[currentIndex].toString() == ICON_MARK_FLAG) {
                // 计算当前标记的高亮范围
                val range = calculateHighlightRangeForMark(markedText, currentIndex)
                highlightRanges.add(range)
            }
            currentIndex++
        }
        return highlightRanges
    }

    private fun isMarkOrPunctuationChar(c: Char, text: String, index: Int): Boolean {
        if (c == ICON_MARK_FLAG[0] || c == ICON_INTERMEDIATE_MARK_FLAG[0]) {
            return true
        }
        // 标记字符以外的断句符号判断规则复用“4.5.3.3 录音原文内容界面视觉更新适配”中的isPunctuationChar的逻辑设计
        return isPunctuationChar(c, text, index)
    }

    fun calculateHighlightRangeForMark(text: String, markIndex: Int): Pair<Int, Int> {
        if (text.isEmpty() || markIndex !in text.indices) {
            return markIndex to markIndex
        }

        // 如果标记前没有字符，仅高亮标记本身
        if (markIndex == 0) {
            return 0 to 0
        }

        var lastIndex = markIndex - 1
        // 标记前一个字符为断句字符及前两个字符为连续断句字符的特殊处理
        if (isMarkOrPunctuationChar(text[lastIndex], text, lastIndex)) {
            if (lastIndex == 0) {
                return markIndex to markIndex
            }
            // 标记前连续两个断句字符则仅高亮标记字符本身
            lastIndex = lastIndex - 1
            if (isMarkOrPunctuationChar(text[lastIndex], text, lastIndex)) {
                return markIndex to markIndex
            }
            if (lastIndex == 0) {
                return 0 to markIndex
            }
        }

        // 继续向前查找标记字符以确定高亮的起始索引位置
        var startIndex = 0
        for (i in lastIndex - 1 downTo 0) {
            if (isMarkOrPunctuationChar(text[i], text, i)) {
                startIndex = i + 1
                break
            }
        }

        return startIndex to markIndex
    }
}