/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 * File: - SubtitleDataInsertHelper.java
 * Description:
 *     The helper class for ui process of subtitle data.
 * Version: 1.0
 * Date: 2025-06-06
 * Author: <EMAIL>
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-05-30   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

import android.animation.ValueAnimator;
import android.app.Activity;
import android.view.MotionEvent;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.COUIRecyclerView;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.oplus.anim.EffectiveAnimationView;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.buryingpoint.RecorderUserAction;
import com.soundrecorder.common.buryingpoint.RecorderUserActionKt;
import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache;
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatusConst;
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface;
import com.soundrecorder.modulerouter.utils.KoinInterfaceHelper;
import com.soundrecorder.record.subtitle.SubtitleMarkInsertHelper;
import com.soundrecorder.record.subtitle.data.DisplaySubtitleEntry;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

public class SubtitleDataInsertHelper {

    private static final String TAG = "SubtitleDataInsertHelper";
    private static final int THRESHOLD = 5000;
    private static final int SETREQUESTCODE = 211;
    private final List<DisplaySubtitleEntry> mTotalList = new ArrayList<>();
    private final HashSet<ConvertContentItem> mInvalidList = new HashSet<>();
    private int mLastFirstVisiblePosition = 0;
    private RenameFileDialog mRenameDialog = null;
    private boolean mIsLoadingSuccessful = false;
    private ConstraintLayout mCaptionsGradientView;
    private COUIRecyclerView mCaptionsRecyclerView;
    private LinearLayout mCaptionsLoadingView;
    private EffectiveAnimationView mCaptionsAnimationView;
    private RecordSubtitleAdapter mSubtitleAdapter;
    private SubtitleMarkInsertHelper mSubtitleMarkInsertHelper;
    private WeakReference<Activity> mActivity;
    private volatile boolean mIsOpenSubtitles = false;
    private volatile boolean mIsHoldOn = false;
    private RecorderServiceInterface mRecorderViewModelApi = KoinInterfaceHelper.INSTANCE.getRecorderViewModelApi();
    private Runnable mResetHoldOnRunnable = () -> {
        mIsHoldOn = false;
        if (mActivity == null || mSubtitleAdapter == null) {
            return;
        }
        Activity activity = mActivity.get();
        if (activity != null && !activity.isFinishing() && !activity.isDestroyed() && !mSubtitleAdapter.getDataList().isEmpty()) {
            SubtitleRecyclerViewUtils.scrollToBottom(mCaptionsRecyclerView, mSubtitleAdapter);
        }
    };

    public SubtitleDataInsertHelper(Activity context,
                                    ConstraintLayout mCaptionsGradientView,
                                    COUIRecyclerView mCaptionsRecyclerView,
                                    LinearLayout mCaptionsLoadingView,
                                    EffectiveAnimationView mCaptionsAnimationView) {
        this.mActivity = new WeakReference<>(context);
        this.mCaptionsGradientView = mCaptionsGradientView;
        this.mCaptionsRecyclerView = mCaptionsRecyclerView;
        this.mCaptionsLoadingView = mCaptionsLoadingView;
        this.mSubtitleMarkInsertHelper = new SubtitleMarkInsertHelper(); // 初始化helper
        this.mCaptionsAnimationView = mCaptionsAnimationView;
        initGradientRecyclerView();
        openSubtitles();
        initLoadingAnimation();
    }

    public List<DisplaySubtitleEntry> getmTotalList() {
        return mTotalList;
    }

    public void saveOldList(boolean realTimeSwitch) {
        if (realTimeSwitch) {
            mTotalList.clear();
            for (int i = 0; i < mSubtitleAdapter.getDataList().size(); i++) {
                DisplaySubtitleEntry e = mSubtitleAdapter.getDataList().get(i);
                mTotalList.add(new DisplaySubtitleEntry(e.getOriginContent(), e.getDisplayContent(),
                        e.getInsertedMarks(), e.isFromCompletedSubtitles(), e.getLastContent(), e.isMarked()));
            }
        }
    }

    public void addInvialList(@NonNull IRealtimeSubtitleCache cache) {
        List<ConvertContentItem> completedItems = cache.getGeneratedSubtitles();
        List<ConvertContentItem> processingItem = cache.getTemporySubtitles();
        if (!completedItems.isEmpty()) {
            for (ConvertContentItem item:completedItems) {
                if (mInvalidList.contains(item)) {
                    continue;
                }
                mInvalidList.add(item);
            }
        }
        if (!processingItem.isEmpty()) {
            for (ConvertContentItem item:processingItem) {
                if (mInvalidList.contains(item)) {
                   continue;
                }
                mInvalidList.add(item);
            }
        }
    }

    public boolean containsInList(List<DisplaySubtitleEntry> list, DisplaySubtitleEntry entry) {
        final ConvertContentItem targetContent = entry.getOriginContent();
        for (int i = 0; i < list.size(); i++) {
            final DisplaySubtitleEntry currentEntry = list.get(i);
            final ConvertContentItem originContent = currentEntry.getOriginContent();
            if (originContent.getStartTime() == targetContent.getStartTime()
                    && originContent.getTextContent().equals(targetContent.getTextContent())) {
                if (RecordSubtitleAdapter.TYPE_INTERMEDIATE.equals(originContent.getTextType())) {
                    list.set(i, entry);
                }
                return true;
            }
        }
        return false;
    }

    public void setAdapterData(List<DisplaySubtitleEntry> subtitles) {
        if (mActivity == null) {
            return;
        }
        Activity activity = mActivity.get();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }

        if (subtitles.isEmpty()) {
            showLoadingAnim(false);
            return;
        }
        if (mIsOpenSubtitles) {
            showLoadingAnim(true);
        }
        List<DisplaySubtitleEntry> subtitlesReal = new ArrayList<>(mTotalList);
        for (int i = 0; i < subtitles.size(); i++) {
            DisplaySubtitleEntry entry = subtitles.get(i);
            if (entry == null) {
                continue;
            }
            if (!mInvalidList.contains(entry.getOriginContent()) && !containsInList(mTotalList, entry)) {
                subtitlesReal.add(entry);
            }
        }
        mActivity.get().runOnUiThread(() -> {
            DebugUtil.i(TAG, "setAdapterData mIsHoldOn: " + mIsHoldOn);
            mSubtitleAdapter.setData(subtitlesReal);
            if (!mIsHoldOn) {
                SubtitleRecyclerViewUtils.scrollToBottom(mCaptionsRecyclerView, mSubtitleAdapter);
            }
        });

    }

    public void onAsrStatus(int code) {
        DebugUtil.i(TAG, "onAsrStatus: code=" + code);
        int buryingPointFailCode = -1;
        if (mSubtitleAdapter != null) {
            switch (code) {
                case RealTimeAsrStatusConst.STATUS_INIT_SUCCESS:
                    showLoadingAnim(!mSubtitleAdapter.getDataList().isEmpty());
                    DebugUtil.i(TAG, "STATUS_INIT_SUCCESS: code=" + code);
                    break;
                case RealTimeAsrStatusConst.STATUS_INIT_ERROR:
                    showLoadingAnim(false);
                    DebugUtil.i(TAG, "STATUS_INIT_ERROR: code=" + code);
                    buryingPointFailCode = RecorderUserAction.FAIL_REASON_OTHER;
                    break;
                case RealTimeAsrStatusConst.STATUS_NET_DISCONNECT:
                    mSubtitleAdapter.setNoNetWork(true);
                    onScrollToBottom();
                    buryingPointFailCode = RecorderUserAction.FAIL_REASON_NO_NETWORK;
                    break;
                case RealTimeAsrStatusConst.STATUS_SERVER_DISCONNECT:
                    mSubtitleAdapter.setNoNetWork(false);
                    onScrollToBottom();
                    buryingPointFailCode = RecorderUserAction.FAIL_REASON_SERVICE_EXCEPTION;
                    break;
                default:
                    mSubtitleAdapter.setNoNetWork(false);
                    onScrollToBottom();
                    buryingPointFailCode = RecorderUserAction.FAIL_REASON_OTHER;
                    break;
            }
        }
        DebugUtil.i(TAG, "onAsrStatus: buryingPointFailCode=" + buryingPointFailCode);
        if (buryingPointFailCode != -1) {
            int finalBuryingPointFailCode = buryingPointFailCode;
            mRecorderViewModelApi.getRealTimeSubtitleBuryingPointCallback(realTimeSubtitleInstance ->
                    realTimeSubtitleInstance.addReturnFail(finalBuryingPointFailCode));
        }
    }

    private void onScrollToBottom() {
        if (!mIsHoldOn) {
            SubtitleRecyclerViewUtils.scrollToBottom(mCaptionsRecyclerView, mSubtitleAdapter);
        }
    }

    /**
     * 录音转写功能默认关闭
     */
    private void openSubtitles() {
        openSubtitles(true);
    }

    /**
     * 录音转写功能开关
     *
     * @param isOpenSubtitles 是否开启
     */
    private void openSubtitles(boolean isOpenSubtitles) {
        mIsOpenSubtitles = isOpenSubtitles;
        if (mCaptionsGradientView == null) {
            return;
        }
        if (isOpenSubtitles) {
            mCaptionsGradientView.setVisibility(VISIBLE);
        } else {
            mCaptionsGradientView.setVisibility(GONE);
        }
    }

    private void initGradientRecyclerView() {
        DebugUtil.i(TAG, "=========>initGradientRecyclerView");
        if (mActivity == null) {
            return;
        }
        Activity activity = mActivity.get();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }
        mCaptionsRecyclerView.setLayoutManager(new LinearLayoutManager(mActivity.get()));
        mSubtitleAdapter = new RecordSubtitleAdapter(mActivity.get(), mSubtitleMarkInsertHelper);
        mSubtitleAdapter.setListener(new RecordSubtitleAdapter.OnItemClickListener() {
            public void onItemClickSpeaker(int position) {
                DebugUtil.i(TAG, "onItemClickSpeaker");
                String name = mSubtitleAdapter.getDataList().get(position).getOriginContent().getRoleName();
                showRenameDialog(name == null ? "" : name,
                        mSubtitleAdapter.getDataList().get(position).getOriginContent().getRoleId());
            }
        });
        mCaptionsRecyclerView.setAdapter(mSubtitleAdapter);
        mSubtitleAdapter.setData(new ArrayList());
    }

    private void initLoadingAnimation() {
        if (mCaptionsAnimationView == null) {
            return;
        }
        mCaptionsAnimationView.setAnimation("ic_subtitle_loading.json");
        mCaptionsAnimationView.setRepeatCount(ValueAnimator.INFINITE);
        mCaptionsAnimationView.playAnimation();
    }

    public void showLoadingAnim(boolean isLoadingSuccessful) {
        mIsLoadingSuccessful = isLoadingSuccessful;
        if (mCaptionsRecyclerView == null || mCaptionsLoadingView == null) {
            DebugUtil.i(TAG, "showLoadingAnim");
            return;
        }
        if (mIsLoadingSuccessful) {
            mCaptionsRecyclerView.setVisibility(VISIBLE);
            mCaptionsLoadingView.setVisibility(GONE);
            mCaptionsAnimationView.cancelAnimation();
        } else {
            mCaptionsRecyclerView.setVisibility(GONE);
            mCaptionsLoadingView.setVisibility(VISIBLE);
            if (!mCaptionsAnimationView.isAnimating() && mIsLoadingSuccessful) {
                mCaptionsAnimationView.playAnimation();
            }
        }
    }

    public synchronized void initRecyclerViewOnTouchEvent() {
        if (mCaptionsRecyclerView == null) {
            return;
        }
        mCaptionsRecyclerView.setOnTouchListener((v, ev) -> {
            if (ev.getAction() == MotionEvent.ACTION_DOWN || ev.getAction() == MotionEvent.ACTION_MOVE) {
                mCaptionsRecyclerView.removeCallbacks(mResetHoldOnRunnable);
                // 如果是mIsHoldOn，则直接返回
                if (mIsHoldOn) {
                    return false;
                }
                // 否则 mIsHoldOn设置为true，并停止滚动，
                mIsHoldOn = true;
                mCaptionsRecyclerView.stopScroll();
            } else if (ev.getAction() == MotionEvent.ACTION_UP || ev.getAction() == MotionEvent.ACTION_CANCEL) {
                // 延迟 5s 后滑动到最新位置
                LinearLayoutManager layoutManager = (LinearLayoutManager) mCaptionsRecyclerView.getLayoutManager();
                if (layoutManager != null) {
                    mLastFirstVisiblePosition = layoutManager.findFirstVisibleItemPosition();
                    mCaptionsRecyclerView.postDelayed(mResetHoldOnRunnable, THRESHOLD);
                }
            }
            return false;
        });
    }

    private void showRenameDialog(String text, long recordId) {
        if (mRenameDialog != null && mRenameDialog.isShowing()) {
            DebugUtil.i(TAG, "mRenameDialog is showing");
            return;
        }
        if (mActivity == null) {
            return;
        }
        Activity activity = mActivity.get();
        if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
            return;
        }

        mRenameDialog = new RenameFileDialog(mActivity.get(), RenameFileDialog.FROM_PLAYBACK_MORE, text, (displayName, path) -> {
            mRecorderViewModelApi.updateSpeakerName(Integer.parseInt(recordId + ""), displayName);
            for (int i = 0; i < mSubtitleAdapter.getDataList().size(); i++) {
                if (recordId == mSubtitleAdapter.getDataList().get(i).getOriginContent().getRoleId()) {
                    mSubtitleAdapter.getDataList().get(i).getOriginContent().setRoleName(displayName);
                    mSubtitleAdapter.notifyItemChanged(i);
                    /*讲话人编辑次数 累加*/
                    RecorderUserActionKt.sEditCount++;
                    break;
                }
            }
        }
        );

        DebugUtil.i(TAG, "mRenameDialog != null text = ： " + text);
        mRenameDialog.setRequestCode(SETREQUESTCODE);
        mRenameDialog.show();
    }

    public void onLanguageChanged(String code) {
        if (mSubtitleAdapter != null) {
            mSubtitleAdapter.setLanguageCode(code);
        }
    }

    public void release() {
        if (mResetHoldOnRunnable != null) {
            mCaptionsRecyclerView.removeCallbacks(mResetHoldOnRunnable);
            mResetHoldOnRunnable = null;
            mSubtitleAdapter = null;
        }
        if (mActivity != null && mActivity.get() != null) {
            mActivity.clear();
            mActivity = null;
        }
        if (mCaptionsGradientView != null) {
            mCaptionsGradientView = null;
        }
        if (mCaptionsRecyclerView != null) {
            mCaptionsRecyclerView = null;
        }
        if (mCaptionsLoadingView != null) {
            mCaptionsLoadingView = null;
        }
        if (mCaptionsAnimationView != null) {
            mCaptionsAnimationView.cancelAnimation();
            mCaptionsAnimationView = null;
        }
        if (mRenameDialog != null) {
            mRenameDialog.dismiss();
            mRenameDialog = null;
        }
    }
}
