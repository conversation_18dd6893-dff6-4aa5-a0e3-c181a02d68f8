/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SaveFileAlertDialog
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record.views.dialog

import android.app.Activity
import android.text.TextUtils
import android.view.View
import androidx.core.view.isVisible
import com.coui.appcompat.checkbox.COUICheckBox
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.RecorderUserActionKt
import com.soundrecorder.common.dialog.AbsEditAlertDialog
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import java.io.File

class SaveFileAlertDialog(
    private val content: String,
    private val dialogCallback: VerticalButtonDialogCallback,
    activity: Activity
) : AbsEditAlertDialog(activity) {

    companion object {
        private const val TAG = "SaveFileAlertDialog"
    }

    private var cbSummary: COUICheckBox? = null

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    private fun isExistFileName(displayName: String): Boolean {
        return if (BaseUtil.isAndroidQOrLater) {
            val relativePath = recorderViewModelApi?.getRelativePath() ?: ""
            FileUtils.isFileExist(relativePath, displayName)
        } else {
            val filePath = recorderViewModelApi?.getRecordFilePath() ?: ""
            val parent = File(filePath).parent ?: ""
            File(parent + displayName).exists()
        }
    }

    override fun onInitCustomView(customView: View) {
        getEditText()?.setHint(com.soundrecorder.common.R.string.enter_filename)
        cbSummary = customView.findViewById(com.soundrecorder.common.R.id.cb_summary)
        cbSummary?.isVisible = (summaryApi?.getSupportRecordSummaryValue()?.value == true)
    }

    override fun onSave() {
        val editTitle = getNewContent()
        if (TextUtils.isEmpty(editTitle)) {
            showTextNote(com.soundrecorder.common.R.string.error_none_filename)
            return
        }
        if (editTitle.startsWith(".")) {
            showTextNote(com.soundrecorder.common.R.string.notify_illegal_emoji_new)
            return
        }
        val displayName = editTitle + RecorderViewModelApi.getSuffix()
        if (getOriginalContent() != editTitle
            && isExistFileName(displayName)) {
            showTextNote(com.soundrecorder.common.R.string.error_title)
            return
        }
        val nameEdited = if (isTitleChange()) "1" else "0"
        DebugUtil.i(TAG, "onSave: displayName = $displayName, nameEdited = $nameEdited")
        BuryingPoint.addRecordMode(RecorderViewModelApi.getRecordType().toString(), nameEdited)
        BuryingPoint.addRecordType(RecorderViewModelApi.getRecordType())
        BuryingPoint.addRecordDuration(RecorderViewModelApi.getAmplitudeCurrentTime())
        BuryingPoint.addClickSaveRecord(RecorderUserAction.VALUE_SAVE_RECORD_DIALOG_SAVE)
        dialogCallback.save(displayName, content + RecorderViewModelApi.getSuffix())
        dismiss()
    }

    override fun onCancel() {
        BuryingPoint.addClickSaveRecord(RecorderUserAction.VALUE_SAVE_RECORD_DIALOG_CANCEL)
        /* 完整录制音频 返回值 cancel */
        RecorderUserActionKt.sSaveRecord = RecorderUserAction.VALUE_RETURN_TYPE_CANCEL
        dismiss()
        dialogCallback.onCancel()
    }

    override fun getOriginalContent() = content

    override fun onWindowDetached() {
        dialogCallback.disableAllClickViews(true)
        super.onWindowDetached()
    }

    fun isSummaryChecked(): Boolean {
        return cbSummary?.isChecked ?: false
    }

    fun isSummaryVisible(): Boolean {
        return cbSummary?.isVisible ?: false
    }
}