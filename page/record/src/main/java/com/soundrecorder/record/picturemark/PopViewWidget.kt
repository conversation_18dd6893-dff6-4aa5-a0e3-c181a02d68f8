/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PopViewWidget
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record.picturemark

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.view.accessibility.AccessibilityEvent
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.utils.ViewUtils.dp2px
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.imageload.ImageLoaderUtils
import com.soundrecorder.imageload.ImageLoaderUtils.into
import com.soundrecorder.record.R

class PopViewWidget(val mContext: Context) : FrameLayout(mContext) {

    companion object {
        private const val TAG = "PictureRecommendPopupWidget"
        private const val TOP_MARGIN = 11f
    }

    private var popUp: ConstraintLayout? = null
    private var popUpLayoutParams: LayoutParams? = null
    private var tvPictureNum: TextView? = null
    private val pathInterpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
    private val popPictures = mutableListOf<PopPicture>()
    private val imageLoadData = ImageLoadData("", 0, 0)

    private val popUpAnimatorSet by lazy {
        AnimatorSet().apply {
            interpolator = pathInterpolator
            playTogether(
                ObjectAnimator.ofFloat(popUp, View.SCALE_X, 0f, 1f).apply {
                    duration = 250L
                    pivotX = 0.5f
                    pivotY = 1.0f
                },
                ObjectAnimator.ofFloat(popUp, View.SCALE_Y, 0f, 1f).apply {
                    duration = 250L
                    pivotX = 0.5f
                    pivotY = 1.0f
                },
                ObjectAnimator.ofFloat(popUp, View.ALPHA, 0f, 1f).apply {
                    duration = 250L
                },
                ObjectAnimator.ofFloat(tvPictureNum!!, View.SCALE_X, 0f, 1f).apply {
                    pivotY = 0.5f
                    pivotX = 0.5f
                    duration = 520L
                },
                ObjectAnimator.ofFloat(tvPictureNum!!, View.SCALE_Y, 0f, 1f).apply {
                    pivotY = 0.5f
                    pivotX = 0.5f
                    duration = 520L
                }
            )
        }
    }

    init {
        visibility = View.INVISIBLE
        popUp = inflate(
            mContext,
            R.layout.layout_popup_picture_recommendation,
            null
        ) as ConstraintLayout
        popUpLayoutParams = LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT)
        addView(popUp, popUpLayoutParams)
        tvPictureNum = popUp?.findViewById(R.id.tv_picture_num)
        popUp?.accessibilityDelegate = object : View.AccessibilityDelegate() {
            override fun sendAccessibilityEvent(host: View, eventType: Int) {
                if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                    popUp?.contentDescription =
                        mContext.resources.getString(com.soundrecorder.common.R.string.photo_mark_recommend_popup_insert)
                }
                super.sendAccessibilityEvent(host, eventType)
            }
        }
        this.setOnClickListener {
            exitAndRemoveSelf()
        }
    }

    /** 图片标记代码后续str5若不需要可删除
    // refreshing popup location according to the photo mark btn
    fun setPopUpLocation(anchorView: View) {
        (parent as ViewGroup).post {
            DebugUtil.i(TAG, "height: $measuredHeight  width: $measuredWidth")
            DebugUtil.i(
                TAG,
                "popup height: ${popUp?.measuredHeight} popup width: ${popUp?.measuredWidth}"
            )
            val popUpWidth = popUp?.measuredWidth ?: 0
            val popUpHeight = popUp?.measuredHeight ?: 0
            val customButtonView = anchorView.findViewById<CustomButtonView>(R.id.ib_mark_photo)
            popUpLayoutParams?.apply {
                gravity = Gravity.TOP or Gravity.START
                topMargin = anchorView.top + dp2px(TOP_MARGIN).toInt() - popUpHeight
                marginStart = if (BaseApplication.sIsRTLanguage) {
                    measuredWidth - anchorView.left - ((customButtonView.measuredWidth + popUpWidth) / 2)
                } else {
                    anchorView.left + customButtonView.measuredWidth / 2 - popUpWidth / 2
                }
            }
            popUp?.layoutParams = popUpLayoutParams
            visibility = View.VISIBLE
        }
    }*/

    fun setPopPictures(popPictures: List<PopPicture>) {
        this.popPictures.clear()
        this.popPictures.addAll(popPictures)
        val imageView = findViewById<ImageView>(R.id.iv_picture_recommendation)
        val src = popPictures.last()
        imageLoadData.src = src.data
        imageLoadData.width = dp2px(30f).toInt()
        imageLoadData.height = imageLoadData.width
        imageView.into(imageLoadData)
    }

    fun setPopUpOnClickListener(listener: OnClickListener) {
        popUp?.setOnClickListener(listener)
    }

    // set red dot visible and picture num
    fun setRedDotNum(num: Int) {
        tvPictureNum?.apply {
            if (num <= 1) {
                visibility = View.GONE
            } else {
                text = num.toString()
                bringToFront()
                visibility = View.VISIBLE
            }
        }
    }

    //todo run enter animation when attach to window
    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        DebugUtil.i(TAG, "onAttachedToWindow")
        if (popUpAnimatorSet.isRunning) popUpAnimatorSet.cancel()
        popUpAnimatorSet.start()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        popPictures.lastOrNull()?.data?.let {
            ImageLoaderUtils.clearMemoryCacheByKey(imageLoadData)
        }
        popUpAnimatorSet.cancel()
        popUp = null
    }

    //todo run exit animation when detach from window
    fun exitAndRemoveSelf() {
        DebugUtil.i(TAG, "onDetachedFromWindow")
        popUpAnimatorSet.apply {
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    if (parent != null) {
                        (parent as ViewGroup).removeView(this@PopViewWidget)
                    }
                    popUpAnimatorSet.removeAllListeners()
                }

                override fun onAnimationCancel(animation: Animator) {
                    popUpAnimatorSet.removeAllListeners()
                }
            })
            reverse()
        }
    }
}