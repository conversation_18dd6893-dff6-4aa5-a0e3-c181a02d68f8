/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PictureSelectActivity
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/12/22 1.0 create
 */

package com.soundrecorder.record.picturemark.view

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.children
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.ViewUtils.doOnLayoutChange
import com.soundrecorder.common.utils.ViewUtils.dp2px
import com.soundrecorder.modulerouter.recorder.PAUSED
import com.soundrecorder.modulerouter.recorder.RECORDING
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.recorder.SaveFileState
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.record.R
import com.soundrecorder.record.databinding.ActivityPictureSelectBinding
import com.soundrecorder.record.picturemark.PopPicture

class PictureSelectActivity : BaseActivity() {

    companion object {
        val MIN_IMAGE_WIDTH = dp2px(100f, false).toInt()
        val MARGIN = dp2px(22f, false).toInt()
        val ITEM_MARGIN = dp2px(2f, false).toInt()
        private const val TAG = "PictureSelectActivity"
        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "SelectPicture"
    }
    private var mItemSelectAllMenuItem: MenuItem? = null
    private val mAdapter: PopPicturesAdapter by lazy { PopPicturesAdapter() }
    private val mViewModel by lazy { ViewModelProvider(this)[PictureSelectViewModel::class.java] }
    private lateinit var mBinding: ActivityPictureSelectBinding

    private val mOnItemClickListener = object : PopPicturesAdapter.OnItemClickListener {
        override fun onClick(position: Int) {
            val canMark = mViewModel.checkTimeDuplicate(position)
            mAdapter.setCheckBoxState(position, canMark)
            if (canMark) mViewModel.addOrRemovePicture(position)
        }
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_picture_select)
        setContentView(mBinding.root)
        initView()
        initData()
        checkNeedRestore()
        doOnApplyWindowInsets()
        if (savedInstanceState != null) {
            val isRecorderServiceRunning = (recorderViewModelApi?.getCurrentStatus() ?: SaveFileState.INIT) in intArrayOf(
                RECORDING,
                PAUSED
            )
            if (!isRecorderServiceRunning) {
                finish()
                overridePendingTransition(0, 0)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mBinding.rvPopPictures.checkContentFull()
    }

    override fun onDestroy() {
        super.onDestroy()
        mAdapter.clearCache()
    }

    private fun initView() {
        initActionBar()
        mBinding.rvPopPictures.apply {
            updateLayoutParams<MarginLayoutParams> {
                marginStart = MARGIN
                marginEnd = MARGIN
            }
            layoutManager =
                GridLayoutManager(this@PictureSelectActivity, GridLayoutManager.DEFAULT_SPAN_COUNT)
            (itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
            adapter = mAdapter
            updateWidthAndSpanCount()
        }
        COUIChangeTextUtil.adaptFontSize(
            mBinding.btnAdd,
            resources.configuration?.fontScale?.toInt() ?: 1
        )
        mBinding.btnAdd.setOnClickListener {
            val intent = Intent().apply {
                putParcelableArrayListExtra(
                    "selectedPhotos",
                    ArrayList(mViewModel.pictureSelectedList.value ?: arrayListOf())
                )
            }
            mAdapter.clearCache()
            setResult(RESULT_OK, intent)
            finish()
        }
        mAdapter.setOnItemClickListener(mOnItemClickListener)

        mBinding.rootView.doOnLayoutChange { _, _, _ ->
            updateWidthAndSpanCount()
        }
    }

    private fun updateWidthAndSpanCount() {
        val row = getPhotoItemWidthAndRow()
        (mBinding.rvPopPictures.layoutManager as? GridLayoutManager)?.spanCount = row
        mAdapter.notifyDataSetChanged()
        DebugUtil.d(TAG, "updateWidthAndSpanCount spanCount == $row")
    }

    private fun initActionBar() {
        mBinding.toolbar.apply {
            inflateMenu(R.menu.menu_picture_select)
            title = getString(com.soundrecorder.common.R.string.choose_item)
            isTitleCenterStyle = true
            mItemSelectAllMenuItem = menu.findItem(R.id.item_select_all)
            menu.children.forEach {
                it.setOnMenuItemClickListener { menuItem ->
                    onOptionsItemSelected(menuItem)
                }
            }
        }
    }

    @Suppress("DEPRECATION")
    private fun initData() {
        mViewModel.apply {
            if (marks.isEmpty()) {
                val marks =
                    intent?.extras?.getParcelableArrayList<MarkDataBean>("marks") as? ArrayList<MarkDataBean>
                this.marks = marks ?: arrayListOf()
            }
            if (popPictures.isEmpty()) {
                val popPictures =
                    intent?.extras?.getParcelableArrayList<PopPicture>("popPictures") as? ArrayList<PopPicture>
                this.popPictures = popPictures ?: arrayListOf()
            }
            mAdapter.setPopPictures(mViewModel.popPictures, mViewModel.getDuplicateItems())
        }
        mViewModel.pictureSelectedList.observe(this) { selectedList ->
            val size = selectedList.size
            if (size >= mViewModel.maxMarkLimit) {
                mAdapter.disableUnselectItems()
            } else {
                mAdapter.enableAllItems()
            }
            when (size) {
                0 -> {
                    mBinding.toolbar.title = getString(com.soundrecorder.common.R.string.choose_item)
                    mViewModel.buttonEnable.value = false
                    mItemSelectAllMenuItem?.setTitle(com.soundrecorder.common.R.string.select_all)
                }
                mViewModel.popPictures.size -> {
                    mBinding.toolbar.title = getString(com.soundrecorder.common.R.string.item_select, selectedList.size)
                    mViewModel.buttonEnable.value = true
                    mItemSelectAllMenuItem?.setTitle(com.soundrecorder.common.R.string.record_delete_all_cancel)
                }
                else -> {
                    mBinding.toolbar.title = getString(com.soundrecorder.common.R.string.item_select, selectedList.size)
                    mViewModel.buttonEnable.value = true
                    mItemSelectAllMenuItem?.setTitle(com.soundrecorder.common.R.string.select_all)
                }
            }
        }
        mViewModel.buttonEnable.observe(this) { enable ->
            mBinding.btnAdd.isEnabled = enable
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.item_cancel) {
            finish()
        } else {
            if (mViewModel.hasDuplicateItems) {
                ToastManager.showLongToast(com.soundrecorder.common.R.string.photo_mark_recommend_unsupport_select_all)
            } else {
                val selectAll = mViewModel.pictureSelectedList.value?.size != mAdapter.itemCount
                mViewModel.isSelectAll = selectAll
                mAdapter.selectAllOrNone(
                    mViewModel.isSelectAll,
                    mViewModel.pictureSelectedList.value ?: arrayListOf()
                )
            }
        }

        return true
    }

    private fun checkNeedRestore() {
        if (mViewModel.pictureSelectedList.value.isNullOrEmpty()) return
        mAdapter.restoreSelected(mViewModel.pictureSelectedList.value ?: arrayListOf())
    }

    private fun RecyclerView.checkContentFull() {
        post {
            mBinding.bottomDivider.isVisible = if (childCount <= 0) {
                false
            } else {
                val manager = layoutManager as GridLayoutManager
                val lastPosition = manager.findLastVisibleItemPosition()
                val lastView = manager.findViewByPosition(lastPosition)
                DebugUtil.d("$TAG-checkContentFull", "$height -- ${lastView?.bottom ?: 0}")
                height <= (lastView?.bottom ?: 0)
            }
        }
    }

    // 根据屏幕宽度、图片Item宽度、图片列表边距计算GridLayoutManager一行的Item个数以及对称分布的每个Item的左右边距, Item最小为100dp
    private fun getPhotoItemWidthAndRow(): Int {
        val appScreenWidth = ScreenUtil.getRealScreenWidth() - 2 * MARGIN
        val minWidth = MIN_IMAGE_WIDTH + ITEM_MARGIN * 2
        return appScreenWidth / minWidth
    }

    /**
     * 设置到状态栏的间隔
     */
    private fun doOnApplyWindowInsets() {
        val window = window
        if (window != null) {
            WindowCompat.setDecorFitsSystemWindows(window, false)
            val decorView = window.decorView
            ViewCompat.setOnApplyWindowInsetsListener(decorView) { _, insets ->
                val systemBar = insets.getInsetsIgnoringVisibility(
                    WindowInsetsCompat.Type.systemBars()
                )
                DebugUtil.i("$TAG-doOnApplyWindowInsets", systemBar.toString())
                mBinding.rootView.setPadding(
                    systemBar.left,
                    systemBar.top,
                    systemBar.right,
                    systemBar.bottom
                )
                WindowInsetsCompat.CONSUMED
            }
        }
    }
}