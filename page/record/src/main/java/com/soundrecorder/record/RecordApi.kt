/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordApi
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.record

import android.app.Activity
import android.content.Context
import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.R
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.task.RecordRouterManager
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerData
import com.soundrecorder.modulerouter.recorder.RecordInterface
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.record.picturemark.PopPicture
import com.soundrecorder.record.picturemark.PopViewLoadingActivity.Companion.startPopViewLoadingActivity
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.manager.AudioModeChangeManager
import com.soundrecorder.recorderservice.manager.RecordStatusManager
import oplus.multimedia.soundrecorder.BreenoStartRecordUtil

object RecordApi : RecordInterface {

    private const val TAG = "RecorderApi"

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    override fun createRecorderIntent(context: Context, isStatusBar: Boolean): Intent {
        val jumpIntent = Intent()
        jumpIntent.setClass(context, RecorderActivity::class.java)
        jumpIntent.putExtra(RecorderDataConstant.PLAYBACK_VIEWER_EXTRA_STATUSBAR, isStatusBar)
        return jumpIntent
    }

    override fun canStartRecord(checkIsCall: Boolean, comeFrom: String?): Boolean {
        if (checkIsCall && !AudioModeChangeManager.checkModeCanRecord()) {
            return false
        }

        val isAlreadyRecording = RecordStatusManager.isAlreadyRecording()
        val intercept = RecordRouterManager.instance?.interceptStartRecord(comeFrom ?: "") ?: false
        DebugUtil.e(
            TAG,
            "startRecordActivity isAlreadyRecording：$isAlreadyRecording comFrom：$comeFrom intercept：$intercept"
        )
        if (isAlreadyRecording && intercept) {
            ToastManager.showShortToast(BaseApplication.getAppContext(), R.string.record_conflict)
            return false
        }

        return true
    }

    override fun isRecorderActivity(context: Context): Boolean = context is RecorderActivity

    override fun getRecorderActivityClass(): Class<out Activity> {
        return RecorderActivity::class.java
    }

    override fun removeRecordIdWhenRecordComplete(id: String?, keySet: String?): Boolean {
        return RecorderService.removeRecordIdWhenRecordComplete(id, keySet)
    }

    override fun sendStartForegroundServiceBroadCast() {
        BreenoStartRecordUtil.sendStartForegroundServiceBroadCast()
    }

    override fun sendBrowseFrontToRecordBroadCast() {
        BreenoStartRecordUtil.sendBrowseFrontToRecordBroadCast()
    }

    override fun sendStartOtherPageFailedBroadCast() {
        BreenoStartRecordUtil.sendStartOtherPageFailedBroadCast()
    }

    override fun doCheckExceptionWhenStartRecordFromBreno(): Boolean {
        return BreenoStartRecordUtil.doCheckExceptionWhenStartRecordFromBreeno()
    }

    /**
     * 获取录音照片与视频权限弹窗是否提示
     */
    override fun getShowReadImagePermissionTips(): Boolean {
        return RecordImagesPermissionTips.mShowReadImagePermissionTips
    }

    /**
     * 设置录音照片与视频权限弹窗已经弹出过
     */
    override fun setShowReadImagePermissionTips(show: Boolean) {
        RecordImagesPermissionTips.mShowReadImagePermissionTips = show
    }

    override fun createNotificationIntent(context: Context): Intent? {
        if (recorderViewModelApi?.getCurrentStatus() == RecordStatusManager.RECORDING ||
            recorderViewModelApi?.getCurrentStatus() == RecordStatusManager.PAUSED
        ) {
            val jumpIntent = Intent()
            jumpIntent.setClass(context, RecorderActivity::class.java)
            return jumpIntent
        } else {
            return browseFileApi?.createBrowseFileIntent(context)
        }
    }

    override fun <T, R : PhotoViewerData> startPopViewLoadingActivity(
        activity: AppCompatActivity,
        result: ArrayList<R>,
        doMultiPictureMark: (ArrayList<T>) -> Int,
        doCompleteMultiPictureMark: () -> Unit,
        requestCode: Int,
    ) {
        activity.startPopViewLoadingActivity(
            result as ArrayList<PopPicture>,
            doMultiPictureMark as (ArrayList<MarkMetaData>) -> Int,
            doCompleteMultiPictureMark,
            requestCode
        )
    }
}