/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - RecordSubtitleAdapter.kt
 * Description:
 *     The helper class for ui showing subtitle in record pat.
 *
 * Version: 1.0
 * Date: 2025-05-30
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-05-30   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.record

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Typeface
import android.os.Handler
import android.os.Looper
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.text.style.StyleSpan
import android.text.style.SuperscriptSpan
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.graphics.ColorUtils
import androidx.recyclerview.widget.RecyclerView
import com.oplus.anim.EffectiveAnimationView
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.widget.COUIAnimateTextView
import com.soundrecorder.modulerouter.recorder.PAUSED
import com.soundrecorder.modulerouter.recorder.RECORDING
import com.soundrecorder.record.subtitle.SubtitleMarkInsertHelper
import com.soundrecorder.record.subtitle.data.DisplayMark
import com.soundrecorder.record.subtitle.data.DisplaySubtitleEntry
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.concurrent.ConcurrentHashMap

class RecordSubtitleAdapter(var context: Context, private val subtitleMarkInsertHelper: SubtitleMarkInsertHelper) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    private var dataList = mutableListOf<DisplaySubtitleEntry>()
    private var listener: OnItemClickListener? = null
    private var holderNow: ViewHolderDataNow? = null
    private var oldDataSize = 0
    private var speakerColors = context.resources.getIntArray(R.array.speakerColor)
    private var speakerColorsIndex = -1
    private var speakerStyleHashMap = ConcurrentHashMap<Int, Int>()
    private var mainHandler: Handler = Handler(Looper.getMainLooper())
    var languageCode = RecorderViewModelApi.getCurSelectedLanguage()
    private var launchTask: Job? = null
    var isFromDataToNow = false
    var isPaused = false

    /**
     * 处理暂停时的转换逻辑
     */
    fun handlePauseConversion(state: Int) {
        if (state == PAUSED) {
            launchTask?.cancel()
            holderNow?.let { holder ->
                isPaused = true
                // 找到对应的position并转换textType
                val position = dataList.indexOfFirst { it.originContent.textType == TYPE_INTERMEDIATE }
                if (position != -1) {
                    val pauseTask = Runnable {
                        dataList[position].originContent.textType = TYPE_VAD_FINAL
                        notifyItemChanged(position)
                    }
                    if (holder.textAnimationIsRunning()) {
                        holder.setTextAnimationCallback(pauseTask)
                    } else {
                        pauseTask.run()
                    }
                    isFromDataToNow = true
                }
            }
        } else if (state == RECORDING) {
            isPaused = false
        }
    }

    companion object {
        const val TYPE_DATA_OK = 1
        const val TYPE_DATA_NOW = 2
        const val TYPE_NO_NET = 3
        const val TYPE_NO_SERVICE = 4
        const val TYPE_NO_DATA = -1
        private const val TIME_ZERO = 0
        private const val TIME_HOUR_24 = 24
        private const val TIME_HOUR = 3600000
        private const val FLAG_SIZE = 0.7f
        const val TAG = "RecordSubtitleAdapter"
        const val TYPE_VAD_FINAL = "VAD_FINAL"
        const val TYPE_INTERMEDIATE = "INTERMEDIATE"
        const val TYPE_NOT_NET = "TYPE_NO_NET"
        const val TYPE_NOT_SERVICE = "TYPE_NO_SERVICE"
        private val TIME_FORMATTER_HH_MM_SS = DateTimeFormatter.ofPattern("HH:mm:ss")
        private val TIME_FORMATTER_00_MM_SS = DateTimeFormatter.ofPattern("00:mm:ss")
        private const val INVALID_TIME_FORMAT_LONG = "--:--:--"
        private const val INVALID_TIME_FORMAT_SHORT = "--:--"
        private const val ICON_MARK_FLAG = "\u2691"
        private const val ICON_INTERMEDIATE_MARK_FLAG = "\u0007"
        private const val TASH_DELAY_TIME = 10000L
        private const val ALPHA_ANIMATION_DURATION = 350L
        private const val SPEAKER_BACKGROUND_ALPHA = 255 * 0.1

        @JvmStatic
        private fun formatTime(time: Long): String {
            return try {
                val duration = java.time.Duration.ofMillis(time)
                if (duration.toHours() > TIME_ZERO) {
                    TIME_FORMATTER_HH_MM_SS.format(Instant.ofEpochMilli(time).atZone(ZoneId.of("UTC"))
                            .withHour(duration.toHours().toInt() % TIME_HOUR_24))
                } else {
                    TIME_FORMATTER_00_MM_SS.format(Instant.ofEpochMilli(time).atZone(ZoneId.of("UTC")))
                }
            } catch (e: IllegalArgumentException) {
                DebugUtil.e(TAG, "Invalid timestamp format: ${e.message}")
                if (time >= TIME_HOUR) INVALID_TIME_FORMAT_LONG else INVALID_TIME_FORMAT_SHORT
            } catch (e: ArithmeticException) {
                DebugUtil.e(TAG, "Timestamp calculation error: ${e.message}")
                if (time >= TIME_HOUR) INVALID_TIME_FORMAT_LONG else INVALID_TIME_FORMAT_SHORT
            } catch (e: UnsupportedOperationException) {
                DebugUtil.e(TAG, "Unsupported date operation: ${e.message}")
                if (time >= TIME_HOUR) INVALID_TIME_FORMAT_LONG else INVALID_TIME_FORMAT_SHORT
            }
        }
    }

    class ViewHolderData(itemView: View, private val context: Context, private val subtitleMarkInsertHelper: SubtitleMarkInsertHelper) :
        RecyclerView.ViewHolder(itemView) {
        val speaker: TextView = itemView.findViewById(R.id.tv_speaker)
        val speakerPortrait: ImageView = itemView.findViewById(R.id.speaker_portrait)
        val dataSpeaker: ConstraintLayout = itemView.findViewById(R.id.data_speaker)
        val llSpeaker: LinearLayout = itemView.findViewById(R.id.ll_speaker)
        val startTime: TextView = itemView.findViewById(R.id.start_time)
        val subtitle: TextView  = itemView.findViewById(R.id.item_content)
        val speakerBackgroundAnimation: EffectiveAnimationView  =
            itemView.findViewById<EffectiveAnimationView?>(R.id.speaker_background_animation).apply {
                setAnimation(com.soundrecorder.common.R.raw.speaker_background_loading)
                repeatCount = ValueAnimator.INFINITE
        }
        val speakerPortraitAnimation: EffectiveAnimationView =
            itemView.findViewById<EffectiveAnimationView?>(R.id.speaker_portrait_animation).apply {
                setAnimation(com.soundrecorder.common.R.raw.speaker_portrait_loading)
                repeatCount = ValueAnimator.INFINITE
        }
        val llSpeakerAnimation: LinearLayout  = itemView.findViewById(R.id.ll_speaker_animation)
        val tvSpeakerAnimation: TextView  = itemView.findViewById(R.id.tv_speaker_animation)

        init {
            subtitle.gravity = if (itemView.layoutDirection == View.LAYOUT_DIRECTION_RTL) {
                Gravity.RIGHT
            } else {
                Gravity.LEFT
            }
        }

        @SuppressLint("ResourceAsColor")
        fun setSpeakerStyle(backgroundColor: Int) {
            if (backgroundColor != -1) {
                val portraitDrawable =
                    itemView.context.resources.getDrawable(R.drawable.speaker_portrait_picture)
                portraitDrawable.setTint(backgroundColor)
                speakerPortrait.background = portraitDrawable
                val backgroundDrawable =
                    itemView.context.resources.getDrawable(R.drawable.background_speaker)
                // 设置背景颜色为肖像颜色的0.15透明度，使背景更明显
                val transparentColor = ColorUtils.setAlphaComponent(backgroundColor, SPEAKER_BACKGROUND_ALPHA.toInt())
                backgroundDrawable.setTint(transparentColor)
                llSpeaker.background = backgroundDrawable
            }
        }

        fun updateData(data: DisplaySubtitleEntry, position: Int, listener: OnItemClickListener?) {
            speaker.text = data.originContent.roleName
            DebugUtil.e(TAG, "Unsupported speaker: ${speaker.text}")
            if (data.originContent.roleId == -1
                || data.originContent.roleName == null
            ) {
                dataSpeaker.visibility = View.GONE
            }
            speaker.setOnClickListener {
                listener?.onItemClickSpeaker(position)
            }
            startTime.text = formatTime(data.originContent.startTime)
            subtitle.text = if (data.displayContent.isNotEmpty()) {
                data.displayContent
                } else {
                data.originContent.textContent
                }
            if (data.isMarked) {
                subtitle.setText(createMarkedSpannable(data), TextView.BufferType.SPANNABLE)
            }
        }

        private fun buildAlphaAnimation(view: View, startValue: Float, endValue: Float): ObjectAnimator {
            return ObjectAnimator.ofFloat(view, "alpha", startValue, endValue).apply {
                duration = ALPHA_ANIMATION_DURATION
                interpolator = LinearInterpolator()
                start()
            }
        }

        fun startSpeakingAnimation() {
            // 当ViewHolderDataNow转换为ViewHolderData时的动画
            tvSpeakerAnimation.text = itemView.resources.getString(com.soundrecorder.base.R.string.recognizing_speaker)
            llSpeaker.alpha = 1f
            llSpeaker.visibility = View.VISIBLE
            llSpeakerAnimation.visibility = View.VISIBLE
            speakerBackgroundAnimation.visibility = View.VISIBLE
            speakerPortraitAnimation.visibility = View.VISIBLE
            speakerPortraitAnimation.playAnimation()
            speakerBackgroundAnimation.playAnimation()
            buildAlphaAnimation(llSpeakerAnimation, 1f, 0f).addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    speakerPortraitAnimation.cancelAnimation()
                    llSpeakerAnimation.visibility = View.GONE
                }
            })
            buildAlphaAnimation(speakerBackgroundAnimation, 1f, 0f).addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    speakerBackgroundAnimation.cancelAnimation()
                    speakerBackgroundAnimation.visibility = View.GONE
                }
            })
            buildAlphaAnimation(llSpeaker, 0f, 1f).addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    llSpeakerAnimation.visibility = View.GONE
                }
            })
        }

        fun inVisiableAnimation() {
            speakerBackgroundAnimation.visibility = View.GONE
            llSpeakerAnimation.visibility = View.GONE
        }


        private fun createMarkedSpannable(data: DisplaySubtitleEntry): SpannableStringBuilder {
            val spannable = SpannableStringBuilder(subtitle.text)
            val mHighlightColor = COUIContextUtil.getColor(context, com.support.appcompat.R.color.coui_color_blue)

            data.insertedMarks.forEach { mark ->
                val text = subtitle.text.toString()
                var start = mark.insertPosition
                var end = mark.insertPosition

                // 查找句子边界
                while (start > 0 && !subtitleMarkInsertHelper.isPunctuationChar(text[start - 1], text, start - 1)) {
                    start--
                }
                while (end < text.length && !subtitleMarkInsertHelper.isPunctuationChar(text[end], text, end)) {
                    end++
                }

                // 插入小旗子
                if (end < text.length && subtitleMarkInsertHelper.isPunctuationChar(text[end], text, end)) {
                    val flagSpan = SpannableStringBuilder(ICON_MARK_FLAG).apply {
                        setSpan(SuperscriptSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        setSpan(RelativeSizeSpan(FLAG_SIZE), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        setSpan(ForegroundColorSpan(mHighlightColor), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    }
                    spannable.insert(end, flagSpan)
                }

                // 设置高亮样式
                spannable.setSpan(ForegroundColorSpan(mHighlightColor), start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                spannable.setSpan(StyleSpan(Typeface.NORMAL), start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            return spannable
        }
    }
    class ViewHolderDataNow(itemView: View, private val context: Context, private val subtitleMarkInsertHelper: SubtitleMarkInsertHelper) :
        RecyclerView.ViewHolder(itemView) {
        val speaker: TextView = itemView.findViewById(R.id.tv_speaker)
        val tvSpeakerAnimation: TextView = itemView.findViewById(R.id.tv_speaker_animation)
        private val llSpeaker: LinearLayout = itemView.findViewById(R.id.ll_speaker)
        private val llSpeakerAnimation: LinearLayout = itemView.findViewById(R.id.ll_speaker_animation)
        private val speakerPortrait: EffectiveAnimationView =
            itemView.findViewById<EffectiveAnimationView?>(R.id.speaker_portrait_animation).apply {
                setAnimation(com.soundrecorder.common.R.raw.speaker_portrait_loading)
                repeatCount = ValueAnimator.INFINITE
        }
        private val speakerBackgroundAnimation: EffectiveAnimationView =
            itemView.findViewById<EffectiveAnimationView?>(R.id.speaker_background_animation).apply {
                setAnimation(com.soundrecorder.common.R.raw.speaker_background_loading)
                repeatCount = ValueAnimator.INFINITE
        }
        private val dataSpeaker: ConstraintLayout = itemView.findViewById(R.id.data_speaker)
        private val startTime: TextView = itemView.findViewById(R.id.start_time)
        private val subtitle: COUIAnimateTextView = itemView.findViewById(R.id.item_content)

        init {
            subtitle.gravity = if (itemView.layoutDirection == View.LAYOUT_DIRECTION_RTL) {
                Gravity.RIGHT
            } else {
                Gravity.LEFT
            }
        }

        /**
         * 启动当前讲话人的JSON动效（第一次出现）
         */
        fun startSpeakingAnimation() {
            speakerBackgroundAnimation.alpha = 0f
            llSpeakerAnimation.alpha = 0f
            speakerBackgroundAnimation.playAnimation()
            speakerPortrait.playAnimation()
            buildAlphaAnimation(speakerBackgroundAnimation, 0f, 1f)
            buildAlphaAnimation(llSpeakerAnimation, 0f, 1f)
        }

        @SuppressLint("ResourceAsColor")
        fun setSpeakerStyle(backgroundColor: Int) {
            if (backgroundColor != -1) {
                val portraitDrawable =
                    itemView.context.resources.getDrawable(R.drawable.speaker_portrait_picture)
                portraitDrawable.setTint(backgroundColor)
                speakerPortrait.background = portraitDrawable
                val backgroundDrawable =
                    itemView.context.resources.getDrawable(R.drawable.background_speaker)
                // 设置背景颜色为肖像颜色的0.15透明度，使背景更明显
                val transparentColor = ColorUtils.setAlphaComponent(backgroundColor, SPEAKER_BACKGROUND_ALPHA.toInt())
                backgroundDrawable.setTint(transparentColor)
                llSpeaker.background = backgroundDrawable
            }
        }

        /**
         * 从ViewHolderData变为ViewHolderDataNow时的动画
         * 讲话人布局淡出，JSON动画淡入
         */
        fun startSpeakingAnimationFromData() {
            // 1. 讲话人布局淡出（如果有的话，这里需要从外部传入或者通过其他方式处理）
            llSpeaker.visibility = View.VISIBLE
            buildAlphaAnimation(llSpeaker, 1f, 0f).addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    llSpeaker.visibility = View.GONE
                }
            })
            // 2. JSON动画淡入
            speakerBackgroundAnimation.alpha = 0f
            llSpeakerAnimation.alpha = 0f
            speakerBackgroundAnimation.playAnimation()
            speakerPortrait.playAnimation()
            buildAlphaAnimation(speakerBackgroundAnimation, 0f, 1f)
            buildAlphaAnimation(llSpeakerAnimation, 0f, 1f)
        }

        private fun buildAlphaAnimation(view: View, startValue: Float, endValue: Float): ObjectAnimator {
            return ObjectAnimator.ofFloat(view, "alpha", startValue, endValue).apply {
                duration = ALPHA_ANIMATION_DURATION
                interpolator = LinearInterpolator()
                start()
            }
        }

        /**
         * 更新数据
         * @param data 数据
         * @param position 位置
         * @param languageCode 语言代码
         * @param isNewParagraph 是否新的段落
         * @param listener 监听器
         * @param isRecognizing 是否正在识别中
         */
        fun updateData(
            data: DisplaySubtitleEntry,
            position: Int,
            languageCode: String,
            isNewParagraph: Boolean,
            listener: OnItemClickListener?,
            isRecognizing: Boolean = false
        ) {
            speaker.text = data.originContent.roleName
            tvSpeakerAnimation.text = itemView.resources.getString(com.soundrecorder.base.R.string.recognizing_speaker)
            DebugUtil.e(TAG, "Unsupported speaker: ${speaker.text}")
            if (data.originContent.roleId == -1
                || data.originContent.roleName == null
            ) {
                dataSpeaker.visibility = View.GONE
            }

            startTime.text = formatTime(data.originContent.startTime)
            val content = if (data.displayContent.isNotEmpty()) {
                data.displayContent
            } else {
                data.originContent.textContent
            }
            subtitle.setSubtitleAnimateText(
                if (!data.isFromCompletedSubtitles && data.isMarked) createMarkedNowSpannable(data, content) else content,
                languageCode,
                isNewParagraph
            )
        }

        fun textAnimationIsRunning(): Boolean {
            return subtitle.triggerAnimatorIsRunning()
        }

        fun setTextAnimationCallback(delayTask: Runnable) {
            subtitle.setTriggerAnimatorEndCallback(delayTask)
        }

        private fun createMarkedNowSpannable(data: DisplaySubtitleEntry, content: String): SpannableStringBuilder {
            val spannable = SpannableStringBuilder(content)
            val mHighlightColor = COUIContextUtil.getColor(context, com.support.appcompat.R.color.coui_color_blue)
            var totalOffset = 0

            data.insertedMarks
                .sortedByDescending { it.insertPosition }
                .forEach { mark ->
                    val adjustedPosition = mark.insertPosition + totalOffset
                    val range = subtitleMarkInsertHelper.calculateHighlightRangeForMark(
                        content,
                        adjustedPosition
                    )
                    if (range.first < content.length && range.second < content.length) {
                        val endIndex = range.first + Character.charCount(content.codePointAt(range.first))
                        spannable.setSpan(ForegroundColorSpan(mHighlightColor), range.first, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        spannable.setSpan(StyleSpan(Typeface.BOLD), range.first, endIndex, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        spannable.insert(adjustedPosition, ICON_INTERMEDIATE_MARK_FLAG)
                        totalOffset += ICON_INTERMEDIATE_MARK_FLAG.length
                    }
                }
            return spannable
        }
    }

    class ViewHolderError(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val netError: TextView = itemView.findViewById(R.id.tv_error)
        val retryTv: TextView = itemView.findViewById(R.id.tv_retry)
        fun updateData(context: Context, data: DisplaySubtitleEntry) {
            if ((data.originContent.textType) == TYPE_NOT_NET) {
                netError.text = context.resources.getString(com.soundrecorder.base.R.string.subtitle_network_connect_error)
                retryTv.text = context.resources.getString(com.soundrecorder.base.R.string.retry)
                COUITextViewCompatUtil.setPressRippleDrawable(retryTv)
                retryTv.setOnClickListener {
                    if (NetworkUtils.isNetworkInvalid(context)) {
                        Toast.makeText(context, com.soundrecorder.common.R.string.network_disconnect, Toast.LENGTH_SHORT).show()
                        return@setOnClickListener
                    }
                    DebugUtil.i(TAG, "ViewHolderError-RecorderViewModelApi.externalInitAsr")
                    RecorderViewModelApi.externalInitAsr()
                }
            } else if (data.originContent.textType == TYPE_NOT_SERVICE) {
                netError.text = context.resources.getString(com.soundrecorder.base.R.string.subtitle_service_connect_error)
                retryTv.text = context.resources.getString(com.soundrecorder.base.R.string.retry)
                COUITextViewCompatUtil.setPressRippleDrawable(retryTv)
                retryTv.setOnClickListener {
                    if (NetworkUtils.isNetworkInvalid(context)) {
                        Toast.makeText(context, com.soundrecorder.common.R.string.network_disconnect, Toast.LENGTH_SHORT).show()
                        return@setOnClickListener
                    }
                    RecorderViewModelApi.externalInitAsr()
                }
            }
        }
    }

    fun getDataList(): List<DisplaySubtitleEntry> {
        return dataList
    }

    /**
     * 获取类型
     */
    override fun getItemViewType(position: Int): Int {
        val item = dataList.getOrNull(position) ?: return TYPE_DATA_OK
        return when (item.originContent.textType) {
            TYPE_VAD_FINAL -> TYPE_DATA_OK
            TYPE_INTERMEDIATE -> TYPE_DATA_NOW
            TYPE_NOT_NET -> TYPE_NO_NET
            TYPE_NOT_SERVICE -> TYPE_NO_SERVICE
            else -> TYPE_NO_DATA
        }
    }

    /**
     * 创建 ViewHolder
     */
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            TYPE_DATA_OK -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_data, parent, false)
                ViewHolderData(item, parent.context, subtitleMarkInsertHelper)
            }
            TYPE_DATA_NOW -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_data_now, parent, false)
                ViewHolderDataNow(item, parent.context, subtitleMarkInsertHelper)
            }
            TYPE_NO_NET -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_error, parent, false)
                ViewHolderError(item)
            }
            TYPE_NO_SERVICE -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_error, parent, false)
                ViewHolderError(item)
            }
            else -> {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_data, parent, false)
                ViewHolderData(item, parent.context, subtitleMarkInsertHelper)
            }
        }
    }

    /**
     * 绑定数据到 ViewHolder
     */
    @SuppressLint("ClickableViewAccessibility")
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        if (dataList.size <= 0) return
        val data = dataList[position]
        val speakerStyle = calculateSpeakerStyle(holder, data)
        val nowDataSize = dataList.size
        if (holder is ViewHolderData) {
            speakerStyle?.let { holder.setSpeakerStyle(it) }
            holder.updateData(data, position, listener)
            val needAnimation = if (position == dataList.size - 2) {
                true
            } else {
                dataList.size == 1
            }
            if (oldDataSize != nowDataSize && needAnimation) {
                DebugUtil.d("TGDAS", "oldDataSize != nowDataSize")
                oldDataSize = nowDataSize
                holder.startSpeakingAnimation()
            } else {
                holder.inVisiableAnimation()
            }
        } else if (holder is ViewHolderDataNow) {
            holderNow = holder
            var isNewParagraph = false
            if (oldDataSize != nowDataSize) {
                oldDataSize = nowDataSize
                isNewParagraph = true
            }
            holder.updateData(data, position, languageCode, isNewParagraph, listener)

            // 根据不同情况执行不同的动画
            if (isNewParagraph) {
                holder.startSpeakingAnimation()
            } else if (isFromDataToNow) {
                speakerStyle?.let { holder.setSpeakerStyle(it) }
                holder.startSpeakingAnimationFromData()
                isFromDataToNow = false
            }
        } else if (holder is ViewHolderError) {
            holder.updateData(context, data)
        }
    }

    private fun calculateSpeakerStyle(
        holder: RecyclerView.ViewHolder,
        data: DisplaySubtitleEntry
    ): Int? {
        val speakerStyle = if (holder !is ViewHolderError) {
            val roleId = data.originContent.roleId
            if (speakerStyleHashMap.containsKey(roleId)) {
                speakerStyleHashMap[roleId]
            } else {
                val speakerColorsIndex = (speakerStyleHashMap.size + 1) % speakerColors.size
                val color = speakerColors[speakerColorsIndex]
                speakerStyleHashMap[roleId] = speakerColors[speakerColorsIndex]
                color
            }
        } else {
            -1
        }
        return speakerStyle
    }

    /**
     * 获取item总数
     */
    override fun getItemCount(): Int {
        return dataList.size
    }
    /**
     * 刷新数据
     */
    @SuppressLint("NotifyDataSetChanged")
    fun setData(data: MutableList<DisplaySubtitleEntry>) {
        if (data.size <= 0) return
        DebugUtil.i(TAG, "setData =: " + data.size)
        dataList = data
        if (isPaused) {
            dataList[dataList.size - 1].originContent.textType = TYPE_VAD_FINAL
        }
        // 十秒后没有新数据上屏，则将识别中的讲话人改为真实讲话人
        launchTask?.cancel()
        launchTask = CoroutineScope(Dispatchers.IO).launch {
            delay(TASH_DELAY_TIME)
            withContext(Dispatchers.Main) {
                val position = dataList.size - 1
                dataList[dataList.size - 1].originContent.textType = TYPE_VAD_FINAL
                notifyItemChanged(position)
            }
        }
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setNoNetWork(isNONetWork: Boolean) {
        val item = ConvertContentItem()
        val emptyMarkList = mutableListOf<DisplayMark>()
        item.textType = if (isNONetWork) TYPE_NOT_NET else TYPE_NOT_SERVICE
        dataList.add(DisplaySubtitleEntry(item, "", emptyMarkList))
        notifyDataSetChanged()
    }

    fun setListener(listener: OnItemClickListener) {
        this.listener = listener
    }

    interface OnItemClickListener {
        fun onItemClickSpeaker(position: Int)
    }
}