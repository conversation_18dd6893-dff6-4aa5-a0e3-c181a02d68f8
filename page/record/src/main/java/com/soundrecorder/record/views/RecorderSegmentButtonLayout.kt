/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - RecorderSegmentButtonLayout.kt
 * Description:
 *     Refactor platform button.
 *
 * Version: 1.0
 * Date: 2025-07-26
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                  <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2025-07-26      1.0    Refactor platform button.
 *********************************************************************************/
package com.soundrecorder.record.views

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import com.coui.appcompat.segmentbutton.COUISegmentButtonLayout

class RecorderSegmentButtonLayout@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : COUISegmentButtonLayout(context, attrs, defStyleAttr) {

    var callbackTask: AddCallbackTask? = null

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (!isEnabled) {
            callbackTask?.execCallback()
            return true
        }
        return super.dispatchTouchEvent(ev)
    }

    interface AddCallbackTask {
        fun execCallback()
    }
}