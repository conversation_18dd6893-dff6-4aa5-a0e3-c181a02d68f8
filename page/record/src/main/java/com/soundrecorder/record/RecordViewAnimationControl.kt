/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecordViewAnimationControl
 * Description:
 * Version: 1.0
 * Date: 2023/7/12
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/7/12 1.0 create
 */

package com.soundrecorder.record

import android.animation.AnimatorSet
import android.view.View
import android.view.ViewGroup
import android.widget.Space
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.splitwindow.FoldingWindowObserver
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.getFloatValue
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.common.utils.ViewUtils.updateConstraintHeight
import kotlin.math.roundToInt

class RecordViewAnimationControl(rootView: ViewGroup) {
    private val logTag = "RecorderViewAnimation"
    private var waveGradientView: ViewGroup = rootView.findViewById(R.id.wave_gradient_view)
    private var toobarLayout: View = rootView.findViewById(R.id.abl)
    private var middleControl: ViewGroup = rootView.findViewById(R.id.middle_control)
//    private var directRecordingView: ViewGroup = rootView.findViewById(R.id.view_direct_view)
    private var viewCenterArea: Space = rootView.findViewById(R.id.view_center_divider)
    private val pathInterpolator = COUIMoveEaseInterpolator()

    var foldWindowType: Int = FoldingWindowObserver.SCREEN_VERTICAL_EXPAND
    private val wavePercentHover: Float = BaseApplication.getAppContext()
        .getFloatValue(R.dimen.record_wave_view_height_percent_hover)
    private val wavePercentShowMark: Float = BaseApplication.getAppContext()
        .getFloatValue(R.dimen.record_wave_view_height_percent_show_mark)
    private val wavePercentDefault: Float = BaseApplication.getAppContext()
        .getFloatValue(R.dimen.record_wave_view_height_percent)

    private var showMarkAnimation: AnimatorSet? = null
    private var hideMarkAnimation: AnimatorSet? = null
    private var maxTimeLayoutHeight: Int = 0

    init {
        val resource = rootView.resources
        maxTimeLayoutHeight = resource.getDimensionPixelSize(com.soundrecorder.common.R.dimen.common_max_time_layout_height)
    }

    /**
     * 录制时间动画结束后应该显示的偏移位置。
     *
     * @param showCenter 是否显示在正中间（原始位置就是居中显示）
     */
    fun showRecordTopCenterOrTop(showCenter: Boolean) {
        DebugUtil.i(logTag, "showRecordTopCenterOrTop showCenter=$showCenter")
        if (showCenter) {
            if (hideMarkAnimation?.isRunning == true) {
                DebugUtil.i(logTag, "showRecordTopCenterOrTop doHideAnimationTime is running")
                return
            }
            showViewLocationCenter()
        } else {
            if (showMarkAnimation?.isRunning == true) {
                DebugUtil.i(logTag, "showRecordTopCenterOrTop doShowAnimationTime is running")
                return
            }
            showViewLocationTop()
        }
    }

    private fun showViewLocationCenter() {
        DebugUtil.i(logTag, "showViewLocationCenter")
        val waveHeight = getWaveHeight()
        waveGradientView.updateConstraintHeight(waveHeight)
    }

    /**
     * 有标记置顶显示
     */
    private fun showViewLocationTop() {
        DebugUtil.i(logTag, "showViewLocationTop")
        val waveHeight = getWaveHeight()
        waveGradientView.updateConstraintHeight(waveHeight)
    }

    /**
     * 获取波形高度
     */
    fun getWaveHeight(): Int {
        val percent = getWavePercent()
        val screenHeight = ScreenUtil.screenHeight
        val result =  (screenHeight * percent).roundToInt()
        DebugUtil.i(logTag, "getWaveHeight percent =$percent,screenHeight=$screenHeight,result=$result")
        return result
    }

    /**
     * 获取当前波形区域占屏幕的百分占比
     */
    fun getWavePercent(): Float {
        return if (isHoverState()) {
            wavePercentHover
        } else {
            wavePercentDefault
        }
    }

    /**
     * 是否处于悬停状态
     */
    fun isHoverState(): Boolean =
        foldWindowType == FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER

    fun cancelAllAnimation() {
        showMarkAnimation?.cancel()
        hideMarkAnimation?.cancel()
    }

    fun release() {
        cancelAllAnimation()
        hideMarkAnimation = null
        showMarkAnimation = null
    }
}