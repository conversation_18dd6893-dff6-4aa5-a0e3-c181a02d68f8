/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.keyword

import android.content.Context
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.playback.R
import com.soundrecorder.common.databean.KeyWord
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.convertservice.keyword.ExtractKeyWordTask
import com.soundrecorder.convertservice.keyword.IExtractKeywordCallback
import com.soundrecorder.common.databean.ConvertContentItem
import java.lang.StringBuilder

/**
 * 提取关键词的Manager
 */
class ExtractKeyWordManager(private val convertVM: PlaybackConvertViewModel) {

    companion object {
        val TAG = "ExtractKeyWordManager"
    }

    /**
     * 提取关键词
     */
    fun extractKeyWord(recordId: Long): Boolean {
        val code = checkCanExtract(BaseApplication.getAppContext())
        if (code != ExtractKeyWordTask.CODE_OK) {
            DebugUtil.e(TAG, "extractKeyWord can't extract")
            onExtractFailed(code, "不能提取")
            return false
        }
        DebugUtil.d(TAG, "extractKeyWord recordId:$recordId start...")
        val task = ExtractKeyWordTask(recordId, object : IExtractKeywordCallback {

            override fun getConvertText(): List<String>? {
                val convertText = convertVM.convertContentData
                return if (convertText == null) {
                    null
                } else {
                    mergeConvertText(convertText)
                }
            }

            /**
             * 将转文本内容拼装成List<String>,size=1
             */
            private fun mergeConvertText(list: List<ConvertContentItem>): List<String> {
                val builder = StringBuilder()
                list.forEach {
                    builder.append(it.textContent)
                }
                return listOf(builder.toString())
            }

            override fun onSuccess(data: List<KeyWord>) {
                val keyWords = data.map {
                    it.name
                }

                setKeyWords(keyWords)
            }

            override fun onFailed(code: Int, msg: String) {

                onExtractFailed(code, msg)
            }
        })

        onExtracting()

        Thread(task).start()
        return true
    }

    /**
     * 显示加载中的动效
     */
    private fun onExtracting() {
        convertVM.extractState = KeyWordChipGroup.LOADING_STATE
        convertVM.keyWords.postValue(emptyList())
    }

    /**
     * 设置关键词
     */
    private fun setKeyWords(keyWords: List<String>) {
        convertVM.extractState = KeyWordChipGroup.DEFAULT_STATE
        convertVM.keyWords.postValue(keyWords)
    }

    /**
     * 提取失败
     */
    private fun onExtractFailed(code: Int, msg: String) {
        setKeyWords(emptyList())

        // 根据code进行toast提示
        val context = BaseApplication.getAppContext()
        when (code) {
            ExtractKeyWordTask.CODE_NO_NET -> ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
            else -> ToastManager.showShortToast(context, com.soundrecorder.common.R.string.extract_keywords_fail_hint)
        }
    }

    /**
     * 判断是否能够提取关键词
     */
    private fun checkCanExtract(context: Context): Int {
        if (NetworkUtils.isNetworkInvalid(context)) { //先判断网络
            return ExtractKeyWordTask.CODE_NO_NET
        }
        // TODO 其他判断
        return ExtractKeyWordTask.CODE_OK
    }

    fun release() { }
}