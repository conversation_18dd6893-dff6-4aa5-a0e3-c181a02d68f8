/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.buttonpanel

import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.accessibility.AccessibilityEvent
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.constraintlayout.widget.Guideline
import androidx.core.view.contains
import androidx.core.view.isVisible
import androidx.core.view.marginBottom
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import com.coui.appcompat.seekbar.COUISeekBar
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.currentHasHourFormatTimeExclusive
import com.soundrecorder.base.ext.durationHasHourFormatTimeExclusive
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.RecorderTextUtils
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.common.utils.visible
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.playback.PlaybackContainerFragment
import com.soundrecorder.playback.PlaybackContainerFragment.Companion.ARG_KEY_SHOW_LOADING
import com.soundrecorder.playback.PlaybackContainerFragment.Companion.MAX_PROGRESS
import com.soundrecorder.playback.PlaybackContainerViewModel
import com.soundrecorder.playback.R
import com.soundrecorder.playback.audio.PlaybackAudioFragment
import com.soundrecorder.playback.view.PlaybackAnimatedCircleButton
import com.soundrecorder.playback.view.CustomConstraintLayout
import com.soundrecorder.playback.view.SegmentSeekBar
import com.soundrecorder.player.status.PlayStatus

class ButtonPanelControl(
    private val browseFileApi: BrowseFileInterface?,
    private val containerFragment: PlaybackContainerFragment,
    private val audioFragment: PlaybackAudioFragment?,
    private val viewModel: PlaybackContainerViewModel?,
    private val browseViewModel: ViewModel?
) : View.OnClickListener {

    companion object {
        private const val TAG = "ButtonPanelControl"
    }

    private val viewLifecycleOwner = containerFragment
    private val resources = containerFragment.resources
    private val layoutInflater = containerFragment.layoutInflater

    private var seekBar: SegmentSeekBar? = null
    private var buttonPanel: CustomConstraintLayout? = null
    private var imgBackward: ImageView? = null
    private var imgForward: ImageView? = null
    private var imgMarkAdd: ImageView? = null
    private var imgMarkList: ImageView? = null
    private var redCircleIcon: PlaybackAnimatedCircleButton? = null
    private var llSeekbarContainer: LinearLayout? = null
    private var toolCenterGuideLine: Guideline? = null
    private var toolRightGuideLine: Guideline? = null
    private var middleControl: RelativeLayout? = null
    private var tvDuration: TextView? = null
    private var tvCurrent: TextView? = null

    fun addButtonPanel() {
        val container = containerFragment.contentBinding?.body ?: return
        buttonPanel?.let {
            if (container.contains(it)) {
                return
            }
        }
        buttonPanel = layoutInflater.inflate(
            R.layout.activity_playback_button_panel,
            null,
            false
        ) as? CustomConstraintLayout
        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        layoutParams.gravity = Gravity.BOTTOM
        container.addView(buttonPanel, layoutParams)

        initButtonPanel()
        initObserver()
    }

    fun getButtonPanel(): CustomConstraintLayout? {
        return buttonPanel
    }

    fun updateLayoutParam(navigationHeight: Int) {
        buttonPanel?.updateLayoutParams<FrameLayout.LayoutParams> {
            bottomMargin = navigationHeight
        }
    }

    fun observerButtonPanelHeightChange(callback: ((height: Int) -> Unit)) {
        buttonPanel?.observeHeightChange(callback)
    }

    fun disObserverButtonPanelHeightChange() {
        buttonPanel?.unObserveHeightChange()
    }

    fun getButtonPanelHeight(): Int {
        return buttonPanel?.let { it.getPanelHeight() + it.marginBottom } ?: 0
    }

    fun removeButtonPanel() {
        buttonPanel ?: return
        val container = containerFragment.contentBinding?.body ?: return
        container.removeView(buttonPanel)
        release()
    }

    private fun initButtonPanel() {
        imgBackward = buttonPanel?.findViewById(R.id.img_backward)
        imgForward = buttonPanel?.findViewById(R.id.img_forward)
        imgMarkAdd = buttonPanel?.findViewById(R.id.img_mark_add)
        imgMarkList = buttonPanel?.findViewById(R.id.img_mark_list)
        redCircleIcon = buttonPanel?.findViewById(R.id.red_circle_icon)
        llSeekbarContainer = buttonPanel?.findViewById(R.id.ll_seekbar_container)
        toolCenterGuideLine = buttonPanel?.findViewById(R.id.tool_center_guide_line)
        toolRightGuideLine = buttonPanel?.findViewById(R.id.tool_right_guide_line)
        middleControl = buttonPanel?.findViewById(R.id.middle_control)
        tvDuration = buttonPanel?.findViewById(R.id.tv_duration)
        tvCurrent = buttonPanel?.findViewById(R.id.tv_current)

        if (BaseApplication.sIsRTLanguage) {
            // RTl语言下，快进快退图标也需要适配RTL
            imgBackward?.setImageResource(R.drawable.ic_forward)
            imgForward?.setImageResource(R.drawable.ic_backward)
        }
        imgMarkAdd?.isVisible = !recordFilterIsRecycle()
        imgMarkList?.isVisible = !recordFilterIsRecycle()

        imgMarkAdd?.setOnClickListener(this)
        imgForward?.setOnClickListener(this)
        imgBackward?.setOnClickListener(this)
        imgMarkList?.setOnClickListener(this)
        redCircleIcon?.setOnClickListener(this)
        initAccess()
        initSeekBar()
        initPlaybackPanel()
        checkShowLoading()
    }

    private fun initAccess() {
        //play button support accessibility. the view should be red_circle_icon, not the parent view middle_control
        redCircleIcon?.accessibilityDelegate = object : View.AccessibilityDelegate() {
            override fun sendAccessibilityEvent(host: View, eventType: Int) {
                if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                    if (viewModel?.playerController?.isWholePlaying() == true) {
                        redCircleIcon?.contentDescription = resources.getString(
                            com.soundrecorder.common.R.string.recorder_pause
                        )
                    } else {
                        redCircleIcon?.contentDescription = resources.getString(
                            com.soundrecorder.common.R.string.recorder_play
                        )
                    }
                }
                super.sendAccessibilityEvent(host, eventType)
            }
        }
    }

    private fun initSeekBar() {
        layoutInflater.inflate(R.layout.layout_playback_seekbar, llSeekbarContainer, true)
        seekBar = llSeekbarContainer?.findViewById(R.id.seek_bar)
        val seekBar = seekBar ?: return
        seekBar.alpha = 1f
        seekBar.visible()
        seekBar.max = MAX_PROGRESS
        seekBar.isHapticFeedbackEnabled = false
        seekBar.setOnSeekBarChangeListener(object : COUISeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: COUISeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    viewModel?.apply {
                        val seekToTime = progress * this.playerController.getDuration() / MAX_PROGRESS
                        needSyncRulerView = true
                        playerController.setOCurrentTimeMillis(seekToTime)
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: COUISeekBar?) {
                viewModel?.apply {
                    playerController.onStartTouchSeekBar()
                    if (playerController.isWholePlaying()) {
                        playerController.stopTimerNow()
                        audioFragment?.waveStopMove()
                    }
                }
            }

            override fun onStopTrackingTouch(seekBar1: COUISeekBar?) {
                viewModel?.apply {
                    playerController.onStopTouchSeekBar()
                    seekBar.run {
                        val seekToTime = progress * playerController.getDuration() / MAX_PROGRESS
                        seekTime(seekToTime)
                    }
                    //按下之前是播放状态，松手后也应该继续播放。
                    playerController.onResetPlayState()
                }
            }
        })
    }

    private fun checkShowLoading() {
        val needShowLoading = containerFragment.arguments?.getBoolean(ARG_KEY_SHOW_LOADING, false) ?: false
        if (!needShowLoading) {
            // 默认不showLoading场景下，禁止拖动seekbar
            seekBar?.isEnabled = viewModel?.loadAmpSuccess() ?: false
        }
    }

    private fun initPlaybackPanel() {
        if (ScreenUtil.getWindowType() != WindowType.MIDDLE) {
            DebugUtil.i(TAG, "initPlaybackPanel window type is not middle.")
            return
        }
        val buttonPanelLayout = buttonPanel
        if (buttonPanelLayout == null) {
            DebugUtil.e(PlaybackAudioFragment.Companion.TAG, "initPlaybackPanel init the view fail.")
            return
        }
        val constraintSet = ConstraintSet()
        constraintSet.clone(buttonPanelLayout)
        constraintSet.clear(R.id.ll_seekbar_container, ConstraintSet.TOP)
        constraintSet.clear(R.id.ll_seekbar_container, ConstraintSet.BOTTOM)
        constraintSet.clear(R.id.ll_seekbar_container, ConstraintSet.START)
        constraintSet.clear(R.id.ll_seekbar_container, ConstraintSet.END)

        val seekBarTimeMargin = resources.getDimension(R.dimen.seekbar_time_margin).toInt()
        val seekBarMargin = resources.getDimension(R.dimen.seekbar_margin).toInt()

        constraintSet.connect(R.id.tv_current, ConstraintSet.BOTTOM, R.id.middle_control, ConstraintSet.TOP)
        constraintSet.connect(R.id.tv_current, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START, seekBarTimeMargin)

        constraintSet.connect(R.id.ll_seekbar_container, ConstraintSet.TOP, R.id.tv_current, ConstraintSet.TOP)
        constraintSet.connect(R.id.ll_seekbar_container, ConstraintSet.BOTTOM, R.id.tv_current, ConstraintSet.BOTTOM)
        constraintSet.connect(R.id.ll_seekbar_container, ConstraintSet.START, R.id.tv_current, ConstraintSet.END, seekBarMargin)
        constraintSet.connect(R.id.ll_seekbar_container, ConstraintSet.END, R.id.tv_duration, ConstraintSet.START, seekBarMargin)

        constraintSet.connect(R.id.tv_duration, ConstraintSet.BOTTOM, R.id.middle_control, ConstraintSet.TOP)
        constraintSet.connect(R.id.tv_duration, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END, seekBarTimeMargin)

        constraintSet.applyTo(buttonPanelLayout)
    }

    private fun initObserver() {
        initWindowTypeObserver()
        initPlayDurationObserver()
        initTargetPlaySegmentObserver()
        initMarkEnableObserver()
        initIsDecodeReadyObserver()
        initCurrentTimeMillisObserver()
        initPlayerStateObserver()
    }

    private fun initWindowTypeObserver() {
        browseFileApi?.getViewModelWindowType<WindowType>(browseViewModel)?.observe(viewLifecycleOwner) {
            DebugUtil.d(PlaybackAudioFragment.Companion.TAG, "observe current windowType=$it")
            redCircleIcon?.windowType = it
            correctBottomButtonWhenLayoutNotReCreate(it == WindowType.LARGE)
            correctBottomViewMarginBottom()
        }
    }

    /**
     * 矫正layout改变，应用未重建，导致加载layout同实际layout不匹配，底部操作按钮显示异常问题
     * 平板上 录音同其他应用分屏加载小中布局，其他应用切位浮窗后，录音退出分屏显示全屏，此时录音未重建，仍然加载的小中布局
     */
    private fun correctBottomButtonWhenLayoutNotReCreate(isLarge: Boolean) {
        /*回收站或折叠屏不显示横向摘要、转文本等操作按钮布局，设为0F*/
        if (recordFilterIsRecycle() || FeatureOption.getIsFoldFeature()) {
            toolCenterGuideLine?.setGuidelinePercent(0F)
            toolRightGuideLine?.setGuidelinePercent(NumberConstant.NUM_F1_0)
        } else {
            val guidePercent = (toolCenterGuideLine?.layoutParams as? ConstraintLayout.LayoutParams)?.guidePercent
            if (isLarge && guidePercent != NumberConstant.NUM_F0_2) {
                toolCenterGuideLine?.setGuidelinePercent(NumberConstant.NUM_F0_2)
                toolRightGuideLine?.setGuidelinePercent(NumberConstant.NUM_F0_8)
            } else if (!isLarge && guidePercent != 0F) {
                toolCenterGuideLine?.setGuidelinePercent(0F)
                toolRightGuideLine?.setGuidelinePercent(NumberConstant.NUM_F1_0)
            }
        }
    }

    private fun correctBottomViewMarginBottom() {
        middleControl?.updateLayoutParams<MarginLayoutParams> {
            bottomMargin = resources.getDimension(com.soundrecorder.common.R.dimen.circle_playback_button_margin_bottom).toInt()
            val newWidth = resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_diam).toInt()
            if (width != newWidth) {
                width = newWidth
                height = newWidth
                redCircleIcon?.refreshCircleRadius(
                    resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_radius)
                )
            }
        }
    }

    private fun initPlayDurationObserver() {
        viewModel?.playerController?.mDuration?.observe(viewLifecycleOwner) { duration ->
            tvDuration?.contentDescription =
                TimeUtils.getContentDescriptionForTimeDuration(duration)
            tvDuration?.text = duration.durationHasHourFormatTimeExclusive(true)
            seekBar?.setDuration(duration)
        }
    }

    private fun initTargetPlaySegmentObserver() {
        viewModel?.targetPlaySegment?.observe(viewLifecycleOwner) { timeSegmentList ->
            seekBar?.setSegmentTimeMillList(timeSegmentList)
        }
    }

    private fun initMarkEnableObserver() {
        viewModel?.markEnable?.observe(viewLifecycleOwner) { isMarkEnable ->
            val pictureMarkHelper = containerFragment.mPictureMarkHelper
            val isEnable = isMarkEnable && (pictureMarkHelper?.checkNeedAddMark() == true)
            val backgroundDrawable = if (isEnable) {
                AppCompatResources.getDrawable(BaseApplication.getAppContext(), R.drawable.ic_mark_icon_new)
            } else {
                AppCompatResources.getDrawable(BaseApplication.getAppContext(), R.drawable.ic_mark_icon_disable_new)
            }
            backgroundDrawable?.let {
                imgMarkAdd?.setImageDrawable(it)
            }
        }
    }

    private fun initIsDecodeReadyObserver() {
        viewModel?.mIsDecodeReady?.observe(viewLifecycleOwner) { isReady ->
            if (isReady) {
                seekBar?.isEnabled = true // 拿到波形数据，恢复按钮点击状态
            }
        }
    }

    private fun initCurrentTimeMillisObserver() {
        viewModel?.playerController?.currentTimeMillis?.observe(viewLifecycleOwner) { currentTime ->
            tvCurrent?.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(currentTime)
            tvCurrent?.text = currentTime.currentHasHourFormatTimeExclusive(viewModel.playerController.getDuration())
            refreshSeekBar(currentTime)
        }
    }

    private fun refreshSeekBar(currentTime: Long) {
        if (viewModel?.playerController?.mIsTouchSeekbar?.value == false) {
            val duration = viewModel.playerController.getDuration()
            if (duration > 0) {
                val seekBar = seekBar ?: return
                seekBar.progress = (currentTime * MAX_PROGRESS / (duration)).toInt()
                seekBar.contentDescription = RecorderTextUtils.getNewProgressDescription(
                    BaseApplication.getAppContext(), currentTime, duration
                )
                seekBar.accessibilityDelegate = object : View.AccessibilityDelegate() {
                    override fun sendAccessibilityEvent(host: View, eventType: Int) {
                        if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                            seekBar.contentDescription = RecorderTextUtils.getNewProgressDescription(
                                BaseApplication.getAppContext(), currentTime, duration
                            )
                        }
                        super.sendAccessibilityEvent(host, eventType)
                    }
                }
            }
        }
    }

    private val playStateChangeObserver = Observer<Int> {
        DebugUtil.i(TAG, "mPlayerState changed $it")
        when (it) {
            PlayStatus.PLAYER_STATE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> redCircleIcon?.switchPauseState()

            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE -> redCircleIcon?.switchPlayState()

            PlayStatus.PLAYER_STATE_HALTON -> redCircleIcon?.switchPlayState()

            else -> redCircleIcon?.switchPlayState()
        }
    }


    private fun initPlayerStateObserver() {
        viewModel?.playerController?.playerState?.observeForever(playStateChangeObserver)
    }

    private fun recordFilterIsRecycle(): Boolean {
        return viewModel?.isRecycle == true
    }

    private fun release() {
        disObserverButtonPanelHeightChange()
        viewModel?.playerController?.playerState?.removeObserver(playStateChangeObserver)
        seekBar = null
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.img_mark_add -> containerFragment.handleMarkAdd(v)
            R.id.img_forward -> containerFragment.handleClickForward()
            R.id.img_backward -> containerFragment.handleClickBackward()
            R.id.img_mark_list -> containerFragment.handleClickMarkList()
            R.id.red_circle_icon -> containerFragment.handleClickRedCircleIcon()
        }
    }
}