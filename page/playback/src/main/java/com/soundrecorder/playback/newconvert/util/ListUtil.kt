/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.util

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.RecorderICUFormateUtils
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.db.RecorderDBUtil
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

object ListUtil {
    const val TAG = "ListUtil"

    @JvmStatic
    fun getDistinctRoleName(data: List<ConvertContentItem>?): MutableList<String> {
        val originList = ArrayList<String>()
        data?.let { list ->
            for (item in list) {
                if (isValidRoleName(item)) {
                    item.roleName?.let {
                        originList.add(it)
                    }
                }
            }
        }
        return originList.distinct().toMutableList()
    }

    @JvmStatic
    fun isValidRoleName(item: ConvertContentItem): Boolean {
        return (item.roleId > 0 && !item.roleName.isNullOrBlank())
    }

    @JvmStatic
    fun getTimeString(recordId: Long?, playName: String?): String {
        val date = Date(
            RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                .getCreateTimeByPath(
                    recordId
                        ?: -1, playName?.endsWith(".amr") ?: false
                )
        )
        return runCatching {
            // 基于年份的动态时间格式化
            val currentCalendar = Calendar.getInstance()
            val recordCalendar = Calendar.getInstance().apply { time = date }
            val currentYear = currentCalendar.get(Calendar.YEAR)
            val recordYear = recordCalendar.get(Calendar.YEAR)

            val formatPattern = if (currentYear == recordYear) {
                // 同年：格式为 M/d HH:mm
                "M/d HH:mm"
            } else {
                // 不同年：格式为 yyyy-M-d HH:mm
                "yyyy/M/d HH:mm"
            }
            val formatter = SimpleDateFormat(formatPattern, Locale.getDefault())
            formatter.format(date)
        }.getOrElse {
            // 异常情况下回退到原有格式化方法
            DebugUtil.e(TAG, "getTimeString format error", it)
            RecorderICUFormateUtils.formatDateTimeShort(date)
        }
    }

    @JvmStatic
    fun getMetaDataList(data: List<ConvertContentItem>?): MutableList<ConvertContentItem.ItemMetaData> {
        val originList = mutableListOf<ConvertContentItem.ItemMetaData>()
        data?.let { list ->
            for (item in list) {
                item.mTextOrImageItems?.let {
                    originList.addAll(it)
                }
            }
        }
        return originList
    }

    @JvmStatic
    fun getTextMetaDataList(data: List<ConvertContentItem>?): MutableList<Pair<Int, ConvertContentItem.TextItemMetaData>> {
        val result = mutableListOf<Pair<Int, ConvertContentItem.TextItemMetaData>>()
        var initPosition = 0
        data?.let { list ->
            for (item in list) {
                item.mTextOrImageItems?.forEachIndexed { index, metaItem ->
                    var position = initPosition
                    if (metaItem is ConvertContentItem.TextItemMetaData) { // 从搜索结果中匹配到item的位置,高亮搜索词
                        position += index
                        result.add(Pair(position, metaItem))
                    }
                }
                initPosition += (item.mTextOrImageItems?.size ?: 0)
            }
        }
        DebugUtil.i(TAG, "getTextMetaDataList input data $data, outputData $result")
        return result
    }

    @JvmStatic
    fun getTimeMetaDataList(data: List<ConvertContentItem>?): MutableList<Pair<Int, ConvertContentItem.TimerDividerMetaData>> {
        val result = mutableListOf<Pair<Int, ConvertContentItem.TimerDividerMetaData>>()
        var initPosition = 0
        data?.let { list ->
            for (item in list) {
                item.mTextOrImageItems?.forEachIndexed { index, metaItem ->
                    var position = initPosition
                    if (metaItem is ConvertContentItem.TimerDividerMetaData) { // 从搜索结果中匹配到item的位置,高亮搜索词
                        position += index
                        result.add(Pair(position, metaItem))
                    }
                }
                initPosition += (item.mTextOrImageItems?.size ?: 0)
            }
        }
        DebugUtil.i(TAG, "getTextMetaDataList input data $data, outputData $result")
        return result
    }
}