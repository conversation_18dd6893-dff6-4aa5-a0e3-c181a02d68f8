/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Rect
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import com.soundrecorder.playback.R
import com.google.android.material.imageview.ShapeableImageView
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.newconvert.ui.SeekPlayActionModeCallback

class TextImageMixLayout @JvmOverloads constructor(
    context: Context,
    var attrs: AttributeSet? = null,
    var defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        const val TAG = "TextImageMixLayout"

        const val WIDTH_HEIGHT_STANDARD_RATIO: Float = 4f / 3f

        const val IMAGE_SPAN_SPACE = 1  //图片实际占用字符数   图片多加空格
        const val SPACE = "□ "  //空格字符  a为占用旗子的占位符  空格处于行尾会被干掉
    }

    private var convertContentItem: ConvertContentItem? = null
    private var callback = SeekPlayActionModeCallback()

    var onClickListener: OnMixLayoutClickListenner? = null
    var mTextViewList: MutableList<Pair<Int, BackgroundTextView>> = mutableListOf()
    var mImageViews: MutableList<Pair<Int, ShapeableImageView>> = mutableListOf()
    var mImageLoadDatas: MutableList<Pair<Int, ImageLoadData>> = mutableListOf()


    lateinit var drawAttr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr
    val layoutHelper: TextImageMixLayoutHelper = TextImageMixLayoutHelper()

    //是否是搜索模式
    private var searchMode = false
    private var position = -1

    init {
        parceAttr(attrs, defStyleAttr)
    }

    private fun parceAttr(attrs: AttributeSet?, defStyleAttr: Int) {
        val array =
            context.obtainStyledAttributes(attrs, R.styleable.TextImageMixLayout, defStyleAttr, 0)
        val defaultTopMargin =
            context.resources.getDimensionPixelSize(R.dimen.mix_image_text_layout_first_top_margin)
        val firstItemTopMargin = array.getDimensionPixelSize(
            R.styleable.TextImageMixLayout_first_item_top_margin,
            defaultTopMargin
        )
        val defaultBottomMargin =
            context.resources.getDimensionPixelSize(R.dimen.mix_image_text_layout_last_buttom_margin)
        val lastItemButtomMargin = array.getDimensionPixelSize(
            R.styleable.TextImageMixLayout_last_item_buttom_margin,
            defaultBottomMargin
        )
        val defaultMarginBetweenImageAndText =
            context.resources.getDimensionPixelSize(R.dimen.mix_image_margin_between_image_and_text)
        val marginBetweenImageAndText = array.getDimensionPixelSize(
            R.styleable.TextImageMixLayout_margin_between_image_and_text,
            defaultMarginBetweenImageAndText
        )
        val defaultMarginBetweenTextAndImage =
            context.resources.getDimensionPixelSize(R.dimen.mix_image_margin_between_text_and_image)
        val marginBetweenTextAndImage = array.getDimensionPixelSize(
            R.styleable.TextImageMixLayout_margin_between_text_and_image,
            defaultMarginBetweenTextAndImage
        )
        val defaultMarginBetweenImages = context.resources.getDimensionPixelSize(R.dimen.mix_image_margin_between_image_and_image)
        val marginBetweenImages = array.getDimensionPixelSize(
            R.styleable.TextImageMixLayout_margin_between_image_and_image,
            defaultMarginBetweenImages
        )
        val defaultCornerRadiusInDp = context.resources.getDimension(R.dimen.round_cornor_radius)
        val roundedCornerRadisInDp = array.getDimension(
            R.styleable.TextImageMixLayout_margin_between_image_and_image,
            defaultCornerRadiusInDp
        )
        val textBackgroundColor = array.getColor(
            R.styleable.TextImageMixLayout_text_background_color,
            com.support.appcompat.R.attr.couiColorPrimary
        )
        val bottomMargin = context.resources.getDimension(R.dimen.mix_image_text_layout_bottom_margin)
        drawAttr = TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr(
            firstItemTopMargin,
            lastItemButtomMargin,
            marginBetweenImageAndText,
            marginBetweenTextAndImage,
            marginBetweenImages,
            roundedCornerRadisInDp,
            textBackgroundColor,
            bottomMargin.toInt()
        )
        DebugUtil.i(TAG, "parce attr drawAttr $drawAttr")
        array.recycle()
    }


    fun setConvertContentItem(data: ConvertContentItem, searchMode: Boolean, position: Int) {
        DebugUtil.i(TAG, "setConvertContentItem position:$position data: $data searchMode: $searchMode")
        convertContentItem = data
        this.searchMode = searchMode
        this.position = position
        this.layoutHelper.searchMode = searchMode
        addTextViewOrImageViewInternal()
        postInvalidate()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        DebugUtil.i(TAG, "onMeasure position:$position width: ${MeasureSpec.toString(widthMeasureSpec)}" +
                ", height: ${MeasureSpec.toString(heightMeasureSpec)} this $this")
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        DebugUtil.i(TAG, "onLayout position:$position changed $changed, bounds: ${Rect(left, top, right, bottom)} this $this")
        super.onLayout(changed, left, top, right, bottom)
    }

    override fun dispatchDraw(canvas: Canvas) {
        DebugUtil.i(TAG, "dispatchDraw")
        super.dispatchDraw(canvas)
    }

    override fun draw(canvas: Canvas) {
        super.draw(canvas)
        DebugUtil.i(TAG, "draw")
    }

    private fun addTextViewOrImageViewInternal() {
        val textOrImageItems = convertContentItem?.mTextOrImageItems
        textOrImageItems?.let {
            removeAllViewsInLayout()
            mTextViewList.clear()
            mImageViews.clear()
            mImageLoadDatas.clear()
            if (textOrImageItems.isNotEmpty()) {
                var lastTopView: View? = null
                val iterator = textOrImageItems.listIterator()
                var indexIterator = iterator.withIndex()
                while (indexIterator.hasNext()) {
                    val indexValue = indexIterator.next()
                    val textOrImageItem = indexValue.value
                    val itemIndex = indexValue.index
                    val isLastItem = !indexIterator.hasNext()
                    var currentAddView =
                        if (textOrImageItem is ConvertContentItem.TextItemMetaData) {
                            layoutHelper.checkAndAddBackgroundTextView(
                                this.context,
                                itemIndex,
                                textOrImageItem,
                                convertContentItem,
                                lastTopView,
                                this,
                                isLastItem,
                                callback,
                                onClickListener,
                                drawAttr
                            )
                        } else if (textOrImageItem is ConvertContentItem.ImageMetaData) {
                            val result = layoutHelper.checkAndAddImageView(
                                this.context,
                                textOrImageItem,
                                convertContentItem,
                                lastTopView,
                                this,
                                isLastItem,
                                onClickListener,
                                drawAttr
                            )
                            result?.second?.let { mImageLoadDatas.add(Pair(itemIndex, it)) }
                            result?.first
                        } else {
                            null
                        }
                    if (currentAddView != null) {
                        this.addView(currentAddView)
                        if (currentAddView is BackgroundTextView) {
                            mTextViewList.add(Pair(itemIndex, currentAddView))
                        } else if (currentAddView is ShapeableImageView) {
                            mImageViews.add(Pair(itemIndex, currentAddView))
                        }
                        lastTopView = currentAddView
                    }
                }
            }
        }
    }

    interface OnMixLayoutClickListenner {
        fun onImageViewClick(
            view: ImageView,
            convertContentItem: ConvertContentItem?,
            currentDataBean: MarkDataBean
        )

        fun onTextViewClick(
            view: View,
            convertContentItem: ConvertContentItem?,
            currentItemIndex: Int
        )
    }
}