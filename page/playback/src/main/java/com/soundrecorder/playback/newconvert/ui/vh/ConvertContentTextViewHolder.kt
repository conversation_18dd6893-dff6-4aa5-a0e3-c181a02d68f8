/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ConvertContentTextViewHolder.kt
 ** Description : ConvertContentTextViewHolder.kt
 ** Version     : 1.0
 ** Date        : 2025/07/04
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/07/04      1.0      create
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui.vh

import android.view.LayoutInflater
import android.view.ViewGroup
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.R
import com.soundrecorder.playback.newconvert.ui.SeekPlayActionModeCallback
import com.soundrecorder.playback.newconvert.ui.TextImageItemAdapter
import com.soundrecorder.playback.newconvert.view.BackGroundTextViewSetupHelper
import com.soundrecorder.playback.newconvert.view.BackgroundTextView
import java.lang.ref.WeakReference

class ConvertContentTextViewHolder(parent: ViewGroup, adapter: TextImageItemAdapter) : AbsContentViewHolder(
    LayoutInflater.from(parent.context)
        .inflate(R.layout.item_convert_content_text, parent, false)
) {

    companion object {
        private const val TAG = "ConvertContentTextViewHolder"
    }

    var textView: BackgroundTextView = itemView.findViewById(R.id.contentTextView)

    private val setupHelper: BackGroundTextViewSetupHelper = BackGroundTextViewSetupHelper()

    private var mSubItemData: ConvertContentItem.TextItemMetaData? = null
    private var mParentData: ConvertContentItem? = null

    private val mWeakAdapter = WeakReference(adapter)

    //长按文字的相关callback
    private var actionModeCallback = SeekPlayActionModeCallback()

    init {
        mWeakAdapter.get()?.getTextHighlightType()?.let { textView.setHighlightType(it) }
    }

    fun setData(subItemData: ConvertContentItem.ItemMetaData, parentData: ConvertContentItem) {
        if (subItemData is ConvertContentItem.TextItemMetaData) {
            mSubItemData = subItemData
            mParentData = parentData
            generateLayoutParagrameForText(subItemData, parentData)
        }
    }

    private fun generateLayoutParagrameForText(
        subItemData: ConvertContentItem.TextItemMetaData,
        parentData: ConvertContentItem
    ) {
        val searchMode = mWeakAdapter.get()?.searchMode
        val textClickCallback = mWeakAdapter.get()?.mTextClickCallback
        val drawAttr = mWeakAdapter.get()?.drawAttr
        if (searchMode == null || textClickCallback == null || drawAttr == null) {
            DebugUtil.d(TAG, "generateLayoutParagrameForText searchMode or textClickCallback or drawattr is null")
            return
        }
        val subItemIndex = parentData.mTextOrImageItems?.indexOf(subItemData) ?: -1
        val subItemListSize = parentData.mTextOrImageItems?.size ?: -1
        val isFirstOne = (subItemListSize != -1) && (subItemIndex == 1) //第0个item为固定的TIME_DIVIDER类型的item，所以这个判定为index=1
        val isLastOne = (subItemListSize != -1) && (subItemIndex == (subItemListSize - 1))
        setupHelper.setUpBackgroundTextView(
            subItemIndex,
            subItemData,
            parentData,
            textView,
            isFirstOne,
            isLastOne,
            searchMode,
            actionModeCallback,
            textClickCallback,
            drawAttr,
            mWeakAdapter.get()?.mContentScrollHelper?.getCurrentPlaybackTime() ?: 0L
        )
    }

    fun setKeyWordSpannable(data: ConvertContentItem.TextItemMetaData, position: Int) {
        val searchHelper = mWeakAdapter.get()?.searchHelper
        val convertSearchCurrentFocus = mWeakAdapter.get()?.mConvertSearchCurrentFocus
        if (searchHelper == null || convertSearchCurrentFocus == null) {
            DebugUtil.d(TAG, "setKeyWordSpannable searchHelper or convertSearchCurrentFocus is null")
            return
        }
        // 设置searchBeans中的isFocus参数
        val itemSearchResult = searchHelper.filterCurrentPositionSearchResult(
            position,
            convertSearchCurrentFocus
        )
        mWeakAdapter.get()?.setTextItemContent(textView, data, itemSearchResult)
    }

    fun switchSentenceBackground() {
        val searchMode = mWeakAdapter.get()?.searchMode
        if (searchMode == null) {
            DebugUtil.d(TAG, "switchSentenceBackground searchMode is null")
            return
        }
        val parentData = mParentData
        val subItemData = mSubItemData
        if (parentData == null || subItemData == null) {
            DebugUtil.i(
                TAG,
                "switchSentenceBackground parentData $parentData, subItemData $subItemData, null return"
            )
            return
        }
        val itemIndex = parentData.mTextOrImageItems?.indexOf(subItemData) ?: -1
        //点击标记功能后，更新显示小旗子图标
        setupHelper.setContentMarkedSpannable(
            textView,
            parentData,
            subItemData,
            itemIndex
        )
        setupHelper.switchSentenceBackground(
            textView,
            parentData,
            subItemData,
            itemIndex,
            searchMode,
            mWeakAdapter.get()?.mContentScrollHelper?.getCurrentPlaybackTime() ?: 0L
        )
    }

    /**
     * show speaker view animator
     */
    override fun showAnimate() {
        mWeakAdapter.get()?.showItemTextContentAnimation(itemView)
    }

    /**
     * dismiss speaker view animator
     */
    override fun dismissAnimate() {
        mWeakAdapter.get()?.dismissItemTextContentAnimation(itemView)
    }


    override fun refreshSpeaker() {
        mWeakAdapter.get()?.mNeedShowRole?.let { switchSpeaker(it) }
    }

    /**
     * switch speaker view state
     */
    private fun switchSpeaker(flag: Boolean) {
        if (flag) {
            val y = BaseApplication.getAppContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp4)
            itemView.translationY = y
        } else {
            itemView.translationY = 0f
        }
    }

    override fun release() {
        itemView.animate().cancel()
    }


    override fun releaseBitmapCache() {}
}