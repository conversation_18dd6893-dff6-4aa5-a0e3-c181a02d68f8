/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/18
 * * Author      : kewei<PERSON>@oppo.com
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout
import android.widget.ScrollView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePaddingRelative
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.common.widget.OSImageView

abstract class ConvertView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ScrollView(context, attrs, defStyleAttr) {

    companion object {
        private const val DELAY = 100L
    }

    protected var bottomInset: Int = 0
    protected var loadingView: OSImageView? = null
    protected var textLayout: LinearLayout? = null

    private var lastScale = -1F
    private var lastContentHeight = -1

    private val marginTop by lazy {
        context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.play_convert_margin_top)
    }

    init {
        initSelf()
        initContainer()
        initViews()
        loadingView = initLoadingView()
        textLayout = initTextLayout()
    }

    private fun initSelf() {
        val paddingStartEnd = context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp16)
        updatePaddingRelative(paddingStartEnd, 0, paddingStartEnd, 0)
        isFillViewport = true
    }

    /**
     * 初始化container
     */
    abstract fun initContainer()

    /**
     * 初始化其他视图
     */
    abstract fun initViews()

    /**
     * 初始化osimage
     */
    abstract fun initLoadingView(): OSImageView?

    /**
     * 初始化textview视图
     */
    abstract fun initTextLayout(): LinearLayout?

    /**
     * 获取tag
     */
    abstract fun tag(): String

    fun release() {
        loadingView?.release()
    }

    fun initRefresh(width: Int, height: Int) {
        post {
            if ((context as? Activity)?.isDestroyed == true) return@post
            notifyViewSizeChange(width, height)
            startAnim()
        }
    }

    fun startAnim() {
        loadingView?.initImageResource()
    }

    fun cancelAnim() {
        loadingView?.cancelJsonAnimation()
    }

    fun notifyViewSizeChange(width: Int, height: Int) {
        val activity = context as? Activity ?: return
        val loadingView = this.loadingView ?: return
        val widthDp = activity.px2dp(width).toInt()
        val heightDp = activity.px2dp(height).toInt()
        val scale = loadingView.getNeedScale(widthDp, heightDp)
        DebugUtil.d(tag(), "notifyViewSizeChange $lastScale $scale $widthDp $heightDp")
        if (lastScale != scale) {
            loadingView.setScaleByEmptySize(widthDp, heightDp, tag())
        } else {
            DebugUtil.d(tag(), "notifyViewSizeChange scale一样，不需要更新 = $scale")
        }
        if (scale == 0f) {
            setLayoutParamsWhenImageGone()
        } else {
            setLayoutParamsWhenShowImage()
        }
        lastScale = scale

        if (isVisible && height != lastContentHeight) {
            postDelayed({
                fullScroll(FOCUS_DOWN)
                lastContentHeight = height
            }, DELAY)
        }
    }

    open fun setLayoutParamsWhenImageGone() {
        updateLayoutParams<LayoutParams> {
            bottomMargin = <EMAIL>
        }
        textLayout?.updateLayoutParams<ConstraintLayout.LayoutParams> {
            topToTop = ConstraintLayout.LayoutParams.PARENT_ID
            bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
            bottomToTop = ConstraintLayout.LayoutParams.UNSET
            topToBottom = ConstraintLayout.LayoutParams.UNSET
            bottomMargin = 0
        }
    }

    open fun setLayoutParamsWhenShowImage() {
        updateLayoutParams<LayoutParams> {
            bottomMargin = <EMAIL>
        }
        textLayout?.updateLayoutParams<ConstraintLayout.LayoutParams> {
            topToTop = ConstraintLayout.LayoutParams.UNSET
            topToBottom = loadingView?.id ?: 0
            bottomToBottom = ConstraintLayout.LayoutParams.UNSET
        }
        loadingView?.updateLayoutParams<ConstraintLayout.LayoutParams> {
            topToTop = ConstraintLayout.LayoutParams.PARENT_ID
            bottomToTop = textLayout?.id ?: 0
            topMargin = getLoadingTopMargin()
        }
    }

    private fun getLoadingTopMargin(): Int {
        val activity = context as? Activity ?: return 0
        val screenHeight = ScreenUtil.getRealHeight(activity)
        val windowHeight = activity.window.decorView.height
        val scaleRadio = windowHeight / screenHeight.toFloat()
        DebugUtil.d(tag(), "screenHeight = $screenHeight, " +
                "windowHeight = $windowHeight, " +
                "scaleRadio = $scaleRadio, " +
                "bottomInset = $bottomInset")
        return (marginTop * scaleRadio).toInt()
    }

    fun updatePaddingBottom(paddingBottom: Int) {
        DebugUtil.d(tag(), "updatePaddingBottom paddingBottom = $paddingBottom")
        this.bottomInset = paddingBottom
    }
}