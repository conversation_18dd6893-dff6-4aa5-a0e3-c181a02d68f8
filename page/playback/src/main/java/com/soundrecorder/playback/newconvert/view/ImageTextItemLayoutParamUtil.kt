/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.content.Context
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import com.soundrecorder.playback.R
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem

object ImageTextItemLayoutParamUtil {

    const val TAG = "ImageTextItemLayoutParamUtil"

    @JvmStatic
    fun getConstraintLayoutParamForTextItemAndSetIds(
        context: Context,
        isFirstOne: Boolean,
        isLastOne: Boolean,
        drawAttr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr,
        anchorTopView: View?,
        parentView: View
    ): ConstraintLayout.LayoutParams? {
        val textLayoutParams = getConstraintLayoutParamForTextItem(context, isFirstOne, drawAttr)
        textLayoutParams?.let {
            setConstrainstParamIds(anchorTopView, parentView, isLastOne, it)
        }
        return textLayoutParams
    }

    @JvmStatic
    fun getConstraintLayoutParamForTextItem(
        context: Context,
        isFirstOne: Boolean,
        drawAttr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr
    ): ConstraintLayout.LayoutParams? {
        val layoutParams: ConstraintLayout.LayoutParams =
            ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            )
        layoutParams.topMargin = context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp3)
        if (isFirstOne) {
            //第一个item需要加入firstTopMargin
            layoutParams.topMargin = drawAttr.firstItemTopMargin
            DebugUtil.i(
                TAG,
                "checkAndAddBackgroundTextView first one set topMargin ${layoutParams.topMargin}"
            )
        } else {
            //非第一个item，上一个是肯定是ImageView，所以这个地方需要加上ImageView和TextView之间的topMargin
            layoutParams.topMargin = drawAttr.marginBetweenImageAndText
            DebugUtil.i(
                TAG,
                "checkAndAddBackgroundTextView not first one set topMargin ${layoutParams.topMargin}"
            )
        }
        return layoutParams
    }

    @JvmStatic
    fun setTopMarginsForBackGroundTextView(
        isFirstOne: Boolean,
        drawAttr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr,
        targetTextView: BackgroundTextView?
    ) {
        val layoutParams = targetTextView?.layoutParams as MarginLayoutParams?
        if (layoutParams != null) {
            if (isFirstOne) {
                //第一个item需要加入firstTopMargin
                layoutParams.topMargin = drawAttr.firstItemTopMargin
                DebugUtil.i(
                    TAG,
                    "setTopMarginsForBackGroundTextView first one set topMargin ${layoutParams.topMargin}"
                )
            } else {
                //非第一个item，上一个是肯定是ImageView，所以这个地方需要加上ImageView和TextView之间的topMargin
                layoutParams.topMargin = drawAttr.marginBetweenImageAndText
                DebugUtil.i(
                    TAG,
                    "setTopMarginsForBackGroundTextView not first one set topMargin ${layoutParams.topMargin}"
                )
            }
        }
        targetTextView?.layoutParams = layoutParams
    }

    @JvmStatic
    @Suppress("LongParameterList")
    fun getConstraintLayoutParamForImageItemAndSetIds(
        context: Context,
        currentItem: ConvertContentItem.ImageMetaData?,
        isFirstOne: Boolean,
        isLastOne: Boolean,
        anchorTopView: View?,
        parentView: View,
        lastItemType: Int,
        drawAttr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr
    ): ConstraintLayout.LayoutParams? {
        val imageLayoutParams = getConstraintLayoutParamForImageItem(context, currentItem, isFirstOne, isLastOne, lastItemType, drawAttr)
        imageLayoutParams?.let {
            setConstrainstParamIds(anchorTopView, parentView, isLastOne, it)
        }
        return imageLayoutParams
    }

    @JvmStatic
    fun getConstraintLayoutParamForImageItem(
        context: Context,
        currentItem: ConvertContentItem.ImageMetaData?,
        isFirstOne: Boolean,
        isLastOne: Boolean,
        lastItemType: Int,
        drawAttr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr
    ): ConstraintLayout.LayoutParams? {
        var currentPictureMarkDataBean: MarkDataBean = currentItem?.imageItem ?: return null
        var maxtWithAndRatio = ImageWithHeightCaculateUtil.getImageViewMaxtWithAndRatio(context)
        var imageViewConfig = ImageWithHeightCaculateUtil.caculateImageViewWithAndHeight(
            currentPictureMarkDataBean,
            maxtWithAndRatio
        )
        val layoutParams: ConstraintLayout.LayoutParams = ConstraintLayout.LayoutParams(
            imageViewConfig.imageViewWidth,
            imageViewConfig.imageViewHeight
        )
        if (isFirstOne) {
            //第一个item需要加入firstTopMargin
            layoutParams.topMargin = drawAttr.firstItemTopMargin
            DebugUtil.i(
                TAG,
                "checkAndAddImageView first one set topMargin ${layoutParams.topMargin}"
            )
        } else {
            //非第一个item，需要区分上一个是imageView还是textView，根据上一个的imageView或textView动态添加view的topMargin
            if (lastItemType == ConvertContentItem.ItemMetaData.TYPE_IMAGE) {
                layoutParams.topMargin = drawAttr.marginBetweenImages
            } else if (lastItemType == ConvertContentItem.ItemMetaData.TYPE_TEXT) {
                layoutParams.topMargin = drawAttr.marginBetweenTextAndImage
            }
            DebugUtil.i(
                TAG,
                "checkAndAddImageView not first one set topMargin ${layoutParams.topMargin}"
            )
        }
        if (isLastOne) {
            layoutParams.bottomMargin = drawAttr.lastItemButtomMargin
            //layoutParams.bottomToBottom = parentView.id
            DebugUtil.i(
                TAG,
                "checkAndAddImageView last one set bottomMargin ${layoutParams.bottomMargin}"
            )
        }
        return layoutParams
    }

    @JvmStatic
    @Suppress("LongParameterList")
    fun setupLayoutParamForImageItem(
        context: Context,
        currentItem: ConvertContentItem.ImageMetaData?,
        isFirstOne: Boolean,
        isLastOne: Boolean,
        lastItemType: Int,
        drawAttr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr,
        targetImageView: ImageView
    ) {
        var currentPictureMarkDataBean: MarkDataBean = currentItem?.imageItem ?: return
        var maxtWithAndRatio = ImageWithHeightCaculateUtil.getImageViewMaxtWithAndRatio(context)
        var imageViewConfig = ImageWithHeightCaculateUtil.caculateImageViewWithAndHeight(
            currentPictureMarkDataBean,
            maxtWithAndRatio
        )
        val layoutParams: LinearLayout.LayoutParams = LinearLayout.LayoutParams(
            imageViewConfig.imageViewWidth,
            imageViewConfig.imageViewHeight
        )
        if (isFirstOne) {
            //第一个item需要加入firstTopMargin
            layoutParams.topMargin = drawAttr.firstItemTopMargin
            DebugUtil.i(
                TAG,
                "checkAndAddImageView first one set topMargin ${layoutParams.topMargin}"
            )
        } else {
            //非第一个item，需要区分上一个是imageView还是textView，根据上一个的imageView或textView动态添加view的topMargin
            if (lastItemType == ConvertContentItem.ItemMetaData.TYPE_IMAGE) {
                layoutParams.topMargin = drawAttr.marginBetweenImages
            } else if (lastItemType == ConvertContentItem.ItemMetaData.TYPE_TEXT) {
                layoutParams.topMargin = drawAttr.marginBetweenTextAndImage
            } else if (lastItemType == ConvertContentItem.ItemMetaData.TYPE_TIME_DIVIDER) {
                layoutParams.topMargin = 0
            }
            DebugUtil.i(
                TAG,
                "checkAndAddImageView not first one set topMargin ${layoutParams.topMargin}"
            )
        }
        if (isLastOne) {
            layoutParams.bottomMargin = drawAttr.lastItemButtomMargin
            //layoutParams.bottomToBottom = parentView.id
            DebugUtil.i(
                TAG,
                "checkAndAddImageView last one set bottomMargin ${layoutParams.bottomMargin}"
            )
        } else {
            layoutParams.bottomMargin = drawAttr.bottomMargin
        }
        targetImageView.layoutParams = layoutParams
    }

    @JvmStatic
    private fun setConstrainstParamIds(
        anchorTopView: View?,
        parentView: View,
        isLastOne: Boolean,
        layoutParams: ConstraintLayout.LayoutParams
    ) {
        if (anchorTopView == null) {
            //布局内第一个ItemView,需要参考parentView
            layoutParams.topToTop = parentView.id
            layoutParams.startToStart = parentView.id
        } else {
            //布局内非第一个ItemView
            layoutParams.topToBottom = anchorTopView.id
            layoutParams.startToStart = parentView.id
        }
        if (isLastOne) {
            layoutParams.bottomToBottom = parentView.id
        }
    }
}