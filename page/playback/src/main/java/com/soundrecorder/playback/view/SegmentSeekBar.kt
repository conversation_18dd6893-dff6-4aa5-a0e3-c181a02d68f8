/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SegmentSeekBar
 * Description:
 * Version: 1.0
 * Date: 2025/3/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/18 1.0 create
 */

package com.soundrecorder.playback.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.RectF
import android.util.AttributeSet
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.widget.seekbar.COUISeekBarOS15

class SegmentSeekBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0,
) : COUISeekBarOS15(context, attrs, defStyleAttr, defStyleRes) {
    companion object {
        const val DEFAULT_BORDER_SIZE = 1f
    }
    private val logTag = "SegmentSeekBar"
    private var segmentList: List<Pair<Float, Float>>? = null
    private var segmentPaint: Paint? = null
    private var duration: Long = 0L
    private var thumbBordersPaint: Paint? = null
    private var thumbBordersSize: Float? = DEFAULT_BORDER_SIZE
    private var thumbBorderColor: Int? = null
    private var thumbRadiusSize: Float? = null

    init {
        thumbBordersPaint = Paint().apply {
            isAntiAlias = true
            style = Paint.Style.STROKE
        }
        val typedArray = context.obtainStyledAttributes(
            attrs,
            com.soundrecorder.common.R.styleable.CustomSeekBar,
            defStyleAttr,
            0
        )
        typedArray.let {
            thumbRadiusSize = it.getDimension(com.soundrecorder.common.R.styleable.CustomSeekBar_thumbRadiusSize, DEFAULT_BORDER_SIZE)
            thumbBordersSize = it.getDimension(com.soundrecorder.common.R.styleable.CustomSeekBar_thumbBordersSize, DEFAULT_BORDER_SIZE)
            thumbBorderColor = it.getColor(com.soundrecorder.common.R.styleable.CustomSeekBar_thumbBordersColor,
                COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
            )
            typedArray.recycle()
        }
        mPaddingHorizontal = mProgressHeight / 2
        segmentPaint = Paint().apply {
            style = Paint.Style.FILL
            color = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
        }
    }

    /**
     * 设置音频总时长，毫秒
     */
    fun setDuration(durationMill: Long) {
        this.duration = durationMill
    }

    /**
     * 设置时间片，对应的时间段毫秒
     */
    fun setSegmentTimeMillList(list: List<Pair<Long, Long>>?) {
        if (list.isNullOrEmpty() && segmentList.isNullOrEmpty()) {
            /**改变前后数据都为null，则不用处理*/
            DebugUtil.d(logTag, "setSegmentTimeMillList return by 2 list is empty")
            return
        }
        DebugUtil.d(logTag, "setSegmentTimeMillList,list.size=${list?.size},segmentList.size=${segmentList?.size}")
        segmentList = convertTimeMillToProgressPercent(list, duration)
        mShowProgress = segmentList.isNullOrEmpty()
        invalidate()
    }

    /**
     * 将时间片转换成进度比例（当前时间戳/总时长）
     */
    private fun convertTimeMillToProgressPercent(timeList: List<Pair<Long, Long>>?, duration: Long): List<Pair<Float, Float>>? {
        if (timeList.isNullOrEmpty()) {
            return null
        }
        if (duration <= 0) {
            DebugUtil.w(logTag, "convertTimeMillToProgressPercent duration is $duration")
            return null
        }
        val result = arrayListOf<Pair<Float, Float>>()
        timeList.forEach {
            result.add(Pair(it.first.toFloat() / duration, it.second.toFloat() / duration))
        }
        return result
    }


    override fun onDraw(canvas: Canvas) {
        thumbRadiusSize?.let {
            mThumbRadius = it
            mCurThumbRadius = it
        }
        val seekBarWidth = this.seekBarWidth.toFloat()
        drawInactiveTrack(canvas)
        drawSegmentProgress(canvas, seekBarWidth)
        drawActiveTrack(canvas, seekBarWidth)
        drawThumbBorder(canvas)
    }

    private fun drawThumbBorder(canvas: Canvas) {
        if (this.mShowThumb) {
            thumbBordersPaint?.let {
                it.strokeWidth = this.thumbBordersSize ?: DEFAULT_BORDER_SIZE
                it.color = thumbBorderColor ?: COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
                val centerY = this.seekBarCenterY
                val thumbInnerAreaLeft = this.mThumbPosition - this.mCurThumbRadius
                val thumbInnerAreaRight = this.mThumbPosition + this.mCurThumbRadius

                canvas.drawRoundRect(
                    thumbInnerAreaLeft, centerY.toFloat() - this.mCurThumbRadius,
                    thumbInnerAreaRight, centerY.toFloat() + this.mCurThumbRadius,
                    this.mCurThumbRadius,
                    this.mCurThumbRadius,
                    it
                )
            }
        }
    }

    private fun drawSegmentProgress(canvas: Canvas, seekBarWidth: Float) {
        if (segmentList.isNullOrEmpty()) {
            return
        }
        var progressPadding = 0.0f
        var progressWidth = 0.0f

        val progressHeight = mProgressHeight
        val radius = progressHeight / 2
        if (!mShowThumb) {
            progressPadding = mCurPaddingHorizontal - radius
            progressWidth = seekBarWidth + radius * 2.0f
        } else {
            progressPadding = mCurPaddingHorizontal + (mThumbOutHeight / 2.0f - mThumbOutRadius)
            progressWidth = seekBarWidth - (mThumbOutHeight - mThumbOutRadius * 2.0f)
        }

        val centerY = this.seekBarCenterY
        val startY = centerY - progressHeight / 2
        val endY = centerY + progressHeight / 2
        segmentPaint?.let { segmentPaint ->
            segmentList?.forEach {
                if (isLayoutRtl) {
                    val startX = (1 - it.first) * progressWidth + start + progressPadding + mThumbOutHeight / 2.0f
                    val endX = (1 - it.second) * progressWidth + start + progressPadding - mThumbOutHeight / 2.0f
                    canvas.drawRoundRect(RectF(startX, startY, endX, endY), radius, radius, segmentPaint)
                } else {
                    val startX = it.first * progressWidth + start + progressPadding - mThumbOutHeight / 2.0f
                    val endX = it.second * progressWidth + start + progressPadding + mThumbOutHeight / 2.0f
                    canvas.drawRoundRect(RectF(startX, startY, endX, endY), radius, radius, segmentPaint)
                }
            }
        }
    }
}