/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IConvertViewController
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/11/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.convert

import androidx.recyclerview.widget.LinearLayoutManager
import com.soundrecorder.playback.PlaybackActivityViewModel
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel

interface IConvertViewController {
    fun setNeedShowRoleName(needShowRole: Boolean)
    fun scrollToLastPositionByManual()
    fun getCustomLinearLayoutManager(): LinearLayoutManager?

    fun dismissRenameSpeakerDialog()

    /**
     * 退出搜索模式的动效
     */
    fun animSearchOut()

    /**
     * 保存搜索动效View的高度
     */
    fun saveKeyWordViewHeight()

    /**
     * 记录滚动的位置
     * @param from
     */
    fun saveScrollPosition(from: String)

    fun startConvertInitAnimation()

    /**
     * 退出搜索检测播放位置与当前位置
     * 是否要显示返回气泡
     */
    fun checkOutSearchShowBackOfLocation()

    fun release()

    fun getViewModel(): PlaybackActivityViewModel?

    fun getConvertViewModel(): PlaybackConvertViewModel?

    fun cancelConvertAnim()

    /**
     * 执行进入沉浸态动画
     */
    fun startImmersiveAnimation()

    /**
     * 执行退出沉浸态动画
     */
    fun reverseImmersiveAnimation()

    /**
     * 设置底部padding
     */
    fun updatePaddingBottom(paddingBottom: Int)
}