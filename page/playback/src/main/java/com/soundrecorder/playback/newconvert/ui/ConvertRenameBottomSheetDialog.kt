/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui

import android.content.Context
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.coui.appcompat.checkbox.COUICheckBox
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.edittext.COUIEditText
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.dialog.AbsEditAlertDialog
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.playback.R
import com.soundrecorder.playback.view.RestrictChipGroup

class ConvertRenameBottomSheetDialog(
    activity: AppCompatActivity
) : AbsEditAlertDialog(activity) {

    companion object {
        private const val TAG = "ConvertTextRenameBottomSheetDialog"
    }

    var recordId: Long = -1
    var roleNameList: List<String>? = null
    var mIsModifyAll: Boolean = false
    var convertRenameCb: COUICheckBox? = null
    var tagChipGroup: RestrictChipGroup? = null
    var tagDeleteAll: ImageView? = null
    var mEditText: COUIEditText? = null
    var content: String? = null

    /* 讲话人名称的原始值 */
    var originContent: String = ""

    private var mOnClickListener: OnClickListener? = null
    private lateinit var mLinear: LinearLayout
    private var mDeletePopupWindow: COUIPopupListWindow? = null
    private var mDeleteItem: MutableList<PopupListItem>? = null
    private var mDeleteAllDialog: AlertDialog? = null
    private var mSaveItem: MenuItem? = null
    //rename speaker from click history tips list
    private var isClickHistory: Boolean = false

    override fun getLayoutId(): Int {
        return R.layout.layout_bottom_sheet_convert_rename
    }
    override fun onInitCustomView(customView: View) {
        super.onInitCustomView(customView)
        mEditText = customView.findViewById(R.id.coui_edit)
        tagDeleteAll = customView.findViewById(R.id.tag_deleteAll)
        tagChipGroup = customView.findViewById(R.id.tag_chipGroup)
        mLinear = customView.findViewById(R.id.content)
        mEditText?.apply {
            hint = activity.getString(com.soundrecorder.common.R.string.enter_person_name)
            contentDescription = activity.getString(com.soundrecorder.common.R.string.enter_person_name)
        }
        initView()
    }

    private fun initView() {
        initFlows()
        convertRenameCb = customView.findViewById(R.id.convert_rename_cb)
        convertRenameCb?.text =
            activity.resources?.getString(com.soundrecorder.common.R.string.modify_all_rolename, originContent)
        activity.resources?.getColor(R.color.modify_all_rolename)
            ?.let { convertRenameCb?.setTextColor(it) }
        convertRenameCb?.state = if (mIsModifyAll) {
            COUICheckBox.SELECT_ALL
        } else {
            COUICheckBox.SELECT_NONE
        }
        convertRenameCb?.setOnStateChangeListener { _, i ->
            mIsModifyAll = i == COUICheckBox.SELECT_ALL
        }
        //if text changed after click history tips list , set isClickHistory false
        mEditText?.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                isClickHistory = false
                textChangeSaveIcon()
            }
        })

        mDeleteItem = ArrayList()
        mDeleteItem?.apply {
            add(
                PopupListItem(
                    BaseApplication.getAppContext().resources.getString(com.soundrecorder.common.R.string.delete),
                    true
                )
            )
        }
    }

    private fun textChangeSaveIcon() {
        val inputText = mEditText?.text?.toString()
        val inputTextTrim = inputText?.trim()
        mSaveItem?.isEnabled = !TextUtils.isEmpty(inputTextTrim)
    }

    private fun deleteOneByOne(chip: COUIChip) {
        val text = chip.text.toString()
        DebugUtil.e(TAG, "text = $text")
        mOnClickListener?.onHistoryItemDeleteClick(recordId, false, text)
        val toMutableList = roleNameList?.toMutableList()
        toMutableList?.remove(text)
        roleNameList = toMutableList?.toList()
        if (roleNameList?.size == 0) {
            mLinear.visibility = View.GONE
            tagChipGroup?.removeAllViews()
        } else {
            tagChipGroup?.removeView(chip)
        }
    }

    private fun initFlows() {
        if (roleNameList.isNullOrEmpty()) {
            mLinear.visibility = View.GONE
        }
        tagDeleteAll?.setOnClickListener {
            if (popWindowIsShow()) {
                return@setOnClickListener
            }
            if (mDeleteAllDialog != null) {
                return@setOnClickListener
            }
            showDeleteAllDialog()
        }

        initChips()
    }

    @Suppress("TooGenericExceptionCaught")
    private fun showPopWindow(chip: COUIChip) {
        if (mDeletePopupWindow == null) {
            mDeletePopupWindow = COUIPopupListWindow(activity).apply {
                itemList = mDeleteItem
                setDismissTouchOutside(true)
                setOnItemClickListener { _, _, _, _ ->
                    mDeletePopupWindow?.let {
                        if (it.isShowing) {
                            deleteOneByOne(chip)
                            it.dismiss()
                        }
                    }
                }
                setOnDismissListener {
                    chip.setBackgroundResource(com.support.chip.R.color.chip_unchecked_background_color)
                    convertRenameCb?.isEnabled = true
                    mDeletePopupWindow = null
                }
            }
        }
        if (mDeletePopupWindow?.isShowing == false) {
            mDeletePopupWindow?.isFocusable = false
            try {
                mDeletePopupWindow?.show(chip)
            } catch (e: Exception) {
                DebugUtil.e(TAG, "showPopWindow show error:", e)
            }
            convertRenameCb?.isEnabled = false
        }
    }

    private fun popWindowIsShow(): Boolean {
        if (mDeletePopupWindow != null && mDeletePopupWindow?.isShowing == true) {
            return true
        }
        return false
    }

    private fun deleteAllDialogIsShow(): Boolean {
        if (mDeleteAllDialog?.isShowing == true) {
            return true
        }
        return false
    }

    fun dismissPop() {
        if (popWindowIsShow()) {
            mDeletePopupWindow?.dismiss()
        }
    }

    private fun initChips() {
        val size = roleNameList?.size ?: 0
        tagChipGroup?.removeAllViews()
        for (i in size - 1 downTo 0) {
            val name = roleNameList?.get(i)
            val chip: COUIChip = LayoutInflater.from(activity)
                .inflate(R.layout.item_tag, tagChipGroup, false) as COUIChip
            chip.text = name
            tagChipGroup?.addView(chip)
            chip.apply {
                setOnClickListener {
                    if (!popWindowIsShow()) {
                        mEditText?.setText(name)
                        //Set isClickHistory true when history tips list item was clicked.
                        isClickHistory = true
                    }
                }

                setOnLongClickListener {
                    chip.setBackgroundResource(com.support.appcompat.R.color.coui_btn_check_color_off_disabled)
                    showPopWindow(this)
                    true
                }
            }
        }
    }

    override fun getTextNoteView(): TextView? {
        return customView.findViewById(R.id.dlg_rename_note)
    }
    override fun getEditText(): COUIEditText? {
        return mEditText
    }

    override fun getTitleText(): Int {
        return com.soundrecorder.common.R.string.rename
    }

    private fun showDeleteAllDialog() {
        val builder = COUIAlertDialogBuilder(activity as Context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
        builder.setBlurBackgroundDrawable(true)
        builder.setPositiveButton(com.soundrecorder.common.R.string.role_name_tag_clear) { dialog, which ->
            mOnClickListener?.onClearClick(recordId)
            roleNameList?.toMutableList()?.clear()
            mLinear.visibility = View.GONE
            tagChipGroup?.removeAllViews()
        }
        builder.setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
        builder.setOnDismissListener {
            mDeleteAllDialog = null
        }
        builder.setCancelable(true)
        mDeleteAllDialog = builder.create()
        mDeleteAllDialog?.show()
        ViewUtils.updateWindowLayoutParams(mDeleteAllDialog?.window)
    }

    private fun onSaveClick(checked: Boolean) {
        if (popWindowIsShow()) {
            DebugUtil.d(TAG, "onSaveClick  popwindow is showing return")
            return
        }
        val inputText = mEditText?.text?.toString()
        val inputTextTrim = inputText?.trim()
        if (TextUtils.isEmpty(inputTextTrim)) {
            DebugUtil.d(TAG, "onSaveClick  inputText isEmpty return")
            return
        }
        DebugUtil.d(TAG, "originContent:$originContent inputText:$inputTextTrim")

        if (TextUtils.equals(inputTextTrim, originContent)) {
            dismiss()
            return
        }
        val resultName = inputText ?: ""
        saveRename(resultName, checked)
    }

    private fun onCancelClick() {
        if (popWindowIsShow()) {
            DebugUtil.d(TAG, "onCancelClick  popwindow is showing return")
            return
        }
        mOnClickListener?.onNegtiveClick()
        dismiss()
    }


    private fun saveRename(roleName: String, checked: Boolean) {
        mOnClickListener?.onPositiveClick(recordId, checked, roleName, isClickHistory)
        DebugUtil.d(TAG, "addHistoryRoleName $recordId $roleName")
        dismiss()
    }

    override fun onSave() {
        onSaveClick(mIsModifyAll)
    }

    override fun onCancel() {

        onCancelClick()
    }
    override fun getOriginalContent() = content ?: ""

    override fun onWindowDetached() {
        super.onWindowDetached()
        mOnClickListener = null
        if (popWindowIsShow()) {
            mDeletePopupWindow?.dismiss()
            mDeletePopupWindow = null
        }
        if (deleteAllDialogIsShow()) {
            mDeleteAllDialog?.dismiss()
            mDeleteAllDialog = null
        }
        mDeleteItem = null
    }

    fun setClickListener(listener: OnClickListener?) {
        mOnClickListener = listener
    }


    interface OnClickListener {
        fun onPositiveClick(
            recordId: Long,
            checked: Boolean,
            roleName: String,
            isClickHistory: Boolean
        )

        fun onClearClick(
            recordId: Long,
        )

        fun onHistoryItemDeleteClick(
            recordId: Long,
            checked: Boolean,
            roleName: String,
        )

        fun onNegtiveClick()
    }
}