/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.search.view

import android.content.Context
import android.text.TextUtils
import android.util.AttributeSet
import android.widget.ImageButton
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.soundrecorder.playback.R
import com.soundrecorder.base.utils.DebugUtil

/**
 * 转文本搜索结果的view，显示在最底部
 */
open class ConvertSearchBottomArea @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "ConvertSearchBottomArea"
        const val POS_IMG_PREVIOUS = 0
        const val POS_IMG_NEXT = 1
    }

    private var previousImg: ImageButton? = null
    private var nextImg: ImageButton? = null
    private var resultTv: TextView? = null
    private var currentPos = 0
    private var totalCount = 0
    private var searchContent = ""

    var positionChangeListener: SearchPositionChangeListener? = null

    init {
        inflate(context, R.layout.view_convert_search_bottom, this)
        initView()
        initData()
        initEvent()
    }

    private fun initView() {
        previousImg = findViewById(R.id.ib_search_previous)
        resultTv = findViewById(R.id.tv_search_result)
        nextImg = findViewById(R.id.ib_search_next)
    }

    /**
     * 设置初始内容
     */
    private fun initData() {
        resultTv?.text =
            context.getString(com.soundrecorder.common.R.string.search_position_total_text, 0, 0)
        resultTv?.isEnabled = false
        previousImg?.isEnabled = false
        nextImg?.isEnabled = false
    }

    private fun initEvent() {
        previousImg?.setOnClickListener {
            val prev = currentPos - 1
            DebugUtil.i(TAG, "click jump prev btn. prev=$prev")
            if (prev >= 0) {
                currentPos = prev
                jumpPos(POS_IMG_PREVIOUS)
            }
        }

        nextImg?.setOnClickListener {
            val next = currentPos + 1
            DebugUtil.i(TAG, "click jump next btn. next=$next")
            if (next < totalCount) {
                currentPos = next
                jumpPos(POS_IMG_NEXT)
            }
        }
    }

    /**
     * 跳转到具体的位置
     */
    private fun jumpPos(imgBtnPos: Int = -1) {
        val isInSearch = isInSearch()
        DebugUtil.d(TAG, "jumpPos currentPos is $currentPos totalCount: $totalCount inSearch:$isInSearch")
        if (totalCount <= 0) { // 搜索结果为空
            if (isInSearch) { // 处于搜索状态，则显示搜索结果为空
                setEmptyResult()
            } else { //没有处于搜索状态，则显示0/0
                initData()
            }
            return
        }
        setPositionContent()

        if (currentPos in 0 until totalCount) {
            positionChangeListener?.onPositionChanged(currentPos, imgBtnPos)
        }
    }

    /**
     * 设置无搜索结果的界面
     */
    private fun setEmptyResult() {
        resultTv?.text = context.getString(com.soundrecorder.common.R.string.not_search_result)
        resultTv?.isEnabled = false
        previousImg?.isEnabled = false
        nextImg?.isEnabled = false
    }

    /**
     * 设置 position/totalCount的内容
     */
    private fun setPositionContent() {
        resultTv?.text = context.getString(
            com.soundrecorder.common.R.string.search_position_total_text,
            currentPos + 1,
            totalCount
        )
        resultTv?.isEnabled = true
        previousImg?.isEnabled = currentPos > 0
        nextImg?.isEnabled = currentPos < totalCount - 1
    }


    /**
     * 设置当前的位置
     * @param position 当前位置
     * @param size 总数
     */
    fun setCurrentPosition(position: Int, size: Int) {
        DebugUtil.i(TAG, "setCurrentPosition position:$position size: $size")
        currentPos = position
        totalCount = size

        jumpPos()
    }

    /**
     * 设置搜索内容
     */
    fun setSearchText(keyword: String?) {
        DebugUtil.i(TAG, "setSearchText ：$keyword")
        searchContent = keyword ?: ""
    }

    /**
     * 是否处于搜索状态,
     * 当搜索值为空时，则返回false
     */
    private fun isInSearch(): Boolean = !TextUtils.isEmpty(searchContent)

    /**
     * 搜索的当前位置发生变化
     */
    interface SearchPositionChangeListener {
        fun onPositionChanged(position: Int, imgBtnPos: Int)
    }
}