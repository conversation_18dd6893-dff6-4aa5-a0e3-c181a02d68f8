/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertInitView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.soundrecorder.common.widget.OSImageView

class ConvertInitView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConvertView(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "ConvertInitView"
    }

    private var clickListener: (() -> Unit)? = null
    private var container: ConstraintLayout? = null
    private var convertStart: TextView? = null
    private var convertTitle: TextView? = null
    private var convertingDesc: TextView? = null

    override fun initContainer() {
        LayoutInflater.from(context)
            .inflate(com.soundrecorder.playback.R.layout.fragment_convert_init, this, true)
        container = findViewById(com.soundrecorder.playback.R.id.container)
    }

    override fun initViews() {
        convertStart = findViewById(com.soundrecorder.playback.R.id.tv_start)
        convertStart?.setOnClickListener {
            clickListener?.invoke()
        }
        convertTitle = findViewById(com.soundrecorder.playback.R.id.init_msg_text)
        convertingDesc = findViewById(com.soundrecorder.playback.R.id.init_des_text)
    }

    override fun initLoadingView(): OSImageView? {
        return findViewById(com.soundrecorder.playback.R.id.init_logo)
    }

    override fun initTextLayout(): LinearLayout? {
        return findViewById(com.soundrecorder.playback.R.id.ll_convert)
    }

    override fun tag(): String {
        return TAG
    }

    fun setClickStartListener(callback: () -> Unit) {
        clickListener = callback
    }
}