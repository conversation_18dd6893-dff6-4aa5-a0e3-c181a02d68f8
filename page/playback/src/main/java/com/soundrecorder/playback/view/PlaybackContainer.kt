/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackContainer
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/22
 * * Author      : kewei<PERSON>@oppo.com
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.view

import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import androidx.constraintlayout.widget.ConstraintLayout
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.common.utils.isTouchInside
import com.soundrecorder.playback.PlaybackContainerFragment
import kotlin.math.abs
import kotlin.math.atan2
import kotlin.math.sqrt

class PlaybackContainer @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "PlaybackContainer"
        private const val MOVE_ANGLE = 45
    }

    private val touchSlop = ViewConfiguration.get(context).scaledTouchSlop

    private var isTouchOutside = false
    private var containerFragment: PlaybackContainerFragment? = null
    private var downX: Float = 0f
    private var downY: Float = 0f

    fun setContainerFragment(fragment: PlaybackContainerFragment?) {
        this.containerFragment = fragment
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        ev ?: return super.dispatchTouchEvent(ev)
        val floatingPanel = containerFragment?.contentBinding?.floatButtonPanel?.panel
            ?: return super.dispatchTouchEvent(ev)
        val expandCollapseAnimationHelper =
            containerFragment?.expandCollapseAnimationHelper ?: return super.dispatchTouchEvent(ev)
        if (expandCollapseAnimationHelper.isExpand()) {
            val action = ev.action
            when (action) {
                MotionEvent.ACTION_DOWN -> {
                    isTouchOutside = isPointOutsidePanel(floatingPanel, ev)
                    downX = ev.x
                    downY = ev.y
                    DebugUtil.d(TAG, "isTouchOutside = $isTouchOutside")
                }

                MotionEvent.ACTION_UP -> {
                    val touchOutside = isTouchOutside && isPointOutsidePanel(floatingPanel, ev)
                    val immersiveState =
                        containerFragment?.immersiveAnimationHelper?.isInAnimation() == true
                                || containerFragment?.mViewModel?.isImmersiveState?.value == true
                    if (touchOutside && immersiveState.not()) {
                        DebugUtil.i(TAG, "ACTION_UP to startCollapseAnimation")
                        expandCollapseAnimationHelper.startCollapseAnimation()
                        val cancel = MotionEvent.obtain(ev)
                        cancel.action = MotionEvent.ACTION_CANCEL
                        super.dispatchTouchEvent(cancel)
                        return true
                    }
                }

                MotionEvent.ACTION_MOVE -> {
                    val deltaX = ev.x - downX
                    val deltaY = ev.y - downY
                    val distance = sqrt(deltaX * deltaX + deltaY * deltaY)
                    if (distance > touchSlop) {
                        if (isTouchOutside) {
                            val angle = Math.toDegrees(atan2(abs(deltaY), abs(deltaX)).toDouble())
                            if (angle < MOVE_ANGLE) {
                                return true
                            }
                        }
                    }
                }
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    private fun isPointOutsidePanel(floatingPanel: View, ev: MotionEvent): Boolean {
        var isTouchInView = false
        if (isFloatWindow()) {
            DebugUtil.d(TAG, "isPointOutsidePanel float window type.")
            isTouchInView = floatingPanel.isTouchInside(ev)
        } else {
            val panelRect = Rect()
            floatingPanel.getGlobalVisibleRect(panelRect)
            val rawX = ev.rawX.toInt()
            val rawY = ev.rawY.toInt()
            isTouchInView = panelRect.contains(rawX, rawY)
        }
        return isTouchInView.not()
    }

    private fun isFloatWindow(): Boolean {
        val activity = getActivity()
        val isFloating = activity?.let {
            ScreenUtil.isFloatingWindow(it)
        } ?: false
        return isFloating
    }

    private fun getActivity(): Activity? {
        if (context is Activity) {
            return context as Activity
        }
        return null
    }
}