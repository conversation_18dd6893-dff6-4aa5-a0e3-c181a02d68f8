/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert

import android.annotation.SuppressLint
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.contains
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.DiffUtil
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.uiutil.ShadowUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.common.buryingpoint.ConvertStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.RecorderUserActionKt
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.common.utils.DensityHelper
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.utils.TipUtil
import com.soundrecorder.common.utils.TipUtil.Companion.TYPE_ROLE_NAME
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.convertservice.util.ConvertToUtils
import com.soundrecorder.convertservice.util.RoleNameUtil
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.ConvertImmersiveAnimationHelper
import com.soundrecorder.playback.FloatButtonPanelLayoutParams
import com.soundrecorder.playback.PlaybackContainerFragment
import com.soundrecorder.playback.PlaybackContainerViewModel
import com.soundrecorder.playback.convert.IConvertViewController
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.STATE_INIT_TO_CONVERT
import com.soundrecorder.playback.newconvert.convert.anim.ConvertAnimation
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipClickListener
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipGroup
import com.soundrecorder.playback.newconvert.search.ConvertSearchHelper
import com.soundrecorder.playback.newconvert.ui.ContentScrollHelper
import com.soundrecorder.playback.newconvert.ui.ConvertRenameBottomSheetDialog
import com.soundrecorder.playback.newconvert.ui.ConvertViewContainer
import com.soundrecorder.playback.newconvert.ui.ISelectSpeakerListener
import com.soundrecorder.playback.newconvert.ui.TextImageItemAdapter
import com.soundrecorder.playback.newconvert.ui.vh.ConvertHeaderViewHolder
import com.soundrecorder.playback.newconvert.util.ListUtil
import com.soundrecorder.playback.newconvert.view.CustomLinearLayoutManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Objects
import java.util.concurrent.CopyOnWriteArrayList
import java.util.function.Supplier

@Suppress("LargeClass")
class ConvertViewController(
    val containerFragment: PlaybackContainerFragment?,
    val container: ConvertViewContainer?,
    private val lifecycleOwner: LifecycleOwner?
) : TextImageItemAdapter.OnSpeakerNameClick, KeyWordChipClickListener, IConvertViewController,
    ISelectSpeakerListener {

    companion object {
        private const val TAG = "ConvertViewController"
        const val DELAY_TIME = 300L
        private const val RESPONSE_VIEW_SPACE_IMMERSIVE_ANIMATION: Float = 0.16f
        private const val MIN_HEIGHT = 140
        private const val APPLY_SIZE_DELAY = 50L
        private const val DURATION = 180L
        private const val ANIM_START_DELAY = 200L
        private const val ANIM_START_DELAY_1 = 50L
    }

    var onConvertCancelClickListener: OnConvertCancelClickListener? = null
    var onConvertStartClickListener: OnConvertStartClickListener? = null
    var mRenameSpeakerDialog: ConvertRenameBottomSheetDialog? = null
    var mContinueTranDialog: AlertDialog? = null

    // 获取高频词的接口
    var keyWordSupplier: Supplier<Boolean>? = null

    //转文本底部渐隐遮罩
    var viewSpace: View? = null

    var mConvertingView: ConvertingView? = null
    var mConvertContentView: View? = null
    var mConvertInitView: ConvertInitView? = null

    private val mActivity = containerFragment?.requireActivity() as? AppCompatActivity

    private var mMovePointView: View? = null
    private var mViewModel: PlaybackContainerViewModel? = null
    private var mConvertViewModel: PlaybackConvertViewModel? = null
    private var mInflater: LayoutInflater? = null

    //支持将录音转为文本
    private var mConvertContent: COUIRecyclerView? = null
    private var mConvertContentLayout: ConstraintLayout? = null
    private var mConvertBackButton: ImageView? = null
    private var backBtnLayout: RelativeLayout? = null

    //转写 时间 + 描述
    private var mConvertContentAdapter: TextImageItemAdapter? = null
    private var mLinearLayoutManager: CustomLinearLayoutManager? = null
    private var mContentScrollHelper: ContentScrollHelper? = null

    private val pathInterpolator = PathInterpolatorHelper.couiEaseInterpolator
    private var searchHelper: ConvertSearchHelper? = null

    //沉浸态动画
    private var viewSpaceImmersiveAnimation: COUISpringAnimation? = null

    //调用沉浸态动画时布局还未初始化，在初始化完成后继续动画
    private var isImmersiveAnimationWait: Boolean = false

    //调用退出沉浸态动画时布局还未初始化，在初始化完成后继续动画
    private var isRecoverAnimationWait: Boolean = false
    private var smartNaming: Boolean? = null
    private var smartResultName: String? = null
    private var applySizeRunnable: Runnable? = null

    private var immersiveAnimationHelper: ConvertImmersiveAnimationHelper? = null
    private var params: FloatButtonPanelLayoutParams? = null

    //进入

    private val browseFileAction by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val backLayoutMarginBottom by lazy {
        BaseApplication.getAppContext().resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp24)
    }

    private val convertAnimation by lazy { ConvertAnimation(this) }

    private var mClickListener = object : ConvertRenameBottomSheetDialog.OnClickListener {

        override fun onPositiveClick(
            recordId: Long,
            checked: Boolean,
            roleName: String,
            isClickHistory: Boolean
        ) {
            DebugUtil.d(TAG, "showRenameDialog $recordId $checked")
            renameSpeaker(recordId, checked, roleName, isClickHistory)
            releaseRenameSpeakerDialog()
        }

        override fun onNegtiveClick() {
            releaseRenameSpeakerDialog()
        }

        override fun onClearClick(recordId: Long) {
            deleteSpeaker(recordId, true, "")
        }

        override fun onHistoryItemDeleteClick(recordId: Long, checked: Boolean, roleName: String) {
            deleteSpeaker(recordId, false, roleName)
        }
    }

    init {
        init()
    }

    private fun init() {
        mInflater = mActivity?.layoutInflater
        container?.observeHeightChange { width, height ->
            container.removeCallbacks(applySizeRunnable)
            applySizeRunnable = Runnable {
                notifyConvertingViewSizeChange(width, height)
                notifyConvertInitViewSizeChange(width, height)
            }
            container.postDelayed(applySizeRunnable, APPLY_SIZE_DELAY)
        }
    }

    override fun release() {
        dismissDialog()
        DebugUtil.i(TAG, "release")
        mContentScrollHelper?.removeOnScrollListener()
        mContentScrollHelper?.release()
        mContentScrollHelper = null
        onConvertCancelClickListener = null
        mMovePointView = null
        mInflater = null
        releaseConvertInitView()
        releaseConvertingView()
        releaseConvertContentView()
        keyWordSupplier = null
        onConvertStartClickListener = null
        mConvertViewModel?.viewPagerScrollEnable?.value = null
        container?.unObserveHeightChange()
        container?.removeCallbacks(applySizeRunnable)
        convertAnimation.release()
    }

    private fun releaseConvertInitView() {
        mConvertInitView?.release()
        mConvertInitView = null
    }

    private fun releaseConvertingView() {
        mConvertingView?.release()
        mConvertingView = null
    }

    private fun releaseConvertContentView() {
        mConvertContentAdapter?.release()
        mConvertContentAdapter = null
        mConvertContent = null
        mConvertContentView = null
        mConvertBackButton = null
        backBtnLayout = null
    }

    private fun dismissDialog() {
        mRenameSpeakerDialog?.dismiss()
        mRenameSpeakerDialog = null
        mContinueTranDialog?.dismiss()
        mContinueTranDialog = null
    }

    private fun initConvertInitView() {
        if (mConvertInitView != null) {
            return
        }
        val activity = mActivity ?: return
        mConvertInitView = ConvertInitView(activity)
        mConvertInitView?.layoutParams =  FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )
        mConvertInitView?.setClickStartListener {
            DebugUtil.d(TAG, "click start convert $onConvertStartClickListener")
            onConvertStartClickListener?.onClick()
            mConvertViewModel?.lastConvertState = STATE_INIT_TO_CONVERT
            val duration = mViewModel?.playerController?.getDuration()
            ConvertStaticsUtil.addConvertFileDuration(duration)
        }
    }

    private fun initConvertContentView() {
        if (mConvertContentView != null) {
            return
        }
        mConvertContentView = mInflater?.inflate(com.soundrecorder.playback.R.layout.fragment_convert_content, null)
        mConvertContent = mConvertContentView?.findViewById(com.soundrecorder.playback.R.id.convert_content)
        //手动设置constraintSet，按照不同屏幕宽度设置widthPercent达到相关显示宽度动态适配屏幕宽度的效果
        mConvertContentLayout = mConvertContentView?.findViewById(com.soundrecorder.playback.R.id.converrt_content_layout)
        val constrainSet = ConstraintSet()
        constrainSet.connect(
            com.soundrecorder.playback.R.id.convert_content,
            ConstraintSet.TOP,
            com.soundrecorder.playback.R.id.converrt_content_layout,
            ConstraintSet.TOP
        )
        constrainSet.connect(
            com.soundrecorder.playback.R.id.convert_content,
            ConstraintSet.BOTTOM,
            com.soundrecorder.playback.R.id.converrt_content_layout,
            ConstraintSet.BOTTOM
        )
        constrainSet.connect(
            com.soundrecorder.playback.R.id.convert_content,
            ConstraintSet.START,
            com.soundrecorder.playback.R.id.converrt_content_layout,
            ConstraintSet.START
        )
        constrainSet.connect(
            com.soundrecorder.playback.R.id.convert_content,
            ConstraintSet.END,
            com.soundrecorder.playback.R.id.converrt_content_layout,
            ConstraintSet.END
        )
        val ratioTypedValue = TypedValue()
        mActivity?.resources?.getValue(com.soundrecorder.playback.R.dimen.screen_width_percent_parentchild, ratioTypedValue, true)
        val ratio = ratioTypedValue.float
        constrainSet.constrainPercentWidth(com.soundrecorder.playback.R.id.convert_content, ratio)
        constrainSet.applyTo(mConvertContentLayout)
        mConvertBackButton = mConvertContentView?.findViewById(com.soundrecorder.playback.R.id.back_btn)
        backBtnLayout = mConvertContentView?.findViewById(com.soundrecorder.playback.R.id.back_btn_layout)
        backBtnLayout?.updateLayoutParams<CoordinatorLayout.LayoutParams> {
            this.bottomMargin =  params?.let { backLayoutMarginBottom + it.height + it.marginBottom } ?: 0
        }
        viewSpace = mConvertContentView?.findViewById(com.soundrecorder.playback.R.id.view_space)

        mLinearLayoutManager = CustomLinearLayoutManager(BaseApplication.getAppContext())
        //这里设置recycleChildrenOnDetach的值为true是为了，让界面退出时，recyclerView将所有的viewholder都recycle掉，优化部分内存占用
        mLinearLayoutManager?.recycleChildrenOnDetach = true
        mConvertContent?.layoutManager = mLinearLayoutManager
        mConvertContentAdapter = TextImageItemAdapter(mActivity)

        mConvertContentAdapter?.mOnSpeakerNameClick = this
        mConvertContentAdapter?.keyWordClickListener = this
        mConvertContentAdapter?.speakerSelectListener = this
        mConvertContent?.adapter = mConvertContentAdapter
        DebugUtil.i(TAG, "===>initConvertContentView: ratio=$ratio" +
                ",container = $container" +
                ",mViewModel = $mViewModel" +
                ",mConvertViewModel = $mConvertViewModel" +
                ",mConvertContent = $mConvertContent"
        )
        mContentScrollHelper = ContentScrollHelper(container, mViewModel, mConvertViewModel, mConvertContent)
        mContentScrollHelper?.addOnScrollListener()
        mContentScrollHelper?.setConvertImmersiveAnimationHelper(immersiveAnimationHelper)
        mConvertContentAdapter?.mContentScrollHelper = mContentScrollHelper
        mConvertContentAdapter?.updatePaddingBottom(params?.let { it.height + it.marginVertical - it.marginBottom } ?: 0)
        if (isImmersiveAnimationWait) {
            isImmersiveAnimationWait = false
            startImmersiveAnimation()
        } else if (isRecoverAnimationWait) {
            isRecoverAnimationWait = false
            reverseImmersiveAnimation()
        }
    }

    fun setViewModel(viewModel: PlaybackContainerViewModel?) {
        DebugUtil.i(TAG, "===>setViewModel: $viewModel")
        mViewModel = viewModel
        mContentScrollHelper?.setViewModel(viewModel)
    }

    override fun getViewModel(): PlaybackContainerViewModel? {
        return mViewModel
    }

    override fun getConvertViewModel(): PlaybackConvertViewModel? {
        return mConvertViewModel
    }

    fun setConvertViewModel(viewModel: PlaybackConvertViewModel?) {
        DebugUtil.i(TAG, "===>setConvertViewModel: $viewModel")
        mConvertViewModel = viewModel
        mContentScrollHelper?.setConvertViewModel(viewModel)
    }

    private fun initConvertingView() {
        if (mConvertingView != null) {
            return
        }
        val activity = mActivity ?: return
        mConvertingView = ConvertingView(activity)
        mConvertingView?.layoutParams =  FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.MATCH_PARENT
        )
        mConvertingView?.setClickCancelListener {
            showContinueTranscribingDialog()
            ConvertStaticsUtil.addConvertCancelEvent()
        }
        mConvertingView?.layoutByTop = ((container?.childCount) ?: 0) > 0
    }

    private fun showContinueTranscribingDialog() {
        val activity = this.mActivity ?: run {
            DebugUtil.e(TAG, "showContinueTranscribingDialog mActivity is null")
            return
        }
        if (mContinueTranDialog?.isShowing == true) {
            return
        }
        val builder = COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_Bottom)
        builder.setBlurBackgroundDrawable(true)
        builder.setTitle(com.soundrecorder.common.R.string.cancel_transcribing)
        builder.setMessage(com.soundrecorder.common.R.string.record_cancel_convert_dialog_content)
        builder.setNegativeButton(com.soundrecorder.common.R.string.continue_transcribing) { dialog, which ->
            ConvertStaticsUtil.addConvertContinueDialog()
        }
        builder.setPositiveButton(com.soundrecorder.common.R.string.cancel_transcribing) { dialog, which ->
            ConvertStaticsUtil.addConvertCancelDialog()
            onConvertCancelClickListener?.onClick()
        }
        mContinueTranDialog = builder.create()
        mContinueTranDialog?.setCancelable(true)
        mContinueTranDialog?.show()
        ViewUtils.updateWindowLayoutParams(mContinueTranDialog?.window)
    }

    /**
     * 加载标记数据
     * 1.划线标记所在短句，以句号为分割。多个标记在同一段句，划一次线添加一个小旗子
     * 2.标记分属句逻辑-标记处于两短句之间，该标记归属上一句，该标记处于两段之间，该标记属于上一文段
     * 3.句中只含有图片标记-该句无下划线，无小旗子图标，图文混排插入图片，其他有标记情况都要展示下划线和小旗子
     */
    //todo 调整图片标记的相关分段逻辑
    private fun loadSubSentenceMarkBean(data: List<ConvertContentItem>?) {
        if (data == null || data.isEmpty()) {
            DebugUtil.i(TAG, "loadSubSentenceMarkBean convert data is null or empty return")
            return
        }

        val markList = mViewModel?.markHelper?.getMarkDatas()?.value ?: mutableListOf()
        val copyOnWriteArrayList = CopyOnWriteArrayList<MarkDataBean>()
        copyOnWriteArrayList.addAll(markList)
        for (convertIndex in data.indices) {
            val contentItem = data[convertIndex]
            //子句列表
            val listSubSentences = contentItem.listSubSentence
            //time 是子句开始时间
            if (!listSubSentences.isNullOrEmpty()) {
                //循环子句列表
                for (sentencesIndex in listSubSentences.indices) {
                    val subSentence = listSubSentences[sentencesIndex]
                    subSentence.onlyHasSimpleMark = false
                    val startTime = if (sentencesIndex == 0) {
                        //如果是当前item的第一个分句，时间从item的开始时间来计算
                        contentItem.startTime.toFloat()
                    } else {
                        //非第一个分句，开始时间从当前item来计算
                        subSentence.time
                    }

                    val endTime = if (sentencesIndex < listSubSentences.size - 1) {
                        listSubSentences[sentencesIndex + 1].time
                    } else {
                        if (convertIndex < data.size - 1) {
                            val nextConvert = data[convertIndex + 1]
                            nextConvert.startTime.toFloat()
                        } else {
                            //如果是最后一段  以外的标记信息都记入最后一段
                            //录音尾部静音片段的标记会记录到最后一句文本上，即使最后一句文本原本没有标记
                            mViewModel?.playerController?.getDuration()?.toFloat()
                                ?: contentItem.endTime.toFloat()
                        }
                    }
                    // 判断第一个item的第一个分句时，需要将之前的图片标记分配在当前句的列表中
                    val needAddPictureMarkBeforeFirstSentce = (convertIndex == 0) && (sentencesIndex == 0)
                    val isLastSentenceInAudioFile =
                        (convertIndex == (data.size - 1)) && (sentencesIndex == (listSubSentences.size - 1))
                    getMarkListOfTime(
                        subSentence,
                        needAddPictureMarkBeforeFirstSentce,
                        startTime,
                        endTime,
                        isLastSentenceInAudioFile,
                        copyOnWriteArrayList
                    )
                    //文段中是否有文本标记信息
                }
            }
            //第一个convertItem不需要再段落内部将第一句分句之前的ImageMark人为调整到第一句之后，非第一个ConvertItem默认执行分句调整逻辑
            val needJustFirstImage = (convertIndex != 0)
            contentItem.parceNewTextOrImageItems(needJustFirstImage)
        }
    }

    /**
     * 查询结果
     */
    private fun queryKeyWord(data: List<ConvertContentItem>?) {
        if (data.isNullOrEmpty()) {
            return
        }
        val searchWord = mViewModel?.browseSearchWord ?: return
        if (searchHelper == null) {
            searchHelper = ConvertSearchHelper(data)
        } else {
            searchHelper?.convertTextItems = data
        }
        searchHelper?.queryByKeyWord(searchWord)
    }


    private fun getMarkListOfTime(
        subSentence: ConvertContentItem.SubSentence,
        needAddPictureMarkBeforeFirstSentce: Boolean = false,
        startTime: Float,
        endTime: Float,
        lastSentenceInTheFile: Boolean = false,
        copyMarkList: CopyOnWriteArrayList<MarkDataBean>
    ) {
        val newMarkList: MutableList<MarkDataBean> = mutableListOf()
        val iterator: Iterator<MarkDataBean> = copyMarkList.iterator()
        while (iterator.hasNext()) {
            val markDataBean = iterator.next()
            val endTimeSmallerThanEnd = if (lastSentenceInTheFile) {
                //最后一段最后一句需要endTime需要包含，
                markDataBean.correctTime <= endTime
            } else {
                //非最后一段最后一句，endTime不包含
                markDataBean.correctTime < endTime
            }
            //起点时间>=,包含当前起点时间
            val startTimeBiggerThanStart = markDataBean.correctTime >= startTime
            if (endTimeSmallerThanEnd && startTimeBiggerThanStart) {
                //文本标记
                if (!markDataBean.fileExists() && !subSentence.onlyHasSimpleMark) {
                    subSentence.onlyHasSimpleMark = true
                }
                newMarkList.add(markDataBean)
                //减少循环
                copyMarkList.remove(markDataBean)
            } else {
                //是否需要将当前Sentence之前的图片标记放在markList列表中
                if (needAddPictureMarkBeforeFirstSentce &&
                    (markDataBean.correctTime < startTime && markDataBean.correctTime >= 0) &&
                    markDataBean.fileExists()
                ) {
                    newMarkList.add(markDataBean)
                    //减少循环
                    copyMarkList.remove(markDataBean)
                }
            }
        }
        subSentence.markDataBeanList = newMarkList
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun loadContent(data: List<ConvertContentItem>?, timeString: String) {
        DebugUtil.i(
            TAG, "updateContent ${data?.size} activity is $mActivity, mViewModel $mViewModel" +
                    ", mIsInConvertSearch.Boolean ${mConvertViewModel?.mIsInConvertSearch?.value}, mContentScrollHelper $mContentScrollHelper"
        )
        if (mActivity == null) {
            return
        }
        loadSubSentenceMarkBean(data)
        val convertViewModel = mConvertViewModel ?: return
        convertViewModel.timeItems = ListUtil.getTimeMetaDataList(data)
        convertViewModel.mTextItems = ListUtil.getTextMetaDataList(data)
        mConvertContentAdapter?.searchHelper = searchHelper
        /*1. 加载所有讲话人数据*/
        mConvertContentAdapter?.loadAllSpeakers(data)
        /*2.根据筛选的讲话人去设置对应UI列表数据*/
        val contentDataList = if (!convertViewModel.selectSpeakerList.isNullOrEmpty()) {
            mConvertContentAdapter?.selectSpeakersData = convertViewModel.selectSpeakerList!!.toMutableList()
            data?.filter { convertViewModel.selectSpeakerList?.contains(it.roleName) == true }
        } else {
            data
        }
        // 解析搜索词
        queryKeyWord(contentDataList)
        mConvertContentAdapter?.setContentData(contentDataList, ListUtil.getMetaDataList(contentDataList))
        mConvertContentAdapter?.player = mViewModel?.playerController
        mConvertContentAdapter?.setHeaderData(mViewModel?.playName?.value ?: "", timeString, "", "", mViewModel?.duration)
        smartNaming = mViewModel?.recordId?.let { browseFileAction?.isSmartNaming(it) }
        mConvertContentAdapter?.refreshSmartNameStatusChange(smartNaming, smartResultName, false)
        //需要调用notify
        mConvertContentAdapter?.notifyDataSetChanged()
        if (convertViewModel.mIsInConvertSearch.value != true) {
            //非搜索情况下走restore逻辑
            mContentScrollHelper?.restoreForRebuild()
        }
        roleControl(convertViewModel.isSpeakerRoleShowing.value ?: false, true)
    }

    private fun dismissContinueTranDialog() {
        if (mContinueTranDialog?.isShowing == true) {
            mContinueTranDialog?.dismiss()
            mContinueTranDialog = null
        }
    }

    fun switchConvertInitView(needAnimation: Boolean = false) {
        mConvertInitView?.let {
            if (container?.contains(it) == true) {
                DebugUtil.i(TAG, "switchConvertInitView already has convert init")
                return
            }
        }
        val childCount = container?.childCount ?: 0
        DebugUtil.i(TAG, "switchConvertInitView = $mConvertInitView, needAnimation = $needAnimation, childCount = $childCount")
        val needAnim = needAnimation || (childCount > 0)
        doStartSwitchConvertInitView()

        fun doFinal() {
            containerFragment?.showConvertInit()
            container?.removeAllViewsExcept(mConvertInitView)
            convertAnimation.release()
            mConvertInitView?.startAnim()
            releaseConvertingView()
        }
        if (needAnim) {
            convertAnimation.doPreSwitchAnim(mConvertInitView, false)
            container?.postDelayed({
                if (containerFragment?.isAdded == true) {
                    convertAnimation.doSwitchConvertInitAnimation {
                        doFinal()
                        convertAnimation.doEndSwitchAnim()
                    }
                }
            }, ANIM_START_DELAY_1)
        } else {
            doFinal()
        }
    }

    @VisibleForTesting
    fun doStartSwitchConvertInitView() {
        cancelConvertAnim()
        mConvertInitView = null
        container?.let { group ->
            initConvertInitView()
            mConvertInitView?.let {
                if (group.contains(it).not()) {
                    group.addView(it)
                    it.bringToFront()
                    it.initRefresh(group.width, group.height)
                }
            }
        }
        dismissContinueTranDialog()
    }

    private fun notifyConvertInitViewSizeChange(width: Int, height: Int) {
        if (container?.indexOfChild(mConvertInitView) != -1) {
            val paddingBottom = params?.let { it.height + it.marginBottom } ?: 0
            mConvertInitView?.updatePaddingBottom(paddingBottom)
            mConvertInitView?.notifyViewSizeChange(width, height)
        }
    }

    fun switchConvertingView() {
        mConvertingView?.let {
            if (container?.contains(it) == true) {
                DebugUtil.i(TAG, "switchConvertingView already has converting")
                return
            }
        }
        val needAnim = (container?.childCount ?: 0) > 0
        DebugUtil.i(TAG, "switchConvertingView = $mConvertingView, needAnim = $needAnim")
        doSwitchConvertingView()

        fun doFinal() {
            containerFragment?.showConverting()
            container?.removeAllViewsExcept(mConvertingView)
            convertAnimation.release()
            mConvertingView?.startAnim()
            releaseConvertInitView()
        }

        if (needAnim) {
            convertAnimation.doPreSwitchAnim(mConvertingView, false)
            container?.postDelayed({
                if (containerFragment?.isAdded == true) {
                    convertAnimation.doSwitchConvertingAnimation {
                        doFinal()
                        convertAnimation.doEndSwitchAnim()
                    }
                }
            }, ANIM_START_DELAY_1)
        } else {
            doFinal()
        }
    }

    @VisibleForTesting
    fun doSwitchConvertingView() {
        cancelConvertInitAnimation()
        mConvertingView = null
        container?.let { group ->
            initConvertingView()
            mConvertingView?.let { view ->
                if (group.contains(view).not()) {
                    group.addView(view)
                    view.bringToFront()
                    view.initRefresh(group.width, group.height)
                }
            }
        }
    }

    private fun notifyConvertingViewSizeChange(width: Int, height: Int) {
        if (container?.indexOfChild(mConvertingView) != -1) {
            val paddingBottom = params?.let { it.height + it.marginBottom } ?: 0
            mConvertingView?.updatePaddingBottom(paddingBottom)
            mConvertingView?.notifyViewSizeChange(width, height)
        }
    }

    fun switchConvertContentView() {
        mViewModel?.hasConvertContent?.postValueSafe(true)
        val needAnim = (container?.childCount ?: 0) > 0
        DebugUtil.i(TAG, "switchConvertContentView = $mConvertContentView, needAnim = $needAnim")
        doSwitchConvertContentView()

        fun doFinal() {
            containerFragment?.showConvertContent()
            container?.removeAllViewsExcept(mConvertContentView)
            convertAnimation.release()
            releaseConvertInitView()
            releaseConvertingView()
        }

        if (needAnim) {
            convertAnimation.doPreSwitchAnim(mConvertContentView)
            container?.postDelayed({
                if (containerFragment?.isAdded == true) {
                    convertAnimation.doSwitchConvertContentAnimation {
                        doFinal()
                        convertAnimation.doEndSwitchAnim()
                    }
                }
            }, ANIM_START_DELAY)
        } else {
            doFinal()
        }
    }

    /**
     * 当白天鹅或者孔雀开合折叠系统多次执行生命周期时
     * Alpha动画执行有延时   mConvertContentView可能会为null
     */
    @VisibleForTesting
    fun doSwitchConvertContentView() {
        cancelConvertAnim()
        val timeString = getTimeString()
        container?.let {
            initConvertContentView()
            mConvertContentView?.let { contentView ->
                if (container.contains(contentView).not()) {
                    it.addView(contentView)
                    contentView.bringToFront()
                }
            }
        }
        dismissContinueTranDialog()
        initConvertContentViewBackButton()
        loadContent(mConvertViewModel?.convertContentData, timeString)
        update(mViewModel?.playerController?.currentTimeMillis?.value ?: 0L)
    }

    override fun startConvertInitAnimation() {
        mConvertInitView?.startAnim()
    }

    @VisibleForTesting
    fun cancelConvertInitAnimation() {
        mConvertInitView?.cancelAnim()
    }

    override fun cancelConvertAnim() {
        mConvertingView?.cancelAnim()
    }

    fun updateProgress(progress: Int) {
        mConvertingView?.updateProgress(progress)
    }

    fun updateProgress(progress: Int, serverPlanCode: Int) {
        mConvertingView?.updateProgress(progress, serverPlanCode)
    }

    private fun showBackButtonAnimate() {
        backBtnLayout?.apply {
            mConvertContent?.post {
                val h = DensityHelper.px2dip(context, mConvertContent?.height ?: 0)
                if (h < MIN_HEIGHT) {
                    DebugUtil.i(TAG, "showBackButtonAnimate h is too small, do not show button")
                    return@post
                }
                isVisible = true
                isClickable = true
                updateBackButtonDrawable(mConvertViewModel?.mContentBackDirection?.value == true)
                animate().setDuration(DURATION).alpha(1f)
                    .setInterpolator(pathInterpolator).start()
            }
        }
    }

    private fun hideBackButtonAnimate() {
        backBtnLayout?.apply {
            isVisible = true
            isClickable = false
            animate().setDuration(DURATION).alpha(0f)
                .setInterpolator(pathInterpolator)
                .withEndAction {
                    isGone = true
                }.start()
        }
    }

    private fun initConvertContentViewBackButton() {
        backBtnLayout?.setOnClickListener {
            scrollToLastPositionByManual()
        }
        backBtnLayout?.apply {
            ShadowUtils.setElevationToView(
                this,
                ShadowUtils.SHADOW_LV3,
                ViewUtils.dp2px(NumberConstant.NUM_F26_0).toInt(),
                ViewUtils.dp2px(NumberConstant.NUM_F2_0).toInt(),
                resources.getColor(com.support.snackbar.R.color.coui_snack_bar_background_shadow_color, this.context.theme)
            )
        }

        lifecycleOwner?.let {
            mConvertViewModel?.mHasUserDraggedText?.observe(it, androidx.lifecycle.Observer { it1 ->
                if (it1) {
                    showBackButtonAnimate()
                } else {
                    if (backBtnLayout?.visibility == View.VISIBLE) {
                        hideBackButtonAnimate()
                    }
                }
            })

            mViewModel?.playerController?.mIsTouchSeekbar?.observe(it) { isTouching ->
                if (isTouching) {
                    mConvertViewModel?.mHasUserDraggedText?.value = false
                }
            }

            mConvertViewModel?.mContentBackDirection?.observe(
                it,
                androidx.lifecycle.Observer { it1 ->
                    updateBackButtonDrawable(it1)
                })

            mViewModel?.lastMarkAction?.observe(it) { action ->
                DebugUtil.i(TAG, "lastMarkAction changed $action")
                //图片标记删除或增加时，更新viewModel中的图文混排列表和
                val contentList = mConvertContentAdapter?.mContentItemList
                // 保存更新前的数据副本用于差分对比
                val oldContentData = createContentDataCopy(contentList)
                when (action) {
                    //目前只处理增删
                    PlaybackContainerViewModel.MARK_ACTION_DELETE,
                    PlaybackContainerViewModel.MARK_ACTION_ADD -> {
                        loadSubSentenceMarkBean(contentList)
                        mConvertViewModel?.mTextItems = ListUtil.getTextMetaDataList(mConvertViewModel?.convertContentData)
                        mConvertViewModel?.timeItems = ListUtil.getTimeMetaDataList(mConvertViewModel?.convertContentData)
                        // 使用更新后的数据作为新数据
                        diffRefreshContentData(oldContentData, contentList as ArrayList<ConvertContentItem>)
                    }

                    PlaybackContainerViewModel.MARK_ACTION_RENAME -> {
                        val adapter = mConvertContentAdapter ?: return@observe
                        loadSubSentenceMarkBean(contentList)
                        mConvertViewModel?.mTextItems = ListUtil.getTextMetaDataList(mConvertViewModel?.convertContentData)
                        mConvertViewModel?.timeItems = ListUtil.getTimeMetaDataList(mConvertViewModel?.convertContentData)
                        val newContentData = contentList as ArrayList<ConvertContentItem>
                        adapter.setContentData(newContentData, ListUtil.getMetaDataList(newContentData))
                        adapter.notifyDataSetChanged()
                    }
                }
            }
            mConvertViewModel?.searchAnimEnd?.observe(it) { value ->
                if (value) { //进入搜索动画结束
                    // 转文本界面的关键词需要隐藏
                    hideKeyWordView()
                }
            }
        }
    }

    /**
     * 创建内容数据的深度副本，用于差分对比
     */
    private fun createContentDataCopy(contentList: List<ConvertContentItem>?): ArrayList<ConvertContentItem>? {
        if (contentList == null) return null

        return ArrayList(contentList.map { item ->
            ConvertContentItem(
                startTime = item.startTime,
                endTime = item.endTime,
                textContent = item.textContent,
                isFocusItem = item.isFocusItem,
                roleId = item.roleId,
                roleName = item.roleName,
                roleAvatar = item.roleAvatar,
                textWithWords = item.textWithWords,
                textWithWordsTimeStamp = item.textWithWordsTimeStamp,
                listSubSentence = item.listSubSentence?.map { subSentence ->
                    ConvertContentItem.SubSentence(
                        startCharSeq = subSentence.startCharSeq,
                        endCharSeq = subSentence.endCharSeq,
                        time = subSentence.time,
                        text = subSentence.text,
                        isFocused = subSentence.isFocused,
                        onlyHasSimpleMark = subSentence.onlyHasSimpleMark,
                        markDataBeanList = subSentence.markDataBeanList?.toList()
                    )
                }?.toMutableList()
            ).apply {
                textType = item.textType
            }
        })
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun diffRefreshContentData(
        oldContentData: ArrayList<ConvertContentItem>?,
        newContentData: ArrayList<ConvertContentItem>?
    ) {
        val adapter = mConvertContentAdapter ?: return
        if (newContentData != null && oldContentData != null) {
            lifecycleOwner?.lifecycleScope?.launch {
                val diffResult = DiffUtil.calculateDiff(createContentDiffCallback(oldContentData, newContentData))
                withContext(Dispatchers.Main) {
                    adapter.setContentData(newContentData, ListUtil.getMetaDataList(newContentData))
                    diffResult.dispatchUpdatesTo(adapter)
                }
            }
        } else {
            adapter.setContentData(newContentData, ListUtil.getMetaDataList(newContentData))
            adapter.notifyDataSetChanged()
        }
    }

    /**
     * 创建精确的内容差分回调，关注markDataBeanList的变化
     */
    private fun createContentDiffCallback(
        oldContentData: ArrayList<ConvertContentItem>,
        newContentData: ArrayList<ConvertContentItem>
    ): DiffUtil.Callback {
        return object : DiffUtil.Callback() {
            override fun getOldListSize() = oldContentData.size
            override fun getNewListSize() = newContentData.size

            override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                val oldItem = oldContentData[oldItemPosition]
                val newItem = newContentData[newItemPosition]
                // 使用唯一标识符判断是否为同一项：开始时间+结束时间
                return oldItem.startTime == newItem.startTime && oldItem.endTime == newItem.endTime
            }

            override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
                val oldItem = oldContentData[oldItemPosition]
                val newItem = newContentData[newItemPosition]
                // 比较 SubSentence 列表的 markDataBeanList
                return subSentenceListsSame(oldItem.listSubSentence, newItem.listSubSentence)
            }
        }
    }

    /**
     * 比较SubSentence子句列表是否相同，重点检查markDataBeanList的变化
     */
    private fun subSentenceListsSame(
        oldList: MutableList<ConvertContentItem.SubSentence>?,
        newList: MutableList<ConvertContentItem.SubSentence>?
    ): Boolean {
        if (oldList == null && newList == null) return true
        if (oldList == null || newList == null) return false
        if (oldList.size != newList.size) return false
        for (i in oldList.indices) {
            // 比较子句的markDataBeanList是否相等
            if (oldList[i].markDataBeanList != newList[i].markDataBeanList) {
                return false
            }
        }
        return true
    }

    private fun updateBackButtonDrawable(isDown: Boolean) {
        if (backBtnLayout?.visibility != View.VISIBLE) {
            return
        }
        if (isDown) {
            mConvertBackButton?.setImageResource(com.soundrecorder.playback.R.drawable.ic_back_btn_down)
        } else {
            mConvertBackButton?.setImageResource(com.soundrecorder.playback.R.drawable.ic_back_btn_up)
        }
    }


    fun getTimeString(): String {
        return ListUtil.getTimeString(mViewModel?.recordId, mViewModel?.playName?.value)
    }

    fun updatePlayName() {
        mConvertContentAdapter?.setHeaderData(mViewModel?.playName?.value ?: "", getTimeString(), "", "", mViewModel?.duration)
        mConvertContentAdapter?.notifyDataSetChanged()
    }

    fun removeAllViews() {
        container?.removeAllViews()
    }

    interface OnConvertCancelClickListener {
        fun onClick()
    }

    interface OnConvertStartClickListener {
        fun onClick()
    }

    override fun onClick(pos: Int) {
        if (ClickUtils.isQuickClick()) {
            return
        }
        if (mViewModel?.recordId == null) {
            return
        }
        mViewModel?.dataPosition = pos
        val content =
            mViewModel?.dataPosition?.let { mConvertContentAdapter?.mContentItemList?.get(it)?.roleName }
                ?: ""
        mConvertViewModel?.originContent = content
        val originContent = mConvertViewModel?.originContent ?: ""
        showRenameSpeakerDialog(content, true, originContent)
        //add click speaker tips point
        ConvertStaticsUtil.addSpeakerClickSpeakerTipEvent()
    }

    private fun renameSpeaker(
        recordId: Long,
        checked: Boolean,
        roleName: String,
        isClickHistory: Boolean
    ) {
        if (mViewModel?.dataPosition == null) {
            return
        }
        DebugUtil.i(TAG, "renameSpeaker recordId $recordId, dataPosition ${mViewModel?.dataPosition}")
        if (checked) {
            renameSpeakerAll(recordId, mViewModel?.dataPosition!!, roleName)
        } else {
            renameSpeakerOne(recordId, mViewModel?.dataPosition!!, roleName)
        }
        //add rename speaker point
        if (isClickHistory) {
            ConvertStaticsUtil.addSpeakerRenameByHistoryEvent()
        }
    }

    private fun deleteSpeaker(recordId: Long, isClear: Boolean, roleName: String) {
        if (isClear) {
            deleteHistoryRoleNameAll(recordId)
        } else {
            deleteHistoryRoleNameOne(recordId, roleName)
        }
    }

    private fun renameSpeakerOne(recordId: Long, pos: Int, roleName: String) {
        val item = mConvertContentAdapter?.mContentItemList?.get(pos)
        val oldName = item?.roleName ?: ""
        mConvertViewModel?.convertContentData?.find {
            it.startTime == item?.startTime && it.endTime == item.endTime
        }?.run {
            this.roleName = roleName
            this.changeRoleNameForTimeDivider(roleName)
        }

        mConvertContentAdapter?.let {
            val isSelectAllBeforeRename = it.isRealSelectAllSpeaker()
            // 更新所有讲话人名称列表
            it.loadAllSpeakers(mConvertViewModel?.convertContentData)
            it.renameSpeakerSuccess(roleName, oldName, isSelectAllBeforeRename)
        }
        CoroutineUtils.doInIOThread(
            {
                reWriteConvertFile(recordId, mConvertViewModel?.convertContentData)
                RoleNameUtil.addHistoryRoleName(recordId, roleName)
            },
            mConvertViewModel?.viewModelScope!!
        )

        //add rename current speaker point
        ConvertStaticsUtil.addSpeakerRenameCurrentEvent()
    }

    private fun renameSpeakerAll(recordId: Long, pos: Int, roleName: String) {
        val item = mConvertContentAdapter?.mContentItemList?.get(pos)
        val oldName = item?.roleName
        mConvertViewModel?.convertContentData?.forEach { i ->
            if (i.roleName == oldName) {
                i.roleName = roleName
                i.changeRoleNameForTimeDivider(roleName)
            }
        }
        mConvertContentAdapter?.let {
            val isSelectAllBeforeRename = it.isRealSelectAllSpeaker()
            // 更新所有讲话人名称列表
            it.loadAllSpeakers(mConvertViewModel?.convertContentData)
            it.renameSpeakerSuccess(roleName, oldName ?: "", isSelectAllBeforeRename)
        }
        CoroutineUtils.doInIOThread(
            {
                reWriteConvertFile(recordId, mConvertViewModel?.convertContentData)
                RoleNameUtil.addHistoryRoleName(recordId, roleName)
            },
            mConvertViewModel?.viewModelScope!!
        )
        //add rename all speaker point
        ConvertStaticsUtil.addSpeakerRenameAllEvent()
    }

    private fun deleteHistoryRoleNameOne(recordId: Long, roleName: String) {
        CoroutineUtils.doInIOThread(
            {
                RoleNameUtil.deleteHistoryRoleName(recordId, roleName)
            },
            mViewModel?.viewModelScope!!
        )
    }

    private fun deleteHistoryRoleNameAll(recordId: Long) {
        CoroutineUtils.doInIOThread(
            {
                RoleNameUtil.clearHistoryRoleName(recordId)
            },
            mViewModel?.viewModelScope!!
        )
    }


    private fun reWriteConvertFile(recordId: Long, dataList: List<ConvertContentItem>?) {
        val convertRecord = ConvertDbUtil.selectByRecordId(recordId)
        val textFilePath = convertRecord?.convertTextfilePath
        DebugUtil.e(TAG, "reWriteConvertFile textFilePath: $textFilePath")
        if (dataList != null) {
            for (i in dataList) {
                DebugUtil.i(TAG, "==>reWriteConvertFile item: $i")
            }
        }
        if (convertRecord?.isOShareFile == true) {
            ConvertToUtils.reWriteOShareConvertFile(convertRecord.recordId, textFilePath, dataList)
        } else {
            ConvertToUtils.reWriteConvertFile(
                textFilePath,
                dataList
            )
        }
    }

    fun showRenameSpeakerDialog(content: String, isModifyAll: Boolean, originContent: String) {
        if (mViewModel == null) {
            return
        }
        val recordId = mViewModel!!.recordId
        DebugUtil.i(TAG, "showRenameDialog recordId:$recordId")
        val list = RoleNameUtil.getHistoryRoleName(recordId)
        for (i in list) {
            DebugUtil.e(TAG, "showRenameDialog i:$i")
        }
        if (mActivity == null) {
            return
        }
        mRenameSpeakerDialog?.dismiss()
        mRenameSpeakerDialog = ConvertRenameBottomSheetDialog(mActivity)
        mRenameSpeakerDialog?.content = content
        mRenameSpeakerDialog?.originContent = originContent
        mRenameSpeakerDialog?.recordId = recordId
        mRenameSpeakerDialog?.roleNameList = list
        mRenameSpeakerDialog?.mIsModifyAll = isModifyAll
        mRenameSpeakerDialog?.setClickListener(mClickListener)
        mRenameSpeakerDialog?.show()

        /*讲话人编辑次数 累加*/
        RecorderUserActionKt.sEditCount++
        /* 判断 是否应用到全部讲话人 */
        if (isModifyAll) {
            RecorderUserActionKt.sIsAppliedAll = RecorderUserAction.VALUE_APPLY_ALL_YES
        }
    }

    fun releaseRenameSpeakerDialog() {
        mRenameSpeakerDialog?.setClickListener(null)
        mRenameSpeakerDialog = null
    }

    /**
     * @param refresh true -> notifyDataChange()  false -> speaker animate
     */
    fun roleControl(needShowRole: Boolean, refresh: Boolean) {
        DebugUtil.i(TAG, "roleControl needShowRole $needShowRole, refresh: $refresh")
        mConvertContentAdapter?.roleControl(needShowRole, refresh)
        /*关闭讲话人，若已选择讲话人，则重置讲话人数据*/
        if (!needShowRole && mConvertContentAdapter?.selectSpeakersData?.isEmpty() == false) {
            mConvertContentAdapter?.selectSpeakersData?.clear()
            updateAdapterContent(mConvertViewModel?.convertContentData)
        }
    }

    fun addSpeakerTipTask(isOnConvertWhenViewPager2IDLE: () -> Boolean) {
        val activity = mActivity
        if (activity == null) {
            restorePageScroll()
            return
        }
        if (activity.isFinishing) {
            DebugUtil.w(TAG, "addSpeakerTipTask return by activity isFinishing")
            return
        }
        val lifecycle = activity.lifecycle
        TipUtil.checkShow(
            {
                if (isOnConvertWhenViewPager2IDLE.invoke()) {
                    val parent = mConvertContent?.layoutManager?.getChildAt(1)
                    parent?.findViewById<LinearLayout>(com.soundrecorder.playback.R.id.ll_speaker)
                } else {
                    null
                }
            },
            TYPE_ROLE_NAME,
            DELAY_TIME,
            lifecycle,
            activity.isInMultiWindowMode,
            onFinish = {
                restorePageScroll()
            }
        )
    }

    private fun restorePageScroll() {
        DebugUtil.i(TAG, "restorePageScroll")
        getCustomLinearLayoutManager()?.mCanScrollVertically = true
        scrollToLastPositionByManual()
        mViewModel?.mIsPageStopScroll?.postValue(false)
        mConvertViewModel?.viewPagerScrollEnable?.value = true
    }

    fun removeSpeakerTipTask() {
        DebugUtil.i(TAG, "removeSpeakerTipTask")
        TipUtil.dismissSelf(TYPE_ROLE_NAME)
    }

    fun postShowSwitch(boolean: Boolean) {
        mViewModel?.mShowSwitch?.postValue(boolean)
    }

    fun postRoleNumber(roleNumber: Int) {
        mViewModel?.mSpeakerNumber?.postValue(roleNumber)
    }

    fun getConvertContentData(): List<ConvertContentItem>? {
        return mConvertViewModel?.convertContentData
    }

    override fun getCustomLinearLayoutManager(): CustomLinearLayoutManager? {
        return this.mLinearLayoutManager
    }

    override fun scrollToLastPositionByManual() {
        mContentScrollHelper?.scrollToLastPositionByManual()
    }

    fun update(currentTimeMillis: Long) {
        mContentScrollHelper?.update(currentTimeMillis)
    }

    override fun setNeedShowRoleName(needShowRole: Boolean) {}

    /**
     * 设置关键词
     * @param keyWords 关键词列表，有可能为空，为空时UI自动转成“提取关键词”
     */
    fun setKeyWords(keyWords: List<String>) {
        val state = mConvertViewModel?.extractState ?: KeyWordChipGroup.DEFAULT_STATE
        mConvertContentAdapter?.setKeyWords(keyWords, state)
    }

    /**
     * 隐藏关键词的View
     */
    private fun hideKeyWordView() {
        DebugUtil.d(TAG, "隐藏关键词View")
        val viewHolder = findConvertHeader() ?: return
        viewHolder.hideKeyWordView()
    }

    /**
     * 显示关键词的View
     */
    private fun showKeyWordView() {
        DebugUtil.d(TAG, "显示关键词View")
        val viewHolder = findConvertHeader() ?: return
        viewHolder.showKeyWordView()
    }

    /**
     * 查找转文本的Header
     */
    private fun findConvertHeader(): ConvertHeaderViewHolder? {
        val viewHolder = mConvertContent?.findViewHolderForAdapterPosition(0)
        if (Objects.isNull(viewHolder)) {
            DebugUtil.e(TAG, "findConvertHeader position 0 holder is null")
            return null
        }
        if (viewHolder is ConvertHeaderViewHolder) {
            return viewHolder
        }
        return null
    }

    /**
     * 退出搜索动效
     */
    override fun animSearchOut() {
        val inScreen = mConvertViewModel?.isHeaderInScreen()
        DebugUtil.d(TAG, "关键词View 是否在当前屏幕：$inScreen")
        if (inScreen == true) {
            DebugUtil.d(TAG, "退出搜索动效")
            mConvertContent?.post { // 防止从其他位置滚动到0，会出现闪一下的效果
                hideKeyWordView() // 防止之前的View没有Gone，导致View显示->View从0到高显示出来，会出现闪一下的效果
                mConvertContentAdapter?.triggerSearchAnim()
            }
        } else {
            showKeyWordView()
        }
    }

    /**
     * 获取搜索view的高度
     */
    override fun saveKeyWordViewHeight() {
        val header = findConvertHeader()
        header?.saveKeyWordViewHeight()
    }

    /**
     * 提取关键词
     */
    override fun extractKeyWord(): Boolean {
        // 调用网络接口提取关键词
        val success = keyWordSupplier?.get() ?: false
        // 记录加载按钮的状态
        mConvertViewModel?.extractState = if (success) {
            KeyWordChipGroup.LOADING_STATE
        } else {
            KeyWordChipGroup.DEFAULT_STATE
        }
        return success
    }

    /**
     * 点击跳转到转文本搜索界面
     */
    override fun onClickKeyWord(chip: COUIChip, keyWord: String) {
        if (ClickUtils.isQuickClick()) {
            return
        }
        // 记录滚动位置
        saveScrollPosition("keyword_chip")
        mConvertViewModel?.let {
            it.mConvertSearchValue = keyWord // 搜索内容
            it.autoSearch = true //开启自动搜索
            it.clickChipCount++ //点击关键词标签次数
            it.mIsInConvertSearch.value = true // 跳转到文本搜索页面
        }
    }

    /**
     * 记录滚动的位置
     * @param from
     */
    override fun saveScrollPosition(from: String) {
        val layoutManager = getCustomLinearLayoutManager()
        layoutManager?.let {
            mConvertViewModel?.saveScrollPosition(from, it)
        }
    }

    /**
     * 退出搜索检测播放位置与当前位置
     * 是否要显示返回气泡
     */
    override fun checkOutSearchShowBackOfLocation() {
        mContentScrollHelper?.updateContentBackButton(true)
    }

    override fun dismissRenameSpeakerDialog() {
        mRenameSpeakerDialog?.dismissPop()
    }

    override fun onSpeakerSelect(speakers: List<String>, isSelectAll: Boolean) {
        DebugUtil.d(TAG, "onSpeakerSelect= ${speakers.size},selectAll=$isSelectAll")
        mConvertViewModel?.selectSpeakerList = speakers

        val originAllContent = mConvertViewModel?.convertContentData
        if (speakers.isEmpty() || isSelectAll) {
            queryKeyWord(originAllContent)
            updateAdapterContent(originAllContent)
            updateFilterData(originAllContent)
            updatePlayTimeSegmentList(null)
            return
        }
        originAllContent?.filter { speakers.contains(it.roleName) }?.run {
            queryKeyWord(this)
            updateAdapterContent(this)
            updateFilterData(this)
            updatePlayTimeSegmentList(this)
        }
    }

    /**
     * 更新播放时间片
     */
    private fun updatePlayTimeSegmentList(contentItemList: List<ConvertContentItem>?) {
        DebugUtil.d(TAG, "updatePlayTimeSegmentList ${contentItemList?.size}")
        if (contentItemList.isNullOrEmpty()) {
            mViewModel?.targetPlaySegment?.value = null
            return
        }
        val timeSegmentList = arrayListOf<Pair<Long, Long>>()
        contentItemList.forEach {
            val pair = Pair(it.startTime, it.endTime)
            timeSegmentList.add(pair)
        }
        mViewModel?.targetPlaySegment?.value = timeSegmentList
    }

    /**
     * 更新UI数据
     */
    private fun updateAdapterContent(contentItemList: List<ConvertContentItem>?) {
        mConvertContentAdapter?.let {
            it.setContentData(contentItemList, ListUtil.getMetaDataList(contentItemList))
            it.notifyDataSetChanged()
        }
    }

    private fun initImmersiveAnimation() {
        viewSpaceImmersiveAnimation = COUISpringAnimation(viewSpace, COUIDynamicAnimation.ALPHA, 1f)
        val spring = COUISpringForce()
            .setFinalPosition(0f)
            .setBounce(0f)
            .setResponse(RESPONSE_VIEW_SPACE_IMMERSIVE_ANIMATION)
        viewSpaceImmersiveAnimation?.spring = spring
    }

    override fun startImmersiveAnimation() {
        //布局还未初始化，等初始化完成后再执行动画
        if (viewSpace == null) {
            isImmersiveAnimationWait = true
            DebugUtil.i(TAG, "startImmersiveAnimation  viewSpace is null")
            return
        }
        if (viewSpaceImmersiveAnimation == null) {
            initImmersiveAnimation()
        } else {
            viewSpaceImmersiveAnimation?.apply {
                spring.finalPosition = 0f
            }
        }
        viewSpaceImmersiveAnimation?.start()
    }

    override fun reverseImmersiveAnimation() {
        //viewSpaceImmersiveAnimation为空表示界面不在沉浸态
        if (viewSpaceImmersiveAnimation == null) {
            DebugUtil.i(TAG, "reverseImmersiveAnimation  viewSpaceImmersiveAnimation is null")
            return
        }
        //布局还未初始化，等初始化完成后再执行动画
        if (viewSpace == null) {
            isRecoverAnimationWait = true
            DebugUtil.i(TAG, "reverseImmersiveAnimation  viewSpace is null")
            return
        }
        viewSpaceImmersiveAnimation?.animateToFinalPosition(1f)
    }

    fun updateSmartNameStatus(display: Boolean?, resultName: String?) {
        smartNaming = display ?: false
        smartResultName = resultName
        mConvertContentAdapter?.refreshSmartNameStatusChange(display, resultName)
    }

    fun setConvertImmersiveAnimationHelper(animationHelper: ConvertImmersiveAnimationHelper) {
        immersiveAnimationHelper = animationHelper
        mContentScrollHelper?.setConvertImmersiveAnimationHelper(animationHelper)
    }


    override fun updateFloatPanelLayoutParams(params: FloatButtonPanelLayoutParams) {
        this.params = params
        mConvertContentAdapter?.updatePaddingBottom(params.height + params.marginVertical)
        backBtnLayout?.updateLayoutParams<CoordinatorLayout.LayoutParams> {
            this.bottomMargin = backLayoutMarginBottom + params.height + params.marginBottom
        }
        val paddingBottom = params.height + params.marginBottom
        mConvertInitView?.updatePaddingBottom(paddingBottom)
        mConvertingView?.updatePaddingBottom(paddingBottom)
    }

    fun updateMark() {
        DebugUtil.d(TAG, "mark update. notify refresh")
        mConvertContentAdapter?.notifyDataSetChanged()
    }

    /**
     * 更新空白View高度
     */
    override fun updateViewSpaceHeight(spaceHeight: Int) {
        viewSpace?.updateLayoutParams<CoordinatorLayout.LayoutParams> {
            this.height = spaceHeight
        }
    }

    private fun updateFilterData(contentItemList: List<ConvertContentItem>?) {
        mConvertViewModel?.mFilterBySpeakerTextItem = ListUtil.getTextMetaDataList(contentItemList)
    }
}