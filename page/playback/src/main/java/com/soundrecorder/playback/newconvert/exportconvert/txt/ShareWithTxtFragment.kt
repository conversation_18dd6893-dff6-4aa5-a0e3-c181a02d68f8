/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.txt

import android.app.Dialog
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import android.widget.ImageView
import android.widget.RelativeLayout
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.COUIRecyclerView
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.checkbox.COUICheckBox
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.material.navigation.NavigationBarView
import com.coui.appcompat.snackbar.COUISnackBar
import com.coui.appcompat.toolbar.COUIToolbar
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.backpressed.OnBackPressedListener
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.dialog.LoadingDialog
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareType
import com.soundrecorder.playback.R
import com.soundrecorder.playback.databinding.FragmentShareWithTxtBinding
import com.soundrecorder.playback.newconvert.ui.ConvertContentItemAdapter
import com.soundrecorder.playback.newconvert.view.CustomLinearLayoutManager

class ShareWithTxtFragment : Fragment(), OnBackPressedListener {
    companion object {
        const val TAG = "ShareWithTxtFragment"
        const val CHOICE_ITEMS_COUNT = 2
        const val SHARE_PARAGRAPH_POSITION = 0
        const val SHARE_TIME_POSITION = 1
        const val SHARE_SPEAKER_POSITION = 2
    }

    private var mToolbar: COUIToolbar? = null

    //保存TXT到本地成功的提示
    private var mCOUISnackBar: COUISnackBar? = null

    //设置dialog里面的listview
    private var mBuilder: COUIAlertDialogBuilder? = null
    private var mCOUISettingDialog: AlertDialog? = null
    private var mBottomNavigationView: COUINavigationView? = null
    private var mediaRecordId = 0L

    //录音文件的路径
    private var playFilePath = ""
    private var mConvertContentAdapter: ConvertContentItemAdapter? = null

    //转文本数据列表
    private var mDataRecyclerview: COUIRecyclerView? = null
    private var mLinearLayoutManager: CustomLinearLayoutManager? = null
    private val mViewModel by activityViewModels<ShareWithTxtViewModel>()

    //是否需要上传埋点
    private var needUploadBuryPoint = true
    private var repository: ShareWithTxtRepository? = null

    private var mTxtLoadingDialog: LoadingDialog? = null
    private var mRootView: View? = null
    private lateinit var binding: FragmentShareWithTxtBinding

    /**
     * 保存文本到本地结果的callback
     */
    private val saveTxtToLocalCallback = object : SaveToLocalCallback {

        override fun onShowSaveFileWaitingDialog() {
            DebugUtil.i(TAG, "onShowSaveFileWaitingDialog...")
            showWaitingDialog()
        }

        override fun onGetFileName(fileName: String, fileAbsPath: String) {
            DebugUtil.i(TAG, "保存到本地名称，fileName >> $fileName， fileAbsPath >> $fileAbsPath")
        }

        override fun onSaveSuccess(fileName: String, fileAbsPath: String) {
            if (fileAbsPath.isBlank() || fileName.isBlank()) {
                DebugUtil.e(TAG, "保存到本地失败，路径或文件名为空")
                dismissDialogShowSnackBar(false, "")
            } else {
                dismissDialogShowSnackBar(true, fileAbsPath)
            }
        }

        override fun onSaveFailed(message: String) {
            DebugUtil.e(TAG, "保存到本地失败，message >> $message")
            dismissDialogShowSnackBar(false, "")
        }
    }

    /**
     * 分享文本前置流程callback
     */
    private val shareListener = object : IShareListener {

        override fun onShowShareWaitingDialog(mediaId: Long, type: ShareType) {
            DebugUtil.i(TAG, "onShowShareWaitingDialog type:$type")
            showWaitingDialog()
        }

        override fun onShareSuccess(mediaId: Long, type: ShareType) {
            DebugUtil.i(TAG, "onShareSuccess，type >> $type")
            dismissDialog()
        }

        override fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String) {
            DebugUtil.i(TAG, "onShareFailed，type >> $type  error:$error  message:$message")
            dismissDialog()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        mRootView = inflater.inflate(R.layout.fragment_share_with_txt, container, false)

        mRootView?.let {
            binding = FragmentShareWithTxtBinding.bind(it)
            initActionBar()
            ensureNavigationView()
            initRecyclerView()
        }

        return mRootView
    }

    private fun initActionBar() {
        mToolbar = mRootView?.findViewById(R.id.toolbar)
        mToolbar?.setNavigationIcon(R.drawable.ic_home_back_arrow)
        mToolbar?.title = getString(com.soundrecorder.common.R.string.export_share_preview_title)
        mToolbar?.setNavigationOnClickListener {
            onBackPressed()
        }
    }

    private fun ensureNavigationView() {
        mBottomNavigationView = mRootView?.findViewById(R.id.navi_menu_tool_share_txt)
        mBottomNavigationView?.setOnItemSelectedListener(
            NavigationBarView.OnItemSelectedListener OnItemSelectedListener@{ item ->
                if (ClickUtils.isFastDoubleClick()) {
                    DebugUtil.i(TAG, "isFastDoubleClick return")
                    return@OnItemSelectedListener true
                }
                onItemSelected(item)
                true
            })
    }

    private fun initRecyclerView() {
        mDataRecyclerview = mRootView?.findViewById(R.id.rv_share_txt_list)
        mConvertContentAdapter = ConvertContentItemAdapter()
        activity?.let {
            mLinearLayoutManager = CustomLinearLayoutManager(it)
        }
        mDataRecyclerview?.layoutManager = mLinearLayoutManager
        mDataRecyclerview?.adapter = mConvertContentAdapter
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        initArgFromBundle(arguments)
        initObservers()
        initListener()
        initData()
        if (savedInstanceState != null) {
            restoreData(savedInstanceState)
        }
        checkNeedRestoreDialog(mViewModel)
        (activity as? BaseActivity)?.registerFinishAndRemoveTaskCallback()
    }

    private fun initArgFromBundle(arguments: Bundle?) {
        arguments?.let {
            mediaRecordId = it.getLong("mediaRecordId")
            playFilePath = it.getString("playFilePath", "")
            DebugUtil.i(TAG, "mediaRecordId >> $mediaRecordId")
            DebugUtil.i(TAG, "playFilePath >> $playFilePath")
        }
    }

    private fun initObservers() {
        mViewModel.mTitleLiveData.observe(viewLifecycleOwner) {
            binding.tvShareTxtTitle.text = it
        }
        mViewModel.mDateLiveData.observe(viewLifecycleOwner) {
            binding.tvShareTxtDate.text = it
        }
        mViewModel.mSubjectLiveData.observe(viewLifecycleOwner) {
            binding.tvShareTxtSubject.text = it
        }
        mViewModel.mContentStringLiveData.observe(viewLifecycleOwner) {
            binding.tvShareTxtContent.text = it
        }
        mViewModel.mItemsListLiveData.observe(viewLifecycleOwner) {
            loadAdapterData(it)
        }
        mViewModel.mShowSpeakerLiveData.observe(viewLifecycleOwner) {
            mConvertContentAdapter?.roleControl(it, false)
        }
        mViewModel.mShowDateLiveData.observe(viewLifecycleOwner) {
            mConvertContentAdapter?.timeControl(it, false)
        }
        mViewModel.mShowLineLiveData.observe(viewLifecycleOwner) {
            switchLineSpacingShowState(it)
        }
    }

    private fun initListener() {
        //保存文本到本地结果的listener
        mViewModel.setOnSaveTxtToLocalResultCallback(saveTxtToLocalCallback)
        mViewModel.setOnShareTxtCallbackResultCallback(shareListener)
    }

    private fun initData() {
        if (repository == null) {
            repository = ShareWithTxtRepository()
        }
        mViewModel.setRepository(repository)
        showLoading()
        mViewModel.initArgFromBundle(arguments)
        mViewModel.loadData()
    }

    /**
     * 如果是重建，则恢复段落、时间和讲话人的选择状态，不使用默认的
     */
    private fun restoreData(bundle: Bundle) {
        val showLine = bundle.getBoolean("showLine")
        val showDate = bundle.getBoolean("showDate")
        val showSpeaker = bundle.getBoolean("showSpeaker")
        mViewModel.mShowLineLiveData.value = showLine
        mViewModel.mShowDateLiveData.value = showDate
        mViewModel.mShowSpeakerLiveData.value = showSpeaker
        DebugUtil.i(TAG, "restoreData showLine = $showLine showDate = $showDate showSpeaker = $showSpeaker")
    }

    /**
     * 检查是否需要重建加载中 dialog
     * 特殊情况，在保存的过程中切换暗色，需要重建dialog
     * 重建过程中保存完成了，需要等待重建完成之后再显示snackBar
     *
     * 具体做法：在viewModel中保存一个值 mNeedShowSnackBar ，表示是否需要展示 snackBar
     * 如果重建完成还没有保存成功，则显示dialog
     * 重建完成之前已经保存完成了，则直接显示snackBar，不显示dialog
     */
    private fun checkNeedRestoreDialog(viewModel: ShareWithTxtViewModel) {
        DebugUtil.i(TAG, "getNeedRestoreDialog >> ${viewModel.getNeedRestoreDialog()}")
        //需要重建的情况下
        if (viewModel.getNeedRestoreDialog()) {
            //如果需要展示snackBar，表示已经保存成功，则不需要重建waitingDialog了，直接展示snackBar
            when {
                viewModel.getNeedShowSnackBar() -> {
                    showSnackBar(
                        getString(
                            com.soundrecorder.common.R.string.export_store_loacl_tips,
                            viewModel.getSaveCallBackFilePath()
                        ),
                        getString(com.soundrecorder.common.R.string.export_view_look),
                        viewModel.getSaveCallBackFilePath()
                    )
                }

                else -> {
                    //没有保存/分享完成，则显示waitDialog
                    showWaitingDialog()
                }
            }
        }
    }

    /**
     * 保存当前段落、时间和讲话人的显示状态
     */
    override fun onSaveInstanceState(outState: Bundle) {
        val showLine = mViewModel.mShowLineLiveData.value == true
        val showDate = mViewModel.mShowDateLiveData.value == true
        val showSpeaker = mViewModel.mShowSpeakerLiveData.value == true
        outState.putBoolean("showLine", showLine)
        outState.putBoolean("showDate", showDate)
        outState.putBoolean("showSpeaker", showSpeaker)
        DebugUtil.i(TAG, "onSaveInstanceState showLine = $showLine showDate = $showDate showSpeaker = $showSpeaker")
        super.onSaveInstanceState(outState)
    }


    /**
     * Set data into adapter. Must set header when data is ready.
     */
    private fun loadAdapterData(it: MutableList<ConvertContentItem>?) {
        if (it.isNullOrEmpty()) {
            showEmptyView()
        } else {
            val name = mViewModel.mTitleLiveData.value ?: ""
            val date = mViewModel.mDateLiveData.value ?: ""
            val subject = mViewModel.mSubjectLiveData.value ?: ""
            val roles = mViewModel.mRolesStringLiveData.value ?: ""
            mConvertContentAdapter?.setHeaderData(name, date, subject, roles)
            mConvertContentAdapter?.data = it
            mConvertContentAdapter?.notifyDataSetChanged()
            hideLoading()
        }
    }

    /**
     * check if show line spacing
     * @param showLineSpacing 是否显示分段
     *      true -> show content textView, hide recyclerView
     *      false ->hide content textView, show recyclerView
     */
    private fun switchLineSpacingShowState(showLineSpacing: Boolean) {
        DebugUtil.i(TAG, "switchLineSpacingShowState showLineSpacing = ${showLineSpacing}")
        val inAlphaAnimation = AlphaAnimation(0f, 1f)
        val outAlphaAnimation = AlphaAnimation(1f, 0f)
        inAlphaAnimation.interpolator = PathInterpolatorHelper.couiEaseInterpolator
        outAlphaAnimation.interpolator = PathInterpolatorHelper.couiEaseInterpolator

        outAlphaAnimation.duration = 200
        inAlphaAnimation.duration = 200

        if (showLineSpacing) {
            binding.rvShareTxtList.startAnimation(inAlphaAnimation)
            binding.rvShareTxtList.visibility = View.VISIBLE
            binding.svShareTxtContent.startAnimation(outAlphaAnimation)
            binding.svShareTxtContent.visibility = View.GONE
        } else {
            binding.svShareTxtContent.startAnimation(inAlphaAnimation)
            binding.svShareTxtContent.visibility = View.VISIBLE
            binding.rvShareTxtList.startAnimation(outAlphaAnimation)
            binding.rvShareTxtList.visibility = View.GONE
        }
    }

    /**
     * 底部菜单栏点击事件
     */
    private fun onItemSelected(item: MenuItem) {
        if (ClickUtils.isQuickClick()) {
            return
        }
        if (mCOUISnackBar?.isShown == true) {
            mCOUISnackBar?.dismiss()
            return
        }
        when (item.itemId) {
            //显示设置
            R.id.menu_share_setting -> {
                DebugUtil.i(TAG, "menu_share_setting click")
                //显示之前保存当前的显示状态，点击dialog取消后恢复当前的状态
                mViewModel.saveResultChoice()
                showSettingDialog()
            }
            //保存txt到本地
            R.id.menu_share_save -> {
                DebugUtil.i(TAG, "menu_share_save click")
                mViewModel.saveTxtToLocal()
            }
            //分享txt
            R.id.menu_share_txt -> {
                DebugUtil.i(TAG, "menu_share_txt click")
                activity?.let {
                    mViewModel.shareTxt(it)
                }
            }
        }
    }

    /**+
     * 显示设置dialog
     * 段落默认开启（mShowLineLiveData）
     * 时间默认开启（mShowDateLiveData）
     * 讲话人与转文本界面的显示状态保持一致（mShowSpeakerLiveData）
     * 如果是旧版本的转文本，不支持讲话人，则不显示讲话人选项
     */
    private fun showSettingDialog() {
        val dialogContentItemsView = layoutInflater.inflate(R.layout.fragment_share_with_txt_dialog, null)
        initDialogContentItemsView(dialogContentItemsView)
        context?.resources?.let {
            mBuilder = COUIAlertDialogBuilder(
                requireContext(),
                com.support.dialog.R.style.COUIAlertDialog_BottomAssignment
            ).apply {
                setBlurBackgroundDrawable(true)
                setView(dialogContentItemsView)
                setTitle(com.soundrecorder.common.R.string.export_paragraph_setting)
                setPositiveButton(com.soundrecorder.common.R.string.rename_save) { _, _ ->
                    onOkButtonClick()
                }
                setNegativeButton(com.soundrecorder.common.R.string.cancel) { _, _ ->
                    onCancelButtonClick()
                }
                setWindowGravity(getBottomAlertDialogWindowGravity(context))
                setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context))
            }
            mCOUISettingDialog = mBuilder?.show()
            mCOUISettingDialog?.window?.findViewById<View>(com.support.dialog.R.id.rootView)
                ?.setBackgroundColor(it.getColor(R.color.share_txt_dialog_bg))
            mCOUISettingDialog?.setOnDismissListener {
                onDialogDismiss()
            }
            ViewUtils.updateWindowLayoutParams(mCOUISettingDialog?.window)
        }
    }

    private fun initDialogContentItemsView(dialogContentItemsView: View) {
        val cbShareParagraph = dialogContentItemsView.findViewById<COUICheckBox>(R.id.cb_share_paragraph)
        val rlShareParagraph = dialogContentItemsView.findViewById<RelativeLayout>(R.id.rl_share_paragraph)
        val cbShareTime = dialogContentItemsView.findViewById<COUICheckBox>(R.id.cb_share_time)
        val ivShareTime = dialogContentItemsView.findViewById<ImageView>(R.id.iv_share_time)
        val rlShareSpeaker = dialogContentItemsView.findViewById<RelativeLayout>(R.id.rl_share_speaker)
        val cbShareSpeaker = dialogContentItemsView.findViewById<COUICheckBox>(R.id.cb_share_speaker)
        rlShareParagraph.visibility = View.GONE
        //根据内容设置UI
        val choiceString = getChoiceString()
        if (choiceString.size == CHOICE_ITEMS_COUNT) {
            rlShareSpeaker.visibility = View.GONE
            ivShareTime.visibility = View.GONE
        }
        //根据设置选项初始化值
        val choiceItems = getChoiceItems()
        choiceItems.forEachIndexed { index, choiceItem ->
            when (index) {
                SHARE_PARAGRAPH_POSITION -> cbShareParagraph.isChecked = choiceItem
                SHARE_TIME_POSITION -> cbShareTime.isChecked = choiceItem
                SHARE_SPEAKER_POSITION -> cbShareSpeaker.isChecked = choiceItem
            }
        }
        cbShareParagraph.setOnStateChangeListener { p0, _ ->
            p0?.let {
                val showSpeaker = mViewModel.mShowSpeakerLiveData.value ?: false
                val showDate = mViewModel.mShowDateLiveData.value ?: false
                mViewModel.mShowLineLiveData.postValue(it.isChecked)
                if (!it.isChecked) { //关闭段落
                    if (showDate) {
                        //如果显示了时间，则关闭
                        cbShareTime.isChecked = false
                    }
                    if (showSpeaker) {
                        //如果显示了讲话人，则关闭
                        cbShareSpeaker.isChecked = false
                    }
                }
            }
        }
        cbShareTime.setOnStateChangeListener { p0, _ ->
            p0?.let {
                val showLine = mViewModel.mShowLineLiveData.value ?: false
                //显示时间，如果没有显示段落，则同时显示段落
                if (it.isChecked && !showLine) {
                    cbShareParagraph.isChecked = true
                }
                mViewModel.mShowDateLiveData.postValue(it.isChecked)
            }
        }
        cbShareSpeaker.setOnStateChangeListener { p0, p1 ->
            p0?.let {
                val showLine = mViewModel.mShowLineLiveData.value ?: false
                //显示讲话人，如果没有显示段落，则同时显示段落
                if (it.isChecked && !showLine) {
                    cbShareParagraph.isChecked = true
                }
                mViewModel.mShowSpeakerLiveData.postValue(it.isChecked)
            }
        }
    }

    /**
     * 设置dialog消失的时候保存结果
     * 如果是用户行为导致dialog关闭(点击确定按钮，点击外部区域，点击返回按钮)，则上传埋点
     * 如果是重建和点击取消按钮则不上传埋点
     */
    private fun onDialogDismiss() {
        mViewModel.saveResultChoice()
        addBuryPoint()
    }

    /**
     * 添加埋点，关闭分段、关闭时间、关闭讲话人
     */
    private fun addBuryPoint() {
        if (needUploadBuryPoint) {
            //如果是用户行为导致dialog关闭(点击确定按钮dismiss，点击外部按钮dismiss，点击返回按钮dismiss)，则上传埋点
            //如果是重建和点击取消按钮则不上传埋点
            mViewModel.addBuryPoint()
        }
        needUploadBuryPoint = true
    }

    private fun onCancelButtonClick() {
        DebugUtil.i(TAG, "dialog click cancel")
        mViewModel.restoreChoice()
        needUploadBuryPoint = false
        mCOUISettingDialog?.dismiss()
    }

    private fun onOkButtonClick() {
        DebugUtil.i(TAG, "dialog click ok")
        //点击确定按钮，会保存选项值，需要上传埋点
        mCOUISettingDialog?.dismiss()
    }

    /**
     * 获取设置弹窗的选项，如果没有分人功能则不显示讲话人
     */
    private fun getChoiceString(): Array<String> {
        DebugUtil.i(TAG, "getChoiceString mViewModel.getCanShowSpeakerRole() = ${mViewModel.getCanShowSpeakerRole()}")
        return if (mViewModel.getCanShowSpeakerRole()) {
            arrayOf(
                getString(com.soundrecorder.common.R.string.export_show_paragraph),
                getString(com.soundrecorder.common.R.string.export_show_time),
                getString(com.soundrecorder.common.R.string.export_show_speaker)
            )
        } else {
            arrayOf(
                getString(com.soundrecorder.common.R.string.export_show_paragraph), getString(
                    com.soundrecorder.common.R.string.export_show_time
                )
            )
        }
    }

    /**
     * 初始化设置弹窗的选项的选中状态，默认显示分段和时间
     * 讲话人是否显示与转文本页面保持一致
     * 如果是旧版本的转文本，不支持讲话人，则不显示讲话人选项
     */
    private fun getChoiceItems(): BooleanArray {
        DebugUtil.i(TAG, "getChoiceItems mViewModel.getCanShowSpeakerRole() = ${mViewModel.getCanShowSpeakerRole()}")
        val b = mViewModel.mShowLineLiveData.value ?: true
        val b1 = mViewModel.mShowDateLiveData.value ?: true
        val b2 = mViewModel.mShowSpeakerLiveData.value ?: true
        return if (mViewModel.getCanShowSpeakerRole()) {
            val booleanArray = BooleanArray(3)
            booleanArray[0] = b
            booleanArray[1] = b1
            booleanArray[2] = b2
            booleanArray
        } else {
            // 如果是旧版本的转文本，不支持讲话人，则不显示讲话人选项
            val booleanArray = BooleanArray(2)
            booleanArray[0] = b
            booleanArray[1] = b1
            booleanArray
        }
    }

    /**
     * 设置dialog外部的灰色蒙版透明，给dialog添加elevation实现阴影效果
     */
    private fun setPanelGone(view: Dialog) {
        try {
            view.apply {
                //去掉背景灰色蒙版
                findViewById<View>(com.support.panel.R.id.panel_outside)?.apply {
                    setBackgroundResource(com.support.appcompat.R.color.coui_transparence)
                }
                //新版本bottomSheetDialog取消掉弹窗的elevation，需要自己加上
                findViewById<View>(com.support.appcompat.R.id.design_bottom_sheet)?.apply {
                    elevation = resources.getDimension(com.soundrecorder.common.R.dimen.dp20)
                }
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "setPanelGone error >> ${e.message}")
        }
    }

    /**
     * 保存到本地文件成功之后显示提示
     * 点击“查看”可以跳转到文件管理对应的目录
     */
    @Suppress("TooGenericExceptionCaught")
    private fun showSnackBar(message: String, actionText: String, fileAbsPath: String) {
        mCOUISnackBar?.dismiss()
        mBottomNavigationView?.let {
            it.post {
                mCOUISnackBar = COUISnackBar.make(it, message, 5000).apply {
                    (parent as? ViewGroup)?.clipChildren = false
                    setOnAction(actionText) {
                        DebugUtil.i(TAG, "save snackBar action click...fileAbsPath >> $fileAbsPath")
                        if (fileAbsPath.isNotBlank()) {
                            activity?.let { activity ->
                                try {
                                    val intent = Intent("oppo.filemanager.intent.action.BROWSER_FILE")
                                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                                    intent.putExtra("CurrentDir", mViewModel.saveTxtFolder)
                                    activity.startActivity(intent)
                                } catch (e: Exception) {
                                    DebugUtil.w(TAG, "showSnackBar error: $e")
                                }
                            }
                        }
                    }
                    show()
                    /**
                     * 防止转写文本选中状态时，复制分享全选弹框遮盖 snackBar
                     * 通过snackBar中textView抢走焦点。使转写文本退出选中状态。
                     */
                    contentView.focusable = View.FOCUSABLE
                    contentView.requestFocusFromTouch()
                    contentView.focusable = View.FOCUSABLE_AUTO
                }
            }
        }
        mViewModel.setAlreadyShowSnackBar()
    }

    override fun onBackPressed(): Boolean {
        DebugUtil.i(TAG, "ShareWithTxt activity onBackPressed...")
        if (mCOUISnackBar?.isShown == true) {
            mCOUISnackBar?.dismiss()
        }
        if (checkDialogShowing()) {
            return true
        }
        activity?.finish()
        return true
    }

    /**
     * 点击返回键的时候判断dialog是否在显示
     * 显示则关闭dialog
     */
    private fun checkDialogShowing(): Boolean {
        if (mCOUISettingDialog?.isShowing == true) {
            mCOUISettingDialog?.dismiss()
            return true
        }
        return false
    }

    /**
     * 显示 “请稍后。。。” dialog
     * 当转文本的文件大于  Constants.SHOW_WAITING_DIALOG__THRESHOLD 的时候，保存到本地和分享需要弹窗
     * 弹窗显示不足1s的时候，需要延迟到1s之后再关闭
     */
    private fun showWaitingDialog() {
        if (mTxtLoadingDialog == null) {
            mTxtLoadingDialog = LoadingDialog(activity)
        }
        //修复loading dialog只显示一次的问题
        if (mTxtLoadingDialog?.isActivityNull() == true) {
            mTxtLoadingDialog?.resetActivity(activity)
        }
        mTxtLoadingDialog?.show(com.soundrecorder.common.R.string.waiting)
        mViewModel.setNeedRestoreDialog(false)
    }

    /**
     * 分享前置流程结束，关闭waitingDialog，执行弹出分享面板操作，重置参数
     */
    private fun dismissDialog() {
        if (mTxtLoadingDialog?.isShowing() == true) {
            mTxtLoadingDialog?.dismiss()
        }
    }

    /**
     * 保存TXT到本地流程结束，关闭waitingDialog，执行弹出snackbar操作，重置参数
     */
    private fun dismissDialogShowSnackBar(success: Boolean, fileAbsPath: String) {
        if (mTxtLoadingDialog?.isShowing() == true) {
            mTxtLoadingDialog?.dismiss()
        }
        if (success) {
            context?.resources?.let {
                showSnackBar(
                    it.getString(com.soundrecorder.common.R.string.export_store_loacl_tips, fileAbsPath),
                    it.getString(com.soundrecorder.common.R.string.export_view_look),
                    fileAbsPath
                )
            }
        }
        mViewModel.setAlreadyShowSnackBar()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        DebugUtil.i(TAG, "onConfigurationChanged......")
        mCOUISnackBar?.dismiss()
        dismissSettingDialog()
        super.onConfigurationChanged(newConfig)
    }

    override fun onDestroyView() {
        DebugUtil.i(TAG, "onDestroyView......")
        super.onDestroyView()
        needUploadBuryPoint = false
        dismissSettingDialog()
        dismissWaitingDialog()
        releaseListener()
        (activity as? BaseActivity)?.unregisterFinishAndRemoveTaskCallback()
    }

    private fun dismissSettingDialog() {
        if (mCOUISettingDialog?.isShowing == true) {
            mCOUISettingDialog?.dismiss()
        }
        mBuilder = null
        mCOUISettingDialog = null
    }

    private fun dismissWaitingDialog() {
        if (mTxtLoadingDialog?.isShowing() == true) {
            mViewModel.setNeedRestoreDialog(true)
            mTxtLoadingDialog?.dismiss()
        }
        mTxtLoadingDialog = null
    }

    private fun showLoading() {
        binding.body.visibility = View.GONE
        binding.colorLoadView.visibility = View.VISIBLE
    }

    private fun hideLoading() {
        binding.body.visibility = View.VISIBLE
        binding.colorLoadView.visibility = View.GONE
    }

    private fun showEmptyView() {
        binding.body.visibility = View.GONE
        binding.colorLoadView.visibility = View.GONE
    }

    private fun releaseListener() {
        mViewModel.setOnSaveTxtToLocalResultCallback(null)
        mViewModel.setOnShareTxtCallbackResultCallback(null)
    }
}