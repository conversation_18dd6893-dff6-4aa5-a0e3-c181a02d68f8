/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertEmptyView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import com.soundrecorder.common.widget.OSImageView

class ConvertEmptyView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConvertView(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "ConvertEmptyView"
    }

    private var container: ConstraintLayout? = null

    override fun initContainer() {
        LayoutInflater.from(context)
            .inflate(com.soundrecorder.playback.R.layout.fragment_convert_empty, this, true)
        container = findViewById(com.soundrecorder.playback.R.id.empty)
    }

    override fun initViews() {
    }

    override fun initLoadingView(): OSImageView? {
        return findViewById(com.soundrecorder.playback.R.id.empty_convert_image)
    }

    override fun initTextLayout(): LinearLayout? {
        return findViewById(com.soundrecorder.playback.R.id.ll_convert_empty)
    }

    override fun tag(): String {
        return TAG
    }
}