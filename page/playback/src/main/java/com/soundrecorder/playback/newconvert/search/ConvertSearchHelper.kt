/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.search

import android.util.SparseArray
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.convert.search.ConvertSearchBean
import java.util.*

/**
 * 转文本搜索工具类
 */
class ConvertSearchHelper(var convertTextItems: List<ConvertContentItem>?) {

    companion object {
        private const val TAG = "ConvertSearchUtil"
    }

    /**
     * 一次搜索的搜索结果
     */
    private var searchResult: MutableList<ConvertSearchBean> = mutableListOf()

    /**
     * 获取搜索结果
     */
    fun getSearchBean(index: Int): ConvertSearchBean? {
        return searchResult.getOrNull(index)
    }


    /**
     * 搜索关键词
     */
    fun queryByKeyWord(keyWord: String): MutableList<ConvertSearchBean> {
        //清空上一次的搜索结果
        searchResult.clear()

        // 如果keyword为空，则返回空集合
        if (keyWord.isBlank()) {
            return searchResult
        }
        var metaItemIndexInAllList = 0
        convertTextItems?.forEachIndexed { index, convertText ->
            val list = queryKeyWordInConvertText(keyWord, convertText, metaItemIndexInAllList)
            searchResult.addAll(list)
            metaItemIndexInAllList += convertText.mTextOrImageItems?.size ?: 0
        }
        DebugUtil.i(TAG, "queryByKeyWord executed. resultSize=${searchResult.size}")
        return searchResult
    }

    /**
     * 在一个转文本段中搜索关键词
     * @param keyWord 搜索关键词
     * @param index 转文本段在整个转文本文件中的索引
     * @param convertContent 转文本段
     */
    private fun queryKeyWordInConvertText(
        keyWord: String,
        convertContent: ConvertContentItem,
        metaItemIndexBefore: Int
    ): List<ConvertSearchBean> {
        val result = mutableListOf<ConvertSearchBean>()
        if (convertContent.textContent.contains(keyWord, false)) {
            val textOrImageItems = convertContent.mTextOrImageItems
            var currentItemIndex = metaItemIndexBefore
            textOrImageItems?.forEachIndexed { textIndex, textImg ->
                if (textImg is ConvertContentItem.TextItemMetaData) {
                    val textImgQueryList = queryKeyWordInTextImg(keyWord, textImg, currentItemIndex)
                    result.addAll(textImgQueryList)
                }
                currentItemIndex += 1
            }
        }
        return result
    }

    /**
     * 在一个TextImg中搜索关键词
     * @param keyWord 搜索关键词
     * @param index 转文本段在整个转文本文件中的索引
     * @param convertText 转文本段的文本内容
     * @param textIndex textImg在转文本段中的索引
     * @param textImg 转文本段中的一个TextImg
     */
    private fun queryKeyWordInTextImg(
        keyWord: String,
        textImg: ConvertContentItem.TextItemMetaData,
        currentItemIndex: Int
    ): List<ConvertSearchBean> {
        val result = mutableListOf<ConvertSearchBean>()
        var keyWordIndexOfSentence = 0 //在子句中的索引，从0开始
        var startOffset = 0 //开始索引的偏移位置
        var currentItemText = textImg.getTextString()
        if (currentItemText == null) {
            return result
        }
        var keyWordIndex = currentItemText.indexOf(keyWord, 0, false)
        while (keyWordIndex != -1) {
            for ((sentenceIndex, sentence) in textImg.textParagraph!!.withIndex()) {   // 文本搜索必须有跳转播放[分句]，所以此处!!无空指针隐患
                if (sentenceIndex == 0) { // 由于SubSentence中的start，end索引是一直连续的，因此在有多个TextOrImageItem的时候，开始索引需要重置
                    startOffset = sentence.startCharSeq
                }
                if (sentence.startCharSeq <= keyWordIndex && keyWordIndex <= sentence.endCharSeq) {
                    keyWordIndexOfSentence = keyWordIndex - startOffset

                    result.add(ConvertSearchBean(keyWord, currentItemIndex, keyWordIndexOfSentence))
                    break
                }
            }
            keyWordIndex += keyWord.length
            keyWordIndex = currentItemText.indexOf(keyWord, keyWordIndex, false)
        }
        return result
    }


    /**
     * 获取当前item位置的搜索结果
     *
     * 一个item有多个TextOrImage，而一个TextOrImage上面有可能有多个ConvertSearchBean，需要返回SpareArray
     * key: TextOrImage的index
     * value: 属于TextOrImage的List<ConvertSearchBean>
     * @param itemIndex 当前TextMetaItem中在整个数据列表中的index
     * @param selectIndex 当前选中的第几个搜索结果
     * @return 返回当前item的搜索结果，并将搜索结果合并起来，以TextOrImage的index为key,--》TextOrImage上面应该显示的搜索结果
     *
     */
    fun filterSearchResult(
        itemIndex: Int,
        selectIndex: Int
    ): SparseArray<MutableList<ConvertSearchBean>> {
        if (searchResult.isNullOrEmpty()) {
//            DebugUtil.d(TAG,"filterSearchResult 搜索结果为空")
            return SparseArray<MutableList<ConvertSearchBean>>()
        }
        val findList = filterCurrentPositionSearchResult(itemIndex, selectIndex)
        if (findList.isEmpty()) { //如果没有找到，表示当前item不是搜索词所在的item,直接设置内容
//            DebugUtil.d(TAG,"setKeyWordSpannable 当前位置：$position item不是搜索词所在的item")
            return SparseArray<MutableList<ConvertSearchBean>>()
        }
        // 如果找到了
        val textItems = splitSearchResultByTextItem(findList) // 根据不同的TextOrImage拆分成不同的集合

        return textItems
    }

    /**
     * 查找属于当前item位置的搜索结果
     * @param itemIndex 当前textMetaData具体分段的index
     * @param selectIndex 搜索结果的选中的第几个
     * @return
     */
    fun filterCurrentPositionSearchResult(
        itemIndex: Int,
        selectIndex: Int
    ): MutableList<ConvertSearchBean> {
        val list = mutableListOf<ConvertSearchBean>() // 保存搜索结果中匹配到的当前位置
        searchResult.forEachIndexed { index, searchBean ->
            if (searchBean.textItemIndex == itemIndex) { // 从搜索结果中匹配到item的位置,高亮搜索词
                searchBean.focus = selectIndex == index // 当前是否是高亮选中的item
                list.add(searchBean)
            }
        }
        return list
    }

    /**
     * 根据搜索结果中的TextItem不同，拆分成不同的集合
     */
    private fun splitSearchResultByTextItem(list: List<ConvertSearchBean>): SparseArray<MutableList<ConvertSearchBean>> {
        val result = SparseArray<MutableList<ConvertSearchBean>>()
        list.forEach {
            val textItemIndex = it.textItemIndex
            var subList = result.get(textItemIndex)
            if (Objects.isNull(subList)) { //不存在，则重新创建一个
                subList = mutableListOf<ConvertSearchBean>()
                result.put(textItemIndex, subList)
            }
            subList.add(it)
        }
        return result
    }
}