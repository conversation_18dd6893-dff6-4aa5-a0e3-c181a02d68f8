/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.SystemClock
import androidx.annotation.StringRes
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.OS12FeatureUtil.isSuperSoundRecorderEpicEffective
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.ConvertStaticsUtil
import com.soundrecorder.common.databean.BeanConvertText
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.convertservice.convert.ConvertCheckUtils
import com.soundrecorder.convertservice.convert.ConvertTaskThreadManager
import com.soundrecorder.convertservice.convert.IConvertCallback
import com.soundrecorder.convertservice.convert.NewConvertTextBinder
import com.soundrecorder.convertservice.convert.NewConvertTextService
import com.soundrecorder.convertservice.process.Code
import com.soundrecorder.convertservice.util.ConvertToUtils
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction
import com.soundrecorder.modulerouter.translate.IAsrDownloadCallback
import com.soundrecorder.modulerouter.translate.IAsrPluginDownloadDelegate
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_CANCEL
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_COMPLETE
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_PROGRESS
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_USER_TIMEOUT
import com.soundrecorder.playback.newconvert.ui.ConvertLoadedCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Collections.emptyList

class ConvertServiceManager(
    var playbackConvertViewModel: PlaybackConvertViewModel?,
    private val convertSupportType: Int?
) : ConvertLoadedCallback {

    companion object {
        private const val TAG = "ConvertServiceManager"
        val CONVERT_FILE_MAP: MutableMap<Long, ConvertContentItem.ConvertFileData> = HashMap()
    }

    private var mainHandler: Handler = Handler(Looper.getMainLooper())
    private var mToastMsg = ""
    private var mToastRunnable: Runnable? = Runnable {
        ToastManager.showShortToast(BaseApplication.getAppContext(), mToastMsg)
    }

    private var mConnection: ServiceConnection? = null
    private var mConvertService: NewConvertTextService? = null
    private var isBindService: Boolean = false
    private var asrPluginDownloadDelegate: IAsrPluginDownloadDelegate? = null
    private var asrDownloadCallback: IAsrDownloadCallback? = null
    private val aiAsrManagerAction by lazy {
        Injector.injectFactory<AIAsrManagerAction>()
    }

    private var mConvertCallback: IConvertCallback? = object : IConvertCallback {
        override fun onConvertStatusChange(
            mediaId: Long,
            convertStatus: ConvertStatus,
            errorCode: Int,
            errorMessage: String,
            convertAiTitle: Boolean
        ) {
            if (playbackConvertViewModel?.mediaRecordId == mediaId) {
                DebugUtil.i(TAG, "<<< onConvertStatusChange errorCode = $errorCode convertStatus = $convertStatus")
                // 错误码有新增变动需要同步逻辑到ConvertCheckUtils方法getErrorMsgByStatus
                when (convertStatus.uploadStatus) {
                    ConvertStatus.USERTIMEOUT_EXCEPTION -> {
                        cancelByUserTimeOut(mediaId, errorMessage)
                        ConvertStaticsUtil.addConvertFailedMessage(convertStatus.uploadStatus, convertStatus.convertStatus, errorMessage)
                        return
                    }

                    ConvertStatus.UPLOAD_STATUS_NO_NETWORK,
                    ConvertStatus.NETWORKERROR_EXCEPTION -> {
                        cancelAndRuntoast(mediaId, com.soundrecorder.common.R.string.network_disconnect)
                        ConvertStaticsUtil.addConvertFailedMessage(convertStatus.uploadStatus, convertStatus.convertStatus, errorMessage)
                        return
                    }

                    ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_FORMAT -> {
                        cancelAndRuntoast(mediaId, com.soundrecorder.common.R.string.convert_error_format)
                        return
                    }

                    ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_DURATION -> {
                        cancelAndRuntoast(mediaId, com.soundrecorder.common.R.string.convert_error_duration_long)
                        return
                    }

                    ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_SIZE -> {
                        cancelAndRuntoast(mediaId, com.soundrecorder.common.R.string.convert_error_size_long)
                        return
                    }

                    ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_DURATION_ZERO -> {
                        cancelAndRuntoast(mediaId, com.soundrecorder.common.R.string.convert_error_damage_file)
                        return
                    }

                    ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL,
                    ConvertStatus.ENCRYPT_EXCEPTION,
                    ConvertStatus.JSONPARSE_EXCEPTION,
                    ConvertStatus.EXCEPTION -> {
                        cancelAndRuntoast(mediaId, com.soundrecorder.common.R.string.convert_text_error)
                        ConvertStaticsUtil.addConvertFailedMessage(convertStatus.uploadStatus, convertStatus.convertStatus, errorMessage)
                        return
                    }

                    ConvertStatus.UPLOAD_STATUS_UPLOAD_ABORT_SUC -> cancelAndRuntoast(mediaId, com.soundrecorder.common.R.string.convert_text_stopped)
                }
                processConvertStatus(convertStatus, mediaId, errorMessage)
            }
        }

        override fun onConvertTextReceived(
            mediaId: Long,
            convertType: Int,
            convertTextResult: BeanConvertText,
            showSwitch: Boolean,
            convertAiTitle: Boolean
        ) {
            DebugUtil.i(TAG, "<<< onConvertTextReceived, convertAiTitle:$convertAiTitle")
            if (playbackConvertViewModel?.mediaRecordId == mediaId) {
                val sublist = convertTextResult.sublist
                val mConvertContent = mutableListOf<ConvertContentItem>()
                if (!sublist.isNullOrEmpty()) {
                    val isSupportXunFeiOrByte = ConvertToUtils.convertImplByXunFeinOrByte(convertType)
                    for (it in sublist) {
                        val item = ConvertToUtils.toConvertContentItem(it, isSupportXunFeiOrByte)
                        if (item != null) {
                            mConvertContent.add(item)
                        }
                    }
                }
                val convertRecord = ConvertDbUtil.selectByRecordId(mediaId)
                playbackConvertViewModel?.let { vm ->
                    val keyWords = convertTextResult.keyWord?.map { it.name } ?: emptyList()
                    vm.keyWords.postValue(keyWords)
                    vm.convertContentData = mConvertContent
                    //这个地方需要放在下面，先赋值，后发送状态更改，状态更改之后触发分句的解析处理
                    vm.mConvertStatus.postValue(CONVERT_STATUS_COMPLETE)
                    val convertTime = getConvertTime(mediaId)
                    ConvertStaticsUtil.addConvertSuccessDuration(convertTime)

                    if (convertRecord != null) {
                        val convertShowSwitch = showSwitch && convertRecord.speakerRoleOriginalNumber > 0
                        DebugUtil.d(TAG, "onConvertTextReceived, showSwitch:$showSwitch, " +
                                "speakerRoleOriginalNumber:${convertRecord.speakerRoleOriginalNumber}")
                        if (isSuperSoundRecorderEpicEffective()) {
                            vm.mShowSwitchInConvertModel.postValue(convertShowSwitch)
                        } else {
                            vm.mShowSwitchInConvertModel.postValue(false)
                        }
                        if (convertShowSwitch) {
                            vm.isSpeakerRoleShowing.postValue(true)
                        } else {
                            vm.isSpeakerRoleShowing.postValue(false)
                        }
                        vm.mOriginalRoleNumber.postValue(convertRecord.speakerRoleOriginalNumber)
                    }
                }
            }
        }

        override fun onConvertProgressChanged(mediaId: Long, uploadProgress: Int, convertProgress: Int, serverPlanCode: Int) {
            DebugUtil.i(
                TAG, """
                    onConvertProgressChanged mediaId = $mediaId
                    uploadProgress = $uploadProgress
                    convertProgress = $convertProgress
                     serverPlanCode = $serverPlanCode
                    """
            )
            if ((playbackConvertViewModel?.mediaRecordId == mediaId) && (convertProgress > 0)) {
                playbackConvertViewModel?.mServerPlanCode = serverPlanCode
                playbackConvertViewModel?.mConvertProgress?.postValue(convertProgress)
            }
        }
    }

    private fun processConvertStatus(
        convertStatus: ConvertStatus,
        mediaId: Long,
        errorMessage: String
    ) {
        when (convertStatus.convertStatus) {
            ConvertStatus.CONVERT_STATUS_NO_NETWORK,
            ConvertStatus.NETWORKERROR_EXCEPTION -> {
                cancelAndRuntoast(mediaId, com.soundrecorder.common.R.string.network_disconnect)
                ConvertStaticsUtil.addConvertFailedMessage(
                    convertStatus.uploadStatus,
                    convertStatus.convertStatus,
                    errorMessage
                )
                return
            }

            ConvertStatus.CONVERT_STATUS_QUERY_FAIL,
            ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL,
            ConvertStatus.CONVERT_STATUS_QUERY_TASK_TIMEOUT,
            ConvertStatus.ENCRYPT_EXCEPTION,
            ConvertStatus.JSONPARSE_EXCEPTION,
            ConvertStatus.EXCEPTION -> {
                cancelAndRuntoast(mediaId, com.soundrecorder.common.R.string.convert_text_error)
                ConvertStaticsUtil.addConvertFailedMessage(
                    convertStatus.uploadStatus,
                    convertStatus.convertStatus,
                    errorMessage
                )
            }

            ConvertStatus.CONVERT_STATUS_ABORTTASK_SUC,
            ConvertStatus.CONVERT_STATUS_ABORTTASK_FAIL -> cancelAndRuntoast(mediaId, com.soundrecorder.common.R.string.convert_text_stopped)
        }
    }

    inner class ConvertTextConnection(
        private var uiCallback: IConvertCallback?,
        private val mediaId: Long
    ) : ServiceConnection {

        override fun onServiceConnected(componentName: ComponentName, service: IBinder) {
            DebugUtil.i(TAG, "onServiceConnected")
            val binder = service as? NewConvertTextBinder
            mConvertService = binder?.newConvertTextService
            if (mConvertService == null) {
                DebugUtil.e(TAG, "onServiceConnected, ConvertTextService from binder is null, return")
                return
            }
            if (mediaId > 0) {
                uiCallback?.let {
                    mConvertService?.registerCallback(mediaId, it)
                }
                DebugUtil.i(TAG, "onServiceConnected IBinder: $service, cts: $mConvertService, registerCallback ")

                playbackConvertViewModel?.executeAsyncCheckCompletedConvert()
            }
        }

        override fun onServiceDisconnected(componentName: ComponentName) {
            DebugUtil.i(TAG, "onServiceDisconnected ")
            if (mediaId > 0) {
                uiCallback?.let {
                    mConvertService?.unregisterCallback(mediaId, it)
                }
            }
            mConvertService = null
            uiCallback = null
        }
    }


    fun startConvertTextService(activity: AppCompatActivity?) {
        val convertServiceIntent = Intent(activity, NewConvertTextService::class.java)
        activity?.packageName.also { convertServiceIntent.`package` = it }
        activity?.startService(convertServiceIntent)
    }

    fun bindConvertTextService(context: Context?, mediaId: Long = -1L): Boolean {
        if (context == null) return false
        if (!isBindService) {
            isBindService = true
            val convertServiceIntent = Intent(context, NewConvertTextService::class.java)
            mConnection = ConvertTextConnection(mConvertCallback, mediaId)
            mConnection?.let {
                return context.applicationContext.bindService(convertServiceIntent, it, Context.BIND_AUTO_CREATE)
            }
        }
        return false
    }

    fun unbindConvertService(context: Context?) {
        mConvertCallback?.let {
            mConvertService?.unregisterCallback(playbackConvertViewModel?.mediaRecordId ?: -1, it)
        }
        if (isBindService) {
            context?.run {
                mConnection?.let {
                    DebugUtil.i(TAG, "unbindConvertService! ")
                    applicationContext.unbindService(it)
                }
                mConnection = null
            }
            mConvertService = null
            isBindService = false
        }
    }

    /**
     * 非实时asr转写的条件和之前内销一致
     */
    private fun checkCanConvert(
        record: com.soundrecorder.common.databean.Record?,
        fileFormat: String?,
        fileDuration: Long?
    ): Boolean {
        if (record == null) {
            DebugUtil.i(TAG, "checkCanConvert, record is null")
            return false
        }
        val context = BaseApplication.getAppContext()
        if (NetworkUtils.isNetworkInvalid(context)) {
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
            return false
        }
        val isComplete: Boolean = ConvertDbUtil.checkAlreadyConvertComplete(record.id)
        if (isComplete) {
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.transfer_finished)
            return false
        }

        val canAddNewTask = ConvertTaskThreadManager.checkCanAddNewTask(record.id)
        DebugUtil.i(TAG, "checkCanConvert, recordIdFromActivity: ${record.id},canAddNewTask: $canAddNewTask")
        if (canAddNewTask == ConvertTaskThreadManager.OVER_LIMIT) {
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.convert_recordings_exceeds_upper_limit_v2)
            return false
        }

        if (!isFileConditionMet(record, fileFormat, fileDuration)) {
            return false
        }
        return true
    }

    private fun isFileConditionMet(record: com.soundrecorder.common.databean.Record, fileFormat: String?, fileDuration: Long?): Boolean {
        if (!ConvertCheckUtils.isFileSizeMinMet(record.mFileSize)) {
            DebugUtil.d(TAG, "isFileConditionMet, upload_status_exception")
            mConvertCallback?.onConvertStatusChange(
                record.id,
                ConvertStatus(ConvertStatus.UPLOAD_STATUS_EXCEPTION, ConvertStatus.CONVERT_STATUS_UNINIT),
                Code.FILESIZE_WRONG
            )
            return false
        }
        if (!ConvertCheckUtils.isFileSizeMaxMet(record.mFileSize)) {
            DebugUtil.d(TAG, "isFileConditionMet, upload_status_unsupported_file_size")
            mConvertCallback?.onConvertStatusChange(
                record.id,
                ConvertStatus(
                    ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_SIZE,
                    ConvertStatus.CONVERT_STATUS_UNINIT
                ),
                Code.FILESIZE_WRONG
            )
            return false
        }
        if (!isFileDurationMet(fileDuration, record)) {
            return false
        }
        if (!isFileFormatMet(fileFormat, record)) {
            return false
        }
        return true
    }

    private fun isFileFormatMet(
        fileFormat: String?,
        record: com.soundrecorder.common.databean.Record
    ): Boolean {
        fileFormat?.let {
            return ConvertCheckUtils.isFileFormatMet(it).apply {
                if (!this) {
                    DebugUtil.d(TAG, "isFileConditionMet, upload_status_unsupported_file_format")
                    mConvertCallback?.onConvertStatusChange(
                        record.id,
                        ConvertStatus(
                            ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_FORMAT,
                            ConvertStatus.CONVERT_STATUS_UNINIT
                        ),
                        Code.FILEFORMAT_WRONG
                    )
                }
            }
        }
        return true
    }

    private fun isFileDurationMet(
        fileDuration: Long?,
        record: com.soundrecorder.common.databean.Record
    ): Boolean {
        fileDuration?.let {
            if (!ConvertCheckUtils.isFileDurationMinMet(it)) {
                DebugUtil.w(TAG, "mFileDuration <= 0!")
                mConvertCallback?.onConvertStatusChange(
                    record.id,
                    ConvertStatus(
                        ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_DURATION_ZERO,
                        ConvertStatus.CONVERT_STATUS_UNINIT
                    ),
                    Code.FILEDURATION_WRONG
                )
                return false
            }
            if (!ConvertCheckUtils.isFileDurationMaxMet(it)) {
                DebugUtil.d(TAG, "isFileConditionMet, fileDuration max")
                mConvertCallback?.onConvertStatusChange(
                    record.id,
                    ConvertStatus(
                        ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_DURATION,
                        ConvertStatus.CONVERT_STATUS_UNINIT
                    ),
                    Code.FILEDURATION_WRONG
                )
                return false
            }
        }
        return true
    }

    override fun onLoadCurrentConvert() {
        DebugUtil.i(TAG, "onLoadCurrentConvert callback")
        startOrResumeConvert()
    }

    fun startOrResumeConvert(convertAiTitle: Boolean = false) {
        val canConvert = checkCanConvert(playbackConvertViewModel?.mRecord,
            playbackConvertViewModel?.mFileFormat,
            playbackConvertViewModel?.mFileDuration)
        DebugUtil.d(TAG, "startOrResumeConvert, canConvert:$canConvert")
        if (canConvert) {
            checkAsrPluginDownloadState {
                val result = mConvertService?.startOrResumeConvert(
                    mediaId = playbackConvertViewModel?.mediaRecordId ?: -1,
                    convertAbilityType = convertSupportType ?: ConvertSupportManager.CONVERT_DISABLE,
                    convertAiTitle = convertAiTitle
                )
                DebugUtil.i(TAG, "startConvert convertSupportType=$convertSupportType, result = $result")
                if (result == true) {
                    playbackConvertViewModel?.mediaRecordId?.let { mediaId ->
                        if (CONVERT_FILE_MAP[mediaId] == null) {
                            CONVERT_FILE_MAP[mediaId] = ConvertContentItem.ConvertFileData(mediaId, SystemClock.elapsedRealtime())
                        }
                    }
                    playbackConvertViewModel?.updateConvertStatus(CONVERT_STATUS_PROGRESS)
                }
                ConvertStaticsUtil.addConvertStartEvent()
            }
        }
    }

    private fun checkAsrPluginDownloadState(funStartConvert: (() -> Unit)) {
        if (convertSupportType != ConvertSupportManager.CONVERT_AI_CONVERT) {
            funStartConvert.invoke()
            return
        }
        DebugUtil.i(TAG, "checkAsrPluginDownloadState")

        playbackConvertViewModel?.viewModelScope?.launch(Dispatchers.IO) {
            if (asrPluginDownloadDelegate == null) {
                asrPluginDownloadDelegate = aiAsrManagerAction?.newAsrPluginDownloadDelegate()
            }
            if (asrPluginDownloadDelegate?.isAsrPluginDownload() == false) {
                if (asrDownloadCallback == null) {
                    asrDownloadCallback = object : IAsrDownloadCallback {

                        override fun downloadSuccess(sceneName: String) {
                            funStartConvert.invoke()
                        }

                        override fun downloadFail(errorMessage: String) {
                        }
                    }
                }
                asrPluginDownloadDelegate?.checkAndDownloadAsrPlugin(BaseApplication.getAppContext(), false, asrDownloadCallback)
            } else {
                funStartConvert.invoke()
            }
        }
    }

    fun cancelConvert(mediaId: Long) {
        if (ClickUtils.isQuickClick()) {
            DebugUtil.i(TAG, "cancelConvert isQuickClick return")
            return
        }
        val isConverting: Boolean? = mConvertService?.checkConvertTaskRunning(mediaId)
        DebugUtil.i(TAG, "cancelConvert isConverting : $isConverting")
        if (isConverting == true) {
            val result = mConvertService?.cancelConvert(mediaId)
        }
        val convertTime = getConvertTime(mediaId)
        if (convertTime > 0) {
            ConvertStaticsUtil.addConvertCancelDuration(convertTime)
        }
        ConvertStaticsUtil.addConvertStopEvent()
    }

    private fun cancelByUserTimeOut(mediaId: Long, message: String) {
        playbackConvertViewModel?.mUserConvertTimePerDay = message
        playbackConvertViewModel?.updateConvertStatus(CONVERT_STATUS_USER_TIMEOUT)
        val convertTime = getConvertTime(mediaId)
        if (convertTime > 0) {
            ConvertStaticsUtil.addConvertFailDuration(convertTime)
        }
    }

    private fun cancelAndRuntoast(mediaId: Long, @StringRes resId: Int) {
        playbackConvertViewModel?.updateConvertStatus(CONVERT_STATUS_CANCEL)
        val convertTime = getConvertTime(mediaId)
        if (convertTime > 0) {
            ConvertStaticsUtil.addConvertFailDuration(convertTime)
        }
        mToastMsg = BaseApplication.getAppContext().getString(resId)
        mToastRunnable?.let {
            mainHandler.removeCallbacks(it)
            mainHandler.post(it)
        }
    }

    private fun getConvertTime(mediaId: Long): Long {
        var convertTime = 0L
        CONVERT_FILE_MAP[mediaId]?.let { item ->
            convertTime = SystemClock.elapsedRealtime() - item.startTime
            CONVERT_FILE_MAP.remove(mediaId)
        }
        return convertTime
    }

    fun release() {
        DebugUtil.d(TAG, "release.")
        unbindConvertService(BaseApplication.getAppContext())
        mToastRunnable = null
        mainHandler.removeCallbacksAndMessages(null)
        mConvertCallback = null
        asrDownloadCallback = null
        asrPluginDownloadDelegate = null
    }
}