/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameServiceManger
 * * Description: SmartNameServiceManger
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.playback.smartname

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.suffix
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.convertservice.convert.ConvertCheckUtils
import com.soundrecorder.convertservice.smartname.SmartNameTaskManager
import com.soundrecorder.modulerouter.smartname.ISmartNameCallback
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object SmartNameManger {

    private const val TAG = "SmartNameServiceManger"
    private var mSelectRecordList: MutableList<Record>? = null
    private var mSmartCallback: ISmartNameCallback? = object : ISmartNameCallback {

        override fun onSmartNameError(mediaId: Long, errorCode: Int, errorMsg: String?) {
            DebugUtil.d(TAG, "processSmartNameError, mediaId:$mediaId, errorCode:$errorCode, errorMsg:$errorMsg")
            unregisterSmartNameCallback(mediaId)
        }

        override fun onSmartNameFinished(
            mediaId: Long,
            jsonResult: String,
            extras: Map<String, Any>?
        ) {
            DebugUtil.d(TAG, "processSmartNameFinished, mediaId:$mediaId")
            unregisterSmartNameCallback(mediaId)
        }
    }

    @JvmStatic
    private fun unregisterSmartNameCallback(mediaId: Long) {
        mSmartCallback?.let { SmartNameTaskManager.unregisterSmartNameCallback(mediaId, it) }
    }

    @JvmStatic
    fun toastMessage(resId: Int) {
        ToastManager.showShortToast(BaseApplication.getAppContext(), resId)
    }

    /**
     * 开始智能命名/转文本
     */
    @JvmStatic
    fun startOrResumeConvertSmartName(selectedMediaIdList: MutableList<Long>?) {
        if (selectedMediaIdList.isNullOrEmpty()) {
            return
        }
        DebugUtil.d(TAG, "startOrResumeConvertSmartName, selectedMediaIdList:${selectedMediaIdList.size}")
        if (selectedMediaIdList.size > SmartNameTaskManager.LIMIT_SIZE
            || SmartNameTaskManager.checkTaskRunningMaxLimitSize()) {
            toastMessage(com.soundrecorder.common.R.string.smart_name_task_too_many)
        }
        val idList = arrayOfNulls<String?>(selectedMediaIdList.size)
        selectedMediaIdList.forEachIndexed { index, id ->
            idList[index] = id.toString()
        }
        CoroutineScope(Dispatchers.IO).launch {
            mSelectRecordList = MediaDBUtils.getMediaRecordsById(BaseApplication.getAppContext(), idList)
            mSelectRecordList?.forEach { item ->
                val mediaId = item.id
                val canConvert = checkCanConvert(item)
                DebugUtil.d(TAG, "startOrResumeConvertSmartName, canConvert:$canConvert")
                if (canConvert) {
                    mSmartCallback?.let { SmartNameTaskManager.registerSmartNameCallback(mediaId, it) }
                    SmartNameTaskManager.startSmartName(mediaId, null)
                }
            }
        }
    }

    /**
     * 文件后缀是否支持智能命名
     * 智能命名支持格式：MP3、AMR、AWB、AAC、WAV
     */
    @JvmStatic
    fun isSuffixSupport(suffix: String?): Boolean {
        DebugUtil.d(TAG, "isSuffixSupport, suffix:$suffix")
        if (suffix.isNullOrEmpty()) {
            return false
        }
        return when (suffix) {
            RecorderConstant.MP3_FILE_SUFFIX,
            RecorderConstant.AAC_FILE_SUFFIX,
            RecorderConstant.WAV_FILE_SUFFIX,
            RecorderConstant.AMR_FILE_SUFFIX,
            RecorderConstant.AMR_WB_FILE_SUFFIX -> true
            else -> false
        }
    }

    @JvmStatic
    private fun checkCanConvert(mediaRecord: Record): Boolean {
        val mediaId = mediaRecord.id
        val context = BaseApplication.getAppContext()
        if (NetworkUtils.isNetworkInvalid(context)) {
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
            return false
        }
        DebugUtil.i(TAG, "CheckConvertTask start. record:$mediaRecord")
        val suffix = mediaRecord.data.suffix()
        if (!isSuffixSupport(suffix)) {
            toastMessage(com.soundrecorder.common.R.string.smart_name_error_damage_file)
            return false
        }
        var fileFormat: String? = null
        var fileDuration: Long? = null
        val fileSize: Long? = mediaRecord.mFileSize

        if (PermissionUtils.hasReadAudioPermission()) {
            ConvertDbUtil.updateRecordIdByMediaPath(mediaRecord.data, mediaId)
        }
        val convertRecord = ConvertDbUtil.selectByRecordId(mediaId)
        if (convertRecord == null) {
            DebugUtil.i(TAG, "MediaMetadataRetriever start.}")
            val mLocalUri = MediaDBUtils.genUri(mediaId)
            val pairFormatAndDuration = MediaDBUtils.getFileFormatAndDurationFromUri(mLocalUri)
            fileFormat = pairFormatAndDuration.first
            fileDuration = pairFormatAndDuration.second
            DebugUtil.i(TAG, "MediaMetadataRetriever end. mFileFormat:$fileFormat, mFileDuration:$fileDuration")
        }

        fileSize?.let {
            if (!ConvertCheckUtils.isFileSizeMinMet(it)) {
                DebugUtil.d(TAG, "isFileConditionMet, upload_status_exception")
                toastMessage(com.soundrecorder.common.R.string.smart_name_error_damage_file)
                return false
            }
            if (!ConvertCheckUtils.isFileSizeMaxMet(fileSize)) {
                toastMessage(com.soundrecorder.common.R.string.smart_name_error_size_long)
                return false
            }
        }

        if (!isFileDurationMet(fileDuration)) {
            return false
        }
        if (!isFileFormatMet(fileFormat)) {
            return false
        }
        return true
    }

    @JvmStatic
    private fun isFileFormatMet(fileFormat: String?): Boolean {
        fileFormat?.let {
            return ConvertCheckUtils.isFileFormatMet(it).apply {
                if (!this) {
                    toastMessage(com.soundrecorder.common.R.string.smart_name_error_format)
                }
            }
        }
        return true
    }

    @JvmStatic
    private fun isFileDurationMet(fileDuration: Long?): Boolean {
        fileDuration?.let {
            if (!ConvertCheckUtils.isFileDurationMinMet(it)) {
                DebugUtil.w(TAG, "mFileDuration <= 0!")
                toastMessage(com.soundrecorder.common.R.string.smart_name_error_damage_file)
                return false
            }
            if (!ConvertCheckUtils.isFileDurationMaxMet(it)) {
                DebugUtil.d(TAG, "isFileConditionMet, fileDuration max")
                toastMessage(com.soundrecorder.common.R.string.smart_name_error_duration_long)
                return false
            }
        }
        return true
    }

    @JvmStatic
    fun release() {
        mSelectRecordList?.clear()
        SmartNameTaskManager.releaseAll()
    }
}