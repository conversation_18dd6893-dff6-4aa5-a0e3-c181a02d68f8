/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.graphics.Color
import android.graphics.Typeface
import android.os.Handler
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextPaint
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.RelativeSizeSpan
import android.text.style.StyleSpan
import android.text.style.SuperscriptSpan
import android.view.View
import androidx.annotation.VisibleForTesting
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.utils.DatumPointUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.playback.R
import com.soundrecorder.playback.newconvert.ui.SeekPlayActionModeCallback
import java.lang.ref.WeakReference
import java.util.concurrent.atomic.AtomicBoolean

class BackGroundTextViewSetupHelper {
    // 使用AtomicBoolean保证线程安全
    private val isMarkMode = AtomicBoolean(false)

    private inline fun <T> withMarkMode(block: () -> T): T {
        isMarkMode.set(true)
        try {
            return block()
        } finally {
            isMarkMode.set(false)
        }
    }

    companion object {
        const val TAG = "BackGroundTextViewSetupHelper"
        private const val ICON_MARK_FLAG = "\u2691"
        private const val FLAG_SIZE = 0.7f
        private var fragmentHandler: WeakReference<Handler>? = null
        private val MARK_MODE_PUNCTUATION_REGEX = Regex("""[、，。：；！？,:;!?]""")
        private val NORMAL_MODE_PUNCTUATION_REGEX = Regex("""[。：；！？:;!?]""")
    }

    object MessageTypes {
        const val MSG_MARK_CLICKED = 1
        const val MSG_SHOW_MARK_LIST = 2
    }

    /**
     * 设置Fragment的Handler
     * */
    fun setFragmentHandler(handler: Handler) {
        fragmentHandler = WeakReference(handler)
    }

    /**
     * 发送标记点击消息
     * */
    fun sendMarkClicked(markTime: Long) {
        fragmentHandler?.get()?.obtainMessage(MessageTypes.MSG_MARK_CLICKED, markTime)?.sendToTarget()
            ?: DebugUtil.w(TAG, "Fragment handler not available")
    }

    /**
     * 发送显示标记列表消息
     * */
    fun sendShowMarkList() {
        fragmentHandler?.get()?.sendEmptyMessage(MessageTypes.MSG_SHOW_MARK_LIST)
            ?: DebugUtil.w(TAG, "Fragment handler not available")
    }

    interface OnBlackGroundTextClickListenner {
        fun onTextViewClick(
            view: View,
            convertContentItem: ConvertContentItem?,
            currentItemIndex: Int
        )
    }

    fun setUpBackgroundTextView(
        itemIndex: Int,
        currentItem: ConvertContentItem.TextItemMetaData?,
        convertContentItem: ConvertContentItem?,
        backgroundTextView: BackgroundTextView?,
        isFirstOne: Boolean,
        isLastOne: Boolean,
        searchMode: Boolean,
        callback: SeekPlayActionModeCallback?,
        onClickListener: OnBlackGroundTextClickListenner?,
        drawAttr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr,
        currentPlaybackTime: Long
    ) {
        DebugUtil.i(TAG, "setUpBackgroundTextView isLastOne $isLastOne isFirstOne $isFirstOne, searchMode $searchMode")
        if (currentItem == null || convertContentItem == null || backgroundTextView == null) {
            DebugUtil.i(TAG, "checkAndAddBackgroundTextView currentItem or convertContentItem null or backgroundTextView null, return")
            return
        }
        ImageTextItemLayoutParamUtil.setTopMarginsForBackGroundTextView(
            isFirstOne,
            drawAttr,
            backgroundTextView
        )

        /**
        * Epic 9309689-AI录音视觉焕新
        * 4.5.4.2 AI录音视觉焕新
        * 1. 根据视觉figma去除标记下划线
        * 2. 根据视觉figma修改标记小旗子逻辑
        * 3. 以下先注释掉，后续决定是否删除逻辑
        */
/*        //设置标记下划线
        sentenceOfContentUnderLine(backgroundTextView, currentItem, convertContentItem, itemIndex)
        //设置text，同时设置标记小旗子
        setItemContent(backgroundTextView, currentItem)*/
        setContentMarkedSpannable(backgroundTextView,
            convertContentItem,
            currentItem,
            itemIndex)
        //设置正在播放的的高亮部分
        switchSentenceBackground(
            backgroundTextView,
            convertContentItem,
            currentItem,
            itemIndex,
            searchMode,
            currentPlaybackTime
        )

        //设置TextView相关的Callback
        callback?.mItemTextView = backgroundTextView
        backgroundTextView.customSelectionActionModeCallback = callback
        DebugUtil.i(
            TAG,
            "checkAndAddBackgroundTextView addTextViewHeight ${backgroundTextView.measuredHeight}"
        )
        //设置TextView的点击事件处理
        if (FunctionOption.IS_SUPPORT_SEEKPLAY_FEATURE) {
            backgroundTextView.setOnClickListener {
                DebugUtil.i(TAG, "onTextViewClick")
                onClickListener?.onTextViewClick(it, convertContentItem, itemIndex)
            }
        }
        return
    }

    @VisibleForTesting
    fun sentenceOfContentUnderLine(
        backgroundTextView: BackgroundTextView,
        currentItem: ConvertContentItem.TextItemMetaData,
        convertContentItem: ConvertContentItem,
        itemIndex: Int
    ) {
        if (!FunctionOption.IS_SUPPORT_NEW_UI_OF13) return
        if (currentItem.textParagraph.isNullOrEmpty()) return
        if (currentItem.hasTextMark()) {
            val underline = mutableListOf<Pair<Int, Int>>()
            var count = 0
            for (sentencesIndex in currentItem.textParagraph!!.indices) {
                val subItem = currentItem.textParagraph?.get(sentencesIndex)
                if (subItem?.onlyHasSimpleMark == true) {
                    count++
                    val stringLengthBefore =
                        convertContentItem.getTextStringLengthBeforeTextImageItemIndex(itemIndex)
                    val space = count * TextImageMixLayout.SPACE.length  //当前段旗子所占用字符数
                    val startSeq = subItem.startCharSeq + space - stringLengthBefore
                    val endSeq =
                        subItem.endCharSeq + space - 1 - stringLengthBefore  //句尾不需要覆盖标点符号 减去标点符号所占用的字符数
                    DebugUtil.i(
                        TAG,
                        "sentenceOfContentUnderLine stringLengthBefore $stringLengthBefore, startSeq $startSeq, endSeq $endSeq"
                    )
                    underline.add(Pair(startSeq, endSeq))
                }
            }
            DebugUtil.i(
                TAG,
                "sentenceOfContentUnderLine   size = ${underline.size} underline == $underline"
            )
            if (underline.size > 0) {
                backgroundTextView.setIsUnderLine(underline, true)
            } else {
                backgroundTextView.setIsUnderLine(null, false)
            }
        } else {
            DebugUtil.i(
                TAG,
                "sentenceOfContentUnderLine hasMark = ${currentItem.hasTextMark()}" +
                        " listSubSentence is null or empty = ${currentItem.textParagraph.isNullOrEmpty()}"
            )
            backgroundTextView.setIsUnderLine(null, false)
        }
    }

    fun setItemContent(
        itemTextContent: BackgroundTextView,
        currentItem: ConvertContentItem.TextItemMetaData?,
    ) {
        if (!FunctionOption.IS_SUPPORT_NEW_UI_OF13) {
            itemTextContent.text = currentItem?.getTextString()
        } else {
            setItemContentSpannable(
                SpannableStringBuilder(currentItem?.getTextString()),
                itemTextContent,
                currentItem,
            )
        }
    }

    @VisibleForTesting
    fun setItemContentSpannable(
        builder: SpannableStringBuilder,
        itemTextContent: BackgroundTextView,
        currentItem: ConvertContentItem.TextItemMetaData?,
        searchMode: Boolean = false
    ) {
        if (currentItem == null) {
            DebugUtil.i(TAG, "setItemContentSpannable item == null, return")
            return
        }
        if (currentItem.textParagraph != null && currentItem.textParagraph!!.isNotEmpty()) {
            var start = 0
            for (sentencesIndex in currentItem.textParagraph!!.indices) {
                val sentencesItem = currentItem.textParagraph!![sentencesIndex]
                if (sentencesItem.onlyHasSimpleMark) {
                    val imageSpan = CenterImageSpan(itemTextContent.context, R.drawable.ic_red_flag)
                    builder.insert(start, TextImageMixLayout.SPACE)
                    builder.setSpan(
                        imageSpan,
                        start,
                        start + TextImageMixLayout.IMAGE_SPAN_SPACE,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    //todo
                    if (searchMode && itemTextContent.getHighLight()) { // 本TextView中有关键词被选中，存在标题ICON的字符长度，需要矫正index
                        val originStart = itemTextContent.getStartHighLightSeq()
                        val originEnd = itemTextContent.getEndHighLightSeq()

                        if (originStart >= start) {  //在高亮词前插入ImageSpan，需要矫正高亮词Index
                            itemTextContent.updateHighLight(
                                originStart + TextImageMixLayout.SPACE.length,
                                originEnd + TextImageMixLayout.SPACE.length,
                                true,
                                invalidate = false
                            )
                            //DebugUtil.i(TAG, "setItemContentSpannable originStart ${originStart}, originEnd $originEnd ")
                        }
                    }
                    start += TextImageMixLayout.SPACE.length
                }
                start += sentencesItem.text.length
            }
        }
        DebugUtil.i(TAG, "setItemContentSpannable  text = ${builder.length}")
        itemTextContent.text = builder
    }

    /**
     * change focus state background by Sentence
     */
    fun switchSentenceBackground(
        backgroundTextView: BackgroundTextView,
        convertContentItem: ConvertContentItem?,
        currentItem: ConvertContentItem.TextItemMetaData?,
        itemIndex: Int,
        searchMode: Boolean,
        currentPlaybackTime: Long
    ) {
        if (convertContentItem == null || currentItem == null) {
            return
        }
        //当前小段落为选中状态，设置为选中状态
        if (currentItem.isFocuse() && !searchMode) {
            var count = 0
            var startSeq: Int
            var endSeq = 0
            var space: Int
            for (subItem in currentItem.textParagraph!!) {
                if (subItem.onlyHasSimpleMark) {
                    val hasFlag = backgroundTextView.hasFlagSpan(endSeq, endSeq + TextImageMixLayout.SPACE.length)
                    DebugUtil.i(TAG, "switchSentenceBackground data has mark start:$endSeq view has flag：$hasFlag")
                    if (hasFlag) {
                        count++
                    }
                }
                space = count * TextImageMixLayout.SPACE.length  //当前段旗子所占用字符数
                val playHighlightRange = calculatePlayHighlightRange(
                    backgroundTextView.text.toString(),
                    convertContentItem.startTime,
                    convertContentItem.endTime,
                    currentPlaybackTime
                )
                startSeq = playHighlightRange.textStartIndex + space
                endSeq = playHighlightRange.textEndIndex + space
                if (subItem.isFocused) {
                    val textLengthBeforeIndex =
                        convertContentItem.getTextStringLengthBeforeTextImageItemIndex(itemIndex)
                    val showStartSeq = startSeq - textLengthBeforeIndex
                    val showEndSeq = endSeq - textLengthBeforeIndex
                    backgroundTextView.updateHighLight(showStartSeq, showEndSeq, true, invalidate = true)
                    DebugUtil.i(TAG, "switchSentenceBackground ItemIndex $itemIndex startSeq $startSeq, endSeq $endSeq" +
                            " , subItem.startCharSeq${subItem.startCharSeq}, subItem.endCharSeq ${subItem.endCharSeq}" +
                            ", space $space , textLengthBeforeIndex $textLengthBeforeIndex"
                    )
                }
            }
        } else {
            //当前整个item不是focus，设置为非高亮
            backgroundTextView.updateHighLight(0, 0, false, invalidate = true)
            backgroundTextView.background = null
        }
    }

    /**
     * 设置text，同时设置标记小旗子
     * 1. index：已添加得标识个数
     * 2.调用calculatePlayHighlightRange，计算出需要高亮得范围
     * 3.showStartSeq：高亮开始位置的索引
     * 4.showEndSeq：高亮结束位置的索引
     */
    fun setContentMarkedSpannable(
        backgroundTextView: BackgroundTextView,
        convertContentItem: ConvertContentItem,
        currentItem: ConvertContentItem.TextItemMetaData?,
        itemIndex: Int
    ) {
        if (currentItem == null) return
        withMarkMode {
            val text = convertContentItem.textContent
            val spannable = SpannableStringBuilder(text)

            currentItem.textParagraph?.forEach { subItem ->
                if (subItem.onlyHasSimpleMark) {
                    var index = 0
                    subItem.markDataBeanList?.forEach { markDataBean ->
                        markDataBean?.let { bean ->
                            val playHighlightRange = calculatePlayHighlightRange(
                                convertContentItem.textContent,
                                convertContentItem.startTime,
                                convertContentItem.endTime,
                                bean.timeInMills
                            )
                            val textLengthBeforeIndex = convertContentItem.getTextStringLengthBeforeTextImageItemIndex(itemIndex)
                            val showStartSeq = playHighlightRange.textStartIndex - textLengthBeforeIndex
                            val showEndSeq = playHighlightRange.textEndIndex - textLengthBeforeIndex
                            val mHighlightColor = COUIContextUtil.getColor(backgroundTextView.context, com.support.appcompat.R.color.coui_color_blue)
                            val start = showStartSeq + index
                            val end = showEndSeq + index
                            index++
                            val flagSpan = SpannableStringBuilder(ICON_MARK_FLAG).apply {
                                setSpan(SuperscriptSpan(), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                                setSpan(RelativeSizeSpan(FLAG_SIZE), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                                setSpan(ForegroundColorSpan(mHighlightColor), 0, length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                            }
                            spannable.insert(end, flagSpan)

                            spannable.setSpan(
                                createClickableSpan(bean.timeInMills),
                                start,
                                end,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )

                            spannable.setSpan(ForegroundColorSpan(mHighlightColor), start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                            spannable.setSpan(StyleSpan(Typeface.NORMAL), start, end, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                        }
                    }

                    setupTextViewForClickableSpans(backgroundTextView)
                    backgroundTextView.text = spannable
                } else {
                    backgroundTextView.text = text
                }
            }
        }
    }

    /**
     * 点击加入标记的句子拉起标记列表，触发点击事件时发送消息
     */
    private fun createClickableSpan(markTime: Long): ClickableSpan {
        return object : ClickableSpan() {
            override fun onClick(widget: View) {
                DebugUtil.d(TAG, "Marked sentence clicked at time $markTime")
                sendMarkClicked(markTime)
                sendShowMarkList()
            }
            override fun updateDrawState(ds: TextPaint) {
                super.updateDrawState(ds)
                ds.isUnderlineText = false
            }
        }
    }

    /**
     * 设置TextView支持可点击Span
     */
    private fun setupTextViewForClickableSpans(textView: BackgroundTextView) {
        textView.apply {
            movementMethod = LinkMovementMethod.getInstance()
            highlightColor = Color.TRANSPARENT
            isClickable = true
            isFocusable = true
        }
    }

    /**
     * 计算播放高亮范围
     * @param asrText ASR文本内容
     * @param asrStartTime ASR文本起始时间戳
     * @param asrEndTime ASR文本结束时间戳
     * @param currentPlayTime 当前播放进度时间点
     * @return PlayHighlightRange 高亮范围对象
     */
    fun calculatePlayHighlightRange(
        asrText: String,
        asrStartTime: Long,
        asrEndTime: Long,
        currentPlayTime: Long
    ): PlayHighlightRange {
        if (asrText.isEmpty() || asrEndTime <= asrStartTime) {
            return PlayHighlightRange(0, 0, asrStartTime, asrEndTime)
        }
        // 计算当前播放时间对应的文本索引位置
        val baseIndex =  DatumPointUtil.calculateMarkFlagInsertPosition(
            asrText,
            asrStartTime,
            asrEndTime,
            currentPlayTime
        ).coerceIn(0, asrText.length - 1)
        var adjustedBaseIndex = baseIndex
        // 检查基准点是否是断句字符
        if (isPunctuationChar(asrText[baseIndex], asrText, baseIndex)) {
            // 向后查找非断句字符
            for (i in baseIndex + 1 until asrText.length) {
                if (!isPunctuationChar(asrText[i], asrText, i)) {
                    adjustedBaseIndex = i
                    break
                }
            }
            // 如果后面全是断句字符，向前查找
            if (adjustedBaseIndex == baseIndex) {
                for (i in baseIndex - 1 downTo 0) {
                    if (!isPunctuationChar(asrText[i], asrText, i)) {
                        adjustedBaseIndex = i
                        break
                    }
                }
            }
        }
        // 向前查找断句字符作为起始索引
        var startIndex = 0
        for (i in adjustedBaseIndex - 1 downTo 0) {
            if (isPunctuationChar(asrText[i], asrText, i)) {
                startIndex = i + 1
                break
            }
        }
        // 向后查找断句字符作为结束索引
        var endIndex = asrText.length - 1
        for (i in adjustedBaseIndex + 1 until asrText.length) {
            if (isPunctuationChar(asrText[i], asrText, i)) {
                endIndex = i
                break
            }
        }
        // 计算对应的时间范围
        val timeStart = if (startIndex == 0) {
            asrStartTime
        } else {
            asrStartTime + ((asrEndTime - asrStartTime) * startIndex / asrText.length.toFloat()).toLong()
        }
        val timeEnd = if (endIndex == asrText.length - 1) {
            asrEndTime
        } else {
            asrStartTime + ((asrEndTime - asrStartTime) * (endIndex + 1) / asrText.length.toFloat()).toLong()
        }
        return PlayHighlightRange(
            textStartIndex = startIndex,
            textEndIndex = endIndex,
            timeStart = timeStart,
            timeEnd = timeEnd
        )
    }

    /**
     * 判断字符是否为断句字符
     * @param c 要判断的字符
     * @param text 完整文本(用于上下文判断)
     * @param index 字符在文本中的索引
     * @return 是否为断句字符
     */
    private fun isPunctuationChar(c: Char, text: String, index: Int): Boolean {
        val punctuationRegex = if (isMarkMode.get()) { // 增加断句符号区分，确保线程安全读取，避免在多线程中出现异常
            MARK_MODE_PUNCTUATION_REGEX
        } else {
            NORMAL_MODE_PUNCTUATION_REGEX
        }
        // 如果不是断句符号直接返回false
        if (!c.toString().matches(punctuationRegex)) return false
        // 特殊处理英文句点(.)
        if (c == '.') {
            // 如果后面是数字，可能是小数点或IP地址
            if (index + 1 < text.length && text[index + 1].isDigit()) {
                return false
            }
            // 如果前面是数字，可能是小数点
            if (index > 0 && text[index - 1].isDigit()) {
                return false
            }
            // 如果后面没有字符或非数字，视为句号
            if (index + 1 >= text.length || text[index + 1].isDigit().not()) {
                return true
            }
        }
        return true
    }

    /**
     * 播放高亮范围数据类
     * @property textStartIndex 高亮文本起始索引
     * @property textEndIndex 高亮文本结束索引
     * @property timeStart 高亮时间段起始时间戳
     * @property timeEnd 高亮时间段结束时间戳
     */
    data class PlayHighlightRange(
        val textStartIndex: Int,
        val textEndIndex: Int,
        val timeStart: Long,
        val timeEnd: Long
    )
}