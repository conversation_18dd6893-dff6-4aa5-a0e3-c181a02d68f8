/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: EffectiveAnimationTextView
 * Description:
 * Version: 1.0
 * Date: 2022/9/29
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/9/29 1.0 create
 */

package com.soundrecorder.playback.newconvert.view

import android.animation.ValueAnimator
import android.content.Context
import android.content.res.Configuration
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.animation.addListener
import com.oplus.anim.EffectiveAnimationView
import com.oplus.anim.utils.EffectiveValueAnimator
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.common.widget.OSImageView
import com.soundrecorder.playback.R

class TransferAnimationTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0,
) : LinearLayout(context, attrs, defStyleAttr, defStyleRes) {

    private val mLogTag = "TransferAnimationTextView"
    private var mAnimView: EffectiveAnimationView? = null
    private var mTextView: TextView? = null

    private var mAnimRepeatRateStart: Int = -1
    private var mAnimRepeatRateEnd: Int = -1
    private var mAnimFrameRate: Int = -1
    private var mAnimRawResLight = 0
    private var mAnimRawResNight = 0
    private var mDefaultImageRes = 0
    private var mStartConvertAnimator: ValueAnimator? = null
    private var mEndConvertAnimator: ValueAnimator? = null
    private var mUIMode = 0

    init {
        val attributeArray = context.obtainStyledAttributes(attrs, R.styleable.TransferAnimationTextView, defStyleAttr, defStyleRes)
        mAnimFrameRate = attributeArray.getInteger(R.styleable.TransferAnimationTextView_animFrameRate, mAnimFrameRate)
        mAnimRepeatRateStart = attributeArray.getInteger(R.styleable.TransferAnimationTextView_animRepeatRateStart, mAnimRepeatRateStart)
        mAnimRepeatRateEnd = attributeArray.getInteger(R.styleable.TransferAnimationTextView_animRepeatRateEnd, mAnimRepeatRateEnd)
        mAnimRawResLight = attributeArray.getResourceId(R.styleable.TransferAnimationTextView_anim_rawRes_light, 0)
        mAnimRawResNight = attributeArray.getResourceId(R.styleable.TransferAnimationTextView_anim_rawRes_night, 0)
        mDefaultImageRes = attributeArray.getResourceId(R.styleable.TransferAnimationTextView_default_image_res, 0)
        val animViewWidth = attributeArray.getDimension(R.styleable.TransferAnimationTextView_animViewWidth,
            LayoutParams.WRAP_CONTENT.toFloat()).toInt()
        val animViewHeight = attributeArray.getDimension(R.styleable.TransferAnimationTextView_animViewHeight,
            LayoutParams.WRAP_CONTENT.toFloat()).toInt()

        val bottomTextContent = attributeArray.getString(R.styleable.TransferAnimationTextView_bottom_text)
        val bottomTextMarginTop = attributeArray.getDimension(R.styleable.TransferAnimationTextView_bottom_text_margin_top, 0F)
        val bottomTextPaddingBottom = attributeArray.getDimension(R.styleable.TransferAnimationTextView_bottom_text_paddingBottom, 0F)
        val bottomTextPaddingHorizontal = attributeArray.getDimension(R.styleable.TransferAnimationTextView_bottom_text_paddingHorizontal, 0F)
        val bottomTextMinWidthStyle = attributeArray.getBoolean(R.styleable.TransferAnimationTextView_bottom_text_minWidthStyle, false)
        attributeArray.recycle()

        mAnimView = EffectiveAnimationView(context).apply {
            // 用于中大屏下占位，避免切换闪烁
            this.setImageResource(mDefaultImageRes)
        }
        val style = if (bottomTextMinWidthStyle) {
            R.style.textView_playBack_bottomTool_minWidth_force_dark
        } else {
            R.style.textView_playBack_bottomTool_force_dark
        }
        mTextView = TextView(context, null, 0, style).apply {
            this.text = bottomTextContent
            this.setPadding(bottomTextPaddingHorizontal.toInt(), paddingTop, bottomTextPaddingHorizontal.toInt(), bottomTextPaddingBottom.toInt())
        }
        setAnimRawRes()
        addView(mAnimView, animViewWidth, animViewHeight)
        addView(mTextView, LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT).apply {
            topMargin = bottomTextMarginTop.toInt()
            gravity = Gravity.CENTER
        })
        orientation = VERTICAL
        gravity = Gravity.CENTER_HORIZONTAL
        mUIMode = context.resources.configuration.uiMode
        isForceDarkAllowed = false
    }

    private fun setAnimRawRes() {
        if (NightModeUtil.isNightMode(context)) {
            mAnimView?.setAnimation(mAnimRawResNight)
        } else {
            mAnimView?.setAnimation(mAnimRawResLight)
        }
    }

    fun setMaxWidth(maxWidth: Int) {
        mTextView?.maxWidth = maxWidth
    }

    private fun runStartTransferAnim(startFrame: Int = 0) {
        cancelAnimNotRunEndCallBack(mStartConvertAnimator)
        cancelAnimNotRunEndCallBack(mEndConvertAnimator)

        if (startFrame >= mAnimRepeatRateStart) {
            runRepeatAnim(mAnimRepeatRateStart, mAnimRepeatRateEnd)
            return
        }
        /*1.开始转写第一次从0开始播放至103F*/
        mStartConvertAnimator = genValueAnimator(startFrame, mAnimRepeatRateEnd, 0, ValueAnimator.RESTART)
        mStartConvertAnimator?.addListener(onEnd = {
            mStartConvertAnimator?.removeAllListeners()
            mStartConvertAnimator?.removeAllUpdateListeners()
            /*2.结束后，重复播放 19-103帧*/
            runRepeatAnim(mAnimRepeatRateStart, mAnimRepeatRateEnd)
        })
        mStartConvertAnimator?.start()
    }

    private fun runRepeatAnim(startFrame: Int, endFrame: Int) {
        /*2.然后循环播放19-103F*/
        mStartConvertAnimator = genValueAnimator(startFrame, endFrame, ValueAnimator.INFINITE, ValueAnimator.RESTART)
        mStartConvertAnimator?.start()
    }

    private fun runEndTransferAnim() {
        if (mEndConvertAnimator?.isRunning == true) {
            mEndConvertAnimator?.cancel()
        }
        val function = fun() {
            /*3.当转写完成时，循环完本轮播放动画后播放至结束128F*/
            val currentFrame = mAnimView?.frame ?: 0
            val maxFrame = mAnimView?.maxFrame ?: 0
            // 检查是否需要执行结束动画，若当前在第一帧或者最后一帧，不需要动画
            if (currentFrame > 0 && (currentFrame != maxFrame)) {
                mEndConvertAnimator = genValueAnimator(currentFrame, mAnimView?.maxFrame?.toInt() ?: 0, 0, ValueAnimator.RESTART)
                mEndConvertAnimator?.start()
            }
        }

        if ((mStartConvertAnimator?.isRunning == true) || (mStartConvertAnimator?.isStarted == true)) {
            mStartConvertAnimator?.repeatCount = 0
            mStartConvertAnimator?.removeAllListeners()
            mStartConvertAnimator?.removeAllUpdateListeners()
            mStartConvertAnimator?.addListener(onCancel = {
                function.invoke()
            })
            mStartConvertAnimator?.cancel()
        } else {
            function.invoke()
        }
    }

    private fun genValueAnimator(startFrame: Int, endFrame: Int, repeatCount: Int, repeatMode: Int): ValueAnimator? {
        if (startFrame >= endFrame) {
            DebugUtil.i(mLogTag, "startFrame >= endFrame ")
            return null
        }
        val animationDuration = getAnimationDuration(startFrame, endFrame, mAnimFrameRate)
        if (animationDuration <= 0) {
            DebugUtil.i(mLogTag, "animationDuration <= 0 ")
            return null
        }
        return EffectiveValueAnimator.ofInt(startFrame, endFrame).apply {
            duration = animationDuration
            this.repeatCount = repeatCount
            this.repeatMode = repeatMode
            addUpdateListener {
                mAnimView?.frame = it.animatedValue as Int
            }
        }
    }

    private fun getAnimationDuration(startFrame: Int, endFrame: Int, frameRate: Int): Long {
        DebugUtil.i(mLogTag, "getAnimationDuration,startFrame $startFrame, endFrame $endFrame,  frameRate$frameRate")
        if ((startFrame < 0) || (endFrame <= 0) || (frameRate <= 0)) {
            return 0
        }
        val offsetFrame = (endFrame - startFrame).toFloat()
        if (offsetFrame <= 0) {
            return 0
        }
        val duration = (offsetFrame / frameRate) * OSImageView.DURATION_ONE_SEC_WITH_MS
        return duration.toLong()
    }

    override fun onDetachedFromWindow() {
        DebugUtil.i(mLogTag, "onDetachedFromWindow>>>> ")
        super.onDetachedFromWindow()
        cancelAnimator()
    }

    fun cancelAnimator() {
        cancelAnimNotRunEndCallBack(mStartConvertAnimator)
        cancelAnimNotRunEndCallBack(mEndConvertAnimator)
        if (mAnimView?.isAnimating == true) {
            mAnimView?.cancelAnimation()
        }
        mStartConvertAnimator = null
        mEndConvertAnimator = null
    }

    override fun onVisibilityChanged(changedView: View, visibility: Int) {
        DebugUtil.i(mLogTag, "onVisibilityChanged,visibility $visibility ")
        super.onVisibilityChanged(changedView, visibility)
        if (mAnimView?.isAnimating == true) {
            if (visibility == VISIBLE) {
                mAnimView?.resumeAnimation()
            } else {
                mAnimView?.pauseAnimation()
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        super.onConfigurationChanged(newConfig)
        newConfig?.let {
            if (mUIMode != it.uiMode) {
                DebugUtil.i(mLogTag, "onConfigurationChanged mUIMode=$mUIMode, new ${it.uiMode}")
                mUIMode = it.uiMode
                setAnimRawRes()
                continuePlay()
            }
        }
    }

    private fun continuePlay() {
        mStartConvertAnimator?.let {
            if (it.isRunning || it.isStarted) {
                cancelAnimNotRunEndCallBack(it)
                runStartTransferAnim(mAnimView?.frame ?: 0)
            }
        }
        mEndConvertAnimator?.let {
            if (it.isRunning || it.isStarted) {
                cancelAnimNotRunEndCallBack(it)
                runEndTransferAnim()
            }
        }
    }

    private fun cancelAnimNotRunEndCallBack(anim: ValueAnimator?) {
        anim?.let {
            if (it.isRunning || it.isStarted) {
                it.repeatCount = 0
                it.removeAllListeners()
                it.removeAllUpdateListeners()
                it.cancel()
            }
        }
    }

    fun getText(): String {
        return mTextView?.text?.toString() ?:  ""
    }
}