/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackContainerTabMediator
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback

import android.view.ViewGroup
import com.soundrecorder.playback.newconvert.ui.ContentScrollHelper

class SummaryImmersiveHelper(containerFragment: PlaybackContainerFragment) {
    private val scrollHelper: ContentScrollHelper = ContentScrollHelper(
        containerFragment.view as? ViewGroup,
        containerFragment.mViewModel,
        null,
        null
    )

    fun setConvertImmersiveAnimationHelper(animationHelper: ConvertImmersiveAnimationHelper) {
        scrollHelper.setConvertImmersiveAnimationHelper(animationHelper)
    }

    fun updateIsImmersive(dy: Int) {
        scrollHelper.updateIsImmersiveState(dy)
    }
}