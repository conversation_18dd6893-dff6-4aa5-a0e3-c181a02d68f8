/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlayWaveItemView
 * Description:
 * Version: 1.0
 * Date: 2023/6/26
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/6/26 1.0 create
 */

package com.soundrecorder.playback.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.RectF
import android.util.AttributeSet
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.WaveViewUtil.getOneWaveLineTimeByWaveType
import com.soundrecorder.wavemark.wave.view.WaveItemView
import com.soundrecorder.wavemark.wave.view.drawNormalPlayAmp

class PlayWaveItemView(context: Context, attrs: AttributeSet?, defStyle: Int) : WaveItemView(context, attrs, defStyle) {

    companion object {
        private const val TAG = "PlayWaveItemView"
        private const val FAKE_AMPLITUDE: Int = 500
    }

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    override fun drawDottedLineWhenNoData(): Boolean {
        return true
    }

    override fun drawAmplitude(canvas: Canvas) {
        if (mViewIndex == 0) {
            drawFirstItemAmplitude(canvas)
            return
        }

        drawPlayAmplitude(canvas)
    }

    data class DrawPlayAmplitudeParams(
        var currentX: Float = 0f,
        var startX: Float = 0f,
        var amplitueIndex: Int = 0,
        var amplitueVaule: Int = 0,
        var preAmplitueVaule: Int = 0,
        var waveStartY: Int = 0,
        var waveEndY: Int = 0,
        var lineHeight: Float = 0f,
        val isRTL: Boolean,
        var isInDirectRecordTime: Boolean = false,
        var startTime: Long = -1L,
        var endTime: Long = -1L,
        var waveStartTime: Long = -1L
    )

    /**
     * 绘制播放波形振幅
     * 主要负责协调整个波形绘制流程，包括初始化参数、处理不同数据源、绘制波形柱和定向录音效果
     *
     * @param canvas 画布对象，用于绘制波形
     */
    private fun drawPlayAmplitude(canvas: Canvas) {
        // 初始化绘制参数
        val localVariables = initializeDrawParams()
        val viewWidth = width

        // 主绘制循环：遍历视图宽度范围内的所有波形柱位置
        while (localVariables.currentX <= viewWidth) {
            // 处理振幅数据获取，如果无法获取有效数据则跳过当前位置
            if (!processAmplitudeData(canvas, localVariables, viewWidth)) {
                continue
            }

            // 绘制单个振幅柱（包括普通波形和定向录音效果）
            localVariables.currentX = drawAmplitudeColumn(canvas, localVariables)
        }
    }

    /**
     * 初始化绘制参数
     * 设置波形绘制的起始位置、索引和时间等基础参数
     *
     * @return 初始化完成的绘制参数对象
     */
    private fun initializeDrawParams(): DrawPlayAmplitudeParams {
        val localVariables = DrawPlayAmplitudeParams(isRTL = isReverseLayout)

        // 波形实际上是从第二个item开始绘制（index = 1）
        localVariables.amplitueIndex = calculateAmpStartIndex()
        // startX 是第一个振幅数据在X轴上的起始绘制位置
        localVariables.startX = calculateAmpStartX(localVariables.amplitueIndex.toLong())
        localVariables.currentX = localVariables.startX
        localVariables.waveStartTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION

        // 清理已缓存的波形音柱开始绘制位置
        cachedAmpStartXList.clear()

        return localVariables
    }

    /**
     * 处理振幅数据获取逻辑
     * 根据不同的数据源（MP3书签、解码文件、无数据）获取当前位置的振幅值
     *
     * @param canvas 画布对象
     * @param localVariables 绘制参数对象
     * @param viewWidth 视图宽度
     * @return true表示成功获取到振幅数据，false表示需要跳过当前位置
     */
    private fun processAmplitudeData(canvas: Canvas, localVariables: DrawPlayAmplitudeParams, viewWidth: Int): Boolean {
        return when {
            // 优先处理来自MP3书签的振幅数据
            hasValidAmplitudes() -> processAmplitudesData(canvas, localVariables, viewWidth)
            // 其次处理来自解码音频文件的振幅数据
            hasValidDecodedAmplitudes() -> processDecodedAmplitudesData(canvas, localVariables, viewWidth)
            // 最后处理无振幅数据的情况
            else -> handleNoAmplitudeData(canvas, localVariables, viewWidth)
        }
    }

    /**
     * 检查是否有有效的MP3书签振幅数据
     *
     * @return true表示存在有效的振幅数据
     */
    private fun hasValidAmplitudes(): Boolean {
        return amplitudes != null && amplitudes.size > 0
    }

    /**
     * 检查是否有有效的解码振幅数据
     *
     * @return true表示存在有效的解码振幅数据
     */
    private fun hasValidDecodedAmplitudes(): Boolean {
        return decodedAmplitudeList != null && decodedAmplitudeList.size > 0
    }

    /**
     * 处理MP3书签振幅数据
     * 从MP3书签数据源获取当前位置的振幅值
     *
     * @param canvas 画布对象
     * @param localVariables 绘制参数对象
     * @param viewWidth 视图宽度
     * @return true表示成功处理数据，false表示需要绘制虚线并跳过
     */
    private fun processAmplitudesData(canvas: Canvas, localVariables: DrawPlayAmplitudeParams, viewWidth: Int): Boolean {
        return if (localVariables.amplitueIndex < amplitudes.size) {
            // 获取前一个振幅值，用于平滑波形过渡
            if (localVariables.amplitueIndex - 1 >= 0) {
                localVariables.preAmplitueVaule = amplitudes[localVariables.amplitueIndex - 1]
            } else {
                // 第一个振幅值时，前一个值设为0
                localVariables.preAmplitueVaule = 0
            }
            // 获取当前振幅值
            localVariables.amplitueVaule = amplitudes[localVariables.amplitueIndex]
            // 移动到下一个振幅索引
            localVariables.amplitueIndex++
            true
        } else {
            drawDottedLine(canvas, localVariables.isRTL, localVariables.currentX, viewWidth)
            localVariables.currentX += mVirtualAmpGap
            false
        }
    }

    /**
     * 处理解码振幅数据
     * 从解码音频文件数据源获取当前位置的振幅值，必要时使用假振幅数据
     *
     * @param canvas 画布对象
     * @param localVariables 绘制参数对象
     * @param viewWidth 视图宽度
     * @return true表示成功处理数据，false表示需要绘制虚线并跳过
     */
    private fun processDecodedAmplitudesData(canvas: Canvas, localVariables: DrawPlayAmplitudeParams, viewWidth: Int): Boolean {
        return if (localVariables.amplitueIndex < decodedAmplitudeList.size) {
            // 获取前一个振幅值，用于平滑波形过渡
            if (localVariables.amplitueIndex - 1 >= 0) {
                localVariables.preAmplitueVaule = decodedAmplitudeList[localVariables.amplitueIndex - 1]
            } else {
                // 第一个振幅值时，前一个值设为0
                localVariables.preAmplitueVaule = 0
            }

            // 获取当前振幅值
            localVariables.amplitueVaule = decodedAmplitudeList[localVariables.amplitueIndex]

            // 移动到下一个振幅索引
            localVariables.amplitueIndex++
            true
        } else {
            val usedFakeAmplitude = calculateFakeAmplitude(localVariables)
            if (usedFakeAmplitude) {
                true
            } else {
                drawDottedLine(canvas, localVariables.isRTL, localVariables.currentX, viewWidth)
                localVariables.currentX += mVirtualAmpGap
                false
            }
        }
    }

    /**
     * 处理无振幅数据的情况
     * 当没有任何振幅数据时，根据配置决定是否绘制默认虚线
     *
     * @param canvas 画布对象
     * @param localVariables 绘制参数对象
     * @param viewWidth 视图宽度
     * @return true表示成功处理，false表示需要跳过当前位置
     */
    private fun handleNoAmplitudeData(canvas: Canvas, localVariables: DrawPlayAmplitudeParams, viewWidth: Int): Boolean {
        if (!drawDottedLineWhenNoData()) {
            DebugUtil.d(TAG, "---drawAmplitude no waveform data---")
            // 直接结束绘制，通过设置currentX超出视图宽度来终止循环
            localVariables.currentX = viewWidth.toFloat() + 1
            return false
        }

        // 绘制默认虚线
        localVariables.waveStartY = getStartYByHeight(mDottedLineHeight)
        localVariables.waveEndY = getEndYByHeight(mDottedLineHeight)
        canvas.drawLine(
            localVariables.currentX,
            localVariables.waveStartY.toFloat(),
            localVariables.currentX,
            localVariables.waveEndY.toFloat(),
            mHorizontalDashPaint
        )
        localVariables.currentX += mVirtualAmpGap
        return false
    }

    /**
     * 绘制单个振幅柱
     * 包括计算波形高度、绘制普通波形和定向录音效果
     *
     * @param canvas 画布对象
     * @param localVariables 绘制参数对象
     * @return 下一个绘制位置的X坐标
     */
    private fun drawAmplitudeColumn(canvas: Canvas, localVariables: DrawPlayAmplitudeParams): Float {
        // 计算波形的高度（基于当前和前一个振幅值的平均值）
        localVariables.lineHeight = getWaveLineHeight(localVariables.preAmplitueVaule, localVariables.amplitueVaule)

        // 绘制普通波形柱
        val nextCurrentX = this.drawNormalPlayAmp(canvas, localVariables.currentX, localVariables.lineHeight)

        // 绘制定向录音音柱（如果启用）
        drawEnhanceAmplitude(localVariables, canvas)

        return nextCurrentX
    }

    /**
     * 计算假振幅数据
     * 在解码数据不足但仍在总时长范围内时，生成随机假振幅数据以保持波形连续性
     *
     * @param localVariables 绘制参数对象
     * @return true表示使用了假振幅数据，false表示未使用
     */
    private fun calculateFakeAmplitude(localVariables: DrawPlayAmplitudeParams): Boolean {
        var usedFakeAmplitude = false
        val oneWaveLineTime = getOneWaveLineTimeByWaveType(context, mWaveType)
        val decodeIsBeforeTotal = oneWaveLineTime * decodedAmplitudeList.size < mTotalTime
        val nowIsBeforeTotal = oneWaveLineTime * localVariables.amplitueIndex < mTotalTime

        // 当解码数据不足但当前时间仍在总播放时长内时，使用假振幅数据
        if (decodeIsBeforeTotal && nowIsBeforeTotal) {
            // 临时使用假振幅数据，用于修复bug 1646242
            DebugUtil.d(
                TAG, ("In this case, we temporary use fake amplitude, amp : "
                        + ", mPlayTotalTime: " + mTotalTime + ", decodedAmplitudeList size : "
                        + decodedAmplitudeList.size)
            )
            usedFakeAmplitude = true
            // 生成随机假振幅值，保持波形的视觉连续性
            localVariables.amplitueVaule = (FAKE_AMPLITUDE * Math.random()).toInt()
            localVariables.preAmplitueVaule = (FAKE_AMPLITUDE * Math.random()).toInt()

            // 如果是最后一个视图项，需要递增振幅索引
            if (mViewIndex == mTotalCount - 1) {
                localVariables.amplitueIndex++
            }
        }
        return usedFakeAmplitude
    }

    /**
     * 绘制定向录音音柱
     * @param localVariables 当前绘制的参数
     * @param canvas 画布对象
     */
    private fun drawEnhanceAmplitude(
        localVariables: DrawPlayAmplitudeParams,
        canvas: Canvas
    ) {
        if (isEnhance && directTimeList?.isNotEmpty() == true) {
            val currentTime = localVariables.waveStartTime + ((localVariables.currentX - localVariables.startX) / pxPerMs).toLong()
            val isNeedInit = (!localVariables.isInDirectRecordTime && localVariables.startTime == -1L)
            val isNeedUpdate = (!localVariables.isInDirectRecordTime && currentTime >= localVariables.startTime)
                        || (localVariables.isInDirectRecordTime && currentTime > localVariables.endTime)
            if (isNeedInit || isNeedUpdate) {
                DebugUtil.d(TAG, "drawPlayAmplitude isNeedInit=$isNeedInit, isNeedUpdate=$isNeedUpdate currentTime=$currentTime" +
                            ", startTime=${localVariables.startTime}, endTime=${localVariables.endTime}")
                val isInDirectRecordTimeRange = isInDirectRecordTimeRange(currentTime)
                localVariables.isInDirectRecordTime = isInDirectRecordTimeRange.first
                localVariables.startTime = isInDirectRecordTimeRange.second
                localVariables.endTime = isInDirectRecordTimeRange.third
                DebugUtil.i(TAG, "drawPlayAmplitude isInDirectRecordTime=${localVariables.isInDirectRecordTime}" +
                        ", startTime=${localVariables.startTime}, endTime=${localVariables.endTime}")
            }
            if (localVariables.isInDirectRecordTime && currentTime in localVariables.startTime..localVariables.endTime) {
                drawEnhanceAmpColumn(canvas, localVariables.currentX, localVariables.lineHeight)
            }
        }
    }

    /**
     * 获取指定时间点是否在定向录音范围
     * @param time 要检查的时间点
     * @return Triple<Boolean, Long, Long> 第一个元素表示是否在定向录音时间段内
     * 若第一个元素为 true，则第二个和第三个元素表示定向录音时间段的起始和结束时间
     * 若第一个元素为 false，则第二个和第三个元素表示当前时间点的后面一段定向录音时间段的起始和结束时间
     */
    private fun isInDirectRecordTimeRange(time: Long): Triple<Boolean, Long, Long> {
        // 若当前时间在定向录音时间段内，则返回true，并返回定向录音时间段
        directTimeList?.forEach { directTime ->
            if (time >= directTime.startTime && time <= directTime.endTime) {
                return Triple(true, directTime.startTime, directTime.endTime)
            }
        }
        // 计算当前时间点的后面一段定向录音时间段
        directTimeList?.forEach { directTime ->
            if (time < directTime.startTime) {
                return Triple(false, directTime.startTime, directTime.endTime)
            }
        }
        // 当前时间点后面没有定向录音时间段，则返回false，并返回 0，0
        return Triple(false, 0, 0)
    }

    /**
     * 绘制单个定向录音音柱
     * @param canvas 画布对象
     * @param currentX 音柱的X坐标位置
     * @param lineHeight 音柱的高度
     */
    private fun drawEnhanceAmpColumn(canvas: Canvas, currentX: Float, lineHeight: Float) {
        val waveStartY = getStartYByHeight(lineHeight)
        val waveEndY = getEndYByHeight(lineHeight)
        var newCurrentX = currentX
        var newCurrentXLatter = currentX + mDirectAmpWidth
        // 处理RTL布局
        if (isReverseLayout) {
            newCurrentX = width - currentX
            newCurrentXLatter = width - currentX - mDirectAmpWidth
        }

        // 使用定向录音专用画笔绘制音柱
        mEnhanceAmplitudePaint?.let { paint ->
            val ampRectF = RectF(newCurrentX, waveStartY.toFloat(), newCurrentXLatter, waveEndY.toFloat())
            canvas.drawRoundRect(ampRectF, mAmplitudeWidth, mAmplitudeWidth, paint)
        }
    }
}