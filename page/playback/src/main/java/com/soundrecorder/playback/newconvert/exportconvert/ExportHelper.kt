/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_PDF
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_TXT
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_WORD
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_SHARE_AUDIO
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_SHARE_DOCUMENT
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_SHARE_LINK
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_SHARE_SUMMARY
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.ConvertStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.buryingpoint.ShareStatisticsUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.dialog.LoadingDialog
import com.soundrecorder.common.exportfile.ExportDoc
import com.soundrecorder.common.exportfile.ExportPdf
import com.soundrecorder.common.exportfile.ExportSummaryData
import com.soundrecorder.common.exportfile.SummaryContent
import com.soundrecorder.common.exportfile.SummaryContentViewUtil
import com.soundrecorder.common.exportfile.ui.ExportTipsManager
import com.soundrecorder.common.share.OShareConvertUtil
import com.soundrecorder.common.share.ShareSupportHelper
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.common.share.ShareTypeCopy
import com.soundrecorder.common.share.ShareTypeDoc
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.common.share.ShareTypeNote
import com.soundrecorder.common.share.ShareTypeRecorderAndText
import com.soundrecorder.common.utils.amplitude.AmpFileUtil
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.convertservice.convert.NewConvertResultUtil
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.newconvert.convert.ConvertManagerImpl
import com.soundrecorder.summary.exportfile.ExportTxt
import com.soundrecorder.summary.ui.content.SummaryExportToNote
import com.soundrecorder.summary.ui.content.SummaryExportToNote.ContentViewParams
import com.soundrecorder.summary.ui.content.SummaryExportToNote.ExportRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import java.lang.ref.WeakReference
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class ExportHelper(private var convertManagerImpl: ConvertManagerImpl?, private var exportHelperListener: ExportHelperListener?) {

    companion object {
        private const val TAG = "ExportHelper"
        private const val LOADING_TIPS_DELAY = 1000L
        const val DOC_SUFFIX = "doc"
        const val PDF_SUFFIX = "pdf"
        const val TXT_SUFFIX = "txt"
    }
    private var mActivity = convertManagerImpl?.activity
    private var mMediaRecordId = convertManagerImpl?.playbackConvertViewModel?.mediaRecordId ?: 0
    private var mLoadingDialog: LoadingDialog? = null
    private val mMainHandler = Handler(Looper.getMainLooper())
    private var mTask: Runnable? = null
    private val mRecord by lazy {
        convertManagerImpl?.playbackConvertViewModel?.mRecord
    }
    private val shareSupportHelper: ShareSupportHelper by lazy {
        ShareSupportHelper()
    }
    private val shareAction by lazy {
        Injector.injectFactory<ShareAction>()
    }

    private val summaryAction by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    private val exportTipsManager: ExportTipsManager by lazy { ExportTipsManager() }

    private val lifecycle = mActivity?.lifecycleScope

    fun getExportDialogIsShowing(): Boolean {
        return shareSupportHelper.getExportDialogIsShowing()
    }

    fun getExportDialogSupportFollow(): Boolean {
        return shareSupportHelper.getExportDialogSupportFollow()
    }

    fun getExportTextDialogIsShowing(): Boolean {
        return shareSupportHelper.getExportTextDialogIsShowing()
    }

    fun getExportTextDialogSupportFollow(): Boolean {
        return shareSupportHelper.getExportTextDialogSupportFollow()
    }

    fun release() {
        convertManagerImpl = null
        mActivity = null
        exportHelperListener = null
        removeTask()
        mTask = null
        shareSupportHelper.release()
        exportTipsManager.release()
    }

    private fun showProgress(titleResId: Int) {
        mActivity?.let {
            if ((mLoadingDialog == null)) {
                mLoadingDialog = LoadingDialog(it)
            }
            //修复loading dialog只显示一次的问题
            if (mLoadingDialog?.isActivityNull() == true) {
                mLoadingDialog?.resetActivity(it)
            }
            mLoadingDialog?.show(titleResId, false)
        }
    }

    private fun hideProgress() {
        mLoadingDialog?.dismiss()
    }

    fun isShareDialogShowing(): Boolean = shareSupportHelper.getExportDialogIsShowing()

    fun isShareTextDialogShowing(): Boolean = shareSupportHelper.getExportTextDialogIsShowing()

    fun isShareSummaryDialogShowing(): Boolean = shareSupportHelper.getExportSummaryDialogIsShowing()

    fun shareType(): Int = shareSupportHelper.shareType()

    fun showExportDialog(
        anchor: View?,
        dismissListener: DialogInterface.OnDismissListener?,
        fragment: Fragment? = null
    ) {
        val act = mActivity ?: return
        act.lifecycleScope.launch(Dispatchers.IO) {
            val summaryEntity = summaryAction?.getSummaryContent(act, mMediaRecordId) ?: return@launch
            val summaryContent = summaryEntity.first

            val hasConvertContent = (convertManagerImpl?.getConvertViewController()?.getViewModel()?.hasConvertContent?.value == true)
            withContext(Dispatchers.Main) {
                val hasSummary = summaryContent.isNotEmpty()
                if (isCanShowDialog(fragment)) {
                    showShareDialog(anchor, dismissListener, fragment?.childFragmentManager, hasSummary, hasConvertContent, summaryContent)
                }
            }
        }
    }

    private fun isCanShowDialog(fragment: Fragment?): Boolean {
        var result = false
        fragment?.let {
            it.activity?.let { mFragmentActivity ->
                result = it.isAdded && !mFragmentActivity.isFinishing && !mFragmentActivity.isDestroyed
            }
        }
        return result
    }

    private fun showShareDialog(
        anchor: View?,
        dismissListener: DialogInterface.OnDismissListener?,
        childFragmentManager: FragmentManager? = null,
        hasSummary: Boolean,
        hasConvertContent: Boolean,
        summaryContent: String?,
    ) {
        shareSupportHelper.showShareDialog(mActivity, anchor, dismissListener, childFragmentManager, hasSummary, hasConvertContent) { type ->
            when (type) {
                ShareSupportHelper.SHARE_TYPE_LINK -> {
                    if (!hasConvertContent) {
                        ShareStatisticsUtil.addClickShareTypeEvent(ShareStatisticsUtil.VALUE_LINK_SHARE)
                        mActivity?.let { shareLink(it, true, false, false, summaryContent) }
                        AISummaryBuryingUtil.addShareSummaryEvent(mMediaRecordId.toString(), VALUE_SHARE_LINK, "")
                    } else {
                        showExportLinkDialogTow(hasConvertContent, hasSummary, summaryContent, childFragmentManager)
                    }
                }

                ShareSupportHelper.SHARE_TYPE_AUDIO -> {
                    BuryingPoint.addSelectMoreRecordToShare(RecorderUserAction.VALUE_SHARE_RECORDING)
                    ShareStatisticsUtil.addClickShareTypeEvent(ShareStatisticsUtil.VALUE_AUDIO_SHARE)
                    FileDealUtil.sendRecordFile(mActivity, mMediaRecordId, anchor)
                    AISummaryBuryingUtil.addShareSummaryEvent(mMediaRecordId.toString(), VALUE_SHARE_AUDIO, "")
                }

                ShareSupportHelper.SHARE_TYPE_TEXT -> {
                    BuryingPoint.addSelectMoreRecordToShare(RecorderUserAction.VALUE_SHARE_TEXTING)
                    ShareStatisticsUtil.addClickShareTypeEvent(ShareStatisticsUtil.VALUE_TEXT_SHARE)
                    showExportTextDialogTow(ShareSupportHelper.SHARE_TEXT_TYPE, childFragmentManager)
                }

                ShareSupportHelper.SHARE_TYPE_SUMMARY -> showExportSummaryDialog(ShareSupportHelper.SHARE_SUMMARY_TYPE, childFragmentManager)
            }
        }
        ConvertStaticsUtil.addClickExport(RecorderUserAction.VALUE_EXPORT_CLICK_EXPORT_COMPLETE)
    }

    private fun generateExportTitle(context: Context, title: String, recordTitle: String): String {
        if (recordTitle.isNotEmpty()) {
            return recordTitle.title() ?: run {
                val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                context.getString(com.soundrecorder.common.R.string.summary) + "$timestamp"
            }
        }
        return title
    }

    private fun removeTask() {
        mTask?.let { mMainHandler.removeCallbacks(it) }
    }

    fun restoreShareTextDialog(childFragmentManager: FragmentManager, selectShareType: Int) {
        showExportTextDialogTow(ShareSupportHelper.SHARE_TEXT_TYPE, childFragmentManager, selectShareType)
    }

    fun restoreShareSummaryDialog(childFragmentManager: FragmentManager, selectShareType: Int) {
        showExportSummaryDialog(ShareSupportHelper.SHARE_SUMMARY_TYPE, childFragmentManager, selectShareType)
    }

    private fun showExportSummaryDialog(shareType: Int, childFragmentManager: FragmentManager? = null, selectShareType: Int = -1) {
        val act = mActivity ?: return
        shareSupportHelper.showShareSummaryDialog(false, act, childFragmentManager, selectShareType) { type ->
            when (type) {
                ShareSupportHelper.SHARE_TYPE_TEXT_TO_DOC -> {
                    showProgress(com.soundrecorder.common.R.string.generating)
                    removeTask()
                    mTask = Runnable {
                        AISummaryBuryingUtil.addShareSummaryEvent(mMediaRecordId.toString(), VALUE_SHARE_SUMMARY, VALUE_FORMAT_WORD)
                        startSaveFile(DOC_SUFFIX, shareType)
                    }
                    mTask?.let { mMainHandler.postDelayed(it, LOADING_TIPS_DELAY) }
                }

                ShareSupportHelper.SHARE_TYPE_ONLY_TEXT -> {
                    showProgress(com.soundrecorder.common.R.string.generating)
                    removeTask()
                    mTask = Runnable {
                        AISummaryBuryingUtil.addShareSummaryEvent(mMediaRecordId.toString(), VALUE_SHARE_SUMMARY, VALUE_FORMAT_TXT)
                        startSaveFile(TXT_SUFFIX, shareType)
                    }
                    mTask?.let { mMainHandler.postDelayed(it, LOADING_TIPS_DELAY) }
                }

                ShareSupportHelper.SHARE_TYPE_TEXT_TO_PDF -> {
                    showProgress(com.soundrecorder.common.R.string.generating)
                    removeTask()
                    mTask = Runnable {
                        AISummaryBuryingUtil.addShareSummaryEvent(mMediaRecordId.toString(), VALUE_SHARE_SUMMARY, VALUE_FORMAT_PDF)
                        startSaveFile(PDF_SUFFIX, shareType)
                    }
                    mTask?.let { mMainHandler.postDelayed(it, LOADING_TIPS_DELAY) }
                }

                ShareSupportHelper.SHARE_TYPE_TEXT_OR_SUMMARY_TO_NOTE -> {
                    shareSummaryToNote(act)
                }
            }
        }
    }

    private fun startSaveFile(fileType: String, shareType: Int) {
        val act = mActivity ?: return
        act.lifecycleScope.launch(Dispatchers.IO) {
            kotlin.runCatching {
                val summaryEntity = summaryAction?.getSummaryContent(act, mMediaRecordId) ?: return@launch
                val summaryContent = summaryEntity.first
                val summaryTitle = generateExportTitle(act, summaryEntity.second, (mRecord?.displayName ?: ""))
                val exportFilePath = SummaryContentViewUtil.generateExportFilePath(act, fileType, summaryTitle)

                when (fileType) {
                    DOC_SUFFIX -> {
                        ExportDoc.saveToWord(
                            BaseApplication.getAppContext(), exportFilePath, ExportSummaryData(
                                summaryTitle, SummaryContent(summaryContent, listOf()), listOf()))
                    }
                    TXT_SUFFIX -> ExportTxt.saveReport(exportFilePath, summaryContent)
                    PDF_SUFFIX -> {
                        ExportPdf.saveToPdf(
                            BaseApplication.getAppContext(), exportFilePath, ExportSummaryData(
                                summaryTitle, SummaryContent(summaryContent, listOf()), listOf()))
                    }
                    else -> DebugUtil.d(TAG, "startSaveFile other")
                }
                withContext(Dispatchers.Main) {
                    startShareWithTxtActivity(exportFilePath, fileType, shareType)
                }
            }
        }
    }

    private fun showExportTextDialogTow(shareType: Int, childFragmentManager: FragmentManager? = null, selectShareType: Int = -1) {
        val act = mActivity ?: return
        shareSupportHelper.showShareSummaryDialog(true, act, childFragmentManager, selectShareType) { type ->
            when (type) {
                //转文本时不需要传 summaryFilePath 和fileType 的参数 所以为null
                ShareSupportHelper.SHARE_TYPE_ONLY_TEXT -> {
                    showProgress(com.soundrecorder.common.R.string.generating)
                    removeTask()
                    mTask = Runnable {
                        startShareWithTxtActivity("", "", shareType)
                        AISummaryBuryingUtil.addShareSummaryEvent(mMediaRecordId.toString(), VALUE_SHARE_DOCUMENT, VALUE_FORMAT_TXT)
                    }
                    mTask?.let { mMainHandler.postDelayed(it, LOADING_TIPS_DELAY) }
                }

                ShareSupportHelper.SHARE_TYPE_TEXT_TO_DOC -> {
                    showProgress(com.soundrecorder.common.R.string.generating)
                    removeTask()
                    mTask = Runnable {
                        ConvertStaticsUtil.addExportDoc()
                        shareToDoc(act)
                        AISummaryBuryingUtil.addShareSummaryEvent(mMediaRecordId.toString(), VALUE_SHARE_DOCUMENT, VALUE_FORMAT_WORD)
                    }
                    mTask?.let { mMainHandler.postDelayed(it, LOADING_TIPS_DELAY) }
                }

                ShareSupportHelper.SHARE_TYPE_TEXT_OR_SUMMARY_TO_NOTE -> {
                    showProgress(com.soundrecorder.common.R.string.is_saving_talk_back)
                    removeTask()
                    mTask = Runnable {
                        shareToNote(act)
                    }
                    mTask?.let { mMainHandler.postDelayed(it, LOADING_TIPS_DELAY) }
                }
            }
        }
    }

    private fun showExportLinkDialogTow(
        isShowTxt: Boolean,
        isShowSummary: Boolean,
        summaryContent: String?,
        childFragmentManager: FragmentManager? = null
    ) {
        val act = mActivity ?: return
        shareSupportHelper.showShareLinkDialog(isShowTxt, isShowSummary, act, childFragmentManager) { isSelectAudio, isSelectTXT, isSelectSummary ->
            DebugUtil.i(TAG, "showExportLinkDialogTow isShowTxt = $isShowTxt, " +
                    "isShowSummary = $isShowSummary, " +
                    "isSelectAudio = $isSelectAudio, " +
                    "isSelectTXT = $isSelectTXT, " +
                    "isSelectSummary = $isSelectSummary")
            ShareStatisticsUtil.addClickShareTypeEvent(ShareStatisticsUtil.VALUE_LINK_SHARE)
            mActivity?.let { shareLink(it, isSelectAudio, isSelectTXT, isSelectSummary, summaryContent) }
            AISummaryBuryingUtil.addShareSummaryEvent(mMediaRecordId.toString(), VALUE_SHARE_LINK, "")
        }
    }

    private fun shareToDoc(activity: AppCompatActivity) {
        hideProgress()
        if (shareSupportHelper.isSupportWPS()) {
            checkNeedShowDocOnlySupportTextDialog(activity)
        } else {
            showWpsGuideDialog(activity)
        }
    }

    private fun shareLink(
        activity: AppCompatActivity,
        isSelectAudio: Boolean,
        isSelectTXT: Boolean,
        isSelectSummary: Boolean,
        summaryContent: String?
    ) {
        DebugUtil.d(TAG, "shareLink isSelectAudio = $isSelectAudio isSelectTXT = $isSelectTXT isSelectSummary = $isSelectSummary")
        convertManagerImpl?.playbackConvertViewModel?.viewModelScope?.let { scope ->
            scope.launch(Dispatchers.Main) {
                if (NetworkUtils.isNetworkInvalid(activity)) {
                    ToastManager.showShortToast(activity, com.soundrecorder.common.R.string.no_network_unable_generate_share_link)
                    return@launch
                }
                var record = mRecord
                if (record == null) {
                    withContext(Dispatchers.IO) {
                        record = MediaDBUtils.queryRecordById(mMediaRecordId)
                    }
                }
                if (record == null) {
                    DebugUtil.e(TAG, "shareLink record is null, mMediaRecordId=$mMediaRecordId")
                    ToastManager.showShortToast(activity, com.soundrecorder.common.R.string.link_generation_failure_retry)
                    return@launch
                }
                if (AmpFileUtil.isFileSizeLargerThanOrEqualToLimit(record?.fileSize, AmpFileUtil.MAX_UPLOAD_AUDIO_FILE_SIZE_MB)) {
                    val fileOverLimitString = activity.resources.getString(
                        com.soundrecorder.common.R.string.files_over_200M_unable_generate_share_link,
                        AmpFileUtil.MAX_UPLOAD_AUDIO_FILE_SIZE_MB
                    )
                    ToastManager.showShortToast(activity, fileOverLimitString)
                    return@launch
                }
                if (false == shareAction?.canUploadMoreAudioFiles()) {
                    ToastManager.showShortToast(activity, com.soundrecorder.common.R.string.uploading_last_audio_retry)
                    return@launch
                }
                if (isSelectAudio) {
                    record?.setmHasRecordFile(record?.id ?: -1L)
                } else {
                    record?.setmHasRecordFile(-1L)
                }
                record?.setmHasRecordCovertItems(isSelectTXT)
                if (isSelectSummary) {
                    record?.setmHasRecordSummary(summaryContent)
                } else {
                    record?.setmHasRecordSummary("")
                }
                var shareTextContent = getShareTextData()
                if (shareTextContent == null) {
                    DebugUtil.e(TAG, "shareLink shareTextContent is null")
                    shareTextContent = ShareTextContent(record?.id ?: -1L, false, "", -1L, arrayListOf())
                }
                convertManagerImpl?.getConvertViewController()?.getViewModel()
                    ?.let { shareAction?.registerShareListener(it) }
                shareAction?.share(activity, shareTextContent, ShareTypeLink("", record), scope,
                    convertManagerImpl?.getConvertViewController()?.getViewModel())
            }
        }
    }


    private fun showWpsGuideDialog(activity: Activity) {
        shareSupportHelper.showInstallWpsGuideDialog(activity) { isAgree ->
            if (isAgree) {
                //暂停播放
                convertManagerImpl?.getConvertViewController()
                    ?.getViewModel()?.playerController?.pausePlay()
            }
        }
        ConvertStaticsUtil.addShowInstallWpsDialogEvent()
    }

    private fun excuteDocByOldWay(activity: AppCompatActivity) {
        executeExportFormatDoc(activity, convertManagerImpl?.getConvertViewController()?.getViewModel())
        ConvertStaticsUtil.addExportDocEvent()
    }

    private fun checkNeedShowDocOnlySupportTextDialog(activity: AppCompatActivity) {
        val hasImageMark = hasImg()
        if (hasImageMark && FunctionOption.IS_SUPPORT_MIX_EXPORT_DOC) {
            shareSupportHelper.showDocOnlySupportTextDialog(activity) { isAgree ->
                if (isAgree) {
                    if (shareSupportHelper.isSupportWPS()) {
                        excuteDocByOldWay(activity)
                    } else {
                        showWpsGuideDialog(activity)
                    }
                }
            }
        } else {
            excuteDocByOldWay(activity)
        }
    }

    private fun shareToNote(activity: AppCompatActivity) {
        hideProgress()
        convertManagerImpl?.playbackConvertViewModel?.viewModelScope?.let { scope ->
            scope.launch(Dispatchers.Main) {
                if (!shareSupportHelper.isNoteInstalled()) {
                    return@launch
                }
                if (!shareSupportHelper.isNoteEnabled(activity)) {
                    return@launch
                }
                if (!FunctionOption.IS_SUPPORT_SAVE_TO_NOTE_MIX) {
                    shareToNoteOnlyText(activity)
                    return@launch
                }
                if (shareSupportHelper.isNoteSupportImg(activity)) {
                    val shareTextContent = getShareTextData()
                    if (shareTextContent == null) {
                        DebugUtil.e(TAG, "shareToNote shareTextContent is null")
                        return@launch
                    }
                    if (shareTextContent.textContentItems.isEmpty()) {
                        DebugUtil.e(TAG, "shareToNote convertContentList is null or empty")
                        return@launch
                    }
                    shareAction?.share(
                        activity,
                        shareTextContent,
                        ShareTypeNote(true),
                        scope,
                        convertManagerImpl?.getConvertViewController()?.getViewModel()
                    )
                } else {
                    if (hasImg()) {
                        shareSupportHelper.showNoteNotSupportImgDialog(activity) { isAgree ->
                            if (isAgree) {
                                shareToNoteOnlyText(activity)
                            }
                        }
                    } else {
                        shareToNoteOnlyText(activity)
                    }
                }
            }
        }
    }

    private fun shareSummaryToNote(activity: AppCompatActivity) {
        hideProgress()
        // 安全获取record对象
        val record = mRecord
        if (record != null) {
            val recordTitle = record.displayName?.title() ?: ""
            val recordFilePath = record.data ?: ""
            DebugUtil.d(TAG, "shareToNote: Starting export - recordTitle=$recordTitle, recordFilePath=$recordFilePath")
            // 执行摘要导出
            runCatching {
                val exportRequest = ExportRequest(activity, mMediaRecordId, recordTitle, recordFilePath)
                // 获取Activity的根View作为SnackBar容器
                val containerView = activity.findViewById<View>(android.R.id.content)
                containerView?.let {
                    val contentViewParams = ContentViewParams(
                        lifecycle ?: activity.lifecycleScope,
                        exportTipsManager,
                        WeakReference(it)
                    )
                    SummaryExportToNote.exportSummaryToNoteFromContentView(exportRequest, contentViewParams)
                }
            }.onFailure { exception ->
                DebugUtil.e(TAG, "shareToNote error: ${exception.message}")
            }
        }
    }

    private fun hasImg(): Boolean {
        convertManagerImpl?.playbackConvertViewModel?.convertContentData?.let { data ->
            for (i in data.indices) {
                data[i].mTextOrImageItems?.forEach {
                    if (it is ConvertContentItem.ImageMetaData) {
                        return true
                    }
                }
            }
        }
        return false
    }

    private fun executeExportFormatDoc(
        activity: AppCompatActivity,
        listener: IShareListener?
    ) {
        convertManagerImpl?.playbackConvertViewModel?.viewModelScope?.let { scope ->
            scope.launch(Dispatchers.Main) {
                val shareTextContent = getShareTextData()
                if (shareTextContent == null) {
                    DebugUtil.e(TAG, "executeExportFormatDoc shareTextContent is null")
                    return@launch
                }
                if (shareTextContent.textContentItems.isEmpty()) {
                    DebugUtil.e(TAG, "executeExportFormatDoc convertContentList is null or empty")
                    return@launch
                }
                shareAction?.share(
                    activity,
                    shareTextContent,
                    ShareTypeDoc(
                        convertManagerImpl?.playbackConvertViewModel?.mShowSwitchInConvertModel?.value == true
                    ),
                    scope,
                    listener
                )
            }
        }
    }

    private fun executeAsyncClipboard(activity: AppCompatActivity?) {
        convertManagerImpl?.playbackConvertViewModel?.viewModelScope?.let { scope ->
            scope.launch(Dispatchers.Main) {
                val shareTextContent = getShareTextData()
                if (shareTextContent == null) {
                    DebugUtil.e(TAG, "executeAsyncClipboard shareTextContent is null")
                    return@launch
                }
                if (shareTextContent.textContentItems.isEmpty()) {
                    DebugUtil.e(TAG, "executeAsyncClipboard convertContentList is null or empty")
                    return@launch
                }
                shareAction?.share(
                    activity,
                    shareTextContent,
                    ShareTypeCopy,
                    scope,
                    convertManagerImpl?.getConvertViewController()?.getViewModel()
                )
                ConvertStaticsUtil.addExportCopyEvent()
            }
        }
    }

    /**
     * 根据版本开关要求要求，决定是去预览页面还是直接分享转文本内容
     */
    private fun startShareWithTxtActivity(summaryFilePath: String, fileType: String, shareType: Int) {
        hideProgress()
        ConvertStaticsUtil.addExportTxtEvent()
        exportHelperListener?.startWithSharedTextActivity(summaryFilePath, mMediaRecordId, shareType, fileType)
    }

    /**
     * 保存至便签（仅支持文本）
     */
    private fun shareToNoteOnlyText(activity: AppCompatActivity?) {
        convertManagerImpl?.playbackConvertViewModel?.viewModelScope?.let { scope ->
            scope.launch(Dispatchers.Main) {
                val shareTextContent = getShareTextData()
                if (shareTextContent == null) {
                    DebugUtil.e(TAG, "shareToNoteOnlyText shareTextContent is null")
                    return@launch
                }
                if (shareTextContent.textContentItems.isEmpty()) {
                    DebugUtil.e(TAG, "shareToNoteOnlyText convertContentList is null or empty")
                    return@launch
                }
                shareAction?.share(
                    activity,
                    shareTextContent,
                    ShareTypeNote(false),
                    scope,
                    convertManagerImpl?.getConvertViewController()?.getViewModel()
                )
                ConvertStaticsUtil.addSendToNote()
            }
        }
    }

    private fun shareTextAndAudio(anchor: View?) {
        if (mActivity == null) {
            DebugUtil.i(TAG, "shareTextAndAudio mActivity is null")
            return
        }
        convertManagerImpl?.playbackConvertViewModel?.viewModelScope?.let { scope ->
            scope.launch(Dispatchers.Main) {
                val shareTextContent = getShareTextData()
                if (shareTextContent == null) {
                    DebugUtil.e(TAG, "executeAsyncClipboard shareTextContent is null")
                    return@launch
                }
                if (shareTextContent.textContentItems.isEmpty()) {
                    DebugUtil.e(TAG, "executeAsyncClipboard convertContentList is null or empty")
                    return@launch
                }
                shareAction?.share(
                    mActivity,
                    shareTextContent,
                    ShareTypeRecorderAndText(anchor),
                    scope,
                    convertManagerImpl?.getConvertViewController()?.getViewModel()
                )
            }
        }
    }

    private fun getConvertFileName(context: Context?, convertRecord: ConvertRecord?): String? {
        var convertFilePath: String? = null
        var convertFileName: String? = null
        if (convertRecord != null) {
            convertFilePath = convertRecord.convertTextfilePath
        }
        if (!TextUtils.isEmpty(convertFilePath) && (context != null)) {
            val convertFileDir = if (convertRecord?.isOShareFile == true) {
                OShareConvertUtil.getConvertSavePath() + File.separator
            } else {
                NewConvertResultUtil.getConvertSavePath(context) + File.separator
            }
            convertFileName = convertFilePath?.replace(convertFileDir, "")
        }
        DebugUtil.i(TAG, "getConvertFileName, convertFileName:$convertFileName")
        return convertFileName
    }

    private suspend fun getShareTextData(): ShareTextContent? =
        withContext(Dispatchers.IO) {
            val convertRecord = ConvertDbUtil.selectByRecordId(mMediaRecordId)
            if (convertRecord == null) {
                DebugUtil.i(TAG, "getShareTextData convertRecord is null")
                return@withContext null
            }
            val covertFileName = getConvertFileName(BaseApplication.getAppContext(), convertRecord)
            if (covertFileName.isNullOrEmpty()) {
                DebugUtil.i(TAG, "getShareTextData covertFileName is null or empty")
                return@withContext null
            }
            //转文本内容大小,超过50M的时候，保存和分享需要弹窗,
            val convertFileSize =
                NewConvertResultUtil.getConvertFileSize(convertRecord.convertTextfilePath)
            if (convertFileSize <= 0) {
                DebugUtil.i(TAG, "getShareTextData convertFileSize <= 0")
                return@withContext null
            }
            var convertContentList: List<ConvertContentItem>? = null
            try {
                convertContentList = convertManagerImpl?.getConvertContentData()
            } catch (e: IOException) {
                DebugUtil.e(TAG, "getShareTextData error $e")
            }
            if (convertContentList == null) {
                DebugUtil.i(TAG, "getShareTextData convertContentList is null")
                return@withContext null
            }
            ShareTextContent(
                mMediaRecordId,
                exportHelperListener?.isSpeakerRoleShowing() == true,
                covertFileName,
                convertFileSize,
                convertContentList
            )
        }

    interface ExportHelperListener {
        fun isSpeakerRoleShowing(): Boolean?

        fun startWithSharedTextActivity(summaryFilePath: String, mediaId: Long, shareType: Int, fileType: String)

        fun provideLifeCycleOwner(): LifecycleOwner?
    }
}
