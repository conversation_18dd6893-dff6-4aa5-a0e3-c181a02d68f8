/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.txt

import android.content.Context
import android.text.TextUtils
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.base.utils.RecorderICUFormateUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.convertservice.convert.NewConvertResultUtil
import com.soundrecorder.common.databean.ConvertContentItem
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class ShareWithTxtRepository {

    fun getConvertFileName(convertRecord: ConvertRecord?, context: Context): String? {
        val convertFilePath: String? = convertRecord?.convertTextfilePath
        var convertFileName: String? = null
        if (!TextUtils.isEmpty(convertFilePath)) {
            val savePathDir = NewConvertResultUtil.getConvertSavePath(context) + File.separator
            convertFileName = convertFilePath?.replace(savePathDir, "")
        }
        DebugUtil.i(ShareWithTxtViewModel.TAG, "getConvertFileName, convertFileName:$convertFileName convertFilePath = $convertFilePath")
        return convertFileName
    }

    /**
     * 获取文件名称
     */
    fun getFileName(convertRecord: ConvertRecord?): String {
        var fileName = ""
        convertRecord?.mediaPath?.let {
            fileName = it.substring(it.lastIndexOf("/") + 1, it.lastIndexOf("."))
        }
        DebugUtil.i(ShareWithTxtViewModel.TAG, "getFileName >> $fileName")
        return fileName
    }

    /**
     * 获取录音文件的生成时间的date格式
     */
//    private fun getRecordFileCreateDate(recordId: Long, path: String): Date {
//        return Date(
//            RecorderDBUtil.getInstance(RecorderApplication.getAppContext())
//                .getCreateTimeByPath(recordId, path.endsWith(".amr"))
//        )
//    }
    fun getRecordFileCreateDate(createTime: Long): Date {
        return try {
            if (createTime > 0) {
                Date(createTime)
            } else {
                DebugUtil.e(ShareWithTxtViewModel.TAG, "getRecordFileCreateDate error , mCreateTime < 0")
                Date()
            }
        } catch (e: Exception) {
            DebugUtil.e(ShareWithTxtViewModel.TAG, "getRecordFileCreateDate error , ${e.message}")
            Date()
        }
    }

    /**
     * 获取标题显示时间字符串,如果data为nul则返回当前的时间
     */
    fun getTimeString(date: Date?): String {
        val mData = date ?: Date()
        return RecorderICUFormateUtils.formatDateTime(mData)
    }

    /**
     * 获取保存本地文件名称中包含的时间字符串 2021-10-26 10-42-35
     */
    fun getSaveTxtToLocalDate(date: Date?): String {
        return try {
            SimpleDateFormat("yyyy-MM-dd HH-mm-ss", Locale.getDefault()).format(date)
        } catch (e: Exception) {
            DebugUtil.e(ShareWithTxtViewModel.TAG, "getSaveTxtToLocalDate format error>> ${e.message}")
            ""
        }
    }

    /**
     * 获取header里面参会人信息
     */
    fun getRolesString(readConvertContent: ArrayList<ConvertContentItem>): String {
        val stringBuilder = StringBuilder()
        val rolesList = mutableListOf<String>()
        for (item in readConvertContent) {
            item.roleName?.let {
                if (it.isNotBlank() && !rolesList.contains(it)) {
                    rolesList.add(it)
                    stringBuilder.append("$it,  ")
                }
            }
        }
        if (rolesList.isNotEmpty()) {
            return stringBuilder.deleteCharAt(stringBuilder.lastIndexOf(",")).toString()
        }
        return ""
    }

    /**
     * 获取所有item的文本内容，在不显示分段的时候，将显示这个文本内容
     */
    fun getItemsContentString(readConvertContent: ArrayList<ConvertContentItem>): String {
        val builder = StringBuilder()
        for (item in readConvertContent) {
            builder.append(item.textContent)
        }
        return builder.toString()
    }
}