/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertAnimation
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert.anim

import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.view.View
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.view.forEach
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.toolbar.COUIToolbar
import com.soundrecorder.common.utils.isVisible
import com.soundrecorder.playback.newconvert.convert.ConvertViewController

class ConvertAnimation(private val controller: ConvertViewController) {

    companion object {
        private const val OUT_DURATION = 400L
        private const val IN_DURATION = 450L
        private const val PANEL_DELAY = 367L
        private const val PANEL_TRAN_Y = 30f
    }

    private var switchAnim: AnimatorSet? = null

    fun doPreSwitchAnim(tagetView: View?, otherView: Boolean = true) {
        release()
        tagetView?.alpha = 0f
        if (otherView) {
            appbarAlpha(0f)
            controller.containerFragment?.contentBinding?.floatButtonPanel?.root?.apply {
                alpha = 0f
                translationY = PANEL_TRAN_Y
            }
        }
    }

    fun doEndSwitchAnim() {
        controller.containerFragment?.contentBinding?.toolbar?.titleView?.alpha = 1f
    }

    fun doSwitchConvertContentAnimation(actionDoAnimEnd: (() -> Unit)? = null) {
        switchAnim?.cancel()
        switchAnim = null
        val outAnim = outAnim()
        val inAnim = contentInAnim()
        switchAnim = AnimatorSet().apply {
            doOnEnd {
                actionDoAnimEnd?.invoke()
            }
        }
        switchAnim?.playSequentially(outAnim, inAnim)
        switchAnim?.start()
    }

    private fun outAnim(): ValueAnimator {
        return ValueAnimator.ofFloat(1f, 0f).apply {
            interpolator = COUIEaseInterpolator()
            duration = OUT_DURATION
            addUpdateListener {
                val alpha = it.animatedValue as Float
                controller.mConvertingView?.alpha = alpha
                controller.containerFragment?.buttonPanelControl?.getButtonPanel()?.alpha = alpha
                controller.containerFragment?.contentBinding?.toolbar?.titleView?.alpha = alpha
            }
            doOnStart {
                controller.mConvertingView?.alpha = 1f
                controller.containerFragment?.buttonPanelControl?.getButtonPanel()?.alpha = 1f
                controller.containerFragment?.contentBinding?.toolbar?.titleView?.alpha = 1f
            }
            doOnEnd {
                controller.mConvertingView?.alpha = 0f
                controller.containerFragment?.buttonPanelControl?.getButtonPanel()?.alpha = 0f
                controller.containerFragment?.contentBinding?.toolbar?.titleView?.alpha = 0f
            }
        }
    }

    private fun contentInAnim(): AnimatorSet {
        val inAnimSet = AnimatorSet()
        val inAlphaAnim = ValueAnimator.ofFloat(0f, 1f).apply {
            interpolator = COUIEaseInterpolator()
            duration = IN_DURATION
            addUpdateListener {
                val alpha = it.animatedValue as Float
                appbarAlpha(alpha)
                controller.mConvertContentView?.alpha = alpha
            }
            doOnStart {
                appbarAlpha(0f)
                controller.mConvertContentView?.alpha = 0f
            }
            doOnEnd {
                appbarAlpha(1f)
                controller.mConvertContentView?.alpha = 1f
            }
        }

        val panelAnim = panelAnim()
        panelAnim?.let {
            inAnimSet.playTogether(inAlphaAnim, it)
        } ?: run {
            inAnimSet.play(inAlphaAnim)
        }
        return inAnimSet
    }

    private fun panelAnim(): ValueAnimator? {
        val panel =
            controller.containerFragment?.contentBinding?.floatButtonPanel?.root ?: return null
        return ValueAnimator.ofFloat(0f, 1f).apply {
            interpolator = COUIEaseInterpolator()
            duration = IN_DURATION
            startDelay = PANEL_DELAY
            addUpdateListener {
                val alpha = it.animatedValue as Float
                panel.alpha = alpha
                panel.translationY = (1f - alpha) * PANEL_TRAN_Y
            }
            doOnStart {
                panel.translationY = PANEL_TRAN_Y
                panel.alpha = 0f
            }
            doOnEnd {
                panel.translationY = 0f
                panel.alpha = 1f
            }
        }
    }

    private fun appbarAlpha(alpha: Float) {
        controller.containerFragment?.contentBinding?.appbarLayout?.forEach {
            if (it.isVisible() && (it is COUIToolbar).not()) {
                it.alpha = alpha
            }
        }
    }

    fun release() {
        switchAnim?.cancel()
        switchAnim = null
    }

    fun doSwitchConvertingAnimation(actionDoAnimEnd: (() -> Unit)? = null) {
        switchAnim?.cancel()
        switchAnim = null
        val outAnim = outInitViewAnim()
        val inAnim = convertingInAnim()
        switchAnim = AnimatorSet().apply {
            doOnEnd {
                actionDoAnimEnd?.invoke()
            }
        }
        switchAnim?.playSequentially(outAnim, inAnim)
        switchAnim?.start()
    }

    private fun outInitViewAnim(): ValueAnimator {
        return ValueAnimator.ofFloat(1f, 0f).apply {
            interpolator = COUIEaseInterpolator()
            duration = OUT_DURATION
            addUpdateListener {
                val alpha = it.animatedValue as Float
                controller.mConvertInitView?.alpha = alpha
            }
            doOnStart {
                controller.mConvertInitView?.alpha = 1f
            }
            doOnEnd {
                controller.mConvertInitView?.alpha = 0f
            }
        }
    }

    private fun convertingInAnim(): AnimatorSet {
        val inAnimSet = AnimatorSet()
        val inAlphaAnim = ValueAnimator.ofFloat(0f, 1f).apply {
            interpolator = COUIEaseInterpolator()
            duration = IN_DURATION
            addUpdateListener {
                val alpha = it.animatedValue as Float
                controller.mConvertingView?.alpha = alpha
            }
            doOnStart {
                controller.mConvertingView?.alpha = 0f
            }
            doOnEnd {
                controller.mConvertingView?.alpha = 1f
            }
        }
        inAnimSet.play(inAlphaAnim)
        return inAnimSet
    }

    fun doSwitchConvertInitAnimation(actionDoAnimEnd: (() -> Unit)? = null) {
        switchAnim?.cancel()
        switchAnim = null
        val outAnim = outConvertingViewAnim()
        val inAnim = convertInitInAnim()
        switchAnim = AnimatorSet().apply {
            doOnEnd {
                actionDoAnimEnd?.invoke()
            }
        }
        switchAnim?.playSequentially(outAnim, inAnim)
        switchAnim?.start()
    }

    private fun outConvertingViewAnim(): ValueAnimator {
        return ValueAnimator.ofFloat(1f, 0f).apply {
            interpolator = COUIEaseInterpolator()
            duration = OUT_DURATION
            addUpdateListener {
                val alpha = it.animatedValue as Float
                controller.mConvertingView?.alpha = alpha
            }
            doOnStart {
                controller.mConvertingView?.alpha = 1f
            }
            doOnEnd {
                controller.mConvertingView?.alpha = 0f
            }
        }
    }

    private fun convertInitInAnim(): AnimatorSet {
        val inAnimSet = AnimatorSet()
        val inAlphaAnim = ValueAnimator.ofFloat(0f, 1f).apply {
            interpolator = COUIEaseInterpolator()
            duration = IN_DURATION
            addUpdateListener {
                val alpha = it.animatedValue as Float
                controller.mConvertInitView?.alpha = alpha
            }
            doOnStart {
                controller.mConvertInitView?.alpha = 0f
            }
            doOnEnd {
                controller.mConvertInitView?.alpha = 1f
            }
        }
        inAnimSet.play(inAlphaAnim)
        return inAnimSet
    }
}