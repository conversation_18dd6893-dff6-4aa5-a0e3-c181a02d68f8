/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MarkListBottomSheetDialogFragment
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.view

import android.widget.FrameLayout
import com.coui.appcompat.panel.COUIBottomSheetBehavior
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.StatusBarUtil

class MarkListBottomSheetDialogFragment : COUIBottomSheetDialogFragment() {
    private var markListContainerFragment: MarkListContainerFragment? = null

    companion object {
        private const val TAG = "MarkListBottomSheetDialogFragment"
    }

    override fun setMainPanelFragment(panelFragment: COUIPanelFragment?) {
        super.setMainPanelFragment(panelFragment)
        if (panelFragment is MarkListContainerFragment) {
            this.markListContainerFragment = panelFragment
        }
    }

    override fun onStart() {
        super.onStart()
        if (bottomSheetDialog != null) {
            bottomSheetDialog.setOutsideMaskColor(android.R.color.transparent)
            context?.let {
                StatusBarUtil.setDialogStatusBarTransparentAndBlackFont(it, bottomSheetDialog, true)
            }
        }
    }

    fun setDialogContentViewState() {
        markListContainerFragment?.setDialogContentViewState()
    }

    fun setBottomSheetDialogFragmentState(state: Int) {
        DebugUtil.i(TAG, "setBottomSheetDialogFragmentState show state:$state")
        this.bottomSheetDialog?.let {
            if (it.behavior is COUIBottomSheetBehavior) {
                val bottomSheetBehavior: COUIBottomSheetBehavior<FrameLayout> = it.behavior as COUIBottomSheetBehavior<FrameLayout>
                bottomSheetBehavior.setPanelState(state)
            }
        }
    }
}