/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlaybackContainerFragment
 * Description:
 * Version: 1.0
 * Date: 2022/11/4
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/11/4 1.0 create
 */

package com.soundrecorder.playback

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.DialogInterface.OnDismissListener
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.database.Cursor
import android.media.AudioManager
import android.net.Uri
import android.os.Bundle
import android.os.FileObserver
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.MediaStore
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.view.Gravity
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.accessibility.AccessibilityEvent
import android.widget.AdapterView
import android.widget.FrameLayout
import android.widget.TextView
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.children
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import androidx.fragment.app.Fragment
import androidx.fragment.app.commit
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.viewpager2.widget.ViewPager2
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.panel.COUIBottomSheetBehavior
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.poplist.PopupListItem.Builder
import com.coui.appcompat.searchview.COUISearchBar
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.backpressed.OnBackPressedListener
import com.soundrecorder.base.ext.currentHasHourFormatTimeExclusive
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.ext.durationHasHourFormatTimeExclusive
import com.soundrecorder.base.ext.getValueWithDefault
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.ext.removeFragment
import com.soundrecorder.base.ext.replaceFragmentByTag
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.userchange.OnFragmentUserChangeListener
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.RecorderICUFormateUtils
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.StatusBarUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.base.view.DeDuplicateInsetsCallback
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil
import com.soundrecorder.common.buryingpoint.CuttingStaticsUtil
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.dialog.ButtonType
import com.soundrecorder.common.dialog.LoadingDialog
import com.soundrecorder.common.dialog.PositiveCallback
import com.soundrecorder.common.fileobserve.MultiFileObserver
import com.soundrecorder.common.fileobserve.OnFileEventListener
import com.soundrecorder.common.fileoperator.delete.DeleteFileDialog
import com.soundrecorder.common.fileoperator.delete.OnFileDeleteListener
import com.soundrecorder.common.fileoperator.recover.OnRecoverFileListener
import com.soundrecorder.common.fileoperator.recover.RecoverFileDialog
import com.soundrecorder.common.fileoperator.rename.RenameFileDialog
import com.soundrecorder.common.flexible.FollowDialogRestoreUtils
import com.soundrecorder.common.permission.PermissionDialogUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.share.OShareConvertUtil
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.common.share.ShareTypeNote
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.RecordFileChangeNotify
import com.soundrecorder.common.utils.SendSetUtil
import com.soundrecorder.common.utils.TimeSetUtils
import com.soundrecorder.common.utils.VibrateUtils
import com.soundrecorder.common.utils.sound.DeleteSoundEffectManager
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.EditRecordInterface
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_CONVERT_STATUS
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_RECORD_ID
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_NAME_TEXT
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_STATUS
import com.soundrecorder.modulerouter.playback.NOTIFY_CONVERT_STATUS_UPDATE
import com.soundrecorder.modulerouter.playback.NOTIFY_SMART_NAME_STATUS_UPDATE
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.PAGE_FROM_PLAYBACK
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.share.ShareType
import com.soundrecorder.modulerouter.smartname.ISmartNameManager
import com.soundrecorder.modulerouter.summary.IAISummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.modulerouter.waveMark.IIPictureMarkListener
import com.soundrecorder.modulerouter.waveMark.IPictureMarkDelegate
import com.soundrecorder.modulerouter.waveMark.IPictureMarkLifeOwnerProvider
import com.soundrecorder.modulerouter.waveMark.WaveMarkInterface
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_ADD
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_DELETE
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_INIT
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_RENAME
import com.soundrecorder.playback.audio.PlaybackAudioFragment
import com.soundrecorder.playback.audio.setting.PlaySettingDialogFragment
import com.soundrecorder.playback.buttonpanel.ButtonPanelControl
import com.soundrecorder.playback.convert.IConvertManager
import com.soundrecorder.playback.databinding.FragmentPlaybackContainerBinding
import com.soundrecorder.playback.newconvert.PlaybackConvertFragment
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_CANCEL
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_COMPLETE
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_INIT
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_PROGRESS
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_SUMMARY_NONE_COMPLETE
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_USER_TIMEOUT
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_USER_TIMEOUT_NOT_COMPLETE
import com.soundrecorder.playback.newconvert.convert.ConvertManagerImpl
import com.soundrecorder.playback.newconvert.search.ConvertSearchFragment
import com.soundrecorder.playback.newconvert.ui.ConvertViewContainer
import com.soundrecorder.playback.newconvert.view.BackGroundTextViewSetupHelper
import com.soundrecorder.playback.view.MarkListBottomSheetDialogFragment
import com.soundrecorder.playback.view.MarkListContainerFragment
import com.soundrecorder.playback.view.PlayPointMoveListener
import com.soundrecorder.player.TimerTickCallback
import com.soundrecorder.player.speaker.SpeakerModeController
import com.soundrecorder.player.speaker.SpeakerStateManager
import com.soundrecorder.player.status.PlayStatus
import com.soundrecorder.wavemark.mark.MarkListAdapter
import kotlinx.coroutines.launch
import java.util.Date

@Suppress("LargeClass", "LongMethod")
class PlaybackContainerFragment : Fragment(), View.OnClickListener, OnFileEventListener,
    IIPictureMarkListener<MarkMetaData, MarkDataBean>, OnBackPressedListener,
    IPlayBackContainerListener,
    OnFragmentUserChangeListener {

    companion object {
        /*
        延迟showLoading时间，默认0
        */
        const val ARG_KEY_SHOW_LOADING = "key_show_loading"

        const val MAX_PROGRESS = 1000
        const val THREE_SECONDS = 3000
        const val TIP_SHOW_DELAY_TIME = 300L
        const val SEARCH_INPUT_MAX_LENGTH = 50
        const val REQUEST_CODE_SYS_RENAME_AUTH = 211
        const val REQUEST_CODE_SYS_DELETE_AUTH = 212
        const val REQUEST_CODE_SYS_RECOVER_AUTH = 213
        const val MENU_POSITION_0 = 0

        private const val TAG = "PlaybackContainerFragment"

        private const val EKY_IS_IN_CONVERT_SEARCH = "key_is_in_convert_search"
        private const val KEY_IN_CONVERT_SEARCH_VALUE = "key_convert_search_value"
        private const val KEY_CONVERT_SEARCH_CURRENT_POS = "key_convert_search_current_pos"
        private const val KEY_CONVERT_SEARCH_LAST_POS = "key_convert_search_last_pos"
        private const val MARK_LIST_FRAGMENT_TAG = "MarkListView"
        private const val MARK_LIST_SHOW_STATE = "MARK_LIST_SHOW_STATE"
        private const val MARK_LIST_SHOW = "SHOW"
    }

    var mConvertManagerImpl: IConvertManager? = null
    var convertViewContainer: ConvertViewContainer? = null
    var mViewModel: PlaybackContainerViewModel? = null
    var mPlaybackConvertViewModel: PlaybackConvertViewModel? = null
    var contentBinding: FragmentPlaybackContainerBinding? = null
    //系统任务栏的高度
    var navigationHeight: Int = 0
    val playbackHeaderHelper by lazy {
        PlaybackHeaderHelper(this)
    }
    var mPictureMarkHelper: IPictureMarkDelegate<MarkMetaData>? = null

    val buttonPanelControl by lazy {
        ButtonPanelControl(
            browseFileApi,
            this,
            getAudioFragment(),
            mViewModel,
            mBrowseFileActivityViewModel
        )
    }

    //播放面板展开/折叠动画
    val expandCollapseAnimationHelper: ExpandCollapseAnimationHelper by lazy {
        expandCollapseAnimationHelperInitial = true
        ExpandCollapseAnimationHelper(requireContext(), mViewModel, binding)
    }

    //切换沉浸态动画
    val immersiveAnimationHelper: ConvertImmersiveAnimationHelper by lazy {
        immersiveAnimationHelperInitial = true
        ConvertImmersiveAnimationHelper(binding, expandCollapseAnimationHelper)
    }

    private val pagerMediator by lazy {
        PlaybackPagerMediator(this)
    }
    private var mBrowseFileActivityViewModel: ViewModel? = null
    private var speakerModeController: SpeakerModeController? = null
    private var mDeleteDialog: DeleteFileDialog? = null
    private var mDetailDialog: AlertDialog? = null
    private var mRenameDialog: RenameFileDialog? = null
    private var mSpeakerMenuItem: MenuItem? = null
    private var mDeleteMenuItem: MenuItem? = null
    private var mRecoverMenuItem: MenuItem? = null
    private var mPlaySettingMenuItem: MenuItem? = null
    private var mCutMenuItem: MenuItem? = null
    private var mSearchMenuItem: MenuItem? = null
    private var mConvertRoleMenuItem: MenuItem? = null
    private var mStartEditLauncher: ActivityResultLauncher<Intent>? = null
    private var mAudioFragment: PlaybackAudioFragment? = null
    private var mConvertFragment: PlaybackConvertFragment? = null
    private var mSummaryFragment: IAISummaryInterface? = null
    private var mConvertSearchFragment: ConvertSearchFragment? = null

    /*
    旧版分享txt，转文本文件超过50M的时候，显示“请稍后...”dialog
    链接分享,生成链接时，显示"正在生成…"dialog
    */
    private var mLoadingDialog: LoadingDialog? = null
    private var mPageChangeCallback: ViewPager2.OnPageChangeCallback? = null

    //播放设置弹窗
    private var bottomSheetDialogFragment: COUIBottomSheetDialogFragment? = null
    private lateinit var binding: FragmentPlaybackContainerBinding
    private var mLoadingViewControl: LoadingViewControl? = null
    private val markMenuList by lazy { ArrayList<PopupListItem>() }

    private var searchAnimView: COUISearchBar? = null
    private var disableDialog: AlertDialog? = null
    private var mRecoverDialog: RecoverFileDialog? = null
    private var immersiveAnimationHelperInitial: Boolean? = null
    private var expandCollapseAnimationHelperInitial: Boolean? = null

    //系统任务栏的高度
    private var windowType: WindowType? = null

    //分享弹窗关闭监听
    private val shareDialogDismissListener by lazy {
        OnDismissListener {
            DebugUtil.i(TAG, "shareDialogDismissListener")
            mViewModel?.mNeedShowShareDialog?.value = false
        }
    }

    private var mToolbarOverflowPopupWindow: COUIPopupListWindow? = null
    private var mSubMenuCheckedPosition = 0
    private var isClickAiTitle: Boolean = false

    private var mSmartNameMangerImpl: ISmartNameManager? = null
    private var mSupportSmartName: Boolean = false
    private var mFilePermissionDialog: AlertDialog? = null

    private var mTimerTickCallback: TimerTickCallback? = null
    private var mMarkListAdapter: MarkListAdapter? = null
    private var markListDialogFragment: MarkListBottomSheetDialogFragment? = null

    private var isShareDialogShowing = false
    private var isShareOriginDialogShowing = false
    private var isShareSummaryDialogShowing = false
    private var shareSelectType = -1
    private var fromRestore = false

    private val mainHandler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                BackGroundTextViewSetupHelper.MessageTypes.MSG_MARK_CLICKED -> {
                    val markTime = msg.obj as? Long ?: return
                    mMarkListAdapter?.setCheck(markTime)
                    handleMarkClick(markTime)
                }
                BackGroundTextViewSetupHelper.MessageTypes.MSG_SHOW_MARK_LIST -> handleClickMarkList()
            }
        }
    }

    private val shareListener = object : IShareListener {
        override fun onShowShareWaitingDialog(mediaId: Long, type: ShareType) {
            DebugUtil.i(TAG, "onShowShareWaitingDialog type:$type")
            activity?.runOnUiThread {
                showWaitingDialog(type)
            }
        }

        override fun onShareSuccess(mediaId: Long, type: ShareType) {
            DebugUtil.i(TAG, "onShareSuccess，type >> $type")
            activity?.runOnUiThread {
                dismissDialog()
            }
            unregisterShareListener()
        }

        override fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String) {
            DebugUtil.i(TAG, "onShareFailed，type >> $type  error:$error  message:$message")
            activity?.runOnUiThread {
                dismissDialog()
            }
            unregisterShareListener()
        }
    }

    private fun unregisterShareListener() {
        shareAction?.unregisterShareListener(shareListener)
    }

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val editRecordApi by lazy {
        Injector.injectFactory<EditRecordInterface>()
    }

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val cloudTipManagerAction by lazy {
        Injector.injectFactory<CloudTipManagerAction>()
    }

    private val seedingApi by lazy {
        Injector.injectFactory<SeedingInterface>()
    }

    private val waveMarkApi by lazy {
        Injector.injectFactory<WaveMarkInterface>()
    }

    private val shareAction by lazy {
        Injector.injectFactory<ShareAction>()
    }

    private val playbackAction by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    private val convertReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val mediaId = intent?.getLongExtra(KEY_NOTIFY_RECORD_ID, -1L) ?: -1L
            val isConvert = intent?.getBooleanExtra(KEY_NOTIFY_CONVERT_STATUS, false)
            DebugUtil.d(
                TAG, "ConvertReceiver, intent?.action:${intent?.action} , " +
                        "KEY_NOTIFY_CONVERT_STATUS:$isConvert ,KEY_NOTIFY_RECORD_ID:$mediaId "
            )
            when (intent?.action) {
                NOTIFY_CONVERT_STATUS_UPDATE -> {
                    if (isConvert == true && mediaId == mViewModel?.recordId) {
                        mPlaybackConvertViewModel?.updateConvertStatus(CONVERT_STATUS_PROGRESS)
                    }
                }

                NOTIFY_SMART_NAME_STATUS_UPDATE -> {
                    if (mediaId == mViewModel?.recordId) {
                        val resultName = intent.getStringExtra(KEY_NOTIFY_SMART_NAME_NAME_TEXT)
                        val display = intent.getBooleanExtra(KEY_NOTIFY_SMART_NAME_STATUS, false)
                        DebugUtil.d(TAG, "onReceive, display:$display, smartName:$resultName")
                        //待分析：转文本页面都没标题了，还要这个调用么，看起来调用链挺长
                        mConvertManagerImpl?.onSmartNameStatusChange(display, resultName)
                        playbackHeaderHelper.onSmartNameStatusChange(display, resultName)
                        if (!resultName.isNullOrEmpty()) {
                            mViewModel?.renameRecordByCore2Full(resultName)
                        }
                        getSummaryFragment()?.onRecordNameStatusChange(resultName = resultName)
                    }
                }
            }
        }
    }

    private val playAmpSeekbarPointMoveListener = object : PlayPointMoveListener {
        override fun onTouchDownMiddleBar() {
            mViewModel?.let {
                it.playerController.onStartTouchSeekBar()
                if (it.playerController.isWholePlaying()) {
                    it.playerController.stopTimerNow()
                    getAudioFragment()?.waveStopMove()
                }
            }
        }

        override fun onMoveOnMiddleBar(time: Long) {
            mViewModel?.let {
                it.needSyncRulerView = true
                it.playerController.setOCurrentTimeMillis(time)
            }
        }

        override fun onTouchUpMiddleBar(time: Long) {
            mViewModel?.let {
                it.playerController.onStopTouchSeekBar()
                it.seekTime(time)
                //按下之前是播放状态，松手后也应该继续播放。
                it.playerController.onResetPlayState()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initBrowseFileActivityViewModel()
        removeWhenChangeSmallIfToRecord()
        registerReceivers()

        StatusBarUtil.switchNavigationBarTransparent(activity, false)
    }

    private fun initBrowseFileActivityViewModel() {
        mBrowseFileActivityViewModel = browseFileApi?.getBrowseActivityViewModel(activity as? AppCompatActivity)
    }

    private fun removeWhenChangeSmallIfToRecord() {
        if (ScreenUtil.isSmallScreen(context) && (browseFileApi?.getViewModelClickedToRecord(mBrowseFileActivityViewModel) == true)) {
            /*进入了录制页面，再小屏下，退出播放详情*/
            activity?.supportFragmentManager?.commit {
                remove(this@PlaybackContainerFragment)
            }
            browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
        }
    }

    private fun initConvertManager() {
        val viewModel = mViewModel ?: return
        val convertViewContainer = this.convertViewContainer ?: return
        if (viewModel.recordId != -1L) {
            mConvertManagerImpl = ConvertManagerImpl()
            DebugUtil.i(TAG, "initConvertManager recordId:${viewModel.recordId} convertSupportType:${viewModel.convertSupportType}")
            getConvertFragment()?.mConvertManagerImpl = mConvertManagerImpl
            mConvertManagerImpl?.register(this, convertViewContainer, viewModel.recordId, viewModel.convertSupportType)
            mConvertManagerImpl?.setExportMenuItem()
            mConvertManagerImpl?.setViewModel(viewModel)
            mConvertManagerImpl?.setConvertImmersiveAnimationHelper(animationHelper = immersiveAnimationHelper)
            checkRestoreShareDialog()
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val rootView = inflater.inflate(R.layout.fragment_playback_container, container, false)
        binding = FragmentPlaybackContainerBinding.bind(rootView)
        activity?.let {
            val convertViewContainer = ConvertViewContainer(it)
            convertViewContainer.id = com.soundrecorder.common.R.id.convert_view_container
            this.convertViewContainer = convertViewContainer
        }
        if (ScreenUtil.isSmallScreen(context)) {
            (activity as? BaseActivity)?.registerBackPressed()
        } else {
            (activity as? BaseActivity)?.registerFinishAndRemoveTaskCallback()
        }
        contentBinding = binding
        return rootView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        fromRestore = savedInstanceState != null
        DebugUtil.d(TAG, "onViewCreated, savedInstanceState is null:${savedInstanceState == null}")
        initPictureMarkHelper(fromRestore)
        initViewModel(savedInstanceState)
        configToolbar()
        initStartLauncher()
        initPlayContainer()
        initPanelOverlay()
        initHeader()
        initTab(fromRestore)
        initAmpSeekBar()
        checkShowLoading()
        initAccess()
        initFloatPanel()
        initBrowseActivityViewModelObserver()
        initViewModelObserver()
        initOtherViews()
        initiateWindowInsets(binding.rootView)
        initListener()
        checkDialogRestore(fromRestore)
        checkDialogShowStatus(fromRestore)
        initSmartNameManagerImpl()
        initMarkMenu()
        initMarkList(savedInstanceState)
        initMarkListObserver()
        observerButtonPanelHeight()
        observe()
        initBackGroundTextViewSetupHelper()
    }

    private fun initBackGroundTextViewSetupHelper() {
        // 初始化并设置Handler
        BackGroundTextViewSetupHelper().apply {
            setFragmentHandler(mainHandler)
        }
    }

    private fun handleMarkClick(markTime: Long) {
        // 处理标记点击逻辑
        refreshCurPlayTime(markTime)
    }

    private fun initSmartNameManagerImpl() {
        if (mSmartNameMangerImpl == null) {
            mSmartNameMangerImpl = playbackAction?.getSmartNameManager()
        }
    }

    private fun initMarkMenu() {
        val mark = resources.getString(com.soundrecorder.common.R.string.talkback_flag)

        markMenuList.apply {
            clear()
            add(Builder().setTitle(mark).build())
        }
    }

    private fun observerButtonPanelHeight() {
        binding.floatButtonPanel.panel.observeHeightChange { height ->
            floatButtonPanelHeightChange(height)
        }
    }

    private fun floatButtonPanelHeightChange(height: Int) {
        val floatButtonMarginVertical = binding.floatButtonPanel.panel.getPanelMarginVertical()
        getConvertFragment()?.updateFloatPanelLayoutParams(FloatButtonPanelLayoutParams(height, floatButtonMarginVertical))
        getSummaryFragment()?.setPaddingBottom(height + floatButtonMarginVertical)
    }

    private fun observe() {
        mViewModel?.showShareLinkPanel?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe showShareLinkPanel $it ${mViewModel?.currentLifecycleState}")
            if (it?.isNotEmpty() == true && mViewModel?.currentLifecycleState == Lifecycle.Event.ON_RESUME) {
                activity?.let { activity ->
                    shareAction?.showShareLinkPanel(activity, it, null)
                    mViewModel?.showShareLinkPanel?.value = null
                }
            }
        }

        mViewModel?.showShareToast?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe showShareToast $it ${mViewModel?.currentLifecycleState}")
            if (it != null && mViewModel?.currentLifecycleState == Lifecycle.Event.ON_RESUME) {
                ToastManager.showShortToast(context, it)
                mViewModel?.showShareToast?.value = null
            }
        }
    }

    private fun initMarkListObserver() {
        mViewModel?.lastMarkAction?.observe(viewLifecycleOwner) {
            DebugUtil.i(TAG, "lastMarkAction changed $it")
            when (it) {
                MARK_ACTION_INIT -> notifyMarkListByData { markListDialogFragment?.setDialogContentViewState() }
                MARK_ACTION_DELETE -> {
                    notifyMarkListByData { markListDialogFragment?.setDialogContentViewState() }
                    BuryingPoint.addMarkDelete(mViewModel?.recordType?.value ?: 0)
                }

                MARK_ACTION_RENAME -> {
                    notifyMarkListByData()
                    BuryingPoint.addMarkRename(mViewModel?.recordType?.value ?: 0)
                }

                MARK_ACTION_ADD -> {
                    kotlin.runCatching {
                        val index = mViewModel?.addMarkIndex
                        mMarkListAdapter?.setShowAnimatorPos(index ?: 0)
                        notifyMarkListByData { markListDialogFragment?.setDialogContentViewState() }
                        mViewModel?.playerController?.playerState?.value?.let { playerState ->
                            if (playerState in intArrayOf(
                                    PlayStatus.PLAYER_STATE_PAUSE,
                                    PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
                                    PlayStatus.PLAYER_STATE_HALTON
                                )
                            ) {
                                BuryingPoint.addMarkWhenUnplay()
                            }
                        }
                        BuryingPoint.addMarkAdd(mViewModel?.recordType?.value ?: 0)
                    }.onSuccess {
                        DebugUtil.w(TAG, "Add mark success.")
                    }.onFailure {
                        DebugUtil.e(TAG, "Add mark error.")
                    }
                }
            }
        }

        mViewModel?.isShowMarkList?.observe(viewLifecycleOwner) { isShowing ->
            DebugUtil.i(TAG, "isShowing = $isShowing")
            if (isShowing) {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_SHOWING)
            } else {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_UNSHOWING)
            }
            changeMarkListViewDisplayState(isShowing)
        }
    }

    private fun notifyMarkListByData(commitCallback: Runnable? = null) {
        mViewModel?.getMarkList()?.value?.let {
            mMarkListAdapter?.setData(it, commitCallback)
        }
    }

    private fun initViewModel(savedInstanceState: Bundle?) {
        val activity = activity as? AppCompatActivity ?: return
        val isFromOtherProcess = browseFileApi?.getViewModelIsFromOther(mBrowseFileActivityViewModel) ?: false
        val supportConvertType = ConvertSupportManager.getConvertSupportType(!isFromOtherProcess)
        initConvertViewModel(savedInstanceState, supportConvertType)
        initViewModel(activity, savedInstanceState, isFromOtherProcess, supportConvertType)
        TimeSetUtils(viewLifecycleOwner) {
            mViewModel?.playerController?.playerState?.value = mViewModel?.playerController?.playerState?.value
        }
    }

    private fun initConvertViewModel(savedInstanceState: Bundle?, supportConvertType: Int) {
        mPlaybackConvertViewModel =
            ViewModelProvider(this)[PlaybackConvertViewModel::class.java].apply {
                if (savedInstanceState != null && supportConvertType != ConvertSupportManager.CONVERT_DISABLE) {
                    if (!recordFilterIsRecycle()) {
                        savedInstanceState.getBoolean(EKY_IS_IN_CONVERT_SEARCH, false)
                            .let { isConvertSearch ->
                                DebugUtil.d(TAG, "initViewModel, isConvertSearch:$isConvertSearch")
                                mIsInConvertSearch.value = isConvertSearch
                            }
                    }
                    mConvertSearchValue = savedInstanceState.getString(KEY_IN_CONVERT_SEARCH_VALUE, "")
                    currentPos = savedInstanceState.getInt(KEY_CONVERT_SEARCH_CURRENT_POS, 0)
                    lastPos = savedInstanceState.getInt(KEY_CONVERT_SEARCH_LAST_POS, 0)
                }
            }
    }

    private fun initViewModel(activity: AppCompatActivity, savedInstanceState: Bundle?, isFromOtherProcess: Boolean, supportConvertType: Int) {
        mViewModel = ViewModelProvider(this)[PlaybackContainerViewModel::class.java].apply {
            isRecycle = browseFileApi?.getViewModelPlayData<StartPlayModel>(mBrowseFileActivityViewModel)?.value?.isRecycle ?: false
            mIsFromOtherApp = isFromOtherProcess
            convertSupportType = supportConvertType
            isAddPictureMarkingCallback = {
                <EMAIL>?.isAddPictureMarking()
                    ?: MutableLiveData(false)
            }
            oShareConvertTextPath = activity.intent?.getStringExtra(OShareConvertUtil.EXTRA_OSHARE_CONVERT_TEXT_PATH)
            /*
             * 注：<1>一定要放在 isAddPictureMarkingCallback初始化之后
             *    <2>需要mPlaybackConvertViewModel?.mIsInConvertSearch先初始化
             */
            observeNotificationBtn(
                savedInstanceState != null,
                mPlaybackConvertViewModel?.mIsInConvertSearch
            )
            initLifecycle(this@PlaybackContainerFragment)
        }
    }

    private fun initPictureMarkHelper(isFromRestore: Boolean) {
        val activity = activity as? AppCompatActivity ?: return
        mPictureMarkHelper =  waveMarkApi?.newPictureMarkDelegate(object : IPictureMarkLifeOwnerProvider {
            override fun provideLifeCycleOwner(): LifecycleOwner = this@PlaybackContainerFragment

            override fun provideActivity(): AppCompatActivity = activity

            override fun isFinishing(): Boolean = (activity.isFinishing || <EMAIL>)
        }, isActivityRecreate = isFromRestore, listener = this)
    }

    private fun checkDialogShowStatus(isFromRestore: Boolean) {
        if (!isFromRestore) {
            DebugUtil.i(TAG, "checkDialogShowStatus not from restore, no need to check Dialog Show")
            return
        }
        if (mViewModel?.mNeedShowMarkRenameDialog?.value == true) {
            DebugUtil.i(TAG, "checkDialogShowStatus mMarkRenameDialogShow")
            mMarkListAdapter?.showRenameMark(
                activity,
                mViewModel?.mMarkRenameData,
                mViewModel?.mRenameMarkEditText
            )
        }
        if (mViewModel?.mNeedShowMarkDeleteDialog == true) {
            mMarkListAdapter?.showDeleteMark(activity, mViewModel?.mMarkDeleteData)
        }
    }

    private fun initListener() {
        mViewModel?.setShareCallBack(shareListener)
        binding.floatButtonPanel.floatImgMarkAdd.setOnClickListener(this)
        binding.floatButtonPanel.floatImgForward.setOnClickListener(this)
        binding.floatButtonPanel.floatImgBackward.setOnClickListener(this)
        binding.floatButtonPanel.floatMarkList.setOnClickListener(this)
        binding.floatButtonPanel.floatRedCircleIcon.setOnClickListener(this)
        binding.floatButtonPanel.imgWaveFormBg.setOnClickListener(this)
    }

    private fun checkDialogRestore(isFromRestore: Boolean) {
        if (!isFromRestore) {
            DebugUtil.i(TAG, "checkDialogRestore not from restore, no need to check Dialog Show")
            return
        }
        if (mViewModel?.mNeedShowRenameDialog?.value == true) {
            DebugUtil.i(TAG, "checkDialogRestore showRenameDialog")
            showRenameDialog(mViewModel?.mRenameEditText)
        }
        if (mViewModel?.getNeedRestoreWaitingDialog() == true) {
            DebugUtil.i(TAG, "checkDialogRestore waitingDialog")
            //没有完成分享前置流程，则显示waitDialog
            showWaitingDialog(mViewModel?.getShareWaitingType())
        }
    }

    fun onPermissionGranted() {
        if (!PermissionUtils.hasReadAudioPermission()) {
            DebugUtil.i(TAG, "not hasReadAudioPermission")
            return
        }
        if (mViewModel?.recordId != null && mViewModel?.recordId != -1L) {
            mViewModel?.readMarkTag()
        }
    }

    private fun getAudioFragment(): PlaybackAudioFragment? {
        if (mAudioFragment == null && isAdded) {
            mAudioFragment = childFragmentManager.findFragmentByTag(PlaybackAudioFragment.TAG) as? PlaybackAudioFragment
        }
        return mAudioFragment
    }

    private fun getConvertFragment(): PlaybackConvertFragment? {
        if (mConvertFragment == null && isAdded) {
            mConvertFragment = pagerMediator.findFragmentByType(PlayBackInterface.TAB_TYPE_CONVERT) as? PlaybackConvertFragment
        }
        return mConvertFragment
    }

    private fun getSummaryFragment(): IAISummaryInterface? {
        if (mSummaryFragment == null && isAdded) {
            mSummaryFragment = pagerMediator.findFragmentByType(PlayBackInterface.TAB_TYPE_SUMMARY) as? IAISummaryInterface
        }
        return mSummaryFragment
    }

    private fun getConvertSearchFragment(): ConvertSearchFragment? {
        if (mConvertSearchFragment == null && isAdded) {
            mConvertSearchFragment = childFragmentManager.findFragmentByTag(ConvertSearchFragment.TAG) as? ConvertSearchFragment
        }
        return mConvertSearchFragment
    }

    private fun initOtherViews() {
        if (BaseApplication.sIsRTLanguage) {
            binding.floatButtonPanel.floatImgBackward.setImageResource(R.drawable.ic_forward)
            binding.floatButtonPanel.floatImgForward.setImageResource(R.drawable.ic_backward)
        }
    }

    private fun initAccess() {
        binding.floatButtonPanel.floatRedCircleIcon.accessibilityDelegate = object : View.AccessibilityDelegate() {
            override fun sendAccessibilityEvent(host: View, eventType: Int) {
                if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                    if (mViewModel?.playerController?.isWholePlaying() == true) {
                        binding.floatButtonPanel.floatRedCircleIcon.contentDescription = resources.getString(
                            com.soundrecorder.common.R.string.recorder_pause
                        )
                    } else {
                        binding.floatButtonPanel.floatRedCircleIcon.contentDescription = resources.getString(
                            com.soundrecorder.common.R.string.recorder_play
                        )
                    }
                }
                super.sendAccessibilityEvent(host, eventType)
            }
        }
    }

    private fun initPlayContainer() {
        binding.rootView.setContainerFragment(this)
    }

    private fun initPanelOverlay() {
        binding.panelOverlay.setContainerFragment(this)
    }

    /**
     * 页初始化时间参数
     * */
    private fun initTime() {
        val seekToTime = getPlayerCurrentTimeMillis()
        binding.floatButtonPanel.apply {
            floatTvCurrent.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(seekToTime)
            floatTvCurrent.text =  seekToTime.currentHasHourFormatTimeExclusive(seekToTime)
            tvSeekTime.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(seekToTime)
            tvSeekTime.text =  seekToTime.currentHasHourFormatTimeExclusive(seekToTime)
            amplitudeSeekbar.updatePlayProgress(seekToTime)
        }
    }

    /**
     * 页面重构时初始化面板状态和时间参数
     * */
    private fun initFloatPanel() {
        initTime()
        mViewModel?.isFloatPanelState?.value?.let {
            //界面重建时需要等节布局绘制完成在切换到面板开关状态
            if (binding.floatButtonPanel.root.width == 0) {
                binding.floatButtonPanel.root.post {
                    expandCollapseAnimationHelper.startExpandCollapseAnimation(windowType, !it)
                }
            } else {
                expandCollapseAnimationHelper.startExpandCollapseAnimation(windowType, !it)
            }
        }
    }


    override fun onResume() {
        DebugUtil.i(TAG, "onResume")
        super.onResume()
        checkDeleteOrRenameDialogOnResume()
        resetDialogShowingState()
    }

    override fun onPause() {
        DebugUtil.i(TAG, "onPause")
        super.onPause()
        if (speakerModeController?.mIsSpeakerOn?.value == false) {
            mViewModel?.playerController?.pausePlay()
        }
        releasePopupWindow()
        recordDialogShowingState()
    }

    private fun recordDialogShowingState() {
        isShareDialogShowing = (mConvertManagerImpl as? ConvertManagerImpl)?.mExportHelper?.isShareDialogShowing() == true
        isShareOriginDialogShowing = (mConvertManagerImpl as? ConvertManagerImpl)?.mExportHelper?.isShareTextDialogShowing() == true
        isShareSummaryDialogShowing = (mConvertManagerImpl as? ConvertManagerImpl)?.mExportHelper?.isShareSummaryDialogShowing() == true
        shareSelectType = (mConvertManagerImpl as? ConvertManagerImpl)?.mExportHelper?.shareType() ?: -1
        DebugUtil.d(
            TAG, "recordDialogShowingState isShareDialogShowing = $isShareDialogShowing" +
                    ", isShareOriginDialogShowing = $isShareOriginDialogShowing" +
                    ", isShareSummaryDialogShowing = $isShareSummaryDialogShowing" +
                    ", shareSelectType = $shareSelectType"
        )
    }

    private fun resetDialogShowingState() {
        isShareDialogShowing = false
        isShareOriginDialogShowing = false
        isShareSummaryDialogShowing = false
        shareSelectType = -1
    }

    private fun cacheDialogShowing() {
        mViewModel?.isExportDialogShowing = isShareDialogShowing
        mViewModel?.isExportTextDialogShowing = isShareOriginDialogShowing
        mViewModel?.isExportSummaryDialogShowing = isShareSummaryDialogShowing
        mViewModel?.shareSelectType = shareSelectType
    }

    private fun checkRestoreShareDialog() {
        DebugUtil.d(TAG, "checkRestoreShareDialog fromRestore = $fromRestore")
        if (fromRestore.not()) {
            return
        }
        checkRestoreShowShareDialog()
        checkRestoreShowShareTextDialog()
    }

    private fun checkRestoreShowShareDialog() {
        DebugUtil.d(TAG, "checkRestoreShowShareDialog ${mViewModel?.isExportDialogShowing}")
        if (mViewModel?.isExportDialogShowing == true) {
            mConvertManagerImpl?.processExport2(null, shareDialogDismissListener, this)
            mViewModel?.isExportDialogShowing = false
        }
    }

    private fun checkRestoreShowShareTextDialog() {
        val viewModel = mViewModel ?: return
        DebugUtil.d(
            TAG, "checkRestoreShowShareTextDialog ${viewModel.isExportTextDialogShowing}" +
                    ", ${viewModel.isExportSummaryDialogShowing}"
        )
        when {
            viewModel.isExportTextDialogShowing -> {
                DebugUtil.d(TAG, "isExportTextDialogShowing")
                (mConvertManagerImpl as? ConvertManagerImpl)?.mExportHelper?.restoreShareTextDialog(
                    childFragmentManager,
                    viewModel.shareSelectType
                )
                viewModel.isExportTextDialogShowing = false
                viewModel.shareSelectType = -1
            }

            viewModel.isExportSummaryDialogShowing -> {
                DebugUtil.d(TAG, "isExportSummaryDialogShowing")
                (mConvertManagerImpl as? ConvertManagerImpl)?.mExportHelper?.restoreShareSummaryDialog(
                    childFragmentManager,
                    viewModel.shareSelectType
                )
                viewModel.isExportSummaryDialogShowing = false
                viewModel.shareSelectType = -1
            }
        }
    }

    private fun checkDeleteOrRenameDialogOnResume() {
        mViewModel?.let {
            if (it.isNeedSmartName && it.needSmartNameMediaList.isNotEmpty()) {
                startConvertAndSmartName(it.needSmartNameMediaList)
            }
        }
        if (mRenameDialog?.getOperating() == true) {
            DebugUtil.i(TAG, "check resume  rename")
            if (PermissionUtils.hasFilePermissionCompat()) {
                val renameContent = mRenameDialog?.getRenameContent() ?: ""
                renameAgain(mViewModel?.playerController?.getPlayUri(), renameContent)
            }
            mRenameDialog?.resetOperating()
        }

        if (mDeleteDialog?.getOperating() == true) {
            DebugUtil.i(TAG, "check resume delete")
            if (PermissionUtils.hasFilePermissionCompat()) {
                val activity = activity ?: return
                val record = mViewModel?.getRecord() ?: return
                val isRecycle = mViewModel?.isRecycle ?: return
                val success = FileDealUtil.deleteRecord(activity, record, REQUEST_CODE_SYS_DELETE_AUTH, isRecycle)
                if (success) {
                    onDeleteRecordSuccess(isRecycle)
                    DeleteSoundEffectManager.getInstance().playDeleteSound()
                }
            }
            mDeleteDialog?.resetOperating()
        }

        if (mRecoverDialog?.getOperating() == true) {
            DebugUtil.i(TAG, "check resume ")
            if (PermissionUtils.hasFilePermissionCompat()) {
                val activity = activity ?: return
                val success = FileDealUtil.recoveryRecord(activity, getOperaRecord(), REQUEST_CODE_SYS_DELETE_AUTH)
                if (success) {
                    onRecoverRecordSuccess()
                }
            }
            mRecoverDialog?.resetOperating()
        }
    }

    private fun renameAgain(uri: Uri?, renameContent: String): Boolean {
        val suffix = mViewModel?.getRecordSuffix()
        if (FileDealUtil.renameAgain(uri, renameContent, suffix)) {
            mViewModel?.renameRecordByCore2Full(renameContent)
            notifyRefreshRecordList()
            return true
        }
        return false
    }

    private fun initStartLauncher() {
        //registerForActivityResult instead of  startActivityForResult
        mStartEditLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result: ActivityResult? ->
            when (result?.resultCode) {
                Activity.RESULT_OK -> {

                    if (result.data?.getBooleanExtra(Constants.KEY_IS_CLIPPED_SAVE, false) == true) {
                        val newMediaId = result.data?.getLongExtra(Constants.KEY_CLIPPED_SAVE_RECORD_MEDIA_ID, -1) ?: -1
                        val isSmallWindow =
                            browseFileApi?.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.value == WindowType.SMALL
                        val playModel = if ((newMediaId > 0) && (!isSmallWindow)) {
                            /*裁切成功，若为父子级布局，则播放页面更新为裁切音频*/
                            StartPlayModel(newMediaId).apply {
                                isFromOtherApp = mViewModel?.mIsFromOtherApp ?: false
                            }
                        } else {
                            null
                        }
                        // 中大屏裁切保存新文件，右侧播放更新为新文件详情,小屏下保存成功回到列表页面
                        browseFileApi?.setViewModelPlayData(mBrowseFileActivityViewModel, playModel)
                        notifyCutNewRecordChange()
                        if (isFromAppCard()) {
                            startToBrowseFile()
                        } else {
                            if (isSmallWindow) {
                                DebugUtil.d(TAG, "remove play fragment")
                                // 小屏从裁切回来,直接移除播放fragment，避免移除动效不执行，fragment没被移除
                                activity?.supportFragmentManager?.commit { remove(this@PlaybackContainerFragment) }
                            }
                        }
                    }
                }

                Constants.RESULT_CODE_FILEOBSERVER_FINISH -> browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
            }
        }
    }

    private fun notifyCutNewRecordChange() {
        val intent = Intent(RecordFileChangeNotify.FILE_CUT_NEW_RECORD_ACTION)
        intent.putExtra(Constants.FRESH_FLAG, true)
        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
    }

    private fun startToBrowseFile() {
        activity?.let {
            browseFileApi?.createBrowseFileIntent(it)?.run {
                addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                startActivity(this)
            }
            it.finish()
        }
    }

    private fun initHeader() {
        playbackHeaderHelper.initHeader()
    }

    private fun initTab(isFromRestore: Boolean) {
        pagerMediator.initTabMediator(isFromRestore)
    }

    private fun clickTransferTextSelectTab() {
        if (!recordFilterIsRecycle()) {
            pagerMediator.selectConvertPage()
        }
    }

    private fun initAmpSeekBar() {
        binding.floatButtonPanel.amplitudeSeekbar.playPointMoveListener = playAmpSeekbarPointMoveListener
    }

    private fun initBrowseActivityViewModelObserver() {
        browseFileApi?.getViewModelPlayData<StartPlayModel>(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            onPlayModelChange(it)
        }
        browseFileApi?.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe current windowType=$it")
            windowType = it
            if (FeatureOption.getIsFoldFeature()) {
                mViewModel?.mShowActivityControlView?.value = false
            } else {
                mViewModel?.mShowActivityControlView?.value = (it == WindowType.LARGE)
            }
            /*小屏且未处于转文本搜索页面才显示返回箭头*/
            setNavigationIcon()
            binding.floatButtonPanel.floatRedCircleIcon.windowType = it
            mPlayStateChangeObserver.onChanged(mViewModel?.playerController?.playerState?.value ?: PlayStatus.PLAYER_STATE_INIT)
            /*windowType发生变化，更新下*/
            if (mViewModel?.isImmersiveState?.value == true) {
                immersiveAnimationHelper.checkUpdateWindowTypeChanged(navigationHeight, windowType = it)
            }
        }

        mViewModel?.isImmersiveState?.observe(viewLifecycleOwner) {
            switchImmersive(it)
        }

        //浮窗面板展开时更新一下时间
        mViewModel?.isFloatPanelState?.observe(viewLifecycleOwner) {
            if (it) initTime()
        }
    }


    private fun onPlayModelChange(model: StartPlayModel?) {
        model ?: run {
            DebugUtil.i(TAG, "onPlayModelChange play data to be null")
            mViewModel?.cancelLoadData()
            return
        }
        if (model.mediaId == mViewModel?.recordId) {
            DebugUtil.i(TAG, "onPlayModelChange play data id not change,id=${model.mediaId}")
            initConvertManager()
            pagerMediator.setConvertImmersiveAnimationHelper(immersiveAnimationHelper)
            return
        }
        DebugUtil.i(TAG, "onPlayModelChange play record data changed to $model")
        //音频发生变化
        mViewModel?.startPlayModel = model
        mViewModel?.autoPlay = model.autoPlay
        mPlaybackConvertViewModel?.isCheckSpeakerRole = model.isCheckSpeakerRole
        initConvertManager()
        onPermissionGranted()
        pagerMediator.setConvertImmersiveAnimationHelper(immersiveAnimationHelper)
    }

    private fun setNavigationIcon() {
        val isConvertSearch = mPlaybackConvertViewModel?.mIsInConvertSearch?.value
        DebugUtil.d(TAG, "setNavigationIcon, isInConvertSearch:$isConvertSearch")
        if (isFromAppCard() || (browseFileApi?.isSmallWindow(mBrowseFileActivityViewModel) == true && (isConvertSearch != true))) {
            binding.toolbar.setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
        } else {
            binding.toolbar.navigationIcon = null
        }
    }

    private fun initViewModelObserver() {
        mTimerTickCallback = object : TimerTickCallback {
            override fun onTimerTick(timeTickMillis: Long) {
                mViewModel?.onTimerRefreshTime(timeTickMillis)
            }
        }
        mViewModel?.playerController?.addTimerTickListener(mTimerTickCallback)

        mViewModel?.markEnable?.observe(viewLifecycleOwner) {
            refreshMarkEnable(it)
        }
        mViewModel?.playerController?.currentTimeMillis?.observe(viewLifecycleOwner) { currentTime ->
            refreshCurPlayTime(currentTime)
            if (mViewModel?.playerController?.mIsTouchSeekbar?.value == false) {
                /*若有target时间片段，未在时间片内，矫正播放时间片内数据*/
                mViewModel?.correctPlayTime(currentTime, mViewModel?.targetPlaySegment?.value) { seekTo, findCurOrNextNull ->
                    if (findCurOrNextNull && mViewModel?.playerController?.isWholePlaying() == true) {
                        mViewModel?.playerController?.doPausePlay()
                    }
                    mViewModel?.playerController?.seekTime(seekTo)
                }
            }
        }
        mViewModel?.playerController?.mDuration?.observe(viewLifecycleOwner) { duration ->
            binding.floatButtonPanel.apply {
                floatTvDuration.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(duration)
                floatTvDuration.text = duration.durationHasHourFormatTimeExclusive(true)
                amplitudeSeekbar.totalTime = duration
            }
        }
        //observe the playState forever, remember to release it
        mViewModel?.playerController?.playerState?.observeForever(mPlayStateChangeObserver)
        mViewModel?.playerController?.playSpeedIndex?.observe(viewLifecycleOwner) {
            mViewModel?.playerController?.changePlayerSpeed()
        }

        val loadAmpObserver = Observer<Boolean> {
            if (it) {
                if (browseFileApi?.getViewModelAnimRunning(mBrowseFileActivityViewModel)?.value != true) {
                    // 小屏动效执行完
                    PermissionUtils.checkNotificationPermission(activity)
                    handleViewHierarchy(false)
                }
            }
        }
        mViewModel?.mIsDecodeReady?.observe(viewLifecycleOwner, loadAmpObserver)
        mViewModel?.isPrepareAmplitudeAndMark?.observe(viewLifecycleOwner, loadAmpObserver)
        /*animEnd默认为null，作用等同于true的*/
        browseFileApi?.getViewModelAnimRunning(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            if (!it && (mViewModel?.loadAmpSuccess() == true)) {
                PermissionUtils.checkNotificationPermission(activity)
                handleViewHierarchy(false)
            }
        }

        browseFileApi?.getDeleteSummaryNoteIdLiveData(mBrowseFileActivityViewModel)?.observe(viewLifecycleOwner) {
            mViewModel?.handleClearSummaryCallId(it)
        }

        mPlaybackConvertViewModel?.isSpeakerRoleShowing?.observe(viewLifecycleOwner) { needShowRole ->
            mViewModel?.mPanelShowStatus?.value?.let { showStatus ->
                handlePanelShowStatus(showStatus)
            } ?: kotlin.run {
                DebugUtil.d(TAG, "isSpeakerRoleShowing mPanelShowStatus is null")
            }

            if (needShowRole) {
                mConvertRoleMenuItem?.title = getString(com.soundrecorder.common.R.string.export_hide_speaker)
            } else {
                mConvertRoleMenuItem?.title = getString(com.soundrecorder.common.R.string.export_show_speaker)
            }
            if (needShowRole) {
                getConvertFragment()?.stopScroll()
            } else {
                mConvertManagerImpl?.removeSpeakerTipTask()
                /*关闭讲话人，重置目标静音片、筛选讲话人数据*/
                mPlaybackConvertViewModel?.selectSpeakerList = null
                mViewModel?.targetPlaySegment?.value = null
            }
        }

        mViewModel?.mPanelShowStatus?.observe(viewLifecycleOwner) {
            handlePanelShowStatus(it)
        }

        mPlaybackConvertViewModel?.mConvertStatus?.observe(viewLifecycleOwner) {
            DebugUtil.i(TAG, "convertViewModelChange mConvertStatus changed: $it")
            handleViewHierarchy(mViewModel?.loadAmpSuccess() != true)
        }

        mViewModel?.mMimeType?.observe(viewLifecycleOwner) {
            mCutMenuItem?.isVisible = isSupportAudioCut(it)
        }
        mPlaybackConvertViewModel?.mIsInConvertSearch?.observe(viewLifecycleOwner) {
            setNavigationIcon()
            inAndOutConvertSearchFragment(it)
            browseFileApi?.onConvertSearchStateChanged(activity, it, !mPlaybackConvertViewModel?.mConvertSearchValue.isNullOrBlank()) {
                getConvertSearchFragment()?.onClickCancel()
            }
        }
        mViewModel?.targetPlaySegment?.observe(viewLifecycleOwner) { timeSegmentList ->
            if (timeSegmentList?.isNotEmpty() == true) {
                /*
                设置目标静音片后,若当前暂停状态，需要矫正当前时间在时间片内
                */
                mViewModel?.run {
                    resetCurrentPlaySegment()
                    if (playerController.hasPaused()) {
                        correctPlayTime(playerController.currentTimeMillis.getValueWithDefault(), timeSegmentList) { seekTo, _ ->
                            DebugUtil.i(TAG, " target changed, seek to $seekTo")
                            playerController.seekTime(seekTo)
                        }
                    }
                }
            }
        }

        initSpeakerMenuObservers()
        initDialog()
        initRecordObserver()
        initAmp90Observer()
        initPlayNameObserver()
    }

    private fun initPlayNameObserver() {
        mViewModel?.getPlayerName()?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initPlayNameObserver it = $it")
            if (binding.flConvertAudioContainer.isVisible) {
                binding.toolbar.title = it.title()
            } else {
                playbackHeaderHelper.refreshName(it)
            }
        }
    }

    private fun initAmp90Observer() {
        mViewModel?.ampList90?.observe(viewLifecycleOwner) { ampList90 ->
            if (!ampList90.isNullOrEmpty()) {
                binding.floatButtonPanel.amplitudeSeekbar.amplitudes = ampList90
            }
        }
    }

    private fun initRecordObserver() {
        mViewModel?.mRecordLiveData?.observe(viewLifecycleOwner) { record ->
            DebugUtil.d(TAG, "initRecordObserver record = $record")
            bindHeadData(record)
        }
    }

    private fun bindHeadData(record: Record) {
        val mediaId = mViewModel?.recordId ?: return
        val name = record.displayName ?: return
        val duration = record.duration
        playbackHeaderHelper.bindHeadData(mediaId, name, duration)
    }

    private fun handlePanelShowStatus(showStatus: PlaybackContainerViewModel.PanelShowStatus) {
        if (recordFilterIsRecycle()) {
            /*隱藏倍速等*/
            mPlaySettingMenuItem?.isVisible = false
            mCutMenuItem?.isVisible = false
        } else {
            setVisibleMenu(showStatus.mHasConvertContent, showStatus.mConvertShowSwitch)
        }
    }

    private fun setVisibleMenu(hasConvertContent: Boolean, roleVisible: Boolean) {
        val convertAndHasConvertContent = isOnConvert() && hasConvertContent
        // 讲话人
        if ((convertAndHasConvertContent && roleVisible) != mConvertRoleMenuItem?.isVisible) {
            mConvertRoleMenuItem?.isVisible = (convertAndHasConvertContent && roleVisible)
        }
        // 内容搜索
        if (convertAndHasConvertContent != mSearchMenuItem?.isVisible) {
            mSearchMenuItem?.isVisible = convertAndHasConvertContent
        }
    }

    /**
     * 是否支持裁切
     * @param mimeType 音频文件mimeType
     * @return boolean true: 支持 false：不支持
     */
    private fun isSupportAudioCut(mimeType: String?): Boolean {
        var supportMime = arrayOf(
            RecordConstant.MIMETYPE_MP3,
            RecordConstant.MIMETYPE_AMR,
            RecordConstant.MIMETYPE_AMR_WB,
            RecordConstant.MIMETYPE_3GPP
        )
        // 品牌融合后，支持aac和wav
        supportMime = supportMime
            .plus(RecordConstant.MIMETYPE_ACC)
            .plus(RecordConstant.MIMETYPE_ACC_ADTS)
            .plus(RecordConstant.MIMETYPE_WAV)
        return (mimeType in supportMime)
    }

    private fun refreshCurPlayTime(seekToTime: Long) {
        binding.floatButtonPanel.apply {
            if (floatTvCurrent.isVisible) {
                floatTvCurrent.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(seekToTime)
                floatTvCurrent.text = seekToTime.currentHasHourFormatTimeExclusive(seekToTime)
            }
            if (tvSeekTime.isVisible) {
                tvSeekTime.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(seekToTime)
                tvSeekTime.text = seekToTime.currentHasHourFormatTimeExclusive(seekToTime)
            }
            if (isVisible) {
                amplitudeSeekbar.updatePlayProgress(seekToTime)
            }
        }
    }

    private fun refreshMarkEnable(isMarkEnable: Boolean) {
        val isEnable = isMarkEnable && (mPictureMarkHelper?.checkNeedAddMark() == true)
        val backgroundDrawable = if (isEnable) {
            AppCompatResources.getDrawable(BaseApplication.getAppContext(), R.drawable.ic_mark_icon_new)
        } else {
            AppCompatResources.getDrawable(BaseApplication.getAppContext(), R.drawable.ic_mark_icon_disable_new)
        }

        backgroundDrawable?.let {
            binding.floatButtonPanel.floatImgMarkAdd.setImageDrawable(it)
        }
    }

    private fun getMarkButtonEnable(): Boolean {
        return (mViewModel?.markEnable?.value ?: false) && (mPictureMarkHelper?.checkNeedAddMark() ?: false)
    }

    private fun initMarkList(savedInstanceState: Bundle?) {
        mMarkListAdapter = MarkListAdapter(activity, true).apply {
            setOnDeleteListener {
                val index = mViewModel?.getMarkList()?.value?.indexOf(it) ?: return@setOnDeleteListener
                mViewModel?.removeMark(index)
                mConvertManagerImpl?.updateMark()
                if (getAudioFragment()?.isResumed == true) {
                    getAudioFragment()?.mBinding?.waveRecyclerview?.removeMarkData = it
                }
            }
            setOnMarkClickListener { data ->
                if (getAudioFragment()?.isResumed == true) {
                    getAudioFragment()?.mBinding?.waveRecyclerview?.stopScroll()
                }
                markListDialogFragment?.setBottomSheetDialogFragmentState(COUIBottomSheetBehavior.STATE_COLLAPSED)
                mViewModel?.needSyncRulerView = true
                mViewModel?.seekTime(data.correctTime)
                mMarkListAdapter?.setCheck(data.timeInMills)
                mMarkListAdapter?.notifyDataSetChanged()
                BuryingPoint.seekToMarkTagWhenPlayback(mViewModel?.recordType?.value)
            }
            setOnRenameMarkListener(mViewModel?.onRenameMarkListener)
            setEditMarkDialogListener {
                when (it) {
                    ButtonType.TAKE_PHOTO -> {
                        getIPictureMarkDelegate()?.launchTakePhotoSingle(IPictureMarkDelegate.SOURCE_CAMERA, System.currentTimeMillis())
                    }

                    ButtonType.SELECT_PICTURE -> {
                        getIPictureMarkDelegate()?.launchTakePhotoSingle(IPictureMarkDelegate.SOURCE_ALBUM, System.currentTimeMillis())
                    }
                }
            }
        }
        val systemCacheMarkListFragment = childFragmentManager.findFragmentByTag(MARK_LIST_FRAGMENT_TAG)
        if (savedInstanceState != null && systemCacheMarkListFragment != null) {
            val showStatus = savedInstanceState.getString(MARK_LIST_SHOW_STATE, "")
            DebugUtil.w(TAG, "initMarkList $showStatus")
            if (systemCacheMarkListFragment is COUIBottomSheetDialogFragment && showStatus == MARK_LIST_SHOW) {
                systemCacheMarkListFragment.dismiss()
            }
        }
    }

    private fun initDialog() {
        mViewModel?.mNeedShowRecoverDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showRecoverDialog()
            }
        }
        mViewModel?.mNeedShowRecycleDeleteDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showDeleteDialog(cloudTipManagerAction?.isCloudSwitchOn() == true, true)
            }
        }
        mViewModel?.mNeedShowDeleteDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showDeleteDialog(cloudTipManagerAction?.isCloudSwitchOn() == true, false)
            }
        }
        mViewModel?.mNeedShowDetailDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showDetailDialog()
            }
        }
        mViewModel?.mNeedShowSpeedDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                showPlaySettingDialog()
            }
        }
        mViewModel?.mNeedShowShareDialog?.observe(viewLifecycleOwner) { needShow ->
            if (needShow) {
                processExport(null)
            }
        }
    }

    private fun handleViewHierarchy(loadingShow: Boolean) {
        DebugUtil.d(TAG, "handleViewHierarchy loadingShow:$loadingShow")
        if (loadingShow) {
            mLoadingViewControl?.handleLoading(true)
            binding.flConvertAudioContainer.isVisible = false
            binding.viewpager.isVisible = false
        } else {
            val convertStatus = mPlaybackConvertViewModel?.mConvertStatus?.value
            DebugUtil.e(TAG, "handleViewHierarchy convertStatus:$convertStatus")
            when (convertStatus) {
                // 转写中
                CONVERT_STATUS_PROGRESS -> {
                    binding.flConvertAudioContainer.isVisible = false
                    binding.viewpager.isVisible = true
                    binding.viewpager.isUserInputEnabled = false
                }
                //转写完成
                CONVERT_STATUS_COMPLETE,
                //有便签摘要，但是没有转文本
                CONVERT_STATUS_SUMMARY_NONE_COMPLETE,
                //NONE_COMPLETE 界面超时
                CONVERT_STATUS_USER_TIMEOUT_NOT_COMPLETE -> {
                    binding.flConvertAudioContainer.isVisible = false
                    binding.viewpager.isVisible = true
                    binding.viewpager.isUserInputEnabled = true
                    binding.floatButtonPanel.panel.isVisible = true
                    mViewModel?.mPanelShowStatus?.value?.let { showStatus ->
                        handlePanelShowStatus(showStatus)
                    } ?: run {
                        DebugUtil.d(TAG, "mPanelShowStatus is null")
                    }
                }

                else -> {
                    /*
                    CONVERT_STATUS_INIT, CONVERT_STATUS_CANCEL, CONVERT_STATUS_USERTIMEOUT , null值都要如此处理
                     */
                    binding.flConvertAudioContainer.isVisible = true
                    binding.viewpager.isVisible = false
                    binding.viewpager.isUserInputEnabled = true
                }
            }
            updateHeadAndTab()
            addOrRemoveAudioFragment()
            addButtonPanel()
            pagerMediator.selectCurrentPage()
            mLoadingViewControl?.handleLoading(false)
        }
    }

    private fun updateHeadAndTab() {
        playbackHeaderHelper.checkShowHead()
        pagerMediator.updateTab()
    }

    private fun addButtonPanel() {
        DebugUtil.d(TAG, "addButtonPanel ${binding.flConvertAudioContainer.isVisible}")
        val buttonPanelNeedShow = binding.flConvertAudioContainer.isVisible
        if (buttonPanelNeedShow) {
            buttonPanelControl.addButtonPanel()
            buttonPanelControl.updateLayoutParam(navigationHeight)
            buttonPanelControl.observerButtonPanelHeightChange {
                getAudioFragment()?.updatePaddingBottom(it + navigationHeight)
            }
        }
    }

    private fun removeButtonPanel() {
        DebugUtil.d(TAG, "removeButtonPanel ${binding.flConvertAudioContainer.isVisible}")
        val convertStatus = mPlaybackConvertViewModel?.mConvertStatus?.value
        val removeButtonPanel = convertStatus == CONVERT_STATUS_COMPLETE
                || convertStatus == CONVERT_STATUS_SUMMARY_NONE_COMPLETE
        if (removeButtonPanel) {
            buttonPanelControl.removeButtonPanel()
        } else {
            DebugUtil.e(TAG, "why convertStatus = $convertStatus")
        }
    }

    fun showConvertEmpty() {
        DebugUtil.d(TAG, "showConvertEmpty")
        removeButtonPanel()
        binding.toolbar.titleView?.alpha = 1f
        binding.toolbar.title = ""
    }

    fun showConverting() {
        DebugUtil.d(TAG, "showConverting")
    }

    fun showConvertInit() {
        DebugUtil.d(TAG, "showConvertInit")
        removeButtonPanel()
        binding.toolbar.titleView?.alpha = 1f
        binding.toolbar.title = ""
    }

    fun showConvertContent() {
        DebugUtil.d(TAG, "showConvertContent")
        removeButtonPanel()
        binding.toolbar.titleView?.alpha = 1f
        binding.toolbar.title = ""
    }

    /**
     * 检查并设置加载状态和Tab显示
     * 注意：此方法不再处理seekBar的启用状态，该逻辑已移至updateSeekBarState方法
     */
    private fun checkShowLoading() {
        if (mLoadingViewControl == null) {
            mLoadingViewControl = LoadingViewControl(lifecycle, binding.colorLoadView.colorLoadView)
        }
        handleViewHierarchy(true)
    }

    private fun initSpeakerMenuObservers() {
        speakerModeController = browseFileApi?.getViewModelSpeakerModeController<SpeakerModeController>(mBrowseFileActivityViewModel)
        speakerModeController?.mSpeakerUiMode?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initSpeakerMenuObservers the mode is $it")
            changeActivityVolumeStream(
                if (it == SpeakerStateManager.SPEAKER_OFF_WITHOUT_HEADSET) {
                    AudioManager.STREAM_VOICE_CALL
                } else AudioManager.STREAM_MUSIC
            )

            when (it) {
                SpeakerStateManager.SPEAKER_OFF_WITHOUT_HEADSET -> {
                    val text = if (recordFilterIsRecycle()) {
                        mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_small_speaker_red)
                        getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                    } else {
                        mSpeakerMenuItem?.icon = null
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    }
                    mSpeakerMenuItem?.title = text
                    mSpeakerMenuItem?.contentDescription = text
                    mSpeakerMenuItem?.isEnabled = true
                }

                SpeakerStateManager.SPEAKER_ON_WITHIN_HEADSET -> {
                    /*don't be grey on androidS*/
                    val text = if (recordFilterIsRecycle()) {
                        val icon = if (BaseUtil.isAndroidSOrLater) {
                            com.soundrecorder.common.R.drawable.ic_big_speaker_black
                        } else {
                            com.soundrecorder.common.R.drawable.ic_big_speaker_gray
                        }
                        mSpeakerMenuItem?.setIcon(icon)
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    } else {
                        mSpeakerMenuItem?.icon = null
                        getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                    }
                    mSpeakerMenuItem?.title = text
                    mSpeakerMenuItem?.contentDescription = text
                }

                SpeakerStateManager.SPEAKER_OFF_WITHIN_HEADSET -> {
                    /*don't be grey on androidS*/
                    val text = if (recordFilterIsRecycle()) {
                        val icon = if (BaseUtil.isAndroidSOrLater) {
                            com.soundrecorder.common.R.drawable.ic_small_speaker_red
                        } else {
                            com.soundrecorder.common.R.drawable.ic_small_speaker_gray
                        }
                        mSpeakerMenuItem?.setIcon(icon)
                        getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                    } else {
                        mSpeakerMenuItem?.icon = null
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    }
                    mSpeakerMenuItem?.title = text
                    mSpeakerMenuItem?.contentDescription = text
                }

                SpeakerStateManager.SPEAKER_ON_WITHOUT_HEADSET -> {
                    val text = if (recordFilterIsRecycle()) {
                        mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_big_speaker_black)
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    } else {
                        mSpeakerMenuItem?.icon = null
                        getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                    }
                    mSpeakerMenuItem?.title = text
                    mSpeakerMenuItem?.contentDescription = text
                    mSpeakerMenuItem?.isEnabled = true
                }

                else -> {
                    val text = if (recordFilterIsRecycle()) {
                        mSpeakerMenuItem?.setIcon(com.soundrecorder.common.R.drawable.ic_big_speaker_black)
                        getString(com.soundrecorder.common.R.string.talk_back_speaker_play)
                    } else {
                        mSpeakerMenuItem?.icon = null
                        getString(com.soundrecorder.common.R.string.talk_back_handset_play)
                    }
                    mSpeakerMenuItem?.title = text
                    mSpeakerMenuItem?.contentDescription = text
                    mSpeakerMenuItem?.isEnabled = true
                }
            }
        }

        speakerModeController?.mIsSpeakerOn?.observe(viewLifecycleOwner) {
            mViewModel?.playerController?.replay()
        }
        speakerModeController?.mPlayerCommand?.observe(viewLifecycleOwner) {
            if (it == SpeakerModeController.PLAYER_COMMAND_PAUSE) {
                mViewModel?.playerController?.pausePlay()
            }
        }
    }

    private fun changeActivityVolumeStream(streamType: Int? = AudioManager.STREAM_MUSIC) {
        DebugUtil.d(TAG, "changeActivityVolumeStream  $streamType")
        activity?.volumeControlStream = streamType ?: AudioManager.STREAM_MUSIC
    }

    fun handleMarkAdd(v: View) {
        DebugUtil.i(TAG, "handleMarkAdd begin.")
        if (mViewModel?.checkLoadAmpFinished() != true) {
            // 波形还未解析出来，不能使用标记功能
            DebugUtil.d(TAG, "checkLoadAmpFinished false")
            return
        }

        if (mViewModel?.checkMarkNumberIsExceed() == true) {
            DebugUtil.w(TAG, "handleMarkAdd mark number is exceed.")
            ToastManager.showShortToast(BaseApplication.getAppContext(), com.soundrecorder.common.R.string.photo_mark_recommend_mark_limit)
            return
        }

        if (!getMarkButtonEnable()) {
            DebugUtil.w(TAG, "handleMarkAdd mark button is disable.")
            return
        }

        mPictureMarkHelper?.saveSelectMarkTime()
        DebugUtil.d(TAG, "onClick layout_mark_activity:")
        val markMetaData = MarkMetaData("", "", currentTimeMillis = mPictureMarkHelper?.getSelectMarkTime() ?: 0, -1, -1)
        val result = mViewModel?.addMark(false, markMetaData) ?: -1
        if (result >= 0) {
            getAudioFragment()?.mBinding?.waveRecyclerview?.addMarkData = mViewModel?.markHelper?.getMark(result)
            mConvertManagerImpl?.updateMark()
        }
        BuryingPoint.addClickPlayTextMark()
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.float_img_mark_add -> handleMarkAdd(v)
            R.id.float_img_forward -> handleClickForward()
            R.id.float_img_backward -> handleClickBackward()
            R.id.float_mark_list -> handleClickMarkList()
            R.id.float_red_circle_icon -> handleClickRedCircleIcon()
            R.id.img_wave_form_bg -> handleClickWave()
        }
    }

    fun handleClickForward() {
        if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用快进功能
            DebugUtil.d(TAG, "checkLoadAmpFinished false")
            return
        }
        BuryingPoint.addFastForward()
        mViewModel?.forwardOrBackWard(true)
    }

    fun handleClickBackward() {
        if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用快退功能
            DebugUtil.d(TAG, "checkLoadAmpFinished false")
            return
        }
        BuryingPoint.addFastBack()
        mViewModel?.forwardOrBackWard(false)
    }

    fun handleClickMarkList() {
        val markListShowState = mViewModel?.isShowMarkList?.value.let { if (it == null) true else !it }
        mViewModel?.isShowMarkList?.postValueSafe(markListShowState)
    }

    fun handleClickRedCircleIcon() {
        if (recordFilterIsRecycle() && !PermissionUtils.hasAllFilePermission()) {
            activity?.let {
                PermissionDialogUtils.showPermissionAllFileAccessDialog(
                    it,
                    object : PermissionDialogUtils.PermissionDialogListener {
                        override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
                            if (isOk) {
                                PermissionUtils.goToAppAllFileAccessConfigurePermissions(it)
                            }
                        }
                    })
                return
            }
        }
        if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用播放功能
            DebugUtil.d(TAG, "checkLoadAmpFinished false")
            return
        }
        mViewModel?.recordControlClick()
    }

    private fun handleClickWave() {
        expandCollapseAnimationHelper.startExpandCollapseAnimation(windowType, mViewModel?.isFloatPanelState?.value ?: false)
    }


    fun handleTransferText() {
        if (!ClickUtils.isQuickClick()) {
            DebugUtil.d(TAG, "onClick layout_transfer_text_activity: ${mPlaybackConvertViewModel?.mConvertStatus?.value}")
            clickTransferTextSelectTab()
            mPlaybackConvertViewModel?.mConvertStatus?.value?.let {
                if (it in intArrayOf(CONVERT_STATUS_INIT, CONVERT_STATUS_CANCEL, CONVERT_STATUS_USER_TIMEOUT)) {
                    isClickAiTitle = false
                    mConvertManagerImpl?.convertStartClickHandle()
                }
            }
        }
    }

    private fun processExport(anchor: View?) {
        if (ClickUtils.isQuickClick()) {
            return
        }
        mConvertManagerImpl?.processExport2(
            anchor,
            shareDialogDismissListener,
            this
        )
    }

    private fun showRecoverDialog() {
        if (mRecoverDialog?.isShowing() == true) {
            DebugUtil.i(TAG, "mDeleteDialog is showing")
            return
        }
        mRecoverDialog =
            activity?.let {
                RecoverFileDialog(
                    it,
                    getString(com.soundrecorder.common.R.string.recycle_tips_restore_record_title),
                    getString(com.soundrecorder.common.R.string.recycle_delete_recover)
                )
            }
        mRecoverDialog?.mOnFileRecoverListener = object : OnRecoverFileListener {
            override fun onRecoverFileResult(deleteSuccess: Boolean) {
                if (deleteSuccess) {
                    onRecoverRecordSuccess()
                }
            }

            override fun provideRecoverRequestCode(): Int {
                return REQUEST_CODE_SYS_RECOVER_AUTH
            }
        }
        mRecoverDialog?.mHideListener = OnDismissListener {
            if (mRecoverDialog != null) {
                mViewModel?.mNeedShowRecoverDialog?.postValue(false)
            }
        }
        mRecoverDialog?.showRecoverDialog(getOperaRecord())
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[RecorderUserAction.KEY_RECYCLE_RECOVER] = RecorderUserAction.VALUE_MORE_DELETE
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), RecorderUserAction.USER_ACTION_PLAY_MORE, RecorderUserAction.EVENT_PLAY_MORE,
            eventInfo, false
        )
    }

    private fun onRecoverRecordSuccess() {
        notifyRefreshRecordList()
        browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
        //埋点
        BuryingPoint.addPlayRecycleRecoverSuccess()
    }

    private fun showDeleteDialog(isCloudOn: Boolean, isRecycle: Boolean) {
        DebugUtil.d(TAG, "showDeleteDialog, isRecycle:$isRecycle")
        if (mDeleteDialog?.isShowing() == true) {
            DebugUtil.i(TAG, "mDeleteDialog is showing")
            return
        }
        val activity = activity ?: return
        mDeleteDialog = DeleteFileDialog(
            activity, getString(com.soundrecorder.common.R.string.record_delete_title),
            getDeleteDialogMessage(isCloudOn, isRecycle), getString(com.soundrecorder.common.R.string.delete)
        )
        mDeleteDialog?.mOnFileDeleteListener = object : OnFileDeleteListener {
            override fun onDeleteFileResult(deleteSuccess: Boolean) {
                if (deleteSuccess) {
                    VibrateUtils.vibrate(activity)
                    onDeleteRecordSuccess(isRecycle)
                }
            }

            override fun provideDeleteRequestCode(): Int {
                return REQUEST_CODE_SYS_DELETE_AUTH
            }
        }
        mDeleteDialog?.mHideListener = OnDismissListener {
            if (mDeleteDialog != null) {
                if (isRecycle) {
                    mViewModel?.mNeedShowRecycleDeleteDialog?.postValue(false)
                } else {
                    mViewModel?.mNeedShowDeleteDialog?.postValue(false)
                }
            }
        }

        mDeleteDialog?.showDeleteDialog(getOperaRecord(), isRecycle)
        // 埋点
        val eventInfoDelete: MutableMap<String?, String?> = HashMap()
        if (isRecycle) {
            eventInfoDelete[RecorderUserAction.KEY_RECYCLE_DELETE] = RecorderUserAction.VALUE_MORE_DELETE
        } else {
            eventInfoDelete[RecorderUserAction.KEY_MORE_DELETE] = RecorderUserAction.VALUE_MORE_DELETE
        }
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), RecorderUserAction.USER_ACTION_PLAY_MORE, RecorderUserAction.EVENT_PLAY_MORE,
            eventInfoDelete, false
        )
    }

    private fun getDeleteDialogMessage(isCloudOn: Boolean, isRecycle: Boolean): String {
        val message: String = if (isRecycle) {
            resources.getString(com.soundrecorder.common.R.string.the_record_will_be_deleted_from_device)
        } else {
            if (isCloudOn) { //开启云同步时
                resources.getString(com.soundrecorder.common.R.string.recycle_tips_cloud_recently_delete_record)
            } else {
                resources.getString(com.soundrecorder.common.R.string.recycle_tips_recently_delete_record)
            }
        }
        return message
    }

    private fun showDetailDialog() {
        val context = context ?: return
        val inflater = LayoutInflater.from(context)
        val view = inflater.inflate(R.layout.layout_playback_detail, null)
        val name = view.findViewById<TextView>(R.id.name)
        val time = view.findViewById<TextView>(R.id.time)
        val length = view.findViewById<TextView>(R.id.length)
        val path = view.findViewById<TextView>(R.id.path)
        val pathString: String?
        val pathStart = BaseUtil.getPhoneStorageDir(BaseApplication.getAppContext())
        val sdPathStart = BaseUtil.getSDCardStorageDir(BaseApplication.getAppContext())
        pathString = if (pathStart != null && (mViewModel?.playPath?.value?.startsWith(pathStart) == true)) {
            resources.getString(
                if (FeatureOption.IS_PAD) {
                    com.soundrecorder.common.R.string.device_phone_storage
                } else {
                    com.soundrecorder.common.R.string.phone_storage
                },
                mViewModel?.playPath?.value?.replace(pathStart, "")
            )
        } else if (mViewModel?.playPath?.value?.startsWith(sdPathStart) == true) {
            resources.getString(
                com.soundrecorder.common.R.string.sd_card_storage,
                mViewModel?.playPath?.value?.replace(sdPathStart, "")
            )
        } else {
            mViewModel?.playPath?.value
        }
        name.text = mViewModel?.playName?.value
        val date = Date(
            RecorderDBUtil.getInstance(BaseApplication.getAppContext()).getCreateTimeByPath(
                mViewModel?.recordId
                    ?: -1, mViewModel?.playName?.value?.endsWith(".amr") ?: false
            )
        )
        time.text = RecorderICUFormateUtils.formatDateTime(date)
        length.text = FileUtils.getSizeDescription(
            FileUtils.getFileSize(
                MediaDBUtils.genUri(
                    mViewModel?.recordId
                        ?: -1
                )
            )
        )
        path.text = pathString

        COUIAlertDialogBuilder(context).apply {
            setView(view)
            setTitle(com.soundrecorder.common.R.string.talkback_detail)
            setNegativeButton(com.soundrecorder.base.R.string.close) { _, _ ->
                mDeleteDialog?.dismiss()
            }
            setOnDismissListener {
                if (mDetailDialog != null) {
                    mViewModel?.mNeedShowDetailDialog?.postValue(false)
                }
            }
            setBlurBackgroundDrawable(true)
            mDetailDialog = show()
        }
        val eventInfoDetail: MutableMap<String?, String?> = HashMap()
        eventInfoDetail[RecorderUserAction.KEY_PLAY_MORE_DETAIL] = RecorderUserAction.VALUE_PLAY_MORE_DETAIL
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(), RecorderUserAction.USER_ACTION_PLAY_MORE, RecorderUserAction.EVENT_PLAY_MORE,
            eventInfoDetail, false
        )
    }

    private fun showRenameDialog(text: String? = null) {
        if (mRenameDialog?.isShowing() == true) {
            DebugUtil.i(TAG, "mRenameDialog is showing")
            return
        }
        val activity = activity ?: return
        val queryUri = MediaDBUtils.BASE_URI
        var cursor: Cursor? = null
        val where: String = MediaStore.Audio.Media._ID + "=?"
        var mediaRecord: Record? = null
        try {
            cursor = context?.contentResolver?.query(queryUri, null, where, arrayOf(mViewModel?.recordId.toString()), null)
            cursor?.let {
                if (it.moveToNext()) {
                    mediaRecord = Record(
                        cursor,
                        Record.TYPE_FROM_MEDIA
                    )
                }
            }
        } catch (ignored: Exception) {
            DebugUtil.e(TAG, "cursor is exception$ignored")
        } finally {
            cursor?.close()
        }

        val content = if (text == null) {
            var lastIndex = -1
            if (!TextUtils.isEmpty(mediaRecord?.displayName)
                && (mediaRecord?.displayName?.lastIndexOf(".").also { lastIndex = it ?: 0 } ?: 0) > 0
            ) {
                val content = mediaRecord?.displayName?.substring(0, lastIndex) ?: ""
                DebugUtil.i(TAG, "showRenameDialog, mRenameDialog content is $content")
                content
            } else {
                return
            }
        } else {
            text
        }

        mRenameDialog = RenameFileDialog(activity, RenameFileDialog.FROM_PLAYBACK_MORE, content, object : PositiveCallback {
            override fun callback(displayName: String?, path: String?) {
                DebugUtil.i(TAG, "rename callback displayName: $displayName, path: $path")
                var index = -1
                if (path == null || path.lastIndexOf(".").also { index = it } == -1) {
                    return
                }
                val recordName = displayName + path.substring(index)
                DebugUtil.i(TAG, "setValue: recordName = $recordName")
                mViewModel?.renameRecord(path, recordName)
                getSummaryFragment()?.onRecordNameStatusChange(true, displayName)
                mRenameDialog?.resetOperating()
                notifyRefreshRecordList()
                BuryingPoint.addActionForPlaybackRenameSuccess()
            }
        }).apply {
            this.requestCode = REQUEST_CODE_SYS_RENAME_AUTH
            this.mediaRecord = mediaRecord
        }
        mRenameDialog?.show()
        BuryingPoint.addActionForPlaybackRename()
    }

    private fun showPlaySettingDialog() {
        if (mViewModel == null) {
            return
        }
        if (bottomSheetDialogFragment?.dialog?.isShowing == true) {
            DebugUtil.i(TAG, "bottomSheetDialogFragment is showing")
            return
        }
        bottomSheetDialogFragment = COUIBottomSheetDialogFragment()
        val dialogFragment = PlaySettingDialogFragment()
        bottomSheetDialogFragment?.setMainPanelFragment(dialogFragment)
        if (isAdded) {
            bottomSheetDialogFragment?.show(childFragmentManager, PlaySettingDialogFragment.FRAGMENT_TAG)
        }
        mViewModel?.mNeedShowSpeedDialog?.value = false
    }

    private fun configToolbar() {
        binding.toolbar.clearMenu()
        if (recordFilterIsRecycle()) {
            showRecycleToolBar()
        } else {
            showNormalToolbar()
        }

        setNavigationIcon()
        binding.toolbar.setNavigationOnClickListener {
            if (isFromAppCard()) {
                activity?.finish()
            } else {
                browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
            }
        }
    }

    /**
     * 从小布建议卡跳转过来
     */
    private fun isFromAppCard(): Boolean {
        val from = activity?.intent?.getStringExtra("jumpFrom") ?: return false
        return from == Constants.CONVERT_TEXT_EXTRA_JUMP_FROM_CARD || from == Constants.AI_SUMMARY_EXTRA_JUMP_FROM_CARD
    }

    private fun showNormalToolbar() {
        binding.toolbar.inflateMenu(R.menu.menu_play_back)
        binding.toolbar.menuView?.apply {
            this.setItemSpecialColor(
                R.id.delete,
                COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorError)
            )
        }
        mSpeakerMenuItem = binding.toolbar.menu.findItem(R.id.speaker)
        mPlaySettingMenuItem = binding.toolbar.menu.findItem(R.id.play_setting)
        mCutMenuItem = binding.toolbar.menu.findItem(R.id.cut)
        mSearchMenuItem = binding.toolbar.menu.findItem(R.id.search)
        mConvertRoleMenuItem = binding.toolbar.menu.findItem(R.id.convert_role)

        lifecycleScope.launchWhenResumed {
            binding.toolbar.menu.apply {
                ensureConvertSearchAnimView(this)
                setShowSmartNameMenuView()
                if (FeatureOption.IS_PAD) {
                    mSpeakerMenuItem?.isVisible = false
                }
                this.children.forEach {
                    it.setOnMenuItemClickListener { menuItem ->
                        onOptionsItemSelected(menuItem)
                    }
                }
            }
        }
    }

    private fun setShowSmartNameMenuView() {
        mSupportSmartName = Injector.injectFactory<BrowseFileInterface>()?.isSupportSmartName(mBrowseFileActivityViewModel) ?: false
        DebugUtil.d(TAG, "setShowSmartNameMenuView, supportSmartName:$mSupportSmartName")
        if (mSupportSmartName) {
            binding.toolbar.menu.findItem(R.id.rename).isVisible = false
            binding.toolbar.menu.findItem(R.id.rename_new).isVisible = true
            binding.toolbar.menuView?.apply {
                setOverflowMenuListener { popup ->
                    mToolbarOverflowPopupWindow = popup
                    val mainList = popup.itemList
                    DebugUtil.d(TAG, "configToolbar mainList:${mainList.size}")
                    for (item in mainList) {
                        if (item.id == R.id.rename_new) {
                            for (subItem in item.subMenuItemList) {
                                subItem.stateIcon = ResourcesCompat.getDrawable(
                                    resources,
                                    R.drawable.menu_transparent_selector,
                                    activity?.theme
                                )
                            }
                        } else if (item.id == R.id.delete) {
                            item.itemType = PopupListItem.MENU_ITEM_TYPE_ALERT
                        }
                    }
                }
            }
        } else {
            binding.toolbar.menu.findItem(R.id.rename).isVisible = true
            binding.toolbar.menu.findItem(R.id.rename_new).isVisible = false
            binding.toolbar.menuView?.apply {
                setOverflowMenuListener { popup ->
                    mToolbarOverflowPopupWindow = popup
                    val mainList = popup.itemList
                    DebugUtil.d(TAG, "showSmartNameMenuView false configToolbar mainList:${mainList.size}")
                    for (item in mainList) {
                        if (item.id == R.id.delete) {
                            item.itemType = PopupListItem.MENU_ITEM_TYPE_ALERT
                        }
                    }
                }
            }
        }
    }

    private fun showRecycleToolBar() {
        binding.toolbar.inflateMenu(R.menu.menu_play_back_recycle)

        mSpeakerMenuItem = binding.toolbar.menu.findItem(R.id.speaker)
        mRecoverMenuItem = binding.toolbar.menu.findItem(R.id.recycle_recover)
        mDeleteMenuItem = binding.toolbar.menu.findItem(R.id.recycle_delete)

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                binding.toolbar.menu.apply {
                    if (FeatureOption.IS_PAD) {
                        mSpeakerMenuItem?.isVisible = false
                    }
                    this.children.forEach {
                        it.setOnMenuItemClickListener { menuItem ->
                            onOptionsItemSelected(menuItem)
                        }
                    }
                }
            }
        }
    }

    private fun ensureConvertSearchAnimView(menu: Menu?) {
        if (menu != null) {
            val mSearchItem = menu.findItem(R.id.search)
            if (searchAnimView == null) {
                searchAnimView = mSearchItem?.actionView as? COUISearchBar
            }
            mPlaybackConvertViewModel?.let {
                searchAnimView?.apply {
                    setAtBehindToolBar(binding.toolbar, Gravity.TOP, mSearchItem)
                    searchEditText.filters =
                        arrayOf<InputFilter>(InputFilter.LengthFilter(SEARCH_INPUT_MAX_LENGTH))
                    searchEditText.hint =
                        BaseApplication.getAppContext().resources.getString(com.soundrecorder.common.R.string.search_convert_content)
                    functionalButton?.setOnClickListener {
                        getConvertSearchFragment()?.onClickCancel()
                    }
                }
            }
            searchAnimView?.searchEditText?.let {
                it.setText(mPlaybackConvertViewModel?.mConvertSearchValue ?: "")
                it.addTextChangedListener(object : android.text.TextWatcher {
                    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
                    }

                    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                        getConvertSearchFragment()?.onQueryTextChange(s?.toString())
                        browseFileApi?.onConvertSearchStateChanged(
                            activity, mPlaybackConvertViewModel?.mIsInConvertSearch?.value ?: false,
                            !s.isNullOrBlank()
                        ) {
                            getConvertSearchFragment()?.onClickCancel()
                        }
                    }

                    override fun afterTextChanged(s: Editable?) {
                    }
                })
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (mPlaybackConvertViewModel?.mIsInConvertSearch?.value == true) {
            DebugUtil.i(TAG, "onOptionsItemSelected in convert search ,ignore menu item click event")
            return super.onOptionsItemSelected(item)
        }
        when (item.itemId) {
            android.R.id.home -> browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
            R.id.speaker -> {
                if (mViewModel?.checkLoadAmpFinished() != true) {
                    // 波形还未解析出来，不能使用听筒切换功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false return")
                    return false
                }
                browseFileApi?.getViewModelSpeakerModeController<SpeakerModeController>(mBrowseFileActivityViewModel)
                    ?.performSpeakerMenuItemClick(SpeakerModeController.PLAYER_SPEAKER_PLAYBACK)
            }

            R.id.recycle_delete -> mViewModel?.mNeedShowRecycleDeleteDialog?.value = true
            R.id.recycle_recover -> mViewModel?.mNeedShowRecoverDialog?.value = true
            R.id.export -> {
                mViewModel?.mNeedShowShareDialog?.value = true
                BuryingPoint.addClickMoreShare()
            }

            R.id.move -> {
                browseFileApi?.showGroupChooseFragment(
                    this@PlaybackContainerFragment,
                    mViewModel?.getRecord()
                )
            }

            R.id.rename -> {
                showRenameDialog()
            }

            R.id.rename_new -> {
                if (mSupportSmartName) {
                    showSmartNamePopupWindow()
                } else {
                    showRenameDialog()
                }
            }

            R.id.detail -> mViewModel?.mNeedShowDetailDialog?.value = true
            R.id.delete -> mViewModel?.mNeedShowDeleteDialog?.value = true
            R.id.set_ringtone -> {
                BuryingPoint.addClickSetRingtone(RecorderUserAction.VALUE_SET_RINGTONE_FROM_MORE)
                SendSetUtil.setAs(mViewModel?.recordId ?: -1, activity)
            }

            R.id.search -> {
                showSearch()
            }

            R.id.play_setting -> {
                if (mViewModel?.checkLoadAmpFinished() != true) { // 波形还未解析出来，不能使用倍速功能
                    DebugUtil.d(TAG, "checkLoadAmpFinished false")
                    return true
                }
                BuryingPoint.addPlaySetting()
                mViewModel?.mNeedShowSpeedDialog?.postValue(true)
            }

            R.id.cut -> {
                if (mViewModel?.ampList?.value.isNullOrEmpty()) {
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(),
                        com.soundrecorder.common.R.string.amplitues_not_ready
                    )
                    return true
                }
                activity?.let { act ->
                    editRecordApi?.createEditRecordIntent(act)?.let {
                        it.putExtra("playPath", mViewModel?.playPath?.value)
                        it.putExtra("playName", mViewModel?.playName?.value)
                        it.putExtra("recordType", mViewModel?.recordType?.value)
                        it.putExtra("recordId", mViewModel?.recordId)
                        it.putExtra("isFromOtherApp", mViewModel?.mIsFromOtherApp)
                        it.putExtra("mimeType", mViewModel?.mMimeType?.value)
                        it.putParcelableArrayListExtra(
                            "mark", ArrayList(
                                mViewModel?.getMarkList()?.value
                                    ?: arrayListOf()
                            )
                        )
                        mStartEditLauncher?.launch(it)
                        activity?.overridePendingTransition(
                            com.soundrecorder.common.R.anim.enter_bottom,
                            com.soundrecorder.common.R.anim.exit_top
                        )
                        mViewModel?.playerController?.pausePlay()
                        CuttingStaticsUtil.addPlayMoreTrim()
                    }
                }
            }

            R.id.convert_role -> {
                if (isOnConvert()) {
                    getConvertFragment()?.switchSpeakerRoleState()
                }
            }
        }
        return super.onOptionsItemSelected(item)
    }

    private fun showSearch() {
        if (ClickUtils.isFastDoubleClick()) {
            DebugUtil.i(TAG, "showSearch isFastDoubleClick ")
            return
        }

        if (isOnConvert()) {
            getConvertFragment()?.convertSearch()
        }
    }

    private fun showSmartNamePopupWindow() {
        mToolbarOverflowPopupWindow?.apply {
            setSubMenuClickListener(AdapterView.OnItemClickListener { _, _, position, _ ->
                val mainList = itemList
                DebugUtil.d(TAG, "onOptionsItemSelected, mainList:${mainList.size}")
                for (itemGroup in mainList) {
                    if (itemGroup.id == R.id.rename_new) {
                        mSubMenuCheckedPosition = position
                        when (mSubMenuCheckedPosition) {
                            0 -> showRenameDialog()
                            1 -> {
                                //智能命名
                                DebugUtil.d(TAG, "subMenuClick, rename ai:$position")
                                isClickAiTitle = true
                                val mediaIdList = mutableListOf<Long>()
                                mViewModel?.recordId?.let { mediaIdList.add(it) }
                                if (!PermissionUtils.hasAllFilePermission()) {
                                    showPermissionAllFileDialog(true, mediaIdList)
                                    dismiss()
                                    return@OnItemClickListener
                                }
                                startConvertAndSmartName(mediaIdList)
                            }
                        }
                        dismiss()
                    }
                }
            })
        }
    }

    private fun startConvertAndSmartName(mediaIdList: MutableList<Long>) {
        if (PermissionUtils.hasAllFilePermission()) {
            initSmartNameManagerImpl()
            mSmartNameMangerImpl?.convertStartSmartNameClickHandle(activity, mediaIdList, PAGE_FROM_PLAYBACK)
            mViewModel?.clearNeedSmartNameMedias()
        }
    }

    private fun showPermissionAllFileDialog(needSmartName: Boolean = false, selectedMediaIdList: MutableList<Long>? = null) {
        val activity = this.activity ?: return
        if (ClickUtils.isFastDoubleClick()) {
            return
        }
        mFilePermissionDialog = PermissionDialogUtils.showPermissionAllFileAccessDialog(
            activity,
            object : PermissionDialogUtils.PermissionDialogListener {
                override fun onClick(
                    alertType: Int,
                    isOk: Boolean,
                    permissions: ArrayList<String>?
                ) {
                    DebugUtil.d(TAG, "showPermissionAllFileDialog, isOk:$isOk, permissions:$permissions")
                    if (isOk) {
                        if (needSmartName) {
                            mViewModel?.addNeedSmartNameMedias(selectedMediaIdList)
                        }
                        PermissionUtils.goToAppAllFileAccessConfigurePermissions(activity)
                    }
                }
            }
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        DebugUtil.i(TAG, "onActivityResult: request code $requestCode result code $resultCode")
        when (requestCode) {
            REQUEST_CODE_SYS_RENAME_AUTH -> {
                if (resultCode == Activity.RESULT_OK) {
                    val suffix = mViewModel?.getRecordSuffix()
                    val renameContent = mRenameDialog?.getRenameContent()
                    if (FileDealUtil.renameAgain(mViewModel?.playerController?.getPlayUri(), renameContent, suffix)) {
                        mViewModel?.renameRecordByCore2Full(renameContent)
                        CloudStaticsUtil.addCloudLog(TAG, "renameAgain, ${mViewModel?.playName?.value} renameTo $renameContent success")
                    }
                }
                mRenameDialog?.resetOperating()
            }

            REQUEST_CODE_SYS_DELETE_AUTH -> {
                if (resultCode == Activity.RESULT_OK) {
                    responseDeleteBatch()
                }
                mDeleteDialog?.resetOperating()
            }

            REQUEST_CODE_SYS_RECOVER_AUTH -> {
                if (resultCode == Activity.RESULT_OK) {
                    responseRecoverBatch()
                }
                mRecoverDialog?.resetOperating()
            }

            Constants.EXPORT_TO_NOTE_REQUEST_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(),
                        com.soundrecorder.common.R.string.save_success
                    )
                }
            }

            Constants.RESULT_CODE_PLAYBACK_SUMMARY -> {
                // 通话记录ID没有摘要笔记
                if (resultCode == Constants.NOTES_RESULT_CODE_ERROR) {
                    mViewModel?.summaryNoteId?.let {
                        browseFileApi?.getDeleteSummaryNoteIdLiveData(mBrowseFileActivityViewModel)?.value = it
                    }
                    mViewModel?.deleteNoteData()
                    ToastManager.showShortToast(
                        BaseApplication.getAppContext(),
                        com.soundrecorder.common.R.string.tip_summary_be_deleted
                    )
                }
            }
        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    private fun responseRecoverBatch() {
        val activity = activity ?: return
        val result = FileDealUtil.recoveryRecord(activity, getOperaRecord(), REQUEST_CODE_SYS_RECOVER_AUTH)
        DeleteSoundEffectManager.getInstance().playDeleteSound()
        CloudStaticsUtil.addCloudLog(TAG, "responseDeleteBatch, delete ${mViewModel?.playName?.value} result=$result")
        onRecoverRecordSuccess()
    }

    private fun getOperaRecord(): Record {
        return mViewModel?.getRecord() ?: Record().apply {
            id = mViewModel?.recordId ?: -1
            data = mViewModel?.playPath?.value ?: ""
            recordType = mViewModel?.recordType?.value ?: 0
            recycleFilePath = mViewModel?.mPlayPath ?: mViewModel?.playPath?.value
            displayName = mViewModel?.playName?.value ?: ""
            isRecycle = recordFilterIsRecycle()
        }
    }

    private fun responseDeleteBatch() {
        val activity = activity ?: return
        val id = mViewModel?.recordId ?: -1
        val result = FileDealUtil.deleteRecordDBBatch(activity, mViewModel?.playPath?.value, id)
        DeleteSoundEffectManager.getInstance().playDeleteSound()
        CloudStaticsUtil.addCloudLog(TAG, "responseDeleteBatch, delete ${mViewModel?.playName?.value} result=$result")
        onDeleteRecordSuccess(recordFilterIsRecycle())
    }

    private fun onDeleteRecordSuccess(isRecycle: Boolean) {
        notifyRefreshRecordList()
        browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
        if (isRecycle) {
            BuryingPoint.addPlayRecycleDeleteSuccess()
        } else {
            seedingApi?.sendRecordDeleteEvent()
            BuryingPoint.addPlayMoreDeleteSuccess()
        }
        if (isFromAppCard()) {
            startToBrowseFile()
        }
    }

    private fun notifyRefreshRecordList() {
        val intent = Intent(RecordFileChangeNotify.FILE_UPDATE_ACTION)
        intent.putExtra(Constants.FRESH_FLAG, true)
        BaseUtil.sendLocalBroadcast(BaseApplication.getAppContext(), intent)
    }

    private fun stopObserver() {
        mViewModel?.playerController?.playerState?.removeObserver(mPlayStateChangeObserver)
    }

    private fun registerReceivers() {
        MultiFileObserver.getInstance().addFileEventListener(this)
        registerConvertReceiver()
    }

    private fun unregisterReceivers() {
        MultiFileObserver.getInstance().removeFileEventListener(this)
        LocalBroadcastManager.getInstance(context ?: BaseApplication.getAppContext())
            .unregisterReceiver(convertReceiver)
    }

    override fun onFileObserver(event: Int, path: String?, allPath: String?) {
        when (event) {
            FileObserver.MOVED_FROM, FileObserver.DELETE,
            FileObserver.DELETE_SELF, FileObserver.MOVE_SELF -> {
                DebugUtil.d("$TAG-onFileObserver", "path:$path,allPath:$allPath")
                cancelAndFinish(path, allPath)
            }
        }
    }

    private fun isSmallWindow(): Boolean {
        return browseFileApi?.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.value == WindowType.SMALL
    }

    private fun cancelAndFinish(path: String?, allPath: String?) {
        val playPath = mViewModel?.playPath?.value
        if (!allPath.isNullOrEmpty() && !playPath.isNullOrEmpty() && playPath.startsWith(allPath)) {
            /*再有的外销手机上，fileObserver会先于OnActivityResult方法，所以在此处增加删除逻辑*/
            if (mDeleteDialog?.getOperating() == true) {
                responseDeleteBatch()
                return
            }
            /*中大屏下处理列表重命名音频文件场景*/
            if (!isSmallWindow()) {
                val mediaRecord = MediaDBUtils.getRecordFromMediaByUriId(MediaDBUtils.genUri(mViewModel?.recordId ?: -1))
                DebugUtil.i(TAG, "onFileObserver,media.name=${mediaRecord?.displayName},show.name=${mViewModel?.playName?.value}")
                /**媒体库有延迟，mediaRecord有可能查询到之前的信息，所以这里增加了两边name不一致的判断*/
                if ((mediaRecord != null) && (mViewModel?.playName?.value != mediaRecord.displayName)) {
                    mViewModel?.renameRecord(
                        mediaRecord.data,
                        mediaRecord.displayName
                    )
                    return
                }
            }
            DebugUtil.d("$TAG-onFileObserver", "path:$path,allPath:$allPath,playPath:$playPath")
            mViewModel?.cancelNotification()
            browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
            DebugUtil.d("$TAG-onFileObserver", "path:$path,allPath:$allPath,playPath:$playPath,结束")
        }
    }

    /**
     * 显示 “请稍后。。。” dialog
     * 当转文本的文件大于  Constants.SHOW_WAITING_DIALOG__THRESHOLD 的时候，保存到本地和分享需要弹窗
     * 弹窗显示不足1s的时候，需要延迟到1s之后再关闭
     */
    private fun showWaitingDialog(type: ShareType?) {
        val msgResId = when (type) {
            is ShareTypeNote -> com.soundrecorder.common.R.string.is_saving
            is ShareTypeLink -> com.soundrecorder.common.R.string.generating
            else -> com.soundrecorder.common.R.string.waiting
        }
        val activity = activity ?: return
        if (mLoadingDialog == null) {
            mLoadingDialog = LoadingDialog(activity)
        }
        if (mLoadingDialog?.isActivityNull() == true) {
            mLoadingDialog?.resetActivity(activity)
        }
        mLoadingDialog?.show(msgResId)
        mViewModel?.setNeedRestoreWaitingDialog(false)
    }

    /**
     * 关闭waitingDialog并且显示分享面板
     */
    private fun dismissDialog() {
        DebugUtil.i(TAG, "dismissDialogDoExport...")
        if (mLoadingDialog?.isShowing() == true) {
            mLoadingDialog?.dismiss()
        }
    }

    override fun onDestroyView() {
        DebugUtil.e(TAG, "onDestroyView")
        cacheDialogStatus()
        releaseDialog()
        releasePopupWindow()
        mPageChangeCallback?.let { binding.viewpager.unregisterOnPageChangeCallback(it) }
        FollowDialogRestoreUtils.releaseFollowDialogRunnable(activity?.window?.decorView)
        disableDialog.dismissWhenShowing()
        mFilePermissionDialog.dismissWhenShowing()
        super.onDestroyView()
        mLoadingViewControl = null
        disableDialog = null
        browseFileApi?.getViewModelAnimRunning(mBrowseFileActivityViewModel)?.let {
            if (it.value == true) {
                // 将小屏动效置为false，避免动效执行过程中，重建导致该值没有被重置
                it.value = false
                DebugUtil.i(TAG, "getViewModelAnimRunning set false")
            }
        }
        if (immersiveAnimationHelperInitial == true) {
            immersiveAnimationHelper.release()
        }
        if (expandCollapseAnimationHelperInitial == true) {
            expandCollapseAnimationHelper.release()
        }
        mViewModel?.playerController?.removeTimerTickListener(mTimerTickCallback)
        binding.floatButtonPanel.panel.unObserveHeightChange()
        binding.floatButtonPanel.amplitudeSeekbar.playPointMoveListener = null
        if (ScreenUtil.isSmallScreen(context)) {
            (activity as? BaseActivity)?.unRegisterBackPressed()
        } else {
            (activity as? BaseActivity)?.unregisterFinishAndRemoveTaskCallback()
        }

        mainHandler.removeCallbacksAndMessages(null)
        StatusBarUtil.switchNavigationBarTransparent(activity, true)
    }

    private fun releasePopupWindow() {
        if (mToolbarOverflowPopupWindow?.isShowing == true) {
            mToolbarOverflowPopupWindow?.dismiss()
        }
        mToolbarOverflowPopupWindow = null
    }

    override fun onDestroy() {
        DebugUtil.d(TAG, "onDestroy: isFinishing = ${activity?.isFinishing}, isRemoving = $isRemoving")
        if ((activity?.isFinishing == true) || isRemoving) {
            checkIfNeedTrigCloudSync()
        }
        cacheDialogShowing()
        unregisterReceivers()
        stopObserver()
        mViewModel?.let {
            it.isAddPictureMarkingCallback = null
            it.setShareCallBack(null)
            it.clearLifecycle(this)
        }
        if ((activity?.isFinishing == true) || isRemoving) {
            mViewModel?.cancelNotification()
        }
        mSmartNameMangerImpl?.release()
        mSmartNameMangerImpl = null
        super.onDestroy()
        mPictureMarkHelper = null
        val isFinish = activity?.isFinishing == true
        if (isFinish) {
            mViewModel?.playerController?.releasePlay()
        }
        fromRestore = false
        buttonPanelControl.removeButtonPanel()
        pagerMediator.onDestroy()
    }

    private fun cacheDialogStatus() {
        mViewModel?.mNeedShowRenameDialog?.value = mRenameDialog?.isShowing() ?: false
        mViewModel?.setNeedRestoreWaitingDialog(mLoadingDialog?.isShowing() ?: false)
        mViewModel?.mNeedShowSelectPictureDialog?.value = mPictureMarkHelper?.isDialogShowing()
        mViewModel?.mNeedShowMarkRenameDialog?.value = mMarkListAdapter?.isRenameMarkDialogShowing ?: false
        mViewModel?.mNeedShowMarkDeleteDialog = mMarkListAdapter?.isDeleteMarkDialogShowing ?: false
    }

    private fun dismissMenuPop() {
        mMarkListAdapter?.dismissMenuPop()
    }

    private fun releaseDialog() {
        mLoadingDialog?.dismiss()
        mLoadingDialog = null
        mDeleteDialog?.release()
        mDeleteDialog = null
        mDetailDialog?.dismiss()
        mDetailDialog = null
        mViewModel?.mRenameEditText = mRenameDialog?.getNewContent().toString()
        mRenameDialog?.release()
        mRenameDialog = null
        releaseMarkListDialogs()
    }

    private fun releaseMarkListDialogs() {
        mMarkListAdapter?.let {
            if (it.isRenameMarkDialogShowing) {
                DebugUtil.i(TAG, "releaseDialogs mRenameMarkDialog")
                mViewModel?.mRenameMarkEditText = it.renameMarkDialog?.getNewContent().toString()
                mViewModel?.mMarkRenameData = it.mRenameMark
                it.dismissRenameDialog()
            }
            if (it.isDeleteMarkDialogShowing) {
                mViewModel?.mMarkDeleteData = it.mDeleteMark
                it.dismissDeleteDialog()
            }
            it.setOnShouldShowMenuPopListener(null)
        }
        dismissMenuPop()
    }

    private val mPlayStateChangeObserver = Observer<Int> {
        DebugUtil.i(TAG, "mPlayerState changed $it")
        mViewModel?.let { vm ->
            vm.markEnable.value = vm.isMarkEnabled()
        }
        when (it) {
            PlayStatus.PLAYER_STATE_PLAYING -> {
                binding.floatButtonPanel.floatRedCircleIcon.switchPauseState()
                mViewModel?.showNotification()
            }

            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
                binding.floatButtonPanel.floatRedCircleIcon.switchPauseState()
            }

            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE -> {
                binding.floatButtonPanel.floatRedCircleIcon.switchPlayState()
            }

            else -> {
                binding.floatButtonPanel.floatRedCircleIcon.switchPlayState()
                mViewModel?.cancelNotification()
            }
        }
    }

    override fun doPictureMark(pictureMarkMetaData: MarkMetaData) {
        if (mMarkListAdapter?.renameMarkDialog?.isShowing() == true) {
            val path = pictureMarkMetaData.imagePath
            mMarkListAdapter?.renameMarkDialog?.addImage(path)
        } else {
            mViewModel?.addMark(false, pictureMarkMetaData)
        }
    }

    override fun doSingleOrMultiPictureMarkEnd(operateCancel: Boolean, fromSource: Int) {
        when (fromSource) {
            IPictureMarkDelegate.SOURCE_ALBUM -> {
                if (operateCancel) {
                    BuryingPoint.playingAddPictureByAlbumCancel()
                } else {
                    BuryingPoint.playingAddPictureByAlbumOk()
                }
            }

            IPictureMarkDelegate.SOURCE_CAMERA -> {
                if (operateCancel) {
                    BuryingPoint.playingAddPictureByCameraCancel()
                } else {
                    BuryingPoint.playingAddPictureByCameraNumber()
                }
            }
        }
    }

    override fun releaseMarks(isFinishing: Boolean) {
        mViewModel?.getMarkList()?.value?.forEach {
            it.release(isFinishing)
        }
    }

    override fun supportPictureMarkSource(): List<Int> = listOf(IPictureMarkDelegate.SOURCE_CAMERA, IPictureMarkDelegate.SOURCE_ALBUM)

    override fun getPlayerCurrentTimeMillis(): Long {
        return mViewModel?.getCurrentTime() ?: -1L
    }

    override fun getMarkList(): List<MarkDataBean>? = mViewModel?.getMarkList()?.value

    override fun getDuration(): Long = mViewModel?.playerController?.getDuration() ?: -1L

    override fun restorePlayerStatePlaying(cachePlayerState: Int, hasNeedSpeakOff: Boolean): Int {
        when (cachePlayerState) {
            PlayStatus.PLAYER_STATE_PLAYING, PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
                if (hasNeedSpeakOff && speakerModeController?.mIsSpeakerOn?.value == false) {
                    return PlayStatus.PLAYER_STATE_INIT
                }
                continuePlay()
            }
        }
        return PlayStatus.PLAYER_STATE_INIT
    }

    override fun onActivityLaunched(sourceType: Int) {
        when (sourceType) {
            IPictureMarkDelegate.SOURCE_ALBUM -> {
                activity?.overridePendingTransition(
                    com.soundrecorder.common.R.anim.enter_bottom,
                    com.soundrecorder.common.R.anim.exit_top
                )
            }

            IPictureMarkDelegate.SOURCE_CAMERA -> {
                activity?.overridePendingTransition(
                    com.support.appcompat.R.anim.coui_open_slide_enter,
                    com.support.appcompat.R.anim.coui_open_slide_exit
                )
            }
        }
    }

    private fun continuePlay() {
        if (mViewModel?.playerController?.isWholePlaying() != true) {
            mViewModel?.playerController?.seekToPlay()
        }
    }

    override fun hasPlayerStatePlaying(): Int {
        return mViewModel?.playerController?.playerState?.value ?: PlayStatus.PLAYER_STATE_INIT
    }

    override fun beforeShowSelectPictureDialog() {
        mViewModel?.playerController?.pausePlay()
    }

    private fun checkIfNeedTrigCloudSync() {
        if (mViewModel?.mNeedTrigCloud == true) {
            cloudKitApi?.trigBackupNow(BaseApplication.getAppContext())
        }
    }

    private fun addOrRemoveAudioFragment() {
        val visible = binding.flConvertAudioContainer.isVisible
        if (visible) {
            val audioFragment = getAudioFragment() ?: PlaybackAudioFragment()
            if (audioFragment.isAdded.not()) {
                DebugUtil.i(TAG, "add audio fragment")
                replaceFragmentByTag(
                    containerId = R.id.fl_convert_audio_container,
                    fragment = audioFragment,
                    tag = PlaybackAudioFragment.TAG
                )
                audioFragment.updatePaddingBottom(buttonPanelControl.getButtonPanelHeight())
                this.mAudioFragment = audioFragment
            }
        } else {
            val audioFragment = getAudioFragment()
            audioFragment?.let { audioFragment ->
                DebugUtil.i(TAG, "remove audio fragment")
                removeFragment(audioFragment)
                mAudioFragment = null
            } ?: run {
                DebugUtil.i(TAG, "remove audio fragment is null")
            }
        }
    }

    private fun inAndOutConvertSearchFragment(jumpToConvertSearch: Boolean) {
        if (jumpToConvertSearch) {
            // 保存搜索动效的高度
            mConvertManagerImpl?.getConvertViewController()?.saveKeyWordViewHeight()
            pagerMediator.tabGone()
            playbackHeaderHelper.headViewGone()
            searchAnimView?.searchEditText?.setText(mPlaybackConvertViewModel?.mConvertSearchValue ?: "")
            binding.toolbar.post {
                searchAnimView?.showInToolBar()
            }
            // 取消右上角的更多弹窗
            binding.toolbar.postDelayed({
                binding.toolbar.dismissPopupMenus()
            }, TIP_SHOW_DELAY_TIME)

            val convertSearchFragment = getConvertSearchFragment() ?: ConvertSearchFragment()
            if (convertSearchFragment.isAdded.not()) {
                replaceFragmentByTag(
                    containerId = R.id.fl_convert_search_container,
                    fragment = convertSearchFragment,
                    tag = ConvertSearchFragment.TAG
                )
            }
        } else {
            val convertSearchFragment = getConvertSearchFragment()
            if (convertSearchFragment != null) {
                // 退出搜索检测是否需要展示气泡
                mConvertManagerImpl?.getConvertViewController()?.checkOutSearchShowBackOfLocation()
                removeFragment(convertSearchFragment)
                mConvertSearchFragment = null
            }
            searchAnimView?.apply {
                searchEditText.setText("")
                hideInToolBar()
            }
            checkoutShowHead()
            // 退出搜索的动效
            mConvertManagerImpl?.getConvertViewController()?.animSearchOut()
        }
    }

    private fun checkoutShowHead() {
        playbackHeaderHelper.checkShowHead()
        pagerMediator.checkShowTabLayout()
    }

    private fun initiateWindowInsets(rootView: View) {
        val callback = object : DeDuplicateInsetsCallback() {
            override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                val stableStatusBarInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
                v.updatePadding(
                    left = stableStatusBarInsets.left,
                    top = stableStatusBarInsets.top,
                    right = stableStatusBarInsets.right
                )
                binding.floatButtonPanel.panel.updateLayoutParams<FrameLayout.LayoutParams> {
                    this.bottomMargin = stableStatusBarInsets.bottom + binding.floatButtonPanel.panel.getPanelMarginBottom()
                }
                binding.viewpager.updateLayoutParams<FrameLayout.LayoutParams> {
                    this.bottomMargin = stableStatusBarInsets.bottom
                }
                DebugUtil.i(TAG, "onApplyInsets stableStatusBarInsets = $stableStatusBarInsets")
                mConvertManagerImpl?.getConvertViewController()?.dismissRenameSpeakerDialog()
                updateImmersiveNavigationHeight(stableStatusBarInsets.bottom)

                val height = binding.floatButtonPanel.panel.height
                floatButtonPanelHeightChange(height)
            }
        }
        ViewCompat.setOnApplyWindowInsetsListener(rootView, callback)
    }

    fun isViewPager2IDLE(): Boolean = binding.viewpager.scrollState == ViewPager2.SCROLL_STATE_IDLE

    fun isOnConvert(): Boolean {
        if (mPlaybackConvertViewModel?.mIsInConvertSearch?.value == true) { // 处于文本搜索界面
            return false
        }
        return binding.viewpager.isVisible && (mViewModel?.mCurrentTabType?.value == PlayBackInterface.TAB_TYPE_CONVERT)
    }

    fun onPrivacyPolicySuccess(type: Int) {
        when (type) {
            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT -> {
                if (isClickAiTitle) {
                    initSmartNameManagerImpl()
                    mSmartNameMangerImpl?.doClickPermissionConvertOK(activity, mViewModel?.needSmartNameMediaList, PAGE_FROM_PLAYBACK)
                    mViewModel?.clearNeedSmartNameMedias()
                } else {
                    mConvertManagerImpl?.doClickPermissionConvertOK()
                }
                isClickAiTitle = false
            }

            PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH -> mConvertManagerImpl?.doClickPermissionConvertSearchOK()
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        DebugUtil.i(TAG, "onSaveInstanceState")
        if (mViewModel?.isSupportConvert() == true) {
            outState.putBoolean(
                EKY_IS_IN_CONVERT_SEARCH,
                mPlaybackConvertViewModel?.mIsInConvertSearch?.value ?: false
            )
            outState.putString(
                KEY_IN_CONVERT_SEARCH_VALUE,
                mPlaybackConvertViewModel?.mConvertSearchValue ?: ""
            )
            outState.putInt(
                KEY_CONVERT_SEARCH_CURRENT_POS,
                mPlaybackConvertViewModel?.currentPos ?: 0
            )
            outState.putInt(
                KEY_CONVERT_SEARCH_LAST_POS,
                mPlaybackConvertViewModel?.lastPos ?: 0
            )
        }
        if (markListDialogFragment?.isAdded == true) {
            DebugUtil.e(TAG, "onSaveInstanceState is add")
            outState.putString(MARK_LIST_SHOW_STATE, MARK_LIST_SHOW)
        }
        super.onSaveInstanceState(outState)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        mPictureMarkHelper?.onConfigurationChanged(newConfig)
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int) {
        mPictureMarkHelper?.setRequestCodeX(requestCode)
        super.startActivityForResult(intent, requestCode)
    }

    override fun startActivityForResult(intent: Intent, requestCode: Int, options: Bundle?) {
        mPictureMarkHelper?.setRequestCodeX(requestCode)
        super.startActivityForResult(intent, requestCode, options)
    }

    fun getIPictureMarkDelegate(): IPictureMarkDelegate<MarkMetaData>? = mPictureMarkHelper

    override fun onBackPressed(): Boolean {
        if (mPlaybackConvertViewModel?.mIsInConvertSearch?.value == true) {
            mPlaybackConvertViewModel?.mIsInConvertSearch?.value = false
            return true
        }
        if (isFromAppCard()) {
            activity?.finish()
            return true
        } else {
            // 小屏下销毁播放页面
            if ((browseFileApi?.getViewModelWindowType<WindowType>(mBrowseFileActivityViewModel)?.value == WindowType.SMALL)
                && (browseFileApi?.getViewModelPlayData<StartPlayModel>(mBrowseFileActivityViewModel) != null)
            ) {
                browseFileApi?.clearViewModelPlayData(mBrowseFileActivityViewModel)
                return true
            }
        }
        return false
    }

    override fun pausePlay(clearNotification: Boolean) {
        mViewModel?.playerController?.pausePlay()
        if (clearNotification) {
            mViewModel?.cancelNotification()
        }
    }

    fun recordFilterIsRecycle(): Boolean {
        return mViewModel?.isRecycle == true
    }

    override fun onUserChange() {
        mViewModel?.cancelNotification()
    }

    private fun registerConvertReceiver() {
        val isFromOtherApp = browseFileApi?.getViewModelIsFromOther(mBrowseFileActivityViewModel) ?: false
        if (ConvertSupportManager.isSupportConvert(!isFromOtherApp)) {
            DebugUtil.d(TAG, "registerConvertReceiver")
            val convertFilter = IntentFilter()
            convertFilter.addAction(NOTIFY_CONVERT_STATUS_UPDATE)
            convertFilter.addAction(NOTIFY_SMART_NAME_STATUS_UPDATE)
            LocalBroadcastManager.getInstance(context ?: BaseApplication.getAppContext())
                .registerReceiver(convertReceiver, convertFilter)
        }
    }

    /**
     * 切换沉浸态
     */
    private fun switchImmersive(isShowImmersive: Boolean) {
        if (isShowImmersive) {
            startImmersiveAnimation()
        } else {
            reverseImmersiveAnimation()
        }
    }

    /**
     * 进入沉浸态
     */
    private fun startImmersiveAnimation() {
        DebugUtil.i(TAG, "startImmersiveAnimation")
        //列表渐变遮住隐藏
        mConvertManagerImpl?.getConvertViewController()?.startImmersiveAnimation()
        //底部面板沉浸态动画
        immersiveAnimationHelper.startImmersiveAnimation(navigationHeight, windowType = windowType)
    }

    /**
     * 退出沉浸态
     */
    private fun reverseImmersiveAnimation() {
        DebugUtil.i(TAG, "reverseImmersiveAnimation")
        mConvertManagerImpl?.getConvertViewController()?.reverseImmersiveAnimation()
        immersiveAnimationHelper.reverseImmersiveAnimation()
    }

    /**
     * 任务栏状态变化更新沉浸态布局
     */
    private fun updateImmersiveNavigationHeight(navigationHeight: Int) {
        DebugUtil.i(
            TAG,
            "updateImmersiveNavigationHeight navigationHeight$navigationHeight this.navigationHeight:${this.navigationHeight} windowType:$windowType"
        )
        this.navigationHeight = navigationHeight
        if (windowType == WindowType.SMALL || navigationHeight == 0) {
            return
        }
        mViewModel?.isImmersiveState?.value?.let {
            immersiveAnimationHelper.updateImmersiveNavigationHeight(navigationHeight)
        }
    }

    private fun changeMarkListViewDisplayState(isDisplay: Boolean = false) {
        if (isDisplay) {
            showBottomSheetDialogFragment()
        } else {
            dismissBottomSheetDialogFragment()
        }
    }

    private fun showBottomSheetDialogFragment() {
        DebugUtil.i(TAG, "showBottomSheetDialogFragment")
        if (markListDialogFragment?.isAdded == true) {
            DebugUtil.i(TAG, "showBottomSheetDialogFragment already show.")
            return
        }
        mMarkListAdapter?.setIsDialogFragment(true)
        markListDialogFragment?.dismiss()
        markListDialogFragment = MarkListBottomSheetDialogFragment()
        markListDialogFragment?.isHandlePanel = true
        markListDialogFragment?.setIsShowInMaxHeight(true)
        val markListContainerFragment = MarkListContainerFragment(mMarkListAdapter)
        markListDialogFragment?.setMainPanelFragment(markListContainerFragment)
        markListDialogFragment?.setOnDismissListener {
            DebugUtil.e(TAG, "setOnDismissListener")
            if (<EMAIL>) {
                mViewModel?.isShowMarkList?.postValueSafe(false)
            }
            markListDialogFragment?.setOnDismissListener(null)
            markListDialogFragment = null
        }
        markListDialogFragment?.show(childFragmentManager, MARK_LIST_FRAGMENT_TAG)
    }

    private fun dismissBottomSheetDialogFragment() {
        markListDialogFragment?.dismiss()
    }
}


data class FloatButtonPanelLayoutParams(val height: Int, val marginVertical: Int)