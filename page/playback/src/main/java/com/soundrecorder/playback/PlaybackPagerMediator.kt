/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackContainerTabMediator
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.ViewTreeObserver
import androidx.fragment.app.Fragment
import androidx.viewpager2.widget.ViewPager2
import com.coui.appcompat.tablayout.COUITab
import com.coui.appcompat.tablayout.COUITabLayout
import com.coui.appcompat.tablayout.COUITabLayoutMediator
import com.coui.appcompat.viewpager.COUIViewPager2
import com.coui.appcompat.viewpager.adapter.COUIFragmentStateAdapter
import com.google.android.material.appbar.AppBarLayout
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.utils.RecordModeUtil.getRecordTypeForMediaRecord
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.invisible
import com.soundrecorder.common.utils.visible
import com.soundrecorder.modulerouter.playback.PlayBackInterface.Companion.TAB_TYPE_CONVERT
import com.soundrecorder.modulerouter.playback.PlayBackInterface.Companion.TAB_TYPE_SUMMARY
import com.soundrecorder.modulerouter.summary.BUNDLE_MEDIA_ID
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_CALL_NAME
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_FILE_DURATION
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_FILE_PATH
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_MODIFY_TIME
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_TITLE
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_TYPE
import com.soundrecorder.modulerouter.summary.IAISummaryCallback
import com.soundrecorder.modulerouter.summary.IAISummaryInterface
import com.soundrecorder.modulerouter.summary.IImmersiveCallback
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.TAB_INDEX_FIRST
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.TAB_INDEX_SECOND
import com.soundrecorder.playback.newconvert.PlaybackConvertFragment
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_COMPLETE
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_INIT
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_PROGRESS
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_SUMMARY_NONE_COMPLETE
import com.soundrecorder.playback.view.PlaybackButtonPanel

class PlaybackPagerMediator(private val containerFragment: PlaybackContainerFragment) {
    companion object {
        private const val TAG = "PlaybackPagerMediator"
    }

    private var appbarLayout: AppBarLayout? = null
    private var tabLayout: COUITabLayout? = null
    private var tabLayoutMediator: COUITabLayoutMediator? = null
    private var viewpager: COUIViewPager2? = null
    private var fragmentAdapter: FragmentAdapter? = null
    private var pageChangeCallback: PageChangeCallback? = null
    private var clButtonPanel: PlaybackButtonPanel? = null

    private var viewModel: PlaybackContainerViewModel? = null
    private var convertViewModel: PlaybackConvertViewModel? = null

    private var convertFragment: PlaybackConvertFragment? = null
    private var summaryFragment: Fragment? = null
    private var summaryInterface: IAISummaryInterface? = null

    private var currentPosition: Int = TAB_INDEX_FIRST

    private val summaryAction by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    private val summaryCallback: IAISummaryCallback = object : IAISummaryCallback {
        override fun onSummaryStart() {
            super.onSummaryStart()
            buttonPanelMaybeInVisible()
        }

        override fun onSummaryEnd() {
            super.onSummaryEnd()
            buttonPanelMaybeVisible()
        }

        override suspend fun getSummaryTime(): String {
            return containerFragment.playbackHeaderHelper.getTime()
        }

        override fun getSummaryTitle(): String {
            return containerFragment.playbackHeaderHelper.getTitle()
        }
    }

    private fun buttonPanelMaybeInVisible() {
        val isSummaryPage = TAB_INDEX_SECOND == currentPosition
        if ((summaryFragment as? IAISummaryInterface)?.isSummaryRunning() == true && isSummaryPage) {
           clButtonPanel.invisible()
        }
    }

    private fun buttonPanelMaybeVisible() {
        clButtonPanel?.alpha = 1f
        clButtonPanel.visible()
    }

    private val immersiveCallback: IImmersiveCallback = object : IImmersiveCallback {
        override fun updateIsImmersive(dx: Int, dy: Int) {
            containerFragment.updateIsImmersive(dy)
        }
    }

    private val tabSelectedListener = object : COUITabLayout.OnTabSelectedListener {
        override fun onTabSelected(tab: COUITab?) {
            DebugUtil.d(TAG, "onTabSelected")
        }

        override fun onTabUnselected(tab: COUITab?) {
            DebugUtil.d(TAG, "onTabUnselected")
        }

        override fun onTabReselected(tab: COUITab?) {
            DebugUtil.d(TAG, "onTabReselected")
        }
    }

    fun initTabMediator(isFromRestore: Boolean) {
        initViewModel()
        initViewpageAdapter()
        initTab()
        initOther()
        layoutOnAppbarLayout()
        setViewPagePosition(isFromRestore)
    }

    private fun initTab() {
        tabLayout =
            containerFragment.contentBinding?.headerContainer?.recordTitleTimeLayout?.findViewById(R.id.tab_layout)
        tabLayout?.let { layout ->
            layout.invisible()
            layout.addOnTabSelectedListener(tabSelectedListener)
            viewpager?.let { pager ->
                tabLayoutMediator = COUITabLayoutMediator(layout, pager, true) { tab, position ->
                    tab.text = <EMAIL>?.getTabName(position) ?: ""
                }
            }
        }
    }

    private fun initViewpageAdapter() {
        viewpager = containerFragment.contentBinding?.viewpager
        viewpager?.apply {
            fragmentAdapter = FragmentAdapter(containerFragment)
            adapter = fragmentAdapter
            offscreenPageLimit = 1
            getChildAt(0).isNestedScrollingEnabled = false

            val pageChangeCallback = PageChangeCallback()
            registerOnPageChangeCallback(pageChangeCallback)
            <EMAIL> = pageChangeCallback
        }
    }

    private fun initViewModel() {
        viewModel = containerFragment.mViewModel
        convertViewModel = containerFragment.mPlaybackConvertViewModel
    }

    private fun initOther() {
        clButtonPanel = containerFragment.contentBinding?.floatButtonPanel?.panel
        appbarLayout = containerFragment.contentBinding?.appbarLayout
    }

    private fun layoutOnAppbarLayout() {
        appbarLayout?.viewTreeObserver?.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                appbarLayout?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                tabLayoutMediator?.attach()
            }
        })
    }

    private fun setViewPagePosition(isFromRestore: Boolean) {
        DebugUtil.i(TAG, "setViewPagePosition isFromRestore:$isFromRestore")
        if (isFromRestore.not()) {
            val playModel = viewModel?.startPlayModel
            val searchTapType = playModel?.selectPosInPlayback ?: TAB_TYPE_CONVERT
            DebugUtil.i(TAG, "searchTapType = $searchTapType")
            viewModel?.mCurrentTabType?.value = searchTapType
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateTab() {
        DebugUtil.d(TAG, "updateTab")
        if (canShowConvert()) {
            addConvertFragment()
        }
        if (canShowSummary()) {
            addSummaryFragment()
        }
        checkShowTabLayout()
    }

    private fun canShowConvert(): Boolean {
        return convertViewModel?.mConvertStatus?.value == CONVERT_STATUS_COMPLETE
                || convertViewModel?.mConvertStatus?.value == CONVERT_STATUS_PROGRESS
                || convertViewModel?.mConvertStatus?.value == CONVERT_STATUS_SUMMARY_NONE_COMPLETE
    }

    private fun canShowSummary(): Boolean {
        return (convertViewModel?.mConvertStatus?.value == CONVERT_STATUS_COMPLETE
                || convertViewModel?.mConvertStatus?.value == CONVERT_STATUS_SUMMARY_NONE_COMPLETE)
                && viewModel?.mPanelShowStatus?.value?.checkHasSummary() == true
    }

    private fun addConvertFragment() {
        val convertFragment = getConvertFragment() ?: PlaybackConvertFragment()
        val marginBottom = clButtonPanel?.let { it.getPanelHeight() + it.getPanelMarginTop() } ?: 0
        DebugUtil.d(TAG, "addConvertFragment TAB_TYPE_CONVERT marginBottom = $marginBottom")
        convertFragment.updatePaddingBottom(marginBottom)
        fragmentAdapter?.addFragment(type = TAB_TYPE_CONVERT, convertFragment)
        this.convertFragment = convertFragment
    }

    private fun getConvertFragment(): PlaybackConvertFragment? {
        if (convertFragment == null) {
            convertFragment = findFragmentByType(TAB_TYPE_CONVERT) as? PlaybackConvertFragment
        }
        return convertFragment
    }

    private fun addSummaryFragment() {
        val summaryFragment = getSummaryFragment() ?: newSummaryFragment()
        summaryFragment?.let { fragment ->
            fragment.arguments = Bundle().apply {
                putLong(BUNDLE_MEDIA_ID, viewModel?.recordId ?: 0)
                val recordType = if (viewModel?.getRecord() == null) -1 else getRecordTypeForMediaRecord(viewModel?.getRecord())
                putInt(BUNDLE_RECORD_TYPE, recordType)
                putString(BUNDLE_RECORD_TITLE, viewModel?.getRecord()?.displayName ?: "")
                putLong(BUNDLE_RECORD_MODIFY_TIME, viewModel?.getRecord()?.dateCreated ?: -1L)
                putString(BUNDLE_RECORD_FILE_PATH, viewModel?.getRecord()?.data ?: "")
                putLong(BUNDLE_RECORD_FILE_DURATION, viewModel?.getRecord()?.duration ?: 0)
                putString(BUNDLE_RECORD_CALL_NAME, viewModel?.getRecord()?.callerName ?: "")
            }
            val marginBottom = clButtonPanel?.let { it.getPanelHeight() + it.getPanelMarginTop() } ?: 0
            (fragment as? IAISummaryInterface)?.setPaddingBottom(marginBottom)
            DebugUtil.d(TAG, "addSummaryFragment TAB_TYPE_SUMMARY buttonPanelHeight = $marginBottom, " +
                    "navigationHeight = ${containerFragment.navigationHeight}")
            containerFragment.navigationHeight?.apply {
                (fragment as? IAISummaryInterface)?.setBottomMargin(this)
            }
            (fragment as? IAISummaryInterface)?.setSummaryCallback(summaryCallback)
            (fragment as? IAISummaryInterface)?.setImmersiveCallback(immersiveCallback)
            fragmentAdapter?.addFragment(type = TAB_TYPE_SUMMARY, fragment)
            this.summaryFragment = summaryFragment
        }
    }

    private fun getSummaryFragment(): Fragment? {
        if (summaryFragment == null) {
            summaryFragment = findFragmentByType(TAB_TYPE_SUMMARY)
        }
        return summaryFragment
    }

    private fun newSummaryFragment(): Fragment? {
        if (summaryInterface == null) {
            summaryInterface = summaryAction?.getAISummaryInterface()
        }
        return summaryInterface?.getFragment()
    }

    fun findFragmentByType(type: String): Fragment? {
        if (containerFragment.isAdded) {
            val tag = fragmentAdapter?.getFragmentTag(type) ?: return null
            return containerFragment.childFragmentManager.findFragmentByTag(tag)
        }
        return null
    }

    fun checkShowTabLayout() {
        val adapter = fragmentAdapter ?: run {
            tabLayout.gone()
            return
        }
        val fragmentCount = adapter.itemCount
        val complete = convertViewModel?.mConvertStatus?.value == CONVERT_STATUS_COMPLETE
                || convertViewModel?.mConvertStatus?.value == CONVERT_STATUS_SUMMARY_NONE_COMPLETE
        DebugUtil.d(TAG, "checkShowTabLayout convertStatus:${convertViewModel?.mConvertStatus?.value}," +
                "size:$fragmentCount," +
                "convertSearch:${convertViewModel?.mIsInConvertSearch?.value}"
        )
        val result = complete
                && containerFragment.recordFilterIsRecycle().not()
                && (fragmentCount > 1)
                && (convertViewModel?.mIsInConvertSearch?.value != true)
        if (result) {
            tabVisible()
        } else {
            tabGone()
        }
    }

    fun tabVisible() {
        tabLayout.visible()
    }

    fun tabGone() {
        tabLayout.gone()
    }

    fun selectConvertPage() {
        val position = fragmentAdapter?.getTabPosition(TAB_TYPE_CONVERT) ?: return
        val tab = tabLayout?.getTabAt(position) ?: return
        tabLayout?.selectTab(tab)
    }

    fun selectCurrentPage() {
        val position = fragmentAdapter?.getTabPosition(viewModel?.mCurrentTabType?.value ?: TAB_TYPE_CONVERT) ?: return
        if (position == -1) {
            return
        }
        viewpager?.post {
            if (containerFragment.isAdded.not()) return@post
            DebugUtil.i(TAG, "showCurrentItem position = $position")
            viewpager?.setCurrentItem(position, false)
        }
    }

    inner class PageChangeCallback : ViewPager2.OnPageChangeCallback() {
        override fun onPageScrollStateChanged(state: Int) {
            super.onPageScrollStateChanged(state)
            if (state == 0) {
                fragmentScrollEnd()
            }
        }

        private fun fragmentScrollEnd() {
            val isSummaryPage = TAB_INDEX_SECOND == currentPosition
            (summaryFragment as? IAISummaryInterface)?.summaryFragmentScrollEnd(isSummaryPage)
            DebugUtil.i(TAG, "fragmentScrollEnd isSummaryPage = $isSummaryPage")
            if (isSummaryPage) {
                buttonPanelMaybeInVisible()
            } else {
                buttonPanelMaybeVisible()
            }
        }

        override fun onPageScrolled(
            position: Int,
            positionOffset: Float,
            positionOffsetPixels: Int
        ) {
            super.onPageScrolled(position, positionOffset, positionOffsetPixels)
            fragmentScroll(position, positionOffset)
        }

        private fun fragmentScroll(position: Int, positionOffset: Float) {
            val summaryFragment = <EMAIL> as? IAISummaryInterface ?: return
            summaryFragment.summaryFragmentScroll(positionOffset)
            if (summaryFragment.isSummaryRunning() && position == 0) {
                val alpha = 1f - positionOffset
                //滑动过程中必须可见
                if (alpha > 0f) clButtonPanel.visible()
                clButtonPanel?.alpha = alpha
            }
        }

        override fun onPageSelected(position: Int) {
            DebugUtil.i(TAG, "onPageSelected position = $position")
            super.onPageSelected(position)
            handlePageSelected(position)
        }

        private fun handlePageSelected(position: Int) {
            if (position < 0) {
                DebugUtil.d(TAG, "handlePageSelected position error")
                return
            }
            val currentTabType = fragmentAdapter?.getFragmentType(position)
            viewModel?.mCurrentTabType?.value = currentTabType
            <EMAIL> = position
            when (currentTabType) {
                TAB_TYPE_SUMMARY -> handleSelectSummaryPage(position)
                TAB_TYPE_CONVERT -> handleSelectConvertPage(position)
                else -> DebugUtil.e(TAG, "handlePageSelected $currentTabType")
            }
        }

        private fun handleSelectSummaryPage(position: Int) {
            (summaryFragment as? IAISummaryInterface)?.apply {
                fragmentSelected()
                BuryingPoint.addPlaySwitchTab(position)
            }
        }

        private fun fragmentSelected() {
            if (TAB_INDEX_SECOND == currentPosition) {
                (summaryFragment as? IAISummaryInterface)?.summaryFragmentSelected()
            } else {
                (summaryFragment as? IAISummaryInterface)?.summaryFragmentUnSelected()
            }
        }

        private fun handleSelectConvertPage(position: Int) {
            checkNeedStartConvertStartAnimation()
            fragmentSelected()
            BuryingPoint.addPlaySwitchTab(position)
        }

        /**
         * 每次进入转文本页面，仅需要播放一次初始动效
         */
        private fun checkNeedStartConvertStartAnimation() {
            getConvertFragment()?.let {
                if ((viewModel?.isFirstInConvertFragment == true) && (it.mConvertManagerImpl?.getConvertStatus()?.value == CONVERT_STATUS_INIT)) {
                    //本次进入没有执行过初始动画，则执行初始动画
                    it.mConvertManagerImpl?.getConvertViewController()?.startConvertInitAnimation()
                }
                viewModel?.isFirstInConvertFragment = false
            }
        }
    }
}

class FragmentAdapter(private val containerFragment: PlaybackContainerFragment) :
    COUIFragmentStateAdapter(containerFragment) {

    data class ContentFragment(val type: String, val fragment: Fragment)

    private val fragmentList = mutableListOf<ContentFragment>()

    fun addFragment(type: String, fragment: Fragment) {
        if (fragmentList.find { it.fragment == fragment } != null) {
            return
        }
        fragmentList.removeIf { it.type == type }
        val contentFragment = ContentFragment(type, fragment)
        fragmentList.add(contentFragment)
    }

    override fun createFragment(position: Int): Fragment {
        val contentFragment = fragmentList[position]
        return contentFragment.fragment
    }

    override fun getItemCount(): Int {
        return fragmentList.size
    }

    fun getFragmentType(position: Int): String {
        val contentFragment = fragmentList[position]
        return contentFragment.type
    }

    fun getTabName(position: Int): String {
        val contentFragment = fragmentList[position]
        return when (contentFragment.type) {
            TAB_TYPE_CONVERT -> containerFragment.getString(com.soundrecorder.common.R.string.original_text)
            TAB_TYPE_SUMMARY -> containerFragment.getString(com.soundrecorder.common.R.string.summary)
            else -> ""
        }
    }

    fun getTabPosition(type: String): Int {
        return fragmentList.indexOfFirst { type == it.type }
    }

    fun getFragmentTag(type: String): String {
        val position = getTabPosition(type)
        return if (position >= 0) {
            "f${getItemId(position)}"
        } else {
            "f${getItemId(0)}"
        }
    }
}