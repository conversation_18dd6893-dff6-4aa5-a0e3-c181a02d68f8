/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackButtonPanel
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.view

import android.content.Context
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.marginBottom
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.R

class CustomConstraintLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var callback: ((height: Int) -> Unit)? = null
    private var lastHeight = 0

    private val floatButtonMarginTop: Int by lazy {
        resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp28)
    }

    /**
     *  播放面板距离底部导航栏间距
     */
    private val floatButtonMarginBottom: Int by lazy {
        resources.getDimensionPixelSize(R.dimen.suspension_marginBottom)
    }

    /**
     * 注册高度的监听
     */
    fun observeHeightChange(callback: ((height: Int) -> Unit)) {
        this.callback = callback
    }

    /**
     * 解除注册
     */
    fun unObserveHeightChange() {
        this.callback = null
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        val newHeight = h
        DebugUtil.d("PlaybackButtonPanel", "onSizeChanged h = $h, oldh = $oldh, marginBottom = $marginBottom")
        if (lastHeight != newHeight) {
            callback?.invoke(newHeight)
            lastHeight = newHeight
        }
    }

    fun getPanelHeight(): Int {
        return lastHeight
    }

    fun getPanelMarginBottom(): Int {
        return floatButtonMarginBottom
    }

    fun getPanelMarginVertical(): Int {
        return floatButtonMarginTop + floatButtonMarginBottom
    }
}