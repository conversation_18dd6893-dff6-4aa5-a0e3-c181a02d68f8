/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.text.TextUtils
import android.view.ActionMode
import android.view.Menu
import android.view.MenuItem
import android.widget.TextView
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.newconvert.view.TextImageMixLayout.Companion.SPACE

class SeekPlayActionModeCallback : ActionMode.Callback2() {

    var mItemTextView: TextView? = null

    override fun onCreateActionMode(mode: ActionMode?, menu: Menu?): Boolean {
         if (menu == null) {
             DebugUtil.e(TAG, "===>onCreateActionMode, menu is null!")
             return false
         }
         val copyItem = menu.findItem(android.R.id.copy)
         val shareItem = menu.findItem(android.R.id.shareText)
         val selectAllItem = menu.findItem(android.R.id.selectAll)
        menu.clear()
        copyItem?.let { menu.add(it.groupId, it.itemId, it.order, it.title) }
        shareItem?.let { menu.add(it.groupId, it.itemId, it.order, it.title) }
        selectAllItem?.let { menu.add(it.groupId, it.itemId, it.order, com.soundrecorder.common.R.string.seek_play_extend) }
        DebugUtil.i(TAG, "===>onCreateActionMode")
        return true
    }

    override fun onPrepareActionMode(mode: ActionMode?, menu: Menu?): Boolean {
        return true
    }

    override fun onActionItemClicked(mode: ActionMode?, item: MenuItem?): Boolean {
        when (item?.itemId) {
            android.R.id.copy -> {
                if (clipboard()) {
                    mode?.finish()
                    return true
                }
            }
            android.R.id.shareText -> {
                if (shareSelectedText()) {
                    mode?.finish()
                    return true
                }
            }
        }
        return false
    }


    override fun onDestroyActionMode(mode: ActionMode?) {
        DebugUtil.i(TAG, "===>onDestroyActionMode")
    }

    fun genStartPlayTime(item: ConvertContentItem?, start: Int): Long {
        if (item?.listSubSentence?.size ?: 0 <= 0) {
            DebugUtil.e(TAG, "===>genStartPlayTime, item is null!")
            return -1
        }
        if (start < 0) {
            DebugUtil.e(TAG, "===>genStartPlayTime, start < 0!")
            return -1
        }

        for (i in item!!.listSubSentence!!.indices) {
            if (start >= item.listSubSentence!![i].startCharSeq && start <= item.listSubSentence!![i].endCharSeq) {
                return item.listSubSentence!![i].time.toLong()
            }
        }
        DebugUtil.e(TAG, "===>genStartPlayTime, find not result")
        return -1
    }

    @SuppressLint("ClipboardManagerDetector")
    private fun clipboard(): Boolean {
        if (!FunctionOption.IS_SUPPORT_NEW_UI_OF13) return false
        val textView = mItemTextView ?: return false
        try {
            val context = BaseApplication.getAppContext()
            if (context == null) {
                DebugUtil.i(TAG, "clipboard context is null.")
                return false
            }
            val manager = context.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
            if (manager == null) {
                DebugUtil.i(TAG, "clipboard manager is null.")
                return false
            }
            val selStart: Int = textView.selectionStart
            val selEnd: Int = textView.selectionEnd
            var text = textView.text.toString()
            var subString = text.substring(selStart, selEnd)
            subString = subString.replace(SPACE, "")
            DebugUtil.i(TAG, "clipboard subString2 == $subString")
            manager.setPrimaryClip(ClipData.newPlainText(null, subString))
            ToastManager.showShortToast(context, context.getString(com.soundrecorder.common.R.string.convert_text_copied))
            return true
        } catch (e: Exception) {
            DebugUtil.e(TAG, "setPrimaryClip", e)
            return false
        }
    }

    private fun shareSelectedText(): Boolean {
        if (!FunctionOption.IS_SUPPORT_NEW_UI_OF13) return false
        val textView = mItemTextView ?: return false
        try {
            val selStart: Int = textView.selectionStart
            val selEnd: Int = textView.selectionEnd
            var text = textView.text.toString()
            if (TextUtils.isEmpty(text)) return false
            var subString = text.substring(selStart, selEnd)
            subString = subString.replace(SPACE, "")
            val sharingIntent = Intent(Intent.ACTION_SEND)
            sharingIntent.type = "text/plain"
            sharingIntent.removeExtra(Intent.EXTRA_TEXT)
            sharingIntent.putExtra(Intent.EXTRA_TEXT, subString)
            val intent = Intent.createChooser(sharingIntent, null)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            BaseApplication.getAppContext().startActivity(intent)
            return true
        } catch (e: Exception) {
            DebugUtil.e(TAG, "shareSelectedText", e)
            return false
        }
    }

    companion object {
        private val TAG = "SeekPlayActionModeCallback"
    }
}