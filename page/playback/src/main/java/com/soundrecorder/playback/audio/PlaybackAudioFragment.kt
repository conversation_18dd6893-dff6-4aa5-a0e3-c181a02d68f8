/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.audio

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.accessibility.AccessibilityEvent
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.graphics.Insets
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.seekbar.COUISeekBar
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.currentHasHourFormatTimeExclusive
import com.soundrecorder.base.ext.currentInMsFormatTimeWithColorSpan
import com.soundrecorder.base.ext.durationHasHourFormatTimeExclusive
import com.soundrecorder.base.splitwindow.WindowLayoutChangeListener
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.RecorderTextUtils
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.base.view.DeDuplicateInsetsCallback
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.ViewUtils.updateConstraintHeight
import com.soundrecorder.common.utils.visible
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.PlaybackContainerViewModel
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_ADD
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_DELETE
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_INIT
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_RENAME
import com.soundrecorder.playback.PlaybackContainerFragment
import com.soundrecorder.playback.PlaybackContainerFragment.Companion.ARG_KEY_SHOW_LOADING
import com.soundrecorder.playback.PlaybackContainerFragment.Companion.MAX_PROGRESS
import com.soundrecorder.playback.R
import com.soundrecorder.playback.databinding.FragmentPlaybackAudioBinding
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.view.SegmentSeekBar
import com.soundrecorder.player.TimerTickCallback
import com.soundrecorder.player.WavePlayerController
import com.soundrecorder.player.status.PlayStatus
import java.lang.Long.min


class PlaybackAudioFragment : Fragment(),
    View.OnCreateContextMenuListener,
    View.OnClickListener {

    companion object {
        const val TAG = "PlaybackAudioFragment"
        const val READ_MARK_TIMEOUT = 20000
    }

    var mBinding: FragmentPlaybackAudioBinding? = null
    private lateinit var mViewModel: PlaybackContainerViewModel
    private var mBrowseViewModel: ViewModel? = null
    private var isNeedRefresh = MutableLiveData(true)
    private var mPlaybackConvertViewModel: PlaybackConvertViewModel? = null
    private var seekBar: SegmentSeekBar? = null

    private var mTimerTickCallback: TimerTickCallback? = null

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val viewAnimateControl: PlayViewAnimateControl by lazy {
        PlayViewAnimateControl(requireActivity())
    }

    private val mPlayStateChangeObserver = Observer<Int> {
        DebugUtil.i(TAG, "mPlayerState changed $it")
        when (it) {
            PlayStatus.PLAYER_STATE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
                mBinding?.buttonPanel?.redCircleIcon?.switchPauseState()
                if (mViewModel.playerController.mIsTouchSeekbar.value != true) {
                    waveStartMove()
                }
            }

            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE -> {
                mBinding?.buttonPanel?.redCircleIcon?.switchPlayState()
                waveStopMove()
            }

            PlayStatus.PLAYER_STATE_HALTON -> {
                mBinding?.buttonPanel?.redCircleIcon?.switchPlayState()
                //可能最后smoothScrollBy后70ms的动画才走到最后，状态变为0后立马stopScroll导致没有滚动到最后
                waveStopMoveForEnd()
            }
            else -> mBinding?.buttonPanel?.redCircleIcon?.switchPlayState()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        DebugUtil.i(TAG, "onCreate $this , savedInstanceState $savedInstanceState")
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        mBinding = FragmentPlaybackAudioBinding.bind(inflater.inflate(R.layout.fragment_playback_audio, container, false))
        mViewModel = ViewModelProvider(requireParentFragment())[PlaybackContainerViewModel::class.java]
        mPlaybackConvertViewModel =
            ViewModelProvider(this.requireParentFragment())[PlaybackConvertViewModel::class.java]
        mBrowseViewModel = browseFileApi?.getBrowseActivityViewModel(activity as? AppCompatActivity)
        isNeedRefresh = browseFileApi?.getBrowseFileActivityViewModel(activity as? AppCompatActivity) ?: MutableLiveData(false)
        initView(savedInstanceState)
        initViewModelObserver()
        return mBinding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initiateWindowInsets(mBinding?.root)
        checkShowStartGuideAnim()
        checkShowLoading()
    }

    private fun initiateWindowInsets(rootView: View?) {
        rootView ?: return
        val callback = object : DeDuplicateInsetsCallback() {
            override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                val stableStatusBarInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
                DebugUtil.i(TAG, "onApplyInsets stableStatusBarInsets = $stableStatusBarInsets")
                updateButtonPanelMargin(stableStatusBarInsets)
            }
        }
        ViewCompat.setOnApplyWindowInsetsListener(rootView, callback)
    }

    private fun updateButtonPanelMargin(insets: Insets) {
        mBinding?.buttonPanel?.root?.updateLayoutParams<ConstraintLayout.LayoutParams> {
            bottomMargin = insets.bottom
        }
    }

    private fun initView(savedInstanceState: Bundle?) {
        if (savedInstanceState == null) {
            //重建不需要刷新波形
            mViewModel.needSyncRulerView = true
        }
        mBinding?.waveRecyclerview?.let { handleWaveRecyclerview(it) }
        mBinding?.tvTransfer?.isVisible =
            mViewModel.isSupportConvert() == true && !recordFilterIsRecycle()
        mBinding?.tvTransfer?.setOnClickListener(this)
        mBinding?.mConstraintLayout?.addOnLayoutChangeListener(mLayoutChangeListener)

        initButtonPanel()
    }

    private fun initButtonPanel() {
        if (BaseApplication.sIsRTLanguage) {
            // RTl语言下，快进快退图标也需要适配RTL
            mBinding?.buttonPanel?.imgBackward?.setImageResource(R.drawable.ic_forward)
            mBinding?.buttonPanel?.imgForward?.setImageResource(R.drawable.ic_backward)
        }
        mBinding?.buttonPanel?.imgMarkAdd?.isVisible = !recordFilterIsRecycle()
        mBinding?.buttonPanel?.imgMarkList?.isVisible = !recordFilterIsRecycle()

        mBinding?.buttonPanel?.imgMarkAdd?.setOnClickListener(this)
        mBinding?.buttonPanel?.imgForward?.setOnClickListener(this)
        mBinding?.buttonPanel?.imgBackward?.setOnClickListener(this)
        mBinding?.buttonPanel?.imgMarkList?.setOnClickListener(this)
        mBinding?.buttonPanel?.redCircleIcon?.setOnClickListener(this)
        initAccess()
        initSeekBar()
        initPlaybackPanel()
    }

    private fun initPlaybackPanel() {
        if (ScreenUtil.getWindowType() != WindowType.MIDDLE) {
            DebugUtil.i(TAG, "initPlaybackPanel window type is not middle.")
            return
        }

        val buttonPanelLayout = mBinding?.buttonPanel?.clButtonPanel

        if (buttonPanelLayout == null) {
            DebugUtil.e(TAG, "initPlaybackPanel init the view fail.")
            return
        }

        val constraintSet = ConstraintSet()
        constraintSet.clone(buttonPanelLayout)
        constraintSet.clear(R.id.ll_seekbar_container, ConstraintSet.TOP)
        constraintSet.clear(R.id.ll_seekbar_container, ConstraintSet.BOTTOM)
        constraintSet.clear(R.id.ll_seekbar_container, ConstraintSet.START)
        constraintSet.clear(R.id.ll_seekbar_container, ConstraintSet.END)

        val seekBarTimeMargin = resources.getDimension(R.dimen.seekbar_time_margin).toInt()
        val seekBarMargin = resources.getDimension(R.dimen.seekbar_margin).toInt()

        constraintSet.connect(R.id.tv_current, ConstraintSet.BOTTOM, R.id.middle_control, ConstraintSet.TOP)
        constraintSet.connect(R.id.tv_current, ConstraintSet.START, ConstraintSet.PARENT_ID, ConstraintSet.START, seekBarTimeMargin)

        constraintSet.connect(R.id.ll_seekbar_container, ConstraintSet.TOP, R.id.tv_current, ConstraintSet.TOP)
        constraintSet.connect(R.id.ll_seekbar_container, ConstraintSet.BOTTOM, R.id.tv_current, ConstraintSet.BOTTOM)
        constraintSet.connect(R.id.ll_seekbar_container, ConstraintSet.START, R.id.tv_current, ConstraintSet.END, seekBarMargin)
        constraintSet.connect(R.id.ll_seekbar_container, ConstraintSet.END, R.id.tv_duration, ConstraintSet.START, seekBarMargin)

        constraintSet.connect(R.id.tv_duration, ConstraintSet.BOTTOM, R.id.middle_control, ConstraintSet.TOP)
        constraintSet.connect(R.id.tv_duration, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END, seekBarTimeMargin)

        constraintSet.applyTo(buttonPanelLayout)
    }

    private fun initAccess() {
        //play button support accessibility. the view should be red_circle_icon, not the parent view middle_control
        mBinding?.buttonPanel?.redCircleIcon?.accessibilityDelegate = object : View.AccessibilityDelegate() {
            override fun sendAccessibilityEvent(host: View, eventType: Int) {
                if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                    if (mViewModel.playerController.isWholePlaying()) {
                        mBinding?.buttonPanel?.redCircleIcon?.contentDescription = resources.getString(
                            com.soundrecorder.common.R.string.recorder_pause
                        )
                    } else {
                        mBinding?.buttonPanel?.redCircleIcon?.contentDescription = resources.getString(
                            com.soundrecorder.common.R.string.recorder_play
                        )
                    }
                }
                super.sendAccessibilityEvent(host, eventType)
            }
        }
    }

    private fun initSeekBar() {
        layoutInflater.inflate(R.layout.layout_playback_seekbar, mBinding?.buttonPanel?.llSeekbarContainer, true)
        seekBar = mBinding?.buttonPanel?.llSeekbarContainer?.findViewById(R.id.seek_bar)
        val seekBar = seekBar ?: return
        seekBar.alpha = 1f
        seekBar.visible()
        seekBar.max = MAX_PROGRESS
        seekBar.isHapticFeedbackEnabled = false
        seekBar.setOnSeekBarChangeListener(object : COUISeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: COUISeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val seekToTime = progress * mViewModel.playerController.getDuration() / MAX_PROGRESS
                    mViewModel.needSyncRulerView = true
                    mViewModel.playerController.setOCurrentTimeMillis(seekToTime)
                }
            }

            override fun onStartTrackingTouch(seekBar: COUISeekBar?) {
                mViewModel.playerController.onStartTouchSeekBar()
                if (mViewModel.playerController.isWholePlaying()) {
                    mViewModel.playerController.stopTimerNow()
                    waveStopMove()
                }
            }

            override fun onStopTrackingTouch(seekBar1: COUISeekBar?) {
                mViewModel.playerController.onStopTouchSeekBar()
                seekBar.run {
                    val seekToTime = progress * mViewModel.playerController.getDuration() / MAX_PROGRESS
                    mViewModel.seekTime(seekToTime)
                }
                //按下之前是播放状态，松手后也应该继续播放。
                mViewModel.playerController.onResetPlayState()
            }
        })
    }

    private fun handleWaveRecyclerview(it: PlayWaveRecyclerView) {
        // 波形load成功之前禁止波形左右滚动
        it.setIsCanScrollTimeRuler(mViewModel.loadAmpSuccess())
        it.setAmplitudeList(mViewModel.ampList.value)
        it.setMaxAmplitudeSource(mViewModel.maxAmplitudeSource)

        it.duration = mViewModel.duration
        it.isDirectOn = mViewModel.isDirectOn
        it.directTime = mViewModel.directTime

        it.setDragListener(mViewModel.mDragListener)
        it.invalidate()
        it.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                when (newState) {
                    RecyclerView.SCROLL_STATE_DRAGGING -> {
                        mViewModel.playerController.stopTimerNow()
                        mViewModel.playerController.scrollFromType =
                            WavePlayerController.SCROLL_FROM_DRAG
                        //手拖动占据控制权后，设置preTimeMillis = -1,恢复后startSmoothScrollBy当次不处理
                        it.setPreTimeMillis(-1)
                    }

                    RecyclerView.SCROLL_STATE_IDLE -> {
                        if (mViewModel.playerController.scrollFromType == WavePlayerController.SCROLL_FROM_DRAG) {
                            val curTime = it.getSlideTime("onScrolledChanged idle")
                            if (curTime >= 0) {
                                mViewModel.needSyncRulerView = false
                                mViewModel.seekTime(curTime)
                                mViewModel.playerController.scrollFromType =
                                    WavePlayerController.SCROLL_FROM_DEFAULT
                                if ((curTime < mViewModel.playerController.getDuration())
                                    && mViewModel.playerController.isWholePlaying()
                                ) {
                                    mViewModel.playerController.startTimerAsync("onScrollStateChanged")
                                }
                            }
                        }
                    }
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                if (mViewModel.playerController.scrollFromType != WavePlayerController.SCROLL_FROM_DRAG) {
                    //onScrolled, is auto scrolling, just return
                    return
                }

                if (dx != 0) {
                    val curTime = it.getSlideTime("onScrolled")
                    if (curTime >= 0) {
                        mViewModel.needSyncRulerView = false
                        mViewModel.setCurrentTime(curTime)
                    }
                }
            }
        })
    }

    private fun checkShowStartGuideAnim() {
        val animRunningLiveData = browseFileApi?.getViewModelAnimRunning(mBrowseViewModel)
        if (animRunningLiveData?.value == true) {
            // 动效执行，波形不绘制，等动效完成后，再绘制，避免进入动效卡顿
            mBinding?.waveRecyclerview?.suppressLayout(true)
            animRunningLiveData.observe(viewLifecycleOwner) {
                if (!it) { // 小屏执行完动效再显示新手引导
                    mBinding?.waveRecyclerview?.suppressLayout(false)
                }
            }
        }
    }

    private fun checkShowLoading() {
        val needShowLoading = arguments?.getBoolean(ARG_KEY_SHOW_LOADING, false) ?: false
        if (!needShowLoading) {
            // 默认不showLoading场景下，禁止拖动seekbar
            seekBar?.isEnabled = mViewModel.loadAmpSuccess()
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initViewModelObserver() {
        // 每次进入初始页面时，不需要做动效
        mViewModel.isShowMarkList.observe(viewLifecycleOwner) { isShowing ->
            DebugUtil.i(TAG, "isShowing = $isShowing")
            if (isShowing) {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_SHOWING)
            } else {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_UNSHOWING)
            }
            mBinding?.mConstraintLayout?.post {
                setShowOrHideWaveViewAndMarkListView()
            }
        }

        mViewModel.isPrepareAmplitudeAndMark.observe(viewLifecycleOwner) {
            if (it) {
                mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true) // 拿到波形数据标记可滚动
                onAmpLoadingFinish()
            }
        }
        mViewModel.mIsDecodeReady.observe(viewLifecycleOwner) { isReady ->
            if (isReady) {
                seekBar?.isEnabled = true // 拿到波形数据，恢复按钮点击状态
                mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true) // 拿到波形数据标记可滚动
                mBinding?.waveRecyclerview?.let { decodeReadyRecyclerView(it) }
            }
        }
        mViewModel.playerController.currentTimeMillis.observe(viewLifecycleOwner) { currentTime ->
            refreshCurPlayTime(currentTime)
            refreshWaveInfo(currentTime)
            refreshSeekBar(currentTime)
        }
        mViewModel.playerController.playerState.observeForever(mPlayStateChangeObserver)
        mViewModel.playerController.isReplay.observe(viewLifecycleOwner) { isReplay ->
            if (isReplay) {
                waveStopMove()
            }
        }

        mViewModel.lastMarkAction.observe(viewLifecycleOwner) {
            DebugUtil.i(TAG, "lastMarkAction changed $it")
            when (it) {
                MARK_ACTION_INIT,
                MARK_ACTION_DELETE,
                MARK_ACTION_RENAME,
                MARK_ACTION_ADD -> {
                    mViewModel.getMarkList()?.value?.let { markList ->
                        mBinding?.waveRecyclerview?.setMarkTimeList(markList)
                    }
                }

                else -> DebugUtil.e(TAG, "lastMarkAction changed unknown mark action:$it")
            }
        }

        mTimerTickCallback = object : TimerTickCallback {
            override fun onTimerTick(timeTickMillis: Long) {
                if (!mViewModel.needSyncRulerView) {
                    //DebugUtil.d(TAG, "refreshTimerTick: newTime = $timeTickMillis, needSyncRulerView = true")
                    val realTime = min(timeTickMillis, mViewModel.playerController.getDuration())
                    mBinding?.waveRecyclerview?.startSmoothScroll(realTime)
                }
            }
        }
        mViewModel.playerController.addTimerTickListener(mTimerTickCallback)

        if (PermissionUtils.hasReadAudioPermission()) {
            mViewModel.readMarkTag()
        }

        initWindowTypeObserver()
        initPlayDurationObserver()
        initTargetPlaySegmentObserver()
        initMarkEnableObserver()
    }

    private fun initWindowTypeObserver() {
        browseFileApi?.getViewModelWindowType<WindowType>(mBrowseViewModel)?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "observe current windowType=$it")
            mBinding?.buttonPanel?.redCircleIcon?.windowType = it
            correctBottomButtonWhenLayoutNotReCreate(it == WindowType.LARGE)
            correctBottomViewMarginBottom()
        }
    }

    /**
     * 矫正layout改变，应用未重建，导致加载layout同实际layout不匹配，底部操作按钮显示异常问题
     * 平板上 录音同其他应用分屏加载小中布局，其他应用切位浮窗后，录音退出分屏显示全屏，此时录音未重建，仍然加载的小中布局
     */
    private fun correctBottomButtonWhenLayoutNotReCreate(isLarge: Boolean) {
        /*回收站或折叠屏不显示横向摘要、转文本等操作按钮布局，设为0F*/
        if (recordFilterIsRecycle() || FeatureOption.getIsFoldFeature()) {
            mBinding?.buttonPanel?.toolCenterGuideLine?.setGuidelinePercent(0F)
            mBinding?.buttonPanel?.toolRightGuideLine?.setGuidelinePercent(NumberConstant.NUM_F1_0)
        } else {
            val guidePercent = (mBinding?.buttonPanel?.toolCenterGuideLine?.layoutParams as? ConstraintLayout.LayoutParams)?.guidePercent
            if (isLarge && guidePercent != NumberConstant.NUM_F0_2) {
                mBinding?.buttonPanel?.toolCenterGuideLine?.setGuidelinePercent(NumberConstant.NUM_F0_2)
                mBinding?.buttonPanel?.toolRightGuideLine?.setGuidelinePercent(NumberConstant.NUM_F0_8)
            } else if (!isLarge && guidePercent != 0F) {
                mBinding?.buttonPanel?.toolCenterGuideLine?.setGuidelinePercent(0F)
                mBinding?.buttonPanel?.toolRightGuideLine?.setGuidelinePercent(NumberConstant.NUM_F1_0)
            }
        }
    }

    private fun correctBottomViewMarginBottom() {
        mBinding?.buttonPanel?.middleControl?.updateLayoutParams<MarginLayoutParams> {
            bottomMargin = resources.getDimension(com.soundrecorder.common.R.dimen.circle_playback_button_margin_bottom).toInt()
            val newWidth = resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_diam).toInt()
            if (width != newWidth) {
                width = newWidth
                height = newWidth
                mBinding?.buttonPanel?.redCircleIcon?.refreshCircleRadius(
                    resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_radius)
                )
            }
        }
    }

    private fun initPlayDurationObserver() {
        mViewModel.playerController.mDuration.observe(viewLifecycleOwner) { duration ->
            mBinding?.buttonPanel?.tvDuration?.contentDescription =
                TimeUtils.getContentDescriptionForTimeDuration(duration)
            mBinding?.buttonPanel?.tvDuration?.text = duration.durationHasHourFormatTimeExclusive(true)
            seekBar?.setDuration(duration)
        }
    }

    private fun initTargetPlaySegmentObserver() {
        mViewModel.targetPlaySegment.observe(viewLifecycleOwner) { timeSegmentList ->
            seekBar?.setSegmentTimeMillList(timeSegmentList)
        }
    }

    private fun initMarkEnableObserver() {
        mViewModel.markEnable.observe(viewLifecycleOwner) { isMarkEnable ->
            val pictureMarkHelper = (requireParentFragment() as? PlaybackContainerFragment)?.mPictureMarkHelper
            val isEnable = isMarkEnable && (pictureMarkHelper?.checkNeedAddMark() == true)
            val backgroundDrawable = if (isEnable) {
                AppCompatResources.getDrawable(BaseApplication.getAppContext(), R.drawable.ic_mark_icon_new)
            } else {
                AppCompatResources.getDrawable(BaseApplication.getAppContext(), R.drawable.ic_mark_icon_disable_new)
            }
            backgroundDrawable?.let {
                mBinding?.buttonPanel?.imgMarkAdd?.setImageDrawable(it)
            }
        }
    }

    private fun decodeReadyRecyclerView(it: PlayWaveRecyclerView) {
        it.setSoundFile(mViewModel.readyAmplitudeList)
        it.totalTime = mViewModel.playerController.getDuration()
        it.setSelectTime(mViewModel.playerController.getCurrentPosition())

        it.duration = mViewModel.duration
        it.isDirectOn = mViewModel.isDirectOn
        it.directTime = mViewModel.directTime
        DebugUtil.d(
            TAG, "initViewModelObserver isDirectOn:${mViewModel.isDirectOn}, " +
                    "directTime:${mViewModel.directTime}"
        )
        it.notifyDataSetChanged()
    }

    private fun recordFilterIsRecycle(): Boolean {
        return mViewModel.isRecycle
    }

    private fun refreshCurPlayTime(seekToTime: Long) {
        mBinding?.buttonPanel?.tvCurrent?.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(seekToTime)
        mBinding?.buttonPanel?.tvCurrent?.text = seekToTime.currentHasHourFormatTimeExclusive(mViewModel.playerController.getDuration())
        mBinding?.tvCurrentTime?.let {
            it.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(seekToTime)
            it.text = context?.run { seekToTime.currentInMsFormatTimeWithColorSpan(this) } ?: TimeUtils.getFormatTimeExclusiveMill(seekToTime)
        }
    }

    private fun refreshWaveInfo(seekToTime: Long) {
        //快进快退或者点击标记的时候，需要更新波形
        if (mViewModel.needSyncRulerView) {
            mBinding?.waveRecyclerview?.let {
                mViewModel.needSyncRulerView = false
                it.setSelectTime(seekToTime)
            }
            DebugUtil.d(
                TAG,
                "refreshWaveInfo: seekToTime = $seekToTime, needSyncRulerView = true"
            )
        }
    }

    private fun refreshSeekBar(currentTime: Long) {
        if (mViewModel.playerController.mIsTouchSeekbar.value == false) {
            val duration = mViewModel.playerController.getDuration()
            if (duration > 0) {
                val seekBar = seekBar ?: return
                seekBar.progress = (currentTime * MAX_PROGRESS / (duration)).toInt()
                seekBar.contentDescription = RecorderTextUtils.getNewProgressDescription(
                    BaseApplication.getAppContext(), currentTime, duration
                )
                seekBar.accessibilityDelegate = object : View.AccessibilityDelegate() {
                    override fun sendAccessibilityEvent(host: View, eventType: Int) {
                        if (eventType == AccessibilityEvent.TYPE_VIEW_ACCESSIBILITY_FOCUSED) {
                            seekBar.contentDescription = RecorderTextUtils.getNewProgressDescription(
                                BaseApplication.getAppContext(), currentTime, duration
                            )
                        }
                        super.sendAccessibilityEvent(host, eventType)
                    }
                }
            }
        }
    }

    private fun waveStartMove() {
        mBinding?.waveRecyclerview?.let {
            it.setSelectTime(mViewModel.getCurrentTime())
            it.setIsCanScrollTimeRuler(true)
        }
    }

    fun waveStopMove() {
        mBinding?.waveRecyclerview?.let {
            it.setSelectTime(mViewModel.getCurrentTime())
            it.stopScroll()
            it.setIsCanScrollTimeRuler(true)
        }
    }

    private fun waveStopMoveForEnd() {
        mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true)
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun onAmpLoadingFinish() {
        val duration = mViewModel.playerController.getDuration()
        val currentTimeMillis = mViewModel.playerController.getCurrentPosition()
        val ampList = mViewModel.ampList.value
        mBinding?.waveRecyclerview?.let {
            it.setAmplitudeList(ampList)
            it.totalTime = duration
            it.setSelectTime(currentTimeMillis)

            it.duration = duration
            it.isDirectOn = mViewModel.isDirectOn
            it.directTime = mViewModel.directTime
        }
        DebugUtil.i(
            TAG,
            "onAmpLoadingFinish mWaveRecyclerView set select time $currentTimeMillis + duration : $duration"
        )
    }

    override fun onClick(v: View?) {
        DebugUtil.d(TAG, "onClick v = $v")
        when (v?.id) {
            R.id.tv_transfer -> (requireParentFragment() as? PlaybackContainerFragment)?.handleTransferText()
            R.id.img_mark_add -> (requireParentFragment() as? PlaybackContainerFragment)?.handleMarkAdd(v)
            R.id.img_forward -> (requireParentFragment() as? PlaybackContainerFragment)?.handleClickForward()
            R.id.img_backward -> (requireParentFragment() as? PlaybackContainerFragment)?.handleClickBackward()
            R.id.img_mark_list -> (requireParentFragment() as? PlaybackContainerFragment)?.handleClickMarkList()
            R.id.red_circle_icon -> (requireParentFragment() as? PlaybackContainerFragment)?.handleClickRedCircleIcon()
        }
    }

    private fun stopObserver() {
        mViewModel.playerController.playerState.removeObserver(mPlayStateChangeObserver)
        mViewModel.playerController.removeTimerTickListener(mTimerTickCallback)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        DebugUtil.d(TAG, "onDestroyView")

        stopObserver()
        viewAnimateControl.release()
        mBinding?.waveRecyclerview?.setDragListener(null)
        mBinding?.mConstraintLayout?.removeOnLayoutChangeListener(mLayoutChangeListener)
    }

    override fun onResume() {
        super.onResume()
        // 临时规避#4501003,造成该问题原有是由于waveStartMove中调用setSelectTime导致最后一次波形滚动被拦截未执行
        if (mViewModel.playerController.playerState.value == PlayStatus.PLAYER_STATE_HALTON) {
            mBinding?.waveRecyclerview?.setSelectTime(mViewModel.getCurrentTime())
        }
    }

    private val mLayoutChangeListener: WindowLayoutChangeListener =
        object : WindowLayoutChangeListener() {
            override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
                activity?.let {
                    setShowOrHideWaveViewAndMarkListView()
                }
            }
        }

    private fun setShowOrHideWaveViewAndMarkListView() {
        if (!isAdded) {
            DebugUtil.i(TAG, "setShowOrHideWaveViewAndMarkListView isAdded is false")
            return
        }
        val binding = mBinding ?: return
        val waveHeight = viewAnimateControl.getWaveHeight(false)
        binding.waveGradientView.updateConstraintHeight(waveHeight)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        isNeedRefresh.value = false
    }
}