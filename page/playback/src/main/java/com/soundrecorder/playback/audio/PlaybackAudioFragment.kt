/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.audio

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.base.ext.currentInMsFormatTimeExclusive
import com.soundrecorder.base.ext.currentInMsFormatTimeWithColorSpan
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.splitwindow.WindowLayoutChangeListener
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.ViewUtils.updateConstraintHeight
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.PlaybackContainerViewModel
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_ADD
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_DELETE
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_INIT
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_RENAME
import com.soundrecorder.playback.PlaybackContainerFragment
import com.soundrecorder.playback.R
import com.soundrecorder.playback.databinding.FragmentPlaybackAudioBinding
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.player.TimerTickCallback
import com.soundrecorder.player.WavePlayerController
import com.soundrecorder.player.status.PlayStatus
import java.lang.Long.min

/**
 * 录音播放
 */
class PlaybackAudioFragment : Fragment(),
    View.OnCreateContextMenuListener,
    View.OnClickListener {

    companion object {
        const val TAG = "PlaybackAudioFragment"
        const val READ_MARK_TIMEOUT = 20000
    }

    var mBinding: FragmentPlaybackAudioBinding? = null
    private lateinit var mViewModel: PlaybackContainerViewModel
    private var mBrowseViewModel: ViewModel? = null
    private var isNeedRefresh = MutableLiveData(true)
    private var mPlaybackConvertViewModel: PlaybackConvertViewModel? = null
    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private var mTimerTickCallback: TimerTickCallback? = null

    private val viewAnimateControl: PlayViewAnimateControl by lazy {
        PlayViewAnimateControl(requireActivity())
    }

    private val mPlayStateChangeObserver = Observer<Int> {
        DebugUtil.i(TAG, "mPlayerState changed $it")
        when (it) {
            PlayStatus.PLAYER_STATE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
                if (mViewModel.playerController.mIsTouchSeekbar.value != true) {
                    waveStartMove()
                }
            }

            PlayStatus.PLAYER_STATE_PAUSE,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE -> waveStopMove()

            PlayStatus.PLAYER_STATE_HALTON -> {
                //可能最后smoothScrollBy后70ms的动画才走到最后，状态变为0后立马stopScroll导致没有滚动到最后
                waveStopMoveForEnd()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        DebugUtil.i(TAG, "onCreate $this , savedInstanceState $savedInstanceState")
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        mBinding = FragmentPlaybackAudioBinding.bind(
            inflater.inflate(
                R.layout.fragment_playback_audio,
                container,
                false
            )
        )

        mViewModel = ViewModelProvider(requireParentFragment())[PlaybackContainerViewModel::class.java]
        mPlaybackConvertViewModel =
            ViewModelProvider(this.requireParentFragment())[PlaybackConvertViewModel::class.java]
        mBrowseViewModel = browseFileApi?.getBrowseActivityViewModel(activity as? AppCompatActivity)
        isNeedRefresh = browseFileApi?.getBrowseFileActivityViewModel(activity as? AppCompatActivity) ?: MutableLiveData(false)
        initView(savedInstanceState)
        initViewModelObserver()
        return mBinding?.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        checkShowStartGuideAnim()
    }

    private fun initView(savedInstanceState: Bundle?) {
        if (savedInstanceState == null) {
            //重建不需要刷新波形
            mViewModel.needSyncRulerView = true
        }
        mBinding?.waveRecyclerview?.let { handleWaveRecyclerview(it) }
        mBinding?.tvTransfer?.isVisible =
            mViewModel.isSupportConvert() == true && !recordFilterIsRecycle()
        mBinding?.tvTransfer?.setOnClickListener(this)
        mBinding?.mConstraintLayout?.addOnLayoutChangeListener(mLayoutChangeListener)
    }

    private fun handleWaveRecyclerview(it: PlayWaveRecyclerView) {
        // 波形load成功之前禁止波形左右滚动
        it.setIsCanScrollTimeRuler(mViewModel.loadAmpSuccess())
        it.setAmplitudeList(mViewModel.ampList.value)
        it.setMaxAmplitudeSource(mViewModel.maxAmplitudeSource)

        it.duration = mViewModel.duration
        it.isDirectOn = mViewModel.isDirectOn
        it.directTime = mViewModel.directTime

        it.setDragListener(mViewModel.mDragListener)
        it.invalidate()
        it.addOnScrollListener(object : RecyclerView.OnScrollListener() {

            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                when (newState) {
                    RecyclerView.SCROLL_STATE_DRAGGING -> {
                        mViewModel.playerController.stopTimerNow()
                        mViewModel.playerController.scrollFromType =
                            WavePlayerController.SCROLL_FROM_DRAG
                        //手拖动占据控制权后，设置preTimeMillis = -1,恢复后startSmoothScrollBy当次不处理
                        it.setPreTimeMillis(-1)
                    }

                    RecyclerView.SCROLL_STATE_IDLE -> {
                        if (mViewModel.playerController.scrollFromType == WavePlayerController.SCROLL_FROM_DRAG) {
                            val curTime = it.getSlideTime("onScrolledChanged idle")
                            if (curTime >= 0) {
                                mViewModel.needSyncRulerView = false
                                mViewModel.seekTime(curTime)
                                mViewModel.playerController.scrollFromType =
                                    WavePlayerController.SCROLL_FROM_DEFAULT
                                if ((curTime < mViewModel.playerController.getDuration())
                                    && mViewModel.playerController.isWholePlaying()
                                ) {
                                    mViewModel.playerController.startTimerAsync("onScrollStateChanged")
                                }
                            }
                        }
                    }
                }
            }

            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                if (mViewModel.playerController.scrollFromType != WavePlayerController.SCROLL_FROM_DRAG) {
                    //onScrolled, is auto scrolling, just return
                    return
                }

                if (dx != 0) {
                    val curTime = it.getSlideTime("onScrolled")
                    if (curTime >= 0) {
                        mViewModel.needSyncRulerView = false
                        mViewModel.setCurrentTime(curTime)
                    }
                }
            }
        })
    }

    private fun checkShowStartGuideAnim() {
        val animRunningLiveData = browseFileApi?.getViewModelAnimRunning(mBrowseViewModel)
        if (animRunningLiveData?.value == true) {
            // 动效执行，波形不绘制，等动效完成后，再绘制，避免进入动效卡顿
            mBinding?.waveRecyclerview?.suppressLayout(true)
            animRunningLiveData.observe(viewLifecycleOwner) {
                if (!it) { // 小屏执行完动效再显示新手引导
                    mBinding?.waveRecyclerview?.suppressLayout(false)
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initViewModelObserver() {
        // 每次进入初始页面时，不需要做动效
        mViewModel.isShowMarkList.observe(viewLifecycleOwner) { isShowing ->
            DebugUtil.i(TAG, "isShowing = $isShowing")
            if (isShowing) {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_SHOWING)
            } else {
                BuryingPoint.addPlayMarkListButton(RecorderUserAction.VALUE_PLAY_MARK_LIST_UNSHOWING)
            }
            mBinding?.mConstraintLayout?.post {
                setShowOrHideWaveViewAndMarkListView()
            }
        }

        mViewModel.isPrepareAmplitudeAndMark.observe(viewLifecycleOwner) {
            if (it) {
                mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true) // 拿到波形数据标记可滚动
                onAmpLoadingFinish()
            }
        }
        mViewModel.mIsDecodeReady.observe(viewLifecycleOwner) { isReady ->
            if (isReady) {
                mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true) // 拿到波形数据标记可滚动
                mBinding?.waveRecyclerview?.let { decodeReadyRecyclerView(it) }
            }
        }
        mViewModel.playerController.currentTimeMillis.observe(viewLifecycleOwner) { currentTime ->
            refreshCurPlayTime(currentTime)
            refreshWaveInfo(currentTime)
        }
        mViewModel.playerController.playerState.observeForever(mPlayStateChangeObserver)
        mViewModel.playerController.isReplay.observe(viewLifecycleOwner) { isReplay ->
            if (isReplay) {
                waveStopMove()
            }
        }

        mViewModel.lastMarkAction.observe(viewLifecycleOwner) {
            DebugUtil.i(TAG, "lastMarkAction changed $it")
            when (it) {
                MARK_ACTION_INIT,
                MARK_ACTION_DELETE,
                MARK_ACTION_RENAME,
                MARK_ACTION_ADD -> {
                    mViewModel.getMarkList()?.value?.let { markList ->
                        mBinding?.waveRecyclerview?.setMarkTimeList(markList)
                    }
                }

                else -> DebugUtil.e(TAG, "lastMarkAction changed unknown mark action:$it")
            }
        }

        mTimerTickCallback = object : TimerTickCallback {
            override fun onTimerTick(timeTickMillis: Long) {
                if (!mViewModel.needSyncRulerView) {
                    //DebugUtil.d(TAG, "refreshTimerTick: newTime = $timeTickMillis, needSyncRulerView = true")
                    val realTime = min(timeTickMillis, mViewModel.playerController.getDuration())
                    mBinding?.waveRecyclerview?.startSmoothScroll(realTime)
                }
            }
        }
        mViewModel.playerController.addTimerTickListener(mTimerTickCallback)

        if (PermissionUtils.hasReadAudioPermission()) {
            mViewModel.readMarkTag()
        }
    }

    private fun decodeReadyRecyclerView(it: PlayWaveRecyclerView) {
        it.setSoundFile(mViewModel.mSoundFile)
        it.totalTime = mViewModel.playerController.getDuration()
        it.setSelectTime(mViewModel.playerController.getCurrentPosition())

        it.duration = mViewModel.duration
        it.isDirectOn = mViewModel.isDirectOn
        it.directTime = mViewModel.directTime
        DebugUtil.d(
            TAG, "initViewModelObserver isDirectOn:${mViewModel.isDirectOn}, " +
                    "directTime:${mViewModel.directTime}"
        )
        it.notifyDataSetChanged()
    }

    private fun recordFilterIsRecycle(): Boolean {
        return mViewModel.isRecycle
    }

    private fun refreshCurPlayTime(seekToTime: Long) {
        mBinding?.tvCurrentTime?.let {
            it.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(seekToTime)
            it.text = context?.run { seekToTime.currentInMsFormatTimeWithColorSpan(this) } ?: TimeUtils.getFormatTimeExclusiveMill(seekToTime)
        }
    }

    private fun refreshWaveInfo(seekToTime: Long) {
        //快进快退或者点击标记的时候，需要更新波形
        if (mViewModel.needSyncRulerView) {
            mBinding?.waveRecyclerview?.let {
                mViewModel.needSyncRulerView = false
                it.setSelectTime(seekToTime)
            }
            DebugUtil.d(
                TAG,
                "refreshWaveInfo: seekToTime = $seekToTime, needSyncRulerView = true"
            )
        }
    }

    private fun waveStartMove() {
        mBinding?.waveRecyclerview?.let {
            it.setSelectTime(mViewModel.getCurrentTime())
            it.setIsCanScrollTimeRuler(true)
        }
    }

    fun waveStopMove() {
        mBinding?.waveRecyclerview?.let {
            it.setSelectTime(mViewModel.getCurrentTime())
            it.stopScroll()
            it.setIsCanScrollTimeRuler(true)
        }
    }

    private fun waveStopMoveForEnd() {
        mBinding?.waveRecyclerview?.setIsCanScrollTimeRuler(true)
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun onAmpLoadingFinish() {
        val duration = mViewModel.playerController.getDuration()
        val currentTimeMillis = mViewModel.playerController.getCurrentPosition()
        val ampList = mViewModel.ampList.value
        mBinding?.waveRecyclerview?.let {
            it.setAmplitudeList(ampList)
            it.totalTime = duration
            it.setSelectTime(currentTimeMillis)

            it.duration = duration
            it.isDirectOn = mViewModel.isDirectOn
            it.directTime = mViewModel.directTime
        }
        DebugUtil.i(
            TAG,
            "onAmpLoadingFinish mWaveRecyclerView set select time $currentTimeMillis + duration : $duration"
        )
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.tv_transfer -> {
                DebugUtil.d(TAG, "onClick layout_transfer_text:")
                (requireParentFragment() as? PlaybackContainerFragment)?.handleTransferText()
            }
        }
    }

    private fun stopObserver() {
        mViewModel.playerController.playerState.removeObserver(mPlayStateChangeObserver)
        mViewModel.playerController.removeTimerTickListener(mTimerTickCallback)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        DebugUtil.d(TAG, "onDestroyView")

        stopObserver()
        viewAnimateControl.release()
        mBinding?.waveRecyclerview?.setDragListener(null)
        mBinding?.mConstraintLayout?.removeOnLayoutChangeListener(mLayoutChangeListener)
    }

    override fun onResume() {
        super.onResume()
        // 临时规避#4501003,造成该问题原有是由于waveStartMove中调用setSelectTime导致最后一次波形滚动被拦截未执行
        if (mViewModel.playerController.playerState.value == PlayStatus.PLAYER_STATE_HALTON) {
            mBinding?.waveRecyclerview?.setSelectTime(mViewModel.getCurrentTime())
        }
    }

    private val mLayoutChangeListener: WindowLayoutChangeListener =
        object : WindowLayoutChangeListener() {
            override fun onLayoutChange(v: View?, rect: Rect, oldRect: Rect) {
                activity?.let {
                    setShowOrHideWaveViewAndMarkListView()
                }
            }
        }

    private fun setShowOrHideWaveViewAndMarkListView() {
        if (!isAdded) {
            DebugUtil.i(TAG, "setShowOrHideWaveViewAndMarkListView isAdded is false")
            return
        }
        val binding = mBinding ?: return
        val waveHeight = viewAnimateControl.getWaveHeight(false)
        binding.waveGradientView.updateConstraintHeight(waveHeight)
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        isNeedRefresh.value = false
    }
}