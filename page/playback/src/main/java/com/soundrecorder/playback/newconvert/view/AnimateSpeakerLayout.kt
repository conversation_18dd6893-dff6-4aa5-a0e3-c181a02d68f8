/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AnimateSpeakerLayout
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9013204
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */
package com.soundrecorder.playback.newconvert.view

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.contains
import androidx.core.view.forEach
import androidx.core.view.marginEnd
import androidx.core.view.marginStart
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.utils.ViewUtils.getUnDisplayViewSize
import com.soundrecorder.playback.R

/**
 * 使用要根据实际的XML来进行适配
 */
class AnimateSpeakerLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "AnimateSpeakerLayout"
        private const val TYPE_ORIGINAL = 0
        private const val TYPE_SHARE = 1
    }

    private val maxPaddingStart: Int
    private val maxPaddingEnd: Int
    private val maxPaddingTop: Int
    private val maxPaddingBottom: Int
    private val layoutType: Int
    private val maxAlpha = 1f
    private val minAlpha = 0f
    private val shapeView by lazy {
        View(context).apply {
            layoutParams = LayoutParams(1, 1).apply {
                gravity = Gravity.TOP or Gravity.START
            }
        }
    }

    init {
        val ta = context.obtainStyledAttributes(attrs, com.soundrecorder.common.R.styleable.AnimateSpeakerLayout)
        maxPaddingStart =
            ta.getDimensionPixelSize(com.soundrecorder.common.R.styleable.AnimateSpeakerLayout_max_padding_start, 0)
        maxPaddingEnd =
            ta.getDimensionPixelSize(com.soundrecorder.common.R.styleable.AnimateSpeakerLayout_max_padding_end, 0)
        maxPaddingTop =
            ta.getDimensionPixelSize(com.soundrecorder.common.R.styleable.AnimateSpeakerLayout_max_padding_top, 0)
        maxPaddingBottom =
            ta.getDimensionPixelSize(com.soundrecorder.common.R.styleable.AnimateSpeakerLayout_max_padding_bottom, 0)
        COUIDarkModeUtil.setForceDarkAllow(shapeView, false)
        shapeView.background = ta.getDrawable(com.soundrecorder.common.R.styleable.AnimateSpeakerLayout_background)
        layoutType = ta.getInt(com.soundrecorder.common.R.styleable.AnimateSpeakerLayout_layout_type, TYPE_ORIGINAL)
        ta.recycle()
    }

    private val maxValue = 360
    private val duration = maxValue * 1L
    private val pathInterpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
    private val showAnimator = ValueAnimator.ofInt(0, maxValue).apply {
        duration = <EMAIL>
        interpolator = pathInterpolator
        when (layoutType) {
            TYPE_ORIGINAL -> addUpdateListener { setChildAlpha((it.animatedValue as Int) * 1f / maxValue) }
            TYPE_SHARE -> {
                addUpdateListener {
                    val v = it.animatedValue as Int
                    val w = (getChildWidth() - getChildHeight()) * v / maxValue + getChildHeight()
                    val h = getChildHeight()
                    val ms = maxPaddingStart * v / maxValue
                    val mt = maxPaddingTop * v / maxValue
                    val me = maxPaddingEnd * v / maxValue
                    val mb = maxPaddingBottom * v / maxValue
                    setWidthAndHeightAndMargin(w, h, ms, mt, me, mb)
                    setChildAlpha(v * 1f / maxValue)
                }
            }

            else -> DebugUtil.d(TAG, "showAnimator layoutType is unknown")
        }
    }

    private val dismissAnimator = ValueAnimator.ofInt(maxValue, 0).apply {
        duration = <EMAIL>
        interpolator = pathInterpolator
        when (layoutType) {
            TYPE_ORIGINAL -> addUpdateListener { setChildAlpha((it.animatedValue as Int) * 1f / maxValue) }
            TYPE_SHARE -> {
                addUpdateListener {
                    val v = it.animatedValue as Int
                    val w = getChildWidth() * v / maxValue
                    val h = getChildHeight()
                    val ms = maxPaddingStart * v / maxValue
                    val mt = maxPaddingTop * v / maxValue
                    val me = maxPaddingEnd * v / maxValue
                    val mb = maxPaddingBottom * v / maxValue
                    setWidthAndHeightAndMargin(w, h, ms, mt, me, mb)
                    setChildAlpha(v * 1f / maxValue)
                }
            }

            else -> DebugUtil.d(TAG, "dismissAnimator layoutType is unknown")
        }
    }

    private fun setChildAlpha(alpha: Float) {
        when (layoutType) {
            TYPE_ORIGINAL -> setChildAlphaOriginal(alpha)

            TYPE_SHARE -> setChildAlphaShare(alpha)

            else -> DebugUtil.d(TAG, "setChildAlpha layoutType is unknown")
        }
    }

    private fun getChildHeight(): Int {
        val view = getChildTextView() ?: return 0
        var measuredHeight = view.measuredHeight
        if (measuredHeight == 0) {
            measuredHeight = view.getUnDisplayViewSize().height
        }
        return measuredHeight
    }

    /**
     * 宽度获取要根据实际的XML来进行适配
     */
    private fun getChildWidth(): Int {
        val tv: TextView = getChildTextView() ?: return 0
        val iv: ImageView? = getChildImageView()
        var tvWidth = tv.paint.measureText(tv.text.toString()).toInt()
        var ivWidth = 0
        var ivPadding = 0
        var ivMargin = 0
        iv?.let {
            ivWidth = it.measuredWidth
            ivPadding = it.paddingStart + it.paddingEnd
            ivMargin = it.marginStart + it.marginEnd
            if (ivWidth == 0) {
                ivWidth = it.getUnDisplayViewSize().width
            }
        }
        var tvPadding = tv.paddingStart + tv.paddingEnd
        var tvMargin = tv.marginStart + tv.marginEnd
        var space = ivPadding + ivMargin + tvPadding + tvMargin
        val maxWith = tv.maxWidth
        if ((maxWith > 0) && (tvWidth > maxWith)) {
            tvWidth = maxWith - tvPadding
        }
        val width = tvWidth + ivWidth + space
        DebugUtil.d(TAG, "getChildWidth: $tvWidth ,${ivWidth},$width")
        return width
    }

    fun showAnimate() {
        release()
        if (!showAnimator.isRunning && !showAnimator.isStarted) {
            showAnimator.start()
        }
    }

    fun dismissAnimate() {
        release()
        if (!dismissAnimator.isRunning && !dismissAnimator.isStarted) {
            dismissAnimator.start()
        }
    }

    fun switchSpeaker(flag: Boolean) {
        release()
        when (layoutType) {
            TYPE_ORIGINAL -> switchSpeakerOriginal(flag)

            TYPE_SHARE -> switchSpeakerShare(flag)

            else -> DebugUtil.d(TAG, "switchSpeaker layoutType is unknown")
        }
    }

    private fun setWidthAndHeightAndMargin(w: Int, h: Int, ms: Int, mt: Int, me: Int, mb: Int) {
        layoutParams.apply {
            width = w + ms + me
            height = h + mt + mb
        }
        (shapeView.layoutParams as LayoutParams).apply {
            width = w
            height = h
        }
        requestLayout()
    }

    @SuppressLint("WrongViewCast")
    private fun getChildImageView(): ImageView? {
        return when (layoutType) {
            TYPE_ORIGINAL -> findViewById(R.id.ic_speaker)
            else -> null
        }
    }

    private fun getChildTextView(): TextView? {
        try {
            val view = getChildAt(1)
            return if (view is ViewGroup) {
                var tv: TextView? = null
                view.forEach {
                    if (it is TextView) {
                        tv = it
                    }
                }
                tv
            } else {
                view as? TextView
            }
        } catch (e: Exception) {
            DebugUtil.d(TAG, "${e.message}")
        }
        return null
    }

    override fun onFinishInflate() {
        super.onFinishInflate()
        if (contains(shapeView)) {
            removeView(shapeView)
        }
        addView(shapeView, 0)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val ws = MeasureSpec.getSize(widthMeasureSpec)
        val hs = MeasureSpec.getSize(heightMeasureSpec)
        measureChildren(
            MeasureSpec.makeMeasureSpec(ws, MeasureSpec.UNSPECIFIED),
            MeasureSpec.makeMeasureSpec(hs, MeasureSpec.UNSPECIFIED)
        )
    }

    fun release() {
        if (showAnimator.isRunning) {
            showAnimator.cancel()
        }
        if (dismissAnimator.isRunning) {
            dismissAnimator.cancel()
        }
    }

    fun setShapeViewBackGroundColor(color: Int) {
        val drawable = shapeView.background as? GradientDrawable ?: return
        if (shapeView.background is GradientDrawable) {
            drawable.setColor(color)
        }
    }

    private fun setChildAlphaOriginal(alpha: Float) {
        val textView = getChildTextView() ?: return
        val imageView = getChildImageView() ?: return
        textView.alpha = alpha
        imageView.alpha = alpha
        shapeView.alpha = alpha
        val newVisibility = if (alpha == minAlpha) GONE else VISIBLE
        if (visibility != newVisibility) {
            visibility = newVisibility
        }
    }

    private fun switchSpeakerOriginal(flag: Boolean) {
        setWidthAndHeightAndMargin(getChildWidth(), getChildHeight(), maxPaddingStart, maxPaddingTop, maxPaddingEnd, maxPaddingBottom)
        if (flag) {
            setChildAlpha(maxAlpha)
        } else {
            setChildAlpha(minAlpha)
        }
    }

    private fun setChildAlphaShare(alpha: Float) {
        val view = getChildTextView() ?: return
        view.alpha = alpha
        shapeView.alpha = alpha
    }

    private fun switchSpeakerShare(flag: Boolean) {
        if (flag) {
            setWidthAndHeightAndMargin(
                getChildWidth(),
                getChildHeight(),
                maxPaddingStart,
                maxPaddingTop,
                maxPaddingEnd,
                maxPaddingBottom
            )
            setChildAlpha(maxAlpha)
        } else {
            setWidthAndHeightAndMargin(0, 0, 0, 0, 0, 0)
            setChildAlpha(minAlpha)
        }
    }
}