/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertViewContainer
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import com.soundrecorder.base.utils.DebugUtil

class ConvertViewContainer @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var callback: ((width: Int, height: Int) -> Unit)? = null
    private var lastWidth = 0
    private var lastHeight = 0

    /**
     * 注册高度的监听
     */
    fun observeHeightChange(callback: ((width: Int, height: Int) -> Unit)) {
        this.callback = callback
    }

    /**
     * 解除注册
     */
    fun unObserveHeightChange() {
        this.callback = null
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        DebugUtil.d("ConvertViewContainer", "onSizeChanged w = $w, oldw = $oldw， h = $h, oldh = $oldh")
        if (lastWidth != w || lastHeight != h) {
            callback?.invoke(w, h)
            lastWidth = w
            lastHeight = h
        }
    }

    fun removeAllViewsExcept(keepView: View?) {
        for (i in childCount - 1 downTo 0) {
            val child = getChildAt(i)
            // 如果当前子视图不是要保留的视图，则删除
            if (child != keepView) {
                removeViewAt(i)
            }
        }
    }
}