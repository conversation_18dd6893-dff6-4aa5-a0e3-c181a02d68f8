/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertingView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.convert

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.RtlUtils
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.utils.ConvertDbUtil.SERVER_PLAN_ASR
import com.soundrecorder.common.utils.ConvertDbUtil.SERVER_PLAN_BEIYAN
import com.soundrecorder.common.utils.ConvertDbUtil.SERVER_PLAN_XUNFEI
import com.soundrecorder.common.widget.OSImageView

class ConvertingView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConvertView(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "ConvertingView"
        private const val BOTTOM_MARGIN_RECENT = 0.315f
    }

    var layoutByTop = false

    private var clickCancelListener: (() -> Unit)? = null

    private var container: ConstraintLayout? = null
    private var convertCancel: TextView? = null
    private var convertProgress: TextView? = null
    private var convertingDesc: TextView? = null
    private var convertingTextLayout: LinearLayout? = null



    override fun initContainer() {
        LayoutInflater.from(context)
            .inflate(com.soundrecorder.playback.R.layout.fragment_converting, this, true)
        container = findViewById(com.soundrecorder.playback.R.id.converting)
    }

    override fun initViews() {
        convertCancel = findViewById(com.soundrecorder.playback.R.id.convert_cancel)
        convertCancel?.setOnClickListener {
            clickCancelListener?.invoke()
        }
        COUITextViewCompatUtil.setPressRippleDrawable(convertCancel)
        convertProgress = findViewById(com.soundrecorder.playback.R.id.convert_progress)
        convertingDesc = findViewById(com.soundrecorder.playback.R.id.convert_desc)
        convertingTextLayout = findViewById(com.soundrecorder.playback.R.id.ll_converting)
        updateProgress(0)
    }

    override fun initLoadingView(): OSImageView? {
        return findViewById(com.soundrecorder.playback.R.id.loadingView)
    }

    override fun initTextLayout(): LinearLayout? {
        return findViewById(com.soundrecorder.playback.R.id.ll_converting)
    }

    override fun tag(): String {
        return TAG
    }

    fun setClickCancelListener(callback: () -> Unit) {
        clickCancelListener = callback
    }



    fun updateProgress(progress: Int) {
        var percent = if (progress > 0) "$progress%" else ""
        if (BaseApplication.sIsRTLanguage && (progress > 0)) {
            percent = RtlUtils.addDirectionSymbolForRtl(percent).toString()
        }
        val progressText = context.getString(com.soundrecorder.common.R.string.transfer_text_progress, percent)
        val descText = context.getString(com.soundrecorder.common.R.string.transferring_content_v2)
        convertProgress?.text = progressText
        convertingDesc?.text = descText
    }

    fun updateProgress(progress: Int, serverPlanCode: Int) {
        if (progress <= 0) {
            return
        }
        val context = BaseApplication.getAppContext()
        when (serverPlanCode) {
            SERVER_PLAN_XUNFEI -> {
                val totalTime = TimeUtils.seconds2min(progress)
                val progressText = context.getString(com.soundrecorder.common.R.string.transfer_text_progress, "")
                val text2 = context.getString(com.soundrecorder.common.R.string.total_transfer_time, totalTime)
                val text3 = context.getString(com.soundrecorder.common.R.string.transferring_content_v2)
                val descText = text2 + "\n" + text3
                convertProgress?.text = progressText
                convertingDesc?.text = descText
            }

            SERVER_PLAN_BEIYAN, SERVER_PLAN_ASR -> {
                val textProgress: String
                val descText = context.getString(com.soundrecorder.common.R.string.transferring_content_v2)
                var percent = "$progress% "

                if (BaseApplication.sIsRTLanguage) {
                    percent = RtlUtils.addDirectionSymbolForRtl(percent).toString()
                }
                textProgress = context.getString(com.soundrecorder.common.R.string.transfer_text_progress, percent)
                convertProgress?.text = textProgress
                convertingDesc?.text = descText
            }

            else -> DebugUtil.w(TAG, "updateProgress serverPlanCode not support:$serverPlanCode")
        }
    }

    override fun setLayoutParamsWhenShowImage() {
        DebugUtil.d(TAG, "setLayoutParamsWhenShowImage layoutByTop = $layoutByTop")
        if (layoutByTop) {
            super.setLayoutParamsWhenShowImage()
        } else {
            updateLayoutParams<LayoutParams> {
                bottomMargin = 0
            }
            textLayout?.updateLayoutParams<ConstraintLayout.LayoutParams> {
                topToTop = ConstraintLayout.LayoutParams.UNSET
                bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                bottomMargin = getBottomMargin()
            }
        }
    }

    /**
     * 从底部开始布局，考虑分屏的情况下是没有虚拟按键的，所以需要正常屏幕下减去虚拟按键的高度再成比例，防止分屏情况下间隔太大
     */
    private fun getBottomMargin(): Int {
        val activity = context as? Activity ?: return 0
        val screenHeight = ScreenUtil.getRealHeight(activity)
        val windowHeight = activity.window.decorView.height
        val scaleRadio = windowHeight / screenHeight.toFloat()
        DebugUtil.d(tag(), "screenHeight = $screenHeight, " +
                "windowHeight = $windowHeight, " +
                "scaleRadio = $scaleRadio, " +
                "bottomInset = $bottomInset")
        return (windowHeight * BOTTOM_MARGIN_RECENT * scaleRadio).toInt()
    }
}