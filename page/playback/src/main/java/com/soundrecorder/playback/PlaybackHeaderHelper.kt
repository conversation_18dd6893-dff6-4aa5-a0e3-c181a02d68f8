/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackHeaderHelper
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback

import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.textview.COUITextView
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.utils.cancelAnimationExt
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.invisible
import com.soundrecorder.common.utils.playAnimationExt
import com.soundrecorder.common.utils.visible
import com.soundrecorder.common.widget.COUIAnimateTextView
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_COMPLETE
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel.Companion.CONVERT_STATUS_SUMMARY_NONE_COMPLETE
import com.soundrecorder.playback.newconvert.util.ListUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class PlaybackHeaderHelper(private val containerFragment: PlaybackContainerFragment) {

    companion object {
        private const val TAG = "PlaybackHeaderHelper"
        const val DIVIDER_WITH_SPACE = " ｜ "
    }

    private var titleView: COUIAnimateTextView? = null
    private var timeView: COUITextView? = null
    private var loading: EffectiveAnimationView? = null
    private var viewModel: PlaybackContainerViewModel? = null
    private var convertViewModel: PlaybackConvertViewModel? = null

    private var mediaId: Long = -1
    private var recordName: String = ""
    private var duration: Long = 0

    fun initHeader() {
        initViewModel()
        titleView = containerFragment.contentBinding?.headerContainer?.recordTitleTimeLayout?.findViewById(R.id.record_title)
        timeView = containerFragment.contentBinding?.headerContainer?.recordTitleTimeLayout?.findViewById(R.id.record_time)
        loading = containerFragment.contentBinding?.headerContainer?.recordTitleTimeLayout?.findViewById(R.id.loading)
    }

    private fun initViewModel() {
        viewModel = containerFragment.mViewModel
        convertViewModel = containerFragment.mPlaybackConvertViewModel
    }

    fun bindHeadData(mediaId: Long, name: String, duration: Long) {
        this.mediaId = mediaId
        this.recordName = name.title() ?: ""
        this.duration = duration
        titleView?.text = recordName
        setTimeView(mediaId, name, duration)
    }

    fun refreshName(newName: String?, animation: Boolean = false) {
        DebugUtil.d(TAG, "refreshName recordName = $recordName, newName = $newName")
        newName ?: return
        val newTitle = newName.title() ?: ""
        if (newTitle == recordName) {
            return
        }
        this.recordName = newTitle
        titleView?.cancelAnimation()
        if (animation) {
            titleView?.setAnimateText(recordName, false)
        } else {
            titleView?.text = recordName
        }
    }

    fun onSmartNameStatusChange(display: Boolean, resultName: String?) {
        if (display) {
            showLoading()
        } else {
            showTitle(resultName)
        }
    }

    fun checkShowHead() {
        val complete = convertViewModel?.mConvertStatus?.value == CONVERT_STATUS_COMPLETE
                || convertViewModel?.mConvertStatus?.value == CONVERT_STATUS_SUMMARY_NONE_COMPLETE
        DebugUtil.d(TAG, "checkShowTabLayout convertStatus:${convertViewModel?.mConvertStatus?.value}," +
                "convertSearch:${convertViewModel?.mIsInConvertSearch?.value}"
        )
        val result = complete
                && containerFragment.recordFilterIsRecycle().not()
                && (convertViewModel?.mIsInConvertSearch?.value != true)
        if (result) {
            headViewVisible()
        } else {
            headViewGone()
        }
    }

    fun headViewVisible() {
        loading?.gone()
        timeView?.visible()
        titleView?.visible()
    }

    fun headViewGone() {
        loading?.cancelAnimationExt(true)
        timeView?.gone()
        titleView?.gone()
        titleView?.cancelAnimation()
    }

    private fun setTimeView(mediaId: Long, name: String, duration: Long) {
        val lifecycle = containerFragment.lifecycleScope
        lifecycle.launch {
            val targetTime = withContext(Dispatchers.IO) {
                formatTime(mediaId, name, duration)
            }
            timeView?.text = targetTime
        }
    }

    private fun formatTime(mediaId: Long, name: String, duration: Long): String {
        val recordTime = ListUtil.getTimeString(mediaId, name)
        val markTime = TimeUtils.getFormatTimeExclusiveMillLeastHour(duration, true)
        return recordTime + DIVIDER_WITH_SPACE + markTime
    }

    fun getTitle(): String {
        return viewModel?.getRecord()?.displayName?.title() ?: ""
    }

    fun getTime(): String {
        val text = timeView?.text?.toString()
        return if (text?.isNotEmpty() == true) {
            text
        } else {
            formatTime(mediaId, recordName, duration)
        }
    }

    private fun showLoading() {
        loading?.visible()
        titleView?.invisible()
        loading?.playAnimationExt()
    }

    private fun showTitle(newTitle: String?) {
        if (newTitle.isNullOrEmpty()) {
            loading?.cancelAnimationExt()
            titleView?.visible()
            return
        }
        recordName = newTitle
        loading?.cancelAnimationExt(true)
        titleView?.visible()
        titleView?.cancelAnimation()
        titleView?.setAnimateText(newTitle, false)
        titleView?.setAnimationListener {
            titleView?.text = newTitle
        }
    }
}