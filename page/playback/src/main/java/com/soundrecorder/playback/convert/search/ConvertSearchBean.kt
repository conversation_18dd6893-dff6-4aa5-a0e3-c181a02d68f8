/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.convert.search

data class ConvertSearchBean(
    val keyWord: String,    // 搜索关键字
    val textItemIndex: Int,  // 在整个数据列表中TextMetaData的index
    val keyWordIndex: Int,  // 在TextMetaData中关键字的Index
    var focus: Boolean = false //当前是否选中
) {
    override fun toString(): String {
        return "ConvertSearchBean(keyWord='$keyWord',  textItemIndex:$textItemIndex ,  keyWordIndex=$keyWordIndex, focus=$focus)"
    }
}