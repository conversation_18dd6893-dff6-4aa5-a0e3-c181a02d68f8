/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ConvertFooterViewHolder.kt
 ** Description : ConvertFooterViewHolder.kt
 ** Version     : 1.0
 ** Date        : 2025/07/03
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/07/03      1.0      create
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui.vh

import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewParent
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.oplus.atlas.DebugLog
import com.soundrecorder.base.ext.durationHasHourFormatTimeExclusive
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.exportfile.ExportDoc
import com.soundrecorder.common.exportfile.ExportPdf
import com.soundrecorder.common.exportfile.SummaryContentViewUtil
import com.soundrecorder.common.exportfile.ui.ExportTipsManager
import com.soundrecorder.common.share.ShareSummaryCopy
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.R
import com.soundrecorder.playback.newconvert.ui.ConvertViewContainer
import com.soundrecorder.playback.newconvert.ui.TextImageItemAdapter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

class ConvertFooterViewHolder(parent: ViewGroup, adapter: TextImageItemAdapter) : ViewHolder(
    LayoutInflater.from(parent.context).inflate(R.layout.footer_convert_content, parent, false)
) {

    companion object {
        private const val TAG = "ConvertFooterViewHolder"
        private const val ITEM_POSITION_0 = 0
        private const val ITEM_POSITION_1 = 1
        private const val LOADING_TIPS_DELAY = 1000L
        private const val DOC_SUFFIX = "doc"
        private const val PDF_SUFFIX = "pdf"
    }

    private val mCopy: AppCompatImageView = itemView.findViewById(R.id.footer_copy)
    private val mExport: AppCompatImageView = itemView.findViewById(R.id.footer_export)
    private val mRefresh: AppCompatImageView = itemView.findViewById(R.id.footer_refresh)
    private val mShareAction by lazy { Injector.injectFactory<ShareAction>() }
    private var mExportPopList: COUIPopupListWindow? = null
    private val mLifecycle = (itemView.context as? AppCompatActivity)?.lifecycleScope
    private val mContext = itemView.context
    private val exportTipsManager: ExportTipsManager by lazy { ExportTipsManager() }

    private val mWeakAdapter = WeakReference(adapter)

    fun bindData() {
        mCopy.setOnClickListener {
            DebugUtil.d(TAG, "click footer copy")
            doCopy()
        }
        mExport.setOnClickListener {
            DebugUtil.d(TAG, "click footer export")
            showExportPopMenu()
        }
        mRefresh.visibility = View.GONE
        mRefresh.setOnClickListener {
            DebugUtil.d(TAG, "click footer refresh")
        }
    }

    private fun doCopy() {
        val adapter = mWeakAdapter.get()
        if (adapter == null) {
            DebugLog.d(TAG, "doCopy adapter is null")
            return
        }
        val sp = StringBuilder()
        sp.append(getTextTitle(true))
        sp.append(getOriginContentText())
        val activity = itemView.context as? Activity
        val lifecycle = (itemView.context as? AppCompatActivity)?.lifecycleScope
        if (activity == null || lifecycle == null) {
            DebugUtil.d(TAG, "do copy. activity or lifecycle is null")
            return
        }
        mShareAction?.share(
            activity,
            ShareTextContent(-1, false, "", 0, emptyList()),
            ShareSummaryCopy(sp.toString()),
            lifecycle,
            null
        )
    }

    private fun showExportPopMenu() {
        mExportPopList?.dismiss()
        val context = itemView.context
        (context as? AppCompatActivity)?.lifecycleScope?.launch {
            val isSupportExportDoc = withContext(Dispatchers.Default) {
                context.let { ExportDoc.isSupportExport(it) }
            }
            val exportItemList = mutableListOf<PopupListItem>().apply {
                val builder = PopupListItem.Builder()
                if (isSupportExportDoc) {
                    builder.setTitle(
                        context.getString(
                            com.soundrecorder.common.R.string.summary_export_to,
                            context.getString(com.soundrecorder.common.R.string.word)
                        )
                    ).setGroupId(com.soundrecorder.common.R.id.group1)
                    add(builder.build())
                }
                builder.reset()

                builder.setTitle(
                    context.getString(
                        com.soundrecorder.common.R.string.summary_export_to,
                        context.getString(com.soundrecorder.common.R.string.pdf)
                    )
                ).setGroupId(com.soundrecorder.common.R.id.group2)
                add(builder.build())
            }

            mExportPopList = COUIPopupListWindow(context).apply {
                this.itemList = exportItemList
                this.anchorView = mExport
                this.resetOffset()
                this.setOnItemClickListener { _, _, pos, _ ->
                    if (ClickUtils.isQuickClick()) {
                        return@setOnItemClickListener
                    }
                    doPopListItemClick(pos)
                    this.dismiss()
                }
                this.show()
            }
        }
    }

    private fun doPopListItemClick(pos: Int) {
        when (pos) {
            ITEM_POSITION_0 -> {
                DebugUtil.d(TAG, "click export to doc")
                doExportToDoc()
            }

            ITEM_POSITION_1 -> {
                DebugUtil.d(TAG, "click export to pdf")
                doExportToPdf()
            }

            else ->  DebugUtil.d(TAG, "click Unknown")
        }
    }

    private fun doExportToDoc() {
        mLifecycle?.launch(Dispatchers.IO) {
            runCatching {
                val title = getTextTitle(false)
                // 准备导出数据
                val exportData = SummaryContentViewUtil.prepareExportData(mContext, title, getOriginContentText())
                // 生成文件路径
                val targetPath = SummaryContentViewUtil.generateExportFilePath(mContext, DOC_SUFFIX, title)
                if (targetPath.isEmpty()) {
                    exportTipsManager.showExportError(mContext.getString(com.soundrecorder.common.R.string.save_failed))
                    return@launch
                }

                // 执行导出
                val success = ExportDoc.saveToWord(mContext, targetPath, exportData)

                withContext(Dispatchers.Main) {
                    //导出成功 弹窗 button 点击跳转文档预览文件
                    if (success) {
                        exportTipsManager.showExportProcessDialog(mContext, com.soundrecorder.common.R.string.summary_export_loading) {
                            exportTipsManager.showSnackBar(
                                mContext,
                                findRootView(),
                                mContext.getString(com.soundrecorder.common.R.string.summary_export_note_finish),
                                mContext.getString(com.soundrecorder.common.R.string.export_view_look)
                            ) {
                                SummaryContentViewUtil.openDocumentFile(mContext, targetPath)
                            }
                        }
                    } else {
                        exportTipsManager.showExportError(mContext.getString(com.soundrecorder.common.R.string.save_failed))
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "exportFile error: ${it.message}")
            }
        }
    }

    private fun doExportToPdf() {
        mLifecycle?.launch(Dispatchers.IO) {
            runCatching {
                val title = getTextTitle(false)
                // 准备导出数据
                val exportData = SummaryContentViewUtil.prepareExportData(mContext, title, getOriginContentText())
                // 生成文件路径
                val targetPath = SummaryContentViewUtil.generateExportFilePath(mContext, PDF_SUFFIX, title)

                if (targetPath.isEmpty()) {
                    exportTipsManager.showExportError(mContext.getString(com.soundrecorder.common.R.string.save_failed))
                    return@launch
                }

                // 执行导出
                val success = ExportPdf.saveToPdf(mContext, targetPath, exportData)

                withContext(Dispatchers.Main) {
                    if (success) {
                        exportTipsManager.showExportProcessDialog(mContext, com.soundrecorder.common.R.string.summary_export_loading) {
                            exportTipsManager.showSnackBar(
                                mContext,
                                findRootView(),
                                mContext.getString(com.soundrecorder.common.R.string.summary_export_note_finish),
                                mContext.getString(com.soundrecorder.common.R.string.export_view_look)
                            ) {
                                SummaryContentViewUtil.openDocumentFile(mContext, targetPath)
                            }
                        }
                    } else {
                        exportTipsManager.showExportError(mContext.getString(com.soundrecorder.common.R.string.save_failed))
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "exportToPdf error: ${it.message}")
            }
        }
    }

    private fun getTextTitle(isCopy: Boolean): String {
        val adapter = mWeakAdapter.get()
        if (adapter == null) {
            DebugLog.d(TAG, "getTextTitle adapter is null")
            return ""
        }
        val sp = StringBuilder()
        sp.append(adapter.mPlayName.title())
        if (isCopy) {
            sp.append("\n")
            sp.append(adapter.mCreateTime.toString() + "\n")
        }
        return sp.toString()
    }

    private fun getOriginContentText(): String {
        val adapter = mWeakAdapter.get()
        if (adapter == null) {
            DebugLog.d(TAG, "getOriginContentText adapter is null")
            return ""
        }
        val sp = StringBuilder()
        adapter.data?.forEach {
            if (it is ConvertContentItem.TimerDividerMetaData) {
                if (adapter.mNeedShowRole) {
                    sp.append("${it.roleName} ${it.startTime.durationHasHourFormatTimeExclusive()}\n")
                } else {
                    sp.append("${it.startTime.durationHasHourFormatTimeExclusive()}\n")
                }
            }
            if (it is ConvertContentItem.TextItemMetaData) {
                sp.append(it.getTextString() + "\n\n")
            }
        }
        return sp.toString()
    }

    /**
     * 寻找文本详解界面的根布局，用作锚点，确保SnackBar不被悬浮按钮遮挡
     */
    private fun findRootView(): View {
        var parent = itemView.parent
        while (parent is ViewParent) {
            if (parent is ConvertViewContainer) {
                return parent
            }
            parent = parent.parent
        }
        return itemView
    }
}