/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.search

import android.content.Context
import android.graphics.Rect
import android.text.TextUtils
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.playback.R
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.convert.search.ConvertSearchBean
import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel

class SearchScrollHelper(
    private val mContainer: ViewGroup,
    private val mConvertViewModel: PlaybackConvertViewModel,
    private val mRecyclerView: COUIRecyclerView
) {

    companion object {
        const val TAG = "SearchScrollHelper"
    }

    private var mFocusSearchBean: ConvertSearchBean? = null
    private var mLayoutManager: LinearLayoutManager? = null
    private var mAdapter: ConvertSearchAdapter? = null
    private var mMeasureTextView: TextView? = null
    private var mContext: Context = mContainer.context

    // 输入法
    private val inputMethodManager: InputMethodManager by lazy {
        mContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    }

    /**
     * 文本列表RecyclerView滚动监听
     */
    private val mOnScrollListener: RecyclerView.OnScrollListener = object : RecyclerView.OnScrollListener() {

        override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
            super.onScrollStateChanged(recyclerView, newState)
            DebugUtil.d(TAG, "onScrollStateChanged $newState")
            if (RecyclerView.SCROLL_STATE_DRAGGING == newState) { //手指滚动文字内容时，隐藏键盘
                hideSoftInput()
            }
        }
    }

    init {
        mAdapter = mRecyclerView.adapter as ConvertSearchAdapter
        mLayoutManager = mRecyclerView.layoutManager as LinearLayoutManager
    }

    fun setFocusSearchBean(bean: ConvertSearchBean?) {
        mFocusSearchBean = bean
    }

    fun updateByKeyWord() {
        val focusTextImageItemIndex = mFocusSearchBean?.textItemIndex ?: 0
        mConvertViewModel.mTextItems?.let {
            val pair = mConvertViewModel.rectifyStartSeqForTextMetaDataItem(mFocusSearchBean)
            if (!TextUtils.isEmpty(pair.first)) {
                val offset = getKeyWordOffset(pair.first, pair.second)
                DebugUtil.i(TAG, "updateByKeyWord executed. realTextContent=${pair.first}, startSeq=${pair.second}, offset=$offset")
                scrollToKeyWord(focusTextImageItemIndex, offset)
            }
        }
    }


    private fun getKeyWordOffset(text: String, startSeq: Int): Int {
        val offset = getScrollOffSet(text, startSeq)
        DebugUtil.i(TAG, "getKeyWordOffset inputString $text, startSeq $startSeq")
        return offset
    }

    fun getScrollOffSet(textContent: String, startSeq: Int): Int {
        if (mMeasureTextView == null) {
            mMeasureTextView = mContainer.findViewById(R.id.measureTextView)
        }
        mMeasureTextView?.text = textContent
        val line = mMeasureTextView?.layout?.getLineForOffset(startSeq) //获取字符在第几行
        val bound = Rect()
        line?.let { mMeasureTextView?.layout?.getLineBounds(it, bound) }
        val charY = bound.top
        mMeasureTextView?.text = ""
        return charY
    }

    //TODO: smoothScrollToPosition()在滚动距离过长时存在定位不准问题，因此先使用scrollToPositionWithOffset()
    private fun scrollToKeyWord(focusParagraph: Int, offset: Int) {
        if (focusParagraph + 1 < mAdapter?.itemCount ?: 0) {
            mRecyclerView.stopScroll()
            mRecyclerView.post {
                mLayoutManager?.scrollToPositionWithOffset(
                    focusParagraph + 1,
                    mContainer.height / 5 - offset
                )
            }
        }
    }

    /**
     * 添加滚动监听
     */
    fun addOnScrollListener() {
        DebugUtil.d(TAG, "add scroll listener")
        mRecyclerView.addOnScrollListener(mOnScrollListener)
    }

    /**
     * 移除滚动监听
     */
    fun removeOnScrollListener() {
        DebugUtil.d(TAG, "remove scroll listener")
        mRecyclerView.removeOnScrollListener(mOnScrollListener)
    }

    /**
     * 隐藏软键盘
     */
    private fun hideSoftInput() {
        if (inputMethodManager.isActive) {
            DebugUtil.d(TAG, "hide softInput")
            inputMethodManager.hideSoftInputFromWindow(mRecyclerView.windowToken, 0)
        }
    }
}