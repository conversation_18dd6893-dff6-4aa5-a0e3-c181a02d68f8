/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ConvertImmersiveAnimationHelper
 * * Description: ConvertImmersiveAnimationHelper
 * * Version: 1.0
 * * Date : 2025/4/18
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/4/18   1.0    build this module
 ****************************************************************/
package com.soundrecorder.playback

import android.animation.ValueAnimator
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.animation.COUISpringInterpolator
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.playback.databinding.FragmentPlaybackContainerBinding

class ConvertImmersiveAnimationHelper(
    private val binding: FragmentPlaybackContainerBinding,
    private val floatPanelAnimationHelper: ExpandCollapseAnimationHelper
) {
    companion object {
        private const val TAG: String = "ConvertImmersiveAnimationHelper"
        private const val DEFAULT_SPRING_BOUNCE: Float = 0f
        private const val RESPONSE_TOOLBAR_ANIMATION: Float = 0.0f
        private const val RESPONSE_FLOAT_PANEL_ANIMATION: Float = 0.3f
        private const val TOOL_BAR_DURATION = 400L
        private const val FLOAT_PANEL_TRAN_Y = 30f
    }

    private var navigationHeight: Int = 0

    //导航栏
    private var toolbarHeight: Int = 0
    private var toolbarHeightImmersive = 0
    private var toolbarAnimator: ValueAnimator? = null
    private var floatPanelTranAnimator: COUISpringAnimation? = null
    private var floatPanelAlphaAnimator: COUISpringAnimation? = null
    private var floatTranY: Float = 0f

    //播放进度与时间
    private var windowType: WindowType? = null

    fun checkUpdateWindowTypeChanged(navigationHeight: Int, windowType: WindowType) {
        if (this.windowType == null || this.windowType == windowType) {
            DebugUtil.d(TAG, "checkUpdateWindowTypeChanged return $windowType")
            return
        }
        DebugUtil.i(TAG, "checkUpdateWindowTypeChanged")
        resetAnimator()
        binding.rootView.post {
            if (this.windowType == windowType) {
                DebugUtil.d(TAG, "checkUpdateWindowTypeChanged return $windowType")
                return@post
            }
            startImmersiveAnimation(navigationHeight, windowType)
        }
    }

    /**
     * 进入沉浸态
     */
    fun startImmersiveAnimation(navigationHeight: Int, windowType: WindowType?) {
        DebugUtil.i(TAG, "startImmersiveAnimation navigationHeight = $navigationHeight, windowType=$windowType")
        this.windowType = windowType
        this.navigationHeight = navigationHeight
        //底部系统任务栏高度
        toolbarHideAnimation()
        floatPanelHideTranAnimation()
        floatPanelHideAlphaAnimation()
    }

    /**
     * 退出沉浸态
     */
    fun reverseImmersiveAnimation() {
        DebugUtil.i(TAG, "reverseImmersiveAnimation")
        recoverAnimation()
    }

    /**
     * 中大屏任务栏固定和悬浮是要重新计算底部面板的高度
     */
    fun updateImmersiveNavigationHeight(navigationHeight: Int) {
        this.navigationHeight = navigationHeight
        startImmersiveAnimation(navigationHeight, windowType)
    }

    /**
     * 导航栏隐藏动画
     */
    private fun toolbarHideAnimation() {
        toolbarHeight = binding.appbarLayout.height
        toolbarHeightImmersive = binding.headerContainer.tabLayout.height
        //目标的top是appbarLayout的高度减去tabLayout的高度
        val targetMarginTop = toolbarHeightImmersive - toolbarHeight
        toolbarAnimator = ValueAnimator.ofInt(0, targetMarginTop)
        toolbarAnimator?.duration = TOOL_BAR_DURATION
        toolbarAnimator?.interpolator = COUISpringInterpolator(RESPONSE_TOOLBAR_ANIMATION.toDouble(), 0.0)
        toolbarAnimator?.addUpdateListener { animation ->
            binding.appbarLayout.updateLayoutParams<ConstraintLayout.LayoutParams> {
                topMargin = animation.animatedValue as Int
            }
        }
        toolbarAnimator?.start()
    }

    private fun floatPanelHideTranAnimation() {
        val floatPanel = binding.floatButtonPanel.panel
        floatTranY = if (floatPanelAnimationHelper.isExpand()) {
            (floatPanel.height + navigationHeight).toFloat()
        } else {
            FLOAT_PANEL_TRAN_Y
        }
        DebugUtil.d(TAG, "floatPanelHideTranAnimation floatTranY = $floatTranY")
        if (floatPanelTranAnimator == null) {
            val springForce = COUISpringForce().setBounce(DEFAULT_SPRING_BOUNCE)
                .setResponse(RESPONSE_FLOAT_PANEL_ANIMATION)
                .setFinalPosition(floatTranY)
            floatPanelTranAnimator = COUISpringAnimation(
                floatPanel,
                COUIDynamicAnimation.TRANSLATION_Y
            ).setStartVelocity(0f).setSpring(springForce)
        } else {
            floatPanelTranAnimator?.spring?.finalPosition = floatTranY
        }
        floatPanelTranAnimator?.start()
    }

    private fun floatPanelHideAlphaAnimation() {
        //展开只有移位动画，没有alpha
        if (floatPanelAnimationHelper.isExpand()) {
            return
        }
        if (floatPanelAlphaAnimator == null) {
            val floatPanel = binding.floatButtonPanel.panel
            val springForce = COUISpringForce().setBounce(DEFAULT_SPRING_BOUNCE)
                .setResponse(RESPONSE_FLOAT_PANEL_ANIMATION)
                .setFinalPosition(0f)
            floatPanelAlphaAnimator = COUISpringAnimation(
                floatPanel,
                COUIDynamicAnimation.ALPHA
            ).setStartVelocity(0f).setSpring(springForce)
        } else {
            floatPanelAlphaAnimator?.spring?.finalPosition = 0f
        }
        floatPanelAlphaAnimator?.start()
    }


    private fun recoverAnimation() {
        toolbarShowAnimation()
        floatPanelShowAnimation()
    }

    private fun toolbarShowAnimation() {
        //目标的top是appbarLayout的高度减去tabLayout的高度
        val targetMarginTop = toolbarHeightImmersive - toolbarHeight
        toolbarAnimator = ValueAnimator.ofInt(targetMarginTop, 0)
        toolbarAnimator?.interpolator = COUISpringInterpolator(RESPONSE_TOOLBAR_ANIMATION.toDouble(), 0.0)
        toolbarAnimator?.duration = TOOL_BAR_DURATION
        toolbarAnimator?.addUpdateListener { animation ->
            binding.appbarLayout.updateLayoutParams<ConstraintLayout.LayoutParams> {
                topMargin = animation.animatedValue as Int
            }
        }
        toolbarAnimator?.start()
    }

    private fun floatPanelShowAnimation() {
        floatPanelTranAnimator?.animateToFinalPosition(0f)
        floatPanelAlphaAnimator?.animateToFinalPosition(1f)
    }

    private fun resetAnimator() {
        cancelAnimation()
        toolbarAnimator = null
        floatPanelTranAnimator = null
        floatPanelAlphaAnimator = null
    }

    private fun cancelAnimation() {
        cancelAnimation(toolbarAnimator)
        cancelAnimation(floatPanelTranAnimator)
        cancelAnimation(floatPanelAlphaAnimator)
    }

    private fun cancelAnimation(animation: COUISpringAnimation?) {
        animation?.cancel()
    }

    private fun cancelAnimation(animation: ValueAnimator?) {
        animation?.cancel()
    }

    fun isInAnimation(): Boolean {
        return toolbarAnimator?.isRunning == true
                || floatPanelAlphaAnimator?.isRunning == true
                || floatPanelTranAnimator?.isRunning == true
    }

    fun release() {
        resetAnimator()
        windowType = null
    }
}