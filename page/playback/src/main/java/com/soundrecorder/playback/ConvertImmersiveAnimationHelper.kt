/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ConvertImmersiveAnimationHelper
 * * Description: ConvertImmersiveAnimationHelper
 * * Version: 1.0
 * * Date : 2025/4/18
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/4/18   1.0    build this module
 ****************************************************************/
package com.soundrecorder.playback

import android.content.Context
import android.view.ViewGroup.LayoutParams
import android.view.ViewGroup.MarginLayoutParams
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.animation.dynamicanimation.COUIDynamicAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringAnimation
import com.coui.appcompat.animation.dynamicanimation.COUISpringForce
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.playback.databinding.FragmentPlaybackContainerBinding
import com.soundrecorder.playback.view.SegmentSeekBar
import kotlin.math.abs

class ConvertImmersiveAnimationHelper(
    private val context: Context,
    private val viewModel: PlaybackContainerViewModel?,
    private val binding: FragmentPlaybackContainerBinding
) {

    companion object {
        private const val TAG: String = "ConvertImmersiveAnimationHelper"
        private const val DEFAULT_SPRING_BOUNCE: Float = 0f
        private const val RESPONSE_SEEKBAR_ANIMATION: Float = 0.4f
        private const val RESPONSE_DURATION_ANIMATION: Float = 0.4f
        private const val RESPONSE_DURATION_ALPHA_ANIMATION: Float = 0.1f
        private const val RESPONSE_CURRENT_ANIMATION: Float = 0.4f
        private const val RESPONSE_TOOLBAR_ANIMATION: Float = 0.3f
        private const val RESPONSE_BOTTOM_MENU_ANIMATION: Float = 0.4f
        private const val RESPONSE_PLAY_ACTION_ANIMATION: Float = 0.4f
        private const val RESPONSE_SPEED_CUT_IN_ANIMATION: Float = 0.1f
        private const val RESPONSE_SPEED_CUT_OUT_ANIMATION: Float = 0.3f
    }

    private var speedAnimation: COUISpringAnimation? = null
    private var cutAnimation: COUISpringAnimation? = null

    //任务栏的高度
    private var navigationHeight: Int = 0

    //播放控制按钮
    private var distancePlayActionY: Float = 1f
    private var scaleValuePlay: Float = 1f
    private var playScaleX: COUISpringAnimation? = null
    private var playScaleY: COUISpringAnimation? = null
    private var distanceForwardX: Float = 1f
    private var forwardTranslationX: COUISpringAnimation? = null
    private var forwardTranslationY: COUISpringAnimation? = null
    private var distancePlayX: Float = 1f
    private var playTranslationX: COUISpringAnimation? = null
    private var playTranslationY: COUISpringAnimation? = null
    private var distanceBackX: Float = 1f
    private var backwardTranslationX: COUISpringAnimation? = null
    private var backwardTranslationY: COUISpringAnimation? = null

    //导航栏和底部控制面板
    private var bottomMenuHeightNormal: Float = 0f
    private var bottomMenuHeightImmersive: Float = 0f
    private var bottomMenuAnimator: COUISpringAnimation? = null
    private var toolbarHeight: Float = 0f
    private var hideToolbarAnimator: COUISpringAnimation? = null

    //播放进度与时间
    private var seekBar: SegmentSeekBar? = null
    private var distanceSeekBarY: Float = 0f
    private var seekBarTranslationY: COUISpringAnimation? = null
    private var distanceSeekBarX: Float = 0f
    private var seekBarTranslationX: COUISpringAnimation? = null
    private var distanceCurrentY: Float = 0f
    private var currentTranslationY: COUISpringAnimation? = null
    private var distanceCurrentX: Float = 0f
    private var currentTranslationX: COUISpringAnimation? = null
    private var distanceDurationY: Float = 0f
    private var durationTranslationY: COUISpringAnimation? = null
    private var distanceDurationX: Float = 0f
    private var durationTranslationX: COUISpringAnimation? = null
    private var durationAlphaFirst: COUISpringAnimation? = null
    private var durationAlphaSecond: COUISpringAnimation? = null
    private var seekBarNormalWidth: Float = 0f
    private var seekBarImmersiveWidth: Float = 0f
    private var translationSeekBarWidth: COUISpringAnimation? = null
    private var seekBarMargin: Float = 0f
    private var translationSeekBarMargin: COUISpringAnimation? = null
    private var windowType: WindowType? = null

    /*大屏activity 操作按钮 摘要、标记等*/
    private var immersiveOperatorAlphaAnimation: COUISpringAnimation? = null

    //触发切换沉浸态第一段动画可滚动的距离
    private val immersiveFirstMoveDownDistance: Float by lazy {
        context.resources?.getDimension(R.dimen.distance_immersive_first_move_down) ?: 0f
    }

    //沉浸态下播放按钮的大小
    private val immersivePlayButtonSize: Float by lazy {
        context.resources.getDimension(R.dimen.circle_record_button_diam_immersive)
    }

    //沉浸态下播放控制按钮的外边距
    private val immersiveActionMargin: Float by lazy {
        context.resources.getDimension(R.dimen.action_margin_immersive)
    }

    //沉浸态下底部控制栏的高度
    private val immersiveBottomMenuHeight: Float by lazy {
        context.resources.getDimension(R.dimen.bottom_menu_height_immersive)
    }

    //沉浸态下底部控制栏的左边距
    private val immersiveBottomMenuLeftMargin: Float by lazy {
        context.resources.getDimension(R.dimen.bottom_menu_left_margin_immersive)
    }

    //沉浸态下底部控制栏的右边距
    private val immersiveBottomMenuRightMargin: Float by lazy {
        context.resources.getDimension(R.dimen.bottom_menu_right_margin_immersive)
    }

    //沉浸态下底部控制栏的底部边距
    private val immersiveBottomMenuBottomMargin: Float by lazy {
        context.resources.getDimension(R.dimen.bottom_menu_bottom_margin_immersive)
    }

    //沉浸态下总时长的右边距
    private val immersiveDurationRightMargin: Float by lazy {
        context.resources.getDimension(R.dimen.duration_right_margin_immersive)
    }
    private val immersiveAnimationEndListener =
        COUIDynamicAnimation.OnAnimationEndListener { animation, canceled, value, velocity ->
            DebugUtil.i(TAG, "immersiveAnimationEndListener $windowType")
            viewModel?.isImmersiveAnimationRunning = false
            enableSeekbarDeformation(false)
        }
    private val reverseImmersiveAnimationEndListener =
        COUIDynamicAnimation.OnAnimationEndListener { animation, canceled, value, velocity ->
            DebugUtil.i(TAG, "reverseImmersiveAnimationEndListener windowtype = $windowType")
            viewModel?.isImmersiveAnimationRunning = false
            enableSeekbarDeformation(true)
        }
    private val durationAlphaFirstEndListener =
        COUIDynamicAnimation.OnAnimationEndListener { animation, canceled, value, velocity ->
            DebugUtil.i(TAG, "durationAlphaFirstEndListener windowtype = $windowType")
            durationAlphaSecond?.start()
        }

    fun checkUpdateWindowTypeChanged(navigationHeight: Int, windowType: WindowType) {
        if (this.windowType == null || this.windowType == windowType) {
            DebugUtil.d(TAG, "checkUpdateWindowTypeChanged return $windowType")
            return
        }
        DebugUtil.i(TAG, "checkUpdateWindowTypeChanged")
        resetViewPosition()
        resetAnimator()
        binding.buttonPanel.middleControl.post {
            if (this.windowType == windowType) {
                DebugUtil.d(TAG, "checkUpdateWindowTypeChanged return $windowType")
                return@post
            }
            startImmersiveAnimation(navigationHeight, windowType)
        }
    }

    /**
     * 进入沉浸态
     */
    fun startImmersiveAnimation(navigationHeight: Int, windowType: WindowType?) {
        DebugUtil.i(TAG, "startImmersiveAnimation navigationHeight:$navigationHeight,windowType=$windowType")
        this.windowType = windowType
        //底部系统任务栏高度
        this.navigationHeight = navigationHeight
        val right = binding.rootView.right
        //底部控制面板的高度
        bottomMenuHeightImmersive = immersiveBottomMenuHeight + navigationHeight
        //控制按钮底部间距
        val bottomMargin = immersiveBottomMenuBottomMargin + navigationHeight
        //播放控制按钮下移距离（播放按钮父布局的高度 - 播报按钮bottom - 沉浸态时的底部间距 - 第一段动画已经下移的距离）
        distancePlayActionY =
            abs(
                binding.buttonPanel.root.height - binding.buttonPanel.middleControl.bottom -
                        bottomMargin - immersiveFirstMoveDownDistance
            )
        playActionAnimation(right, distancePlayActionY)
        playbackProgressAnimation(right, distancePlayActionY)
        speedAndCutImmersiveAnimation()
        toolbarAnimation()
    }

    /**
     * 退出沉浸态
     */
    fun reverseImmersiveAnimation() {
        DebugUtil.i(TAG, "reverseImmersiveAnimation")
        playActionRecoverAnimation()
        playbackProgressRecoverAnimation()
        speedAndCutRecoverAnimation()
        toolbarRecoverAnimation()
        operatorButtonsRecoverAnimation()
    }

    /**
     * 中大屏任务栏固定和悬浮是要重新计算底部面板的高度
     */
    fun updateImmersiveNavigationHeight(navigationHeight: Int, isImmersiveState: Boolean) {
        DebugUtil.i(TAG, "updateImmersiveNavigationHeight navigationHeight:$navigationHeight")
        //bottomMenuHeightNormal等于0也就是还未进入过沉浸态，也就无需更新状态
        if (bottomMenuHeightNormal == 0f) {
            DebugUtil.i(TAG, "updateImmersiveNavigationHeight bottomMenuHeightNormal is 0")
            return
        }
        binding.buttonPanel.root.post {
            if (this.navigationHeight == navigationHeight) {
                DebugUtil.i(TAG, "updateImmersiveNavigationHeight no change")
                return@post
            }
            //重新计算沉浸态播放控制按钮下移的距离（播放按钮父布局的高度 - 沉浸态时的底部间距 - 播报按钮bottom - 第一段动画已经下移的距离）
            val bottomMargin = immersiveBottomMenuBottomMargin + navigationHeight
            distancePlayActionY =
                abs(
                    binding.buttonPanel.root.height - bottomMargin -
                            binding.buttonPanel.middleControl.bottom - immersiveFirstMoveDownDistance
                )
            //底部面板在沉浸态时的高度
            bottomMenuHeightImmersive = immersiveBottomMenuHeight + navigationHeight
            //底部面板正常状态的高度
            bottomMenuHeightNormal = if (this.navigationHeight == 0 && navigationHeight > 0) {
                bottomMenuHeightNormal + navigationHeight
            } else if (this.navigationHeight > 0 && navigationHeight == 0) {
                bottomMenuHeightNormal - this.navigationHeight
            } else {
                bottomMenuHeightNormal
            }
            val bottomMenuHeight = if (isImmersiveState) {
                bottomMenuHeightImmersive
            } else {
                bottomMenuHeightNormal
            }
            //更新底部菜单栏高度
            val params = binding.buttonPanel.root.layoutParams
            params.height = bottomMenuHeight.toInt()
            binding.buttonPanel.root.layoutParams = params
            this.navigationHeight = navigationHeight
            startImmersiveAnimation(navigationHeight, windowType)
        }
    }

    /**
     * 播放设置和剪切按钮切换沉浸态动画
     * 仅做透明度变化
     */
    private fun initSpeedAndCutAnimation() {
        speedAnimation =
            COUISpringAnimation(binding.buttonPanel.imgMarkAdd, COUIDynamicAnimation.ALPHA)
        cutAnimation =
            COUISpringAnimation(binding.buttonPanel.imgMarkList, COUIDynamicAnimation.ALPHA)
        val spring = getDefaultSpringForce(0f, RESPONSE_SPEED_CUT_IN_ANIMATION)
        speedAnimation?.setSpring(spring)
        cutAnimation?.setSpring(spring)
    }

    private fun speedAndCutImmersiveAnimation() {
        if (speedAnimation == null) {
            initSpeedAndCutAnimation()
        } else {
            speedAnimation?.spring?.finalPosition = 0f
            speedAnimation?.spring?.setResponse(RESPONSE_SPEED_CUT_IN_ANIMATION)
            cutAnimation?.spring?.finalPosition = 0f
            cutAnimation?.spring?.setResponse(RESPONSE_SPEED_CUT_IN_ANIMATION)
        }
        speedAnimation?.start()
        cutAnimation?.start()
    }

    private fun speedAndCutRecoverAnimation() {
        speedAnimation?.animateToFinalPosition(1f)
        speedAnimation?.spring?.setResponse(RESPONSE_SPEED_CUT_OUT_ANIMATION)
        cutAnimation?.animateToFinalPosition(1f)
        cutAnimation?.spring?.setResponse(RESPONSE_SPEED_CUT_OUT_ANIMATION)
    }

    private fun initPlayActionAnimation(rootRight: Int, distancePlayActionY: Float) {
        if (BaseApplication.sIsRTLanguage) {
            initPlayActionAnimationRTL(rootRight, distancePlayActionY)
        } else {
            initPlayActionAnimationNormal(rootRight, distancePlayActionY)
        }
    }

    /**
     * 播放控制按钮沉浸态动画
     * 1、播放暂停按钮缩小
     * 2、快退、播放、快进右移到边缘16dp、下移到距底部10dp，按钮间距变为10dp
     */
    private fun initPlayActionAnimationNormal(rootRight: Int, distancePlayActionY: Float) {
        val playButton = binding.buttonPanel.middleControl
        val imgBackward = binding.buttonPanel.imgBackward
        val imgForward = binding.buttonPanel.imgForward
        playButton.bringToFront()
        imgBackward.bringToFront()
        imgForward.bringToFront()
        val playButtonWidth = context.resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_diam)
        //播放暂停按钮缩小
        scaleValuePlay = immersivePlayButtonSize / playButtonWidth
        playScaleX = COUISpringAnimation(playButton, COUIDynamicAnimation.SCALE_X)
        playScaleY = COUISpringAnimation(playButton, COUIDynamicAnimation.SCALE_Y)
        val springScale = getDefaultSpringForce(scaleValuePlay, RESPONSE_PLAY_ACTION_ANIMATION)
        playScaleX?.setSpring(springScale)
        playScaleY?.setSpring(springScale)
        val springTranslationY =
            getDefaultSpringForce(distancePlayActionY, RESPONSE_PLAY_ACTION_ANIMATION)
        //快进按钮右移的距离（根布局right - 快进right - 沉浸态右边距）
        distanceForwardX = abs(rootRight - imgForward.right - immersiveBottomMenuRightMargin)
        forwardTranslationX =
            COUISpringAnimation(imgForward, COUIDynamicAnimation.TRANSLATION_X)
        val springForwardTranslationX =
            getDefaultSpringForce(distanceForwardX, RESPONSE_PLAY_ACTION_ANIMATION)
        forwardTranslationX?.setSpring(springForwardTranslationX)
        forwardTranslationY =
            COUISpringAnimation(imgForward, COUIDynamicAnimation.TRANSLATION_Y)
        forwardTranslationY?.setSpring(springTranslationY)
        //按钮间距减小量(快进left - 播放按钮right + 播放按钮缩小尺寸的1/2 - 沉浸态按钮间距)
        val marginReduction =
            imgForward.left - playButton.right + (playButtonWidth - immersivePlayButtonSize) / 2 - immersiveActionMargin
        //播放按钮右移距离（按钮间距减小量 + 快进按钮右移的距离）
        distancePlayX = abs(marginReduction + distanceForwardX)
        playTranslationX =
            COUISpringAnimation(playButton, COUIDynamicAnimation.TRANSLATION_X)
        val springPlayTranslationX =
            getDefaultSpringForce(distancePlayX, RESPONSE_PLAY_ACTION_ANIMATION)
        playTranslationX?.setSpring(springPlayTranslationX)
        playTranslationY =
            COUISpringAnimation(playButton, COUIDynamicAnimation.TRANSLATION_Y)
        playTranslationY?.setSpring(springTranslationY)
        //回退按钮右移距离（按钮间距减小量 + 播放按钮右移距离）
        distanceBackX = abs(marginReduction + distancePlayX)
        backwardTranslationX =
            COUISpringAnimation(imgBackward, COUIDynamicAnimation.TRANSLATION_X)
        val springBackwardTranslationX =
            getDefaultSpringForce(distanceBackX, RESPONSE_PLAY_ACTION_ANIMATION)
        backwardTranslationX?.setSpring(springBackwardTranslationX)
        backwardTranslationY =
            COUISpringAnimation(imgBackward, COUIDynamicAnimation.TRANSLATION_Y)
        backwardTranslationY?.setSpring(springTranslationY)
    }

    /**
     * RTL播放控制按钮沉浸态动画,与普通语言下的区别是快进和快退按钮的位置互换
     * 1、播放暂停按钮缩小
     * 2、快退、播放、快进右移到边缘、下移到底部，缩小按钮间距
     * 3、播放设置和剪切按钮隐藏
     */
    private fun initPlayActionAnimationRTL(rootRight: Int, distancePlayActionY: Float) {
        val playButton = binding.buttonPanel.middleControl
        val imgBackward = binding.buttonPanel.imgBackward
        val imgForward = binding.buttonPanel.imgForward
        playButton.bringToFront()
        imgBackward.bringToFront()
        imgForward.bringToFront()
        val playButtonWidth = context.resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_diam)
        //播放暂停按钮缩小
        scaleValuePlay = immersivePlayButtonSize / playButtonWidth
        playScaleX = COUISpringAnimation(playButton, COUIDynamicAnimation.SCALE_X)
        playScaleY = COUISpringAnimation(playButton, COUIDynamicAnimation.SCALE_Y)
        val springScale = getDefaultSpringForce(scaleValuePlay, RESPONSE_PLAY_ACTION_ANIMATION)
        playScaleX?.setSpring(springScale)
        playScaleY?.setSpring(springScale)
        val springTranslationY =
            getDefaultSpringForce(distancePlayActionY, RESPONSE_PLAY_ACTION_ANIMATION)
        //快进按钮右移的距离（根布局right - 快进right - 沉浸态右边距）
        distanceForwardX = abs(rootRight - imgBackward.right - immersiveBottomMenuRightMargin)
        forwardTranslationX =
            COUISpringAnimation(imgBackward, COUIDynamicAnimation.TRANSLATION_X)
        val springForwardTranslationX =
            getDefaultSpringForce(distanceForwardX, RESPONSE_PLAY_ACTION_ANIMATION)
        forwardTranslationX?.setSpring(springForwardTranslationX)
        forwardTranslationY =
            COUISpringAnimation(imgBackward, COUIDynamicAnimation.TRANSLATION_Y)
        forwardTranslationY?.setSpring(springTranslationY)
        //按钮间距减小量(快进left - 播放按钮right + 播放按钮缩小的尺寸 - 沉浸态按钮间距)
        val marginReduction =
            imgBackward.left - playButton.right + (playButtonWidth - immersivePlayButtonSize) / 2 - immersiveActionMargin
        //播放按钮右移距离（按钮间距减小量 + 快进按钮右移的距离）
        distancePlayX = abs(marginReduction + distanceForwardX)
        playTranslationX =
            COUISpringAnimation(playButton, COUIDynamicAnimation.TRANSLATION_X)
        val springPlayTranslationX =
            getDefaultSpringForce(distancePlayX, RESPONSE_PLAY_ACTION_ANIMATION)
        playTranslationX?.setSpring(springPlayTranslationX)
        playTranslationY =
            COUISpringAnimation(playButton, COUIDynamicAnimation.TRANSLATION_Y)
        playTranslationY?.setSpring(springTranslationY)
        //回退按钮右移距离（按钮间距减小量 + 播放按钮右移距离）
        distanceBackX = abs(marginReduction + distancePlayX)
        backwardTranslationX =
            COUISpringAnimation(imgForward, COUIDynamicAnimation.TRANSLATION_X)
        val springBackwardTranslationX =
            getDefaultSpringForce(distanceBackX, RESPONSE_PLAY_ACTION_ANIMATION)
        backwardTranslationX?.setSpring(springBackwardTranslationX)
        backwardTranslationY =
            COUISpringAnimation(imgForward, COUIDynamicAnimation.TRANSLATION_Y)
        backwardTranslationY?.setSpring(springTranslationY)
    }

    private fun playActionAnimation(rootRight: Int, distancePlayActionY: Float) {
        if (playScaleX == null) {
            initPlayActionAnimation(rootRight, distancePlayActionY)
        } else {
            playScaleX?.spring?.finalPosition = scaleValuePlay
            playScaleY?.spring?.finalPosition = scaleValuePlay
            forwardTranslationX?.spring?.finalPosition = distanceForwardX
            forwardTranslationY?.spring?.finalPosition = distancePlayActionY
            playTranslationX?.spring?.finalPosition = distancePlayX
            playTranslationY?.spring?.finalPosition = distancePlayActionY
            backwardTranslationX?.spring?.finalPosition = distanceBackX
            backwardTranslationY?.spring?.finalPosition = distancePlayActionY
        }
        playScaleX?.start()
        playScaleY?.start()
        forwardTranslationX?.start()
        forwardTranslationY?.start()
        playTranslationX?.start()
        playTranslationY?.start()
        backwardTranslationX?.start()
        backwardTranslationY?.start()
    }

    private fun playActionRecoverAnimation() {
        playScaleX?.animateToFinalPosition(1f)
        playScaleY?.animateToFinalPosition(1f)
        forwardTranslationX?.animateToFinalPosition(1f)
        forwardTranslationY?.animateToFinalPosition(1f)
        playTranslationX?.animateToFinalPosition(1f)
        playTranslationY?.animateToFinalPosition(1f)
        backwardTranslationX?.animateToFinalPosition(1f)
        backwardTranslationY?.animateToFinalPosition(1f)
    }


    /**
     * 操作按钮动画
     * 1、播放控制按钮调整
     * 2、播放进度显示调整
     * 3、列表高度调整
     * 4、标题栏隐藏
     */
    private fun initToolbarImmersiveAnimation() {
        //底部操作栏高度变化动画
        bottomMenuAnimator =
            COUISpringAnimation(binding.buttonPanel.root, COUIDynamicAnimation.ALPHA)
        bottomMenuHeightNormal = binding.buttonPanel.root.height.toFloat()
        val spring = getDefaultSpringForce(bottomMenuHeightImmersive, RESPONSE_BOTTOM_MENU_ANIMATION)
        bottomMenuAnimator?.setStartValue(bottomMenuHeightNormal)
        bottomMenuAnimator?.setSpring(spring)
        bottomMenuAnimator?.addUpdateListener { animation, value, velocity ->
            val params = binding.buttonPanel.root.layoutParams
            params.height = value.toInt()
            binding.buttonPanel.root.layoutParams = params
        }

        //导航栏隐藏动画
        hideToolbarAnimator =
            COUISpringAnimation(binding.toolBarFrameLayout, COUIDynamicAnimation.ALPHA)
        toolbarHeight = binding.toolBarFrameLayout.height.toFloat()
        val spring2 = getDefaultSpringForce(0f, RESPONSE_TOOLBAR_ANIMATION)
        hideToolbarAnimator?.setStartValue(toolbarHeight)
        hideToolbarAnimator?.setSpring(spring2)
        hideToolbarAnimator?.addUpdateListener { animation, value, velocity ->
            val params = binding.toolBarFrameLayout.layoutParams
            params.height = value.toInt()
            binding.toolBarFrameLayout.layoutParams = params
        }
    }

    private fun toolbarAnimation() {
        if (bottomMenuAnimator == null) {
            initToolbarImmersiveAnimation()
        } else {
            bottomMenuAnimator?.setStartValue(bottomMenuHeightNormal)
            bottomMenuAnimator?.spring?.finalPosition = bottomMenuHeightImmersive
            hideToolbarAnimator?.spring?.finalPosition = 0f
        }
        bottomMenuAnimator?.removeEndListener(reverseImmersiveAnimationEndListener)
        bottomMenuAnimator?.removeEndListener(immersiveAnimationEndListener)
        bottomMenuAnimator?.addEndListener(immersiveAnimationEndListener)
        viewModel?.isImmersiveAnimationRunning = true
        bottomMenuAnimator?.start()
        hideToolbarAnimator?.start()
    }

    private fun toolbarRecoverAnimation() {
        if (bottomMenuAnimator == null) {
            DebugUtil.i(TAG, "toolbarRecoverAnimation bottomMenuAnimator is null")
            return
        }
        bottomMenuAnimator?.removeEndListener(immersiveAnimationEndListener)
        bottomMenuAnimator?.removeEndListener(reverseImmersiveAnimationEndListener)
        bottomMenuAnimator?.addEndListener(reverseImmersiveAnimationEndListener)
        viewModel?.isImmersiveAnimationRunning = true
        bottomMenuAnimator?.setStartValue(bottomMenuHeightImmersive)
        bottomMenuAnimator?.animateToFinalPosition(bottomMenuHeightNormal)
        hideToolbarAnimator?.animateToFinalPosition(toolbarHeight)
    }

    private fun operatorButtonsRecoverAnimation() {
        immersiveOperatorAlphaAnimation?.animateToFinalPosition(1F)
    }

    private fun initPlaybackProgressAnimator(rootRight: Int, distancePlayActionY: Float) {
        if (BaseApplication.sIsRTLanguage) {
            initPlaybackProgressAnimatorNormalRTL(rootRight, distancePlayActionY)
        } else {
            initPlaybackProgressAnimatorNormal(rootRight, distancePlayActionY)
        }
    }

    /**
     * 将做动效的view都恢复原来的样子
     */
    private fun resetViewPosition() {
        binding.buttonPanel.root.updateLayoutParams<MarginLayoutParams> {
            height = LayoutParams.WRAP_CONTENT
        }
        binding.buttonPanel.middleControl.scaleX = 1.0f
        binding.buttonPanel.middleControl.scaleY = 1.0f
        binding.buttonPanel.middleControl.translationX = 1.0f
        binding.buttonPanel.middleControl.translationY = 1.0f

        binding.buttonPanel.imgBackward.translationX = 1.0f
        binding.buttonPanel.imgBackward.translationY = 1.0f

        binding.buttonPanel.imgForward.translationX = 1.0f
        binding.buttonPanel.imgForward.translationY = 1.0f

        binding.buttonPanel.tvCurrent.translationX = 0F
        binding.buttonPanel.tvCurrent.translationY = 0F
        binding.buttonPanel.tvCurrent.alpha = 1F
        binding.buttonPanel.tvDuration.translationX = 0F
        binding.buttonPanel.tvDuration.translationY = 0F
        binding.buttonPanel.tvDuration.alpha = 1F

        binding.buttonPanel.llSeekbarContainer.translationY = 0F
        binding.buttonPanel.llSeekbarContainer.translationX = 0F
        binding.buttonPanel.llSeekbarContainer.alpha = 1F
        seekBar?.alpha = 1F
        binding.buttonPanel.llSeekbarContainer.updateLayoutParams<MarginLayoutParams> { width = LayoutParams.MATCH_PARENT }
        /*seekBar?.updateLayoutParams<MarginLayoutParams> {
            val margin = context.resources.getDimension(R.dimen.seekbar_margin).toInt()
            leftMargin = margin
            rightMargin = margin
        }*/
        enableSeekbarDeformation(true)
    }

    /**
     * 播放进度显示控件沉浸态动画
     * 1、进度条、当前播放进度与总时间下移
     * 2、当前播放进度左移
     * 3、总时间左移
     * 4、进度条缩短并水平移动
     */
    @Suppress("LongMethod")
    private fun initPlaybackProgressAnimatorNormal(rootRight: Int, distancePlayActionY: Float) {
        val tvCurrent = binding.buttonPanel.tvCurrent
        val tvDuration = binding.buttonPanel.tvDuration
        val playButtonWidth = context.resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_diam)
        //下移的目标线是播放按钮的水平中线处
        val targetY =
            binding.buttonPanel.middleControl.top + (playButtonWidth / 2) + distancePlayActionY
        //当前播放时长下移的距离（目标线 - 播放进度top - 1/2播放进度高度）
        distanceCurrentY = abs(targetY - tvCurrent.top - (tvCurrent.height / 2))
        currentTranslationY = COUISpringAnimation(tvCurrent, COUIDynamicAnimation.TRANSLATION_Y)
        val springCurrentTranslationY =
            getDefaultSpringForce(distanceCurrentY, RESPONSE_CURRENT_ANIMATION)
        currentTranslationY?.setSpring(springCurrentTranslationY)
        //总时间下移的距离（目标线 - 总时间top - 1/2总时间高度）
        distanceDurationY = abs(targetY - tvDuration.top - (tvDuration.height / 2))
        durationTranslationY = COUISpringAnimation(tvDuration, COUIDynamicAnimation.TRANSLATION_Y)
        val springDurationTranslationY =
            getDefaultSpringForce(distanceDurationY, RESPONSE_DURATION_ANIMATION)
        durationTranslationY?.setSpring(springDurationTranslationY)
        //当前播放时长左移
        distanceCurrentX = immersiveBottomMenuLeftMargin - tvCurrent.left
        currentTranslationX = COUISpringAnimation(tvCurrent, COUIDynamicAnimation.TRANSLATION_X)
        val springCurrentTranslationX =
            getDefaultSpringForce(distanceCurrentX, RESPONSE_CURRENT_ANIMATION)
        currentTranslationX?.setSpring(springCurrentTranslationX)
        //总时间左移的目标X（根布局right - 总时间右边距 - 快进与快退宽度 - 总的按钮间距 - 播放按钮宽度 - 尾部边距）
        val totalMargin =
            immersiveActionMargin * 2 + immersiveBottomMenuRightMargin + immersiveDurationRightMargin
        val durationTargetRight =
            rootRight - binding.buttonPanel.imgForward.width * 2 - immersivePlayButtonSize - totalMargin
        distanceDurationX = durationTargetRight - tvDuration.right
        durationTranslationX = COUISpringAnimation(tvDuration, COUIDynamicAnimation.TRANSLATION_X)
        val springDurationTranslationX =
            getDefaultSpringForce(distanceDurationX, RESPONSE_DURATION_ANIMATION)
        durationTranslationX?.setSpring(springDurationTranslationX)
        //总时间透明度变化
        durationAlphaFirst =
            COUISpringAnimation(tvDuration, COUIDynamicAnimation.ALPHA)
        durationAlphaSecond =
            COUISpringAnimation(tvDuration, COUIDynamicAnimation.ALPHA)
        val springDurationAlphaFirst = getDefaultSpringForce(0f, RESPONSE_DURATION_ALPHA_ANIMATION)
        val springDurationAlphaSecond = getDefaultSpringForce(1f, RESPONSE_DURATION_ALPHA_ANIMATION)
        durationAlphaFirst?.setSpring(springDurationAlphaFirst)
        durationAlphaFirst?.removeEndListener(durationAlphaFirstEndListener)
        durationAlphaFirst?.addEndListener(durationAlphaFirstEndListener)
        durationAlphaSecond?.setSpring(springDurationAlphaSecond)
        //进度条起点在移动后的当前播放时间右边
        val seekLeft = tvCurrent.right + distanceCurrentX.toInt()
        //进度条结束点在移动后的总时间左边
        val seekRight: Int = durationTargetRight.toInt() - tvDuration.width
        initSeekBarAnimation(targetY, seekRight, seekLeft)
    }

    /**
     * RTL播放进度显示控件沉浸态动画,与普通语言的区别是播放进度与总时间的位置互换
     * 1、当前播放进度与总时间下移
     * 2、当前播放进度左移
     * 3、总时间右移
     * 4、进度条缩短并移动到新的播放进度和总时间之间
     */
    @Suppress("LongMethod")
    private fun initPlaybackProgressAnimatorNormalRTL(rootRight: Int, distancePlayActionY: Float) {
        val tvCurrent = binding.buttonPanel.tvCurrent
        val tvDuration = binding.buttonPanel.tvDuration
        val playButtonWidth = context.resources.getDimension(com.soundrecorder.common.R.dimen.circle_record_button_diam)
        //下移的目标线是播放按钮的水平中线处
        val targetY =
            binding.buttonPanel.middleControl.top + (playButtonWidth / 2) + distancePlayActionY
        //播放进度下移的距离（目标线 - 播放进度top - 1/2播放进度高度）
        distanceCurrentY = abs(targetY - tvDuration.top - (tvDuration.height / 2))
        currentTranslationY = COUISpringAnimation(tvDuration, COUIDynamicAnimation.TRANSLATION_Y)
        val springCurrentTranslationY =
            getDefaultSpringForce(distanceCurrentY, RESPONSE_CURRENT_ANIMATION)
        currentTranslationY?.setSpring(springCurrentTranslationY)
        //总时间下移的距离（目标线 - 总时间top - 1/2总时间高度）
        distanceDurationY = abs(targetY - tvCurrent.top - (tvCurrent.height / 2))
        durationTranslationY = COUISpringAnimation(tvCurrent, COUIDynamicAnimation.TRANSLATION_Y)
        val springDurationTranslationY =
            getDefaultSpringForce(distanceDurationY, RESPONSE_DURATION_ANIMATION)
        durationTranslationY?.setSpring(springDurationTranslationY)
        //当前播放进度右移
        distanceCurrentX = immersiveBottomMenuLeftMargin - tvDuration.left
        currentTranslationX = COUISpringAnimation(tvDuration, COUIDynamicAnimation.TRANSLATION_X)
        val springCurrentTranslationX =
            getDefaultSpringForce(distanceCurrentX, RESPONSE_CURRENT_ANIMATION)
        currentTranslationX?.setSpring(springCurrentTranslationX)
        //总时间左移的目标X（根布局right - 总时间右边距 - 快进与快退宽度 - 按钮间距 - 播放按钮宽度 - 尾部边距）
        val totalMargin =
            immersiveActionMargin * 2 + immersiveBottomMenuRightMargin + immersiveDurationRightMargin
        val durationTargetRight =
            rootRight - binding.buttonPanel.imgForward.width * 2 - immersivePlayButtonSize - totalMargin
        distanceDurationX = durationTargetRight - tvCurrent.right
        durationTranslationX = COUISpringAnimation(tvCurrent, COUIDynamicAnimation.TRANSLATION_X)
        val springDurationTranslationX =
            getDefaultSpringForce(distanceDurationX, RESPONSE_DURATION_ANIMATION)
        durationTranslationX?.setSpring(springDurationTranslationX)
        //总时间透明度变化
        durationAlphaFirst =
            COUISpringAnimation(tvCurrent, COUIDynamicAnimation.ALPHA)
        durationAlphaSecond =
            COUISpringAnimation(tvCurrent, COUIDynamicAnimation.ALPHA)
        val springDurationAlphaFirst = getDefaultSpringForce(0f, RESPONSE_DURATION_ALPHA_ANIMATION)
        val springDurationAlphaSecond = getDefaultSpringForce(1f, RESPONSE_DURATION_ALPHA_ANIMATION)
        durationAlphaFirst?.setSpring(springDurationAlphaFirst)
        durationAlphaFirst?.removeEndListener(durationAlphaFirstEndListener)
        durationAlphaFirst?.addEndListener(durationAlphaFirstEndListener)
        durationAlphaSecond?.setSpring(springDurationAlphaSecond)
        //结束点在移动后的当前播放时间左边
        val seekRight: Int = durationTargetRight.toInt() - tvCurrent.width
        //起点在移动后的总时间右边
        val seekLeft = tvDuration.right + distanceCurrentX.toInt()
        initSeekBarAnimation(targetY, seekRight, seekLeft)
    }

    /**
     * 进度条缩短并移动到播放进度和总时间之间
     */
    private fun initSeekBarAnimation(targetY: Float, seekRight: Int, seekLeft: Int) {
        DebugUtil.i(TAG, "initSeekBarAnimation targetY:$targetY seekLeft:$seekLeft seekRight:$seekRight")
        if (seekBar == null) {
            seekBar = binding.buttonPanel.llSeekbarContainer.findViewById(R.id.seek_bar)
        }
        val llSeekbarContainer = binding.buttonPanel.llSeekbarContainer
        llSeekbarContainer.bringToFront()
        //进度条下移的距离（目标线 - 进度条top - 1/2进度条高度）
        distanceSeekBarY = abs(targetY - llSeekbarContainer.top - (llSeekbarContainer.height / 2))
        seekBarTranslationY =
            COUISpringAnimation(llSeekbarContainer, COUIDynamicAnimation.TRANSLATION_Y)
        val springSeekBarTranslationY =
            getDefaultSpringForce(distanceSeekBarY, RESPONSE_SEEKBAR_ANIMATION)
        seekBarTranslationY?.setSpring(springSeekBarTranslationY)
        //进度条普通状态的宽度
        seekBarNormalWidth = llSeekbarContainer.width.toFloat()
        //进度条沉浸态的宽度
        seekBarImmersiveWidth = seekRight - seekLeft.toFloat()
        //进度条宽度不能使用缩放动画，缩放动画会将进度圆点压扁，只能修改真实宽度
        translationSeekBarWidth =
            COUISpringAnimation(llSeekbarContainer, COUIDynamicAnimation.ALPHA)
        val springSeekBarWidth =
            getDefaultSpringForce(seekBarImmersiveWidth, RESPONSE_SEEKBAR_ANIMATION)
        translationSeekBarWidth?.setStartValue(seekBarNormalWidth)
        translationSeekBarWidth?.setSpring(springSeekBarWidth)
        translationSeekBarWidth?.addUpdateListener { animation, value, velocity ->
            val params = llSeekbarContainer.layoutParams
            params.width = value.toInt()
            llSeekbarContainer.setLayoutParams(params)
        }

        //seekbar缩短后在屏幕中间，需要移动到播放进度与总时长之间(目标right - 缩短后seekBar的right)
        distanceSeekBarX =
            seekRight - (llSeekbarContainer.right - (llSeekbarContainer.width - seekBarImmersiveWidth) / 2)
        seekBarTranslationX =
            COUISpringAnimation(llSeekbarContainer, COUIDynamicAnimation.TRANSLATION_X)
        val springSeekBarTranslationX =
            getDefaultSpringForce(distanceSeekBarX, RESPONSE_SEEKBAR_ANIMATION)
        seekBarTranslationX?.setSpring(springSeekBarTranslationX)

        //移除seekbar的水平margin
        seekBarMargin = context.resources.getDimension(R.dimen.seekbar_margin)
        translationSeekBarMargin = COUISpringAnimation(seekBar, COUIDynamicAnimation.ALPHA)
        val springSeekBarMargin = getDefaultSpringForce(1f, RESPONSE_SEEKBAR_ANIMATION)
        translationSeekBarMargin?.setStartValue(seekBarMargin)
        translationSeekBarMargin?.setSpring(springSeekBarMargin)
        /*translationSeekBarMargin?.addUpdateListener { animation, value, velocity ->
            seekBar?.apply {
                val params = layoutParams as MarginLayoutParams
                params.leftMargin = value.toInt()
                params.rightMargin = value.toInt()
                layoutParams = params
            }
        }*/
    }

    private fun playbackProgressAnimation(rootRight: Int, distancePlayActionY: Float) {
        if (seekBarTranslationY == null) {
            initPlaybackProgressAnimator(rootRight, distancePlayActionY)
        } else {
            seekBarTranslationY?.spring?.finalPosition = distanceSeekBarY
            seekBarTranslationX?.spring?.finalPosition = distanceSeekBarX
            currentTranslationY?.spring?.finalPosition = distanceCurrentY
            currentTranslationX?.spring?.finalPosition = distanceCurrentX
            durationTranslationY?.spring?.finalPosition = distanceDurationY
            durationTranslationX?.spring?.finalPosition = distanceDurationX
            translationSeekBarWidth?.spring?.finalPosition = seekBarImmersiveWidth
            translationSeekBarMargin?.spring?.finalPosition = 1f
        }
        seekBarTranslationY?.start()
        seekBarTranslationX?.start()
        currentTranslationY?.start()
        currentTranslationX?.start()
        durationTranslationY?.start()
        durationTranslationX?.start()
        durationAlphaFirst?.start()
        translationSeekBarWidth?.start()
        translationSeekBarMargin?.start()
    }

    private fun playbackProgressRecoverAnimation() {
        seekBarTranslationY?.animateToFinalPosition(1f)
        seekBarTranslationX?.animateToFinalPosition(1f)
        currentTranslationY?.animateToFinalPosition(1f)
        currentTranslationX?.animateToFinalPosition(1f)
        durationTranslationY?.animateToFinalPosition(1f)
        durationTranslationX?.animateToFinalPosition(1f)
        durationAlphaFirst?.start()
        translationSeekBarWidth?.animateToFinalPosition(seekBarNormalWidth)
        translationSeekBarMargin?.animateToFinalPosition(seekBarMargin)
    }

    private fun getDefaultSpringForce(finalPosition: Float, response: Float): COUISpringForce =
        COUISpringForce().setBounce(DEFAULT_SPRING_BOUNCE)
            .setFinalPosition(finalPosition)
            .setResponse(response)

    private fun resetAnimator() {
        cancelAnimation()

        playScaleX = null
        playScaleY = null
        forwardTranslationX = null
        forwardTranslationY = null
        playTranslationX = null
        playTranslationY = null
        backwardTranslationX = null
        backwardTranslationY = null

        seekBarTranslationY = null
        seekBarTranslationX = null
        currentTranslationY = null
        currentTranslationX = null
        durationTranslationY = null
        durationTranslationX = null
        durationAlphaFirst = null
        durationAlphaSecond = null
        translationSeekBarWidth = null
        translationSeekBarMargin = null

        speedAnimation = null
        cutAnimation = null

        hideToolbarAnimator = null
        bottomMenuAnimator = null
        navigationHeight = 0
        immersiveOperatorAlphaAnimation = null
    }

    private fun cancelAnimation() {
        bottomMenuAnimator?.removeEndListener(reverseImmersiveAnimationEndListener)
        bottomMenuAnimator?.removeEndListener(immersiveAnimationEndListener)
        durationAlphaFirst?.removeEndListener(durationAlphaFirstEndListener)
        cancelAnimation(playScaleX)
        cancelAnimation(playScaleY)
        cancelAnimation(forwardTranslationX)
        cancelAnimation(forwardTranslationY)
        cancelAnimation(playTranslationX)
        cancelAnimation(playTranslationY)
        cancelAnimation(backwardTranslationX)
        cancelAnimation(backwardTranslationY)
        cancelAnimation(seekBarTranslationY)
        cancelAnimation(seekBarTranslationX)
        cancelAnimation(currentTranslationY)
        cancelAnimation(currentTranslationX)
        cancelAnimation(durationTranslationY)
        cancelAnimation(durationTranslationX)
        cancelAnimation(durationAlphaFirst)
        cancelAnimation(durationAlphaSecond)
        cancelAnimation(translationSeekBarWidth)
        cancelAnimation(translationSeekBarMargin)
        cancelAnimation(speedAnimation)
        cancelAnimation(cutAnimation)
        cancelAnimation(hideToolbarAnimator)
        cancelAnimation(bottomMenuAnimator)
        cancelAnimation(immersiveOperatorAlphaAnimation)
    }

    private fun cancelAnimation(animation: COUISpringAnimation?) {
        animation?.cancel()
    }

    /**
     * 设置是否支持形变
     */
    private fun enableSeekbarDeformation(enable: Boolean) {
        seekBar?.setSupportDeformation(enable)
    }

    fun release() {
        resetAnimator()
        seekBar = null
        windowType = null
    }
}