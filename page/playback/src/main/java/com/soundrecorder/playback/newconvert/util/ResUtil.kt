/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.util

import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.playback.R

object ResUtil {

    @JvmStatic
    fun getColorArray(): IntArray {
        val context = BaseApplication.getAppContext()
        val roleColor1 = ContextCompat.getColor(context, R.color.role_color_1)
        val roleColor2 = ContextCompat.getColor(context, R.color.role_color_2)
        val roleColor3 = ContextCompat.getColor(context, R.color.role_color_3)
        val roleColor4 = ContextCompat.getColor(context, R.color.role_color_4)
        val roleColor5 = ContextCompat.getColor(context, R.color.role_color_5)
        val roleColor6 = ContextCompat.getColor(context, R.color.role_color_6)
        val roleColor7 = ContextCompat.getColor(context, R.color.role_color_7)
        val roleColor8 = ContextCompat.getColor(context, R.color.role_color_8)
        val roleColor9 = ContextCompat.getColor(context, R.color.role_color_9)
        val roleColor10 = ContextCompat.getColor(context, R.color.role_color_10)
        val roleColor11 = ContextCompat.getColor(context, R.color.role_color_11)
        return intArrayOf(
            roleColor1,
            roleColor2,
            roleColor3,
            roleColor4,
            roleColor5,
            roleColor6,
            roleColor7,
            roleColor8,
            roleColor9,
            roleColor10,
            roleColor11
        )
    }

    @JvmStatic
    fun getDrawableList(): ArrayList<Drawable> {
        val drawable1: Drawable? =
            BaseApplication.getAppContext().getDrawable(R.drawable.ic_circle1)
        val drawable2: Drawable? =
            BaseApplication.getAppContext().getDrawable(R.drawable.ic_circle2)
        val drawable3: Drawable? =
            BaseApplication.getAppContext().getDrawable(R.drawable.ic_circle3)
        val drawable4: Drawable? =
            BaseApplication.getAppContext().getDrawable(R.drawable.ic_circle4)
        val drawable5: Drawable? =
            BaseApplication.getAppContext().getDrawable(R.drawable.ic_circle5)
        val drawable6: Drawable? =
            BaseApplication.getAppContext().getDrawable(R.drawable.ic_circle6)
        val drawable7: Drawable? =
            BaseApplication.getAppContext().getDrawable(R.drawable.ic_circle7)
        val drawable8: Drawable? =
            BaseApplication.getAppContext().getDrawable(R.drawable.ic_circle8)
        val drawable9: Drawable? =
            BaseApplication.getAppContext().getDrawable(R.drawable.ic_circle9)
        val drawable10: Drawable? =
            BaseApplication.getAppContext().getDrawable(R.drawable.ic_circle10)
        val drawable11: Drawable? =
            BaseApplication.getAppContext().getDrawable(R.drawable.ic_circle11)
        val drawableList = ArrayList<Drawable>()
        drawable1?.let { drawableList.add(it) }
        drawable2?.let { drawableList.add(it) }
        drawable3?.let { drawableList.add(it) }
        drawable4?.let { drawableList.add(it) }
        drawable5?.let { drawableList.add(it) }
        drawable6?.let { drawableList.add(it) }
        drawable7?.let { drawableList.add(it) }
        drawable8?.let { drawableList.add(it) }
        drawable9?.let { drawableList.add(it) }
        drawable10?.let { drawableList.add(it) }
        drawable11?.let { drawableList.add(it) }
        return drawableList
    }
}