/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: PlayAmplitudeSeekbar.kt
 * Description: PlayAmplitudeSeekbar.kt
 * Version: 1.0
 * Date: 2025/7/11
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * v-leng<PERSON>@oppo.com            2025/7/11      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.playback.view

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Shader
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import com.coui.appcompat.animation.COUILinearInterpolator
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.amplitude.AmpSeekbarUtil
import com.soundrecorder.common.utils.amplitude.AmpSeekbarUtil.PERCENT_80
import com.soundrecorder.playback.R
import kotlin.math.abs

class PlayAmplitudeSeekbar @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {

    companion object {
        private const val TAG = "PlayAmplitudeSeekbar"
        private const val FLOAT_TWO = 2f
        private const val NATURE_COUNT = 90
        private const val DURATION_250 = 250L
        private const val DURATION_217 = 217L
        private const val ALPHA_255 = 255
        private val INTERPOLATOR = COUILinearInterpolator()
    }

    private var isRtl = BaseApplication.sIsRTLanguage

    /** 振幅数据*/
    var amplitudes: List<Int>? = null
    var floatPanelExpand = false
        set(value) {
            field = value
            horizontalPadding = if (value) {
                context.resources.getDimension(R.dimen.seekbar_horizontal_padding_expand)
            } else {
                context.resources.getDimension(R.dimen.seekbar_horizontal_padding)
            }
            invalidate()
        }
    private var horizontalPadding = context.resources.getDimension(R.dimen.seekbar_horizontal_padding)
    private var verticalPadding = 0f

    /** 总时长 ms*/
    var totalTime = 0L

    /** 当前播放时间 ms*/
    private var pointTime = 0L

    /** 当前播放时间对应的X坐标*/
    private var pointX = 0f

    /** 每毫秒多少像素*/
    private var pxPerMs = 0f

    /** 可绘制区域的高*/
    private var drawHeight = 0f

    /** 可绘制区域的宽*/
    private var drawWidth = 0f

    /** 波形宽度*/
    private var ampWidth = 0f

    /** 波形间隔*/
    private var ampGapWidth = 0f

    /** 手指按下时的坐标*/
    private var downX = 0f

    /** 是否正在拖动*/
    private var isDragging = false

    /** 判定是否是拖动的阈值*/
    private var touchSlop = ViewConfiguration.get(context).scaledTouchSlop

    /** 判定是否选中pointX的padding值*/
    private val selectPadding = context.resources.getDimension(R.dimen.seekbar_select_padding)

    /** pointX是否在选中区域内*/
    private var isInSelectArea = false

    /** 按下时的播放位置*/
    private var downPointX = 0f

    /** 认为是拖动的上一次X坐标*/
    private var lastDragX = -1f

    /** 记录上一次的方向 -1 未开始拖动 0 向左拖动 1 向右拖动*/
    private var preDirection = -1

    /** 记录当前的方向  -1 未开始拖动 0 向左拖动 1 向右拖动*/
    private var direction = 0

    private val dragStartColor = COUIContextUtil.getColor(context, com.soundrecorder.common.R.color.amp_drag_start_color_half)
    private val dragEndColor = COUIContextUtil.getColor(context, com.soundrecorder.common.R.color.amp_drag_end_color)
    private val dragColorIntArray = intArrayOf(dragStartColor, dragEndColor)

    /** 拖动区域动画的宽度*/
    private val dragRectWidth = context.resources.getDimension(R.dimen.seekbar_drag_width)

    private var gradientRight = LinearGradient(0f, 0f, dragRectWidth, 0f, dragColorIntArray, null, Shader.TileMode.CLAMP)
    private var gradientLeft = LinearGradient(dragRectWidth, 0f, 0f, 0f, dragColorIntArray, null, Shader.TileMode.CLAMP)

    private var leftBitmap: Bitmap? = null
    private var rightBitmap: Bitmap? = null
    private var leftCanvas: Canvas? = null
    private var rightCanvas: Canvas? = null
    private var bitmapRect = Rect()
    private var drawBitmapRectF = RectF()

    /** 拖动动画的起始位置*/
    private var dragAnimationStartX = 0f

    /** 面板展开时，第一次拖动的标志，用于初始化 dragAnimationStartX 的值*/
    private var isExpandAndFirstTime = false
    private var screenSizeChange = false

    // touch监听事件
    var playPointMoveListener: PlayPointMoveListener? = null

    private val wavePlayPaint = Paint().apply {
        color = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelPrimary)
        isAntiAlias = true
        isDither = true
    }

    private val waveUnPlayPaint = Paint().apply {
        color = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTertiary)
        isAntiAlias = true
        isDither = true
    }

    private val pointPaint = Paint().apply {
        color = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
        isAntiAlias = true
        isDither = true
    }

    private val dragBitmapPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    private val dragAnimationPaint = Paint()

    // 淡入动画
    private var fadeInAnimator = ValueAnimator.ofInt(0, ALPHA_255).apply {
        duration = DURATION_250
        interpolator = INTERPOLATOR
        addUpdateListener {
            val alpha = it.animatedValue as Int
            dragAnimationPaint.alpha = alpha
            postInvalidateOnAnimation()
        }
        doOnEnd {
            DebugUtil.d(TAG, "fadeInAnimator donEnd")
            dragAnimationPaint.alpha = ALPHA_255
            postInvalidateOnAnimation()
        }
        doOnCancel {
            DebugUtil.d(TAG, "fadeInAnimator donCancel")
            dragAnimationPaint.alpha = ALPHA_255
            postInvalidateOnAnimation()
        }
    }

    // 淡出动画
    private var fadeOutAnimator = ValueAnimator.ofInt(ALPHA_255, 0).apply {
        duration = DURATION_217
        interpolator = INTERPOLATOR
        addUpdateListener {
            val alpha = it.animatedValue as Int
            dragAnimationPaint.alpha = alpha
            postInvalidateOnAnimation()
        }
        doOnEnd {
            DebugUtil.i(TAG, "fadeOutAnimator donEnd")
            dragAnimationPaint.alpha = 0
            isInSelectArea = false
            isDragging = false
            postInvalidateOnAnimation()
        }
        doOnCancel {
            DebugUtil.i(TAG, "fadeOutAnimator donCancel")
            dragAnimationPaint.alpha = 0
            isInSelectArea = false
            isDragging = false
            postInvalidateOnAnimation()
        }
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (w > 0 && h > 0) {
            drawWidth = w - FLOAT_TWO * horizontalPadding
            val mTotalTime = totalTime.toFloat()
            pxPerMs = if (mTotalTime > 0) {
                drawWidth / mTotalTime
            } else {
                0f
            }

            drawHeight = h * PERCENT_80
            verticalPadding = (h - drawHeight) / FLOAT_TWO
            val calcResult = if (floatPanelExpand) {
                AmpSeekbarUtil.calculateAmpWidthAndGapWidthEqualProportion(drawWidth, NATURE_COUNT)
            } else {
                AmpSeekbarUtil.calculateAmpWidthAndGapWidth(drawWidth, NATURE_COUNT)
            }
            ampWidth = calcResult.first
            ampGapWidth = calcResult.second
            // 播放红线的宽度和波形宽度保持一致
            pointPaint.strokeWidth = ampWidth
            screenSizeChange = true
            DebugUtil.d(TAG, "onSizeChanged, verticalPadding=$verticalPadding drawWidth=$drawWidth ampWidth=$ampWidth ampGapWidth=$ampGapWidth")
        }
    }

    override fun onDraw(canvas: Canvas) {
        calculateBeforeDraw()
        // 绘制波形
        drawAmplitude(canvas)
        // 绘制播放进度
        drawPoint(canvas)
        // 绘制拖拽动画
        drawDragAnimation(canvas)
        super.onDraw(canvas)
    }

    private fun drawDragAnimation(canvas: Canvas) {
        if (isDragging && isInSelectArea) {
            checkIfNeedCreateDragBitmap()
            if (direction > 0) {
                leftBitmap?.let {
                    // 向右拖动
                    if (pointX == dragAnimationStartX) {
                        return
                    }
                    if (pointX - dragAnimationStartX < dragRectWidth) {
                        val rectW = pointX - dragAnimationStartX
                        if (rectW < 0) {
                            DebugUtil.w(TAG, "drawDragAnimation: leftBitmap rectW < 0, not crop bitmap")
                            canvas.drawBitmap(it, pointX - dragRectWidth, 0f, dragAnimationPaint)
                            return
                        }
                        bitmapRect.set((dragRectWidth - rectW).toInt(), 0, dragRectWidth.toInt(), height)
                        drawBitmapRectF.set(pointX - rectW, 0f, pointX, height.toFloat())
                        canvas.drawBitmap(it, bitmapRect, drawBitmapRectF, dragAnimationPaint)
                    } else {
                        canvas.drawBitmap(it, pointX - dragRectWidth, 0f, dragAnimationPaint)
                    }
                }
            } else {
                rightBitmap?.let {
                    // 向左拖动
                    if (pointX == dragAnimationStartX) {
                        return
                    }
                    if (pointX + dragRectWidth > dragAnimationStartX) {
                        val rectW = (dragAnimationStartX - pointX).toInt()
                        if (rectW < 0) {
                            DebugUtil.w(TAG, "drawDragAnimation: rightBitmap rectW < 0, not crop bitmap")
                            canvas.drawBitmap(it, pointX, 0f, dragAnimationPaint)
                            return
                        }
                        bitmapRect.set(0, 0, rectW, height)
                        drawBitmapRectF.set(pointX, 0f, pointX + rectW, height.toFloat())
                        canvas.drawBitmap(it, bitmapRect, drawBitmapRectF, dragAnimationPaint)
                    } else {
                        canvas.drawBitmap(it, pointX, 0f, dragAnimationPaint)
                    }
                }
            }
        }
    }

    /**
     * 计算绘制前的参数
     */
    private fun calculateBeforeDraw() {
        pointX = horizontalPadding + pointTime * pxPerMs
    }

    /**
     * 绘制波形
     */
    private fun drawAmplitude(canvas: Canvas) {
        val mAmplitudes = amplitudes
        if (mAmplitudes.isNullOrEmpty() || mAmplitudes.size != NATURE_COUNT) {
            DebugUtil.d(TAG, "drawAmplitude amplitudes size is not correct, size = ${mAmplitudes?.size}")
            return
        }
        val minAmplitudeHeight = ampWidth.toInt()
        var maxAmp = mAmplitudes.max()
        maxAmp = if (maxAmp == 0) minAmplitudeHeight else maxAmp
        val oneLineHeightScale = drawHeight / maxAmp.toFloat()
        val lenAmplitudes = mAmplitudes.size

        var index = 0
        while (index < lenAmplitudes) {
            val preAmplitude = if (index - 1 >= 0) mAmplitudes[index - 1] else 0
            val curAmplitude = mAmplitudes[index]
            val amplitude = (curAmplitude + preAmplitude) / FLOAT_TWO
            var startY = getStartYByHeight(amplitude * oneLineHeightScale)
            var endY = getEndYByHeight(amplitude * oneLineHeightScale)
            var startX = horizontalPadding + index * (ampWidth + ampGapWidth)
            var endX = startX + ampWidth
            if (endY - startY < minAmplitudeHeight) {
                startY -= minAmplitudeHeight / FLOAT_TWO
                endY += minAmplitudeHeight / FLOAT_TWO
            }
            val isPlayed = startX < pointX - ampWidth / FLOAT_TWO
            if (isRtl) {
                startX = width - startX
                endX = width - endX
            }
            if (isPlayed) {
                canvas.drawRoundRect(startX, startY, endX, endY, ampWidth, ampWidth, wavePlayPaint)
            } else {
                canvas.drawRoundRect(startX, startY, endX, endY, ampWidth, ampWidth, waveUnPlayPaint)
            }
            index++
        }
    }

    /**
     * 绘制播放进度
     */
    private fun drawPoint(canvas: Canvas) {
        if (isRtl) {
            pointX = width - pointX
        }
        canvas.drawLine(pointX, 0f, pointX, height.toFloat(), pointPaint)
    }

    private fun getStartYByHeight(amplitude: Float): Float {
        val drawHeight = drawHeight
        return if (amplitude > drawHeight) {
            verticalPadding
        } else {
            val halfAmplitude: Float = amplitude / 2
            verticalPadding + if (halfAmplitude == 0f) {
                drawHeight / FLOAT_TWO - ampWidth / FLOAT_TWO
            } else {
                drawHeight / FLOAT_TWO - halfAmplitude
            }
        }
    }

    private fun getEndYByHeight(amplitude: Float): Float {
        val drawHeight = drawHeight
        return if (amplitude > drawHeight) {
            drawHeight - verticalPadding
        } else {
            val halfAmplitude: Float = amplitude / 2
            verticalPadding + if (halfAmplitude == 0f) {
                drawHeight / FLOAT_TWO + ampWidth / FLOAT_TWO
            } else {
                drawHeight / FLOAT_TWO + halfAmplitude
            }
        }
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                doActionDown(event)
            }

            MotionEvent.ACTION_MOVE -> {
                doActionMove(event)
            }

            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                doActionUpOrCancel(event)
            }
        }
        return true
    }

    private fun doActionDown(event: MotionEvent) {
        fadeInAnimator?.cancel()
        fadeOutAnimator?.cancel()
        downX = event.x
        downPointX = pointX
        // 判断pointX是否在这个区域内
        isInSelectArea = downX in downPointX - selectPadding..downPointX + selectPadding
        isExpandAndFirstTime = false
        isDragging = false
        if (isInSelectArea || floatPanelExpand) {
            // 如果面板是展开状态，播放红线会跳到按下位置，则isInSelectArea也要置为true
            isInSelectArea = true
            // 初始化lastX, 用于计算拖拽动画
            lastDragX = downX
            // 开始拖拽前，初始化动画上一次方向为-1，表示未开始拖动
            preDirection = -1

            val newPointX = downX.coerceIn(horizontalPadding, width - horizontalPadding)
            downPointX = newPointX
            // 根据新的pointX计算新的pointTime
            pointTime = if (isRtl) {
                ((width - newPointX - horizontalPadding) / pxPerMs).toLong()
            } else {
                ((newPointX - horizontalPadding) / pxPerMs).toLong()
            }.coerceIn(0, totalTime)
            playPointMoveListener?.onTouchDownMiddleBar(pointTime)
        }
        // 面板展开时，允许直接拖动
        if (floatPanelExpand) {
            isDragging = true
            dragAnimationStartX = downPointX
            isExpandAndFirstTime = true
        }
        DebugUtil.d(TAG, "doActionDown isInSelectArea=$isInSelectArea floatPanelExpand=$floatPanelExpand pointX=$pointX downPointX=$downPointX")
    }

    private fun doActionMove(event: MotionEvent) {
        val curX = event.x
        val dx = curX - downX
        if (!isDragging && abs(dx) > touchSlop) {
            isDragging = true
        }
        if (isDragging && isInSelectArea) {
            // 计算新的pointX
            val newPointX = (downPointX + dx).coerceIn(horizontalPadding, width - horizontalPadding)
            // 根据新的pointX计算新的pointTime
            pointTime = if (isRtl) {
                ((width - newPointX - horizontalPadding) / pxPerMs).toLong()
            } else {
                ((newPointX - horizontalPadding) / pxPerMs).toLong()
            }.coerceIn(0, totalTime)
            // 判定是否需要更新拖动x坐标
            val dragDx = curX - lastDragX
            if (abs(dragDx) > touchSlop) {
                lastDragX = curX
            }
            // 判定拖拽方向
            direction = if (dragDx > 0) 1 else 0
            if (direction != preDirection) {
                preDirection = direction
                dragAnimationStartX = if (isExpandAndFirstTime) {
                    isExpandAndFirstTime = false
                    downPointX
                } else {
                    pointX
                }
                DebugUtil.d(TAG, "doActionMove direction change, pointX=$pointX, downPointX=$downPointX, dragAnimationStartX=$dragAnimationStartX")
                startFadeInAnimate()
            }
            playPointMoveListener?.onMoveOnMiddleBar(pointTime)
        }
    }

    private fun doActionUpOrCancel(event: MotionEvent) {
        DebugUtil.d(TAG, "onTouchEvent (${event.action})ACTION_UP/ACTION_CANCEL isDragging = $isDragging isInSelectArea = $isInSelectArea")
        if (!isDragging) {
            // 若不是拖动，则触发父布局的点击执行展开收起动画
            (parent as ViewGroup).performClick()
        }
        if (isInSelectArea || floatPanelExpand) {
            startFadeOutAnimate()
            playPointMoveListener?.onTouchUpMiddleBar(pointTime)
        }
    }

    private fun startFadeInAnimate() {
        fadeOutAnimator.cancel()
        fadeInAnimator.cancel()
        fadeInAnimator.start()
    }

    private fun startFadeOutAnimate() {
        fadeInAnimator.cancel()
        fadeOutAnimator.cancel()
        fadeOutAnimator.start()
    }

    /**
     * 更新播放进度
     */
    fun updatePlayProgress(time: Long) {
        pointTime = time
        invalidate()
    }

    /**
     * 检查是否需要创建拖拽动画的bitmap
     */
    private fun checkIfNeedCreateDragBitmap() {
        if (screenSizeChange || leftBitmap == null || rightBitmap == null) {
            DebugUtil.d(TAG, "checkIfNeedCreateDragBitmap, screenSize change($screenSizeChange) or bitmap is null, " +
                    "need to create new bitmap dragRectWidth = $dragRectWidth height = $height")
            screenSizeChange = false
            recycleBitmap()
            leftBitmap = Bitmap.createBitmap(dragRectWidth.toInt(), height, Bitmap.Config.ARGB_8888)
            rightBitmap = Bitmap.createBitmap(dragRectWidth.toInt(), height, Bitmap.Config.ARGB_8888)
            leftBitmap?.let {
                dragBitmapPaint.shader = gradientLeft
                if (leftCanvas == null) {
                    leftCanvas = Canvas(it)
                }
                leftCanvas?.drawRect(0f, 0f, dragRectWidth, height.toFloat(), dragBitmapPaint)
            }
            rightBitmap?.let {
                dragBitmapPaint.shader = gradientRight
                if (rightCanvas == null) {
                    rightCanvas = Canvas(it)
                }
                rightCanvas?.drawRect(0f, 0f, dragRectWidth, height.toFloat(), dragBitmapPaint)
            }
        }
    }

    private fun recycleBitmap() {
        leftCanvas = null
        rightCanvas = null
        runCatching {
            leftBitmap?.let {
                if (it.isRecycled.not()) {
                    it.recycle()
                }
            }
            leftBitmap = null
            DebugUtil.d(TAG, "recycleBitmap leftBitmap success")
        }.onFailure {
            leftBitmap = null
            DebugUtil.e(TAG, "recycleBitmap leftBitmap error", it)
        }

        runCatching {
            rightBitmap?.let {
                if (it.isRecycled.not()) {
                    it.recycle()
                }
            }
            rightBitmap = null
            DebugUtil.d(TAG, "recycleBitmap rightBitmap success")
        }.onFailure {
            rightBitmap = null
            DebugUtil.e(TAG, "recycleBitmap rightBitmap error", it)
        }
    }

    override fun onDetachedFromWindow() {
        DebugUtil.d(TAG, "onDetachedFromWindow")
        fadeInAnimator.cancel()
        fadeOutAnimator.cancel()
        playPointMoveListener = null
        recycleBitmap()
        super.onDetachedFromWindow()
    }
}