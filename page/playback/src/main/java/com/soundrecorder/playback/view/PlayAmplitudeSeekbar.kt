/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: PlayAmplitudeSeekbar.kt
 * Description: PlayAmplitudeSeekbar.kt
 * Version: 1.0
 * Date: 2025/7/11
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * v-leng<PERSON>@oppo.com            2025/7/11      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.playback.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.R

class PlayAmplitudeSeekbar @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {

    companion object {
        private const val TAG = "PlayAmplitudeSeekbar"
        private const val FLOAT_TWO = 2f
        private const val NATURE_COUNT = 90
        private const val NATURE_SPACE_COUNT = NATURE_COUNT - 1
    }

    private var isRtl = BaseApplication.sIsRTLanguage
    // 振幅数据
    var amplitudes: List<Int>? = null
    private val horizontalPadding = context.resources.getDimension(R.dimen.seekbar_horizontal_padding)
    // 总时长 ms
    var totalTime = 0L
    // 当前播放时间 ms
    private var pointTime = 0L
    // 当前播放时间对应的X坐标
    private var pointX = 0f
    // 可绘制区域的宽高
    private var drawWidth = 0f
    private var drawHeight = 0f
    // 波形宽度 波形间隔
    private val ampWidth = context.resources.getDimension(R.dimen.seekbar_amplitude_width)
    private var ampGapWidth = 0f

    private val wavePlayPaint = Paint().apply {
        color = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelPrimary)
        isAntiAlias = true
        isDither = true
    }

    private val waveUnPlayPaint = Paint().apply {
        color = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTertiary)
        isAntiAlias = true
        isDither = true
    }

    private val pointPaint = Paint().apply {
        color = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
        strokeWidth = context.resources.getDimension(com.soundrecorder.common.R.dimen.dp1)
        isAntiAlias = true
        isDither = true
    }

    override fun onDraw(canvas: Canvas) {
        calculateBeforeDraw()
        // 绘制波形
        drawAmplitude(canvas)
        // 绘制播放进度
        drawPoint(canvas)
        super.onDraw(canvas)
    }

    /**
     * 计算绘制前的参数
     */
    private fun calculateBeforeDraw() {
        val width = width.toFloat()
        val height = height.toFloat()
        drawWidth = width - FLOAT_TWO * horizontalPadding
        drawHeight = height
        ampGapWidth = (drawWidth - ampWidth * NATURE_COUNT) / NATURE_SPACE_COUNT

        val mTotalTime = totalTime.toFloat()
        val pxPerMs = if (mTotalTime > 0) {
            drawWidth / mTotalTime
        } else {
            0f
        }
        pointX = horizontalPadding + pointTime * pxPerMs
    }

    /**
     * 绘制波形
     */
    private fun drawAmplitude(canvas: Canvas) {
        val mAmplitudes = amplitudes
        if (mAmplitudes.isNullOrEmpty() || mAmplitudes.size != NATURE_COUNT) {
            DebugUtil.d(TAG, "drawAmplitude amplitudes size is not correct, size = ${mAmplitudes?.size}")
            return
        }
        val minAmplitudeHeight = 1
        var maxAmp = mAmplitudes.max()
        maxAmp = if (maxAmp == 0) minAmplitudeHeight else maxAmp
        val oneLineHeightScale = drawHeight / maxAmp.toFloat()
        val lenAmplitudes = mAmplitudes.size

        var index = 0
        while (index < lenAmplitudes) {
            val preAmplitude = if (index - 1 >= 0) mAmplitudes[index - 1] else 0
            val curAmplitude = mAmplitudes[index]
            val amplitude = (curAmplitude + preAmplitude) / FLOAT_TWO
            var startY = getStartYByHeight(amplitude * oneLineHeightScale)
            var endY = getEndYByHeight(amplitude * oneLineHeightScale)
            var startX = horizontalPadding + index * (ampWidth + ampGapWidth)
            var endX = startX + ampWidth
            if (endY - startY < minAmplitudeHeight) {
                startY -= minAmplitudeHeight / FLOAT_TWO
                endY += minAmplitudeHeight / FLOAT_TWO
            }
            val isPlayed = startX < pointX - ampWidth / FLOAT_TWO
            if (isRtl) {
                startX = width - startX
                endX = width - endX
            }
            if (isPlayed) {
                canvas.drawRoundRect(startX, startY, endX, endY, ampWidth, ampWidth, wavePlayPaint)
            } else {
                canvas.drawRoundRect(startX, startY, endX, endY, ampWidth, ampWidth, waveUnPlayPaint)
            }
            index++
        }
    }

    /**
     * 绘制播放进度
     */
    private fun drawPoint(canvas: Canvas) {
        if (isRtl) {
            pointX = width - pointX
        }
        canvas.drawLine(pointX, 0f, pointX, height.toFloat(), pointPaint)
    }

    private fun getStartYByHeight(amplitude: Float): Float {
        val drawHeight = height.toFloat()
        return if (amplitude > drawHeight) {
            0f
        } else {
            val halfAmplitude: Float = amplitude / 2
            if (halfAmplitude == 0f) {
                drawHeight / FLOAT_TWO - FLOAT_TWO
            } else {
                drawHeight / FLOAT_TWO - halfAmplitude
            }
        }
    }

    private fun getEndYByHeight(amplitude: Float): Float {
        val drawHeight = height.toFloat()
        return if (amplitude > drawHeight) {
            drawHeight
        } else {
            val halfAmplitude: Float = amplitude / 2
            if (halfAmplitude == 0f) {
                drawHeight / FLOAT_TWO + FLOAT_TWO
            } else {
                drawHeight / FLOAT_TWO + halfAmplitude
            }
        }
    }

    /**
     * 更新播放进度
     */
    fun updatePlayProgress(time: Long) {
        pointTime = time
        invalidate()
    }
}