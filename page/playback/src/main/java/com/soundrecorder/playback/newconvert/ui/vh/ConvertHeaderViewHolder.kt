/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ConvertHeaderViewHolder.kt
 ** Description : ConvertHeaderViewHolder.kt
 ** Version     : 1.0
 ** Date        : 2025/07/04
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/07/04      1.0      create
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui.vh

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isGone
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.playback.R
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipGroup
import com.soundrecorder.playback.newconvert.search.anim.ConvertSearchAnim
import com.soundrecorder.playback.newconvert.ui.OnDismissListener
import com.soundrecorder.playback.newconvert.ui.SelectSpeakerHelper
import com.soundrecorder.playback.newconvert.ui.TextImageItemAdapter
import java.lang.ref.WeakReference

class ConvertHeaderViewHolder(parent: ViewGroup, adapter: TextImageItemAdapter) : ViewHolder(
    LayoutInflater.from(parent.context).inflate(R.layout.header_convert_content, parent, false)
) {
    private val keyWordGroup: KeyWordChipGroup = itemView.findViewById(R.id.key_word_group)
    private val selectSpeaker: TextView = itemView.findViewById(R.id.tv_speakers)

    private val mWeakAdapter = WeakReference(adapter)

    companion object {
        private const val TAG = "ConvertHeaderViewHolder"
    }

    init {
        selectSpeaker.setOnClickListener {
            val context = itemView.context ?: return@setOnClickListener
            val adapter = mWeakAdapter.get()
            adapter ?: return@setOnClickListener
            if (adapter.selectSpeakersHelper == null) {
                adapter.selectSpeakersHelper = SelectSpeakerHelper(adapter.speakerSelectListener)
            }
            adapter.selectSpeakersHelper?.showChooseSpeakerDialog(
                context,
                selectSpeaker,
                adapter.selectSpeakersData,
                adapter.distinctList,
                object : OnDismissListener {
                    override fun onDismiss() {
                        refreshExpandIcon(false, context)
                    }
                })
            refreshExpandIcon(true, context)
        }
    }

    private fun refreshExpandIcon(isIconUp: Boolean, context: Context) {
        if (isIconUp) {
            context.let {
                selectSpeaker.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    null,
                    null,
                    ContextCompat.getDrawable(it, R.drawable.ic_speaker_select_collapsed),
                    null
                )
            }
        } else {
            context.let {
                selectSpeaker.setCompoundDrawablesRelativeWithIntrinsicBounds(
                    null,
                    null,
                    ContextCompat.getDrawable(it, R.drawable.ic_speaker_select_expand),
                    null
                )
            }
        }
    }

    private val anim: ConvertSearchAnim by lazy {
        ConvertSearchAnim(keyWordGroup, mWeakAdapter.get()?.searchAnimListener)
    }

    /**
     * 绑定数据
     * @param name 播放的录音名称
     * @param time 录音文件的创建时间
     * @param keyWords 转文本提取的关键词
     */
    fun bindData(name: String?, time: CharSequence, keyWords: List<String>) {
        val adapter = mWeakAdapter.get()
        if (adapter == null) {
            DebugUtil.d(TAG, "bindData adapter is null")
            return
        }
        DebugUtil.i(
            TAG,
            "bindData $name  time: $time keyWords:${keyWords.size} loadingState:${adapter.loadingState}" +
                    ", supportExactKeyWords: ${FunctionOption.IS_SUPPORT_EXTRACT_KEY_WORDS}, searchMode ${adapter.searchMode}"
        )
        refreshSelectSpeakerState()
        if (!FunctionOption.IS_SUPPORT_EXTRACT_KEY_WORDS) { // 不支持提取关键词
            keyWordGroup.visibility = View.GONE
            return
        }
        keyWordGroup.setKeyWords(keyWords, adapter.loadingState)
        if (adapter.searchMode) { //搜索模式，则不显示显示关键词
            keyWordGroup.visibility = View.GONE
        } else { //非搜索模式，则显示关键词
            keyWordGroup.visibility = View.VISIBLE
            updateKeyWordHeight(keyWords.size)
            keyWordGroup.setKeyWordChipClickListener(adapter.keyWordClickListener)
        }
    }

    fun refreshSelectSpeakerState() {
        val adapter = mWeakAdapter.get()
        if (adapter == null) {
            DebugUtil.d(TAG, "refreshSelectSpeakerState adapter is null")
            return
        }
        selectSpeaker.isGone = !adapter.mNeedShowRole
        if (adapter.mNeedShowRole) {
            selectSpeaker.text = SelectSpeakerHelper.getSelectSpeakerName(adapter.selectSpeakersData)
        } else {
            selectSpeaker.text = ""
        }
    }


    /**
     * 显示搜索的动效
     */
    fun showSearchAnim() {
        if (!FunctionOption.IS_SUPPORT_EXTRACT_KEY_WORDS) { // 不支持提取关键词
            return
        }
        val adapter = mWeakAdapter.get()
        if (adapter == null) {
            DebugUtil.d(TAG, "showSearchAnim adapter is null")
            return
        }
        DebugUtil.d(TAG, "showSearchAnim show:${adapter.showSearchAnim}")
        if (adapter.showSearchAnim) {
            adapter.showSearchAnim = false
            if (adapter.searchMode) {
                anim.animateSearchIn()
            } else {
                anim.animateSearchOut()
            }
        }
    }

    /**
     * 更新关键词列表的高度
     * 通过设置height = wrap_content，来更新列表的高度
     * @param size 关键词的个数
     */
    private fun updateKeyWordHeight(size: Int) {
        val adapter = mWeakAdapter.get()
        if (adapter == null) {
            DebugUtil.d(TAG, "updateKeyWordHeight adapter is null")
            return
        }
        DebugUtil.d(TAG, "updateKeyWordHeight size:$size")
        if (size == 0) { //没有关键词，就不更新列表高度
            return
        }
        if (!adapter.showSearchAnim) { //在动效执行期间，关键词数据请求下来了，需要取消动效
            anim.release()
        }
        keyWordGroup.updateLayoutParams { //设置wrap_content 更新高度
            height = ViewGroup.LayoutParams.WRAP_CONTENT
        }
        saveKeyWordViewHeight() // 更新完成后，更新动效执行的高度
    }

    /**
     * 释放动效
     */
    fun releaseAnim() {
        anim.release()
    }

    /**
     * 通过设置height=0,来隐藏关键词view
     */
    fun hideKeyWordView() {
        val viewHeight = keyWordGroup.height
        DebugUtil.d(TAG, "hideKeyWordView height:$viewHeight")
        if (viewHeight == 0) {
            return
        }
        keyWordGroup.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            height = 0
            topMargin = 0
        }
    }

    /**
     * 重新设置height来显示关键词view
     */
    fun showKeyWordView() {
        val animHeight = ConvertSearchAnim.animHeight
        DebugUtil.d(TAG, "showKeyWordView height:$animHeight")
        if (animHeight <= 0) {
            return
        }
        keyWordGroup.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            height = animHeight
            topMargin = ViewUtils.dp2px(ConvertSearchAnim.MARGIN_TOP).toInt()
        }
    }

    /**
     * 保存关键词View的高度
     */
    fun saveKeyWordViewHeight() {
        keyWordGroup.post {
            ConvertSearchAnim.animHeight = keyWordGroup.height
            DebugUtil.d(TAG, "saveKeyWordViewHeight height:${ConvertSearchAnim.animHeight}")
        }
    }

    /**
     * 进入。退出动画是否正在执行
     */
    fun isSearchAnimRunning(): Boolean {
        return anim.isRunning()
    }
}