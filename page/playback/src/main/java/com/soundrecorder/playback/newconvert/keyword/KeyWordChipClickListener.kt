/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.keyword

import com.coui.appcompat.chip.COUIChip

/**
 * 关键词的点击事件
 */
interface KeyWordChipClickListener {

    /**
     * 提取关键词
     */
    fun extractKeyWord(): Boolean

    /**
     * 当点击关键词时
     * @param chip 点击的关键词的View
     * @param keyWord 关键词
     */
    fun onClickKeyWord(chip: COUIChip, keyWord: String)
}