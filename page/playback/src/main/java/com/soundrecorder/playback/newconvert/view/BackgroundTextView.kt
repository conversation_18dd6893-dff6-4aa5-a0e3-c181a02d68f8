/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.graphics.Path
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.Typeface
import android.os.Build
import android.text.Spannable
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.text.style.TypefaceSpan
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.annotation.IntRange
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.graphics.withClip
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.base.utils.BaseUtil.isAndroidQOrLater
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.dp2px
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.R
import com.soundrecorder.playback.convert.search.ConvertSearchBean

class BackgroundTextView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) :
    AppCompatTextView(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "BackgroundTextView"
        private const val OFFSET_Y = 2
        const val HIGHLIGHT_TYPE_BACKGROUND = 1
        const val HIGHLIGHT_TYPE_FOREGROUND = 2
        // 高亮背景的圆角
        private const val HIGHLIGHT_RADIUS_FILLET = 6
        // 高亮背景的top和bottom的偏移量
        private const val HIGHLIGHT_TOP_BOTTOM_OFFSET = 3
        // 高亮背景在行收和行尾的偏移量
        private const val HIGHLIGHT_START_END_OFFSET = 5
    }
    var offset = -1
    private val mRect: Rect by lazy { Rect() }
    private val mRectF: RectF by lazy { RectF() }
    private var mPaint: Paint? = null
    private val mPath: Path by lazy { Path() }
    private var mColor: Int
    private var isHighLight = false
    private var mStartHighLightSeq = 0
    private var mEndHighLightSeq = 0
    private var isUnderLine = false
    private var mUnderlines = mutableListOf<Pair<Int, Int>>()
    private val span = TypefaceSpan(Typeface.create("sans-serif-medium", Typeface.NORMAL))
    private val foregroundSpan by lazy {
        ForegroundColorSpan(COUIContextUtil.getAttrColor(context,
                com.support.appcompat.R.attr.couiColorPrimary, 0))
    }
    private var highLightType = -1

    private val colorAlpha: Int
        get() {
            val nightMode = NightModeUtil.isNightMode(context)
            return if (nightMode) (255 * 0.25).toInt() else (255 * 0.10).toInt()
        }
    private val mCornerRadius = context.dp2px(HIGHLIGHT_RADIUS_FILLET)

    init {
        val array = context.obtainStyledAttributes(attrs, R.styleable.BackgroundTextView, defStyleAttr, 0)
        mColor = array.getColor(R.styleable.BackgroundTextView_background_color, com.support.appcompat.R.attr.couiColorPrimary)
        array.recycle()
        initPaintWithColor(mColor)
        if (isAndroidQOrLater) {
            @RequiresApi(Build.VERSION_CODES.Q)
            isForceDarkAllowed = false
        }
    }

    private fun initPaintWithColor(inputColor: Int) {
        if (mPaint == null) {
            mPaint = Paint()
        }
        mPaint?.apply {
            style = Paint.Style.FILL
            isAntiAlias = true
            color = inputColor
            alpha = colorAlpha
        }
    }

    fun setBackGroundPaintColor(inputColor: Int) {
        mColor = inputColor
        initPaintWithColor(mColor)
    }

    fun setBackgroundAlpha(@IntRange(from = 0, to = 255) alpha: Int) {
        mPaint?.alpha = alpha
    }


    override fun onDraw(canvas: Canvas) {
        if (isHighLight) {
            when (highLightType) {
                HIGHLIGHT_TYPE_BACKGROUND -> drawBackgroundBySentence(canvas, true)
                HIGHLIGHT_TYPE_FOREGROUND -> drawBackgroundBySentence(canvas, false)
            }
        }
        if (isUnderLine) {
            drawUnderLine(canvas)
        }
        super.onDraw(canvas)
    }

    private fun drawUnderLine(canvas: Canvas) {
        try {
            val paint = Paint()
            paint.style = Paint.Style.STROKE
            paint.color = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
            paint.strokeWidth = 3f
            val array = floatArrayOf(6f, 6f)
            paint.pathEffect = DashPathEffect(array, 0f)
            val tempText = text.toString()
            if (paint == null || mRect == null) {
                DebugUtil.i(TAG, "drawUnderLine paint is null or mRect is null return")
                return
            }
            for (index in mUnderlines.indices) {
                val textLayout = layout
                var xStart: Float
                var xEnd: Float
                var xDiff: Float
                var firstCharInLine: Int
                var lastCharInLine: Int
                val pair = mUnderlines[index]
                val startLine = textLayout.getLineForOffset(pair.first) //获取字符在第几行
                val endLine = textLayout.getLineForOffset(pair.second) //获取字符在第几行

                for (i in startLine..endLine) {
                    val baseLineAtIndex = getLineBounds(i, mRect)
                    xStart = if (i == startLine) {
                        textLayout.getPrimaryHorizontal(pair.first)
                    } else {
                        firstCharInLine = textLayout.getLineStart(i)
                        textLayout.getPrimaryHorizontal(firstCharInLine)
                    }
                    xEnd = if (i == endLine && pair.second < textLayout.getLineEnd(i) - 1) {
                        //DebugUtil.e(TAG, "====>textLayout.getLineEnd(i) = ${textLayout.getLineEnd(i)}")
                        //mEndHighLightSeq 如果是最后一个字符，getPrimaryHorizontal（mEndHighLightSeq + 1） 会返回0
                        textLayout.getPrimaryHorizontal(pair.second + 1)
                    } else {
                        lastCharInLine = textLayout.getLineEnd(i)
                        val text = tempText[lastCharInLine - 1].toString()
                        xDiff = textLayout.paint.measureText(text)
                        val xEndtemp = textLayout.getSecondaryHorizontal(lastCharInLine - 1)
                        //DebugUtil.i(TAG, "lastCharInLine = $lastCharInLine, xEndtemp = $xEndtemp, text = $text, xDiff = $xDiff")
                        xEndtemp + xDiff
                    }
                    //DebugUtil.i(TAG, "====>drawUnderLine, i = $i,  xStart = $xStart, xEnd= $xEnd")
                    val fontBottom = paint?.fontMetrics?.bottom ?: 0f
                    val bottom = baseLineAtIndex + context.dp2px(fontBottom.toInt() + OFFSET_Y)
                    canvas.drawLine(xStart, bottom, xEnd, bottom, paint)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun drawBackgroundBySentence(canvas: Canvas, isSearchModel: Boolean) {
        if (mPaint == null) {
            return
        }
        try {
            val textLayout = layout
            var xStart: Float
            var xEnd: Float
            var xDiff: Float
            var firstCharInLine: Int
            var lastCharInLine: Int
            val startLine = textLayout.getLineForOffset(mStartHighLightSeq) //获取字符在第几行
            val endLine = textLayout.getLineForOffset(mEndHighLightSeq) //获取字符在第几行
            val tempText = text.toString()
            for (i in startLine..endLine) {
                val baseLineAtIndex = getLineBounds(i, mRect)
                firstCharInLine = textLayout.getLineStart(i)
                lastCharInLine = textLayout.getLineEnd(i)
                xStart = if (i == startLine) {
                    textLayout.getPrimaryHorizontal(mStartHighLightSeq)
                } else {
                    textLayout.getPrimaryHorizontal(firstCharInLine)
                }
                xEnd = if (i == endLine && mEndHighLightSeq < textLayout.getLineEnd(i) - 1) {
                    // DebugUtil.e(TAG, "====>textLayout.getLineEnd(i) = ${textLayout.getLineEnd(i)}")
                    //mEndHighLightSeq 如果是最后一个字符，getPrimaryHorizontal（mEndHighLightSeq + 1） 会返回0
                    textLayout.getPrimaryHorizontal(mEndHighLightSeq + 1)
                } else {
                    val text = tempText[lastCharInLine - 1].toString()
                    xDiff = textLayout.paint.measureText(text)
                    val xEndtemp = textLayout.getSecondaryHorizontal(lastCharInLine - 1)
                    xEndtemp + xDiff
                }
                //DebugUtil.i(TAG, "====>drawBackground2, i = $i,  xStart = $xStart, xEnd= $xEnd")
                val fontBottom = mPaint?.fontMetrics?.bottom ?: 0f
                val bottom = baseLineAtIndex + context.dp2px(fontBottom.toInt() + OFFSET_Y)
                if (isSearchModel) {
                    mPaint?.let { canvas.drawRect(xStart, mRect.top.toFloat(), xEnd, bottom, it) }
                } else {
                    val offset = calculateOffset(firstCharInLine, lastCharInLine, startLine, endLine, i)
                    mRectF.apply {
                        left = xStart - offset.first
                        top = mRect.top.toFloat() - context.dp2px(HIGHLIGHT_TOP_BOTTOM_OFFSET)
                        right = xEnd + offset.second
                        this.bottom = bottom + context.dp2px(HIGHLIGHT_TOP_BOTTOM_OFFSET)
                    }
                    mPaint?.let {
                        mPath.addRoundRect(mRectF, mCornerRadius, mCornerRadius, Path.Direction.CW)
                        canvas.withClip(mPath) { drawRect(mRectF, it) }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun calculateOffset(firstCharInLine: Int, lastCharInLine: Int, startLine: Int, endLine: Int, currentLine: Int): Pair<Float, Float> {
        val isThanOneLine = endLine - startLine >= 1
        val isFirstRow = currentLine == startLine
        val startOffset = when {
            // 场景1：高亮从行首开始
            mStartHighLightSeq == firstCharInLine -> context.dp2px(HIGHLIGHT_START_END_OFFSET)
            // 场景2：高亮从非行首开始. 不跨行
            isFirstRow && isThanOneLine.not() -> 0F
            // 场景3：高亮从非行首开始，跨行
            isThanOneLine -> {
                if (isFirstRow) {
                    0F
                } else {
                    context.dp2px(HIGHLIGHT_START_END_OFFSET)
                }
            }
            // 其它未知场景
            else -> 0F
        }
        val endOffset = when {
            // 场景1：高亮结束位置在行尾
            mEndHighLightSeq == lastCharInLine - 1 -> context.dp2px(HIGHLIGHT_START_END_OFFSET)
            // 场景2：高亮结束位置不在行尾，不跨行
            isFirstRow && isThanOneLine.not() -> 0F
            // 场景3：高亮结束位置不在行尾，跨行
            isThanOneLine -> {
                if (isFirstRow) {
                    context.dp2px(HIGHLIGHT_START_END_OFFSET)
                } else {
                    0F
                }
            }
            // 其它未知场景
            else -> 0F
        }
        return Pair(startOffset, endOffset)
    }

    private fun drawForegroundBySentence() {
        removeTextForegroundSpan()
        if (text is SpannableString) {
            val spanString = (text as SpannableString)
            //end + 1是因为加粗需要覆盖标点符号
            spanString?.setSpan(foregroundSpan, mStartHighLightSeq, mEndHighLightSeq + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    }

    fun getHighLight() = isHighLight

    fun getStartHighLightSeq() = mStartHighLightSeq

    fun updateHighLight(start: Int, end: Int, highLight: Boolean, invalidate: Boolean) {
        mStartHighLightSeq = start
        mEndHighLightSeq = end
        isHighLight = highLight
        if (invalidate) {
            invalidate()
        }
    }

    fun setHighlightType(type: Int) {
        this.highLightType = type
    }

    fun getEndHighLightSeq() = mEndHighLightSeq

    fun setIsUnderLine(underlines: MutableList<Pair<Int, Int>>?, underline: Boolean) {
        if (underline != this.isUnderLine) {
            // 状态发生变化
            mUnderlines = underlines ?: mutableListOf()
            isUnderLine = underline
            invalidate()
        } else if (underline) {
            // 标记为true，value发生变化
            isUnderLine = underline
            mUnderlines = underlines ?: mutableListOf()
            invalidate()
        }
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event?.actionMasked == MotionEvent.ACTION_DOWN) {
            offset = getOffsetForPosition(event.x, event.y)
            DebugUtil.i(TAG, "->off = $offset ,textLength = ${text.length}}")
        }
        //requestFocus 防止第二次点击才出发onclick事件
        requestFocus()
        return super.onTouchEvent(event)
    }

    private fun addTextBoldSpan(start: Int, end: Int) {
        //数组越界
        try {
            if (text is SpannableString) {
                val spanString = (text as SpannableString)
                //end + 1是因为加粗需要覆盖标点符号
                spanString?.setSpan(span, start, end + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun removeTextBoldSpan() {
        (text as? SpannableString)?.removeSpan(span)
    }

    private fun removeTextForegroundSpan() {
        (text as? SpannableString)?.removeSpan(foregroundSpan)
    }

    fun setItemContentSpannableForTextItemMetaData(
        builder: SpannableStringBuilder,
        currentItem: ConvertContentItem.TextItemMetaData?,
        searchMode: Boolean = false
    ) {
        if (currentItem == null) {
            DebugUtil.i(TAG, "setItemContentSpannable item == null, return")
            return
        }
        if (currentItem.textParagraph != null && currentItem.textParagraph!!.isNotEmpty()) {
            var start = 0
            val spaceLength = TextImageMixLayout.SPACE.length
            for (sentencesIndex in currentItem.textParagraph!!.indices) {
                val sentencesItem = currentItem.textParagraph!![sentencesIndex]
                if (sentencesItem.onlyHasSimpleMark) {
                    val imageSpan = CenterImageSpan(this.context, R.drawable.ic_red_flag)
                    builder.insert(start, TextImageMixLayout.SPACE)
                    builder.setSpan(imageSpan, start, start + TextImageMixLayout.IMAGE_SPAN_SPACE, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
                    //todo
                    if (searchMode && getHighLight()) { // 本TextView中有关键词被选中，存在标题ICON的字符长度，需要矫正index
                        val originStart = getStartHighLightSeq()
                        val originEnd = getEndHighLightSeq()

                        if (originStart >= start) {
                            //在高亮词前插入ImageSpan，需要矫正高亮词Index
                            val adjustStart = originStart + spaceLength
                            val end = originEnd + spaceLength
                            updateHighLight(adjustStart, end, true, false)
                            //DebugUtil.i(TAG, "setItemContentSpannable originStart ${originStart}, originEnd $originEnd ")
                        }
                    }
                    start += spaceLength
                }
                start += sentencesItem.text.length
            }
        }
        DebugUtil.i(TAG, "setItemContentSpannable  text.length = ${builder.length}")
        text = builder
    }

    /**
     * 高亮搜索选中的
     */
    fun highlightSearchFocus(element: ConvertSearchBean) {
        val keyWordIndex = element.keyWordIndex
        val keyWord = element.keyWord
        updateHighLight(keyWordIndex, keyWord.length + keyWordIndex - 1, true, false)
    }

    /**
     * 判断是否有小旗子的span
     * @param start 开始位置
     * @param end 结束位置
     */
    fun hasFlagSpan(start: Int, end: Int): Boolean {
        val text = text
        if (TextUtils.isEmpty(text)) {
            return false
        }
        if (text is SpannableString) {
            val spans = text.getSpans(start, end, CenterImageSpan::class.java)
            return spans.size > 0
        }
        return false
    }
}