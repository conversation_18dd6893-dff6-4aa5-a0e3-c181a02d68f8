/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PanelOverlay
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.View
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.playback.PlaybackContainerFragment

@SuppressLint("ClickableViewAccessibility")
class PanelOverlay @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "PanelOverlay"
    }

    private var containerFragment: PlaybackContainerFragment? = null

    fun setContainerFragment(fragment: PlaybackContainerFragment?) {
        this.containerFragment = fragment
    }

    init {
        setOnClickListener {
            DebugUtil.d(TAG, "click")
            containerFragment?.expandCollapseAnimationHelper?.startCollapseAnimation()
        }
        setOnLongClickListener {
            DebugUtil.d(TAG, "longClick")
            true
        }
    }
}