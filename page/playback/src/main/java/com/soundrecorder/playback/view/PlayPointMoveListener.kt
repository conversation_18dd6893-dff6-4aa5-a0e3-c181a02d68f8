/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: PlayPointMoveListener.kt
 * Description: PlayPointMoveListener.kt
 * Version: 1.0
 * Date: 2025/7/15
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * v-le<PERSON><PERSON>@oppo.com            2025/7/15      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.playback.view

interface PlayPointMoveListener {
    /**
     * ACTION_DOWN 按下红线
     */
    fun onTouchDownMiddleBar()

    /**
     * ACTION_MOVE 正在拖动中间红线
     * @param time
     */
    fun onMoveOnMiddleBar(time: Long)

    /**
     * ACTION_UP/ACTION_CANCEL
     * @param time
     */
    fun onTouchUpMiddleBar(time: Long)
}
