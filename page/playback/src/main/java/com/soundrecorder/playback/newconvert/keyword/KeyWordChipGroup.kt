/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.keyword

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewStub
import android.widget.FrameLayout
import com.soundrecorder.playback.R
import com.coui.appcompat.button.COUILoadingButton
import com.coui.appcompat.chip.COUIChip
import com.google.android.material.chip.ChipGroup
import com.soundrecorder.base.utils.DebugUtil

/**
 * 关键词的Group
 */
open class KeyWordChipGroup @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "KeyWordChipGroup"

        /**
         * 关键字最大个数是8
         */
        private const val MAX_KEY_WORD_LEN = 8

        /**
         * 加载按钮默认状态：显示“提取关键词”
         */
        const val DEFAULT_STATE = COUILoadingButton.DEFAULT_STATE

        /**
         * 加载按钮加载中的状态：显示“...”，并且有动效
         */
        const val LOADING_STATE = COUILoadingButton.LOADING_STATE
    }

    private var chipGroup: ChipGroup? = null

    // 加载中
    private var loadingLayout: ViewStub? = null
    private var loadingBtn: COUILoadingButton? = null
    private var loadingState: Int = COUILoadingButton.DEFAULT_STATE


    private var chipClickListener: KeyWordChipClickListener? = null

    init {
        inflate(context, R.layout.view_key_word_chip_group, this)

        initView()
        showExtractButton(DEFAULT_STATE)
    }

    private fun initView() {
        chipGroup = findViewById(R.id.chip_group)
        loadingLayout = findViewById(R.id.chip_loading_layout)
    }


    /**
     * 设置关键词
     * @param keyWords 关键词列表
     * @param state 加载中按钮的状态，值只能为LOADING_STATE或者DEFAULT_STATE
     */
    fun setKeyWords(keyWords: List<String>, state: Int) {
        DebugUtil.d(TAG, "setKeyWords size:${keyWords.size} state:$state")

        if (keyWords.isEmpty()) { //没有内容，则显示提取按钮
            showExtractButton(state)
        } else { //最多显示8个数据
            val size = keyWords.size
            var subList = keyWords
            if (size > MAX_KEY_WORD_LEN) {
                subList = keyWords.subList(0, MAX_KEY_WORD_LEN)
            }

            showKeyWords(subList)
        }
    }

    /**
     * 设置点击事件
     */
    fun setKeyWordChipClickListener(listener: KeyWordChipClickListener?) {
        this.chipClickListener = listener
    }

    /**
     * 显示提取的关键词列表
     */
    private fun showKeyWords(keyWords: List<String>) {
        // 隐藏加载中的布局，显示关键词列表
        showLoadingView(false)
        //删除所有的view，重新创建
        chipGroup?.removeAllViews()

        keyWords.forEach { keyWord ->
            val chip = createKeyWordChip()
            chip.text = keyWord

            chip.setOnClickListener {
                // 点击事件，跳转到转文本搜索界面
                chipClickListener?.onClickKeyWord(chip, keyWord)
            }
        }
        requestLayout()
    }

    /**
     * 显示提取关键词的按钮
     * @param state 加载按钮的状态,值只能为LOADING_STATE或者DEFAULT_STATE
     */
    private fun showExtractButton(state: Int) {
        this.loadingState = state
        // 显示加载中的布局
        showLoadingView(true)

        loadingBtn?.let { button ->
            // 根据state切换为加载按钮状态
            showLoadingState(state)

            // 设置点击事件
            button.setOnClickListener {
                val status = button.buttonState
                DebugUtil.d(TAG, "loadingStateChange loading: ${status == COUILoadingButton.LOADING_STATE}")
                if (COUILoadingButton.DEFAULT_STATE == status) { //默认状态，即显示“提取关键词”按钮，点击后切换到加载中
                    if (this.chipClickListener?.extractKeyWord() == true) {
                        // 切换为加载中动效
                        button.switchToLoadingState()
                    }
                }
            }
        }
    }


    /**
     * 创建关键词的chip
     */
    private fun createKeyWordChip(): COUIChip {
        val chip = LayoutInflater.from(context).inflate(R.layout.view_key_word_chip, chipGroup, false) as COUIChip
        chipGroup?.addView(chip)
        return chip
    }

    /**
     * 显示加载布局
     * @param show 是否显示加载中的布局
     */
    private fun showLoadingView(show: Boolean) {
        if (show) {
            ensureLoadingButton()
            chipGroup?.visibility = View.GONE
            loadingBtn?.visibility = View.VISIBLE
        } else {
            chipGroup?.visibility = View.VISIBLE
            loadingBtn?.visibility = View.GONE
        }
    }

    /**
     * 确认存在加载中的按钮
     */
    private fun ensureLoadingButton() {
        loadingLayout?.let {
            if (loadingBtn == null) { // 说明ViewStub还没有inflate
                val layout = it.inflate()
                loadingBtn = layout.findViewById(R.id.key_word_loading)
            }
        }
    }

    /**
     * 根据state切换为加载按钮状态
     * @param state
     */
    private fun showLoadingState(state: Int) {
        loadingBtn?.let { button ->
            when (state) {
                DEFAULT_STATE -> button.resetButtonState()
                LOADING_STATE -> button.switchToLoadingState()
            }
        }
    }

    /**
     * 获取真实的高度
     */
    @SuppressLint("RestrictedApi")
    fun getRealHeight(): Int {
        if (chipGroup?.visibility == View.VISIBLE) { // 显示chipGroup
            chipGroup?.let {
                try {
                    val count = it.childCount
                    if (count <= 0) {
                        DebugUtil.e(TAG, "getRealHeight chipGroup count <= 0")
                        return 0
                    }
                    val chip = it.getChildAt(count - 1)
                    val row = it.getRowIndex(chip) + 1
                    if (row <= 0) {
                        DebugUtil.e(TAG, "getRealHeight chipGroup row <= 0")
                        return 0
                    }
                    val chipHeight = chip.height
                    val space = it.chipSpacingVertical
                    DebugUtil.d(TAG, "getRealHeight chipGroup count:$count row:$row chipHeight:$chipHeight space:$space")
                    return row * chipHeight + (row - 1) * it.chipSpacingVertical
                } catch (e: Exception) {
                    DebugUtil.e(TAG, "getRealHeight chipGroup error", e)
                    return 0
                }
            }
            return 0
        } else { //显示提取按钮
            DebugUtil.d(TAG, "getRealHeight loadingButton 28dp")
            return context.resources.getDimensionPixelOffset(R.dimen.dp28)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        DebugUtil.e(TAG, "onAttachedToWindow $loadingState")
        // 根据state切换为加载按钮状态
        showLoadingState(loadingState)
    }


    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        DebugUtil.e(TAG, "onDetachedFromWindow $loadingState")
        // 防止内存泄露
        loadingBtn?.resetButtonState()
    }
}