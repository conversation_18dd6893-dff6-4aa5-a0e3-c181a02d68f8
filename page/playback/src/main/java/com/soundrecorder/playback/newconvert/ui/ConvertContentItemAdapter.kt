/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui

import android.animation.ValueAnimator
import android.graphics.drawable.Drawable
import android.text.StaticLayout
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AlphaAnimation
import android.view.animation.PathInterpolator
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.playback.R
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.playback.newconvert.util.ResUtil
import com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout
import com.soundrecorder.common.databean.ConvertContentItem

import java.util.*


open class ConvertContentItemAdapter() :
    RecyclerView.Adapter<ViewHolder>() {
    private var mNeedShowRole: Boolean = false
    private var mNeedShowTime: Boolean = false

    private var mPlayName: String = ""
    private var mCreateTime: CharSequence = ""
    private var mSubject: String = ""
    private var mRoles: String = ""
    private var drawableList = ArrayList<Drawable>()
    private var mShareTxtRolesView: TextView? = null
    private var mShareTxtRolesViewHeight = 0f

    private val shareViewHolders = mutableListOf<ShareConvertContentTextViewHolder>()


    var data: List<ConvertContentItem>? = null
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    init {
        drawableList = ResUtil.getDrawableList()
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        //recyclerView.setItemViewCacheSize(2)
        //recyclerView.recycledViewPool.setMaxRecycledViews(TYPE_LIST, 20)
        recyclerView.recycledViewPool.setMaxRecycledViews(TYPE_HEADER, 1)
        recyclerView.recycledViewPool.setMaxRecycledViews(TYPE_FOOTER, 1)
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        shareViewHolders.forEach { it.release() }
        shareViewHolders.clear()
    }

    override fun onViewAttachedToWindow(holder: ViewHolder) {
        super.onViewAttachedToWindow(holder)

        //DebugUtil.e(TAG, "onViewAttachToWindow holder ${holder} holder.absoluteAdapterPosition ${holder.absoluteAdapterPosition}, holder.layoutPosition ${holder.layoutPosition}")
        /**
         * Android Bug , RecycleView lead to lose textView selection ability
         */
        when (holder) {
            is ShareConvertContentTextViewHolder -> {
                holder.itemTextContent.isEnabled = false
                holder.itemTextContent.isEnabled = true
            }
        }
    }

    override fun onViewDetachedFromWindow(holder: ViewHolder) {
        super.onViewDetachedFromWindow(holder)
        //DebugUtil.e(TAG, "onViewDetachedFromWindow holder ${holder} holder.absoluteAdapterPosition ${holder.absoluteAdapterPosition}, holder.layoutPosition ${holder.layoutPosition}")
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val result = when (viewType) {
            TYPE_LIST -> {
                val vh = ShareConvertContentTextViewHolder(parent)
                shareViewHolders.add(vh as ShareConvertContentTextViewHolder)
                vh
            }
            TYPE_FOOTER -> {
                val itemView = View(parent.context).apply {
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        BaseApplication.getAppContext().resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp40)
                    )
                }
                val vh = object : ViewHolder(itemView) {}
                vh
            }
            else -> {
                val vh = ConvertShareTxtHeaderViewHolder(parent)
                vh
            }
        }
        //DebugUtil.e(TAG, "onCreateViewHolder viewType ${viewType} result ${result}")
        return result
    }

    override fun onViewRecycled(holder: ViewHolder) {
        DebugUtil.e(TAG, "onViewRecycled holder $holder holder.absoluteAdapterPosition ${holder.absoluteAdapterPosition}" +
                ", holder.layoutPosition ${holder.layoutPosition}")
        super.onViewRecycled(holder)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        if (data == null) return
        when (holder) {
            is ConvertShareTxtHeaderViewHolder -> {
                holder.playName.text = mPlayName
                holder.createTime.text = mCreateTime
                holder.subject.text = mSubject
            }
            is ShareConvertContentTextViewHolder -> {
                val dataPosition = position - 1
                if ((dataPosition < 0) || dataPosition > data!!.size - 1) {
                    DebugUtil.e(TAG, "dataPosition is wrong!")
                    return
                }
                val item = data!![dataPosition]
                holder.setData(item)
                holder.setSpeakerRoles(mRoles)
                holder.startTime.contentDescription = TimeUtils.getContentDescriptionForTimeDuration(item.startTime)
                holder.startTime.text = item.startTime.durationInMsFormatTimeExclusive()
                holder.itemTextContent.text = item.textContent
                mShareTxtRolesView = holder.topRoles
                holder.refreshSpeaker()
            }
        }
    }

    /*
     * @param refresh true -> notifyDataChange()  false -> speaker animate
     */
    fun roleControl(needShowRole: Boolean, refresh: Boolean) {
        mNeedShowRole = needShowRole
        if (refresh) {
            notifyDataSetChanged()
        } else {
            if (needShowRole) {
                shareViewHolders.forEach { it.showAnimate() }
            } else {
                shareViewHolders.forEach { it.dismissAnimate() }
            }
        }
    }

    /**
     * 分享txt预览界面，控制列表item时间的显示和关闭
     */
    fun timeControl(needShowTime: Boolean, refresh: Boolean) {
        mNeedShowTime = needShowTime
        if (refresh) {
            notifyDataSetChanged()
        } else {
            if (needShowTime) {
                shareViewHolders.forEach {
                    it.showTimeAnimate()
                }
            } else {
                shareViewHolders.forEach {
                    it.dismissTimeAnimate()
                }
            }
        }
    }

    fun setHeaderData(name: String, time: CharSequence, subject: String = "", roles: String = "") {
        mPlayName = name
        mCreateTime = time
        mSubject = subject
        mRoles = roles
    }


    override fun getItemCount(): Int = (data?.size ?: 0) + 1 + 1

    fun getHeaderSize(): Int = 1

    override fun getItemViewType(position: Int): Int {
        return when {
            position < 1 -> {
                TYPE_HEADER
            }
            position + 1 == itemCount -> {
                TYPE_FOOTER
            }
            else -> {
                TYPE_LIST
            }
        }
    }


    interface OnSpeakerNameClick {
        fun onClick(pos: Int)
    }

    /**
     * 分享txt预览界面的header，包含文件名，时间，主题
     * 参会人放在第一个item里面，为了方便实现动画，其他item隐藏不显示
     */
    inner class ConvertShareTxtHeaderViewHolder(parent: ViewGroup) : ViewHolder(
        LayoutInflater.from(parent.context).inflate(R.layout.header_convert_content_share_txt, null)
    ) {
        val playName: TextView = itemView.findViewById(R.id.tv_play_name)
        val createTime: TextView = itemView.findViewById(R.id.tv_create_time)
        val subject: TextView = itemView.findViewById(R.id.tv_subject)
    }

    /**
     * 分享txt预览界面item，第一个item显示参会人topRoles，其他item不显示
     */
    inner class ShareConvertContentTextViewHolder(parent: ViewGroup) : ViewHolder(
        LayoutInflater.from(parent.context).inflate(R.layout.item_share_convert_content_txt, null)
    ) {
        val startTime: TextView = itemView.findViewById(R.id.start_time)
        val itemTextContent: TextView = itemView.findViewById(R.id.item_text_content)
        val animatorSpeakerView: AnimateSpeakerLayout = itemView.findViewById(R.id.animator_speaker)
        val animatorTimeView: AnimateSpeakerLayout = itemView.findViewById(R.id.animator_time)
        val tvSpeaker: TextView = itemView.findViewById(R.id.tv_speaker)
        val topDivider: View = itemView.findViewById(R.id.ll_share_top_divider)
        val topRoles: TextView = itemView.findViewById(R.id.tv_roles)
        val dp14 = parent.context.resources.getDimension(com.soundrecorder.common.R.dimen.dp14).toInt()
        private lateinit var data: ConvertContentItem
        private var callback = SeekPlayActionModeCallback()

        init {
            //只在第一个item显示参会人textview，其他的隐藏。因为有header，所以position=1
            if (mNeedShowRole && layoutPosition == 1) {
                topRoles.visibility = View.VISIBLE
            } else {
                topRoles.visibility = View.GONE
            }
        }

        fun setData(data: ConvertContentItem) {
            this.data = data
            if (FunctionOption.IS_SUPPORT_SEEKPLAY_FEATURE) {
                callback.mItemTextView = itemTextContent
                itemTextContent.customSelectionActionModeCallback = callback
            }
        }

        fun setSpeakerRoles(mRoles: String) {
            if (layoutPosition == 1) {
                topRoles.text = mRoles
            }
        }

        /**
         * 显示参会人信息，在第一个item使用，其他item不使用此方法
         */
        private fun showTopRolesAnimation() {
            topRoles.let {
                if (mRoles.isNotBlank()) {
                    //测量mRoles需要显示的高度
//                    val obtain = StaticLayout.Builder.obtain(mRoles, 0, mRoles.length, it.paint, it.measuredWidth)
//                        .setLineSpacing(it.lineSpacingExtra, it.lineSpacingMultiplier)
//                        .build()
                    val height = getStringHeight(it, mRoles)
                    DebugUtil.e(TAG, "height>>$height")
                    mShareTxtRolesViewHeight = height.toFloat()
                    //开启动画，逐步设置 topRoles 的高度
                    doValueAnimator(ValueAnimator.ofInt(0, height), it, 360)
                    //开启渐变动画，逐渐显现出 topRoles
                    val animation = AlphaAnimation(0f, 1f)
                    animation.duration = 200
                    animation.interpolator = PathInterpolatorHelper.couiEaseInterpolator
                    it.visibility = View.VISIBLE
                    it.startAnimation(animation)
//                val animation = AnimationUtils.loadAnimation(it.context, R.anim.alpha_top_in)
//                it.startAnimation(animation)
                }
            }
        }

        private fun getStringHeight(textView: TextView, string: String): Int {
            val obtain = StaticLayout.Builder.obtain(string, 0, string.length, textView.paint, textView.measuredWidth)
                .setLineSpacing(textView.lineSpacingExtra, textView.lineSpacingMultiplier)
                .build()
            return obtain.height
        }

        /**
         * 隐藏参会人信息，在第一个item使用，其他item不使用此方法
         */
        private fun dismissTopRolesAnimation() {
            topRoles.let {
                val animation = AlphaAnimation(1f, 0f)
                animation.duration = 200
                animation.interpolator = PathInterpolatorHelper.couiEaseInterpolator
                it.startAnimation(animation)
                it.visibility = View.INVISIBLE
//            val animation = AnimationUtils.loadAnimation(it.context, R.anim.alpha_top_out)
//            it.startAnimation(animation)
                doValueAnimator(ValueAnimator.ofInt(it.height, 0), it)
            }
        }

        /**
         * 开启讲话人的动画，如果是第一个item，则显示参会人信息
         */
        private fun showSpeakerAnimation() {
            if (layoutPosition == 1) {
                showTopRolesAnimation()
            }
            //如果没有显示时间，则通过设置topDivider高度来调整顶部间距
            if (!mNeedShowTime) {
                doValueAnimator(ValueAnimator.ofInt(dp14, dp14 * 2), topDivider)
            }
        }

        /**
         * 关闭讲话人的动画，如果是第一个item，则隐藏参会人信息
         */
        private fun dismissSpeakerAnimation() {
            if (layoutPosition == 1) {
                dismissTopRolesAnimation()
            }
            //如果没有显示时间，则通过设置topDivider高度来调整顶部间距
            if (!mNeedShowTime) {
                doValueAnimator(ValueAnimator.ofInt(dp14 * 2, dp14), topDivider)
            }
        }

        /**
         * 显示时间的动画，如果没有显示讲话人，则调整间距
         */
        private fun dismissTimeAnimation() {
            if (!mNeedShowRole) {
                //没有显示讲话人的情况下，设置divider高度为从48dp到24dp
                doValueAnimator(ValueAnimator.ofInt(dp14 * 2, dp14), topDivider)
            }
        }

        /**
         * 隐藏时间的动画，如果没有显示讲话人，则调整间距
         */
        private fun showTimeAnimation() {
            if (!mNeedShowRole) {
                //没有显示讲话人的情况下，高度从24dp变成48dp
                doValueAnimator(ValueAnimator.ofInt(dp14, dp14 * 2), topDivider)
            }
        }

        /*
         * show speaker view animator
         */
        fun showAnimate() {
            showSpeakerAnimation()
            animatorSpeakerView.showAnimate()
        }

        /*
         * dismiss speaker view animator
         */
        fun dismissAnimate() {
            dismissSpeakerAnimation()
            animatorSpeakerView.dismissAnimate()
        }

        /*
         * show time view animator
         */
        fun showTimeAnimate() {
            showTimeAnimation()
            animatorTimeView.showAnimate()
        }

        /*
         * dismiss time view animator
         */
        fun dismissTimeAnimate() {
            dismissTimeAnimation()
            animatorTimeView.dismissAnimate()
        }

        fun refreshSpeaker() {
            if (!this::data.isInitialized) return
            val name = data.roleName.toString()
            tvSpeaker.text = name
            switchSpeaker(mNeedShowRole)
            switchTime(mNeedShowTime)

            //初始化的时候，如果是第一个item，且显示讲话人的情况下，显示参会人信息，否则隐藏
            if (layoutPosition == 1) {
                topRoles.visibility = View.VISIBLE
                if (mNeedShowRole) {
                    topRoles.text = mRoles
                } else {
                    topRoles.height = 0
                }
            } else {
                topRoles.visibility = View.GONE
            }

            //初始化的时候，如果是显示了讲话人或者显示了时间，则间距为48dp
            val layoutParams = topDivider.layoutParams
            layoutParams.height = if (mNeedShowRole || mNeedShowTime) {
                dp14 * 2
            } else {
                //如果两个都没显示，间距则为24dp
                dp14
            }
            topDivider.layoutParams = layoutParams
        }

        /*
         * switch speaker view state
         */
        private fun switchSpeaker(flag: Boolean) {
            animatorSpeakerView.switchSpeaker(flag)
        }

        private fun switchTime(flag: Boolean) {
            animatorTimeView.switchSpeaker(flag)
        }

        fun release() {
            animatorSpeakerView.release()
            animatorTimeView.release()
            topDivider.animation.cancel()
            itemTextContent.customSelectionActionModeCallback = null
        }
    }

    /**
     * view动画，从动画开始到结束连续产生数值，view的高度会跟随数值变化
     */
    private fun doValueAnimator(animation: ValueAnimator, view: View, duration: Long = 360) {
        view.let {
            animation.duration = duration
            animation.interpolator = PathInterpolator(0.33f, 0f, 0.1f, 1f)
            animation.addUpdateListener { animator ->
                val layoutParams = it.layoutParams
                layoutParams.height = animator.animatedValue as Int
                it.layoutParams = layoutParams
            }
        }
        animation.start()
    }

    companion object {
        const val TYPE_HEADER = -1
        const val TYPE_FOOTER = -2
        const val TYPE_LIST = 1
        const val TYPE_MONTH = 2
        const val COLOR_SIZE: Int = 11

        const val TAG = "ConvertContentItemAdapter"
    }
}