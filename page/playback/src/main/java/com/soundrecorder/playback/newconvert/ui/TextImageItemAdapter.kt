/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.drawable.Drawable
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.TextUtils
import android.text.style.CharacterStyle
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.convertservice.util.BeanConvertTextUtil
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.playback.R
import com.soundrecorder.playback.convert.search.ConvertSearchBean
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipClickListener
import com.soundrecorder.playback.newconvert.keyword.KeyWordChipGroup
import com.soundrecorder.playback.newconvert.search.ConvertSearchHelper
import com.soundrecorder.playback.newconvert.ui.vh.AbsContentViewHolder
import com.soundrecorder.playback.newconvert.ui.vh.ConvertContentImageViewHolder
import com.soundrecorder.playback.newconvert.ui.vh.ConvertContentTextViewHolder
import com.soundrecorder.playback.newconvert.ui.vh.ConvertContentTimerViewHolder
import com.soundrecorder.playback.newconvert.ui.vh.ConvertFooterViewHolder
import com.soundrecorder.playback.newconvert.ui.vh.ConvertHeaderViewHolder
import com.soundrecorder.playback.newconvert.util.ListUtil
import com.soundrecorder.playback.newconvert.util.ResUtil
import com.soundrecorder.playback.newconvert.view.BackGroundTextViewSetupHelper
import com.soundrecorder.playback.newconvert.view.BackgroundTextView
import com.soundrecorder.playback.newconvert.view.ShapableImageViewSetupHelper
import com.soundrecorder.playback.newconvert.view.TextImageMixLayout
import com.soundrecorder.playback.newconvert.view.TextImageMixLayoutHelper
import com.soundrecorder.player.WavePlayerController
import java.lang.ref.WeakReference
import java.util.Objects
import java.util.function.Consumer

open class TextImageItemAdapter(var context: Context?) :
    RecyclerView.Adapter<ViewHolder>() {

    companion object {
        const val TAG = "TextImageItemAdapter"
        const val TYPE_HEADER = -1
        const val TYPE_FOOTER = -2
        const val TYPE_TIME_DIVIDER = 1
        const val TYPE_TEXT = 2
        const val TYPE_IMAGE = 3
        private const val ITEM_VIEW_CACHE_SIZE = 15
        private const val COLOR_MAX_COUNT = 10
    }

    var mOnSpeakerNameClick: OnSpeakerNameClick? = null
    var mContentScrollHelper: ContentScrollHelper? = null
    var searchHelper: ConvertSearchHelper? = null
    var keyWordClickListener: KeyWordChipClickListener? = null
    var speakerSelectListener: ISelectSpeakerListener? = null
    // 实际绑定的数据
    var data: List<ConvertContentItem.ItemMetaData>? = null
        private set
    // 之前的mContentList
    var mContentItemList: List<ConvertContentItem>? = null
        private set
    //搜索动效的监听
    var searchAnimListener: Consumer<Boolean>? = null
    /*讲话人选择弹窗相关*/
    var selectSpeakersData = mutableListOf<String>()
    var selectSpeakersHelper: SelectSpeakerHelper? = null
    var drawAttr: TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr =
        TextImageMixLayoutHelper.getDefaultDrawAttr(context)
    var player: WavePlayerController? = null
    // 是否是搜索模式
    internal var searchMode = false
    // 当前选中的第几个搜索结果
    internal var mConvertSearchCurrentFocus: Int = -1
    private val foregroundColorSpanForSearchResult =
        ForegroundColorSpan(
            BaseApplication.getAppContext().resources.getColor(
                R.color.convert_search_result_highlight_foreground,
                context?.theme
            )
        )
    internal var mNeedShowRole: Boolean = false
    internal var mPlayName: String = ""
    internal var mCreateTime: CharSequence = ""
    internal var distinctList: MutableList<String> = ArrayList()
    internal var loadingState: Int = KeyWordChipGroup.DEFAULT_STATE
    //是否显示动效
    internal var showSearchAnim: Boolean = false
    //点击文字的相关Callback
    internal var mTextClickCallback = object : BackGroundTextViewSetupHelper.OnBlackGroundTextClickListenner {

        override fun onTextViewClick(
            view: View,
            convertContentItem: ConvertContentItem?,
            currentItemIndex: Int
        ) {
            if (view is BackgroundTextView) {
                DebugUtil.i(
                    TAG,
                    "OnClick, off = ${view.offset}, len=${convertContentItem?.textContent?.length}, currentItemIndex $currentItemIndex"
                )
                if ((view.offset > 0) && (convertContentItem != null)) {
                    val realOffset =
                        convertContentItem.getTextStringLengthBeforeTextImageItemIndex(
                            currentItemIndex
                        ) + view.offset
                    val seekTime =
                        BeanConvertTextUtil.genStartPlayTime(convertContentItem, realOffset, TextImageMixLayout.SPACE.length)
                    if (seekTime < 0) {
                        DebugUtil.e(TAG, "===>seekTime < 0!")
                        return
                    }
                    DebugUtil.i(TAG, "===>seekTime = $seekTime")
                    if (player?.hasPaused() == true) {
                        player?.seekTime(seekTime)
                    } else {
                        player?.seekToPlay(seekTime)
                    }
                    mContentScrollHelper?.hideBackButtonValue()
                }
            }
        }
    }

    //点击ImageView的相关点击事件Callback
    internal var mImageClickCallback = object : ShapableImageViewSetupHelper.OnShapableImageViewClickListener {

        override fun onImageViewClick(
            view: ImageView,
            convertContentItem: ConvertContentItem?,
            currentDataBean: MarkDataBean
        ) {
            val pictures = getAllPictureMarks()
            val index = pictures.indexOf(currentDataBean)
            photoViewerApi?.startWithBigImage(
                view.context,
                pictures,
                index,
                view,
                null
            )
        }
    }
    private var mSubject: String = ""
    private var mRoles: String = ""
    private var drawableList = ArrayList<Drawable>()
    private var mDurationTime: Long = 0
    private val viewHolders = mutableListOf< WeakReference<AbsContentViewHolder>>()
    // 提取关键词
    private var keyWords: MutableList<String> = mutableListOf()
    private var convertHeaderVH: ConvertHeaderViewHolder? = null
    private var showTextAnim: Boolean? = null
    private var paddingBottom = 0

    private val photoViewerApi by lazy {
        Injector.injectFactory<PhotoViewerInterface>()
    }

    private val footHeight by lazy {
        BaseApplication.getAppContext().resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp48)
    }

    private val mSpeakerIdToColorMap = mutableMapOf<Int, Int>()
    private val mSpeakerColors = arrayOf(
        BaseApplication.getApplication().getColor(R.color.speaker_background_1),
        BaseApplication.getApplication().getColor(R.color.speaker_background_2),
        BaseApplication.getApplication().getColor(R.color.speaker_background_3),
        BaseApplication.getApplication().getColor(R.color.speaker_background_4),
        BaseApplication.getApplication().getColor(R.color.speaker_background_5),
        BaseApplication.getApplication().getColor(R.color.speaker_background_6),
        BaseApplication.getApplication().getColor(R.color.speaker_background_7),
        BaseApplication.getApplication().getColor(R.color.speaker_background_8),
        BaseApplication.getApplication().getColor(R.color.speaker_background_9),
        BaseApplication.getApplication().getColor(R.color.speaker_background_10)
    )


    private fun getAllPictureMarks(): List<MarkDataBean> {
        val pictures: MutableList<MarkDataBean> = ArrayList()
        mContentItemList?.forEach(Consumer { mark: ConvertContentItem ->
            pictures.addAll(mark.getAllPictureMarks())
        })
        pictures.sortBy {
            it.timeInMills
        }
        return pictures
    }


    init {
        drawableList = ResUtil.getDrawableList()
    }

    /**
     * 设置转文本内容列表，非adapter实际使用数据，adapter绑定是data
     * @param contentItemList 全部数据or筛选讲话人对应的数据
     */
    fun setContentData(contentItemList: List<ConvertContentItem>?, dataList: List<ConvertContentItem.ItemMetaData>?) {
        this.mContentItemList = contentItemList
        this.data = dataList
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        recyclerView.recycledViewPool.setMaxRecycledViews(TYPE_HEADER, 1)
        recyclerView.recycledViewPool.setMaxRecycledViews(TYPE_FOOTER, 1)
        recyclerView.setItemViewCacheSize(ITEM_VIEW_CACHE_SIZE)
        recyclerView.itemAnimator = null
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        viewHolders.forEach { it.get()?.release() }
        viewHolders.clear()
    }


    override fun onViewAttachedToWindow(holder: ViewHolder) {
        super.onViewAttachedToWindow(holder)
        /**
         * Android Bug , RecycleView lead to lose textView selection ability
         */
        if (holder is ConvertContentTextViewHolder) {
            holder.textView.isEnabled = false
            holder.textView.isEnabled = true
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val result: ViewHolder = when (viewType) {
            TYPE_TIME_DIVIDER -> ConvertContentTimerViewHolder(parent, this)
            TYPE_IMAGE -> ConvertContentImageViewHolder(parent, this)
            TYPE_TEXT -> ConvertContentTextViewHolder(parent, this)
            TYPE_FOOTER -> ConvertFooterViewHolder(parent, this)
            else -> {
                var vh: ViewHolder
                if (Objects.isNull(convertHeaderVH)) { //进入退出搜索状态时，viewHolder会重新创建，导致动画状态不正确
                    convertHeaderVH = ConvertHeaderViewHolder(parent, this)
                    vh = convertHeaderVH!!
                } else {
                    vh = convertHeaderVH!!
                }
                return vh
            }
        }
        return result
    }

    override fun onViewRecycled(holder: ViewHolder) {
        super.onViewRecycled(holder)
        if (holder is ConvertHeaderViewHolder) {
            holder.releaseAnim()
        }
        if (holder is ConvertContentTimerViewHolder) {
            holder.release()
        }
        if (holder is AbsContentViewHolder) {
            removeHolderToList(holder)
        }
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val data = data ?: return
        when (holder) {
            is ConvertHeaderViewHolder -> {
                holder.bindData(mPlayName.title(), mCreateTime, keyWords)
                holder.showSearchAnim()
            }
            is ConvertContentTextViewHolder -> {
                addHolderToList(holder)
                val itemDataPosition = getMetaItemPositionForViewHolderPosition(position)
                val itemMetadata = data[itemDataPosition]
                val convertItemIndex = getConvertIndexPositionForViewHolderPosition(position)
                val convertContentItem = mContentItemList?.get(convertItemIndex)

                if (convertContentItem != null && itemMetadata is ConvertContentItem.TextItemMetaData) {
                    holder.setData(itemMetadata, convertContentItem)
                    holder.setKeyWordSpannable(itemMetadata, itemDataPosition)
                }
                holder.refreshSpeaker()
            }

            is ConvertContentImageViewHolder -> {
                addHolderToList(holder)
                val itemDataPosition = getMetaItemPositionForViewHolderPosition(position)
                val itemMetadata = data[itemDataPosition]
                val convertItemIndex = getConvertIndexPositionForViewHolderPosition(position)
                val convertContentItem = mContentItemList?.get(convertItemIndex)
                if (convertContentItem != null && itemMetadata is ConvertContentItem.ImageMetaData) {
                    holder.setData(itemMetadata, convertContentItem)
                }
                holder.refreshSpeaker()
            }

            is ConvertContentTimerViewHolder -> {
                addHolderToList(holder)
                val itemDataPosition = getMetaItemPositionForViewHolderPosition(position)
                val itemMetadata = data[itemDataPosition]
                val convertItemIndex = getConvertIndexPositionForViewHolderPosition(position)
                val convertContentItem = mContentItemList?.get(convertItemIndex)
                if (convertContentItem != null && itemMetadata is ConvertContentItem.TimerDividerMetaData) {
                    holder.setData(itemMetadata, convertItemIndex)
                }
                holder.refreshSpeaker()
            }

            is ConvertFooterViewHolder ->  {
                holder.bindData()
                holder.itemView.updateLayoutParams { height = footHeight + paddingBottom }
            }
        }
    }

    private fun getConvertIndexPositionForViewHolderPosition(viewHolderPosition: Int): Int {
        var result = -1
        val dataPosition = getMetaItemPositionForViewHolderPosition(viewHolderPosition)
        if (dataPosition == -1) {
            return -1
        }
        val metaItem = data!![dataPosition]
        run {
            mContentItemList?.forEach { convertItem: ConvertContentItem ->
                if (convertItem.mTextOrImageItems?.contains(metaItem) == true) {
                    result = mContentItemList?.indexOf(convertItem) ?: -1
                    return@run
                }
            }
        }
        return result
    }

    private fun getMetaItemPositionForViewHolderPosition(viewHolderPosition: Int): Int {
        val dataPosition = viewHolderPosition - 1
        if ((dataPosition < 0) || dataPosition > data!!.size - 1) {
            DebugUtil.e(TAG, "getConvertIndexPositionForViewHolderPosition dataPosition is wrong!")
            return -1
        }
        return dataPosition
    }

    /**
     * 设置TextView的内容
     */
    internal fun setTextItemContent(
        textView: BackgroundTextView,
        textItemMetaData: ConvertContentItem.TextItemMetaData,
        searchList: MutableList<ConvertSearchBean>
    ) {

        val textContent = textItemMetaData.getTextString()
        if (TextUtils.isEmpty(textContent)) {
            DebugUtil.e(TAG, "setTextItemContent textItemMetaData:$textItemMetaData content is empty")
            return
        }
        val builder = SpannableStringBuilder(textContent)
        searchList.forEach { bean ->
            highlightKeyword(builder, bean) //高亮搜索词
            if (bean.focus) { //高亮当前选中的
                highlightSearchFocus(builder, bean, textView)
            }
        }
        textView.setItemContentSpannableForTextItemMetaData(builder, textItemMetaData, searchMode)
    }

    /**
     * 高亮搜索词
     */
    open fun highlightKeyword(builder: SpannableStringBuilder, element: ConvertSearchBean) {
        val keyWord = element.keyWord
        val keyWordIndex = element.keyWordIndex
        builder.setSpan(
            CharacterStyle.wrap(foregroundColorSpanForSearchResult),
            keyWordIndex, keyWordIndex + keyWord.length,
            Spannable.SPAN_INCLUSIVE_EXCLUSIVE
        )
    }

    /**
     * 高亮当前选中的搜索词
     */
    open fun highlightSearchFocus(
        builder: SpannableStringBuilder,
        element: ConvertSearchBean,
        itemTextContent: BackgroundTextView
    ) { }

    /**
     * @param refresh true -> notifyDataChange()  false -> speaker animate
     */
    @SuppressLint("NotifyDataSetChanged")
    fun roleControl(needShowRole: Boolean, refresh: Boolean) {
        mNeedShowRole = needShowRole
        if (refresh) {
            notifyDataSetChanged()
        } else {
            if (needShowRole) {
                viewHolders.forEach {
                    if (it.get()?.bindingAdapterPosition == RecyclerView.NO_POSITION) return@forEach
                    it.get()?.showAnimate()
                }
            } else {
                viewHolders.forEach {
                    if (it.get()?.bindingAdapterPosition == RecyclerView.NO_POSITION) return@forEach
                    it.get()?.dismissAnimate()
                }
            }
            convertHeaderVH?.refreshSelectSpeakerState()
        }
    }

    fun refreshSmartNameStatusChange(smartNaming: Boolean?, resultName: String?, showTextAnim: Boolean = true) {
        DebugUtil.d(TAG, "refreshSmartNameStatusChange, smartNaming:$smartNaming, resultName:$resultName, showTextAnim:$showTextAnim")
        this.showTextAnim = showTextAnim
        if (!resultName.isNullOrBlank()) {
            mPlayName = resultName
        }
    }

    fun setHeaderData(name: String, time: CharSequence, subject: String = "", roles: String = "", duration: Long? = 0) {
        mPlayName = name
        mCreateTime = time
        mSubject = subject
        mRoles = roles
        if (duration != null) {
            mDurationTime = duration
        }
    }

    /**
     * 添加关键词
     */
    fun setKeyWords(list: List<String>, state: Int) {
        this.loadingState = state
        keyWords.clear()
        keyWords.addAll(list)
        notifyItemChanged(0)
    }

    /**
     * 触发一次搜索动效
     */
    fun triggerSearchAnim() {
        DebugUtil.d(TAG, "触发搜索动效")
        showSearchAnim = true
        notifyItemChanged(0)
    }

    fun loadAllSpeakers(allContentItem: List<ConvertContentItem>?) {
        distinctList = ListUtil.getDistinctRoleName(allContentItem)
        DebugUtil.d(TAG, "loadAllSpeakers, distinctList:${distinctList.size}")
    }

    fun refreshAllViewHolderContentBackground() {
        viewHolders.forEach {
            val holder = it.get()
            if (holder?.bindingAdapterPosition == RecyclerView.NO_POSITION) return@forEach
            if (holder is ConvertContentTextViewHolder) {
                holder.switchSentenceBackground()
            }
        }
    }

    private fun refreshAllViewHolderSpeaker() {
        viewHolders.forEach {
            if (it.get()?.bindingAdapterPosition == RecyclerView.NO_POSITION) return@forEach
            it.get()?.refreshSpeaker()
        }
    }

    /**
     * 是否分别勾选了所有讲话人，非默认逻辑
     */
    fun isRealSelectAllSpeaker(): Boolean {
        return selectSpeakersData.isNotEmpty() && selectSpeakersData.size == distinctList.size
    }

    /**
     * 重命名讲话人成功
     * 1.更新讲话人UI
     * 2.更新选择讲话人数据
     */
    fun renameSpeakerSuccess(newName: String, oldName: String, isSelectAllSpeaker: Boolean) {
        refreshAllViewHolderSpeaker()
        if (selectSpeakersData.isEmpty() || !selectSpeakersData.contains(oldName)) {
            DebugUtil.i(TAG, "renameSpeakerSuccess return select=${selectSpeakersData.size}")
            // 1.未勾选中oldName，不做任何处理
            return
        }
        DebugUtil.i(TAG, "renameSpeakerSuccess,selectAll=$isSelectAllSpeaker")
        /*修改讲话人前是否分别勾选了所有讲话人, 产品修改交互，目前只支持单选，将这部分代码屏蔽，后续若支持多个直接放开即可*/
        /*if (isSelectAllSpeaker) {
            *//* 2.选中全部讲话人，则更新选中数据
            * - 所有讲话人中还有oldName：选中列表加上newName（选中无newName前提下）
            * - 所有讲话人没有oldName：
            *   - 选中列表newName不存在：替换oldName为newName;
            *   - 选中列表newName存在：从select中移除
            * *//*
            if (!distinctList.contains(oldName)) {
                if (!selectSpeakersData.contains(newName)) {
                    selectSpeakersData[selectSpeakersData.indexOf(oldName)] = newName
                } else {
                    selectSpeakersData.remove(oldName)
                }
            } else if (!selectSpeakersData.contains(newName)) {
                selectSpeakersData.add(newName)
            }
        } else*/ if (!distinctList.contains(oldName)) {
            /*
             * 3.勾选部分讲话人
             * - 之前选中了oldName：
             *   - 所有是否包含oldName
             *      - 包含-单条替换：不需要处理
             *      - 不包含-所有替换：select将oldNew替换为newName（select不含newName）
             * - 之前未勾选oldName：不关心--不需要处理
             */
            if (!selectSpeakersData.contains(newName)) {
                selectSpeakersData[selectSpeakersData.indexOf(oldName)] = newName
            } else {
                selectSpeakersData.remove(oldName)
            }
        }
        DebugUtil.i(TAG, "renameSpeakerSuccess after,select=${selectSpeakersData.size},all=${distinctList.size}")

        speakerSelectListener?.onSpeakerSelect(selectSpeakersData, selectSpeakersData.size == distinctList.size)
        convertHeaderVH?.refreshSelectSpeakerState()
    }


    override fun getItemCount(): Int = (data?.size ?: 0) + 1 + 1

    fun getHeaderSize(): Int = 1

    override fun getItemViewType(position: Int): Int {
        return when {
            position < 1 -> {
                TYPE_HEADER
            }
            position + 1 == itemCount -> {
                TYPE_FOOTER
            }

            data?.get(position - 1) is ConvertContentItem.TextItemMetaData -> {
                TYPE_TEXT
            }

            data?.get(position - 1) is ConvertContentItem.ImageMetaData -> {
                TYPE_IMAGE
            }

            else -> {
                TYPE_TIME_DIVIDER
            }
        }
    }


    interface OnSpeakerNameClick {
        fun onClick(pos: Int)
    }

    internal fun dismissItemTextContentAnimation(itemTextContent: View) {
        DebugUtil.d(TAG, "dismissItemTextContentAnimation")
        val y = BaseApplication.getAppContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp4)
        itemTextContent.translationY = y
        itemTextContent.animate()
            .setDuration(360L)
            .translationY(0f)
            .setInterpolator(PathInterpolatorHelper.couiEaseInterpolator)
            .start()
    }

    internal fun showItemTextContentAnimation(itemTextContent: View) {
        DebugUtil.d(TAG, "showItemTextContentAnimation")
        itemTextContent.translationY = 0f
        val y = BaseApplication.getAppContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp4)
        itemTextContent.animate()
            .setDuration(360L)
            .translationY(y)
            .setInterpolator(PathInterpolatorHelper.couiEaseInterpolator)
            .start()
    }

    /**
     * 搜索动效是否正在执行
     */
    fun isSearchAnimRunning(): Boolean {
        return convertHeaderVH?.isSearchAnimRunning() == true
    }

    fun release() {
        selectSpeakersHelper?.release()
        selectSpeakersHelper = null
        keyWordClickListener = null
        searchAnimListener = null
    }

    open fun getTextHighlightType(): Int = BackgroundTextView.HIGHLIGHT_TYPE_FOREGROUND

    fun updatePaddingBottom(paddingBottom: Int) {
        this.paddingBottom = paddingBottom
    }

    private fun addHolderToList(holder: AbsContentViewHolder) {
        val isInList = viewHolders.any { weakRef ->
            weakRef.get()?.let { it === holder } == true
        }
        if (isInList.not()) {
            viewHolders.add(WeakReference(holder))
        }
    }

    private fun removeHolderToList(holder: AbsContentViewHolder) {
        viewHolders.removeAll { weakRef ->
            weakRef.get() == null || weakRef.get() === holder
        }
    }

    fun getSpeakerColorById(roleId: Int): Int {
        return mSpeakerIdToColorMap.getOrPut(roleId) {
            if (mSpeakerIdToColorMap.size < COLOR_MAX_COUNT) {
                mSpeakerColors[mSpeakerIdToColorMap.size % mSpeakerColors.size]
            } else {
                mSpeakerColors.random()
            }
        }
    }
}