/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/3
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.playback.mute

import android.content.Context
import android.media.MediaCodec
import android.media.MediaExtractor
import android.media.MediaFormat
import android.net.Uri
import android.os.Handler
import android.os.HandlerThread
import android.text.TextUtils
import com.soundrecorder.base.utils.DebugUtil
import java.io.FileDescriptor
import java.nio.ByteBuffer
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock

/**
 * 将音频文件异步转成pcm流
 */
class MuteAudioPCMAsyncManager {

    companion object {
        private const val TAG = "AudioPCMManager"

        private const val HANDLER_MSG_INPUT_AVAILABLE = 1
        private const val HANDLER_MSG_OUTPUT_AVAILABLE = 2
        private const val HANDLER_MSG_ERROR = 3
        private const val HANDLER_MSG_RELEASE = 4
    }

    private val reentrantLock = ReentrantLock()
    private var extractor: MediaExtractor? = MediaExtractor()

    var mediaFormat: MediaFormat? = null

    @Volatile
    private var isReleased = false

    var supportMimeType: MutableList<String>? = null

    @Volatile
    private var isCancel: Boolean = false

    @Volatile
    private var isResultComputing = false

    @Volatile
    private var waitObject = Object()

    private var handlerThread: HandlerThread? = null
    private var handler: Handler? = null

    init {
        val hThread = HandlerThread(TAG).apply { start() }.also { handlerThread = it }
        handler = Handler(hThread.looper) {
            if (isReleased) return@Handler true
            when (it.what) {
                HANDLER_MSG_INPUT_AVAILABLE -> {
                    (it.obj as? MediaCodecCallbackInputData)?.apply {
                        onInputBufferAvailable(audioCodec, info, callback, codec, index)
                    }
                }
                HANDLER_MSG_OUTPUT_AVAILABLE -> {
                    (it.obj as? MediaCodecCallbackOutputData)?.apply {
                        onOutputBufferAvailable(callback, chunkPCM, codec, index, isEnd)
                    }
                }
                HANDLER_MSG_ERROR -> {
                    (it.obj as? MediaCodec)?.apply {
                        onBufferError(this)
                    }
                }
                HANDLER_MSG_RELEASE -> release()
            }
            return@Handler true
        }
    }

    fun extractFormatInfo(context: Context, inputUri: Uri, callback: IExtractFormatCallback?): MediaFormat? {
        if (isReleased) return null
        isCancel = false

        var audioTrack = -1
        var hasAudio = false
        reentrantLock.withLock {
            val inputDescriptor: FileDescriptor? = MuteUtil.getFileDescriptor(context, inputUri, "r")
            if (inputDescriptor != null) {
                extractor?.setDataSource(inputDescriptor)
            } else {
                callback?.onError(MuteConstants.CHECK_STATUS_NO_INPUT_FILE_DESCRIPTOR)
                DebugUtil.e(TAG, "NO File found return")
                return null
            }
            var mimeType: String? = null
            val trackCnt = extractor?.trackCount ?: 0
            if (trackCnt > 0) {
                for (i in 0 until trackCnt) {
                    val format = extractor?.getTrackFormat(i)
                    if (format != null) {
                        mimeType = MuteUtil.getString(format, MediaFormat.KEY_MIME, "").toString()
                        DebugUtil.i(TAG, "convertMp3File mMimetype: $mimeType")
                        if (!TextUtils.isEmpty(mimeType) && (mimeType.startsWith(MuteConstants.TYPE_AUDIO))) {
                            audioTrack = i
                            hasAudio = true
                            break
                        }
                    }
                }
            }

            if (!hasAudio) {
                DebugUtil.e(TAG, "no audio track")
                callback?.onError(MuteConstants.CHECK_STATUS_NO_AUDIO_TRACK)
                return null
            }
            if (!checkSupportMimeType(mimeType)) {
                DebugUtil.e(TAG, "mimetype not in $supportMimeType")
                callback?.onError(MuteConstants.CHECK_STATUS_INVALID_FORMAT)
                return null
            }
            extractor?.selectTrack(audioTrack)
            mediaFormat = extractor?.getTrackFormat(audioTrack)

            if (mediaFormat == null) {
                callback?.onError(MuteConstants.CHECK_STATUS_INVALID_FORMAT)
                DebugUtil.i(TAG, "mediaFormat null")
            }
        }
        return mediaFormat
    }

    private fun checkSupportMimeType(mimeType: String?): Boolean {
        val supportTypes = supportMimeType.takeUnless { it.isNullOrEmpty() } ?: return true
        if (mimeType.isNullOrEmpty()) {
            return false
        }
        supportTypes.forEach {
            if (mimeType.equals(it, true)) {
                return true
            }
        }
        return false
    }

    fun extractPCMData(callback: IExtractPCMCallback?) {
        if (isReleased) return
        reentrantLock.withLock {
            runCatching {
                //init and config mediaCodec, start
                val mimetype = mediaFormat?.getString(MediaFormat.KEY_MIME) ?: return
                val audioCodec = MediaCodec.createDecoderByType(mimetype)
                val inputInfo = MediaCodec.BufferInfo()

                //seekToStart
                extractor?.seekTo(0, MediaExtractor.SEEK_TO_PREVIOUS_SYNC)

                audioCodec.setCallback(object : MediaCodec.Callback() {
                    override fun onInputBufferAvailable(codec: MediaCodec, index: Int) {
                        if (isReleased) return
                        handler?.let {
                            val msg = it.obtainMessage(HANDLER_MSG_INPUT_AVAILABLE)
                            msg.obj = MediaCodecCallbackInputData(audioCodec, inputInfo, callback, codec, index)
                            it.sendMessage(msg)
                        }
                    }

                    override fun onOutputBufferAvailable(codec: MediaCodec, index: Int, info: MediaCodec.BufferInfo) {
                        if (isReleased) return
                        //DebugUtil.i(TAG, "onOutputBufferAvailable: ${index}, info: ${info}")
                        /*异步解析，此回调在主线程中，codec.release耗时会造成卡顿，放在子线程执行
                        同时多个buffer完毕，放入子线程会导致outputBuffer[chunkPCM]申请时内存不足失败，该段代码在主线程执行*/
                        val outputBuffer: ByteBuffer = runCatching {
                            codec.getOutputBuffer(index)
                        }.onFailure {
                            DebugUtil.e(TAG, "getOutputBuffer error,$it")
                        }.getOrNull() ?: return

                        val chunkPCM = ByteArray(info.size)
                        outputBuffer[chunkPCM]
                        outputBuffer.clear()

                        handler?.let {
                            val isEnd = info.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM != 0
                            val msg = it.obtainMessage(HANDLER_MSG_OUTPUT_AVAILABLE)
                            msg.obj = MediaCodecCallbackOutputData(callback, chunkPCM, codec, index, isEnd)
                            it.sendMessage(msg)
                        }
                    }

                    override fun onError(codec: MediaCodec, e: MediaCodec.CodecException) {
                        DebugUtil.e(TAG, "onError", e)
                        handler?.let {
                            val msg = it.obtainMessage(HANDLER_MSG_OUTPUT_AVAILABLE)
                            msg.obj = codec
                            it.sendMessage(msg)
                        }
                    }

                    override fun onOutputFormatChanged(codec: MediaCodec, format: MediaFormat) {
                        DebugUtil.e(TAG, "onOutputFormatChanged $format")
                    }
                })

                mediaFormat?.let {
                    audioCodec.configure(it, null, null, 0)
                    isResultComputing = true
                    audioCodec.start()
                } ?: return
            }.onFailure {
                DebugUtil.e(TAG, "decode error", it)
                callback?.onError(MuteConstants.CONVERT_STATUS_ERROR)
            }
        }

        DebugUtil.i(TAG, "wait for runnable over")
        synchronized(waitObject) {
            while (isResultComputing) {
                try {
                    waitObject.wait()
                } catch (ignored: InterruptedException) {
                    DebugUtil.e(TAG, "extractPCMData wait error", ignored)
                }
            }
        }
    }

    /**
     * 异步解析pcm, inputBuffer数据准备完毕
     */
    private fun onInputBufferAvailable(
        audioCodec: MediaCodec,
        inputInfo: MediaCodec.BufferInfo,
        callback: IExtractPCMCallback?,
        codec: MediaCodec,
        index: Int
    ) {
        //take the input buffers from the splitter and write to the decoder
        val inputBuffer = runCatching {
            codec.getInputBuffer(index) ?: return
        }.onFailure {
            DebugUtil.e(TAG, "getInputBuffer error ${it.message}")
            return
        }.getOrNull() ?: return
        inputBuffer.clear()
        if (isCancel) {
            DebugUtil.e(TAG, "cancel is true, queueInputBuffer end of stream")
            //cancel to quene end flag to mediaCodec to stop codec
            runCatching {
                audioCodec.queueInputBuffer(index, 0, 0, 0L, MediaCodec.BUFFER_FLAG_END_OF_STREAM)
            }.onFailure {
                DebugUtil.e(TAG, "queueInputBuffer error ${it.message}")
            }
            //do not callback cancel here , because to wait to on onOutputBufferAvailable to complete
            return
        }

        if (callback?.checkInputAvailable() == false) {
            DebugUtil.e(TAG, "checkInput is false, queueInputBuffer end of stream")
            //cancel to quene end flag to mediaCodec to stop codec
            runCatching {
                audioCodec.queueInputBuffer(index, 0, 0, 0L, MediaCodec.BUFFER_FLAG_END_OF_STREAM)
            }.onFailure {
                DebugUtil.e(TAG, "queueInputBuffer error ${it.message}")
            }
            //do not callback cancel here , because to wait to on onOutputBufferAvailable to complete
            return
        }

        val sampleSize = runCatching {
            extractor?.readSampleData(inputBuffer, 0) ?: -1
        }.onFailure {
            DebugUtil.e(TAG, "readSampleData: $it")
            -1
        }.getOrNull() ?: -1
        if (sampleSize < 0) {
            DebugUtil.i(TAG, "sampleSize is less than 0")
            runCatching {
                audioCodec.queueInputBuffer(index, 0, 0, 0L, MediaCodec.BUFFER_FLAG_END_OF_STREAM)
            }.onFailure {
                DebugUtil.e(TAG, "queueInputBuffer error ${it.message}")
            }
        } else {
            inputInfo.offset = 0
            inputInfo.size = sampleSize
            inputInfo.flags = MediaCodec.BUFFER_FLAG_SYNC_FRAME
            reentrantLock.withLock {
                val sampleTime = runCatching { extractor?.sampleTime ?: 0 }.onFailure {
                    DebugUtil.e(TAG, "sampleTime error: " + it.message)
                }.getOrNull() ?: 0
                inputInfo.presentationTimeUs = sampleTime
                //Log.i(TAG, "extractor sampleSize: $sampleSize, sampleTime:${sampleTime}, inputIndex : ${index}")
                runCatching {
                    audioCodec.queueInputBuffer(index, inputInfo.offset, sampleSize, inputInfo.presentationTimeUs, 0)
                }.onFailure {
                    DebugUtil.e(TAG, "queueInputBuffer error: " + it.message)
                }

                runCatching {
                    extractor?.advance()
                }.onFailure {
                    DebugUtil.e(TAG, "advance error: " + it.message)
                }
                callback?.onPostInputAvailable(extractor, sampleTime)
            }
        }
    }
    /**
     * 异步解析pcm, outBuffer数据准备完毕
     */
    private fun onOutputBufferAvailable(
        callback: IExtractPCMCallback?,
        chunkPCM: ByteArray,
        codec: MediaCodec,
        index: Int,
        isEnd: Boolean
    ) {
        runCatching {
            codec.releaseOutputBuffer(index, false)
            if (isEnd) {
                codec.stop()
                codec.release()
            }
            callback?.onPostOutputAvailable(chunkPCM, isEnd)
        }.onFailure {
            DebugUtil.e(TAG, "onOutputBufferAvailable error: ${it.message}")
        }
    }

    private fun onBufferError(codec: MediaCodec) {
        runCatching {
            codec.stop()
            codec.release()
        }.onFailure {
            DebugUtil.e(TAG, "onBufferError error: ${it.message}")
        }
        wakeWaitObject()
    }

    fun wakeWaitObject() {
        isResultComputing = false
        synchronized(waitObject) {
            waitObject.notify()
        }
    }

    fun isCancelled(): Boolean {
        return isCancel
    }

    fun cancel() {
        DebugUtil.i(TAG, "cancel")
        isCancel = true
        wakeWaitObject()
    }

    fun sendMsgRelease() {
        handler?.let {
            val msg = it.obtainMessage(HANDLER_MSG_RELEASE)
            it.sendMessage(msg)
        }
    }

    fun release() {
        DebugUtil.i(TAG, "release")

        isReleased = true
        handler?.removeCallbacksAndMessages(null)
        handler = null
        handlerThread?.quitSafely()
        handlerThread = null

        try {
            if (reentrantLock.tryLock(1, TimeUnit.SECONDS)) {
                try {
                    extractor?.release()
                    extractor = null
                } finally {
                    reentrantLock.unlock()
                }
            }
        } catch (e: InterruptedException) {
            DebugUtil.d(TAG, "release, interrupted:$e")
            Thread.currentThread().interrupt()
        }

        mediaFormat = null
        wakeWaitObject()
    }
}

class MediaCodecCallbackInputData(
    val audioCodec: MediaCodec,
    val info: MediaCodec.BufferInfo,
    val callback: IExtractPCMCallback?,
    //data from function callback
    val codec: MediaCodec,
    val index: Int,
)

class MediaCodecCallbackOutputData(
    val callback: IExtractPCMCallback?,
    val chunkPCM: ByteArray,
    //data from function callback
    val codec: MediaCodec,
    val index: Int,
    val isEnd: Boolean
)

interface IExtractFormatCallback {
    fun onError(errorCode: Int)
}

interface IExtractPCMCallback : IExtractFormatCallback {
    fun checkInputAvailable(): Boolean

    fun onPostInputAvailable(extractor: MediaExtractor?, sampleTime: Long)

    fun onPostOutputAvailable(chunkPCM: ByteArray, isEnd: Boolean)
}