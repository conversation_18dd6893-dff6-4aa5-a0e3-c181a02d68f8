package oplus.multimedia.soundrecorder.playback.mute

import oplus.multimedia.soundrecorder.playback.mute.MuteConstants.SMOOTH_TIME
import oplus.multimedia.soundrecorder.playback.mute.MuteConstants.THRESHOLD_TIME

object MuteTimeUtil {

    /**
     * 1.curTime位于静音片段内
     * 2.end - curTime >= 2000ms
     *
     * @param curTime 当前播放时间
     * @param muteInfo 当前时间所查找到的静音数据
     * @return 跳过静音的时间；若为 -1，则表示没有相应静音片段，无需跳过
     */
    fun getSeekTime(curTime: Long, muteInfo: MuteInfo): Long {
        val end = muteInfo.curItem.endTime
        if (curTime >= muteInfo.curItem.startTime && end - curTime >= THRESHOLD_TIME) {
            return end - SMOOTH_TIME
        }
        return -1
    }

    /**
     * 拖动进度条、波形、快进、快退时，判断之前缓存的 [muteInfo] 是否有效
     *
     * @param curTime 当前播放时间
     * @param muteInfo 当前时间所查找到的静音数据
     * @param list 当前音频对应的静音数据
     * @return [muteInfo] 是否有效
     */
    fun isMuteInfoValid(curTime: Long, muteInfo: MuteInfo, list: List<MuteItem>): Boolean {
        val lastItem = muteInfo.lastItem
        val curItem = muteInfo.curItem

        // lastItem curItem 均失效
        if (curTime >= curItem.endTime) {
            return false
        }
        if (lastItem != null) {
            if (curTime >= lastItem.startTime && curTime < lastItem.endTime) {
                //lastItem有效，将 lastItem 赋给 curItem
                muteInfo.curItem = lastItem
                val lastIndex = list.indexOf(lastItem)
                // 把 lastItem 的前一个赋给 lastItem
                muteInfo.lastItem = if (lastIndex >= 1) {
                    list[lastIndex - 1]
                } else {
                    null
                }
            } else if (curTime < lastItem.startTime) {
                // lastItem curItem 均失效
                return false
            }
        }
        return true
    }

    fun getMuteData(list: List<MuteItem>, curTime: Long): MuteInfo? {
        return when (val position = binarySearch(list, curTime)) {
            -1 -> {
                null
            }
            0 -> {
                MuteInfo(null, list[0])
            }
            else -> {
                MuteInfo(list[position - 1], list[position])
            }
        }
    }

    /**
     * 二分查找，从静音数据 [list] 中找到与当前时间 [target] 对应的 [MuteItem]
     *
     * @return [list] 的下标。若找不到则返回 -1
     */
    private fun binarySearch(list: List<MuteItem>, target: Long): Int {
        var low = 0
        var high = list.size - 1
        while (low <= high) {
            val mid = (high - low) / 2 + low
            val value = list[mid]
            //1.当 target 位于 MuteItem 区间内时，返回相应的 MuteItem
            when {
                target in value.startTime..value.endTime -> {
                    return mid
                }
                value.startTime > target -> {
                    high = mid - 1
                }
                else -> {
                    low = mid + 1
                }
            }

            //2.当 target 位于 MuteItem 区间外；且 target 在 静音片段List<MuteItem> 前面
            val isFirst = (mid == 0) && (value.startTime > target)

            //3.当 target 位于 MuteItem 区间外时；且 target 在静音片段 List<MuteItem> 之间，返回与其距离最近的下一个 MuteItem
            val isMiddleOfList = (mid > 0) && (value.startTime > target) && (list[mid - 1].startTime < target)
            if (isFirst || isMiddleOfList) {
                return mid
            }
        }
        return -1
    }
}