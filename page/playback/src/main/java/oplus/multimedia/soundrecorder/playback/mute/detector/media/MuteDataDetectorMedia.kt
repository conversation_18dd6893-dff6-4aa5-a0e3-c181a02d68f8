/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/12/3
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package oplus.multimedia.soundrecorder.playback.mute.detector.media

import android.content.Context
import android.media.MediaExtractor
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.db.MediaDBUtils
import oplus.multimedia.soundrecorder.playback.mute.IExtractFormatCallback
import oplus.multimedia.soundrecorder.playback.mute.IExtractPCMCallback
import oplus.multimedia.soundrecorder.playback.mute.MuteAudioPCMAsyncManager
import oplus.multimedia.soundrecorder.playback.mute.MuteItem
import oplus.multimedia.soundrecorder.playback.mute.detector.IMuteDataDetector
import java.lang.Exception

/**
 * 通过多媒体so包获取音频文件中的静音数据,包括三部分：
 * 1、将音频文件通过Uri获取MediaFormat,并对多媒体so基本参数做初始化
 * 2、文件转成PCM流，异步进行（部分灌入buffer->buffer满后读取&处理->接着灌入buffer循环往复）
 * 3、读取buffer中数据处理：
 *    1）多声道转成单声道处理，双声道中抛弃一个声道数据
 *    2）buffer中数据每次截取10ms对应的大小传给so
 *    3)多媒体silenceDetectProcess返回静音数据片段
 * 4、pcm流读取完毕释放
 */
class MuteDataDetectorMedia : IMuteDataDetector {

    companion object {
        private const val TAG = "MuteDataDetectorMedia"
    }

    private val mPcmManager = MuteAudioPCMAsyncManager()
    private var mTaskManager: MuteDataDetectorMediaTask? = null
    private var firstFrame = true

    override suspend fun load(context: Context, mediaId: Long, callback: IExtractFormatCallback?): List<MuteItem>? {
        val startTime = System.currentTimeMillis()
        try {
            val inputUri = MediaDBUtils.genUri(mediaId)
            //音频数据解析获得MediaFormat基本数据
            val format = mPcmManager.extractFormatInfo(context, inputUri, callback) ?: return null

            //根据基本数据初始化静音检测so
            mTaskManager = MuteDataDetectorMediaTask(format).also {
                it.mOnTaskEnd = { mPcmManager.wakeWaitObject() }
            }

            firstFrame = true
            //正式硬解码获取PCM流，该流程为异步过程，边解边传输数据给多媒体so
            mPcmManager.extractPCMData(object : IExtractPCMCallback {

                override fun checkInputAvailable(): Boolean {
                    return true
                }

                override fun onPostInputAvailable(extractor: MediaExtractor?, sampleTime: Long) {
                    //do nothing
                }

                override fun onPostOutputAvailable(chunkPCM: ByteArray, isEnd: Boolean) {
                    if (firstFrame || isEnd) {
                        firstFrame = false
                        DebugUtil.d(TAG, "onPostOutputAvailable: size is ${chunkPCM.size}, end is $isEnd ")
                    }
                    mTaskManager?.addTask(chunkPCM, isEnd)
                }

                override fun onError(errorCode: Int) {
                    callback?.onError(errorCode)
                }
            })
        } catch (e: Exception) {
            DebugUtil.e(TAG, "load data error", e)
        }

        //calculate cost time
        val endTime = System.currentTimeMillis()
        val diffTime = endTime - startTime
        val showDiffTime = TimeUtils.getFormatTimeByMillisecond(diffTime)
        DebugUtil.i(TAG, "get mute data($mediaId), cost time: $showDiffTime, muteData is ${mTaskManager?.mMuteData}")

        return if (mPcmManager.isCancelled()) {
            null
        } else {
            mTaskManager?.mMuteData
        }
    }

    override fun cancel() {
        mPcmManager.cancel()
        mTaskManager?.onTaskRelease()
        mTaskManager = null
    }

    override fun release() {
//        mPcmManager.release()
        mPcmManager.sendMsgRelease()
        mTaskManager?.onTaskRelease()
        mTaskManager = null
    }
}