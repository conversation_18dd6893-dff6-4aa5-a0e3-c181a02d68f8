<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="168dp"
    android:height="120dp"
    android:viewportWidth="168"
    android:viewportHeight="120">
  <group>
    <clip-path
        android:pathData="M33.32,15h99.45v89.4h-99.45z"/>
    <path
        android:pathData="M34.8,16.45C34.8,16.06 34.96,15.7 35.23,15.42C35.5,15.15 35.87,15 36.25,15H62.06C62.44,15 62.81,15.15 63.08,15.42C63.35,15.7 63.5,16.06 63.5,16.45V56.14C63.5,56.41 63.43,56.68 63.28,56.91C63.14,57.14 62.93,57.33 62.68,57.44C62.44,57.56 62.16,57.61 61.89,57.58C61.62,57.55 61.36,57.44 61.15,57.27C57.17,54.08 53.71,50.91 50.08,47.71C49.82,47.48 49.48,47.35 49.14,47.35C48.79,47.35 48.45,47.48 48.19,47.71C44.6,50.82 40.82,53.94 37.18,57.06C36.97,57.24 36.71,57.36 36.44,57.4C36.16,57.44 35.88,57.4 35.62,57.29C35.37,57.17 35.16,56.98 35.01,56.75C34.86,56.51 34.78,56.24 34.78,55.96L34.8,16.45Z"
        android:fillColor="#8D65AC"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M71.02,92.03C70.61,92.9 70.39,93.85 70.39,94.81C70.39,95.77 70.61,96.72 71.01,97.58"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M71.35,98.8H42.8C42.7,98.8 42.6,98.77 42.51,98.72C42.42,98.67 42.34,98.6 42.29,98.51C41.6,97.4 41.24,96.12 41.24,94.81C41.24,93.51 41.6,92.23 42.29,91.12C42.34,91.03 42.42,90.96 42.51,90.91C42.6,90.86 42.7,90.83 42.8,90.83H71.35C71.51,90.83 71.66,90.89 71.77,91.01C71.89,91.12 71.95,91.27 71.95,91.43C71.95,91.59 71.89,91.74 71.77,91.85C71.66,91.96 71.51,92.03 71.35,92.03H43.15C42.68,92.88 42.44,93.84 42.44,94.81C42.44,95.79 42.68,96.74 43.15,97.6H71.35C71.51,97.6 71.66,97.66 71.77,97.78C71.89,97.89 71.95,98.04 71.95,98.2C71.95,98.36 71.89,98.51 71.77,98.62C71.66,98.73 71.51,98.8 71.35,98.8Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M72.37,90.81H50.14C49.98,90.81 49.83,90.74 49.72,90.63C49.61,90.52 49.54,90.37 49.54,90.21C49.54,90.05 49.61,89.9 49.72,89.79C49.83,89.67 49.98,89.61 50.14,89.61H72.12C72.8,88.84 73.17,87.85 73.17,86.83C73.17,85.8 72.8,84.81 72.12,84.04H50.14C49.98,84.04 49.83,83.98 49.72,83.87C49.61,83.76 49.54,83.6 49.54,83.44C49.54,83.29 49.61,83.13 49.72,83.02C49.83,82.91 49.98,82.85 50.14,82.85H72.37C72.53,82.85 72.68,82.91 72.79,83.02C73.8,84.03 74.37,85.4 74.37,86.83C74.37,88.25 73.8,89.62 72.79,90.63C72.68,90.74 72.53,90.81 72.37,90.81Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M50.68,84.05C51.26,84.86 51.57,85.83 51.57,86.83C51.57,87.83 51.26,88.8 50.68,89.61"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.33,104.29H33.44V100.89C33.44,100.31 33.67,99.75 34.09,99.34C34.5,98.93 35.05,98.7 35.63,98.7H78.13C78.42,98.7 78.71,98.75 78.97,98.86C79.24,98.97 79.48,99.13 79.68,99.34C79.89,99.54 80.05,99.78 80.16,100.05C80.27,100.31 80.33,100.6 80.33,100.89V104.29Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M97.95,41.92C98.14,41.93 98.33,41.92 98.53,41.9C99.61,41.76 100.65,41.46 101.62,41.01C101.34,41.59 100.94,42.11 100.46,42.53C99.74,43.16 98.86,43.56 97.92,43.68C97.89,43.68 97.87,43.68 97.84,43.68L97.95,41.92Z"
        android:fillColor="#808080"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M106.63,25.66C106.18,21.59 102.73,18.43 98.55,18.43C94.06,18.43 90.42,22.07 90.42,26.56C90.42,28.53 91.12,30.33 92.28,31.74C92.35,28.49 93.05,22.95 96.77,22.04C100.37,21.15 104.37,22.92 106.63,25.66Z"
        android:fillColor="#CC8A52"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M96.29,35.54C96.76,35.51 97.2,35.33 97.55,35.03"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.61,31.06C93.61,31.51 93.84,31.88 94.21,31.89C94.57,31.9 94.81,31.56 94.84,31.11C94.87,30.92 94.83,30.73 94.72,30.57C94.6,30.42 94.43,30.31 94.25,30.28C93.92,30.27 93.63,30.62 93.61,31.06Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M100.06,31.38C100.03,31.57 100.07,31.77 100.18,31.93C100.29,32.09 100.46,32.21 100.66,32.25C101.01,32.25 101.31,31.9 101.33,31.43C101.36,31.24 101.32,31.04 101.2,30.87C101.09,30.71 100.92,30.6 100.73,30.56C100.36,30.56 100.08,30.91 100.06,31.38Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M93.31,30.31C93.42,30.1 93.61,29.93 93.83,29.82C94.04,29.71 94.29,29.68 94.53,29.71C94.75,29.74 94.96,29.82 95.15,29.94C95.34,30.05 95.49,30.21 95.62,30.39"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M102.48,30.66C102.31,30.45 102.1,30.28 101.85,30.16C101.61,30.04 101.35,29.97 101.07,29.96C100.83,29.94 100.59,29.99 100.37,30.09C100.16,30.2 99.97,30.36 99.84,30.56"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.68,32.21C108.57,32.09 108.43,32 108.28,31.95C108.12,31.9 107.96,31.88 107.8,31.91C107.47,31.97 107.18,32.12 106.95,32.36"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M108.29,28.87C107.69,30.21 107.34,31.21 106.46,31.71C106.4,31.74 106.33,31.75 106.26,31.75C106.19,31.74 106.13,31.72 106.07,31.68C106.02,31.64 105.98,31.58 105.95,31.52C105.93,31.46 105.92,31.39 105.93,31.32C106.05,30.43 105.9,29.53 105.5,28.72C101.7,28.57 99.22,26.62 96.9,23.4C94.83,24.75 93.2,26.68 92.2,28.95C91.97,25.86 93.7,22.36 96.44,21.31C106.49,17.58 109.85,25.33 108.29,28.87Z"
        android:fillColor="#CC8A52"/>
    <path
        android:pathData="M72.83,36.73L72.6,36.28C72.59,36.26 72.58,36.25 72.57,36.24C72.55,36.22 72.54,36.22 72.52,36.21C72.5,36.21 72.48,36.21 72.47,36.21C72.45,36.21 72.43,36.22 72.42,36.23L71.45,36.96C71.42,36.98 71.39,37.02 71.38,37.06C71.37,37.1 71.37,37.15 71.39,37.19C71.41,37.23 71.44,37.26 71.49,37.28C71.53,37.3 71.57,37.3 71.61,37.28L72.81,36.91C72.84,36.89 72.85,36.85 72.86,36.82C72.86,36.79 72.85,36.75 72.83,36.73Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M106.15,37.89L106.08,38.99C106.39,38.8 106.69,38.59 106.98,38.36C106.66,38.27 106.38,38.11 106.15,37.89ZM109.51,35.64C110.83,33.7 111.68,31.36 111.82,28.91C112.12,23.38 108.25,19.31 103.14,19.05L101.66,18.96C96.56,18.59 92.21,22.18 91.88,27.73L91.8,29.21C91.72,30.6 91.9,31.98 92.29,33.27C92.2,30.73 92.35,23.13 96.77,22.04C101.51,20.87 106.95,24.31 108.18,28.49C108.39,29.19 108.44,29.84 108.39,30.42C108.7,30.53 108.97,30.71 109.21,30.93C109.97,31.86 109.26,33.47 108.39,34.45C108.9,34.67 109.31,35.1 109.51,35.64Z"
        android:fillColor="#CC8A52"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M73.07,36.63L72.84,36.17C72.78,36.05 72.64,36.01 72.52,36.07C72.41,36.12 72.36,36.26 72.42,36.38L72.66,36.84C72.71,36.95 72.85,37 72.97,36.94C73.08,36.88 73.13,36.74 73.07,36.63Z"
        android:fillColor="#323739"/>
    <path
        android:pathData="M99.05,27.98C100.02,27.82 101.02,27.87 101.98,28.12C102.75,28.33 103.49,28.66 104.15,29.11"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.59,37.51C96.59,37.51 96.38,37.12 97.13,36.81C98.01,36.58 98.92,36.46 99.84,36.46C99.78,36.78 99.65,37.08 99.46,37.33C99.27,37.59 99.03,37.8 98.75,37.95C97.08,38.67 96.59,37.51 96.59,37.51Z"
        android:fillColor="#808080"/>
    <path
        android:pathData="M79.19,30.06L78.25,31.76C78.21,31.83 78.2,31.91 78.21,31.99C78.22,32.07 78.25,32.15 78.3,32.21C78.35,32.27 78.41,32.32 78.49,32.35C78.57,32.37 78.65,32.38 78.72,32.36C78.96,32.3 79.2,32.22 79.41,32.1C80.29,31.59 81.32,29.8 81.32,29.8"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M80.98,30.35L81.26,30.85"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.46,37.48C97.67,37.56 98.87,37.17 99.81,36.39"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M84.64,31.89L85.07,32.34"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.27,31.97C92.27,32.99 92.31,33.76 92.33,34C92.74,38.48 95.4,41.99 98.52,41.89C100.28,41.83 102.22,40.59 103.75,39.49"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M97.97,41.94L97.85,43.95C97.85,43.95 97.56,44.22 96.82,44.51"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.24,37.99L105.92,44.05C106.29,44.27 106.69,44.46 107.1,44.61"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.23,37.79C76.23,37.86 76.2,37.95 76.19,38.04C75.89,40.29 75.52,42.55 75.19,44.81"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M75.77,36.31C75.75,36.49 75.75,36.67 75.77,36.84C75.82,37.19 75.97,37.52 76.21,37.79"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.1,29.86L81.62,28.07C81.51,27.97 81.38,27.91 81.24,27.87C81.1,27.84 80.96,27.83 80.82,27.85C80.67,27.88 80.54,27.93 80.42,28.02C80.31,28.1 80.21,28.21 80.14,28.33L79.2,30.04L77.89,30.19C77.37,30.24 76.87,30.44 76.46,30.78C76.05,31.11 75.75,31.56 75.6,32.06C75.53,32.29 75.51,32.52 75.53,32.76C75.56,33.05 75.65,33.32 75.82,33.56"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M82.65,38.64C82.56,40.38 82.5,42.12 82.44,43.87"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M114.79,44.6C114.61,42.94 114.43,41.35 114.22,39.55L114.22,39.55L114.22,39.55L114.22,39.55C114.02,37.84 113.79,35.95 113.54,33.65C113.31,31.6 112.73,30.05 110.54,30.25C109.92,30.31 109.42,30.49 109.03,30.78C109.09,30.83 109.15,30.88 109.21,30.93C109.97,31.86 109.26,33.47 108.39,34.45C109.12,34.77 109.63,35.5 109.63,36.36C109.63,37.34 108.95,38.17 108.02,38.39C107.92,39.15 107.79,39.91 107.65,40.67L107.65,40.67L107.65,40.67V40.67V40.67C107.5,41.53 107.35,42.39 107.25,43.23C107.21,43.61 107.17,43.98 107.14,44.33C109.7,44.03 112.28,44.13 114.79,44.6Z"
        android:fillColor="#CC8A52"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M74.46,34.55C74.46,34.59 74.48,34.62 74.49,34.66L75.28,36.19C75.29,36.21 75.3,36.23 75.32,36.25C75.29,36.27 75.26,36.29 75.22,36.3L73.27,37.07C73.22,37.08 73.16,37.08 73.11,37.05C73.06,37.02 73.02,36.98 73,36.93L72.54,36.04C72.51,35.99 72.5,35.93 72.51,35.88C72.52,35.82 72.55,35.77 72.59,35.73L74.39,34.58C74.41,34.57 74.43,34.56 74.46,34.55Z"
        android:fillColor="#323739"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M70.61,36.76C67.65,36.76 67.65,38.84 64.68,38.84C61.71,38.84 61.68,36.76 58.74,36.76C55.81,36.76 55.78,38.84 52.82,38.84C49.86,38.84 49.83,36.76 46.88,36.76C43.94,36.76 43.89,38.84 40.95,38.84"
        android:strokeLineJoin="round"
        android:strokeWidth="1.54"
        android:fillColor="#00000000"
        android:strokeColor="#B3B0CF"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M78.38,32.36L74.62,34.26C74.59,34.27 74.56,34.3 74.53,34.33C74.51,34.36 74.49,34.39 74.47,34.43C74.46,34.47 74.46,34.51 74.46,34.54C74.46,34.58 74.47,34.62 74.49,34.66L75.28,36.19C75.31,36.26 75.38,36.31 75.45,36.34C75.53,36.36 75.61,36.36 75.68,36.32L81.21,33.5"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.78,32.18L87.74,30.16C87.77,30.14 87.8,30.11 87.83,30.08C87.86,30.05 87.87,30.02 87.89,29.98C87.9,29.94 87.9,29.9 87.9,29.87C87.89,29.83 87.88,29.79 87.86,29.75L87.08,28.22C87.04,28.15 86.98,28.1 86.91,28.07C86.83,28.05 86.75,28.05 86.68,28.09L80.17,31.42"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M79.14,38.01C79.14,38.01 78.77,36.42 79.65,35.43C80.42,34.54 82.96,33.78 82.96,33.78L80.57,33.33C80.38,33.29 80.18,32.97 80.18,32.97C80.16,32.9 80.15,32.81 80.17,32.73C80.18,32.65 80.21,32.58 80.26,32.51C80.31,32.45 80.37,32.39 80.45,32.35C80.52,32.32 80.6,32.3 80.68,32.3C82.14,32.26 83.51,32.06 85.06,32.42C85.83,32.6 85.9,33.75 85.47,34.42L82.66,38.61V38.66"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M77.97,30.22C77.32,30.98 77,31.96 77.08,32.96"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.75,30.6C76.21,31.41 76.01,32.41 76.19,33.37"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M101.24,35.48C103.13,35.48 104.67,33.94 104.67,32.05C104.67,30.16 103.13,28.63 101.24,28.63C99.35,28.63 97.82,30.16 97.82,32.05C97.82,33.94 99.35,35.48 101.24,35.48Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M93.38,35.35C95.27,35.35 96.8,33.82 96.8,31.93C96.8,30.04 95.27,28.5 93.38,28.5C91.49,28.5 89.95,30.04 89.95,31.93C89.95,33.82 91.49,35.35 93.38,35.35Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M97.82,31.87L96.8,31.81"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M92.83,28.02C93.92,27.64 95.11,27.64 96.21,28.02"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#323739"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M106.95,28.65L106.8,30.41C107.16,30.28 107.56,30.24 107.94,30.3C108.42,30.38 108.86,30.6 109.21,30.93C109.97,31.86 109.26,33.47 108.39,34.45C108.55,34.52 108.71,34.61 108.85,34.72C109.54,33.77 110.11,32.69 110.18,31.65C110.4,27.89 106.95,28.65 106.95,28.65Z"
        android:fillColor="#CC8A52"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M107.55,38.44C108.7,38.44 109.63,37.51 109.63,36.36C109.63,35.21 108.7,34.27 107.55,34.27C106.4,34.27 105.47,35.21 105.47,36.36C105.47,37.51 106.4,38.44 107.55,38.44Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.17,48.72C97.26,49.23 98.69,49.64 100.55,49.79C103.25,50 105.27,49.56 106.77,48.91"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M88.62,84.55C88.98,82.26 89.02,79.83 88.88,77.34"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M110.65,83.44C110.84,81.63 111.21,79.61 111.67,77.49"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M70.61,49.38C70.67,46.97 70.78,45.35 70.78,45.35C72.62,45.14 83.3,43.77 83.3,43.77C83.3,43.77 83.15,48.12 83.1,50.51C83.96,48.95 85.18,47.61 86.64,46.59C88.11,45.58 89.79,44.92 91.56,44.66C93.31,44.46 95.07,44.35 96.84,44.33C96.84,44.33 93.18,67.7 91.87,77.42L83.93,77.19L85.47,65.14C85.47,65.14 80.41,73.61 73.97,69.37C71.21,67.55 70.61,59.31 70.57,52.98"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M83.12,50.37L82.66,60.93"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M76.06,52.98H69.79V49.43H76.06C76.54,49.43 76.99,49.62 77.33,49.96C77.67,50.3 77.86,50.75 77.86,51.23C77.86,51.71 77.67,52.16 77.33,52.5C76.99,52.84 76.54,52.98 76.06,52.98Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M87.46,52.3C86.57,55.21 85.78,61.23 85.47,65.14"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.04,82.25C130.93,83.16 135.81,80.28 130.13,67.4C128.53,63.76 127.13,57.61 124.84,52.71M106.75,49.98L107.14,44.33C111.01,43.88 114.93,44.32 118.6,45.63C121.05,46.53 122.9,48.87 124.38,51.77M105.02,75.01L104.87,77.25L113.03,77.5L113.1,77.5M124.38,51.77C124.39,51.79 124.39,51.8 124.4,51.81M124.38,51.77L124.4,51.81M124.4,51.81C124.4,51.82 124.41,51.83 124.41,51.84C124.42,51.85 124.43,51.87 124.43,51.88M124.84,52.71C124.83,52.7 124.83,52.69 124.82,52.68L124.84,52.71Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M107.14,44.33C107.14,44.33 109.61,47.44 111.05,50.4"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M103.54,66.63L102.82,73.83C102.77,74.33 103.14,74.78 103.64,74.83L110.17,75.48M104.43,57.64L105.12,50.78C105.17,50.27 105.62,49.91 106.12,49.96L123.98,51.74C124.48,51.79 124.85,52.24 124.8,52.74L123.24,68.32"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M116.04,51.12L115.48,56.84C115.47,56.88 115.48,56.92 115.49,56.95C115.51,56.99 115.53,57.02 115.56,57.05C115.59,57.07 115.62,57.09 115.66,57.1C115.7,57.11 115.74,57.11 115.78,57.1L117.13,56.57L117.63,56.38C117.71,56.35 117.79,56.34 117.87,56.34C117.95,56.35 118.03,56.37 118.1,56.41L119.29,57.11L120,57.53C120.03,57.55 120.07,57.56 120.11,57.55C120.14,57.55 120.18,57.54 120.21,57.52C120.25,57.5 120.27,57.47 120.29,57.44C120.32,57.41 120.33,57.37 120.33,57.34L120.93,51.6C120.94,51.5 120.9,51.4 120.84,51.32C120.77,51.24 120.68,51.19 120.58,51.18L116.51,50.77C116.45,50.76 116.4,50.76 116.35,50.78C116.29,50.79 116.24,50.81 116.2,50.85C116.16,50.88 116.12,50.92 116.09,50.97C116.07,51.02 116.05,51.07 116.04,51.12Z"
        android:fillColor="#CC8A52"/>
    <path
        android:pathData="M107.68,68.87C107.72,68.88 107.76,68.88 107.81,68.87C109.28,69.78 110.71,70.74 112.11,71.75"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.93,64.02C111.77,62.16 111.01,60.41 109.76,59.02C109.24,58.29 108.64,57.61 107.96,57C107.41,56.55 105.97,56.52 105.41,56.53C105.38,56.54 105.35,56.54 105.32,56.56C105.29,56.57 105.26,56.59 105.24,56.62C105.22,56.64 105.2,56.67 105.19,56.7C105.18,56.74 105.18,56.77 105.18,56.8C105.27,57.52 106.11,57.92 106.75,58.22C106.9,58.49 107.02,58.78 107.09,59.08L105.89,58.2C104.7,57.35 103.46,57.55 103.32,58.43L102.51,63.21C102.38,63.98 102.49,64.77 102.8,65.48C103.12,66.19 103.65,66.79 104.31,67.21C104.31,67.21 106.98,68.66 107.67,68.88"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M111.93,64.03L114.55,65.5L115.13,65.81"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M112.47,63.47L114.5,65.03L110.2,70.6C110.13,70.68 110.04,70.74 109.93,70.75C109.82,70.76 109.71,70.73 109.63,70.67L108.25,69.6C108.16,69.54 108.11,69.44 108.09,69.33C108.08,69.23 108.11,69.12 108.18,69.03L112.47,63.47Z"
        android:fillColor="#B89374"/>
    <path
        android:pathData="M114.79,64.45L112.85,62.94C112.58,62.74 112.19,62.79 111.99,63.05L109.67,66.05C109.46,66.31 109.51,66.7 109.78,66.91L111.72,68.41C111.99,68.62 112.38,68.57 112.58,68.3L114.9,65.31C115.11,65.04 115.06,64.66 114.79,64.45Z"
        android:fillColor="#595050"/>
    <path
        android:pathData="M107.13,59.08L108.26,59.88"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#808080"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M96.84,44.33C96.84,44.33 93.25,47.74 91.45,50.72C89.65,53.7 89.46,55.1 89.46,55.1L93.65,65.12L96.84,44.33Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M125.26,82.28C125.26,82.28 123.14,81.96 118.43,80.06C117.97,79.86 117.51,79.66 117.06,79.45M128.49,70.55L115.53,65.09L110.05,75.8C111.27,76.51 112.52,77.2 113.78,77.85"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M119.14,74.9L116.46,80.56L113.25,79.04L115.94,73.38C116.15,72.95 116.51,72.62 116.96,72.46C117.41,72.3 117.9,72.33 118.34,72.53C118.77,72.74 119.1,73.1 119.26,73.55C119.42,74 119.39,74.49 119.19,74.92L119.14,74.9Z"
        android:strokeLineJoin="round"
        android:strokeWidth="0.4"
        android:fillColor="#00000000"
        android:strokeColor="#848788"
        android:strokeLineCap="round"/>
    <path
        android:pathData="M84.86,104.18H115.48C115.31,98.2 114.55,90.23 110.65,83.47H88.83C88.83,83.47 86.47,90.81 84.86,104.18Z"
        android:fillColor="#B89374"/>
  </group>
</vector>
