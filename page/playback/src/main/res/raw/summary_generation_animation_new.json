{"v": "5.9.6", "fr": 60, "ip": 0, "op": 271, "w": 72, "h": 72, "nm": "录音摘要生成中图标", "ddd": 0, "assets": [{"id": "image_0", "w": 140, "h": 140, "u": "", "p": "data:image/jpeg;base64,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", "e": 1}, {"id": "image_1", "w": 90, "h": 90, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "WhiteLine", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [36, 35.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -0.331], [0, 0], [-0.331, 0], [0, 0], [0, 0.331], [0, 0], [0.331, 0]], "o": [[-0.331, 0], [0, 0], [0, 0.331], [0, 0], [0.331, 0], [0, 0], [0, -0.331], [0, 0]], "v": [[-4.4, -7], [-5, -6.4], [-5, -5.6], [-4.4, -5], [4.4, -5], [5, -5.6], [5, -6.4], [4.4, -7]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.281, 0.91], [0, 0], [0.114, -0.371], [0, 0], [0.91, -0.281], [0, 0], [-0.371, -0.114], [0, 0], [-0.281, -0.91], [0, 0], [-0.114, 0.371], [0, 0], [-0.91, 0.281], [0, 0], [0.371, 0.114], [0, 0]], "o": [[0, 0], [-0.114, -0.371], [0, 0], [-0.281, 0.91], [0, 0], [-0.371, 0.114], [0, 0], [0.91, 0.281], [0, 0], [0.114, 0.371], [0, 0], [0.281, -0.91], [0, 0], [0.371, -0.114], [0, 0], [-0.91, -0.281]], "v": [[-1.519, 1.617], [-1.623, 1.278], [-2.377, 1.278], [-2.481, 1.617], [-4.383, 3.519], [-4.722, 3.623], [-4.722, 4.377], [-4.383, 4.481], [-2.481, 6.383], [-2.377, 6.722], [-1.623, 6.722], [-1.519, 6.383], [0.383, 4.481], [0.722, 4.377], [0.722, 3.623], [0.383, 3.519]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, -0.331], [0, 0], [0.331, 0], [0, 0], [0, 0.331], [0, 0], [-0.331, 0]], "o": [[0.331, 0], [0, 0], [0, 0.331], [0, 0], [-0.331, 0], [0, 0], [0, -0.331], [0, 0]], "v": [[1.4, -3], [2, -2.4], [2, -1.6], [1.4, -1], [-4.4, -1], [-5, -1.6], [-5, -2.4], [-4.4, -3]], "c": true}, "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Union", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Subtract", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [36, 36, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [109, 109, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.168, -0.329], [0, -0.979], [0, 0], [-0.19, -0.374], [-0.329, -0.168], [-0.979, 0], [0, 0], [0, 0], [0, 1.705], [0, 0], [0, 0], [0.022, 0.195], [1.599, 0.182], [0.464, 0], [0, 0], [0, 0], [0.374, -0.19]], "o": [[-0.19, 0.374], [0, 0], [0, 0.979], [0.168, 0.329], [0.374, 0.19], [0, 0], [0, 0], [1.705, 0], [0, 0], [0, 0], [0, -0.469], [-0.182, -1.599], [-0.195, -0.022], [0, 0], [0, 0], [-0.979, 0], [-0.329, 0.168]], "v": [[-7.143, -8.212], [-7.333, -6.371], [-7.333, 6.371], [-7.143, 8.212], [-6.379, 8.976], [-4.537, 9.167], [4.246, 9.167], [4.246, 9.167], [7.333, 6.079], [7.333, -5.203], [7.333, -5.203], [7.311, -6.101], [4.268, -9.144], [3.377, -9.167], [3.377, -9.167], [-4.537, -9.167], [-6.379, -8.976]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "rd", "nm": "圆角 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 2, "mn": "ADBE Vector Filter - RC", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [300, 300], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Subtract", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 2, "nm": "渐变图.png", "cl": "png", "tt": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 270, "s": [360]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [36, 36, 0], "to": [4.608, 0.095, 0], "ti": [-2.85, -2.83, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67.5, "s": [26.832, 55.594, 0], "to": [2.85, 2.83, 0], "ti": [0.089, -3.181, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136.125, "s": [53.1, 52.981, 0], "to": [-0.142, 5.083, 0], "ti": [4.512, 4.136, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202.5, "s": [45.5, 43.22, 0], "to": [-4.191, -3.842, 0], "ti": [-4.077, -0.139, 0]}, {"t": 270, "s": [36, 36, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [70, 70, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [95, 95, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 2, "nm": "通话操作渐变图Mask.png", "cl": "png", "td": 1, "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [36, 36, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [45, 45, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [75, 75, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 2, "nm": "渐变图.png", "cl": "png", "tt": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 270, "s": [360]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [36, 36, 0], "to": [4.608, 0.095, 0], "ti": [-2.85, -2.83, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67.5, "s": [26.832, 55.594, 0], "to": [2.85, 2.83, 0], "ti": [0.089, -3.181, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136.125, "s": [53.1, 52.981, 0], "to": [-0.142, 5.083, 0], "ti": [4.512, 4.136, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202.5, "s": [45.5, 43.22, 0], "to": [-4.191, -3.842, 0], "ti": [-4.077, -0.139, 0]}, {"t": 270, "s": [36, 36, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [70, 70, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [95, 95, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "通话操作渐变图Mask.png", "cl": "png", "td": 1, "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [36, 36, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [45, 45, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [75, 75, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 2, "nm": "渐变图.png", "cl": "png", "tt": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 270, "s": [360]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [36, 36, 0], "to": [4.608, 0.095, 0], "ti": [-2.85, -2.83, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67.5, "s": [26.832, 55.594, 0], "to": [2.85, 2.83, 0], "ti": [0.089, -3.181, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136.125, "s": [53.1, 52.981, 0], "to": [-0.142, 5.083, 0], "ti": [4.512, 4.136, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202.5, "s": [45.5, 43.22, 0], "to": [-4.191, -3.842, 0], "ti": [-4.077, -0.139, 0]}, {"t": 270, "s": [36, 36, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [70, 70, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [95, 95, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 2, "nm": "通话操作渐变图Mask.png", "cl": "png", "td": 1, "refId": "image_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [36, 36, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [45, 45, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [75, 75, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 2, "nm": "渐变图.png", "cl": "png", "tt": 1, "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 270, "s": [360]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [36, 36, 0], "to": [4.608, 0.095, 0], "ti": [-2.85, -2.83, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 67.5, "s": [26.832, 55.594, 0], "to": [2.85, 2.83, 0], "ti": [0.089, -3.181, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 136.125, "s": [53.1, 52.981, 0], "to": [-0.142, 5.083, 0], "ti": [4.512, 4.136, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 202.5, "s": [45.5, 43.22, 0], "to": [-4.191, -3.842, 0], "ti": [-4.077, -0.139, 0]}, {"t": 270, "s": [36, 36, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [70, 70, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [95, 95, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}], "markers": []}