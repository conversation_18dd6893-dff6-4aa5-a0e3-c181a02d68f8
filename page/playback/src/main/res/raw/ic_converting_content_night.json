{"v": "5.12.1", "fr": 60, "ip": 0, "op": 271, "w": 680, "h": 586, "nm": "录音转写 1080x1290", "ddd": 0, "assets": [{"id": "comp_0", "nm": "录音转写", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "气泡 暗色", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [432, 353, 0]}, "a": {"a": 0, "k": [432, 353, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "w": 1000, "h": 1000, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "星4", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [269.309, 184.929, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 17, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 47, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 77, "s": [50, 50, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 107, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 137, "s": [50, 50, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 167, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 197, "s": [50, 50, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 227, "s": [100, 100, 100]}, {"t": 260, "s": [0, 0, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.171, 0.548], [0, 0], [0.083, 0.062], [0.104, 0], [0.083, -0.062], [0.03, -0.099], [0, 0], [0.402, -0.409], [0.547, -0.175], [0, 0], [0.055, -0.08], [0, -0.098], [-0.055, -0.08], [-0.091, -0.035], [0, 0], [-0.406, -0.406], [-0.17, -0.548], [0, 0], [-0.08, -0.055], [-0.098, 0], [-0.08, 0.055], [-0.035, 0.091], [0, 0], [-0.406, 0.406], [-0.549, 0.169], [0, 0], [-0.062, 0.083], [0, 0.104], [0.062, 0.083], [0.099, 0.03], [0, 0], [0.406, 0.406]], "o": [[0, 0], [-0.03, -0.099], [-0.083, -0.062], [-0.104, 0], [-0.083, 0.062], [0, 0], [-0.165, 0.55], [-0.402, 0.409], [0, 0], [-0.091, 0.035], [-0.055, 0.08], [0, 0.098], [0.055, 0.08], [0, 0], [0.549, 0.169], [0.406, 0.406], [0, 0], [0.035, 0.091], [0.08, 0.055], [0.098, 0], [0.08, -0.055], [0, 0], [0.17, -0.548], [0.406, -0.406], [0, 0], [0.099, -0.03], [0.062, -0.083], [0, -0.104], [-0.062, -0.083], [0, 0], [-0.548, -0.17], [-0.406, -0.406]], "v": [[0.56, -2.899], [0.434, -3.313], [0.26, -3.561], [-0.028, -3.657], [-0.315, -3.561], [-0.489, -3.313], [-0.615, -2.899], [-1.48, -1.438], [-2.925, -0.548], [-3.339, -0.422], [-3.565, -0.246], [-3.65, 0.028], [-3.565, 0.301], [-3.339, 0.478], [-2.925, 0.604], [-1.471, 1.479], [-0.593, 2.932], [-0.467, 3.346], [-0.29, 3.571], [-0.017, 3.657], [0.257, 3.571], [0.434, 3.346], [0.56, 2.932], [1.438, 1.479], [2.892, 0.604], [3.306, 0.478], [3.554, 0.304], [3.65, 0.017], [3.554, -0.271], [3.306, -0.445], [2.892, -0.571], [1.438, -1.447]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.86274510622, 0.89411765337, 0.949019610882, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 17, "op": 497, "st": 17, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "星3", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [238.413, 367.878, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 42, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 72, "s": [50, 50, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 102, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 132, "s": [50, 50, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 162, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 194, "s": [50, 50, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 224, "s": [100, 100, 100]}, {"t": 266, "s": [0, 0, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.122, 0.39], [0, 0], [0.059, 0.045], [0.074, 0], [0.059, -0.045], [0.021, -0.071], [0, 0], [0.289, -0.289], [0.391, -0.12], [0, 0], [0.045, -0.059], [0, -0.074], [-0.045, -0.059], [-0.071, -0.021], [0, 0], [-0.289, -0.289], [-0.122, -0.39], [0, 0], [-0.059, -0.045], [-0.074, 0], [-0.059, 0.045], [-0.021, 0.071], [0, 0], [-0.289, 0.289], [-0.391, 0.12], [0, 0], [-0.045, 0.059], [0, 0.074], [0.045, 0.059], [0.071, 0.021], [0, 0], [0.289, 0.289]], "o": [[0, 0], [-0.021, -0.071], [-0.059, -0.045], [-0.074, 0], [-0.059, 0.045], [0, 0], [-0.122, 0.39], [-0.289, 0.289], [0, 0], [-0.071, 0.021], [-0.045, 0.059], [0, 0.074], [0.045, 0.059], [0, 0], [0.391, 0.12], [0.289, 0.289], [0, 0], [0.021, 0.071], [0.059, 0.045], [0.074, 0], [0.059, -0.045], [0, 0], [0.122, -0.39], [0.289, -0.289], [0, 0], [0.071, -0.021], [0.045, -0.059], [0, -0.074], [-0.045, -0.059], [0, 0], [-0.391, -0.12], [-0.289, -0.289]], "v": [[0.419, -2.076], [0.329, -2.373], [0.206, -2.551], [0, -2.62], [-0.206, -2.551], [-0.329, -2.373], [-0.419, -2.076], [-1.045, -1.042], [-2.08, -0.419], [-2.373, -0.329], [-2.551, -0.206], [-2.62, 0], [-2.551, 0.206], [-2.373, 0.329], [-2.08, 0.419], [-1.045, 1.042], [-0.419, 2.076], [-0.329, 2.373], [-0.206, 2.551], [0, 2.62], [0.206, 2.551], [0.329, 2.373], [0.419, 2.076], [1.045, 1.042], [2.08, 0.419], [2.373, 0.329], [2.551, 0.206], [2.62, 0], [2.551, -0.206], [2.373, -0.329], [2.08, -0.419], [1.045, -1.042]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 12, "op": 492, "st": 12, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "星2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [475.672, 251.174, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 8, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 38, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 68, "s": [50, 50, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 98, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 128, "s": [50, 50, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 158, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 187, "s": [50, 50, 100]}, {"i": {"x": [0.622, 0.622, 0.622], "y": [1.16, 1.16, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 217, "s": [100, 100, 100]}, {"t": 253, "s": [0, 0, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.16, 0.515], [0, 0], [0.08, 0.064], [0.102, 0], [0.08, -0.064], [0.023, -0.099], [0, 0], [0.38, -0.381], [0.514, -0.16], [0, 0], [0.06, -0.079], [0, -0.099], [-0.06, -0.079], [-0.095, -0.026], [0, 0], [-0.382, -0.381], [-0.158, -0.515], [0, 0], [-0.079, -0.06], [-0.099, 0], [-0.079, 0.06], [-0.026, 0.095], [0, 0], [-0.382, 0.381], [-0.516, 0.157], [0, 0], [-0.06, 0.079], [0, 0.099], [0.06, 0.079], [0.095, 0.026], [0, 0], [0.382, 0.381]], "o": [[0, 0], [-0.023, -0.099], [-0.08, -0.064], [-0.102, 0], [-0.08, 0.064], [0, 0], [-0.158, 0.515], [-0.38, 0.381], [0, 0], [-0.095, 0.026], [-0.06, 0.079], [0, 0.099], [0.06, 0.079], [0, 0], [0.516, 0.157], [0.382, 0.381], [0, 0], [0.026, 0.095], [0.079, 0.06], [0.099, 0], [0.079, -0.06], [0, 0], [0.16, -0.515], [0.382, -0.381], [0, 0], [0.095, -0.026], [0.06, -0.079], [0, -0.099], [-0.06, -0.079], [0, 0], [-0.516, -0.159], [-0.382, -0.381]], "v": [[0.552, -2.733], [0.434, -3.12], [0.276, -3.372], [-0.005, -3.47], [-0.285, -3.372], [-0.443, -3.12], [-0.565, -2.733], [-1.383, -1.369], [-2.744, -0.545], [-3.131, -0.428], [-3.371, -0.267], [-3.464, 0.007], [-3.371, 0.28], [-3.131, 0.441], [-2.744, 0.563], [-1.378, 1.382], [-0.556, 2.747], [-0.434, 3.138], [-0.273, 3.378], [0, 3.47], [0.273, 3.378], [0.434, 3.138], [0.552, 2.747], [1.377, 1.382], [2.744, 0.563], [3.131, 0.441], [3.371, 0.28], [3.464, 0.007], [3.371, -0.267], [3.131, -0.428], [2.744, -0.545], [1.377, -1.368]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 8, "op": 488, "st": 8, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "星1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [282.217, 406.342, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 3, "s": [0, 0, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 33, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 63, "s": [50, 50, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 93, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 123, "s": [50, 50, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 153, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 184, "s": [50, 50, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 214, "s": [100, 100, 100]}, {"t": 242, "s": [0, 0, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.213, 0.69], [0, 0], [0.104, 0.076], [0.129, 0], [0.104, -0.076], [0.039, -0.122], [0, 0], [0.511, -0.511], [0.691, -0.213], [0, 0], [0.077, -0.105], [0, -0.13], [-0.077, -0.105], [-0.124, -0.038], [0, 0], [-0.511, -0.511], [-0.213, -0.691], [0, 0], [-0.105, -0.077], [-0.13, 0], [-0.105, 0.077], [-0.038, 0.124], [0, 0], [-0.511, 0.511], [-0.69, 0.213], [0, 0], [-0.077, 0.105], [0, 0.13], [0.077, 0.105], [0.124, 0.038], [0, 0], [0.511, 0.511]], "o": [[0, 0], [-0.039, -0.122], [-0.104, -0.076], [-0.129, 0], [-0.104, 0.076], [0, 0], [-0.213, 0.691], [-0.511, 0.511], [0, 0], [-0.124, 0.038], [-0.077, 0.105], [0, 0.13], [0.077, 0.105], [0, 0], [0.691, 0.213], [0.511, 0.511], [0, 0], [0.038, 0.124], [0.105, 0.077], [0.13, 0], [0.105, -0.077], [0, 0], [0.213, -0.69], [0.511, -0.511], [0, 0], [0.124, -0.038], [0.077, -0.105], [0, -0.13], [-0.077, -0.105], [0, 0], [-0.69, -0.213], [-0.511, -0.511]], "v": [[0.736, -3.673], [0.579, -4.191], [0.358, -4.496], [0, -4.612], [-0.358, -4.496], [-0.579, -4.191], [-0.741, -3.673], [-1.842, -1.844], [-3.672, -0.742], [-4.19, -0.584], [-4.499, -0.365], [-4.618, -0.004], [-4.499, 0.357], [-4.19, 0.577], [-3.672, 0.735], [-1.842, 1.836], [-0.741, 3.666], [-0.583, 4.184], [-0.363, 4.494], [-0.002, 4.612], [0.359, 4.494], [0.579, 4.184], [0.736, 3.666], [1.839, 1.837], [3.667, 0.735], [4.19, 0.577], [4.499, 0.357], [4.618, -0.004], [4.499, -0.365], [4.19, -0.584], [3.667, -0.742], [1.839, -1.844]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.86274510622, 0.89411765337, 0.949019610882, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 3, "op": 483, "st": 3, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "右腿线", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [4.605, 110.852, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.357, "y": 1}, "o": {"x": 0.213, "y": 0}, "t": 7, "s": [{"i": [[0, 0], [0, 0], [-3.989, -0.846], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.785, 5.929], [-7.742, -1.347], [-2.123, -5.669], [9.993, -4.404]], "c": false}]}, {"i": {"x": 0.383, "y": 1}, "o": {"x": 0.248, "y": 0}, "t": 62, "s": [{"i": [[0, 0], [0, 0], [-3.989, -0.846], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-8.743, 6.929], [-7.325, -0.222], [-2.081, -5.169], [9.993, -4.404]], "c": false}]}, {"t": 163, "s": [{"i": [[0, 0], [0, 0], [-3.989, -0.846], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-9.785, 5.929], [-7.742, -1.347], [-2.123, -5.669], [9.993, -4.404]], "c": false}]}]}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "右腿膝盖", "parent": 9, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.321], "y": [1]}, "o": {"x": [0.256], "y": [0]}, "t": 7, "s": [0]}, {"i": {"x": [0.333], "y": [1]}, "o": {"x": [0.3], "y": [0]}, "t": 62, "s": [-8]}, {"t": 163, "s": [0]}]}, "p": {"a": 0, "k": [-9.625, 109.375, 0]}, "a": {"a": 0, "k": [13.375, 127.375, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [55.75, 55.75]}, "p": {"a": 0, "k": [0, 0]}, "nm": "椭圆路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.364705890417, 0.47058826685, 0.639215707779, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.125, 123.875]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "椭圆 1", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "右大腿", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [588.8, 614, 0]}, "a": {"a": 0, "k": [94, 114, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[6.25, 0.5], [3, -31.25], [0, 0], [0, 0], [-8.25, 24.5], [0, 0], [0, 0]], "o": [[-6.25, -0.5], [-3, 31.25], [0, 0], [0, 0], [8.177, -24.283], [0, 0], [0, 0]], "v": [[0.5, 77.5], [-33.75, 103.5], [-5, 139.25], [70.5, 140.5], [106, 116.25], [98.5, 89.25], [58.75, 84.25]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.364705890417, 0.47058826685, 0.639215707779, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "形状 1", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "右小腿", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [14, 123, 0]}, "a": {"a": 0, "k": [-9, 105, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[27.5, 9.5], [0, 0], [5.25, -20.5], [-17.25, -0.25], [0, 0], [-7.25, 23]], "o": [[-25.512, -8.813], [0, 0], [2, -5], [18, -0.75], [0, 0], [7.25, -23]], "v": [[-3, 82], [-41.25, 102], [-88, 257], [-60.25, 249], [-23, 258.75], [15.75, 141.5]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.364705890417, 0.47058826685, 0.639215707779, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "形状 1", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "右拖鞋", "parent": 12, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-14.875, 59.16, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.832, 0.203], [2.485, -1.999], [-0.783, -0.018], [0, 0], [-0.043, 0.019], [-0.032, 0.034], [-0.016, 0.044], [0.002, 0.047], [0.064, 0.062], [0.089, 0], [0, 0]], "o": [[-2.058, -0.149], [-0.72, 0.558], [0, 0], [0.047, 0], [0.043, -0.019], [0.032, -0.034], [0.016, -0.044], [-0.002, -0.089], [-0.064, -0.062], [0, 0], [0, 0]], "v": [[-0.976, -1.933], [-6.005, 0.318], [-5.663, 1.961], [6.075, 1.961], [6.212, 1.933], [6.326, 1.853], [6.4, 1.735], [6.421, 1.597], [6.318, 1.36], [6.079, 1.263], [2.302, 1.178]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.364705890417, 0.470588237047, 0.639215707779, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "右脚", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.3], "y": [0]}, "t": 7, "s": [0]}, {"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.3], "y": [0]}, "t": 62, "s": [2.4]}, {"t": 163, "s": [0]}]}, "p": {"a": 0, "k": [-39.339, 183.779, 0]}, "a": {"a": 0, "k": [25, -53, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.756, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.18, 0.734], [0, 0], [0, 0]], "v": [[-2.294, 5.869], [2.208, -11.321], [7.625, -9.561], [2.281, 6.756], [2.978, 9.394], [2.398, 11.321], [-7.625, 11.168]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "左腿线", "parent": 16, "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-42.954, 37.612, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.355, -7.821], [-1.355, 7.821]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "左膝盖", "parent": 17, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.635], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [45]}, {"i": {"x": [0.418], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 75, "s": [61.3]}, {"t": 161, "s": [45]}]}, "p": {"a": 0, "k": [-53.25, -25.5, 0]}, "a": {"a": 0, "k": [-90, -55, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [64, 64]}, "p": {"a": 0, "k": [0, 0]}, "nm": "椭圆路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.364705890417, 0.47058826685, 0.639215707779, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-90, -55]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "椭圆 1", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "左小腿", "parent": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-91.25, -66, 0]}, "a": {"a": 0, "k": [-54.5, -36.5, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[30.25, -2], [5.25, -52.25], [0, 0], [-22.431, -5.776], [0, 0], [0, 0]], "o": [[-4, 2.5], [-11.5, 54.5], [0, 0], [20.875, 5.375], [0, 0], [0, 0]], "v": [[-58.5, -56], [-91, -6], [-113.375, 105.875], [-81, 99], [-55.875, 112.5], [-33, -18.75]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.364705890417, 0.47058826685, 0.639215707779, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "形状 1", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "左大腿", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2, "s": [-67]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 75, "s": [-72.4]}, {"t": 161, "s": [-67]}]}, "p": {"a": 0, "k": [530.5, 598.7, 0]}, "a": {"a": 0, "k": [3.5, 106, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-9, -53], [-22, -20], [0, 0], [26, 44.5]], "o": [[0, 0], [2.546, 14.992], [23.612, 21.465], [0, 0], [-14.112, -24.153]], "v": [[-73.5, -51], [-78, 17], [-31, 107], [25, 84.5], [-27.5, -44]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.364705890417, 0.47058826685, 0.639215707779, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "形状 1", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 19, "ty": 4, "nm": "左袖线", "parent": 20, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [14.36, 16.566, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[2.25, -63.5], [5.25, 7]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.1}, "lc": 2, "lj": 1, "ml": 1, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "形状 1", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 20, "ty": 4, "nm": "左袖", "parent": 22, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-1.26, 23.102, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.199, 0.584], [0, 0], [0, 0], [-0.221, -0.9], [-2.571, 0], [-0.502, 0.359]], "o": [[0, 0], [0, 0], [0, 0], [0.261, 1.049], [0.617, 0.001], [0.502, -0.359]], "v": [[3.429, 5.896], [2.726, -7.895], [-3.429, -7.125], [-2.852, 5.446], [0.633, 7.895], [2.352, 7.345]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.596078455448, 0.850980401039, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 21, "ty": 0, "nm": "左手", "parent": 22, "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.55], "y": [1]}, "o": {"x": [0.643], "y": [0]}, "t": 48, "s": [0]}, {"i": {"x": [0.502], "y": [1]}, "o": {"x": [0.453], "y": [0]}, "t": 136, "s": [25]}, {"t": 207, "s": [0]}]}, "p": {"a": 0, "k": [-2.899, -35.833, 0]}, "a": {"a": 0, "k": [484, 424.5, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "w": 1000, "h": 1000, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 22, "ty": 4, "nm": "左小臂", "parent": 34, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.55], "y": [1]}, "o": {"x": [0.321], "y": [-0.063]}, "t": 54, "s": [-4.299]}, {"i": {"x": [0.528], "y": [0.926]}, "o": {"x": [0.461], "y": [0]}, "t": 142, "s": [-19.5]}, {"t": 213, "s": [-4.299]}]}, "p": {"a": 0, "k": [-17.863, 44.494, 0]}, "a": {"a": 0, "k": [3.25, 54.25, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.011, -0.412], [0, 0], [-0.011, -0.138], [-0.521, -0.408], [-0.657, 0.08], [-0.408, 0.521], [0.08, 0.657], [0, 0], [0.311, 0.27], [0.412, -0.015], [0.29, -0.293]], "o": [[0, 0], [-0.011, 0.138], [0.08, 0.657], [0.521, 0.408], [0.657, -0.08], [0.408, -0.521], [0, 0], [-0.042, -0.41], [-0.311, -0.27], [-0.412, 0.015], [-0.29, 0.293]], "v": [[-2.031, -5.693], [-2.481, 4.663], [-2.481, 5.077], [-1.543, 6.74], [0.297, 7.252], [1.959, 6.313], [2.471, 4.474], [1.202, -5.814], [0.653, -6.873], [-0.472, -7.269], [-1.563, -6.789]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.800000011921, 0.78823530674, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 23, "ty": 4, "nm": "手机", "parent": 21, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [481.473, 385.21, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.048, 0.001], [0, 0], [0.089, 0.062], [0.034, 0.103], [0, 0], [-0.008, 0.048], [-0.029, 0.039], [-0.043, 0.022], [-0.048, 0], [0, 0], [-0.089, -0.064], [-0.033, -0.105], [0, 0], [0.008, -0.048], [0.029, -0.039], [0.043, -0.021]], "o": [[0, 0], [-0.109, 0.005], [-0.089, -0.062], [0, 0], [-0.014, -0.046], [0.008, -0.048], [0.029, -0.039], [0.043, -0.022], [0, 0], [0.11, -0.002], [0.089, 0.064], [0, 0], [0.015, 0.046], [-0.008, 0.048], [-0.029, 0.039], [-0.043, 0.021]], "v": [[3.304, 4.288], [-0.955, 4.523], [-1.26, 4.434], [-1.451, 4.18], [-3.598, -3.888], [-3.607, -4.031], [-3.551, -4.163], [-3.441, -4.256], [-3.301, -4.289], [0.958, -4.523], [1.265, -4.427], [1.453, -4.167], [3.597, 3.892], [3.608, 4.034], [3.553, 4.166], [3.443, 4.258]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 24, "ty": 4, "nm": "补肩", "parent": 36, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-20.147, -169.654, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-1.657, -0.855], [1.878, -1.211], [1.391, 1.211], [-1.878, 1.072]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.596078455448, 0.850980401039, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 25, "ty": 4, "nm": "拉链", "parent": 36, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-8.366, -99.874, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-0.983, 4.809], [0, 0], [0, 0]], "o": [[-0.043, -4.908], [1.189, -6.069], [0, 0], [0, 0]], "v": [[-1.153, 13.134], [0.261, -1.481], [1.112, -10.252], [1.112, -13.134]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.86274510622, 0.89411765337, 0.949019610882, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.45}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 26, "ty": 4, "nm": "领子前", "parent": 36, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [11.284, -172.591, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [-1.801, 0.104], [0, 0], [0, 0], [4.336, -0.45]], "o": [[0, 0], [0, 0], [1.801, -0.104], [0, 0], [0, 0], [-2.062, 0.221]], "v": [[-4.275, -3.018], [-3.586, 0.134], [-0.894, 3.506], [4.275, 0.31], [3.762, -3.509], [-1.907, -1.037]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.596078455448, 0.850980401039, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 27, "ty": 0, "nm": "头", "parent": 31, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 55, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 129, "s": [4.9]}, {"t": 199, "s": [0]}]}, "p": {"a": 0, "k": [7.167, -4.027, 0]}, "a": {"a": 0, "k": [572.5, 394, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "w": 1000, "h": 1000, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 28, "ty": 4, "nm": "线左", "parent": 36, "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-36.72, -123.471, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [{"i": [[0, 0], [-2.512, 2.733]], "o": [[0.005, -2.211], [0, 0]], "v": [[-1.335, 8.179], [1.335, -8.179]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 113, "s": [{"i": [[0, 0], [-1.566, 3.461]], "o": [[0.005, -2.211], [0, 0]], "v": [[-1.25, 4.179], [1.335, -8.179]], "c": false}]}, {"t": 213, "s": [{"i": [[0, 0], [-2.512, 2.733]], "o": [[0.005, -2.211], [0, 0]], "v": [[-1.335, 8.179], [1.335, -8.179]], "c": false}]}]}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 29, "ty": 4, "nm": "蒙版", "parent": 31, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0.004, -0.001, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.162, -1.472], [1.631, 0.149], [0.162, 1.472], [-1.631, -0.149]], "o": [[-1.631, 0.149], [0.162, -1.472], [1.631, -0.149], [-0.162, 1.472]], "v": [[2.452, 2.215], [-2.452, 2.215], [-2.452, -2.215], [2.452, -2.215]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "蒙版", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 30, "ty": 4, "nm": "脖子阴影", "parent": 31, "tt": 1, "tp": 29, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-10.685, -11.753, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-1.86, 0], [0, 2.002], [1.86, 0], [0, -2.002]], "o": [[1.86, 0], [0, -2.002], [-1.86, 0], [0, 2.002]], "v": [[0, 3.624], [3.368, 0], [0, -3.624], [-3.368, 0]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.960784316063, 0.607843160629, 0.584313750267, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "脖子阴影", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 31, "ty": 4, "nm": "脖子", "parent": 36, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 7, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 100, "s": [-1.2]}, {"t": 181, "s": [0]}]}, "p": {"a": 0, "k": [11.649, -177.739, 0]}, "a": {"a": 0, "k": [-0.5, 10.5, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.162, -1.472], [1.631, 0.149], [0.162, 1.472], [-1.631, -0.149]], "o": [[-1.631, 0.149], [0.162, -1.472], [1.631, -0.149], [-0.162, 1.472]], "v": [[2.452, 2.215], [-2.452, 2.215], [-2.452, -2.215], [2.452, -2.215]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.800000011921, 0.78823530674, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 32, "ty": 4, "nm": "肩膀线", "parent": 36, "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [66.138, -154.579, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 36, "s": [{"i": [[0, 0], [-0.315, 1.896]], "o": [[-1.162, -2.175], [0, 0]], "v": [[0.442, 3.602], [-0.193, -3.602]], "c": false}]}, {"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 118, "s": [{"i": [[0, 0], [-0.315, 1.896]], "o": [[-0.932, -2.431], [0, 0]], "v": [[0.593, 3.2], [-0.193, -3.602]], "c": false}]}, {"t": 236, "s": [{"i": [[0, 0], [-0.315, 1.896]], "o": [[-1.162, -2.175], [0, 0]], "v": [[0.442, 3.602], [-0.193, -3.602]], "c": false}]}]}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 33, "ty": 4, "nm": "身体", "parent": 36, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [10.923, -97.803, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0.077, 7.271], [-5.452, -0.459], [0.027, -4.502], [0.644, -4.021], [-0.239, -3.219], [1.909, 1.684]], "o": [[0.612, -6.276], [-0.072, -6.943], [5.588, 0.468], [-0.014, 2.99], [-0.482, 3.003], [0.248, 3.336], [-1.198, -1.054]], "v": [[-9.78, 10.146], [-9.222, -4.65], [0.864, -13.366], [9.927, -7.675], [7.897, 7.894], [6.834, 11.294], [-9.177, 11.771]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.596078455448, 0.850980401039, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 34, "ty": 4, "nm": "左大臂", "parent": 33, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.55], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 6, "s": [0]}, {"i": {"x": [0.502], "y": [1]}, "o": {"x": [0.453], "y": [0]}, "t": 113, "s": [10]}, {"t": 213, "s": [0]}]}, "p": {"a": 0, "k": [-44.095, -58.125, 0]}, "a": {"a": 0, "k": [12, -39.75, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-0.027, 0.081], [1.896, 0.315], [0.648, -1.801], [0.396, -1.229], [-0.332, -0.672], [-0.704, -0.257], [-0.687, 0.3], [-0.29, 0.691], [0, 0], [-0.45, 1.081]], "o": [[0, 0], [0, 0], [0.036, -0.081], [0.662, -1.81], [-3.426, -0.567], [0, 0], [-0.223, 0.716], [0.332, 0.672], [0.704, 0.257], [0.687, -0.3], [0, 0], [0.396, -1.018], [0, 0]], "v": [[2.559, 1.601], [5.495, -4.919], [5.54, -4.964], [5.639, -5.207], [3.599, -9.813], [-1.804, -6.486], [-5.735, 6.121], [-5.565, 8.283], [-3.95, 9.731], [-1.783, 9.664], [-0.26, 8.12], [-0.237, 8.071], [1.114, 4.865]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.596078455448, 0.850980401039, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 35, "ty": 3, "nm": "空 1", "sr": 1, "ks": {"o": {"a": 0, "k": 0}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.644, "y": 0}, "t": 0, "s": [553.8, 594, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 118, "s": [559, 594, 0]}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 36, "ty": 4, "nm": "屁股", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.498], "y": [1]}, "o": {"x": [0.64], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.685], "y": [1]}, "o": {"x": [0.369], "y": [0]}, "t": 118, "s": [2.2]}, {"t": 204, "s": [0]}]}, "p": {"a": 0, "k": [547.985, 586.266, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.068, 0.566], [0, 0], [0, 0], [0, 0], [0, 0], [-0.514, 0.247], [-0.371, 0.433], [-0.165, 0.546]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.57, -0.019], [0.514, -0.247], [0.371, -0.433], [0.165, -0.546]], "v": [[9.028, -0.077], [8.421, -5.007], [-7.338, -4.529], [-9.058, 5.007], [5.08, 4.529], [6.724, 4.126], [8.067, 3.095], [8.881, 1.61]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.364705890417, 0.47058826685, 0.639215707779, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 37, "ty": 4, "nm": "补", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [494.8, 500, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [10.75, -12.75], [-7.75, 15.75]], "o": [[0, 0], [-10.75, 12.75], [7.75, -15.75]], "v": [[104.25, 73.25], [87, 83], [105, 117]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.301960796118, 0.341176480055, 0.400000035763, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "形状 1", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 38, "ty": 4, "nm": "左拖鞋 2", "parent": 43, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 34, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 86, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 96, "s": [100]}, {"t": 148, "s": [0]}]}, "r": {"a": 0, "k": 11.559}, "p": {"a": 0, "k": [-21.961, 61.907, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.832, 0.203], [2.485, -1.999], [-0.783, 0], [0, 0], [-0.044, 0.019], [-0.033, 0.035], [-0.017, 0.045], [0.002, 0.048], [0.064, 0.062], [0.089, 0.001], [0, 0]], "o": [[-2.053, -0.149], [-0.711, 0.572], [0, 0], [0.048, 0], [0.044, -0.019], [0.033, -0.035], [0.017, -0.045], [-0.005, -0.089], [-0.064, -0.062], [0, 0], [0, 0]], "v": [[-0.983, -1.933], [-6.007, 0.318], [-5.665, 1.961], [6.068, 1.961], [6.207, 1.933], [6.323, 1.852], [6.398, 1.732], [6.419, 1.592], [6.312, 1.357], [6.073, 1.259], [2.3, 1.173]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.364705890417, 0.470588237047, 0.639215707779, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": -4, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 39, "ty": 4, "nm": "左拖鞋 变色", "parent": 43, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 11.559}, "p": {"a": 0, "k": [-21.961, 61.907, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.832, 0.203], [2.485, -1.999], [-0.783, 0], [0, 0], [-0.044, 0.019], [-0.033, 0.035], [-0.017, 0.045], [0.002, 0.048], [0.064, 0.062], [0.089, 0.001], [0, 0]], "o": [[-2.053, -0.149], [-0.711, 0.572], [0, 0], [0.048, 0], [0.044, -0.019], [0.033, -0.035], [0.017, -0.045], [-0.005, -0.089], [-0.064, -0.062], [0, 0], [0, 0]], "v": [[-0.983, -1.933], [-6.007, 0.318], [-5.665, 1.961], [6.068, 1.961], [6.207, 1.933], [6.323, 1.852], [6.398, 1.732], [6.419, 1.592], [6.312, 1.357], [6.073, 1.259], [2.3, 1.173]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.270588248968, 0.352941185236, 0.486274540424, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": -11, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 41, "ty": 4, "nm": "右腿后", "parent": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-55.236, 250.565, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[4.588, 0.77], [0, 0], [0, 0], [-5.097, -0.991], [0.405, -1.202]], "o": [[-4.912, -0.824], [0, 0], [0, 0], [2.035, 0.396], [-0.405, 1.202]], "v": [[-1.186, 2.752], [-5.454, 0.739], [-4.648, -1.787], [2.182, -2.732], [5.419, 1.266]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.301960796118, 0.341176480055, 0.400000035763, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 42, "ty": 4, "nm": "左脚趾", "parent": 43, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-26.475, 63.958, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.244, 0], [0, 0], [0, -0.244], [0, 0], [-0.244, 0], [0, 0], [0, 0.244], [0, 0]], "o": [[0, 0], [-0.244, 0], [0, 0], [0, 0.244], [0, 0], [0.244, 0], [0, 0], [0, -0.244]], "v": [[3.181, -0.698], [-3.181, -0.698], [-3.622, -0.257], [-3.622, 0.257], [-3.181, 0.698], [3.181, 0.698], [3.622, 0.257], [3.622, -0.257]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 43, "ty": 4, "nm": "左脚", "parent": 16, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-70.544, 23.257, 0]}, "a": {"a": 0, "k": [16, -54, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.752, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.18, 0.729], [0, 0], [0, 0]], "v": [[-2.659, 6.193], [0.155, -11.303], [6.652, -9.057], [1.43, 7.553], [2.118, 9.674], [1.7, 11.303], [-6.652, 11.303]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 44, "ty": 4, "nm": "左腿后", "parent": 16, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-84.554, 105.095, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[5.732, 1.045], [0, 0], [-5.637, -0.828], [0, 0]], "o": [[-4.007, -0.743], [0, 0], [4.313, 0.657], [0, 0]], "v": [[-0.915, 2.481], [-4.841, 0.387], [1.35, -2.616], [4.763, 0.986]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.298039227724, 0.337254911661, 0.396078467369, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 45, "ty": 4, "nm": "衣领后", "parent": 36, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [9.84, -188.623, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.094, -0.045], [0.324, -1.801], [-3.471, -0.027], [0.239, 1.351]], "o": [[-2.094, 0.045], [0, 0], [3.593, 0.023], [-0.158, -0.891]], "v": [[0.761, -2.476], [-4.034, -0.337], [-0.41, 2.477], [4.002, -0.828]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.364705890417, 0.470588237047, 0.639215707779, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 46, "ty": 4, "nm": "后发", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [567.6, 357.779, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[2.161, 0], [0.462, 0.22], [0.598, 0.237], [0.461, 0.449], [0.254, 0.591], [0.007, 0.643], [0, 0.243], [-3.057, -0.144], [0, 0], [0, -2.954], [0.017, -0.246]], "o": [[-0.506, -0.078], [-0.643, 0.011], [-0.598, -0.237], [-0.461, -0.449], [-0.254, -0.591], [0, -0.243], [0.005, -3.053], [0, 0], [3.066, 0.113], [0.017, 0.246], [-0.554, 3.395]], "v": [[0.795, 5.447], [-0.664, 4.997], [-2.544, 4.654], [-4.148, 3.615], [-5.231, 2.04], [-5.625, 0.17], [-5.625, -0.559], [-0.159, -5.44], [-0.006, -5.44], [5.613, -0.487], [5.613, 0.251]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.61960786581, 0.364705890417, 0.345098048449, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 47, "ty": 4, "nm": "耳机后蓝", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [539.106, 371.888, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.766, 0], [0, 1.09], [0.766, 0], [0, -1.09]], "o": [[0.766, 0], [0, -1.09], [-0.766, 0], [0, 1.09]], "v": [[0, 1.974], [1.388, 0], [0, -1.974], [-1.388, 0]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.596078455448, 0.850980401039, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": -2.291}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 48, "ty": 4, "nm": "耳机后黑", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [537.131, 370.92, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.353, 0.335], [0.029, 0.485], [-0.761, 0.068], [-0.095, -1.085], [0.765, -0.063]], "o": [[-0.353, -0.335], [-0.122, -1.414], [0.761, -0.068], [0.095, 1.085], [-0.486, -0.003]], "v": [[-0.962, 1.44], [-1.556, 0.164], [0.002, -1.961], [1.556, -0.12], [0.345, 1.965]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.301960796118, 0.341176480055, 0.40000000596, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 49, "ty": 4, "nm": "耳机头戴", "parent": 27, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [564.267, 346.32, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-2.38, 0], [0, 2.38], [2.38, 0], [0, -2.38]], "o": [[2.38, 0], [0, -2.38], [-2.38, 0], [0, 2.38]], "v": [[0, 4.309], [4.309, 0], [0, -4.309], [-4.309, 0]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.596078455448, 0.850980401039, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 50, "ty": 0, "nm": "背景 暗色", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [500, 500, 0]}, "a": {"a": 0, "k": [500, 500, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "w": 1000, "h": 1000, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 52, "ty": 4, "nm": "右臂", "parent": 36, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.49], "y": [0]}, "t": 36, "s": [16]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118, "s": [0]}, {"t": 236, "s": [16]}]}, "p": {"a": 0, "k": [52.102, -174.178, 0]}, "a": {"a": 0, "k": [-38, -17.5, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 1, "k": [{"i": {"x": 0.401, "y": 1}, "o": {"x": 0.259, "y": 0}, "t": 36, "s": [{"i": [[3.147, 1.297], [1.348, -0.31], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.27, -0.935], [-2.08, 0.479], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.53, -3.374], [-9.028, -3.013], [-11.372, 0.076], [-8.991, 4.019], [15.869, 7.269], [9.77, 4.019]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 118, "s": [{"i": [[3.147, 1.297], [1.348, -0.31], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.27, -0.935], [-2.08, 0.479], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.53, -3.374], [-8.926, -3.785], [-11.27, -0.696], [-8.991, 4.019], [15.869, 7.269], [9.77, 4.019]], "c": true}]}, {"t": 236, "s": [{"i": [[3.147, 1.297], [1.348, -0.31], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-2.27, -0.935], [-2.08, 0.479], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.53, -3.374], [-9.028, -3.013], [-11.372, 0.076], [-8.991, 4.019], [15.869, 7.269], [9.77, 4.019]], "c": true}]}]}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.596078455448, 0.850980401039, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 53, "ty": 0, "nm": "窗户 暗色", "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [500, 500, 0]}, "a": {"a": 0, "k": [500, 500, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "w": 1000, "h": 1000, "ip": 0, "op": 480, "st": 0, "bm": 0}]}, {"id": "comp_1", "nm": "气泡 暗色", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "形状图层 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [520.55, 501.5, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-147.475, -232.375], [-147.475, -197.75]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.160784319043, 0.800000071526, 0.588235318661, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 9.3}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "形状 1", "bm": 0, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 141.43, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 198.572, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 270, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 340, "s": [54]}]}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 70, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 141.43, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 198.572, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 270, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 340, "s": [54.5]}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "修剪路径 1", "hd": false}], "ip": 0, "op": 531, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "形状图层 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [540.85, 501.5, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-147.45, -229.75], [-147.45, -217.875]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.160784319043, 0.800000071526, 0.588235318661, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 9.3}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "形状 1", "bm": 0, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -34, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107.43, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 164.572, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 236, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 306, "s": [54]}]}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -34, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 36, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 107.43, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 164.572, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 236, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 306, "s": [54.5]}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "修剪路径 1", "hd": false}], "ip": -34, "op": 497, "st": -34, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "形状图层 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [457.4, 499.75, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-147.4, -231.5], [-147.4, -216.25]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.160784319043, 0.800000071526, 0.588235318661, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 9.3}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "形状 1", "bm": 0, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -54, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87.43, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 144.572, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 216, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 286, "s": [54]}]}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -54, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 87.43, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 144.572, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 216, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 286, "s": [54.5]}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "修剪路径 1", "hd": false}], "ip": -54, "op": 477, "st": -54, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "形状图层 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [478.1, 499.75, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-147.4, -251.75], [-147.4, -196.25]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.160784319043, 0.800000071526, 0.588235318661, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 9.3}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "形状 1", "bm": 0, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53.43, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 110.572, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 182, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 252, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 323.43, "s": [0]}]}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 53.43, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 110.572, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 182, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 252, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 323.43, "s": [100]}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "修剪路径 1", "hd": false}], "ip": -88, "op": 443, "st": -88, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "形状图层 1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [499.5, 499.75, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-147.5, -270.75], [-147.5, -177.5]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.160784319043, 0.800000071526, 0.588235318661, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 9.3}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "形状 1", "bm": 0, "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -66, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 5.43, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 62.572, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 134, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 204, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 275.43, "s": [0]}]}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -66, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 5.43, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 62.572, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 134, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 204, "s": [54.5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 275.43, "s": [100]}]}, "o": {"a": 0, "k": 0}, "m": 1, "nm": "修剪路径 1", "hd": false}], "ip": -136, "op": 395, "st": -136, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "星星", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [383.347, 245.193, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 30, "s": [52, 52, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 60, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 90, "s": [52, 52, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 120, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 150, "s": [52, 52, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 180, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 210, "s": [52, 52, 100]}, {"t": 240, "s": [100, 100, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.061, -0.045], [0.022, -0.073], [0, 0], [0.3, -0.299], [0.405, -0.124], [0, 0], [0.045, -0.061], [0, -0.076], [-0.045, -0.061], [-0.073, -0.022], [0, 0], [-0.3, -0.298], [-0.127, -0.403], [0, 0], [-0.061, -0.045], [-0.076, 0], [-0.061, 0.045], [-0.022, 0.073], [0, 0], [-0.3, 0.3], [-0.405, 0.125], [0, 0], [-0.045, 0.061], [0, 0.076], [0.045, 0.061], [0.073, 0.022], [0, 0], [0.3, 0.299], [0.125, 0.405], [0, 0], [0.061, 0.045], [0.076, 0]], "o": [[-0.061, 0.045], [0, 0], [-0.125, 0.405], [-0.3, 0.299], [0, 0], [-0.073, 0.022], [-0.045, 0.061], [0, 0.076], [0.045, 0.061], [0, 0], [0.404, 0.124], [0.3, 0.298], [0, 0], [0.022, 0.073], [0.061, 0.045], [0.076, 0], [0.061, -0.045], [0, 0], [0.125, -0.405], [0.3, -0.3], [0, 0], [0.073, -0.022], [0.045, -0.061], [0, -0.076], [-0.045, -0.061], [0, 0], [-0.405, -0.124], [-0.3, -0.299], [0, 0], [-0.022, -0.073], [-0.061, -0.045], [-0.076, 0]], "v": [[-0.211, -2.64], [-0.34, -2.458], [-0.434, -2.143], [-1.081, -1.072], [-2.154, -0.428], [-2.456, -0.333], [-2.638, -0.205], [-2.707, 0.007], [-2.638, 0.218], [-2.456, 0.347], [-2.154, 0.441], [-1.083, 1.084], [-0.434, 2.152], [-0.34, 2.458], [-0.211, 2.64], [0, 2.709], [0.211, 2.64], [0.34, 2.458], [0.434, 2.152], [1.082, 1.079], [2.154, 0.432], [2.456, 0.338], [2.638, 0.209], [2.707, -0.002], [2.638, -0.214], [2.456, -0.342], [2.154, -0.437], [1.081, -1.081], [0.434, -2.152], [0.34, -2.458], [0.211, -2.64], [0, -2.709]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.20000000298, 0.705882370472, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "气泡", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [352.99, 275.672, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.03, 0.148], [0.056, 0.14], [0, 0], [0.251, 3.32], [2.295, 2.413], [0.113, 0.113], [3.755, -0.087], [2.635, -2.677], [0.027, -3.756], [-2.596, -2.715], [-0.063, -0.054], [-3.358, -0.229], [-2.731, 1.967], [0, 0], [-0.148, 0.032], [-0.106, 0.108]], "o": [[0.03, -0.148], [0, 0], [1.906, -2.731], [-0.251, -3.32], [-0.104, -0.117], [-2.756, -2.553], [-3.755, 0.087], [-2.635, 2.677], [-0.027, 3.756], [0.063, 0.063], [2.439, 2.32], [3.358, 0.229], [0, 0], [0.141, 0.054], [0.148, -0.032], [0.106, -0.108]], "v": [[13.344, 12.537], [13.304, 12.094], [11.687, 8.119], [14.246, -1.238], [10.31, -10.103], [9.981, -10.445], [-0.172, -14.291], [-10.136, -9.982], [-14.287, 0.05], [-10.28, 10.14], [-10.091, 10.316], [-1.121, 14.261], [8.301, 11.572], [12.304, 13.112], [12.747, 13.145], [13.136, 12.93]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.270588248968, 0.282352954149, 0.301960796118, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_2", "nm": "左手", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [471.173, 392.692, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[0.639, -0.822], [0.279, -0.736], [-0.639, 0.822]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [481.074, 393.867, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-1.412, 0.932], [-0.75, -0.432], [1.412, -0.932]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [484.011, 402.065, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.003, -0.021], [-0.047, -0.116], [-0.115, -0.049], [-0.116, 0.047], [-0.049, 0.115], [0, 0], [0, 0], [0, 0], [-0.048, 0.076], [0.02, 0.088], [0.076, 0.048], [0.088, -0.02], [0, 0], [0.045, -0.033], [0.025, -0.049]], "o": [[0, 0], [-0.015, 0.014], [-0.049, 0.115], [0.047, 0.116], [0.115, 0.049], [0.116, -0.047], [0, 0], [0, 0], [0, 0], [0.088, -0.02], [0.048, -0.076], [-0.02, -0.088], [-0.076, -0.048], [0, 0], [-0.055, 0.009], [-0.045, 0.033], [0, 0]], "v": [[-0.582, -0.934], [-1.446, 0.705], [-1.473, 0.759], [-1.477, 1.12], [-1.224, 1.378], [-0.862, 1.381], [-0.604, 1.128], [0.116, -0.592], [-0.154, -0.376], [1.246, -0.745], [1.458, -0.895], [1.503, -1.15], [1.353, -1.363], [1.098, -1.407], [-0.325, -1.15], [-0.476, -1.086], [-0.582, -0.961]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.800000011921, 0.78823530674, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [480.493, 398.453, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.013, -0.02], [-0.056, -0.125], [-0.128, -0.049], [-0.125, 0.056], [-0.049, 0.128], [0, 0], [0, 0], [0, 0], [-0.032, 0.059], [0.016, 0.066], [0.056, 0.039], [0.067, -0.008], [0, 0], [0.043, -0.032], [0.023, -0.048]], "o": [[0, 0], [-0.017, 0.016], [-0.049, 0.128], [0.056, 0.125], [0.128, 0.049], [0.125, -0.056], [0, 0], [0, 0], [0, 0], [0.064, -0.023], [0.032, -0.059], [-0.016, -0.066], [-0.056, -0.039], [0, 0], [-0.053, 0.008], [-0.043, 0.032], [0, 0]], "v": [[-0.707, -1.134], [-1.769, 0.951], [-1.814, 1.005], [-1.803, 1.401], [-1.515, 1.674], [-1.119, 1.662], [-0.846, 1.374], [-0.009, -0.814], [-0.279, -0.589], [1.666, -1.174], [1.815, -1.302], [1.841, -1.496], [1.73, -1.659], [1.54, -1.706], [-0.459, -1.354], [-0.606, -1.292], [-0.707, -1.17]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.800000011921, 0.78823530674, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [478.384, 395.327, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-0.046, -0.13], [-0.124, -0.059], [-0.129, 0.046], [-0.059, 0.124], [0, 0], [0, 0], [0, 0], [-0.042, 0.063], [0.011, 0.075], [0.021, 0.034], [0.032, 0.023], [0.039, 0.009], [0.039, -0.006], [0, 0], [0.044, -0.03], [0.027, -0.046]], "o": [[0, 0], [0, 0], [-0.059, 0.125], [0.046, 0.13], [0.124, 0.058], [0.129, -0.046], [0, 0], [0, 0], [0, 0], [0.073, -0.019], [0.042, -0.063], [-0.006, -0.039], [-0.021, -0.034], [-0.032, -0.023], [-0.039, -0.009], [0, 0], [-0.053, 0.009], [-0.044, 0.03], [0, 0]], "v": [[-0.886, -1.094], [-2.093, 0.9], [-2.138, 0.945], [-2.157, 1.343], [-1.891, 1.639], [-1.495, 1.658], [-1.202, 1.391], [-0.198, -0.712], [-0.472, -0.509], [1.959, -1.09], [2.137, -1.218], [2.184, -1.432], [2.144, -1.543], [2.063, -1.63], [1.956, -1.68], [1.838, -1.684], [-0.63, -1.297], [-0.778, -1.238], [-0.886, -1.121]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.800000011921, 0.78823530674, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [471.736, 389.838, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.009, -0.027], [-0.003, -0.068], [-0.028, -0.062], [-0.05, -0.046], [-0.064, -0.024], [-0.068, 0.003], [-0.062, 0.028], [-0.046, 0.05], [-0.024, 0.064], [0, 0], [0, 0], [0, 0], [-0.008, 0.076], [0.048, 0.06], [0.076, 0.008], [0.06, -0.048], [0, 0], [0.021, -0.043]], "o": [[0, 0], [-0.017, 0.023], [-0.024, 0.064], [0.003, 0.068], [0.028, 0.062], [0.05, 0.046], [0.064, 0.024], [0.068, -0.003], [0.062, -0.028], [0.046, -0.05], [0, 0], [0, 0], [0, 0], [0.06, -0.048], [0.008, -0.076], [-0.048, -0.06], [-0.076, -0.008], [0, 0], [-0.038, 0.029], [0, 0]], "v": [[-0.607, -0.598], [-1.566, 1.311], [-1.607, 1.387], [-1.639, 1.587], [-1.592, 1.784], [-1.473, 1.948], [-1.3, 2.054], [-1.101, 2.085], [-0.904, 2.038], [-0.74, 1.92], [-0.634, 1.747], [0.113, -0.252], [0.005, -0.108], [1.532, -1.571], [1.637, -1.766], [1.574, -1.978], [1.379, -2.084], [1.167, -2.021], [-0.517, -0.743], [-0.607, -0.634]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.800000011921, 0.78823530674, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [476.937, 412.493, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.278, -0.149], [-0.316, 0], [0, 0], [-0.202, 0.12], [-0.11, 0.207], [0.013, 0.234], [0.132, 0.194], [0, 0], [0, 0], [0, 0], [0, 0], [0.144, -0.558], [-0.215, -0.387], [0, 0]], "o": [[0.278, 0.149], [0, 0], [0.235, 0.001], [0.202, -0.12], [0.11, -0.207], [-0.013, -0.234], [0, 0], [-0.365, -0.536], [0, 0], [0, 0], [-0.464, -0.342], [0.052, 0.44], [0, 0], [0.175, 0.263]], "v": [[0.27, 2.71], [1.174, 2.938], [1.196, 2.929], [1.863, 2.747], [2.34, 2.246], [2.489, 1.571], [2.268, 0.916], [2.178, 0.786], [0.886, -1.321], [1.025, -1.772], [-1.028, -2.816], [-2.491, -2.168], [-2.086, -0.916], [-0.42, 2.082]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.800000011921, 0.78823530674, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_3", "nm": "头", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "空 2", "sr": 1, "ks": {"o": {"a": 0, "k": 0}, "r": {"a": 0, "k": 0}, "p": {"a": 1, "k": [{"i": {"x": 0.816, "y": 1}, "o": {"x": 0.167, "y": 0}, "t": 0, "s": [552.125, 368.625, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.79, "y": 1}, "o": {"x": 0.2, "y": 0}, "t": 120, "s": [552.125, 371.025, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 237, "s": [552.125, 368.625, 0]}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "左眉毛", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [543.397, 358.127, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0.196, -0.021], [0, 0], [-0.013, -0.061], [-0.043, -0.008], [0, 0], [-0.226, -0.05], [0, 0], [0, 0], [-0.217, -0.259], [-0.048, 0.04], [0.04, 0.048], [0.387, 0.058]], "o": [[-0.193, -0.042], [0, 0], [-0.061, 0.013], [0.01, 0.045], [0, 0], [0.226, -0.05], [0, 0], [0, 0], [0.335, 0.049], [0.04, 0.048], [0.048, -0.04], [-0.251, -0.3], [0, 0]], "v": [[-0.073, -0.349], [-0.66, -0.38], [-0.855, -0.349], [-0.941, -0.214], [-0.851, -0.128], [-0.806, -0.129], [-0.121, -0.129], [-0.121, -0.128], [-0.113, -0.127], [0.745, 0.351], [0.903, 0.365], [0.918, 0.207], [-0.074, -0.348]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector (Stroke)", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "右眉毛", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [566.713, 360.35, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-0.45, -0.085], [-0.361, -0.256]], "o": [[0.394, -0.233], [0.441, 0.039], [0, 0]], "v": [[-1.267, 0.021], [0.043, -0.209], [1.267, 0.242]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.225}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "嘴", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [555.08, 387.032, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0.314, -0.042], [0.348, 0.081]], "o": [[-0.282, 0.143], [-0.354, 0.049], [0, 0]], "v": [[0.982, -0.153], [0.081, 0.126], [-0.982, 0.076]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.225}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "左眼皮", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-9.157, -4.548, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0.108, -0.018], [0.094, -0.054], [0, 0], [-0.037, -0.05], [-0.043, 0.011], [0, 0], [-0.108, 0.018], [-0.104, -0.033], [-0.073, -0.096], [0, 0], [0, 0], [-0.042, 0.021], [0.003, 0.044], [0, 0], [0, 0], [0.148, 0.072], [0, 0], [0, 0]], "o": [[-0.107, -0.021], [-0.108, 0.018], [0, 0], [-0.05, 0.037], [0.028, 0.037], [0, 0], [0.088, -0.066], [0.108, -0.018], [0.114, 0.044], [0, 0], [0, 0], [0.033, 0.028], [0.042, -0.021], [0, 0], [0, 0], [-0.087, -0.139], [0, 0], [0, 0], [0, 0]], "v": [[0.083, -0.302], [-0.241, -0.306], [-0.546, -0.196], [-0.637, -0.137], [-0.66, 0.021], [-0.543, 0.063], [-0.503, 0.044], [-0.204, -0.084], [0.119, -0.062], [0.404, 0.152], [0.468, 0.256], [0.496, 0.291], [0.62, 0.307], [0.682, 0.198], [0.67, 0.155], [0.629, 0.084], [0.271, -0.24], [0.195, -0.273], [0.189, -0.275]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector (Stroke)", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "左眼", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-9.058, -1.254, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 169, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 177, "s": [100, 20, 100]}, {"t": 187, "s": [100, 100, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.214, 0], [0, 0.127], [0.214, 0], [0, -0.127]], "o": [[0.214, 0], [0, -0.127], [-0.214, 0], [0, 0.127]], "v": [[0, 0.23], [0.387, 0], [0, -0.23], [-0.387, 0]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": -85.7}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "右眼皮", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [12.883, -2.8, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.124, 0.012], [0, 0], [0, 0], [0.199, -0.152], [-0.037, -0.049], [-0.049, 0.038], [-0.202, 0.003], [0, 0], [-0.093, -0.04], [-0.075, -0.067], [0, 0], [0, 0], [-0.035, 0.031], [0.014, 0.041], [0, 0], [0, 0], [0.114, 0.05]], "o": [[0, 0], [0, 0], [-0.25, 0.003], [-0.049, 0.038], [0.038, 0.049], [0.161, -0.123], [0, 0], [0.1, 0.01], [0.093, 0.041], [0, 0], [0, 0], [0.04, 0.018], [0.034, -0.031], [0, 0], [0, 0], [-0.093, -0.083], [-0.115, -0.05]], "v": [[0.036, -0.283], [-0.088, -0.288], [-0.089, -0.288], [-0.782, -0.049], [-0.803, 0.109], [-0.645, 0.13], [-0.087, -0.063], [0.014, -0.058], [0.305, 0.017], [0.559, 0.18], [0.63, 0.251], [0.666, 0.277], [0.789, 0.259], [0.82, 0.139], [0.797, 0.1], [0.708, 0.012], [0.396, -0.189]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector (Stroke)", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "右眼", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [11.03, 0.674, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 169, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 177, "s": [100, 20, 100]}, {"t": 187, "s": [100, 100, 100]}]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.214, 0], [0, 0.139], [0.214, 0], [0, -0.139]], "o": [[0.214, 0], [0, -0.139], [-0.214, 0], [0, 0.139]], "v": [[0, 0.252], [0.387, 0], [0, -0.252], [-0.387, 0]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": -85.7}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "毛前", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [575.059, 331.793, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.202, -0.011], [0.176, -0.1], [-0.153, -0.167], [-0.45, -0.5], [-0.284, 0.18], [-0.102, 0.318], [0.316, 0.254], [0.372, 0.16]], "o": [[-0.202, 0.011], [0.144, 0.176], [0.473, 0.5], [0.332, 0.049], [0.282, -0.179], [-0.391, -0.109], [-0.315, -0.255], [-0.186, -0.08]], "v": [[-1.061, -1.133], [-1.637, -0.965], [-1.285, -0.438], [0.088, 1.12], [1.042, 0.917], [1.637, 0.148], [0.565, -0.402], [-0.471, -1.028]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.61960786581, 0.364705890417, 0.345098048449, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "耳机前", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [500, 500, 0]}, "a": {"a": 0, "k": [500, 500, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "w": 1000, "h": 1000, "ip": 0, "op": 480, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "豁口", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [538.926, 347.942, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.199, 0.48], [-0.016, -0.24], [0.125, -0.254]], "o": [[-0.016, 0.24], [-0.072, 0.273], [-0.003, -0.519]], "v": [[0.149, -0.756], [0.149, -0.036], [-0.149, 0.756]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.800000011921, 0.78823530674, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "头发", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [568.634, 342.799, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.141, -0.182], [-0.351, -1.135], [-2.332, -0.702], [-0.393, -0.679], [-0.044, 0.197], [0.055, 0.194], [-0.188, -0.452], [0.104, -0.478], [-0.216, 0.242], [-0.073, 0.316], [0.065, -0.375], [-0.11, 0.713], [0.285, 0.662], [-0.239, 0.324], [0.406, 0.243], [0.405, 0.302], [0.481, 0.091], [0.315, -0.387], [-0.162, 0.284], [0.724, -0.312], [0.282, -0.229], [-0.054, -0.225], [0.072, 0.215], [0.231, 0.111], [0.012, -0.115], [0.058, -0.101]], "o": [[-0.734, 1.081], [0.509, -0.257], [0.73, 0.289], [0.133, -0.152], [0.044, -0.197], [0.412, 0.263], [0.188, 0.452], [0.306, -0.109], [0.216, -0.242], [0.168, 0.342], [0.471, -0.546], [0.11, -0.713], [0.221, -0.329], [-0.466, 0.08], [-0.432, -0.266], [-0.383, -0.305], [-0.504, -0.081], [0.158, -0.266], [-0.783, -0.091], [-0.333, 0.144], [-0.158, 0.126], [0.046, -0.222], [-0.117, -0.228], [0.035, 0.111], [-0.012, 0.115], [-0.111, 0.202]], "v": [[-4.353, -2.155], [-5.06, 0.906], [-0.517, 0.943], [1.217, 2.437], [1.486, 1.907], [1.469, 1.312], [2.39, 2.41], [2.518, 3.838], [3.312, 3.302], [3.752, 2.451], [3.909, 3.554], [4.798, 1.628], [4.531, -0.476], [5.202, -1.453], [3.851, -1.705], [2.631, -2.502], [1.316, -3.105], [-0.161, -2.907], [0.483, -3.808], [-1.818, -3.47], [-2.745, -2.907], [-3.326, -2.214], [-3.367, -2.88], [-3.902, -3.402], [-3.868, -3.059], [-3.974, -2.732]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.61960786581, 0.364705890417, 0.345098048449, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "鼻子", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [551.502, 380.952, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.035, 0.052], [0.043, -0.008], [0, 0], [0.111, -0.018], [0.083, 0.013], [0, 0], [0, 0], [0.012, -0.045], [-0.034, -0.027], [0, 0], [0, 0], [-0.107, 0.018], [-0.096, 0.05], [0, 0]], "o": [[-0.026, -0.039], [0, 0], [-0.093, 0.062], [-0.083, 0.014], [0, 0], [0, 0], [-0.043, 0.006], [-0.012, 0.045], [0, 0], [0, 0], [0.107, 0.017], [0.107, -0.018], [0, 0], [0.052, -0.035]], "v": [[0.46, -0.139], [0.346, -0.187], [0.304, -0.17], [-0.006, -0.047], [-0.256, -0.046], [-0.338, -0.064], [-0.383, -0.067], [-0.475, 0.016], [-0.436, 0.134], [-0.396, 0.154], [-0.291, 0.177], [0.031, 0.175], [0.337, 0.072], [0.43, 0.017]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.196078434587, 0.215686276555, 0.223529413342, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector (Stroke)", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [586.797, 375.606, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-0.209, 0.07], [-0.103, -0.042], [-0.048, -0.101]], "o": [[0.152, -0.16], [0.105, -0.038], [0.103, 0.042], [0, 0]], "v": [[-0.554, 0.189], [-0.005, -0.162], [0.319, -0.155], [0.554, 0.067]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [585.322, 376.98, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.232, 0.081], [0.234, -0.076], [-0.491, -0.383], [-0.077, 0.05], [0.324, 0.594]], "o": [[-0.232, -0.081], [-0.982, 0.338], [0.351, 0.261], [0.563, -0.405], [-0.135, -0.205]], "v": [[0.518, -1.324], [-0.2, -1.332], [-0.925, 1.315], [0.232, 1], [1.083, -0.882]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.800000011921, 0.78823530674, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [585.322, 376.98, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.232, 0.081], [0.234, -0.076], [-0.491, -0.383], [-0.077, 0.05], [0.324, 0.594]], "o": [[-0.232, -0.081], [-0.982, 0.338], [0.351, 0.261], [0.563, -0.405], [-0.135, -0.205]], "v": [[0.518, -1.324], [-0.2, -1.332], [-0.925, 1.315], [0.232, 1], [1.083, -0.882]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.800000011921, 0.78823530674, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [560.601, 369.964, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0.113, 0.099], [0.145, 0.039], [0, 0], [-0.111, 1.072], [0.05, 0.251], [0.146, 0.211], [0.217, 0.136], [0.253, 0.039], [0, 0], [0, 0], [0, 0], [0.242, -0.087], [0.187, -0.176], [0.1, -0.236], [-0.003, -0.257], [0.024, -1.302], [-0.302, -0.036], [-1.022, 0.973], [-0.428, 0.739], [-0.01, 0.15], [0.057, 0.139]], "o": [[-0.113, -0.099], [0, 0], [0.111, -1.072], [0.053, -0.251], [-0.05, -0.251], [-0.146, -0.211], [-0.217, -0.136], [0, 0], [0, 0], [0, 0], [-0.256, -0.017], [-0.242, 0.087], [-0.187, 0.176], [-0.1, 0.236], [-0.177, 1.29], [0.077, 4.701], [0.928, 0.041], [0.614, -0.593], [0.076, -0.13], [0.01, -0.15], [-0.057, -0.139]], "v": [[3.682, 0.668], [3.29, 0.458], [3.29, 0.422], [3.623, -2.793], [3.627, -3.554], [3.33, -4.254], [2.779, -4.78], [2.065, -5.044], [1.917, -5.044], [-1.577, -5.418], [-1.726, -5.418], [-2.481, -5.312], [-3.13, -4.913], [-3.564, -4.288], [-3.711, -3.54], [-4.013, 0.35], [-1.046, 5.42], [2.308, 3.903], [3.88, 1.894], [4.011, 1.469], [3.94, 1.03]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.988235294819, 0.800000011921, 0.78823530674, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_4", "nm": "耳机前", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [593.508, 369.999, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.327, 0.362], [0.011, 0.488], [0.765, 0.009], [0.027, -1.09], [-0.765, 0]], "o": [[0.327, -0.362], [0.018, -1.418], [-0.765, -0.009], [-0.027, 1.09], [0.487, -0.039]], "v": [[1.063, 1.345], [1.556, 0.02], [-0.137, -1.97], [-1.555, -0.02], [-0.205, 1.97]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.301960796118, 0.341176480055, 0.40000000596, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [587.027, 369.899, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-1.089, 0], [0, 0.766], [1.089, 0], [0, -0.766]], "o": [[1.089, 0], [0, -0.766], [-1.089, 0], [0, 0.766]], "v": [[0, 1.387], [1.972, 0], [0, -1.387], [-1.972, 0]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.596078455448, 0.850980401039, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": -89.22}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [579.737, 342.4, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.479, -1.196], [0.233, -1.094], [0, 0], [0.054, 1.139], [1.234, 0.351], [0.269, -0.035], [0.222, -0.156]], "o": [[0.41, 1.041], [0, 0], [0, 0], [-0.059, -1.229], [-0.254, -0.094], [-0.269, 0.035], [1.147, 0.587]], "v": [[0.497, -0.103], [0.767, 3.17], [2.001, 3.17], [2.001, 0.888], [-0.48, -3.066], [-1.277, -3.157], [-2.025, -2.867]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.427450984716, 0.596078455448, 0.850980401039, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_5", "nm": "背景 暗色", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "草", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -17, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 27.516, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 72.869, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 118.225, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 163.58, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 208.936, "s": [5]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 254.289, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 299.645, "s": [5]}]}, "p": {"a": 0, "k": [810.684, 625.424, 0]}, "a": {"a": 0, "k": [-11.75, 59, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.414, -5.097], [0, 0], [-0.608, 2.734], [0, 0]], "o": [[-1.414, 5.097], [1.219, -2.522], [1.049, -4.597], [0, 0]], "v": [[-1.439, -2.199], [-1.997, 9.926], [0.754, 2.015], [2.253, -9.926]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.223529428244, 0.400000035763, 0.341176480055, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": -17, "op": 493, "st": -17, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "台子", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [802.625, 711.858, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.116, 0], [0, 0], [-0.094, 0.039], [-0.072, 0.072], [-0.039, 0.094], [0.001, 0.102], [0, 0], [0.039, 0.095], [0.072, 0.072], [0.095, 0.039], [0.102, -0.001], [0, 0], [0.139, -0.112], [0.036, -0.175], [0, 0], [-0.028, -0.113], [-0.073, -0.09], [-0.105, -0.05]], "o": [[0, 0], [0.102, 0], [0.094, -0.039], [0.072, -0.072], [0.039, -0.094], [0, 0], [0.001, -0.102], [-0.039, -0.095], [-0.072, -0.072], [-0.095, -0.039], [0, 0], [-0.178, -0.001], [-0.139, 0.112], [0, 0], [-0.023, 0.114], [0.028, 0.113], [0.073, 0.09], [0.105, 0.05]], "v": [[-8.776, 7.776], [8.783, 7.776], [9.081, 7.716], [9.333, 7.547], [9.5, 7.295], [9.558, 6.997], [9.558, -6.997], [9.5, -7.296], [9.331, -7.549], [9.078, -7.718], [8.779, -7.776], [-5.841, -7.776], [-6.331, -7.603], [-6.602, -7.159], [-9.542, 6.835], [-9.535, 7.179], [-9.382, 7.487], [-9.112, 7.699]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.494117677212, 0.498039245605, 0.498039245605, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [818.958, 647.295, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.423, 0], [0, 0], [0.104, 0.311], [0, 0], [0.126, 0.066], [0.141, -0.014], [0, 0], [0.027, -0.252], [0, 0]], "o": [[0, 0], [0.468, 0], [0, 0], [-0.069, -0.124], [-0.126, -0.066], [0, 0], [-0.378, 0], [0, 0], [-0.032, 0.275]], "v": [[-3.115, 3.183], [3.112, 3.183], [3.823, 2.593], [1.977, -2.81], [1.679, -3.1], [1.271, -3.179], [-2.503, -3.179], [-3.227, -2.729], [-3.84, 2.674]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.454901993275, 0.47058826685, 0.498039245605, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [297.545, 620.058, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [-100, -100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.204, 0], [0, 0], [0, -1.204], [0, 0], [-1.204, 0], [0, 0], [0, 1.204], [0, 0]], "o": [[0, 0], [-1.204, 0], [0, 0], [0, 1.204], [0, 0], [1.204, 0], [0, 0], [0, -1.204]], "v": [[9.151, -2.395], [-9.151, -2.395], [-11.33, -0.216], [-11.33, 0.216], [-9.151, 2.395], [9.151, 2.395], [11.33, 0.216], [11.33, -0.216]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65098041296, 0.65098041296, 0.65098041296, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 76.28}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [708.579, 620.09, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.204, 0], [0, 0], [0, -1.204], [0, 0], [-1.204, 0], [0, 0], [0, 1.204], [0, 0]], "o": [[0, 0], [-1.204, 0], [0, 0], [0, 1.204], [0, 0], [1.204, 0], [0, 0], [0, -1.204]], "v": [[9.151, -2.395], [-9.151, -2.395], [-11.33, -0.216], [-11.33, 0.216], [-9.151, 2.395], [9.151, 2.395], [11.33, 0.216], [11.33, -0.216]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65098041296, 0.65098041296, 0.65098041296, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": -76.28}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [502.077, 666.851, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.345, 0], [0, 0], [0, -1.345], [0, 0], [-1.345, 0], [0, 0], [0, 1.345], [0, 0]], "o": [[0, 0], [-1.345, 0], [0, 0], [0, 1.345], [0, 0], [1.345, 0], [0, 0], [0, -1.345]], "v": [[31.981, -3.575], [-31.981, -3.575], [-34.417, -1.139], [-34.417, 1.139], [-31.981, 3.575], [31.981, 3.575], [34.417, 1.139], [34.417, -1.139]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.65098041296, 0.65098041296, 0.65098041296, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [322.415, 619.995, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.459, 11.02], [-2.398, 11.092], [-6.459, -10.993], [2.177, -11.092]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.54509806633, 0.549019634724, 0.549019634724, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [348.472, 620.079, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [-100, -100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.204, 0], [0, 0], [0, -1.204], [0, 0], [-1.204, 0], [0, 0], [0, 1.204], [0, 0]], "o": [[0, 0], [-1.204, 0], [0, 0], [0, 1.204], [0, 0], [1.204, 0], [0, 0], [0, -1.204]], "v": [[9.151, -2.395], [-9.151, -2.395], [-11.33, -0.216], [-11.33, 0.216], [-9.151, 2.395], [9.151, 2.395], [11.33, 0.216], [11.33, -0.216]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.54509806633, 0.549019634724, 0.549019634724, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 76.28}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [722.318, 620.291, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-5.005, 10.97], [0.939, 11.042], [5.005, -11.042], [-0.484, -11.042]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.54509806633, 0.549019634724, 0.549019634724, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [739.599, 620.084, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[1.204, 0], [0, 0], [0, -1.204], [0, 0], [-1.204, 0], [0, 0], [0, 1.204], [0, 0]], "o": [[0, 0], [-1.204, 0], [0, 0], [0, 1.204], [0, 0], [1.204, 0], [0, 0], [0, -1.204]], "v": [[9.151, -2.395], [-9.151, -2.395], [-11.33, -0.216], [-11.33, 0.216], [-9.151, 2.395], [9.151, 2.395], [11.33, 0.216], [11.33, -0.216]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.54509806633, 0.549019634724, 0.549019634724, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": -76.28}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [671.927, 577.013, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [-3.102, 1.932]], "o": [[-1.972, -5.525], [0, 0]], "v": [[-1.045, 8.179], [1.872, -8.179]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [601.679, 577.269, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.358, 0.01], [0, 0], [-0.597, 0.52], [-0.135, 0.78], [0, 0], [0.089, 0.347], [0.231, 0.274], [0.327, 0.146], [0.358, -0.011], [0, 0], [0.596, -0.519], [0.134, -0.779], [0, 0], [-0.088, -0.347], [-0.23, -0.275], [-0.327, -0.147]], "o": [[0, 0], [0.791, -0.027], [0.597, -0.52], [0, 0], [0.071, -0.351], [-0.089, -0.347], [-0.231, -0.274], [-0.327, -0.146], [0, 0], [-0.79, 0.026], [-0.596, 0.519], [0, 0], [-0.072, 0.351], [0.088, 0.347], [0.23, 0.275], [0.327, 0.147]], "v": [[-12.949, 8.739], [10.266, 8.739], [12.412, 7.894], [13.544, 5.885], [15.345, -5.889], [15.319, -6.95], [14.834, -7.894], [13.986, -8.533], [12.945, -8.739], [-10.27, -8.739], [-12.413, -7.897], [-13.543, -5.889], [-15.344, 5.885], [-15.32, 6.946], [-14.836, 7.891], [-13.99, 8.531]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.592156887054, 0.611764729023, 0.65098041296, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [528.594, 649.522, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[34.361, -4.874], [-34.361, -4.874], [-34.361, 4.874], [34.361, 4.874]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.54509806633, 0.549019634724, 0.549019634724, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [561.833, 554.808, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.473, 0.009], [0, 0], [-0.766, 0.682], [-0.15, 1.015], [0, 0], [0.127, 0.455], [0.308, 0.358], [0.431, 0.194], [0.473, -0.008], [0, 0], [0.767, -0.687], [0.146, -1.019], [0, 0], [-0.127, -0.456], [-0.309, -0.359], [-0.432, -0.193]], "o": [[0, 0], [1.026, -0.031], [0.766, -0.682], [0, 0], [0.079, -0.466], [-0.127, -0.455], [-0.308, -0.358], [-0.431, -0.194], [0, 0], [-1.029, 0.033], [-0.767, 0.687], [0, 0], [-0.08, 0.467], [0.127, 0.456], [0.309, 0.359], [0.432, 0.193]], "v": [[-31.638, 16.828], [27.344, 16.828], [30.118, 15.723], [31.536, 13.095], [34.872, -13.073], [34.799, -14.472], [34.139, -15.708], [33.016, -16.546], [31.644, -16.828], [-27.339, -16.828], [-30.118, -15.714], [-31.53, -13.073], [-34.871, 13.073], [-34.8, 14.474], [-34.139, 15.711], [-33.013, 16.549]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.494117677212, 0.498039245605, 0.498039245605, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [688.644, 716.829, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.341, 10.855], [0.072, 10.855], [-2.341, -10.855], [0.968, -10.855]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.447058856487, 0.447058856487, 0.450980424881, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [370.691, 705.982, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.483, 10.633], [-0.214, 10.633], [2.483, -10.633], [-0.831, -10.633]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.345098048449, 0.349019616842, 0.349019616842, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [721.262, 706.131, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[2.339, 10.855], [0.07, 10.855], [-2.339, -10.855], [0.97, -10.855]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.345098048449, 0.349019616842, 0.349019616842, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [333.654, 718.167, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-2.483, 10.633], [-0.214, 10.633], [2.483, -10.633], [-0.826, -10.633]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.447058856487, 0.447058856487, 0.450980424881, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_6", "nm": "窗户 暗色", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [665.559, 554.037, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-20.696, 0], [20.696, 0]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [665.559, 424.582, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-20.696, 0], [20.696, 0]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [665.559, 303.799, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-20.696, 0], [20.696, 0]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 30}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [664.6, 468.684, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 48.03], [0, -48.03]], "c": false}}, "nm": "路径 1", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 0.135}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "窗", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [664.587, 468.643, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[3.911, 3.911], [5.531, 0], [2.531, -1.048], [1.937, -1.937], [1.048, -2.531], [0, -2.739], [0, 0], [0, 0], [0, 0]], "o": [[-3.911, -3.911], [-2.739, -0.001], [-2.531, 1.048], [-1.937, 1.937], [-1.048, 2.531], [0, 0], [0, 0], [0, 0], [0, -5.531]], "v": [[14.749, -41.915], [0.002, -48.023], [-7.98, -46.437], [-14.748, -41.917], [-19.27, -35.15], [-20.858, -27.168], [-20.858, 48.023], [20.858, 48.023], [20.858, -27.168]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.250980407, 0.250980407, 0.250980407, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "光", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [454.371, 489.189, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ks": {"a": 0, "k": {"i": [[-0.165, 0.234], [0, 0], [0, 0], [0, 0], [0.246, 0.147], [0.133, 0.254], [-0.019, 0.286]], "o": [[13.404, -18.424], [0, 0], [0, 0], [-0.286, 0.002], [-0.246, -0.147], [-0.133, -0.254], [0.019, -0.286]], "v": [[-52.103, 42.204], [19.644, -44.658], [52.386, 44.622], [-50.815, 44.658], [-51.629, 44.437], [-52.208, 43.824], [-52.383, 42.999]], "c": true}}, "nm": "路径 1", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.274509817362, 0.282352954149, 0.298039227724, 1]}, "o": {"a": 0, "k": 100}, "r": 1, "bm": 0, "nm": "填充 1", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [600, 600]}, "r": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100}, "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}, "nm": "变换"}], "nm": "Vector", "bm": 0, "hd": false}], "ip": 0, "op": 480, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "录音转写", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [340, 302.2, 0]}, "a": {"a": 0, "k": [500, 500, 0]}, "s": {"a": 0, "k": [89, 89, 100]}}, "ao": 0, "w": 1000, "h": 1000, "ip": 0, "op": 480, "st": 0, "bm": 0}], "markers": [], "props": {}}