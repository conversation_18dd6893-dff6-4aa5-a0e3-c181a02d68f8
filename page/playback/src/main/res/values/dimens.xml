<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="sp4">4sp</dimen>
    <dimen name="dp1p4">1.4dp</dimen>
    <dimen name="dp6">6dp</dimen>
    <dimen name="dp16">16dp</dimen>
    <dimen name="dp26">26dp</dimen>
    <dimen name="dp28">28dp</dimen>
    <dimen name="dp29">29dp</dimen>
    <dimen name="dp33">33dp</dimen>
    <dimen name="dp36">36dp</dimen>
    <dimen name="dp52">52dp</dimen>
    <dimen name="dp54">54dp</dimen>
    <dimen name="dp83">83dp</dimen>
    <dimen name="dp440">440dp</dimen>

    <dimen name="full_screen_empty_mark_list_margin_bottom">56dp</dimen>
    <dimen name="half_screen_empty_mark_list_margin_bottom">40dp</dimen>
    <dimen name="full_screen_empty_mark_list_width">280dp</dimen>
    <dimen name="full_screen_empty_mark_list_height">200dp</dimen>
    <dimen name="half_screen_empty_mark_list_width">168dp</dimen>
    <dimen name="half_screen_empty_mark_list_height">120dp</dimen>

    <item name="play_wave_view_height_percent" type="dimen" format="float">0.4</item>
    <item name="play_wave_view_height_percent_show_mark" type="dimen" format="float">0.15</item>

    <integer name="botton_text_line_limit">1</integer>

    <dimen name="playback_tab_item_max_width">58.5dp</dimen>
    <dimen name="playback_tab_padding_width">12dp</dimen>
    <dimen name="playback_tv_duration_marginBottom">8dp</dimen>
    <dimen name="playback_tv_speaker_maxLength">180dp</dimen>
    <!--播放页面底部按钮图片的宽高-->
    <dimen name="playback_bottom_tool_image_width">22dp</dimen>
    <dimen name="playback_bottom_tool_image_height">22dp</dimen>
    <!--播放页面底部按钮margin-->
    <dimen name="play_btn_marginTop">16dp</dimen>
    <dimen name="play_btn_marginBottom">10dp</dimen>
    <dimen name="play_fragment_bottom_space">@dimen/dp6</dimen>
    <!--播放页面底部按钮容器距离左右间距-->
    <dimen name="play_btn_control_marginHorizontal_os16">36dp</dimen>
    <dimen name="play_btn_control_marginHorizontal">24dp</dimen>
    <dimen name="play_btn_control_paddingHorizontal">4dp</dimen>
    <!--play_btn_control_marginHorizontal-play_btn_control_paddingHorizontal(4)-->
    <dimen name="play_layout_mark_marginHorizontal">20dp</dimen>
    <dimen name="play_control_button_marginStart_activity">20dp</dimen>
    <!--标记/转文本按钮在大屏下距离中线的间距=play_control_button_marginStart_activity-8dp(paddingEnd)-->
    <dimen name="play_layout_mark_marginEnd_activity">12dp</dimen>

    <dimen name="recycler_view_item_marging_start_or_end">28dp</dimen>
    <item name="max_text_image_width_ration" type="dimen" format="float">1.0</item>

    <dimen name="suspension_marginHorizontal">16dp</dimen>
    <dimen name="suspension_marginBottom">12dp</dimen>
    <dimen name="play_btn_marginEnd">10dp</dimen>
    <dimen name="img_backward_margin_middle_line">82dp</dimen>
    <dimen name="mark_icon_marginHorizontal">36dp</dimen>
    <dimen name="seekbar_time_margin_orientation">@dimen/dp12</dimen>
    <dimen name="suspension_middle_control_size">36dp</dimen>
    <dimen name="suspension_handle_margin">10dp</dimen>
    <dimen name="seekbar_text_size">24dp</dimen>
    <dimen name="seekbar_text_vertical_margin">80dp</dimen>
    <dimen name="circle_playback_button_margin_bottom">20dp</dimen>
    <dimen name="panel_height">250dp</dimen>
    <dimen name="panel_expand_radius">24dp</dimen>
    <dimen name="img_mark_add_marginHorizontal">28dp</dimen>
    <dimen name="img_mark_add_marginVertical">36dp</dimen>
    <dimen name="button_default_alpha">0</dimen>
    <dimen name="panel_padding">10dp</dimen>
    <dimen name="mark_list_margin_left">28dp</dimen>
    <dimen name="mark_list_margin_bottom">36dp</dimen>
    <dimen name="mark_ist_size">24dp</dimen>
    <dimen name="mark_list_parent_size">36dp</dimen>
    <dimen name="mark_list_normal_size">20dp</dimen>
    <dimen name="play_button_icon_normal">@dimen/dp18</dimen>
    <dimen name="wave_form_margin_bottom">@dimen/dp100</dimen>
    <dimen name="wave_form_expand_height">78dp</dimen>
    <dimen name="seek_time_text_margin_bottom">176dp</dimen>
    <dimen name="seekbar_time_expand_text_size">24dp</dimen>
    <dimen name="seekbar_time_expand_height">40dp</dimen>
    <dimen name="seek_time_translation_y">10dp</dimen>
    <dimen name="wave_form_margin_left">54dp</dimen>
    <dimen name="wave_form_margin_right">54dp</dimen>
    <dimen name="close_handle_width">32dp</dimen>
    <dimen name="close_handle_height">4dp</dimen>
    <dimen name="close_handle_marginTop">10dp</dimen>
    <dimen name="float_panel_height">56dp</dimen>


    <dimen name="line_spacing_extra">8sp</dimen>
    <dimen name="line_spacing_extra_2sp">2sp</dimen>

    <dimen name="seekbar_margin">10dp</dimen>
    <dimen name="seekbar_time_margin">24dp</dimen>
    <dimen name="seekbar_time_text_size">12dp</dimen>
    <dimen name="max_seekbar_width">1138dp</dimen>
    <dimen name="play_seekbar_margin_bottom">-5dp</dimen>
    <dimen name="play_bottom_seekbar_margin_bottom">15dp</dimen>

    <dimen name="play_mark_list_padding_top">16dp</dimen>
    <dimen name="no_mark_empty_text_size">16dp</dimen>
    <dimen name="botton_max_width">88dp</dimen>
    <dimen name="botton_min_width">60dp</dimen>
    <dimen name="play_bottom_seekbar_top">8dp</dimen>
    <dimen name="play_mark_list_padding_horizontal">16dp</dimen>
    <dimen name="play_time_area_margin_horizontal">@dimen/dp24</dimen>

    <item name="screen_width_percent_parentchild" type="dimen" format="float">1.0</item>
    <dimen name="playback_detail_margin">@dimen/dp28</dimen>
    <dimen name="play_setting_dialog_bottom">10dp</dimen>

    <dimen name="mix_image_text_layout_first_top_margin">6dp</dimen>
    <dimen name="mix_image_text_layout_last_buttom_margin">10dp</dimen>
    <dimen name="mix_image_margin_between_image_and_text">6dp</dimen>
    <dimen name="mix_image_margin_between_text_and_image">0dp</dimen>
    <dimen name="mix_image_margin_between_image_and_image">8dp</dimen>
    <dimen name="round_cornor_radius">4dp</dimen>
    <dimen name= "mix_image_border_stoke">0.5dp</dimen>
    <dimen name="mix_image_text_layout_bottom_margin">14dp</dimen>

    <dimen name="chip_group_max_height">65dp</dimen>
    <dimen name="play_convert_search_bottom_area_height">@dimen/dp56</dimen>
    <dimen name="play_convert_search_bottom_area_padding">@dimen/dp11</dimen>
    <!--    转文本播放界面沉浸态第一段下移动画可滚动的距离-->
    <dimen name="distance_immersive_first_move_down">7dp</dimen>
    <!--    转文本播放界面触发沉浸态切换的滚动距离-->
    <dimen name="distance_switch_immersive">@dimen/dp16</dimen>
    <!--    沉浸态下播放按钮大小-->
    <dimen name="circle_record_button_diam_immersive">@dimen/dp36</dimen>
    <!--    沉浸态下底部按钮的间距-->
    <dimen name="action_margin_immersive">@dimen/dp10</dimen>
    <!--    沉浸态下底部操作栏的右边距-->
    <dimen name="bottom_menu_right_margin_immersive">@dimen/dp16</dimen>
    <!--    沉浸态下底部操作栏的左边距-->
    <dimen name="bottom_menu_left_margin_immersive">@dimen/dp16</dimen>
    <!--    沉浸态下底部操作栏的底部边距-->
    <dimen name="bottom_menu_bottom_margin_immersive">@dimen/dp10</dimen>
    <!--    沉浸态下总时长右边距-->
    <dimen name="duration_right_margin_immersive">@dimen/dp12</dimen>
    <!--    沉浸态下底部操作栏的高度-->
    <dimen name="bottom_menu_height_immersive">@dimen/dp72</dimen>
    <!--    浮窗沉浸态下移动Y的距离边距-->
    <dimen name="panel_duration_y_immersive">@dimen/dp30</dimen>
    <!--    播放进度条左右padding-->
    <dimen name="seekbar_horizontal_padding">16dp</dimen>
    <dimen name="seekbar_horizontal_padding_expand">13dp</dimen>
    <!--    播放红线左右按住拖动的左右距离-->
    <dimen name="seekbar_select_padding">20dp</dimen>
    <!--    播放红线拖动动画矩形的宽度-->
    <dimen name="seekbar_drag_width">38dp</dimen>

    <dimen name="play_audio_tv_current_time_margin_top">@dimen/dp48</dimen>
    <dimen name="play_audio_tv_current_time_margin_bottom">@dimen/dp48</dimen>

</resources>