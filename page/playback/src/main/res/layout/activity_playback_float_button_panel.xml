<?xml version="1.0" encoding="utf-8"?>
<com.soundrecorder.playback.view.CustomConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/panel"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/rounded_shadow_background">

    <RelativeLayout
        android:id="@+id/img_close_handle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:padding="@dimen/dp10"
        android:alpha="@dimen/button_default_alpha"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/close_handle" />
    </RelativeLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp10"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_seek_time"
            android:layout_width="match_parent"
            android:layout_height="@dimen/seekbar_time_expand_height"
            android:layout_marginBottom="@dimen/seek_time_text_margin_bottom"
            android:alpha="@dimen/button_default_alpha"
            android:visibility="gone"
            android:contentDescription=""
            android:fontFamily="OPPO Sans 4.0"
            android:fontFeatureSettings="tnum"
            android:gravity="center"
            tools:text="00:30.25"
            android:textColor="@color/seek_time_text_color"
            android:textFontWeight="600"
            android:textSize="@dimen/seekbar_time_expand_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/float_tv_current"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/seekbar_time_margin_orientation"
            android:layout_marginBottom="@dimen/seekbar_text_vertical_margin"
            android:alpha="@dimen/button_default_alpha"
            android:contentDescription=""
            android:fontFamily="OPPO Sans 4.0"
            android:fontFeatureSettings="tnum"
            android:gravity="start|center_vertical"
            tools:text="00:06"
            android:textColor="@color/coui_color_label_secondary"
            android:textFontWeight="500"
            android:textSize="@dimen/seekbar_time_text_size"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/float_tv_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/seekbar_time_margin_orientation"
            android:layout_marginBottom="@dimen/seekbar_text_vertical_margin"
            android:alpha="@dimen/button_default_alpha"
            android:contentDescription=""
            android:fontFamily="OPPO Sans 4.0"
            android:fontFeatureSettings="tnum"
            android:gravity="end|center_vertical"
            tools:text="04:02"
            android:textColor="@color/coui_color_label_tertiary"
            android:textFontWeight="500"
            android:textSize="@dimen/seekbar_time_text_size"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/float_mark_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/play_btn_marginBottom"
            android:layout_marginBottom="@dimen/play_btn_marginBottom"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <ImageView
                android:id="@+id/mark_list_bg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_play_mark_list_background"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/img_mark_list"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:contentDescription="@string/cut"
                android:src="@drawable/ic_play_mark_list"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/float_img_backward"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/img_backward_margin_middle_line"
            android:layout_marginBottom="@dimen/img_mark_add_marginVertical"
            android:alpha="@dimen/button_default_alpha"
            android:visibility="gone"
            android:background="@drawable/sub_control_item_bg"
            android:contentDescription="@string/back_three_seconds"
            android:src="@drawable/ic_backward"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <FrameLayout
            android:id="@+id/img_wave_form_bg"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp36"
            android:layout_marginBottom="@dimen/play_btn_marginBottom"
            android:layout_marginLeft="@dimen/wave_form_margin_left"
            android:layout_marginRight="@dimen/wave_form_margin_right"
            android:background="@drawable/wave_from_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.soundrecorder.playback.view.PlayAmplitudeSeekbar
                android:id="@+id/amplitude_seekbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </FrameLayout>

        <com.soundrecorder.common.widget.glow.ShadowImageView
            android:id="@+id/play_button_shadow"
            android:layout_width="@dimen/suspension_middle_control_size"
            android:layout_height="@dimen/suspension_middle_control_size"
            android:alpha="0"
            app:shadow_blur_radius="@dimen/dp28"
            app:layout_constraintTop_toTopOf="@id/middle_control"
            app:layout_constraintBottom_toBottomOf="@id/middle_control"
            app:layout_constraintStart_toStartOf="@id/middle_control"
            app:layout_constraintEnd_toEndOf="@id/middle_control"
            app:shadow_offsetY="@dimen/dp14"
            app:shadow_offsetX="@dimen/dp0"
            app:shadow_reference="@id/float_red_circle_icon"
            app:shadow_color="@color/record_btn_shadow_red_color"/>

        <RelativeLayout
            android:id="@+id/middle_control"
            android:layout_width="@dimen/suspension_middle_control_size"
            android:layout_height="@dimen/suspension_middle_control_size"
            android:layout_marginEnd="@dimen/play_btn_marginEnd"
            android:layout_marginBottom="@dimen/play_btn_marginBottom"
            android:importantForAccessibility="no"
            android:transitionName="sharedControlView"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.soundrecorder.playback.view.PlaybackAnimatedCircleButton
                android:id="@+id/float_red_circle_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_centerInParent="true"
                android:contentDescription="@string/talkback_play"
                android:scaleType="fitCenter"
                android:src="@drawable/ic_pause_icon"
                android:transitionName="sharedRedCircleView"
                android:forceDarkAllowed="false"
                android:translationZ="@dimen/dp0"
                app:state_change="false"
                app:glow_enable="true"
                app:reference_id="@id/float_red_circle_icon_reference"
                app:circle_color="@color/coui_color_container_theme_red"
                app:circle_gradient_end_color="@color/record_btn_gradient_end_color"
                app:circle_gradient_start_color="@color/record_btn_gradient_start_color"
                app:circle_radius="@dimen/play_button_icon_normal"
                app:vibrate_toggle="true" />

            <ImageButton
                android:id="@+id/float_red_circle_icon_reference"
                android:forceDarkAllowed="false"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clickable="false"
                android:background="@color/color_transparent"
                android:layout_centerInParent="true"
                android:translationZ="@dimen/dp1"/>

        </RelativeLayout>

        <ImageView
            android:id="@+id/float_img_forward"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/img_backward_margin_middle_line"
            android:layout_marginBottom="@dimen/img_mark_add_marginVertical"
            android:alpha="@dimen/button_default_alpha"
            android:visibility="gone"
            android:background="@drawable/sub_control_item_bg"
            android:contentDescription="@string/fast_three_seconds"
            android:src="@drawable/ic_forward"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <ImageView
            android:id="@+id/float_img_mark_add"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/img_mark_add_marginHorizontal"
            android:layout_marginBottom="@dimen/img_mark_add_marginVertical"
            android:alpha="@dimen/button_default_alpha"
            android:visibility="gone"
            android:background="@drawable/sub_control_item_bg"
            android:src="@drawable/selector_playback_button_mark_new"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.soundrecorder.playback.view.CustomConstraintLayout>


