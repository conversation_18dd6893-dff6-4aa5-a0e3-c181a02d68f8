<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/record_title_time_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.soundrecorder.common.widget.COUIAnimateTextView
            android:id="@+id/record_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp28"
            android:layout_marginTop="8dp"
            android:fontFamily="sans-serif-medium"
            android:forceDarkAllowed="false"
            android:gravity="center_vertical|start"
            android:letterSpacing="0"
            android:lineHeight="@dimen/sp37"
            android:lineSpacingExtra="@dimen/sp6.5"
            android:textColor="?attr/couiColorLabelPrimary"
            android:textFontWeight="700"
            android:textSize="@dimen/sp24"
            android:clickable="true"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.oplus.anim.EffectiveAnimationView
            android:id="@+id/loading"
            android:layout_width="@dimen/dp28"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp28"
            android:layout_marginTop="8dp"
            android:forceDarkAllowed="false"
            android:gravity="center_vertical"
            android:visibility="gone"
            app:anim_loop="true"
            app:layout_constraintBottom_toBottomOf="@+id/record_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/record_title" />

        <com.soundrecorder.common.widget.COUIAnimateTextView
            android:id="@+id/record_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/dp28"
            android:ellipsize="end"
            android:fontFamily="sans-serif-regular"
            android:forceDarkAllowed="false"
            android:gravity="center_vertical|start"
            android:letterSpacing="0"
            android:lineHeight="@dimen/sp20"
            android:lineSpacingExtra="@dimen/sp3"
            android:lines="1"
            android:textColor="?attr/couiColorLabelSecondary"
            android:textFontWeight="400"
            android:textSize="@dimen/sp14"
            android:visibility="gone"
            android:clickable="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/record_title" />

        <com.coui.appcompat.tablayout.COUITabLayout
            android:id="@+id/tab_layout"
            style="@style/PlayBackTabLayoutStyle"
            android:layout_marginTop="@dimen/dp8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/record_time" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
