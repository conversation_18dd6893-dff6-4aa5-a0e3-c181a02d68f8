<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <View
        android:id="@+id/footer_divider_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="?attr/couiColorContainer8"
        android:forceDarkAllowed="false"
        android:visibility="visible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/footer_copy"
        android:layout_width="@dimen/dp36"
        android:layout_height="@dimen/dp36"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginTop="@dimen/dp12"
        android:background="@drawable/bg_image_icon"
        android:forceDarkAllowed="false"
        android:src="@drawable/ic_copy"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/footer_divider_line" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/footer_export"
        android:layout_width="@dimen/dp36"
        android:layout_height="@dimen/dp36"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/dp16"
        android:background="@drawable/bg_image_icon"
        android:forceDarkAllowed="false"
        android:src="@drawable/ic_export"
        app:layout_constraintStart_toEndOf="@id/footer_copy"
        app:layout_constraintTop_toTopOf="@id/footer_copy" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/footer_refresh"
        android:layout_width="@dimen/dp36"
        android:layout_height="@dimen/dp36"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:background="@drawable/bg_image_icon"
        android:forceDarkAllowed="false"
        android:src="@drawable/ic_refresh"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/footer_export" />

</androidx.constraintlayout.widget.ConstraintLayout>