<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                style="@style/detail_item_title"
                android:layout_marginTop="@dimen/dp12"
                android:text="@string/name" />

            <TextView
                android:id="@+id/name"
                style="@style/detail_item_content" />

            <View
                android:background="@color/coui_color_divider"
                android:layout_width="match_parent"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:forceDarkAllowed="false"
                android:layout_height="@dimen/dp0_33"/>

            <TextView
                style="@style/detail_item_title"
                android:text="@string/time" />

            <TextView
                android:id="@+id/time"
                style="@style/detail_item_content" />

            <View
                android:background="@color/coui_color_divider"
                android:layout_width="match_parent"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:forceDarkAllowed="false"
                android:layout_height="@dimen/dp0_33"/>

            <TextView
                style="@style/detail_item_title"
                android:text="@string/length" />

            <TextView
                android:id="@+id/length"
                style="@style/detail_item_content" />

            <View
                android:background="@color/coui_color_divider"
                android:layout_width="match_parent"
                android:layout_marginStart="@dimen/dp24"
                android:layout_marginEnd="@dimen/dp24"
                android:forceDarkAllowed="false"
                android:layout_height="@dimen/dp0_33"/>

            <TextView
                style="@style/detail_item_title"
                android:text="@string/path" />

            <TextView
                android:id="@+id/path"
                style="@style/detail_item_content" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>