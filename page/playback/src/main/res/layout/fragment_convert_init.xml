<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:forceDarkAllowed="false">

    <com.soundrecorder.common.widget.OSImageView
        android:id="@+id/init_logo"
        android:layout_width="@dimen/os_image_def_width"
        android:layout_height="@dimen/os_image_def_height"
        android:forceDarkAllowed="false"
        app:anim_define_scale_type="1"
        app:anim_end_frame="271"
        app:anim_frame_duration="271"
        app:anim_frame_rate="60"
        app:anim_raw_json="@raw/ic_converting_content"
        app:anim_raw_json_night="@raw/ic_converting_content_night"
        app:anim_start_frame="0"
        app:layout_constraintBottom_toTopOf="@+id/ll_convert"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/ll_convert"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <com.coui.appcompat.textview.COUITextView
            android:id="@+id/init_msg_text"
            style="@style/couiTextHeadlineXS"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:text="@string/transfer_dialog_statement_title_new"
            android:layout_marginTop="@dimen/dp8"
            android:textColor="?attr/couiColorLabelPrimary"
            android:textFontWeight="500"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/init_logo" />

        <com.coui.appcompat.textview.COUITextView
            android:id="@+id/init_des_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp2"
            android:fontFamily="sans-serif-regular"
            android:gravity="center"
            android:text="@string/convert_init_description"
            android:textColor="?attr/couiColorLabelSecondary"
            android:textFontWeight="500"
            android:textSize="@dimen/sp12"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/init_msg_text" />

        <com.coui.appcompat.textview.COUITextView
            android:id="@+id/tv_start"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp26"
            android:background="@drawable/bg_text_transfer"
            android:ellipsize="end"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:paddingHorizontal="@dimen/dp12"
            android:paddingVertical="@dimen/dp4"
            android:text="@string/transfer_text"
            android:textColor="?attr/couiColorLabelPrimary"
            android:textFontWeight="500"
            android:textSize="@dimen/sp14"
            app:drawableStartCompat="@drawable/speech_to_text"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/init_des_text" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>