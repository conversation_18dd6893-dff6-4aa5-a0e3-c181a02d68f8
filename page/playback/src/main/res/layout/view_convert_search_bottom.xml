<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="@dimen/play_convert_search_bottom_area_height"
    android:background="@color/convert_search_layout_bg"
    android:padding="@dimen/play_convert_search_bottom_area_padding">

    <ImageButton
        android:id="@+id/ib_search_previous"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="@dimen/dp14"
        android:background="@drawable/selector_imagebtn_search_prev"
        android:contentDescription="@string/previous_result" />

    <TextView
        android:id="@+id/tv_search_result"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@id/ib_search_previous"
        app:layout_constraintEnd_toStartOf="@id/ib_search_next"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginStart="@dimen/dp28"
        android:layout_marginEnd="@dimen/dp28"
        android:ellipsize="end"
        android:clickable="true"
        android:gravity="center"
        android:lineHeight="@dimen/sp20"
        android:maxLines="1"
        android:textColor="@color/selector_search_position_text"
        android:textSize="@dimen/sp14"
        tools:text="111111/15644441111111111111111111111111111111" />

    <ImageButton
        android:id="@+id/ib_search_next"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginEnd="@dimen/dp14"
        android:background="@drawable/selector_imagebtn_search_next"
        android:contentDescription="@string/next_result" />

</merge>