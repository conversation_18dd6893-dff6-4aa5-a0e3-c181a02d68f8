<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <com.soundrecorder.playback.view.PlaybackContainer
        android:id="@+id/rootView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/coui_color_white">

        <include
            android:id="@+id/color_load_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintTop_toBottomOf="@id/appbar_layout"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:visibility="gone"
            layout="@layout/loading_animation_view"/>

        <FrameLayout
            android:id="@+id/body"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appbar_layout">

            <FrameLayout
                android:id="@+id/fl_convert_audio_container"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.coui.appcompat.viewpager.COUIViewPager2
                android:id="@+id/viewpager"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:importantForAccessibility="no" />

            <include
                android:id="@+id/float_buttonPanel"
                layout="@layout/activity_playback_float_button_panel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginLeft="@dimen/suspension_marginHorizontal"
                android:layout_marginRight="@dimen/suspension_marginHorizontal"
                android:layout_marginBottom="@dimen/suspension_marginBottom"
                android:visibility="gone" />

            <com.soundrecorder.playback.view.PanelOverlay
                android:id="@+id/panel_overlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/fl_convert_search_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/appbar_layout" />

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbar_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:elevation="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <com.coui.appcompat.toolbar.COUIToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/recorder_toolbar_height" />

            <include
                android:id="@+id/header_container"
                layout="@layout/layout_record_title_time_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.google.android.material.appbar.AppBarLayout>
    </com.soundrecorder.playback.view.PlaybackContainer>
</layout>