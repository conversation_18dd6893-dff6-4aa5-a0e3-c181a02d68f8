<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.coui.appcompat.edittext.COUIInputView
                    android:id="@+id/coui_edit"
                    android:drawablePadding="@dimen/coui_input_button_layout_margin_start"
                    android:paddingBottom="@dimen/dp12"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp24"
                    android:layout_marginEnd="@dimen/dp24"
                    android:cursorVisible="true"
                    android:inputType="text"
                    android:textAlignment="viewStart"
                    app:couiInputMaxCount="50"
                    app:couiEnableInputCount="true"
                    app:quickDelete="true" />

                <TextView
                    android:id="@+id/dlg_rename_note"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/dp24"
                    android:layout_marginEnd="@dimen/dp24"
                    android:gravity="start|center_vertical"
                    android:textColor="@color/couiRedTintControlNormal"
                    android:textSize="@dimen/sp10"
                    android:visibility="visible"
                    tools:text="文件名已存在" />

                <LinearLayout
                    android:id="@+id/content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp6"
                    android:layout_marginBottom="@dimen/dp10"
                    android:layout_marginStart="@dimen/common_margin"
                    android:layout_marginEnd="@dimen/common_margin"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:layout_toStartOf="@id/tag_deleteAll"
                            android:ellipsize="end"
                            android:maxLines="1"
                            android:singleLine="true"
                            android:text="@string/input_history"
                            android:textColor="@color/tip_gray"
                            android:textSize="@dimen/sp14"
                            tools:ignore="RelativeOverlap" />

                        <ImageView
                            android:id="@+id/tag_deleteAll"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:background="@drawable/ripple_list_play"
                            android:contentDescription="@string/role_name_tag_clear"
                            android:src="@drawable/img_delete_tag" />

                    </RelativeLayout>

                    <com.soundrecorder.playback.view.RestrictChipGroup
                        android:id="@+id/tag_chipGroup"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="13dp"
                        app:chipSpacingHorizontal="@dimen/dp6"
                        app:chipSpacingVertical="@dimen/dp8"/>
                </LinearLayout>

                <com.coui.appcompat.checkbox.COUICheckBox
                    android:id="@+id/convert_rename_cb"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp6"
                    android:layout_marginBottom="@dimen/dp6"
                    android:layout_marginStart="@dimen/dp24"
                    android:layout_marginEnd="@dimen/dp24"
                    android:background="@null"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textSize="@dimen/sp12" />
            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </LinearLayout>
</layout>