<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/dp4"
    android:paddingBottom="@dimen/dp16">

    <TextView
        android:id="@+id/tv_speakers"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp28"
        app:layout_constraintWidth_max="180dp"
        android:lines="1"
        android:ellipsize="end"
        tools:text="@string/all_speaker"
        style="@style/couiTextBodyXS"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingHorizontal="@dimen/dp8"
        android:paddingVertical="@dimen/dp4"
        android:layout_marginTop="@dimen/dp16"
        android:drawablePadding="@dimen/dp4"
        app:layout_constraintHorizontal_bias="1"
        android:drawableEnd="@drawable/ic_speaker_select_expand"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textFontWeight="500"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:forceDarkAllowed="false"
        android:background="@drawable/bg_shape_speaker_select"/>

    <com.soundrecorder.playback.newconvert.keyword.KeyWordChipGroup
        android:id="@+id/key_word_group"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp16"
        app:chipSpacingHorizontal="@dimen/dp6"
        app:chipSpacingVertical="@dimen/dp8"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_speakers" />

</androidx.constraintlayout.widget.ConstraintLayout>