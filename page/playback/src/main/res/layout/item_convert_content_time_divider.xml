<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/layout_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <!--使用要根据实际的XML来进行适配-->
    <com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout
        android:id="@+id/animator_speaker"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:background="@drawable/background_convert_speaker"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:max_padding_end="@dimen/dp6"
        app:layout_type="original">

        <LinearLayout
            android:id="@+id/ll_speaker"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/ic_speaker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp8"
                android:src="@drawable/ic_speaker"/>

            <TextView
                android:id="@+id/tv_speaker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:fontFamily="sans-serif-medium"
                android:gravity="center"
                android:includeFontPadding="false"
                android:lines="1"
                android:maxWidth="@dimen/playback_tv_speaker_maxLength"
                android:minHeight="@dimen/dp22"
                android:layout_marginStart="@dimen/dp2"
                android:layout_marginEnd="@dimen/dp8"
                android:paddingVertical="@dimen/dp4"
                android:textColor="?attr/couiColorLabelPrimary"
                android:textFontWeight="500"
                android:textSize="@dimen/sp10"
                android:textStyle="normal"
                tools:text="@string/convert_speaker" />

        </LinearLayout>
    </com.soundrecorder.playback.newconvert.view.AnimateSpeakerLayout>

    <TextView
        android:id="@+id/start_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription=""
        android:ellipsize="end"
        android:lines="1"
        android:textAppearance="@style/convert_text_time_appearance_not_focused"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>