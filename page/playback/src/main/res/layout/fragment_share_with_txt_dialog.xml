<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp16"
        android:background="@drawable/bg_shape_share_bg"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:id="@+id/rl_share_paragraph"
            android:layout_width="match_parent"
            android:layout_height="@dimen/coui_delete_alert_dialog_button_height"
            android:layout_marginLeft="@dimen/dp16"
            android:layout_marginRight="@dimen/dp16"
            android:descendantFocusability="blocksDescendants">

            <com.coui.appcompat.checkbox.COUICheckBox
                android:id="@+id/cb_share_paragraph"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginVertical="@dimen/alert_dialog_single_list_padding_vertical"
                android:background="@null"
                android:focusable="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:ellipsize="marquee"
                android:gravity="center"
                android:minHeight="@dimen/alert_dialog_title_min_height"
                android:text="@string/export_show_paragraph"
                android:textAppearance="?attr/couiTextAppearanceHeadline6"
                android:textColor="@color/coui_bottom_alert_dialog_button_text_color_selector" />

            <ImageView
                android:id="@+id/iv_share_paragraph"
                android:layout_width="match_parent"
                android:layout_height="@dimen/coui_list_divider_height"
                android:layout_alignParentBottom="true"
                android:background="?attr/couiColorDivider"
                android:forceDarkAllowed="false" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_share_time"
            android:layout_width="match_parent"
            android:layout_height="@dimen/coui_delete_alert_dialog_button_height"
            android:layout_marginLeft="@dimen/dp16"
            android:layout_marginRight="@dimen/dp16"
            android:descendantFocusability="blocksDescendants">

            <com.coui.appcompat.checkbox.COUICheckBox
                android:id="@+id/cb_share_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginVertical="@dimen/alert_dialog_single_list_padding_vertical"
                android:background="@null"
                android:focusable="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:ellipsize="marquee"
                android:gravity="center"
                android:minHeight="@dimen/alert_dialog_title_min_height"
                android:text="@string/export_show_time"
                android:textAppearance="?attr/couiTextAppearanceHeadline6"
                android:textColor="@color/coui_bottom_alert_dialog_button_text_color_selector" />

            <ImageView
                android:id="@+id/iv_share_time"
                android:layout_width="match_parent"
                android:layout_height="@dimen/coui_list_divider_height"
                android:layout_alignParentBottom="true"
                android:background="?attr/couiColorDivider"
                android:forceDarkAllowed="false" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rl_share_speaker"
            android:layout_width="match_parent"
            android:layout_height="@dimen/coui_delete_alert_dialog_button_height"
            android:layout_marginLeft="@dimen/dp16"
            android:layout_marginRight="@dimen/dp16"
            android:descendantFocusability="blocksDescendants">

            <com.coui.appcompat.checkbox.COUICheckBox
                android:id="@+id/cb_share_speaker"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginVertical="@dimen/alert_dialog_single_list_padding_vertical"
                android:background="@null"
                android:focusable="false" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:ellipsize="marquee"
                android:gravity="center"
                android:minHeight="@dimen/alert_dialog_title_min_height"
                android:text="@string/export_show_speaker"
                android:textAppearance="?attr/couiTextAppearanceHeadline6"
                android:textColor="@color/coui_bottom_alert_dialog_button_text_color_selector" />

        </RelativeLayout>

    </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.constraintlayout.widget.ConstraintLayout>