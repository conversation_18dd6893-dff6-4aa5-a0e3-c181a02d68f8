<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/contentImageLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/thumbImage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:contentDescription="@string/talkback_preview_mark_picture"
        android:forceDarkAllowed="false"
        android:padding="0.25dp"
        app:strokeColor="@color/mixlayout_image_bord_color"
        app:strokeWidth="0.5dp"
        tools:ignore="UnusedAttribute" />


</LinearLayout>