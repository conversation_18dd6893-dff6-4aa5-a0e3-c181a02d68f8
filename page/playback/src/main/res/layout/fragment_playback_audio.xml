<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/m_constraint_layout"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout
            android:id="@+id/wave_gradient_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintHeight_percent="@dimen/play_wave_view_height_percent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="@dimen/dp22"
            app:backgroundWhole="@color/wave_recycler_background">

            <com.soundrecorder.playback.audio.PlayWaveRecyclerView
                android:id="@+id/wave_recyclerview"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:importantForAccessibility="no"
                android:layoutDirection="ltr"/>
        </com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout>

        <LinearLayout
            android:id="@+id/layout_time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/play_time_area_margin_horizontal"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="@dimen/play_mark_list_padding_top"
            android:clickable="true"
            android:importantForAccessibility="no"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/wave_gradient_view"
            app:layout_constraintEnd_toEndOf="parent">

            <TextView
                android:id="@+id/tv_current_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textFontWeight="500"
                android:textSize="@dimen/common_max_time_text_size_os16"
                android:gravity="center"
                android:textAppearance="@style/couiTextAppearanceDisplayM"
                android:fontFamily="sys-sans-en"
                android:fontFeatureSettings="tnum"
                android:textStyle="bold"
                android:textColor="@color/coui_color_label_primary"
                tools:text="00:33.00" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_transfer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/play_time_area_margin_horizontal"
            android:background="@drawable/bg_text_transfer"
            android:drawableStart="@drawable/speech_to_text"
            android:textFontWeight="500"
            android:drawablePadding="@dimen/dp6"
            android:fontFamily="sys-sans-en"
            android:ellipsize="end"
            android:gravity="center"
            android:includeFontPadding="false"
            android:text="@string/transfer_text"
            android:textColor="@color/coui_color_label_primary"
            android:textSize="@dimen/sp14"
            android:layout_marginBottom="@dimen/dp10"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="@string/transfer_text" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>