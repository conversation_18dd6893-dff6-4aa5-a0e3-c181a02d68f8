<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/m_constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.coui.appcompat.scrollview.COUIScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintBottom_toTopOf="@+id/tv_transfer"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_current_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/play_audio_tv_current_time_margin_top"
                    android:fontFamily="sys-sans-en"
                    android:fontFeatureSettings="tnum"
                    android:gravity="center"
                    android:lineSpacingExtra="@dimen/dp1"
                    android:textAppearance="@style/couiTextAppearanceDisplayM"
                    android:textColor="@color/coui_color_label_primary"
                    android:textFontWeight="500"
                    android:textSize="@dimen/common_max_time_text_size_os16"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="00:33.00" />

                <com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout
                    android:id="@+id/wave_gradient_view"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginTop="@dimen/play_audio_tv_current_time_margin_bottom"
                    app:backgroundWhole="@color/wave_recycler_background"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_current_time">

                    <com.soundrecorder.playback.audio.PlayWaveRecyclerView
                        android:id="@+id/wave_recyclerview"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:importantForAccessibility="no"
                        android:layoutDirection="ltr"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </com.soundrecorder.wavemark.wave.view.WaveViewGradientLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

        </com.coui.appcompat.scrollview.COUIScrollView>


        <TextView
            android:id="@+id/tv_transfer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/play_time_area_margin_horizontal"
            android:background="@drawable/bg_text_transfer"
            android:drawablePadding="@dimen/dp4"
            android:ellipsize="end"
            android:fontFamily="sans-serif-medium"
            android:gravity="center"
            android:includeFontPadding="false"
            android:paddingTop="@dimen/dp6"
            android:paddingBottom="@dimen/dp6"
            android:text="@string/transfer_text"
            android:textColor="@color/coui_color_label_primary"
            android:textFontWeight="500"
            android:textSize="@dimen/sp14"
            android:layout_marginBottom="@dimen/dp16"
            app:drawableStartCompat="@drawable/speech_to_text"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="@string/transfer_text" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>