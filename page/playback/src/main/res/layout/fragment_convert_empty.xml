<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/empty"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:forceDarkAllowed="false">

    <com.soundrecorder.common.widget.OSImageView
        android:id="@+id/empty_convert_image"
        android:layout_width="@dimen/os_image_def_width"
        android:layout_height="@dimen/os_image_def_height"
        app:anim_raw_json="@raw/ic_convert_content_empty"
        app:anim_raw_json_night="@raw/ic_convert_content_empty_night"
        app:img_draw="@drawable/ic_convert_content_empty"
        app:layout_constraintBottom_toTopOf="@+id/ll_convert_empty"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/ll_convert_empty"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/empty_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif-regular"
            android:lineSpacingExtra="@dimen/sp4"
            android:text="@string/convert_text_empty"
            android:textColor="@color/coui_color_label_secondary"
            android:textFontWeight="500"
            android:textSize="@dimen/sp16" />

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>