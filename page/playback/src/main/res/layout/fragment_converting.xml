<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/converting"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:forceDarkAllowed="false">

    <com.soundrecorder.common.widget.OSImageView
        android:id="@+id/loadingView"
        android:layout_width="@dimen/os_image_def_width"
        android:layout_height="@dimen/os_image_def_height"
        android:importantForAccessibility="no"
        android:scaleType="fitCenter"
        app:anim_define_scale_type="1"
        app:anim_end_frame="271"
        app:anim_frame_duration="271"
        app:anim_frame_rate="60"
        app:anim_json_repeatCount="-1"
        app:anim_raw_json="@raw/ic_converting_content"
        app:anim_raw_json_night="@raw/ic_converting_content_night"
        app:anim_repeatMode="restart"
        app:anim_start_frame="0"
        app:layout_constraintBottom_toTopOf="@+id/ll_converting"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/ll_converting"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/convert_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp8"
            android:fontFamily="sans-serif-medium"
            android:forceDarkAllowed="false"
            android:gravity="center"
            android:lineSpacingExtra="@dimen/sp3"
            android:text="@string/transfer_text_progress"
            android:textColor="?attr/couiColorLabelPrimary"
            android:textFontWeight="500"
            android:textSize="@dimen/sp16" />

        <TextView
            android:id="@+id/convert_desc"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp2"
            android:fontFamily="sans-serif-regular"
            android:forceDarkAllowed="false"
            android:gravity="center"
            android:lineSpacingExtra="@dimen/sp2"
            android:text="@string/transferring_content_v2"
            android:textColor="?attr/couiColorLabelSecondary"
            android:textFontWeight="400"
            android:textSize="@dimen/sp12" />

        <TextView
            android:id="@+id/convert_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp12"
            android:fontFamily="sans-serif-medium"
            android:lineSpacingExtra="@dimen/sp3"
            android:paddingStart="@dimen/dp12"
            android:paddingEnd="@dimen/dp12"
            android:paddingBottom="@dimen/dp4"
            android:text="@string/convert_text_cancel"
            android:textColor="@color/coui_color_label_theme_blue"
            android:textFontWeight="500"
            android:textSize="@dimen/sp14" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>