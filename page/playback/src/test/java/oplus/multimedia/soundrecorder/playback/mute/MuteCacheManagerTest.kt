package oplus.multimedia.soundrecorder.playback.mute

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import org.junit.Assert.*

import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.Mockito.anyLong
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.MockitoAnnotations
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import java.io.File

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MuteCacheManagerTest {

    companion object {
        private const val FULL_PATH = "/storage/emulated/0/Music/Recordings/Standard Recordings/安静1h1M"
        private const val MEDIA_ID = 3L
        private const val LAST_MODIFY = 11111L
    }

    @Mock
    private val mMuteCacheManager: MuteCacheManager? = null

    @Before
    fun setUp() {
        MockitoAnnotations.openMocks(this)
    }

    @After
    fun tearDown() {
    }

    private fun mockData(): List<MuteItem> {
        val muteItem0 = MuteItem(1000, 2000)
        val muteItem1 = MuteItem(2000, 3000)
        val muteItem2 = MuteItem(3000, 4000)
        val muteItem3 = MuteItem(4000, 5000)
        val muteItem4 = MuteItem(5000, 6000)
        return listOf(muteItem0, muteItem1, muteItem2, muteItem3, muteItem4)
    }

    @Test
    fun should_success_when_delete() {
        val mediaId = 10L
        val file = File(MuteUtil.getMutePath(), mediaId.toString())
        val mockMuteUtil = Mockito.mockStatic(MuteUtil::class.java)
        Mockito.`when`(MuteUtil.matchFile(anyLong())).thenReturn(file)
        MuteCacheManager.delete(mediaId)
        Assert.assertFalse(File(MuteUtil.getMutePath(), mediaId.toString()).exists())
        mockMuteUtil.close()
    }

    @Test
    fun should_success_when_delete_file() {
        val fileName = "${MuteUtil.getMutePath()}/123"
        val file = File(fileName)
        val mockMuteUtil = Mockito.mockStatic(MuteUtil::class.java)
        Mockito.`when`(MuteUtil.matchFile(anyString())).thenReturn(file)
        MuteCacheManager.delete(fileName)
        Assert.assertFalse(File(fileName).exists())
        mockMuteUtil.close()
    }

    @Test
    fun should_success_when_clearDirtyData() {
        val context: Context = ApplicationProvider.getApplicationContext()
        val mockedMap = mutableMapOf<String, File>().also {
            it["1"] = File("1")
            it["2"] = File("2")
        }
        val mockMuteUtil = Mockito.mockStatic(MuteUtil::class.java)
        Mockito.`when`(MuteUtil.getSuffixMap()).thenReturn(mockedMap)
        MuteCacheManager.clearDirtyData(context)

        MuteCacheManager.clearDirtyData(context)
        Whitebox.setInternalState(MuteCacheManager::class.java, "sHasClearDirtyData", false)
        MuteCacheManager.clearDirtyData(context)
        Assert.assertTrue(Whitebox.getInternalState(MuteCacheManager::class.java, "sHasClearDirtyData"))
        mockMuteUtil.close()
    }

    @Test
    fun saveTest() {
        val list = mockData()
        mMuteCacheManager?.save(FULL_PATH, MEDIA_ID, LAST_MODIFY, list)
        verify(mMuteCacheManager, times(1))?.save(FULL_PATH, MEDIA_ID, LAST_MODIFY, list)
    }

    @Test
    fun loadTest() {
        val list = mockData()
        MuteCacheManager.save(FULL_PATH, MEDIA_ID, LAST_MODIFY, list)
        val loadData = MuteCacheManager.load(MEDIA_ID, LAST_MODIFY)
        assertEquals(list, loadData)
        println("---------loadData:${loadData}")
    }
}