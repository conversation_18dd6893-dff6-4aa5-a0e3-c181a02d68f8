/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlaybackActivityViewModelTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/1/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback

import android.app.Activity
import android.content.Context
import android.net.Uri
import android.os.Build
import androidx.lifecycle.MutableLiveData
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.StartPlayModel
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.share.ShareTypeCopy
import com.soundrecorder.common.sync.db.RecordDataSync
import com.soundrecorder.modulerouter.notification.NotificationModel
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareType
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_DELETE
import com.soundrecorder.playback.PlaybackContainerViewModel.Companion.MARK_ACTION_RENAME
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.playback.shadows.ShadowRecorderLogger
import com.soundrecorder.player.status.PlayStatus
import com.soundrecorder.wavemark.mark.MarkHelper
import com.soundrecorder.wavemark.mark.MarkHelper.Companion.ADD_RESULT_DUPLICATE_TIME
import com.soundrecorder.wavemark.model.AmpAndMarkModel
import com.soundrecorder.wavemark.wave.WaveViewUtil
import com.soundrecorder.wavemark.wave.load.AmplitudeListUtil
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkStatic
import io.mockk.spyk
import io.mockk.unmockkStatic
import kotlinx.coroutines.MainScope
import oplus.multimedia.soundrecorder.playback.mute.MuteDataManager
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowToast

@RunWith(AndroidJUnit4::class)
@PrepareForTest(PlaybackContainerViewModel::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class,
        ShadowRecorderLogger::class]
)
class PlaybackActivityViewModelTest {

    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        mContext = null
    }

    @Test
    fun should_equals_when_setArguments() {
        val viewModel = PlaybackContainerViewModel()
        val mediaId = 123
        viewModel.startPlayModel = StartPlayModel(mediaId = mediaId.toLong())
        Assert.assertEquals(mediaId.toLong(), viewModel.recordId)
    }

    @Test
    fun should_equals_when_markReadReadyCallback() {
        val viewModel = PlaybackContainerViewModel()
        val defaultMarks = mutableListOf<MarkDataBean>().also {
            it.add(MarkDataBean(1000L))
        }
        viewModel.markHelper?.refreshData(defaultMarks, Uri.EMPTY, "123")
        viewModel.markReadReadyCallback = object : MarkReadReadyCallback {
            override fun onMarkReadReady(marks: List<MarkDataBean>?) {
                Assert.assertEquals(defaultMarks, marks)
            }
        }
    }

    @Test
    fun should_not_null_when_getMuteDataManager() {
        val viewModel = PlaybackContainerViewModel()
        Assert.assertNotNull(viewModel.muteDataManager)
    }

    @Test
    @Ignore
    fun should_equals_when_getNotificationModel() {
        val viewModel = PlaybackContainerViewModel()
        val playName = "标准录音 1.mp3"
        viewModel.playName.value = playName
        val notificationModel =
            Whitebox.getInternalState<NotificationModel>(viewModel, "mPlayNotificationModel")
        Assert.assertEquals(playName, notificationModel.playName?.value)
    }

    @Test
    fun should_equals_when_observeNotificationBtn() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.observeNotificationBtn(false, MutableLiveData(false))
        Assert.assertEquals(false, viewModel.mNotificationBtnStatus.value?.mIsInConvertSearch)
    }

    @Test
    fun should_equals_when_updateNotificationBtnEnable() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.observeNotificationBtn(false, null)
        val isBtnDisabled = viewModel.updateNotificationBtnEnable()
        Assert.assertNotNull(isBtnDisabled)
    }

    @Test
    fun should_equals_when_getRecordInfoFromMedia() {
        val viewModel = PlaybackContainerViewModel()
        val mockedMediaDBUtils = Mockito.mockStatic(MediaDBUtils::class.java)
        Mockito.`when`(MediaDBUtils.queryRecordById(Mockito.anyLong())).thenReturn(Record())
        val record = Whitebox.invokeMethod<Record>(viewModel, "getRecordInfoFromMedia")
        Assert.assertNotNull(record)
        mockedMediaDBUtils.reset()
    }

    @Test
    @Ignore
    fun should_equals_when_observeControlSpeakerArgs() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel())
        Whitebox.invokeMethod<Void>(viewModel, "observeControlSpeakerArgs")
        Assert.assertNotNull(viewModel.mControlSpeakerTip)
    }

    @Test
    fun should_equals_when_getMarkList() {
        val viewModel = PlaybackContainerViewModel()
        val defaultMarks = mutableListOf<MarkDataBean>().also {
            it.add(MarkDataBean(1000L))
        }
        viewModel.markHelper?.refreshData(defaultMarks, Uri.EMPTY, "123")
        Assert.assertEquals(defaultMarks, viewModel.getMarkList()?.value)
    }

    @Test
    fun should_equals_when_readMarkTag() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.hasReadMarkTag = false
        viewModel.readMarkTag()
        Assert.assertTrue(viewModel.hasReadMarkTag)
    }

    @Test
    fun should_equals_when_getCurrentTime() {
        val viewModel = PlaybackContainerViewModel()
        val curTime = 1000L
        viewModel.playerController.currentTimeMillis.value = curTime
        Assert.assertEquals(curTime, viewModel.getCurrentTime())
    }

    @Test
    fun should_equals_when_loadDuration() {
        val viewModel = PlaybackContainerViewModel()
        val duration = 1000L
        viewModel.playerController.mDuration.value = duration
        val loadDuration = Whitebox.invokeMethod<Long>(viewModel, "loadDuration")
        Assert.assertEquals(duration, loadDuration)
    }

    @Test
    fun should_equals_when_readAmpAndMark() {
        val viewModel = PlaybackContainerViewModel()
        val amplitudeListUtil = Mockito.spy(AmplitudeListUtil(mContext, "123", Uri.EMPTY, false))
        Mockito.`when`(amplitudeListUtil.ampString).thenReturn("1,3,5,7,9")
        viewModel.amplitudeListUtil = amplitudeListUtil
        val ampModel = Whitebox.invokeMethod<AmpAndMarkModel>(viewModel, "readAmpAndMark")
        Assert.assertEquals(5, ampModel.ampList.size)
    }

    @Test
    fun should_zero_when_prepareAmplitudeAndMark() {
        val viewModel = PlaybackContainerViewModel()
        val ampModel = AmpAndMarkModel().also {
            it.markDataBeans = mutableListOf<MarkDataBean?>().also { markDataBeans ->
                markDataBeans.add(MarkDataBean(1000))
            }
            it.ampList = mutableListOf(1, 2, 3, 4, 5)
        }
        Whitebox.invokeMethod<Void>(viewModel, "prepareAmplitudeAndMark", "123", ampModel, 2000L)
        Assert.assertNotEquals(0, viewModel.ampList.value?.size)
    }

    @Test
    fun should_equals_when_correctAmplitudeList() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.ampList = MutableLiveData<MutableList<Int>>().also {
            it.value = mutableListOf(1000, 2000, 3000, 4000, 5000)
        }
        val ampList = Whitebox.invokeMethod<ArrayList<Int>>(viewModel, "correctAmplitudeList", 10L)
        Assert.assertEquals(1, ampList.size)
    }

    @Test
    fun should_equals_when_forwardOrBackWard() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.forwardOrBackWard(true)
        Assert.assertEquals(true, viewModel.needSyncRulerView)
    }

    @Test
    fun should_equals_when_seekTime() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.playerController.mDuration.value = 5000
        viewModel.seekTime(3000)
        Assert.assertEquals(3500L, viewModel.muteDataManager?.nextMutePosition)
    }

    @Test
    fun should_equals_when_setCurrentTime() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.playerController.mDuration.value = 5000
        viewModel.setCurrentTime(3000)
        viewModel.playerController.doSetOCurrentTimeMillis(3000)
        Assert.assertEquals(3000L, viewModel.playerController.currentTimeMillis.value)
    }

    @Test
    fun should_equals_when_onRenameMarkListener() {
        val viewModel = PlaybackContainerViewModel()
        val defaultMarks = mutableListOf<MarkDataBean>().apply {
            add(MarkDataBean(1000L))
            add(MarkDataBean(2000L).also { it.markText = "222" })
        }
        viewModel.markHelper?.refreshData(defaultMarks, Uri.EMPTY, "123")
        viewModel.onRenameMarkListener.onRenameMark(MarkDataBean(2000), "2222","")
        Assert.assertEquals(MARK_ACTION_RENAME, viewModel.lastMarkAction.value)
    }

    @Test
    fun should_equals_when_addMark() {
        val viewModel = PlaybackContainerViewModel()
        val defaultMarks = mutableListOf<MarkDataBean>().also {
            it.add(MarkDataBean(1000L))
            it.add(MarkDataBean(2000L))
        }
        viewModel.markHelper?.refreshData(defaultMarks, Uri.EMPTY, "123")
        var index = viewModel.addMark(false, MarkMetaData(currentTimeMillis = 2100L))
        Assert.assertEquals(ADD_RESULT_DUPLICATE_TIME, index)

        index = viewModel.addMark(false, MarkMetaData(currentTimeMillis = 3000L))
        Assert.assertEquals(2, index)

    }

    @Test
    fun should_equals_when_isMarkEnabled() {
        val viewModel = PlaybackContainerViewModel()
        val markHelper = MarkHelper(null, MainScope(), false)
        viewModel.markHelper = markHelper
        Assert.assertFalse(viewModel.isMarkEnabled())
    }

    @Test
    fun should_equals_when_isAddPictureMarking() {
        val viewModel = PlaybackContainerViewModel()
        val addPictureMark = MutableLiveData(false)
        viewModel.isAddPictureMarkingCallback = {
            addPictureMark
        }
        Assert.assertEquals(addPictureMark, viewModel.isAddPictureMarking())
    }

    @Test
    fun should_equals_when_removeMark() {
        val viewModel = PlaybackContainerViewModel()
        val defaultMarks = mutableListOf<MarkDataBean>().also {
            it.add(MarkDataBean(1000L))
            it.add(MarkDataBean(2000L))
        }
        viewModel.markHelper?.refreshData(defaultMarks, Uri.EMPTY, "123")
        viewModel.removeMark(1)
        Assert.assertEquals(MARK_ACTION_DELETE, viewModel.lastMarkAction.value)
    }

    @Test
    fun should_null_when_onCleared() {
        val viewModel = object : PlaybackContainerViewModel() {
            fun onClearedChild() {
                onCleared()
            }
        }
        viewModel.onClearedChild()
        Assert.assertNull(viewModel.amplitudeListUtil)
    }

    @Test
    fun should_null_when_unregisterNotificationReceiver() {
        val viewModel = PlaybackContainerViewModel()
        Whitebox.invokeMethod<Void>(viewModel, "unregisterNotificationReceiver")
        Assert.assertNull(Whitebox.getInternalState(viewModel, "mNotificationReceiver"))
    }

    @Test
    fun should_true_when_setNeedRestoreWaitingDialog() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.setNeedRestoreWaitingDialog(true)
        Assert.assertTrue(viewModel.getNeedRestoreWaitingDialog())
    }

    @Test
    fun should_false_when_onCurTimeChanged() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel())
        Mockito.`when`(viewModel.isMarkEnabled(Mockito.anyLong())).thenReturn(true)
        viewModel.markEnable.value = false
        viewModel.onCurTimeChanged(1000L)
        Assert.assertEquals(true, viewModel.markEnable.value)
    }

    @Test
    fun should_false_when_onTimerRefreshTime() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel())
        val muteDataManager = Mockito.spy(mockk<MuteDataManager>())
        val muteEnable = MutableLiveData(false)
        val nextMutePosition: Long = -1
        Mockito.`when`(viewModel.checkMuteData(Mockito.anyLong())).thenReturn(false)
        Mockito.`when`(muteDataManager.nextMutePosition).thenReturn(nextMutePosition)
        Mockito.`when`(muteDataManager.muteEnable).thenReturn(muteEnable)

        viewModel.playerController.playerDuration = 0L

        viewModel.muteDataManager = muteDataManager

        viewModel.onTimerRefreshTime(1000L)
        Assert.assertFalse(viewModel.needSyncRulerView)

        Assert.assertNotNull(viewModel.muteDataManager)
        Assert.assertFalse(viewModel.checkMuteData(0L))

        viewModel.muteDataManager?.nextMutePosition = 12
        Assert.assertFalse(viewModel.checkMuteData(0L))

        viewModel.playerController.playerState.value = PlayStatus.PLAYER_STATE_PAUSE
        viewModel.mDragListener.onDragged()

        viewModel.playerController.playerState.value = PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE
        viewModel.mDragListener.onDragged()

        viewModel.playerController.playerState.value = PlayStatus.PLAYER_STATE_INIT
        viewModel.mDragListener.onDragged()

        Assert.assertEquals(NumberConstant.NUM_10000, viewModel.maxAmplitudeSource.maxAmplitude)
        viewModel.playerController.playerState.value = PlayStatus.PLAYER_STATE_INIT
        Assert.assertEquals(PlayStatus.PLAYER_STATE_INIT, viewModel.maxAmplitudeSource.recorderState)
    }

    @Test
    fun should_false_when_onShowShareWaitingDialog() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.onShowShareWaitingDialog(0, ShareTypeCopy)
        val needExport = Whitebox.getInternalState<Boolean>(viewModel, "mNeedDoExport")
        Assert.assertFalse(needExport)
    }

    @Test
    fun should_true_when_onShareSuccess() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.onShareSuccess(0, ShareTypeCopy)
        val needExport = Whitebox.getInternalState<Boolean>(viewModel, "mNeedDoExport")
        Assert.assertTrue(needExport)
    }

    @Test
    fun should_null_when_onShareFailed() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.onShareFailed(0, ShareTypeCopy, 0, "")
        val shareUri = Whitebox.getInternalState<Uri>(viewModel, "mShareUri")
        Assert.assertNull(shareUri)
    }

    @Test
    fun assert_value_when_set_arguments() {
        val viewModel = PlaybackContainerViewModel()
        viewModel.startPlayModel = StartPlayModel()
        Assert.assertEquals(-1, viewModel.recordId)
    }

    @Test
    fun assert_value_when_observeNotificationBtn() {
        val viewModel = PlaybackContainerViewModel()
        val isConvertSearch = MutableLiveData(false)
        val isAddPictureMarking = MutableLiveData(false)

        viewModel.observeNotificationBtn(true, isConvertSearch)

        viewModel.observeNotificationBtn(false, null)
        Assert.assertFalse(viewModel.mNotificationBtnStatus.value!!.mIsAddPictureMarking)
        Assert.assertFalse(viewModel.mNotificationBtnStatus.value!!.mIsInConvertSearch)

        viewModel.isAddPictureMarkingCallback = { isAddPictureMarking }
        viewModel.observeNotificationBtn(false, isConvertSearch)
        isConvertSearch.value = true
        isAddPictureMarking.value = true

        Assert.assertNull(viewModel.updateNotificationBtnEnable().value)
    }

    @Test
    fun assert_value_when_getRecordInfoFromMedia() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)

        Assert.assertFalse(viewModel.hasReadMarkTag)
        viewModel.readMarkTag()

        viewModel.hasReadMarkTag = true
        Assert.assertTrue(viewModel.hasReadMarkTag)
        viewModel.readMarkTag()

        Assert.assertEquals(0, viewModel.getCurrentTime())

        Assert.assertEquals(0L, Whitebox.invokeMethod<Long>(viewModel, "loadDuration"))

        mockkStatic(MediaDBUtils::class)

        val record = Record()
        record.displayName = "displayName"
        record.data = "test/path"
        record.duration = 10000L
        record.mimeType = "audio/mp3"
        every { MediaDBUtils.queryRecordById(any()) } returns record

        viewModel.playName.value = ""
        viewModel.playPath.value = ""
        val method = PowerMockito.method(PlaybackContainerViewModel::class.java, "getRecordInfoFromMedia")
        method.invoke(viewModel)

        unmockkStatic(MediaDBUtils::class)
    }

    @Test
    fun assert_value_when_loadDuration() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        val playerController = Mockito.mock(PlaybackPlayerController::class.java)
        viewModel.playerController = playerController
        Mockito.`when`(playerController.getDuration()).thenReturn(500L)
        Mockito.`when`(playerController.loadDuration()).thenReturn(1000L)

        val method = PowerMockito.method(PlaybackContainerViewModel::class.java, "loadDuration")
        Assert.assertEquals(500L, method.invoke(viewModel))

        Mockito.`when`(playerController.getDuration()).thenReturn(0L)
        Assert.assertEquals(1000L, method.invoke(viewModel))
    }

    @Test
    fun assert_value_when_readAmpAndMark() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)

        val markHelper = Mockito.mock(MarkHelper::class.java)
        val amplitudeListUtil = Mockito.mock(AmplitudeListUtil::class.java)
        viewModel.amplitudeListUtil = amplitudeListUtil
        viewModel.markHelper = markHelper

        val method = PowerMockito.method(PlaybackContainerViewModel::class.java, "readAmpAndMark")
        Assert.assertNotNull(method.invoke(viewModel))

        viewModel.amplitudeListUtil = null
        var markModel = method.invoke(viewModel) as AmpAndMarkModel
        Assert.assertNull(markModel.markString)

        viewModel.amplitudeListUtil = amplitudeListUtil
        Mockito.`when`(amplitudeListUtil.markString).thenReturn(null)
        markModel = method.invoke(viewModel) as AmpAndMarkModel
        Assert.assertNull(markModel.markString)

        Mockito.`when`(amplitudeListUtil.markString).thenReturn("")
        markModel = method.invoke(viewModel) as AmpAndMarkModel
        Assert.assertNull(markModel.markString)

        Mockito.`when`(amplitudeListUtil.markString).thenReturn("markString")
        markModel = method.invoke(viewModel) as AmpAndMarkModel
        Assert.assertEquals("markString", markModel.markString)
    }

    @Test
    fun assert_value_when_prepareAmplitudeAndMark() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)

        val markHelper = Mockito.mock(MarkHelper::class.java)
        val ampAndMarkModel = Mockito.spy(AmpAndMarkModel::class.java)
        val amplitudeListUtil = Mockito.mock(AmplitudeListUtil::class.java)

        viewModel.amplitudeListUtil = null
        val markDatas: MutableLiveData<MutableList<MarkDataBean>> = MutableLiveData(mutableListOf())
        Mockito.`when`(markHelper.getMarkDatas()).thenReturn(markDatas)
        viewModel.markHelper = markHelper

        val method = PowerMockito.method(
            PlaybackContainerViewModel::class.java, "prepareAmplitudeAndMark", String::class.java,
            AmpAndMarkModel::class.java, Long::class.java
        )
        method.invoke(viewModel, null, null, 0L)
        Assert.assertEquals("", viewModel.mKeyId)

        mockkStatic(RecorderDBUtil::class)
        every { RecorderDBUtil.getKeyIdByPath(any()) } returns PLAY_PATH

        viewModel.amplitudeListUtil = amplitudeListUtil
        viewModel.markReadReadyCallback = object : MarkReadReadyCallback {
            override fun onMarkReadReady(marks: List<MarkDataBean>?) {
            }
        }

        val markDataBeans = mutableListOf<MarkDataBean>()
        val ampList = mutableListOf<Int>()

        ampAndMarkModel.ampList = ampList
        ampAndMarkModel.markDataBeans = markDataBeans
        method.invoke(viewModel, PLAY_PATH, ampAndMarkModel, 0L)
        Mockito.`when`(ampAndMarkModel.markDataBeans).thenReturn(markDataBeans)
        Assert.assertEquals(PLAY_PATH, viewModel.mKeyId)

        ampList.add(111)
        ampList.add(222)
        markDataBeans.add(mockk())
        ampAndMarkModel.ampList = ampList
        ampAndMarkModel.markDataBeans = markDataBeans
        Assert.assertTrue(ampAndMarkModel.ampList.size > 0)

        method.invoke(viewModel, PLAY_PATH, ampAndMarkModel, 100L)
        Assert.assertEquals(PLAY_PATH, viewModel.mKeyId)

        method.invoke(viewModel, PLAY_PATH, ampAndMarkModel, 10000L)

        method.invoke(viewModel, PLAY_PATH, ampAndMarkModel, 1L)

        unmockkStatic(RecorderDBUtil::class)
    }

    @Test
    fun assert_value_when_prepareAmplitudeBySound() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        val amplitudeListUtil = Mockito.mock(AmplitudeListUtil::class.java)
        val method = PowerMockito.method(
            PlaybackContainerViewModel::class.java, "prepareAmplitudeBySound",
            String::class.java, Long::class.java
        )

        method.invoke(viewModel, null, 0)

        viewModel.amplitudeListUtil = amplitudeListUtil
        method.invoke(viewModel, PLAY_PATH, 1000)
    }

    @Test
    fun assert_recordControlClick() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        viewModel.recordControlClick()
    }

    @Test
    fun assert_value_when_registerNotificationReceiver() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        val method = PowerMockito.method(PlaybackContainerViewModel::class.java, "registerNotificationReceiver")
        val method2 = PowerMockito.method(PlaybackContainerViewModel::class.java, "unregisterNotificationReceiver")
        Assert.assertFalse(viewModel.mIsFromNotification)
        Assert.assertNull(Whitebox.getInternalState(viewModel, "mNotificationReceiver"))

        method.invoke(viewModel)
        Assert.assertNotNull(Whitebox.getInternalState(viewModel, "mNotificationReceiver"))
        method.invoke(viewModel)

        method2.invoke(viewModel)
        Assert.assertNull(Whitebox.getInternalState(viewModel, "mNotificationReceiver"))
        method2.invoke(viewModel)
    }

    @Test
    fun assert_setShareCallBack() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        Assert.assertNull(Whitebox.getInternalState(viewModel, "mShareCallBack"))
        viewModel.setShareCallBack(null)
        Assert.assertNull(Whitebox.getInternalState(viewModel, "mShareCallBack"))
        viewModel.setShareCallBack(object : IShareListener {
            override fun onShowShareWaitingDialog(mediaId: Long, type: ShareType) {
            }

            override fun onShareSuccess(mediaId: Long, type: ShareType) {
            }

            override fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String) {
            }
        })
        Assert.assertNotNull(Whitebox.getInternalState(viewModel, "mShareCallBack"))
    }

    @Test
    fun assert_value_when_insertNewRecord() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        val method = PowerMockito.method(
            PlaybackContainerViewModel::class.java, "insertNewRecord", String::class.java, Context::class.java
        )
        mockkStatic(RecordDataSync::class)

        val list: ArrayList<Record> = arrayListOf()
        every { RecordDataSync.getMediaData(any(), any(), any(), any(), any(), any()) } returns null
        method.invoke(viewModel, "inputPath", mContext)

        every { RecordDataSync.getMediaData(any(), any(), any(), any(), any(), any()) } returns list
        method.invoke(viewModel, "inputPath", mContext)

        val record = Record()
        list.add(record)
        every { RecordDataSync.getMediaData(any(), any(), any(), any(), any(), any()) } returns list
        method.invoke(viewModel, "inputPath", mContext)

        unmockkStatic(RecordDataSync::class)
    }

    @Test
    fun assert_value_when_updateNewRecord() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        val method = PowerMockito.method(
            PlaybackContainerViewModel::class.java, "updateNewRecord", String::class.java, Context::class.java
        )
        mockkStatic(RecordDataSync::class)

        val list: ArrayList<Record> = arrayListOf()
        every { RecordDataSync.getMediaData(any(), any(), any(), any(), any(), any()) } returns null
        method.invoke(viewModel, "inputPath", mContext)

        every { RecordDataSync.getMediaData(any(), any(), any(), any(), any(), any()) } returns list
        method.invoke(viewModel, "inputPath", mContext)

        val record = Record()
        list.add(record)
        every { RecordDataSync.getMediaData(any(), any(), any(), any(), any(), any()) } returns list
        method.invoke(viewModel, "inputPath", mContext)

        unmockkStatic(RecordDataSync::class)
    }

    @Test
    fun assert_other_value() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)

        Assert.assertEquals(WaveViewUtil.getOneLargeWaveLineTime(mContext!!), viewModel.oneWaveLineTime, 0F)
        Assert.assertFalse(viewModel.mIsFromOtherApp)
        Assert.assertFalse(viewModel.mIsFromSearch)
        Assert.assertNull(viewModel.browseSearchWord)
        Assert.assertNull(viewModel.startPlayModel)
        Assert.assertNull(viewModel.mSoundFile)
        Assert.assertNull(viewModel.mMarkRenameData)
        Assert.assertNull(viewModel.async)
        Assert.assertNull(viewModel.dataPosition)
        Assert.assertNull(viewModel.markReadReadyCallback)
        Assert.assertNull(Whitebox.getInternalState(viewModel, "mNotificationReceiver"))
        Assert.assertNull(Whitebox.getInternalState(viewModel, "mShareCallBack"))
        Assert.assertFalse(Whitebox.getInternalState(viewModel, "mNeedRestoreWaitingDialog"))
        Assert.assertNull(Whitebox.getInternalState(viewModel, "mShareUri"))
        Assert.assertFalse(Whitebox.getInternalState(viewModel, "mNeedDoExport"))
    }

    @Test
    fun assert_PanelShowStatus_checkOuterMarkGroupShow() {
        val status = PlaybackContainerViewModel.PanelShowStatus()
        Assert.assertFalse(status.checkOuterMarkGroupShow())

        status.showActivityControlView = true
        Assert.assertTrue(status.checkOuterMarkGroupShow())

        status.showActivityControlView = false
        Assert.assertFalse(status.checkOuterMarkGroupShow())

        status.showActivityControlView = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterMarkGroupShow())

        status.showActivityControlView = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterMarkGroupShow())
    }

    @Test
    fun assert_PanelShowStatus_checkOuterConvertGroupShow() {
        val status = PlaybackContainerViewModel.PanelShowStatus()
        // 1 1 1
        status.showActivityControlView = true
        status.mHasConvertContent = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertTrue(status.checkOuterConvertGroupShow())
        //1 1 0
        status.showActivityControlView = true
        status.mHasConvertContent = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertGroupShow())
        //1 0 1
        status.showActivityControlView = true
        status.mHasConvertContent = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertGroupShow())
        //1 0 0
        status.showActivityControlView = true
        status.mHasConvertContent = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertGroupShow())
        //0 1 1
        status.showActivityControlView = false
        status.mHasConvertContent = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertGroupShow())
        //0 0 1
        status.showActivityControlView = false
        status.mHasConvertContent = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertGroupShow())
        //0 1 0
        status.showActivityControlView = false
        status.mHasConvertContent = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertGroupShow())
        //0 0 0
        status.showActivityControlView = false
        status.mHasConvertContent = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertGroupShow())
    }

    @Test
    fun assert_PanelShowStatus_checkInnerMarkGroupShow() {
        val status = PlaybackContainerViewModel.PanelShowStatus()
        Assert.assertTrue(status.checkInnerMarkGroupShow())

        status.showActivityControlView = true
        Assert.assertFalse(status.checkInnerMarkGroupShow())
    }

    @Test
    fun assert_PanelShowStatus_checkInnerConvertGroupShow() {
        val status = PlaybackContainerViewModel.PanelShowStatus()
        Assert.assertFalse(status.checkInnerConvertGroupShow())

        status.showActivityControlView = false
        status.mHasConvertContent = true
        Assert.assertTrue(status.checkInnerConvertGroupShow())

        status.showActivityControlView = true
        status.mHasConvertContent = false
        Assert.assertFalse(status.checkInnerConvertGroupShow())

        status.showActivityControlView = true
        status.mHasConvertContent = true
        Assert.assertFalse(status.checkInnerConvertGroupShow())
    }

    @Test
    fun assert_PanelShowStatus_adjustConvertGroupShow() {
        val status = Mockito.spy(PlaybackContainerViewModel.PanelShowStatus::class.java)
        val method = PowerMockito.method(PlaybackContainerViewModel.PanelShowStatus::class.java, "adjustConvertGroupShow", Boolean::class.java)

        status.mConvertShowSwitch = true
        Assert.assertTrue(method.invoke(status, true) as Boolean)

        status.mConvertShowSwitch = true
        Assert.assertFalse(method.invoke(status, false) as Boolean)

        status.mConvertShowSwitch = false
        Assert.assertTrue(method.invoke(status, true) as Boolean)

        status.mConvertShowSwitch = false
        Assert.assertFalse(method.invoke(status, false) as Boolean)
    }

    @Test
    fun assert_PanelShowStatus_checkInnerConvertRoleShow() {
        val status = spyk<PlaybackContainerViewModel.PanelShowStatus>()
        //1 1 1
        every { status.checkInnerConvertGroupShow() } returns true
        status.mConvertShowSwitch = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertTrue(status.checkInnerConvertRoleShow())
        //1 1 0
        every { status.checkInnerConvertGroupShow() } returns true
        status.mConvertShowSwitch = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkInnerConvertRoleShow())
        //1 0 1
        every { status.checkInnerConvertGroupShow() } returns true
        status.mConvertShowSwitch = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkInnerConvertRoleShow())
        //1 0 0
        every { status.checkInnerConvertGroupShow() } returns true
        status.mConvertShowSwitch = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkInnerConvertRoleShow())
        //0 1 1
        every { status.checkInnerConvertGroupShow() } returns false
        status.mConvertShowSwitch = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkInnerConvertRoleShow())
        //0 1 0
        every { status.checkInnerConvertGroupShow() } returns false
        status.mConvertShowSwitch = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkInnerConvertRoleShow())
        //0 0 1
        every { status.checkInnerConvertGroupShow() } returns false
        status.mConvertShowSwitch = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkInnerConvertRoleShow())
        //0 0 0
        every { status.checkInnerConvertGroupShow() } returns false
        status.mConvertShowSwitch = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkInnerConvertRoleShow())
    }

    @Test
    fun assert_PanelShowStatus_checkOuterConvertRoleShow() {
        val status = spyk<PlaybackContainerViewModel.PanelShowStatus>()
        //1 1 1
        every { status.checkOuterConvertGroupShow() } returns true
        status.mConvertShowSwitch = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertTrue(status.checkOuterConvertRoleShow())
        //1 1 0
        every { status.checkOuterConvertGroupShow() } returns true
        status.mConvertShowSwitch = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertRoleShow())
        //1 0 1
        every { status.checkOuterConvertGroupShow() } returns true
        status.mConvertShowSwitch = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertRoleShow())
        //1 0 0
        every { status.checkOuterConvertGroupShow() } returns true
        status.mConvertShowSwitch = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertRoleShow())
        //0 1 1
        every { status.checkOuterConvertGroupShow() } returns false
        status.mConvertShowSwitch = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertRoleShow())
        //0 1 0
        every { status.checkOuterConvertGroupShow() } returns false
        status.mConvertShowSwitch = true
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertRoleShow())
        //0 0 1
        every { status.checkOuterConvertGroupShow() } returns false
        status.mConvertShowSwitch = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertRoleShow())
        //0 0 0
        every { status.checkOuterConvertGroupShow() } returns false
        status.mConvertShowSwitch = false
        status.mCurrentTab = PlayBackInterface.TAB_TYPE_CONVERT
        Assert.assertFalse(status.checkOuterConvertRoleShow())
    }

    @Test
    fun assert_ampLoad_when_checkLoadAmpFinished() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        Assert.assertFalse(viewModel.checkLoadAmpFinished())

        viewModel.isPrepareAmplitudeAndMark.value = true
        Assert.assertTrue(viewModel.checkLoadAmpFinished())
        Assert.assertNotNull(ShadowToast.getLatestToast())
    }

    @Test
    fun assert_ampLoad_when_loadAmpSuccess() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        Assert.assertFalse(viewModel.loadAmpSuccess())

        viewModel.isPrepareAmplitudeAndMark.value = true
        Assert.assertTrue(viewModel.loadAmpSuccess())

        viewModel.isPrepareAmplitudeAndMark.value = false
        viewModel.mIsDecodeReady.value = true
        Assert.assertTrue(viewModel.loadAmpSuccess())
    }

    @Test
    fun should_correct_when_toNotesSummaryActivity() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        val activity = Mockito.mock(Activity::class.java)
        viewModel.toNotesSummaryActivity(activity, 1) { _, _ -> }

        Whitebox.setInternalState(viewModel, "summaryNoteId", "1")
        Whitebox.setInternalState(viewModel, "summaryCallId", "1")
        viewModel.toNotesSummaryActivity(activity, 1) { _, _ -> }
    }

    @Test
    fun should_correct_when_addShowSummaryEvent() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        viewModel.addShowSummaryEvent("", true, false)
        viewModel.addShowSummaryEvent("", false, true)
    }

    @Test
    fun should_correct_when_deleteNoteData() {
        val viewModel = Mockito.spy(PlaybackContainerViewModel::class.java)
        Whitebox.setInternalState(viewModel, "summaryNoteId", "1")
        viewModel.handleClearSummaryCallId("1")
        Assert.assertNull(Whitebox.getInternalState(viewModel, "summaryCallId"))
    }

    companion object {
        private const val ACTION_TIME_SET = "android.intent.action.TIME_SET"
        private const val ACTION_DATE_CHANGED = "android.intent.action.DATE_CHANGED"
        private const val RENAME_CONTENT = "renameContent"
        private const val PLAY_PATH = "playPath"
        private val TEST_PATH = MutableLiveData("test/path")
        private const val PLAY_NAME = "playName"
        private val TEST_PLAY_NAME = MutableLiveData("test.amr")
        private const val RECORD_ID = "recordId"
        private const val TEST_RECORD_ID = -1L
        private const val TIME_THREE_SECOND = 3000L
        private const val FIELD_DURATION = "mDuration"
        private const val DURATION = 15000L
        private const val FIELD_CURRENT_TIME_MILLIS = "currentTimeMillis"
        private const val CURRENT_TIME_MILLIS = 10000L
        private const val OTHER_CURRENT_TIME_MILLIS = 2000L
    }
}