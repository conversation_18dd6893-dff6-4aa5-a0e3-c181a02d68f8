package com.soundrecorder.playback.audio;

import android.content.Context;
import android.os.Build;
import android.view.LayoutInflater;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.oplus.anim.EffectiveAnimationView;
import com.soundrecorder.playback.PlaybackActivity;
import com.soundrecorder.playback.R;
import com.soundrecorder.playback.databinding.FragmentPlaybackAudioBinding;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;

import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;


@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class PlaybackAudioFragmentTest{
    private ActivityController<PlaybackActivity> mActivityController;
    private PlaybackActivity mActivity;
    private Context mContext;
    private FragmentPlaybackAudioBinding mMockBinding;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        ShadowLog.stream = System.out;
        mActivityController = Robolectric.buildActivity(PlaybackActivity.class);
        mActivity = mActivityController.get();
        mMockBinding = FragmentPlaybackAudioBinding.bind(LayoutInflater.from(mActivity).inflate(R.layout.fragment_playback_audio, null, false));
    }

    @Test
    public void should_notNull_when_startGuideAnim() throws Exception {
        PlaybackAudioFragment playbackAudioFragment = Mockito.mock(PlaybackAudioFragment.class);
        Whitebox.invokeMethod(playbackAudioFragment, "startGuideAnim");
        EffectiveAnimationView mAnim = Whitebox.getInternalState(playbackAudioFragment, "mAnim");
        Assert.assertNull(mAnim);
    }

    @After
    public void tearDown() {
        mContext = null;
        mActivityController = null;
    }
}
