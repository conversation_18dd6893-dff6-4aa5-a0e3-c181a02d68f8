/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.playback;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.robolectric.Shadows.shadowOf;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.view.MenuItem;
import android.view.View;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModelProvider;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;

import org.robolectric.RuntimeEnvironment;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowApplication;
import org.robolectric.shadows.ShadowDialog;
import org.robolectric.shadows.ShadowLog;
import org.robolectric.shadows.ShadowToast;
import java.util.ArrayList;
import java.util.List;

import com.soundrecorder.playback.newconvert.PlaybackConvertViewModel;

import com.soundrecorder.common.constant.Constants;

import com.soundrecorder.playback.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.playback.shadows.ShadowOplusCompactUtil;
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment;
import com.soundrecorder.player.speaker.SpeakerStateManager;
import com.soundrecorder.player.status.PlayStatus;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S,
        shadows = {ShadowLog.class,
                ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class,
                ShadowFeatureOption.class, ShadowOplusCompactUtil.class, ShadowCOUIVersionUtil.class})
public class PlaybackActivityTest {

    private static final String ACTION_TIME_SET = "android.intent.action.TIME_SET";
    private static final String ACTION_DATE_CHANGED = "android.intent.action.DATE_CHANGED";
    private static final String RENAME_CONTENT = "renameContent";
    private static final String PLAY_PATH = "playPath";
    private static final MutableLiveData<String> TEST_PATH = new MutableLiveData<>("test/path");
    private static final String PLAY_NAME = "playName";
    private static final MutableLiveData<String> TEST_PLAY_NAME = new MutableLiveData<>("test.amr");
    private static final String RECORD_ID = "recordId";
    private static final Long TEST_RECORD_ID = -1L;
    private static final Long TIME_THREE_SECOND = 3000L;
    private static final String FIELD_DURATION = "mDuration";
    private static final Long DURATION = 15000L;
    private static final String FIELD_CURRENT_TIME_MILLIS = "currentTimeMillis";
    private static final Long CURRENT_TIME_MILLIS = 10000L;
    private static final Long OTHER_CURRENT_TIME_MILLIS = 2000L;

    private ActivityController<PlaybackActivity> mActivityController;
    private PlaybackActivity mActivity;
    private Context mContext;
    private PlaybackContainerViewModel mPlaybackContainerViewModel;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        ShadowLog.stream = System.out;
        mActivityController = Robolectric.buildActivity(PlaybackActivity.class);
    }

    @After
    public void tearDown() {
        mActivity = null;
        mContext = null;
        mActivityController = null;
    }

    @Test
    public void should_registerReceiver_when_onCreate() {
        mActivity = mActivityController.create().start().get();
        List<ShadowApplication.Wrapper> receivers = shadowOf(RuntimeEnvironment.application).getRegisteredReceivers();
        List<String> receiverList = new ArrayList<>();
        for (ShadowApplication.Wrapper receiver : receivers) {
            IntentFilter intentFilter = receiver.getIntentFilter();
            for (int i = 0; i < intentFilter.countActions(); i++) {
                receiverList.add(intentFilter.getAction(i));
            }
        }
        assertTrue(receiverList.contains(ACTION_TIME_SET));
        assertTrue(receiverList.contains(ACTION_DATE_CHANGED));
    }

    @Test
    public void should_pausePlay_when_onPause_is_playing() {
        mActivity = mActivityController.create().resume().get();
        mPlaybackContainerViewModel = new ViewModelProvider(mActivity).get(PlaybackContainerViewModel.class);

        SpeakerStateManager.getInstance().setSpeakerOffSp();
        mPlaybackContainerViewModel.getPlayerController().setPlayerStatus(PlayStatus.PLAYER_STATE_PLAYING);
        mActivityController.pause();
        assertFalse(mPlaybackContainerViewModel.getPlayerController().isPlaying());
    }

    @Test
    public void should_unregisterReceiver_when_onDestroy() {
        mActivity = mActivityController.create().start().get();
        List<ShadowApplication.Wrapper> receivers = shadowOf(RuntimeEnvironment.application).getRegisteredReceivers();
        List<String> receiverList = new ArrayList<>();
        for (ShadowApplication.Wrapper receiver : receivers) {
            IntentFilter intentFilter = receiver.getIntentFilter();
            for (int i = 0; i < intentFilter.countActions(); i++) {
                receiverList.add(intentFilter.getAction(i));
            }
        }
        assertTrue(receiverList.contains(ACTION_TIME_SET));
        assertTrue(receiverList.contains(ACTION_DATE_CHANGED));
        mActivityController.resume();
        mActivityController.pause();
        mActivityController.destroy();
        receivers = shadowOf(RuntimeEnvironment.application).getRegisteredReceivers();
        receiverList = new ArrayList<>();
        for (ShadowApplication.Wrapper receiver : receivers) {
            IntentFilter intentFilter = receiver.getIntentFilter();
            for (int i = 0; i < intentFilter.countActions(); i++) {
                receiverList.add(intentFilter.getAction(i));
            }
        }
        Assert.assertFalse(receiverList.contains(ACTION_TIME_SET));
        Assert.assertFalse(receiverList.contains(ACTION_DATE_CHANGED));
    }

    @Test
    public void should_finishActivity_when_onOptionsItemSelected_with_home() {
        mActivity = mActivityController.create().start().resume().get();
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        MenuItem mockMenuItem = mock(MenuItem.class);
        doReturn(android.R.id.home).when(mockMenuItem).getItemId();
        playbackContainerFragment.onOptionsItemSelected(mockMenuItem);
//        assertTrue(mActivity.isFinishing());
    }


    @Test
    public void should_showShareDialog_when_onOptionsItemSelected_with_export() {
        mActivity = mActivityController.create().start().resume().get();
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        MenuItem mockMenuItem = mock(MenuItem.class);
        doReturn(R.id.export).when(mockMenuItem).getItemId();
        playbackContainerFragment.onOptionsItemSelected(mockMenuItem);
        Dialog dialog = ShadowDialog.getLatestDialog();
        assertNull(dialog);
}

    @Test
    public void should_showDetailDialog_when_onOptionsItemSelected_with_detail() {
        mActivity = mActivityController.create().start().resume().get();
        mPlaybackContainerViewModel = new ViewModelProvider(mActivity).get(PlaybackContainerViewModel.class);
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        MenuItem mockMenuItem = mock(MenuItem.class);
        doReturn(R.id.detail).when(mockMenuItem).getItemId();
        Whitebox.setInternalState(mPlaybackContainerViewModel, PLAY_PATH, TEST_PATH);
        Whitebox.setInternalState(mPlaybackContainerViewModel, PLAY_NAME, TEST_PLAY_NAME);
        Whitebox.setInternalState(mPlaybackContainerViewModel, RECORD_ID, TEST_RECORD_ID);
        playbackContainerFragment.onOptionsItemSelected(mockMenuItem);
        Dialog dialog = ShadowDialog.getLatestDialog();
//        assertNotNull(dialog);
    }

    @Test
    public void should_showRenameDialog_when_onOptionsItemSelected_with_rename() {
        mActivity = mActivityController.create().start().resume().get();
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        MenuItem mockMenuItem = mock(MenuItem.class);
        doReturn(R.id.rename).when(mockMenuItem).getItemId();
        playbackContainerFragment.onOptionsItemSelected(mockMenuItem);
        Dialog dialog = ShadowDialog.getLatestDialog();
        assertNull(dialog);
    }

    @Test
    public void should_showDeleteDialog_when_onOptionsItemSelected_with_delete() {
        mActivity = mActivityController.create().start().resume().get();
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        MenuItem mockMenuItem = mock(MenuItem.class);
        doReturn(R.id.delete).when(mockMenuItem).getItemId();
        playbackContainerFragment.onOptionsItemSelected(mockMenuItem);
        Dialog dialog = ShadowDialog.getLatestDialog();
//        assertNotNull(dialog);
    }

    @Test
    public void should_setAsRingtone_when_onOptionsItemSelected_with_setRingtone() {
        mActivity = mActivityController.create().start().resume().get();
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        MenuItem mockMenuItem = mock(MenuItem.class);
        doReturn(R.id.set_ringtone).when(mockMenuItem).getItemId();
        playbackContainerFragment.onOptionsItemSelected(mockMenuItem);
        Intent intent = shadowOf(RuntimeEnvironment.application).getNextStartedActivity();
        assertNotNull(intent);
        assertEquals("com.android.settings", intent.getPackage());
    }

    @Test
    public void should_answer_result_when_onActivityResult_with_different_resultCode() {
        mActivity = mActivityController.create().start().resume().get();
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
//        mActivity.setRenameContent(RENAME_CONTENT);
        playbackContainerFragment.onActivityResult(Constants.REQUEST_CODE_RENAME, Activity.RESULT_OK, null);
        playbackContainerFragment.onActivityResult(Constants.EXPORT_TO_NOTE_REQUEST_CODE, Activity.RESULT_OK, null);
        assertEquals(mContext.getResources().getString(com.soundrecorder.common.R.string.save_success), ShadowToast.getTextOfLatestToast());
        playbackContainerFragment.onActivityResult(Constants.REQUEST_CODE_DELETE_BARCH, Activity.RESULT_OK, null);
//        assertTrue(mActivity.isFinishing());
        playbackContainerFragment.onResume();
//        assertTrue(mActivity.isFinishing());
    }

    @Ignore
    @Test
    public void should_showSpeedDialog_when_onClick_imgSpeed() {
        mActivity = mActivityController.create().start().resume().get();
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        View view = mock(View.class);
        doReturn(R.id.img_mark_add).when(view).getId();
        playbackContainerFragment.onClick(view);
        Dialog latestDialog = ShadowDialog.getLatestDialog();
        assertNotNull(latestDialog);
    }

    @Test
    @Ignore
    public void should_backward_when_onClick_imgBackward() {
        mActivity = mActivityController.create().start().resume().get();
        mPlaybackContainerViewModel = new ViewModelProvider(mActivity).get(PlaybackContainerViewModel.class);
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        View view = mock(View.class);
        doReturn(R.id.img_backward).when(view).getId();
        MutableLiveData<Long> currentMillis = new MutableLiveData(CURRENT_TIME_MILLIS);
        Whitebox.setInternalState(mPlaybackContainerViewModel.getPlayerController(), FIELD_CURRENT_TIME_MILLIS, currentMillis);
        playbackContainerFragment.onClick(view);
        Long actualCurrentTimeMillis = mPlaybackContainerViewModel.getPlayerController().getCurrentTimeMillis().getValue();
        assertEquals(java.util.Optional.of(CURRENT_TIME_MILLIS - TIME_THREE_SECOND), java.util.Optional.of(actualCurrentTimeMillis));
        currentMillis.postValue(OTHER_CURRENT_TIME_MILLIS);
        playbackContainerFragment.onClick(view);
        actualCurrentTimeMillis = mPlaybackContainerViewModel.getPlayerController().getCurrentTimeMillis().getValue();
        assertEquals(java.util.Optional.of(0L), java.util.Optional.of(actualCurrentTimeMillis));
    }

    @Test
    @Ignore
    public void should_forward_when_onClick_imgForward() {
        mActivity = mActivityController.create().start().resume().get();
        mPlaybackContainerViewModel = new ViewModelProvider(mActivity).get(PlaybackContainerViewModel.class);
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        View view = mock(View.class);
        doReturn(R.id.img_forward).when(view).getId();
        MutableLiveData<Long> currentMillis = new MutableLiveData(CURRENT_TIME_MILLIS);
        Whitebox.setInternalState(mPlaybackContainerViewModel.getPlayerController(), FIELD_CURRENT_TIME_MILLIS, currentMillis);
        MutableLiveData<Long> duration = new MutableLiveData(DURATION);
        Whitebox.setInternalState(mPlaybackContainerViewModel.getPlayerController(), FIELD_DURATION, duration);
        playbackContainerFragment.onClick(view);
        Long actualCurrentTimeMillis = mPlaybackContainerViewModel.getPlayerController().getCurrentTimeMillis().getValue();
        assertEquals(java.util.Optional.of(CURRENT_TIME_MILLIS + TIME_THREE_SECOND), java.util.Optional.of(actualCurrentTimeMillis));
        currentMillis.postValue(CURRENT_TIME_MILLIS + CURRENT_TIME_MILLIS);
        playbackContainerFragment.onClick(view);
        actualCurrentTimeMillis = mPlaybackContainerViewModel.getPlayerController().getCurrentTimeMillis().getValue();
        assertEquals(java.util.Optional.of(DURATION), java.util.Optional.of(actualCurrentTimeMillis));
    }

    @Test
    @Ignore
    public void should_startTrim_when_onClick_imgTrim() {
        mActivity = mActivityController.create().start().resume().get();
        mPlaybackContainerViewModel = new ViewModelProvider(mActivity).get(PlaybackContainerViewModel.class);
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        View view = mock(View.class);
        doReturn(R.id.img_mark_list).when(view).getId();
        playbackContainerFragment.onClick(view);
        assertEquals(mContext.getResources().getString(com.soundrecorder.common.R.string.amplitues_not_ready), ShadowToast.getTextOfLatestToast());
        List<Integer> ampList = new ArrayList<>();
        ampList.add(1);
        ampList.add(2);
        mPlaybackContainerViewModel.getAmpList().postValue(ampList);
        mPlaybackContainerViewModel.getPlayerController().setPlayerStatus(PlayStatus.PLAYER_STATE_PLAYING);
        playbackContainerFragment.onClick(view);
        Intent intent = shadowOf(RuntimeEnvironment.application).getNextStartedActivity();
        assertNotNull(intent);
        assertEquals("EditRecordActivity", intent.getComponent().getClassName());
        assertFalse(mPlaybackContainerViewModel.getPlayerController().isPlaying());
    }

    @Test
    public void should_controlState_when_onClick_redCircleIcon() {
        mActivity = mActivityController.create().start().resume().get();
        mPlaybackContainerViewModel = new ViewModelProvider(mActivity).get(PlaybackContainerViewModel.class);
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        View view = mock(View.class);
        doReturn(R.id.red_circle_icon).when(view).getId();
        MutableLiveData<Integer> playerState = new MutableLiveData(PlayStatus.PLAYER_STATE_HALTON);
        mPlaybackContainerViewModel.getPlayerController().setPlayerState(playerState);
        mPlaybackContainerViewModel.getPlayerController().setMDuration(new MutableLiveData<>(DURATION));
        MutableLiveData<Long> currentMillis = new MutableLiveData(CURRENT_TIME_MILLIS + CURRENT_TIME_MILLIS);
        Whitebox.setInternalState(mPlaybackContainerViewModel.getPlayerController(), FIELD_CURRENT_TIME_MILLIS, currentMillis);
        playbackContainerFragment.onClick(view);
        assertNotEquals(0L, (long) currentMillis.getValue());
    }

    @Test
    @Ignore
    public void should_changeState_when_onClick_markList() {
        mActivity = mActivityController.create().start().resume().get();
        mPlaybackContainerViewModel = new ViewModelProvider(mActivity).get(PlaybackContainerViewModel.class);
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        View view = mock(View.class);
        doReturn(R.id.layout_mark_list_activity).when(view).getId();
        mPlaybackContainerViewModel.isShowMarkList().setValue(false);
        playbackContainerFragment.onClick(view);
        assertTrue(mPlaybackContainerViewModel.isShowMarkList().getValue());
        playbackContainerFragment.onClick(view);
        assertFalse(mPlaybackContainerViewModel.isShowMarkList().getValue());
    }

    @Test
    public void should_notNull_when_create_viewmodel() throws Exception {
        mActivity = mActivityController.create().start().resume().get();
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        PlaybackConvertViewModel playbackConvertViewModel = Whitebox.getInternalState(playbackContainerFragment, "mPlaybackConvertViewModel");
        assertNotNull(playbackConvertViewModel);
    }

    @Ignore
    @Test
    public void should_change_status_when_convert_status_observe() throws Exception {
        mActivity = mActivityController.create().start().get();
        PlaybackContainerFragment playbackContainerFragment = (PlaybackContainerFragment) mActivity.getSupportFragmentManager().getFragments().get(0);
        PlaybackConvertViewModel mPlaybackConvertViewModel = Whitebox.getInternalState(playbackContainerFragment, "mPlaybackConvertViewModel");
        Whitebox.invokeMethod(playbackContainerFragment, "initViewModelObserver");
        mPlaybackConvertViewModel.getMConvertStatus().setValue(PlaybackConvertViewModel.CONVERT_STATUS_COMPLETE);
        Assert.assertEquals(mPlaybackConvertViewModel.getMConvertStatus().getValue().intValue(), PlaybackConvertViewModel.CONVERT_STATUS_COMPLETE);
    }
}
