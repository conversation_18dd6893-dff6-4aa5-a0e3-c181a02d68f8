/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.ui;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.utils.FunctionOption;
import com.soundrecorder.common.utils.MarkSerializUtil;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
@PrepareForTest({ConvertContentItem.class, DebugUtil.class})
public class ConvertContenItemTest {

    private ConvertContentItem convertContentItem = new ConvertContentItem();
    private MockedStatic<DebugUtil> debugUtilMockedStatic;


    @Before
    public void setUp() throws Exception {
        debugUtilMockedStatic = Mockito.mockStatic(DebugUtil.class);
    }

    @After
    public void release() {
        if (debugUtilMockedStatic != null) {
            debugUtilMockedStatic.close();
        }
        debugUtilMockedStatic = null;
    }


    public void constructConvertContentItem(boolean firstImageBeforeText) {
        convertContentItem.setStartTime(0);
        convertContentItem.setEndTime(15000);
        int i = 0;
        int startSeque = 0;
        Float startTime = 2000f;
        List<ConvertContentItem.SubSentence> subSentences = new ArrayList<ConvertContentItem.SubSentence>();
        while (i < 10) {

            String text = "abcdefg";
            int endCharSeq = startSeque + text.length();
            MarkDataBean pictureMarkDataBean = null;
            if (firstImageBeforeText && (i == 0)) {
                pictureMarkDataBean = new MarkDataBean(startTime.longValue() - 50, MarkSerializUtil.VERSION_PICTURE);
            } else {
                pictureMarkDataBean = new MarkDataBean(startTime.longValue() + 20, MarkSerializUtil.VERSION_PICTURE);
            }
            pictureMarkDataBean.setPictureHeight(720);
            pictureMarkDataBean.setPictureWith(1080);
            pictureMarkDataBean.setPictureFilePath("/abcd.png");
            MarkDataBean textMarkDataBean = new MarkDataBean(startTime.longValue() + 40, MarkSerializUtil.VERSION_NEW);
            List<MarkDataBean> markDataBeanList = new ArrayList<MarkDataBean>();
            markDataBeanList.add(pictureMarkDataBean);
            markDataBeanList.add(textMarkDataBean);

            ConvertContentItem.SubSentence subSentence = new ConvertContentItem.SubSentence(startSeque, endCharSeq, startTime, text, false, false, null);
            subSentence.setMarkDataBeanList(markDataBeanList);
            subSentences.add(subSentence);
            startTime = startTime + 1000;
            i++;
        }
        convertContentItem.setListSubSentence(subSentences);
    }


    @After
    public void tearDown() {
        convertContentItem = null;
    }


    @Test
    public void should_return_first_textItem_when_parceNewTextOrImageItems() {
        constructConvertContentItem(false);
        convertContentItem.parceNewTextOrImageItems(false);
        ConvertContentItem.ItemMetaData firstItem = Objects.requireNonNull(convertContentItem.getMTextOrImageItems()).get(1);
        assert firstItem instanceof ConvertContentItem.TextItemMetaData;
    }


    @Test
    public void should_return_first_timeDividerItem_when_parceNewTextOrImageItems() {
        constructConvertContentItem(false);
        convertContentItem.parceNewTextOrImageItems(false);
        ConvertContentItem.ItemMetaData firstItem = Objects.requireNonNull(convertContentItem.getMTextOrImageItems()).get(0);
        assert firstItem instanceof ConvertContentItem.TimerDividerMetaData;
    }



    @Test
    public void should_return_first_imageItem_when_parceNewTextOrImageItems() {
        constructConvertContentItem(true);
        convertContentItem.parceNewTextOrImageItems(false);
        ConvertContentItem.ItemMetaData firstItem = Objects.requireNonNull(convertContentItem.getMTextOrImageItems()).get(1);
        if (FunctionOption.IS_SUPPORT_MIX_LAYOUT) {
            assert firstItem instanceof ConvertContentItem.ImageMetaData;
        } else {
            assert firstItem instanceof ConvertContentItem.TextItemMetaData;
        }
    }


}
