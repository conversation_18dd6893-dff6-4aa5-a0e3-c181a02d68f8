/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AnimateSpeakerLayoutTest
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9013204
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */
package com.soundrecorder.playback.newconvert.view

import android.animation.ValueAnimator
import android.content.Context
import android.os.Build
import android.util.Size
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.common.utils.ViewUtils.getUnDisplayViewSize
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class AnimateSpeakerLayoutTest {
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        mContext = null
    }

    @Test
    fun verify_data_when_setAnimateSelected() {
        val animateSpeakerLayout = AnimateSpeakerLayout(mContext!!)
        val textView = TextView(mContext!!)
        val textView2 = TextView(mContext!!)
        Assert.assertEquals(1f, Whitebox.getInternalState(animateSpeakerLayout, "maxAlpha"))
        Whitebox.invokeMethod<Void>(animateSpeakerLayout, "setChildAlpha", 0.5f)
        Assert.assertEquals(1f, Whitebox.getInternalState(animateSpeakerLayout, "maxAlpha"))

        animateSpeakerLayout.addView(textView)
        animateSpeakerLayout.addView(textView2)
        Whitebox.invokeMethod<Void>(animateSpeakerLayout, "setChildAlpha", 0.5f)
        Assert.assertEquals(0.5f, textView2.alpha)
    }

    @Test
    fun verify_data_when_getChildHeight() {
        val animateSpeakerLayout = AnimateSpeakerLayout(mContext!!)
        animateSpeakerLayout.post {
            val textView = TextView(mContext!!)
            Assert.assertEquals(0, Whitebox.invokeMethod<Void>(animateSpeakerLayout, "getChildHeight"))

            val viewUtilsMock = Mockito.mockStatic(ViewUtils::class.java)

            val view = Mockito.mock(View::class.java)
            Mockito.`when`(view.measuredWidth).thenReturn(0)
            Mockito.`when`(view.measuredHeight).thenReturn(0)
//        Mockito.`when`(view.getUnDisplayViewSize()).thenReturn(Size(100, 100))
            viewUtilsMock.`when`<Size> { view.getUnDisplayViewSize() }.thenReturn(Size(100, 100))
            animateSpeakerLayout.addView(textView)
            animateSpeakerLayout.addView(view)
            Assert.assertEquals(100, Whitebox.invokeMethod<Void>(animateSpeakerLayout, "getChildHeight"))

            Mockito.`when`(view.measuredHeight).thenReturn(200)
            Mockito.`when`(view.measuredWidth).thenReturn(200)
            Assert.assertEquals(200, Whitebox.invokeMethod<Void>(animateSpeakerLayout, "getChildHeight"))

            viewUtilsMock.reset()
            viewUtilsMock.close()
        }
    }

    @Test
    fun verify_data_when_getChildWidth() {
        val animateSpeakerLayout = AnimateSpeakerLayout(mContext!!)
        animateSpeakerLayout.post {
            val textView = TextView(mContext!!)
            Assert.assertEquals(0, Whitebox.invokeMethod<Void>(animateSpeakerLayout, "getChildWidth"))

            val viewUtilsMock = Mockito.mockStatic(ViewUtils::class.java)

            val view = Mockito.mock(View::class.java)
            Mockito.`when`(view.measuredWidth).thenReturn(0)
            Mockito.`when`(view.measuredHeight).thenReturn(0)
//        Mockito.`when`(view.getUnDisplayViewSize()).thenReturn(Size(100, 100))
            viewUtilsMock.`when`<Size> { view.getUnDisplayViewSize() }.thenReturn(Size(100, 100))
            animateSpeakerLayout.addView(textView)
            animateSpeakerLayout.addView(view)

            Mockito.`when`(view.measuredHeight).thenReturn(200)
            Mockito.`when`(view.measuredWidth).thenReturn(200)

            viewUtilsMock.reset()
            viewUtilsMock.close()
        }
    }

    @Test
    fun verify_data_when_showAnimate() {
        val linearLayout = LinearLayout(mContext!!)
        val animateSpeakerLayout =
            AnimateSpeakerLayout(mContext!!)

        val textView = TextView(mContext!!)
        val textView2 = TextView(mContext!!)
        animateSpeakerLayout.addView(textView)
        animateSpeakerLayout.addView(textView2)
        linearLayout.addView(animateSpeakerLayout)
        animateSpeakerLayout.post {
            animateSpeakerLayout.showAnimate()
            Assert.assertTrue((Whitebox.getInternalState(animateSpeakerLayout, "showAnimator") as ValueAnimator).isStarted)
            animateSpeakerLayout.dismissAnimate()
            Assert.assertTrue((Whitebox.getInternalState(animateSpeakerLayout, "dismissAnimator") as ValueAnimator).isStarted)
        }
    }

    @Test
    fun verify_data_when_switchSpeaker() {
        val linearLayout = LinearLayout(mContext!!)
        val animateSpeakerLayout = AnimateSpeakerLayout(mContext!!)

        val textView = TextView(mContext!!)
        val textView2 = TextView(mContext!!)

        animateSpeakerLayout.post {
            animateSpeakerLayout.switchSpeaker(true)

            animateSpeakerLayout.addView(textView)
            animateSpeakerLayout.addView(textView2)
            linearLayout.addView(animateSpeakerLayout)
            animateSpeakerLayout.switchSpeaker(flag = true)
            Assert.assertEquals(1f, textView2.alpha)

            animateSpeakerLayout.switchSpeaker(flag = true)
            Assert.assertEquals(1f, textView2.alpha)

            animateSpeakerLayout.switchSpeaker(flag = false)
            Assert.assertEquals(0f, textView2.alpha)
        }
    }

    @Test
    fun verify_data_when_onFinishInflate() {
        val linearLayout = LinearLayout(mContext!!)
        val animateSpeakerLayout = AnimateSpeakerLayout(mContext!!)

        val textView = TextView(mContext!!)
        val textView2 = TextView(mContext!!)
        animateSpeakerLayout.post {
            animateSpeakerLayout.switchSpeaker(true)
            animateSpeakerLayout.addView(textView)
            animateSpeakerLayout.addView(textView2)
            linearLayout.addView(animateSpeakerLayout)
            Whitebox.invokeMethod<Void>(animateSpeakerLayout, "onFinishInflate")
            Whitebox.invokeMethod<Void>(animateSpeakerLayout, "onFinishInflate")
            Assert.assertEquals(3, animateSpeakerLayout.childCount)
        }
    }

    @Test
    fun verify_data_when_onMeasure() {
        val linearLayout = LinearLayout(mContext!!)
        val animateSpeakerLayout = AnimateSpeakerLayout(mContext!!)

        val textView = TextView(mContext!!)
        textView.width = 100
        textView.height = 100
        val textView2 = TextView(mContext!!)
        animateSpeakerLayout.post {
            animateSpeakerLayout.switchSpeaker(true)
            animateSpeakerLayout.addView(textView)
            animateSpeakerLayout.addView(textView2)
            linearLayout.addView(animateSpeakerLayout)
            Whitebox.invokeMethod<Void>(animateSpeakerLayout, "onMeasure", 100, 100)
            Assert.assertEquals(100, animateSpeakerLayout.measuredWidth)
            Assert.assertEquals(100, animateSpeakerLayout.measuredHeight)
        }
    }
}