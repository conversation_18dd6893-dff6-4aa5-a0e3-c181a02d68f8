/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert;

import android.content.Context;
import android.os.Build;
import android.widget.Toast;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.playback.PlaybackActivity;
import com.soundrecorder.playback.newconvert.convert.ConvertManagerImpl;
import com.soundrecorder.playback.shadows.ShadowCOUIVersionUtil;
import com.soundrecorder.playback.shadows.ShadowFeatureOption;
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.android.controller.ActivityController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;
import org.robolectric.shadows.ShadowToast;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class,
        ShadowFeatureOption.class, ShadowCOUIVersionUtil.class})
public class PlaybackConvertFragmentTest {
    private ActivityController<PlaybackActivity> mActivityController;
    private PlaybackActivity mActivity;
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        ShadowLog.stream = System.out;
        mActivityController = Robolectric.buildActivity(PlaybackActivity.class);
        mActivity = mActivityController.get();
    }

    @After
    public void tearDown() {
        mContext = null;
        mActivity = null;
        mActivityController = null;
        mActivity = null;
    }

    @Test
    public void should_notNull_when_initConvertManager() throws Exception {
        PlaybackConvertFragment playbackConvertFragment = Mockito.mock(PlaybackConvertFragment.class);
        Whitebox.invokeMethod(playbackConvertFragment, "initConvertManager");
        ConvertManagerImpl mConvertManagerImpl = Whitebox.getInternalState(playbackConvertFragment, "mConvertManagerImpl");
        Assert.assertNotNull(mConvertManagerImpl);
    }

    @Test
    public void should_showToast_when_toastSpeaker() throws Exception {
        mActivityController.create().start().resume().get();
        PlaybackConvertFragment playbackConvertFragment = Mockito.mock(PlaybackConvertFragment.class);
        Whitebox.invokeMethod(playbackConvertFragment, "toastSpeakerNumberIsOne");
        Toast toast = ShadowToast.getLatestToast();
        Assert.assertNotNull(toast);
        Whitebox.invokeMethod(playbackConvertFragment, "toastSpeakerNumberOverFour");
        Toast toast2 = ShadowToast.getLatestToast();
        Assert.assertNotNull(toast2);
        mActivityController.stop().destroy();
    }
}
