/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.playback.newconvert.exportconvert.txt

import android.content.Context
import android.os.Build
import android.os.Environment
import androidx.lifecycle.MutableLiveData
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.MakeFileUtils
import kotlinx.coroutines.*
import com.soundrecorder.playback.R
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOplusUsbEnvironment
import org.junit.*
import org.junit.runner.RunWith
import org.mockito.InjectMocks
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import java.io.File

@RunWith(AndroidJUnit4::class)
@PrepareForTest(SaveTxtUtilTest::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOplusUsbEnvironment::class, ShadowFeatureOption::class]
)
class SaveTxtUtilTest {
    private var mContext: Context? = null
    private val saveTxtUtil: SaveTxtUtil = SaveTxtUtil.instance
    private var fileName = "标准录音 1"
    private var folderPath = "testFolder/"

    @InjectMocks
    private val mCallback: SaveToLocalCallback = object : SaveToLocalCallback {
        override fun onShowSaveFileWaitingDialog() {
            println("onShowSaveFileWaitingDialog")
        }

        override fun onGetFileName(fileName: String, fileAbsPath: String) {
            println("onGetFileName")
        }

        override fun onSaveSuccess(fileName: String, fileAbsPath: String) {
            println("onSaveSuccess")
        }

        override fun onSaveFailed(message: String) {
            println("onSaveFailed")
        }
    }

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        val file = File("testFolder/")
        try {
            if (!file.exists()) {
                file.mkdir()
            }
        } catch (e: Exception) {
        }
    }

    @After
    fun tearDown() {
        try {
            MakeFileUtils.delFolder("testFolder/")
        } catch (e: Exception) {
        }
        try {
            File("标准录音 1.txt").delete()
        } catch (e: Exception) {
        }
    }

    @Test
    fun saveTxtToLocalTest() {
        val viewModel = initViewModel()
        val folderPath = "testFolder/"
        val originalFileName = "标准录音 1"
        val createDate = "2021-11-10"
        var convertFileSize = 51200L

        viewModel.setOnSaveTxtToLocalResultCallback(null)

        val globalScope = GlobalScope

        saveTxtUtil.saveTxtToLocal(viewModel, folderPath, originalFileName, createDate, convertFileSize, null, globalScope)

        saveTxtUtil.saveTxtToLocal(viewModel, folderPath, originalFileName, createDate, convertFileSize, mCallback, globalScope)

        convertFileSize = 1L
        saveTxtUtil.saveTxtToLocal(viewModel, folderPath, originalFileName, createDate, convertFileSize, mCallback, globalScope)
    }

    @Test
    fun setSaveResultTest() {
        val success = true
        val message = ""

        Whitebox.setInternalState(saveTxtUtil, "needShowDialog", true)
        Whitebox.setInternalState(saveTxtUtil, "mShowDialogDuration", System.currentTimeMillis())
        Whitebox.setInternalState(saveTxtUtil, "mSaveTxtToLocalFileName", "mSaveTxtToLocalFileName")
        Whitebox.setInternalState(saveTxtUtil, "mSaveTxtToLocalAbsPath", "mSaveTxtToLocalAbsPath")
        Whitebox.setInternalState(saveTxtUtil, "mCallback", mCallback)

        GlobalScope.launch {
            saveTxtUtil.setSaveResult(success, message)

            saveTxtUtil.setSaveResult(false, message)
        }
    }

    @Ignore
    @Test
    fun checkFolderExistTest() {
        var folderPath = ""
        var invokeMethod = Whitebox.invokeMethod<Boolean>(saveTxtUtil, "checkFolderExist", folderPath)
        Assert.assertEquals(false, invokeMethod)
        folderPath = "testFolder/"
        invokeMethod = Whitebox.invokeMethod(saveTxtUtil, "checkFolderExist", folderPath)
        Assert.assertEquals(true, invokeMethod)
        //判断异常场景
        folderPath = "?"
        invokeMethod = Whitebox.invokeMethod(saveTxtUtil, "checkFolderExist", folderPath)
        Assert.assertEquals(false, invokeMethod)
    }

    @Test
    fun getTmpFileNameTest() {
        val originalFileName = "test"
        val createDate = "2021-11-9 18-49-53"
        val getTmpFileName = Whitebox.invokeMethod<String>(saveTxtUtil, "getTmpFileName", originalFileName, createDate)
        val string = BaseApplication.getAppContext().resources.getString(
            com.soundrecorder.common.R.string.export_save_file_name,
            "$originalFileName-$createDate"
        )
        Assert.assertEquals(string, getTmpFileName)
    }

    @Test
    fun getRealFileNameTest() {
        fileName = "getRealFileNameTest"
        Assert.assertEquals(fileName, Whitebox.invokeMethod<String>(saveTxtUtil, "getRealFileName", folderPath, fileName))

        fileName = "getRealFileNameTest?"
        Assert.assertEquals(fileName, Whitebox.invokeMethod<String>(saveTxtUtil, "getRealFileName", folderPath, fileName))
    }

    @Test
    fun writeFileToFolderTest1() {
        val viewModel = initViewModel()
        fileName = ""
        folderPath = ""
        Assert.assertEquals(false, Whitebox.invokeMethod<Boolean>(saveTxtUtil, "writeFileToFolder", viewModel, folderPath, fileName))
        fileName = "hhh"
        folderPath = ""
        Assert.assertEquals(false, Whitebox.invokeMethod<Boolean>(saveTxtUtil, "writeFileToFolder", viewModel, folderPath, fileName))
        fileName = ""
        folderPath = "jjj"
        Assert.assertEquals(false, Whitebox.invokeMethod<Boolean>(saveTxtUtil, "writeFileToFolder", viewModel, folderPath, fileName))

        fileName = "标准录音 1"
        folderPath = "testFolder/"

        Assert.assertEquals(true, Whitebox.invokeMethod<Boolean>(saveTxtUtil, "writeFileToFolder", viewModel, folderPath, fileName))
    }

    @Test
    fun writeFileToFolderTest2() {
        val viewModel = PowerMockito.mock(ShareWithTxtViewModel::class.java)
        val spy = PowerMockito.spy(saveTxtUtil)
        //doReturn不执行方法直接返回结果，thenReturn要先执行方法
        Assert.assertEquals(true, Whitebox.invokeMethod<Boolean>(spy, "writeFileToFolder", viewModel, folderPath, fileName))
    }

    @Test
    fun writeFileToFolderTest3() {
        val viewModel = PowerMockito.mock(ShareWithTxtViewModel::class.java)
        //doReturn不执行方法直接返回结果，thenReturn要先执行方法
        val writeFileToFolder = PowerMockito.method(
            SaveTxtUtil::class.java,
            "writeFileToFolder",
            ShareWithTxtViewModel::class.java,
            String::class.java,
            String::class.java
        )
        //模拟发生异常
        PowerMockito.`when`(viewModel.mTitleLiveData).thenThrow(NullPointerException())
        val invoke = writeFileToFolder.invoke(saveTxtUtil, viewModel, folderPath, fileName)
        Assert.assertEquals(false, invoke)
    }

    @Test
    fun getTxtFileContentTest() {
        val getTxtFileContent = PowerMockito.method(SaveTxtUtil::class.java, "getTxtFileContent", ShareWithTxtViewModel::class.java)
        var result = getTxtFileContent.invoke(saveTxtUtil, PowerMockito.mock(ShareWithTxtViewModel::class.java))
        Assert.assertEquals("", result)

        val viewModel = initViewModel()
        val lineSeparator = System.lineSeparator()

        //不显示分段格式
        result = getTxtFileContent.invoke(saveTxtUtil, viewModel)
        var actualString = "title$lineSeparator$lineSeparator" +
                "date$lineSeparator" +
                "subject$lineSeparator" +
                lineSeparator +
                "textContent"
        Assert.assertEquals(actualString, result)

        //只显示分段
        PowerMockito.`when`(viewModel.mShowLineLiveData).thenReturn(MutableLiveData(true))
        result = getTxtFileContent.invoke(saveTxtUtil, viewModel)
        actualString = "title$lineSeparator$lineSeparator" +
                "date$lineSeparator" +
                "subject$lineSeparator" +
                lineSeparator +
                "textContent$lineSeparator$lineSeparator"
        Assert.assertEquals(actualString, result)

        //显示分段和讲话人
        PowerMockito.`when`(viewModel.mShowSpeakerLiveData).thenReturn(MutableLiveData(true))
        result = getTxtFileContent.invoke(saveTxtUtil, viewModel)
        actualString = "title$lineSeparator$lineSeparator" +
                "date$lineSeparator" +
                "subject$lineSeparator" +
                "roles$lineSeparator" +
                lineSeparator +
                "roleName1$lineSeparator" +
                "textContent$lineSeparator$lineSeparator"
        Assert.assertEquals(actualString, result)


        //显示分段和时间
        PowerMockito.`when`(viewModel.mShowSpeakerLiveData).thenReturn(MutableLiveData(false))
        PowerMockito.`when`(viewModel.mShowDateLiveData).thenReturn(MutableLiveData(true))
        result = getTxtFileContent.invoke(saveTxtUtil, viewModel)
        actualString = "title$lineSeparator$lineSeparator" +
                "date$lineSeparator" +
                "subject$lineSeparator" +
                lineSeparator +
                "00:00$lineSeparator" +
                "textContent$lineSeparator$lineSeparator"
        Assert.assertEquals(actualString, result)

        //显示分段和讲话人和时间
        PowerMockito.`when`(viewModel.mShowSpeakerLiveData).thenReturn(MutableLiveData(true))
        result = getTxtFileContent.invoke(saveTxtUtil, viewModel)
        actualString = "title$lineSeparator$lineSeparator" +
                "date$lineSeparator" +
                "subject$lineSeparator" +
                "roles$lineSeparator$lineSeparator" +
                "roleName1  00:00$lineSeparator" +
                "textContent$lineSeparator$lineSeparator"
        Assert.assertEquals(actualString, result)

        //模拟发生异常
        PowerMockito.`when`(viewModel.mItemsListLiveData).thenThrow(NullPointerException())
        result = getTxtFileContent.invoke(saveTxtUtil, viewModel)
        Assert.assertEquals("", result)
    }

    @Ignore
    @Test
    fun doWriteFileTest() {
        var absPath = ""
        val txtString = "content"

        Assert.assertEquals(false, Whitebox.invokeMethod(saveTxtUtil, "doWriteFile", absPath, txtString))
        absPath = "testFolder/?.txt"
        Assert.assertEquals(false, Whitebox.invokeMethod(saveTxtUtil, "doWriteFile", absPath, txtString))
        absPath = "标准录音 1.txt"
        Assert.assertEquals(true, Whitebox.invokeMethod(saveTxtUtil, "doWriteFile", absPath, txtString))
    }

    @Test
    fun updateMediaDatabaseTest() {
        var path = ""
        saveTxtUtil.updateMediaDatabase(BaseApplication.getAppContext(), path)

        path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).absolutePath + File.separator +
                "SoundRecordDoc"
        saveTxtUtil.updateMediaDatabase(BaseApplication.getAppContext(), path)
    }

    private fun initViewModel(): ShareWithTxtViewModel {
        val viewModel = PowerMockito.mock(ShareWithTxtViewModel::class.java)
        PowerMockito.`when`(viewModel.mTitleLiveData).thenReturn(MutableLiveData("title"))
        PowerMockito.`when`(viewModel.mDateLiveData).thenReturn(MutableLiveData("date"))
        PowerMockito.`when`(viewModel.mSubjectLiveData).thenReturn(MutableLiveData("subject"))
        PowerMockito.`when`(viewModel.mRolesStringLiveData).thenReturn(MutableLiveData("roles"))

        PowerMockito.`when`(viewModel.mShowLineLiveData).thenReturn(MutableLiveData(false))
        PowerMockito.`when`(viewModel.mShowDateLiveData).thenReturn(MutableLiveData(false))
        PowerMockito.`when`(viewModel.mShowSpeakerLiveData).thenReturn(MutableLiveData(false))
        PowerMockito.`when`(viewModel.mContentStringLiveData).thenReturn(MutableLiveData("textContent"))

        PowerMockito.`when`(viewModel.getCanShowSpeakerRole()).thenReturn(true)

        val mutableListOf = mutableListOf<ConvertContentItem>()
        val convertContentItem = ConvertContentItem()
        convertContentItem.roleName = "roleName1"
        convertContentItem.textContent = "textContent"
        mutableListOf.add(convertContentItem)
        PowerMockito.`when`(viewModel.mItemsListLiveData).thenReturn(MutableLiveData(mutableListOf))
        return viewModel
    }
}