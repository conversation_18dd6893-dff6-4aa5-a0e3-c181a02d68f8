/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/7/26
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.playback.newconvert.view

import android.os.Build
import android.text.SpannableStringBuilder
import android.view.View
import com.soundrecorder.base.utils.DebugUtil
import io.mockk.*
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.playback.newconvert.ui.SeekPlayActionModeCallback
import com.soundrecorder.playback.shadows.ShadowFeatureOption
import com.soundrecorder.playback.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import org.mockito.Mockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.robolectric.annotation.Config

@RunWith(JUnit4::class)
@PrepareForTest(BackGroundTextViewSetupHelper::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class BackGroundTextViewSetupHelperTest {

    private val listener = object : BackGroundTextViewSetupHelper.OnBlackGroundTextClickListenner {
        override fun onTextViewClick(
            view: View,
            convertContentItem: ConvertContentItem?,
            currentItemIndex: Int
        ) {
            println("=========onTextViewClick==========")
        }
    }
    private val callback = SeekPlayActionModeCallback()

    @Before
    fun setUp() {
        mockkStatic(DebugUtil::class)
        justRun { DebugUtil.i(any(), any()) }
        justRun { DebugUtil.i(any(), any(), any()) }
    }

    @After
    fun release() {
        unmockkAll()
    }

    @Test
    @Suppress("LongMethod")
    fun `assert value when call setUpBackgroundTextView`() {
        val helper = BackGroundTextViewSetupHelper()
        val contentItem = Mockito.mock(ConvertContentItem::class.java)
        val textItemMetaData = Mockito.mock(ConvertContentItem.TextItemMetaData::class.java)
        val backgroundTextView = Mockito.mock(BackgroundTextView::class.java)
        val drawAttr = Mockito.mock(TextImageMixLayoutHelper.TextImageMixLayoutDrawAttr::class.java)

        val mockStatic = Mockito.mockStatic(
            ImageTextItemLayoutParamUtil.setTopMarginsForBackGroundTextView(
                true,
                drawAttr,
                backgroundTextView
            )::class.java
        ) {
            Mockito.doNothing().`when`(
                ImageTextItemLayoutParamUtil.setTopMarginsForBackGroundTextView(
                    true,
                    drawAttr,
                    backgroundTextView
                )
            )
        }

        helper.setUpBackgroundTextView(0, null, contentItem, backgroundTextView, true,
            isLastOne = false,
            searchMode = false,
            callback = callback,
            onClickListener = listener,
            drawAttr = drawAttr
        )
        helper.setUpBackgroundTextView(0, textItemMetaData, null, backgroundTextView, true,
            isLastOne = false,
            searchMode = false,
            callback = callback,
            onClickListener = listener,
            drawAttr = drawAttr
        )
        helper.setUpBackgroundTextView(0, textItemMetaData, contentItem, null, true,
            isLastOne = false,
            searchMode = false,
            callback = callback,
            onClickListener = listener,
            drawAttr = drawAttr
        )
        helper.setUpBackgroundTextView(0, textItemMetaData, contentItem, backgroundTextView, true,
            isLastOne = false,
            searchMode = false,
            callback = null,
            onClickListener = listener,
            drawAttr = drawAttr
        )
        helper.setUpBackgroundTextView(0, textItemMetaData, contentItem, backgroundTextView, true,
            isLastOne = false,
            searchMode = false,
            callback = callback,
            onClickListener = null,
            drawAttr = drawAttr
        )
        helper.setUpBackgroundTextView(0, textItemMetaData, contentItem, backgroundTextView, true,
            isLastOne = false,
            searchMode = false,
            callback = callback,
            onClickListener = listener,
            drawAttr = drawAttr
        )
        backgroundTextView.performClick()
        mockStatic.reset()
        mockStatic.close()
    }

    @Test
    fun `assert value when call sentenceOfContentUnderLine`() {
        val helper = BackGroundTextViewSetupHelper()
        val contentItem = mockk<ConvertContentItem>()
        val textItemMetaData = mockk<ConvertContentItem.TextItemMetaData>()
        val backgroundTextView = mockk<BackgroundTextView>()

        justRun { backgroundTextView.setIsUnderLine(any(), any()) }

        every { textItemMetaData.textParagraph } answers { null }
        helper.sentenceOfContentUnderLine(backgroundTextView, textItemMetaData, contentItem, 0)

        every { textItemMetaData.textParagraph } answers { mutableListOf() }
        helper.sentenceOfContentUnderLine(backgroundTextView, textItemMetaData, contentItem, 0)

        val sentence = mockk<ConvertContentItem.SubSentence>()
        every { textItemMetaData.textParagraph } answers { mutableListOf(sentence) }

        every { textItemMetaData.hasTextMark() } answers { false }
        helper.sentenceOfContentUnderLine(backgroundTextView, textItemMetaData, contentItem, 0)

        every { textItemMetaData.hasTextMark() } answers { true }
        every { sentence.onlyHasSimpleMark } answers { false }
        helper.sentenceOfContentUnderLine(backgroundTextView, textItemMetaData, contentItem, 0)

        every { sentence.onlyHasSimpleMark } answers { true }
        every { sentence.startCharSeq } answers { 0 }
        every { sentence.endCharSeq } answers { 10 }
        every { contentItem.getTextStringLengthBeforeTextImageItemIndex(any()) } answers { 1 }
        helper.sentenceOfContentUnderLine(backgroundTextView, textItemMetaData, contentItem, 0)
    }

    @Test
    fun `assert value when call  setItemContent`() {
        val textItemMetaData = mockk<ConvertContentItem.TextItemMetaData>() {
            every { getTextString() } answers { "testString" }
            every { textParagraph } answers { null }
        }
        val backgroundTextView = mockk<BackgroundTextView>() {
//            justRun { text = Mockito.any(SpannableStringBuilder::class.java) }
            justRun { text = null }
        }
        val helper = spyk<BackGroundTextViewSetupHelper>() {
            justRun { setItemContentSpannable(any(), any(), any(), any()) }
        }
        helper.setItemContent(backgroundTextView, null)
        helper.setItemContent(backgroundTextView, textItemMetaData)
    }

    @Test
    fun `assert value when call setItemContentSpannable`() {
        val helper = BackGroundTextViewSetupHelper()
        val textItemMetaData = mockk<ConvertContentItem.TextItemMetaData> {
            every { getTextString() } answers { "testString" }
        }
        val builder = SpannableStringBuilder()
        val backgroundTextView = mockk<BackgroundTextView> {
            every { context } answers { mockk() }
//            justRun { text = Mockito.any(SpannableStringBuilder::class.java) }
            justRun { text = builder }
//            justRun { text = Mockito.any() }
            justRun { updateHighLight(any(), any(), any(), any()) }
        }

        helper.setItemContentSpannable(builder, backgroundTextView, null)

        every { textItemMetaData.textParagraph } answers { null }
        helper.setItemContentSpannable(builder, backgroundTextView, textItemMetaData)

        every { textItemMetaData.textParagraph } answers { mutableListOf() }
        helper.setItemContentSpannable(builder, backgroundTextView, textItemMetaData)

        val sentence = mockk<ConvertContentItem.SubSentence>()
        val sentence2 = mockk<ConvertContentItem.SubSentence>()
        every { sentence.onlyHasSimpleMark } answers { false }
        every { sentence.text } answers { "testStr1" }
        every { sentence2.onlyHasSimpleMark } answers { true }
        every { sentence2.text } answers { "testStr2" }
        every { textItemMetaData.textParagraph } answers { mutableListOf(sentence, sentence2) }
        helper.setItemContentSpannable(builder, backgroundTextView, textItemMetaData)

        every { backgroundTextView.getHighLight() } answers { true }
        helper.setItemContentSpannable(builder, backgroundTextView, textItemMetaData, false)

        every { backgroundTextView.getHighLight() } answers { false }
        helper.setItemContentSpannable(builder, backgroundTextView, textItemMetaData, true)

        every { backgroundTextView.getHighLight() } answers { true }
        every { backgroundTextView.getStartHighLightSeq() } answers { 10 }
        every { backgroundTextView.getEndHighLightSeq() } answers { 1 }
        helper.setItemContentSpannable(builder, backgroundTextView, textItemMetaData, true)

        every { backgroundTextView.getStartHighLightSeq() } answers { 1 }
        every { backgroundTextView.getEndHighLightSeq() } answers { 10 }
        helper.setItemContentSpannable(builder, backgroundTextView, textItemMetaData, true)

        every { backgroundTextView.getStartHighLightSeq() } answers { 10 }
        every { backgroundTextView.getEndHighLightSeq() } answers { 10 }
        helper.setItemContentSpannable(builder, backgroundTextView, textItemMetaData, true)
    }

    @Test
    fun `assert value when call switchSentenceBackground`() {
        //switchSentenceBackground
        val helper = BackGroundTextViewSetupHelper()
        val backgroundTextView = mockk<BackgroundTextView>() {
            justRun { updateHighLight(any(), any(), any(), any()) }
            justRun { background = null }
            justRun { background = any() }
        }
        val contentItem =
            mockk<ConvertContentItem> { every { getTextStringLengthBeforeTextImageItemIndex(any()) } answers { 0 } }
        val textItemMetaData = mockk<ConvertContentItem.TextItemMetaData>()

        helper.switchSentenceBackground(backgroundTextView, null, null, 0, false)
        helper.switchSentenceBackground(backgroundTextView, contentItem, null, 0, false)
        helper.switchSentenceBackground(backgroundTextView, null, textItemMetaData, 0, false)
        every { textItemMetaData.isFocuse() } answers { false }
        helper.switchSentenceBackground(backgroundTextView, contentItem, textItemMetaData, 0, false)

        every { textItemMetaData.isFocuse() } answers { false }
        helper.switchSentenceBackground(backgroundTextView, contentItem, textItemMetaData, 0, true)

        every { textItemMetaData.isFocuse() } answers { true }
        helper.switchSentenceBackground(backgroundTextView, contentItem, textItemMetaData, 0, true)

        every { textItemMetaData.isFocuse() } answers { true }
        every { textItemMetaData.textParagraph } answers { mutableListOf() }
        helper.switchSentenceBackground(backgroundTextView, contentItem, textItemMetaData, 0, false)


        val sentence = mockk<ConvertContentItem.SubSentence>()
        val sentence2 = mockk<ConvertContentItem.SubSentence>()
        val sentence3 = mockk<ConvertContentItem.SubSentence>()
        every { sentence.onlyHasSimpleMark } answers { false }
        every { sentence.isFocused } answers { false }
        every { sentence.text } answers { "testStr1" }
        every { sentence.startCharSeq } answers { 0 }
        every { sentence.endCharSeq } answers { 10 }

        every { sentence2.onlyHasSimpleMark } answers { true }
        every { sentence2.isFocused } answers { false }
        every { sentence2.text } answers { "testStr2" }
        every { sentence2.startCharSeq } answers { 0 }
        every { sentence2.endCharSeq } answers { 10 }

        every { sentence3.onlyHasSimpleMark } answers { true }
        every { sentence3.isFocused } answers { true }
        every { sentence3.text } answers { "testStr3" }
        every { sentence3.startCharSeq } answers { 0 }
        every { sentence3.endCharSeq } answers { 10 }
        every { textItemMetaData.textParagraph } answers {
            mutableListOf(
                sentence,
                sentence2,
                sentence3
            )
        }
        every { backgroundTextView.hasFlagSpan(any(), any()) } answers { false }
        //helper.switchSentenceBackground(backgroundTextView, contentItem, textItemMetaData, 2, false)

        every { backgroundTextView.hasFlagSpan(any(), any()) } answers { true }
        // helper.switchSentenceBackground(backgroundTextView, contentItem, textItemMetaData, 2, false)
    }
}