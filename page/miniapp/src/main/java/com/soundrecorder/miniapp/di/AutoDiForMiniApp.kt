/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForMiniApp.kt
 * * Description : AutoDiForMiniApp
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.miniapp.di

import com.soundrecorder.miniapp.api.MiniRecorderApi
import com.soundrecorder.modulerouter.miniapp.MiniAppInterface
import org.koin.dsl.module

object AutoDiForMiniApp {
    val miniAppModule = module {
        single<MiniAppInterface>(createdAtStart = true) {
            MiniRecorderApi
        }
    }
}