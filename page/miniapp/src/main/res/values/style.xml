<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="MiniAppCardButton">
        <attr name="pressForeGroundColor" format="color" />
        <attr name="disabledPressAlpha" format="float" />
        <attr name="enablePressAlpha" format="float" />
    </declare-styleable>

    <style name="Button_Mini_Recorder">
        <item name="android:background">@null</item>
        <item name="pressForeGroundColor">@color/black_color</item>
        <item name="disabledPressAlpha">0.06</item>
        <item name="enablePressAlpha">0.12</item>
    </style>
</resources>