<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FAFAFA"
    android:forceDarkAllowed="false">

    <RelativeLayout
        android:id="@+id/toolbarLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp50"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginStart="@dimen/dp2"
        android:layout_marginEnd="@dimen/dp16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="invisible">

        <ImageView
            android:id="@+id/imageRecorderClose"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:src="@drawable/color_menu_ic_cancel_normal"
            android:contentDescription="@string/abc_action_bar_up_description"
            android:padding="@dimen/dp14" />

        <TextView
            android:id="@+id/tvRecorderTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:layout_centerInParent="true"
            android:gravity="center"
            android:textColor="@color/text_color_mini"
            android:fontFamily="sans-serif-medium"
            android:textSize="@dimen/dp16"
            android:layout_marginStart="@dimen/dp30"
            android:layout_marginEnd="@dimen/dp30" />

    </RelativeLayout>

    <TextView
        android:id="@+id/tvRecorderName"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp50"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginTop="@dimen/dp8"
        android:layout_marginEnd="@dimen/dp16"
        android:ellipsize="end"
        android:gravity="start|center_vertical"
        android:maxLines="1"
        android:text="@string/app_name_main"
        android:textColor="@color/text_color_mini"
        android:textFontWeight="1000"
        android:fontFamily="sans-serif-medium"
        android:textStyle="bold"
        android:textSize="@dimen/dp20"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/waveLayout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp100"
        android:layoutDirection="ltr"
        android:background="@color/wave_background_color"
        app:layout_constraintTop_toBottomOf="@id/tvRecorderName">

        <com.soundrecorder.miniapp.view.wave.WaveRecyclerView
            android:id="@+id/waveRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:ignore="SpeakableTextPresentCheck"
            />

        <View
            android:layout_width="2px"
            android:layout_height="match_parent"
            android:background="@color/wave_center_line_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvTimeText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:layout_marginTop="38dp"
        android:gravity="center"
        android:includeFontPadding="false"
        android:lines="1"
        android:text="00:00.00"
        android:textColor="@color/text_color_mini"
        android:textFontWeight="1000"
        android:fontFamily="sys-sans-en"
        android:fontFeatureSettings="tnum"
        android:textStyle="bold"
        android:textSize="@dimen/default_height"
        app:layout_constraintTop_toBottomOf="@+id/waveLayout" />

    <TextView
        android:id="@+id/tvStateText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/dp12"
        android:layout_marginTop="@dimen/dp4"
        android:ellipsize="end"
        android:gravity="center_horizontal|top"
        android:includeFontPadding="false"
        android:lines="2"
        android:textColor="@color/text_color_mini"
        android:textFontWeight="400"
        android:textSize="@dimen/dp14"
        app:layout_constraintTop_toBottomOf="@+id/tvTimeText" />

    <com.soundrecorder.miniapp.view.button.AppCardButton
        android:id="@+id/btnAddTextMark"
        style="@style/Button_Mini_Recorder"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:alpha="0"
        android:contentDescription="@string/talkback_flag"
        android:elevation="0dp"
        android:src="@drawable/icon_mark_normal"
        android:textSize="0sp"
        app:layout_constraintBottom_toBottomOf="@id/btnSwitchState"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/btnSwitchState"
        tools:contentDescription="添加标记" />

    <com.soundrecorder.miniapp.view.button.AppCardButton
        android:id="@+id/btnSaveFile"
        style="@style/Button_Mini_Recorder"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48"
        android:alpha="0"
        android:contentDescription="@string/rename_save"
        android:elevation="0dp"
        android:src="@drawable/icon_save_normal"
        android:textSize="0sp"
        app:layout_constraintBottom_toBottomOf="@id/btnSwitchState"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/btnSwitchState" />

    <com.soundrecorder.common.widget.glow.ShadowImageView
        android:layout_width="@dimen/dp56"
        android:layout_height="@dimen/dp56"
        android:background="@color/color_transparent"
        android:translationZ="@dimen/dp0"
        app:shadow_blur_radius="@dimen/dp28"
        app:layout_constraintTop_toTopOf="@id/btnSwitchState"
        app:layout_constraintBottom_toBottomOf="@id/btnSwitchState"
        app:layout_constraintStart_toStartOf="@id/btnSwitchState"
        app:layout_constraintEnd_toEndOf="@id/btnSwitchState"
        app:shadow_offsetY="@dimen/dp14"
        app:shadow_offsetX="@dimen/dp0"
        app:shadow_reference="@id/btnSwitchState"
        app:shadow_color="@color/record_btn_shadow_red_color"/>

    <com.soundrecorder.miniapp.view.button.AppCardButton
        android:id="@+id/btnSwitchState"
        style="@style/Button_Mini_Recorder"
        android:layout_width="@dimen/dp56"
        android:layout_height="@dimen/dp56"
        android:layout_marginBottom="@dimen/dp32"
        android:elevation="@dimen/dp1"
        android:includeFontPadding="false"
        android:src="@drawable/icon_record_state_normal"
        android:textSize="0sp"
        app:animEnable="true"
        app:animType="0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
