/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecorderViewAnimUtilTeset
 * Description:
 * Version: 1.0
 * Date: 2023/4/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/4/18 1.0 create
 */

package com.soundrecorder.miniapp

import android.content.Context
import android.os.Build
import android.os.SystemClock
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.miniapp.shadow.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class RecorderViewAnimUtilTest {

    private var mContext: Context? = null
    private var mockStaticSystemClock: MockedStatic<SystemClock>? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mockStaticSystemClock = Mockito.mockStatic(SystemClock::class.java).apply {
            `when`<Long> { SystemClock.elapsedRealtime() }.thenReturn(10000)
        }
    }

    @After
    fun release() {
        mContext = null
        mockStaticSystemClock?.close()
        mockStaticSystemClock = null
    }

    @Test
    fun should_correct_when_showMarkAndSaveFileView() {
        val middleView = Mockito.mock(View::class.java)
        val leftView = View(mContext!!)
        val rightView = Mockito.mock(View::class.java)
        Mockito.`when`(middleView.parent).thenReturn(Mockito.mock(ConstraintLayout::class.java))

        val animUtil = RecorderViewAnimUtil(middleView, leftView, rightView)
        animUtil.showMarkAndSaveFileView(false)
        Assert.assertTrue(leftView.alpha == 1F)

        leftView.alpha = 0F
        Whitebox.setInternalState(animUtil, "doShowAnimationTime", SystemClock.elapsedRealtime())
        animUtil.showMarkAndSaveFileView(true)
        Assert.assertFalse(leftView.alpha == 1F)
        Assert.assertTrue(animUtil.isAnimRunning())

        Whitebox.setInternalState(animUtil, "doShowAnimationTime", -1L)
        animUtil.showMarkAndSaveFileView(false)
        Assert.assertTrue(leftView.alpha == 1F)
    }

    @Test
    fun should_correct_when_hideMarkAndSaveFileViewWithAnimation() {
        val middleView = Mockito.mock(View::class.java)
        val leftView = View(mContext!!)
        val rightView = Mockito.mock(View::class.java)
        Mockito.`when`(middleView.parent).thenReturn(Mockito.mock(ConstraintLayout::class.java))

        val animUtil = RecorderViewAnimUtil(middleView, leftView, rightView)
        animUtil.hideMarkAndSaveFileViewWithAnimation(false)
        Assert.assertTrue(leftView.alpha == 0F)

        Whitebox.setInternalState(animUtil, "doHideAnimationTime", SystemClock.elapsedRealtime())
        animUtil.hideMarkAndSaveFileViewWithAnimation(true)
        Assert.assertTrue(leftView.alpha == 0F)
        Assert.assertTrue(animUtil.isAnimRunning())

        leftView.alpha = 1F
        Whitebox.setInternalState(animUtil, "doHideAnimationTime", -1L)
        animUtil.hideMarkAndSaveFileViewWithAnimation(false)
        Assert.assertTrue(leftView.alpha == 0F)

        leftView.alpha = 0.5F
        animUtil.hideMarkAndSaveFileViewWithAnimation(false)
        Assert.assertFalse(leftView.alpha == 0F)
    }
}