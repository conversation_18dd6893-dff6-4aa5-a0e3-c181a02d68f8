/*
File:Base64UtilTest
Description:
Version:
Date:2022/5/27
Author:W9013204

------------------Revision History------------------
<author> <date> <version> <desc>
*/
package com.soundrecorder.base.utils;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.shadows.ShadowFeatureOption;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowFeatureOption.class})
public class Base64UtilTest {

    @Test
    public void verify_value_when_base64Str2Object() throws Exception {
        String s = "123456789";
        String base64Str = Base64Util.object2Base64Str(s);

        Assert.assertNull(Base64Util.base64Str2Object(null));
        Assert.assertEquals(s, Base64Util.base64Str2Object(base64Str));
    }

}