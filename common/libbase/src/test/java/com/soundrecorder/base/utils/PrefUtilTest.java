package com.soundrecorder.base.utils;

import android.content.SharedPreferences;
import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.shadows.ShadowFeatureOption;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowFeatureOption.class})
public class PrefUtilTest {


    @Before
    public void setUp() {
    }

    @After
    public void tearDown() {

    }

    @Test
    public void should_return_not_null_when_getSharedPreferences() {
        SharedPreferences pref = Mockito.mock(SharedPreferences.class);
        Whitebox.setInternalState(PrefUtil.class, "sPref", pref);
        SharedPreferences sharedPreferences = PrefUtil.getSharedPreferences(BaseApplication.getAppContext());
        Assert.assertNotNull(sharedPreferences);

        Whitebox.setInternalState(PrefUtil.class, "sPref", (Object) null);
        SharedPreferences sharedPreferences2 = PrefUtil.getSharedPreferences(BaseApplication.getAppContext());
        Assert.assertNotNull(sharedPreferences2);
    }


    @Test
    public void should_return_not_null_when_getEditor() throws Exception {
        SharedPreferences.Editor ed = Mockito.mock(SharedPreferences.Editor.class);
        Whitebox.setInternalState(PrefUtil.class, "sEditor", ed);
        SharedPreferences.Editor editor = Whitebox.invokeMethod(PrefUtil.class, "getEditor", BaseApplication.getAppContext());
        Assert.assertNotNull(editor);

        Whitebox.setInternalState(PrefUtil.class, "sEditor", (Object) null);
        SharedPreferences.Editor editor2 = Whitebox.invokeMethod(PrefUtil.class, "getEditor", BaseApplication.getAppContext());
        Assert.assertNotNull(editor2);
    }

    @Test
    public void should_return_equals_when_putString() throws Exception {
        Whitebox.setInternalState(PrefUtil.class, "sEditor", (Object) null);
        Whitebox.invokeMethod(PrefUtil.class, "getEditor", BaseApplication.getAppContext());
        String key = "name";
        String value = "Tom";
        PrefUtil.putString(BaseApplication.getAppContext(), key, value);
        String result = PrefUtil.getString(BaseApplication.getAppContext(), key, "");
        Assert.assertEquals(value, result);
    }

    @Test
    public void should_return_equals_when_putStringSet() throws Exception {
        Whitebox.setInternalState(PrefUtil.class, "sEditor", (Object) null);
        Whitebox.invokeMethod(PrefUtil.class, "getEditor", BaseApplication.getAppContext());
        String key = "name";
        Set<String> set = new HashSet<>(Arrays.asList("Tom", "Jack"));
        PrefUtil.putStringSet(BaseApplication.getAppContext(), key, set);
        Set<String> result = PrefUtil.getStringSet(BaseApplication.getAppContext(), key, null);
        Assert.assertEquals(set, result);
    }

    @Test
    public void should_return_equals_when_putLong() throws Exception {
        Whitebox.setInternalState(PrefUtil.class, "sEditor", (Object) null);
        Whitebox.invokeMethod(PrefUtil.class, "getEditor", BaseApplication.getAppContext());
        String key = "name";
        long value = 1000L;
        PrefUtil.putLong(BaseApplication.getAppContext(), key, value);
        long result = PrefUtil.getLong(BaseApplication.getAppContext(), key, 0);
        Assert.assertEquals(value, result);
    }

    @Test
    public void should_return_equals_when_putInt() throws Exception {
        Whitebox.setInternalState(PrefUtil.class, "sEditor", (Object) null);
        Whitebox.invokeMethod(PrefUtil.class, "getEditor", BaseApplication.getAppContext());
        String key = "name";
        int value = 1000;
        PrefUtil.putInt(BaseApplication.getAppContext(), key, value);
        int result = PrefUtil.getInt(BaseApplication.getAppContext(), key, 0);
        Assert.assertEquals(value, result);
    }

    @Test
    public void should_return_equals_when_putFloat() throws Exception {
        Whitebox.setInternalState(PrefUtil.class, "sEditor", (Object) null);
        Whitebox.invokeMethod(PrefUtil.class, "getEditor", BaseApplication.getAppContext());
        String key = "name";
        float value = 1000f;
        PrefUtil.putFloat(BaseApplication.getAppContext(), key, value);
        float result = PrefUtil.getFloat(BaseApplication.getAppContext(), key, 0);
        Assert.assertEquals(value, result, 0);
    }

    @Test
    public void should_return_equals_when_putBoolean() throws Exception {
        Whitebox.setInternalState(PrefUtil.class, "sEditor", (Object) null);
        Whitebox.invokeMethod(PrefUtil.class, "getEditor", BaseApplication.getAppContext());
        String key = "name";
        PrefUtil.putBoolean(BaseApplication.getAppContext(), key, true);
        boolean result = PrefUtil.getBoolean(BaseApplication.getAppContext(), key, false);
        Assert.assertTrue(result);

        PrefUtil.putBoolean(BaseApplication.getAppContext(), key, false);
        boolean result2 = PrefUtil.getBoolean(BaseApplication.getAppContext(), key, true);
        Assert.assertFalse(result2);
    }

    @Test
    public void should_return_equals_when_putObject() throws Exception {
        Whitebox.setInternalState(PrefUtil.class, "sEditor", (Object) null);
        Whitebox.invokeMethod(PrefUtil.class, "getEditor", BaseApplication.getAppContext());
        String key = "name";
        PrefUtil.putObject(BaseApplication.getAppContext(), key, 123);
        int result = (Integer) PrefUtil.getObject(BaseApplication.getAppContext(), key, 0);
        Assert.assertEquals(result, 123);
    }

    @Test
    public void should_return_equals_when_clearPreference() throws Exception {
        PrefUtil.clearPreference(BaseApplication.getAppContext(), "");
        PrefUtil.clearPreference(BaseApplication.getAppContext(), "name");
        SharedPreferences sp = PrefUtil.getSharedPreferences(BaseApplication.getAppContext());
        Map data = sp.getAll();
        Assert.assertEquals(0, data.size());
    }

}
