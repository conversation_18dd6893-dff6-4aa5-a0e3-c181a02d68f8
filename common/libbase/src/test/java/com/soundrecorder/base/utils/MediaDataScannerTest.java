/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : MediaDataScannerTest.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/9/29
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/9/29, LI Kun, create
 ************************************************************/

package com.soundrecorder.base.utils;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.util.ArrayList;

import com.soundrecorder.base.shadows.ShadowFeatureOption;
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class MediaDataScannerTest {

    private MediaDataScanner mMediaDataScanner;
    private Context mContext;

    @Before
    public void setUp() {
        mMediaDataScanner = MediaDataScanner.getInstance();
        mContext = ApplicationProvider.getApplicationContext();
    }

    @Test
    public void should_empty_when_flush() {
        ArrayList<String> paths = Whitebox.getInternalState(mMediaDataScanner, "mPaths");
        for (int i = 0; i <= 50; i++) {
            paths.add("a");
        }
        Whitebox.setInternalState(mMediaDataScanner, "mPaths", paths);
        mMediaDataScanner.add(mContext, "a");
        mMediaDataScanner.flush(mContext);
        ArrayList<String> pathFromScanner = Whitebox.getInternalState(mMediaDataScanner, "mPaths");
        Assert.assertTrue(pathFromScanner.isEmpty());
    }
}
