package com.soundrecorder.base.refresh;

import android.content.Context;
import android.os.Build;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import com.soundrecorder.base.shadows.ShadowFeatureOption;
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.base.shadows.ShadowOplusUsbEnvironment;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOplusUsbEnvironment.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class BounceLayoutTest {

    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_returnTrue_when_setDragDistanceThreshold() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        BaseLoadingView loadingView = new BaseLoadingView(mContext, null, 0) {
            @Override
            public void handleDrag(float dragY) {

            }

            @Override
            public boolean doRefresh() {
                return false;
            }

            @Override
            public boolean isRefreshing() {
                return false;
            }

            @Override
            public void setParent(@Nullable ViewGroup parent) {

            }

            @Override
            public boolean checkRefresh() {
                return false;
            }

            @Override
            public void refreshCompleted() {

            }

            @Override
            public int getLoadingViewHeight() {
                return 0;
            }

            @Override
            public void autoRefresh() {

            }

            @Override
            public void releaseToRefresh() {

            }

            @Override
            public void setRefreshEnable(boolean enable) {

            }
        };
        bounceLayout.setHeaderView(loadingView, new ViewGroup(mContext) {
            @Override
            protected void onLayout(boolean changed, int l, int t, int r, int b) {

            }
        });
        bounceLayout.setMDragDistanceThreshold(100);
        Assert.assertTrue(bounceLayout.getMDragDistanceThreshold() == 100);
        Assert.assertTrue(loadingView.getMDragDistanceThreshold() == 100);
    }

    @Test
    public void should_returnTrue_when_setBounceHandler() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        IBounceHandler handler = new IBounceHandler() {
            @Override
            public boolean canChildDrag(@NotNull View v) {
                return false;
            }

            @Override
            public boolean canChildPull(@NotNull View v) {
                return false;
            }
        };
        bounceLayout.setBounceHandler(handler, new View(mContext));
        Assert.assertTrue(Whitebox.getInternalState(bounceLayout, "mIBounceHandler") != null);
        Assert.assertTrue(Whitebox.getInternalState(bounceLayout, "mChildContent") != null);
    }


    @Test
    public void should_returnTrue_when_setEventForwardingHelper() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        EventForwardingHelper helper = new EventForwardingHelper() {
            @Override
            public boolean notForwarding(float downX, float downY, float moveX, float moveY) {
                return false;
            }
        };
        bounceLayout.setEventForwardingHelper(helper);
        Assert.assertTrue(Whitebox.getInternalState(bounceLayout, "forwardingHelper") != null);
    }

    @Test
    public void should_returnTrue_when_setHeaderView() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        BaseLoadingView loadingView = new BaseLoadingView(mContext,null,0) {
            @Override
            public void handleDrag(float dragY) {

            }

            @Override
            public boolean doRefresh() {
                return false;
            }

            @Override
            public boolean isRefreshing() {
                return false;
            }

            @Override
            public void setParent(@Nullable ViewGroup parent) {

            }

            @Override
            public boolean checkRefresh() {
                return false;
            }

            @Override
            public void refreshCompleted() {

            }

            @Override
            public int getLoadingViewHeight() {
                return 0;
            }

            @Override
            public void autoRefresh() {

            }

            @Override
            public void releaseToRefresh() {

            }

            @Override
            public void setRefreshEnable(boolean enable) {

            }
        };
        bounceLayout.setmDisallowBounce(true);
        bounceLayout.setHeaderView(loadingView, new ViewGroup(mContext) {
            @Override
            protected void onLayout(boolean changed, int l, int t, int r, int b) {

            }
        });
        Assert.assertTrue(Whitebox.getInternalState(bounceLayout, "mLoadingView") != null);
    }

    @Test
    public void should_returnTrue_when_setFooterView() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        BaseLoadingView loadingView = new BaseLoadingView(mContext,null,0) {
            @Override
            public void handleDrag(float dragY) {

            }

            @Override
            public boolean doRefresh() {
                return false;
            }

            @Override
            public boolean isRefreshing() {
                return false;
            }

            @Override
            public void setParent(@Nullable ViewGroup parent) {

            }

            @Override
            public boolean checkRefresh() {
                return false;
            }

            @Override
            public void refreshCompleted() {

            }

            @Override
            public int getLoadingViewHeight() {
                return 0;
            }

            @Override
            public void autoRefresh() {

            }

            @Override
            public void releaseToRefresh() {

            }

            @Override
            public void setRefreshEnable(boolean enable) {

            }
        };
        bounceLayout.setmDisallowBounce(true);
        bounceLayout.setFooterView(loadingView, new ViewGroup(mContext) {
            @Override
            protected void onLayout(boolean changed, int l, int t, int r, int b) {

            }
        });
        Assert.assertTrue(Whitebox.getInternalState(bounceLayout, "mLoadingView") != null);
    }

    @Test
    public void should_returnTrue_when_setIsFooter() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        bounceLayout.setIsFooter(true);
        Assert.assertTrue(Whitebox.getInternalState(bounceLayout, "mIsFooter"));
    }

    @Test
    public void should_returnTrue_when_setBounceCallBack() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        BounceCallBack callBack = new BounceCallBack() {
            @Override
            public void touchEventCallBack(@NotNull MotionEvent ev) {

            }

            @Override
            public void refreshCompleted() {

            }

            @Override
            public void startLoadingMore() {

            }

            @Override
            public void startRefresh() {

            }
        };
        bounceLayout.setBounceCallBack(callBack);
        Assert.assertTrue(Whitebox.getInternalState(bounceLayout, "bounceCallBack") != null);
    }

    @Test
    public void should_returnTrue_when_setRefreshCompleted() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        BounceCallBack callBack = new BounceCallBack() {
            @Override
            public void touchEventCallBack(@NotNull MotionEvent ev) {

            }

            @Override
            public void refreshCompleted() {

            }

            @Override
            public void startLoadingMore() {

            }

            @Override
            public void startRefresh() {

            }
        };
        bounceLayout.setBounceCallBack(callBack);
        BaseLoadingView loadingView = new BaseLoadingView(mContext,null,0) {
            @Override
            public void handleDrag(float dragY) {

            }

            @Override
            public boolean doRefresh() {
                return false;
            }

            @Override
            public boolean isRefreshing() {
                return false;
            }

            @Override
            public void setParent(@Nullable ViewGroup parent) {

            }

            @Override
            public boolean checkRefresh() {
                return false;
            }

            @Override
            public void refreshCompleted() {

            }

            @Override
            public int getLoadingViewHeight() {
                return 0;
            }

            @Override
            public void autoRefresh() {

            }

            @Override
            public void releaseToRefresh() {

            }

            @Override
            public void setRefreshEnable(boolean enable) {

            }
        };
        bounceLayout.setmDisallowBounce(true);
        bounceLayout.setHeaderView(loadingView, new ViewGroup(mContext) {
            @Override
            protected void onLayout(boolean changed, int l, int t, int r, int b) {

            }
        });
        bounceLayout.setRefreshCompleted();

        bounceLayout.setmDisallowBounce(false);
        bounceLayout.setRefreshCompleted();
        Assert.assertFalse(Whitebox.getInternalState(bounceLayout, "mLockBoolean"));
        Assert.assertFalse(Whitebox.getInternalState(bounceLayout, "mForceDrag"));
        Assert.assertTrue(bounceLayout.isRefreshing() == false);
    }

    @Test
    public void should_returnTrue_when_setmDampingCoefficient() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        bounceLayout.setmDampingCoefficient(2f);
        float mDampingCoefficient = Whitebox.getInternalState(bounceLayout, "mDampingCoefficient");
        Assert.assertTrue(mDampingCoefficient == 2f);
    }

    @Test
    public void should_returnTrue_when_setmDisallowBounce() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        bounceLayout.setmDisallowBounce(true);
        Assert.assertTrue(Whitebox.getInternalState(bounceLayout, "mDisallowBounce"));
    }

    @Test
    public void should_returnTrue_when_setRefreshEnable() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        bounceLayout.setRefreshEnable(false);
        Assert.assertFalse(Whitebox.getInternalState(bounceLayout, "mEnable"));
        bounceLayout.setRefreshEnable(true);
        Assert.assertTrue(Whitebox.getInternalState(bounceLayout, "mEnable"));
    }

    @Test
    public void should_returnTrue_when_isRefreshing() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        Assert.assertFalse(bounceLayout.isRefreshing());
    }

    @Test
    public void should_returnTrue_when_dispatchTouchEvent() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        MotionEvent eventDown = MotionEvent.obtain(10, 10, MotionEvent.ACTION_DOWN, 0, 0, 0);
        bounceLayout.setRefreshEnable(false);
        Whitebox.setInternalState(bounceLayout, "mTotalOffsetY",0);
        bounceLayout.dispatchTouchEvent(eventDown);

        bounceLayout.setRefreshEnable(true);
        Whitebox.setInternalState(bounceLayout, "mTotalOffsetY",100);
        bounceLayout.dispatchTouchEvent(eventDown);

        EventForwardingHelper helper = new EventForwardingHelper() {
            @Override
            public boolean notForwarding(float downX, float downY, float moveX, float moveY) {
                return false;
            }
        };
        bounceLayout.setEventForwardingHelper(helper);
        IBounceHandler handler = new IBounceHandler() {
            @Override
            public boolean canChildDrag(@NotNull View v) {
                return false;
            }

            @Override
            public boolean canChildPull(@NotNull View v) {
                return false;
            }
        };
        bounceLayout.setBounceHandler(handler, new View(mContext));
        BaseLoadingView loadingView = new BaseLoadingView(mContext,null,0) {
            @Override
            public void handleDrag(float dragY) {

            }

            @Override
            public boolean doRefresh() {
                return false;
            }

            @Override
            public boolean isRefreshing() {
                return false;
            }

            @Override
            public void setParent(@Nullable ViewGroup parent) {

            }

            @Override
            public boolean checkRefresh() {
                return false;
            }

            @Override
            public void refreshCompleted() {

            }

            @Override
            public int getLoadingViewHeight() {
                return 0;
            }

            @Override
            public void autoRefresh() {

            }

            @Override
            public void releaseToRefresh() {

            }

            @Override
            public void setRefreshEnable(boolean enable) {

            }
        };
        bounceLayout.setHeaderView(loadingView, new ViewGroup(mContext) {
            @Override
            protected void onLayout(boolean changed, int l, int t, int r, int b) {

            }
        });
        bounceLayout.dispatchTouchEvent(eventDown);

        MotionEvent eventMove = MotionEvent.obtain(10, 10, MotionEvent.ACTION_MOVE, 0, 0, 0);
        Whitebox.setInternalState(bounceLayout, "mPointerChange",true);
        bounceLayout.dispatchTouchEvent(eventMove);

        MotionEvent eventUP = MotionEvent.obtain(10, 10, MotionEvent.ACTION_UP, 0, 0, 0);
        bounceLayout.dispatchTouchEvent(eventUP);

        MotionEvent eventCancel = MotionEvent.obtain(10, 10, MotionEvent.ACTION_CANCEL, 0, 0, 0);
        bounceLayout.dispatchTouchEvent(eventCancel);

        MotionEvent eventPointDown = MotionEvent.obtain(10, 10, MotionEvent.ACTION_POINTER_DOWN, 0, 0, 0);
        bounceLayout.dispatchTouchEvent(eventPointDown);

        MotionEvent eventPointUp = MotionEvent.obtain(10, 10, MotionEvent.ACTION_POINTER_UP, 0, 0, 0);
        bounceLayout.dispatchTouchEvent(eventPointUp);
    }

    @Test
    public void should_returnTrue_when_onInterceptTouchEvent() {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        MotionEvent eventDown = MotionEvent.obtain(10, 10, MotionEvent.ACTION_DOWN, 0, 0, 0);
        bounceLayout.setRefreshEnable(true);
        Whitebox.setInternalState(bounceLayout, "mTotalOffsetY",0);
        bounceLayout.onInterceptTouchEvent(eventDown);

        bounceLayout.setRefreshEnable(false);
        bounceLayout.onInterceptTouchEvent(eventDown);

        bounceLayout.setRefreshEnable(true);
        MotionEvent eventUP = MotionEvent.obtain(10, 10, MotionEvent.ACTION_CANCEL, 0, 0, 0);
        bounceLayout.onInterceptTouchEvent(eventUP);
    }

    @Test
    public void should_returnTrue_when_dispatchToChild() throws Exception {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        IBounceHandler handler = new IBounceHandler() {
            @Override
            public boolean canChildDrag(@NotNull View v) {
                return false;
            }

            @Override
            public boolean canChildPull(@NotNull View v) {
                return false;
            }
        };
        bounceLayout.setBounceHandler(handler, new View(mContext));
        Whitebox.invokeMethod(bounceLayout, "dispatchToChild",10f);

        Whitebox.setInternalState(bounceLayout, "mCurrentY",11);
        Whitebox.invokeMethod(bounceLayout, "dispatchToChild",10f);

        Whitebox.setInternalState(bounceLayout, "mCurrentY",10);
        Whitebox.invokeMethod(bounceLayout, "dispatchToChild",10f);
    }

    @Test
    public void should_returnTrue_when_moving() throws Exception {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        Whitebox.invokeMethod(bounceLayout, "moving");
    }

  /*  @Test
    public void should_returnTrue_when_onSizeChanged() throws Exception {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        bounceLayout.onSizeChanged(1,1,1,1);
    }*/

    @Test
    public void should_returnTrue_when_computeScroll() throws Exception {
        BounceLayout bounceLayout = new BounceLayout(mContext);
        Whitebox.setInternalState(bounceLayout, "mForceDrag",true);
        bounceLayout.computeScroll();

        Whitebox.setInternalState(bounceLayout, "mForceDrag",false);
        BaseLoadingView loadingView = new BaseLoadingView(mContext,null,0) {
            @Override
            public void handleDrag(float dragY) {

            }

            @Override
            public boolean doRefresh() {
                return false;
            }

            @Override
            public boolean isRefreshing() {
                return false;
            }

            @Override
            public void setParent(@Nullable ViewGroup parent) {

            }

            @Override
            public boolean checkRefresh() {
                return false;
            }

            @Override
            public void refreshCompleted() {

            }

            @Override
            public int getLoadingViewHeight() {
                return 0;
            }

            @Override
            public void autoRefresh() {

            }

            @Override
            public void releaseToRefresh() {

            }

            @Override
            public void setRefreshEnable(boolean enable) {

            }
        };
        bounceLayout.setHeaderView(loadingView, new ViewGroup(mContext) {
            @Override
            protected void onLayout(boolean changed, int l, int t, int r, int b) {

            }
        });
        bounceLayout.computeScroll();
    }
}
