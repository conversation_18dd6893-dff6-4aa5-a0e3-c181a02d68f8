/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: OSDKCompatUtilsTest
 Description:
 Version: 1.0
 Date: 2023-6-16
 Author: zhanghr
 -----------Revision History-----------
 <author> <date> <version> <desc>
 zhanghr 2023-6-16 create
 */

package com.soundrecorder.base.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class OSDKCompatUtilsTest {

    private var mContext: Context? = null
    private var mMockedBaseApplication: MockedStatic<BaseApplication>? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mMockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockedBaseApplication?.`when`<Any> { BaseApplication.getAppContext() }
            ?.thenReturn(mContext)
    }

    @After
    fun tearDown() {
        if (mMockedBaseApplication != null) {
            mMockedBaseApplication?.close()
            mMockedBaseApplication = null
        }
        mContext = null
    }

    @Test
    fun should_correct_when_getFeatureProperty() {
        val utils = Mockito.mockStatic(BaseUtil::class.java)
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(false)
        val compatUtils = Mockito.mockStatic(OSDKCompatUtils::class.java)
        compatUtils.`when`<String> { OSDKCompatUtils.getFeatureProperty("ro.oplus.rsa") }
            .thenReturn("isAndroidUOrLater false")
        Assert.assertTrue(OSDKCompatUtils.getFeatureProperty("ro.oplus.rsa") == "isAndroidUOrLater false")
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(true)
        compatUtils.`when`<String> { OSDKCompatUtils.getFeatureProperty("ro.oplus.rsa") }
            .thenReturn("isAndroidUOrLater true")
        Assert.assertTrue(OSDKCompatUtils.getFeatureProperty("ro.oplus.rsa") == "isAndroidUOrLater true")
        utils.reset()
        compatUtils.reset()
        utils.close()
        compatUtils.close()
    }

    @Test
    fun should_correct_when_getDoubleFeatureProperty() {
        val utils = Mockito.mockStatic(BaseUtil::class.java)
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(false)
        val compatUtils = Mockito.mockStatic(OSDKCompatUtils::class.java)
        compatUtils.`when`<String> { OSDKCompatUtils.getFeatureProperty("ro.oplus.rsa", "def") }
            .thenReturn("isAndroidUOrLater false")
        Assert.assertTrue(
            OSDKCompatUtils.getFeatureProperty(
                "ro.oplus.rsa",
                "def"
            ) == "isAndroidUOrLater false"
        )
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(true)
        compatUtils.`when`<String> { OSDKCompatUtils.getFeatureProperty("ro.oplus.rsa", "def") }
            .thenReturn("isAndroidUOrLater true")
        Assert.assertTrue(
            OSDKCompatUtils.getFeatureProperty(
                "ro.oplus.rsa",
                "def"
            ) == "isAndroidUOrLater true"
        )
        utils.reset()
        compatUtils.reset()
        utils.close()
        compatUtils.close()
    }

    @Test
    fun should_correct_when_setCompatFlags() {
        val utils = Mockito.mockStatic(BaseUtil::class.java)
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(false)
        val intent = Mockito.mock(Intent::class.java)
        OSDKCompatUtils.setCompatFlags(intent, 0x10000000)
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(false)
        OSDKCompatUtils.setCompatFlags(intent, 0x10000000)
        utils.reset()
        utils.close()
    }

    @Test
    fun should_correct_when_isResumed() {
        val utils = Mockito.mockStatic(BaseUtil::class.java)
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(false)
        val compatUtils = Mockito.mockStatic(OSDKCompatUtils::class.java)
        val activity = Mockito.mock(Activity::class.java)
        compatUtils.`when`<Boolean> { OSDKCompatUtils.isResumed(activity) }
            .thenReturn(true)
        Assert.assertTrue(OSDKCompatUtils.isResumed(activity))
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(true)
        compatUtils.`when`<Boolean> { OSDKCompatUtils.isResumed(activity) }
            .thenReturn(true)
        Assert.assertTrue(OSDKCompatUtils.isResumed(activity))
        utils.reset()
        compatUtils.reset()
        utils.close()
        compatUtils.close()
    }

    @Test
    fun should_correct_when_getFeatureConfig() {
        val utils = Mockito.mockStatic(BaseUtil::class.java)
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(false)
        val compatUtils = Mockito.mockStatic(OSDKCompatUtils::class.java)
        compatUtils.`when`<Boolean> { OSDKCompatUtils.getFeatureConfig("ro.oplus.rsa") }
            .thenReturn(true)
        Assert.assertTrue(OSDKCompatUtils.getFeatureConfig("ro.oplus.rsa"))
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(true)
        compatUtils.`when`<Boolean> { OSDKCompatUtils.getFeatureConfig("ro.oplus.rsa") }
            .thenReturn(true)
        Assert.assertTrue(OSDKCompatUtils.getFeatureConfig("ro.oplus.rsa"))
        utils.reset()
        compatUtils.reset()
        utils.close()
        compatUtils.close()
    }

    @Test
    fun should_correct_when_setConvertToTranslucent() {
        val utils = Mockito.mockStatic(BaseUtil::class.java)
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(false)
        val activity = Mockito.mock(Activity::class.java)
        OSDKCompatUtils.setConvertToTranslucent(activity)
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(false)
        OSDKCompatUtils.setConvertToTranslucent(activity)
        utils.reset()
        utils.close()
    }

    @Test
    fun should_correct_when_setConvertFromTranslucent() {
        val utils = Mockito.mockStatic(BaseUtil::class.java)
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(false)
        val activity = Mockito.mock(Activity::class.java)
        OSDKCompatUtils.setConvertFromTranslucent(activity)
        utils.`when`<Boolean> { BaseUtil.isAndroidUOrLater }.thenReturn(false)
        OSDKCompatUtils.setConvertFromTranslucent(activity)
        utils.reset()
        utils.close()
    }
}