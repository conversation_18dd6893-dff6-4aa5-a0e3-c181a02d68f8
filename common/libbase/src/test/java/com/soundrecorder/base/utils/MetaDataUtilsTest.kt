/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MetaDataUtilsTest
 * Description:
 * Version: 1.0
 * Date: 2024/3/28
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2024/3/28 1.0 create
 */

package com.soundrecorder.base.utils

import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.os.bundleOf
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.shadows.ShadowFeatureOption
import java.lang.NullPointerException
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.ArgumentMatchers.anyString
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MetaDataUtilsTest {

    @Test
    fun should_correct_when_getMetaDataBoolean() {
        val context = Mockito.mock(Context::class.java)
        val mockPackageManager = Mockito.mock(PackageManager::class.java)
        Mockito.`when`(context.packageManager).thenReturn(mockPackageManager)

        val mockApplicationInfo = Mockito.mock(ApplicationInfo::class.java)
        Mockito.`when`(mockPackageManager.getApplicationInfo(anyString(), anyInt())).thenReturn(mockApplicationInfo)

        mockApplicationInfo.metaData = null
        Assert.assertFalse(MetaDataUtils.getMetaDataBoolean(context, "", ""))

        mockApplicationInfo.metaData = bundleOf("" to true)
        Assert.assertTrue(MetaDataUtils.getMetaDataBoolean(context, "", ""))

        Mockito.`when`(mockPackageManager.getApplicationInfo(anyString(), anyInt())).thenThrow(NullPointerException())
        Assert.assertFalse(MetaDataUtils.getMetaDataBoolean(context, "", ""))
    }

    @Test
    fun should_correct_when_getMetaDataString() {
        val context = Mockito.mock(Context::class.java)
        val mockPackageManager = Mockito.mock(PackageManager::class.java)
        val mockApplicationInfo = Mockito.mock(ApplicationInfo::class.java)
        Mockito.`when`(mockPackageManager?.getApplicationInfo(anyString(), anyInt()))?.thenReturn(mockApplicationInfo)
        Mockito.`when`(context.packageManager).thenReturn(mockPackageManager)
        mockApplicationInfo.metaData = null
        Assert.assertEquals("", MetaDataUtils.getMetaDataString(context, "", ""))

        mockApplicationInfo.metaData = bundleOf("" to "1")
        Assert.assertEquals("1", MetaDataUtils.getMetaDataString(context, "", ""))

        Mockito.`when`(mockPackageManager.getApplicationInfo(anyString(), anyInt())).thenThrow(NullPointerException())
        Assert.assertEquals("", MetaDataUtils.getMetaDataString(context, "", ""))
    }

    @Test
    fun should_correct_when_getMetaDataInt() {
        val context = Mockito.mock(Context::class.java)
        val mockPackageManager = Mockito.mock(PackageManager::class.java)
        val mockApplicationInfo = Mockito.mock(ApplicationInfo::class.java)
        Mockito.`when`(mockPackageManager.getApplicationInfo(anyString(), anyInt())).thenReturn(mockApplicationInfo)
        Mockito.`when`(context.packageManager).thenReturn(mockPackageManager)
        mockApplicationInfo.metaData = null
        Assert.assertEquals(-1, MetaDataUtils.getMetaDataInt(context, "", ""))

        mockApplicationInfo.metaData = bundleOf("" to 1)
        Assert.assertEquals(1, MetaDataUtils.getMetaDataInt(context, "", ""))

        Mockito.`when`(mockPackageManager.getApplicationInfo(anyString(), anyInt())).thenThrow(NullPointerException())
        Assert.assertEquals(-1, MetaDataUtils.getMetaDataInt(context, "", ""))
    }
}