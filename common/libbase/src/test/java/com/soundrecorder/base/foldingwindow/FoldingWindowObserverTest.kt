/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FoldingWindowObserverTest
 * Description:
 * Version: 1.0
 * Date: 2022/8/8
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/8/8 1.0 create
 */

package com.soundrecorder.base.foldingwindow

import android.app.Activity
import android.os.Build
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.window.layout.DisplayFeature
import androidx.window.layout.FoldingFeature
import androidx.window.layout.FoldingFeature.State.Companion.FLAT
import androidx.window.layout.FoldingFeature.State.Companion.HALF_OPENED
import androidx.window.layout.WindowLayoutInfo
import com.soundrecorder.base.splitwindow.FoldingWindowObserver
import com.soundrecorder.base.shadows.ShadowFeatureOption
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.base.utils.FeatureOption
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.`when`
import org.mockito.Mockito.mock
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class FoldingWindowObserverTest {
    private lateinit var mActivity: Activity

    @Before
    fun setUp() {
        mActivity = mock(Activity::class.java)
    }

    @Test
    fun should_no_observer_wen_notFoldFeature() {
        FeatureOption.setFoldFeature(false)
        val lifecycleOwner: LifecycleOwner = mock(LifecycleOwner::class.java)
        val lifecycleRegistry = LifecycleRegistry(lifecycleOwner)
        `when`(lifecycleOwner.lifecycle).thenReturn(lifecycleRegistry)
        val foldWindowObserver = FoldingWindowObserver(mActivity)
        lifecycleRegistry.addObserver(foldWindowObserver)

        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_START)
        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    }

    @Test
    fun should_correct_when_isFoldFeature() {
        FeatureOption.setFoldFeature(false)
        val lifecycleOwner: LifecycleOwner = mock(LifecycleOwner::class.java)
        val lifecycleRegistry = LifecycleRegistry(lifecycleOwner)
        `when`(lifecycleOwner.lifecycle).thenReturn(lifecycleRegistry)
        val foldWindowObserver = FoldingWindowObserver(mActivity)
        lifecycleRegistry.addObserver(foldWindowObserver)

        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_START)
        lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    }

    @Test
    fun should_smallScreen_when_layoutChange() {
        val foldWindowObserver = FoldingWindowObserver(mActivity)
        foldWindowObserver.mOnScreenFoldStateChangeListener = object :
            FoldingWindowObserver.OnScreenFoldStateChangeListener {
            override fun onScreenFoldStateChanged(state: Int) {
                Assert.assertEquals(FoldingWindowObserver.SCREEN_SMALL, state)
            }
        }
        val layoutInfo = mock(WindowLayoutInfo::class.java)
        `when`(layoutInfo.displayFeatures).thenReturn(null)
        Whitebox.invokeMethod<Unit>(foldWindowObserver, "layoutChange", layoutInfo)
    }

    @Test
    fun should_other_when_layoutChange() {
        val foldWindowObserver = FoldingWindowObserver(mActivity)
        val layoutInfo = mock(WindowLayoutInfo::class.java)
        `when`(layoutInfo.displayFeatures).thenReturn(mutableListOf(mock(DisplayFeature::class.java)))
        Whitebox.invokeMethod<Unit>(foldWindowObserver, "layoutChange", layoutInfo)
    }

    @Test
    fun should_horizontal_hover_when_layoutChange() {
        val foldWindowObserver = FoldingWindowObserver(mActivity)
        foldWindowObserver.mOnScreenFoldStateChangeListener = object :
            FoldingWindowObserver.OnScreenFoldStateChangeListener {
            override fun onScreenFoldStateChanged(state: Int) {
                Assert.assertEquals(FoldingWindowObserver.SCREEN_HORIZONTAL_HOVER, state)
            }
        }
        val feature = mock(FoldingFeature::class.java)
        `when`(feature.state).thenReturn(HALF_OPENED)
        `when`(feature.orientation).thenReturn(FoldingFeature.Orientation.HORIZONTAL)

        val layoutInfo = mock(WindowLayoutInfo::class.java)
        `when`(layoutInfo.displayFeatures).thenReturn(mutableListOf(feature))
        Whitebox.invokeMethod<Unit>(foldWindowObserver, "layoutChange", layoutInfo)
    }

    @Test
    fun should_vertical_hover_when_layoutChange() {
        val foldWindowObserver = FoldingWindowObserver(mActivity)
        foldWindowObserver.mOnScreenFoldStateChangeListener = object :
            FoldingWindowObserver.OnScreenFoldStateChangeListener {
            override fun onScreenFoldStateChanged(state: Int) {
                Assert.assertEquals(FoldingWindowObserver.SCREEN_VERTICAL_HOVER, state)
            }
        }
        val feature = mock(FoldingFeature::class.java)
        `when`(feature.state).thenReturn(HALF_OPENED)
        `when`(feature.orientation).thenReturn(FoldingFeature.Orientation.VERTICAL)

        val layoutInfo = mock(WindowLayoutInfo::class.java)
        `when`(layoutInfo.displayFeatures).thenReturn(mutableListOf(feature))
        Whitebox.invokeMethod<Unit>(foldWindowObserver, "layoutChange", layoutInfo)
    }

    @Test
    fun should_horizontal_expand_when_layoutChange() {
        val foldWindowObserver = FoldingWindowObserver(mActivity)
        foldWindowObserver.mOnScreenFoldStateChangeListener = object :
            FoldingWindowObserver.OnScreenFoldStateChangeListener {
            override fun onScreenFoldStateChanged(state: Int) {
                Assert.assertEquals(FoldingWindowObserver.SCREEN_HORIZONTAL_EXPAND, state)
            }
        }
        val feature = mock(FoldingFeature::class.java)
        `when`(feature.state).thenReturn(FLAT)
        `when`(feature.orientation).thenReturn(FoldingFeature.Orientation.HORIZONTAL)
        `when`(feature.isSeparating).thenReturn(false)

        val layoutInfo = mock(WindowLayoutInfo::class.java)
        `when`(layoutInfo.displayFeatures).thenReturn(mutableListOf(feature))
        Whitebox.invokeMethod<Unit>(foldWindowObserver, "layoutChange", layoutInfo)
    }

    @Test
    fun should_vertical_expand_when_layoutChange() {
        val foldWindowObserver = FoldingWindowObserver(mActivity)
        foldWindowObserver.mOnScreenFoldStateChangeListener = object :
            FoldingWindowObserver.OnScreenFoldStateChangeListener {
            override fun onScreenFoldStateChanged(state: Int) {
                Assert.assertEquals(FoldingWindowObserver.SCREEN_VERTICAL_EXPAND, state)
            }
        }
        val feature = mock(FoldingFeature::class.java)
        `when`(feature.state).thenReturn(FLAT)
        `when`(feature.orientation).thenReturn(FoldingFeature.Orientation.VERTICAL)
        `when`(feature.isSeparating).thenReturn(false)

        val layoutInfo = mock(WindowLayoutInfo::class.java)
        `when`(layoutInfo.displayFeatures).thenReturn(mutableListOf(feature))
        Whitebox.invokeMethod<Unit>(foldWindowObserver, "layoutChange", layoutInfo)
    }

    @Test
    fun should_correct_when_isTableTopPosture() {
        val foldWindowObserver = FoldingWindowObserver(mActivity)
        var isTableTop = Whitebox.invokeMethod<Boolean>(foldWindowObserver, "isTableTopPosture", null as? FoldingFeature?)
        Assert.assertFalse(isTableTop)

        val feature = mock(FoldingFeature::class.java)
        `when`(feature.state).thenReturn(HALF_OPENED, FLAT)
        `when`(feature.orientation).thenReturn(FoldingFeature.Orientation.HORIZONTAL)

        isTableTop = Whitebox.invokeMethod(foldWindowObserver, "isTableTopPosture", feature)
        Assert.assertTrue(isTableTop)

        isTableTop = Whitebox.invokeMethod(foldWindowObserver, "isTableTopPosture", feature)
        Assert.assertFalse(isTableTop)
    }

    @Test
    fun should_correct_when_isBookPosture() {
        val foldWindowObserver = FoldingWindowObserver(mActivity)
        var isBookPosture = Whitebox.invokeMethod<Boolean>(foldWindowObserver, "isBookPosture", null as? FoldingFeature?)
        Assert.assertFalse(isBookPosture)

        val feature = mock(FoldingFeature::class.java)
        `when`(feature.state).thenReturn(HALF_OPENED, FLAT)
        `when`(feature.orientation).thenReturn(FoldingFeature.Orientation.VERTICAL)

        isBookPosture = Whitebox.invokeMethod(foldWindowObserver, "isBookPosture", feature)
        Assert.assertTrue(isBookPosture)

        isBookPosture = Whitebox.invokeMethod(foldWindowObserver, "isBookPosture", feature)
        Assert.assertFalse(isBookPosture)
    }

    @Test
    fun should_correct_when_isNotSeparating() {
        val foldWindowObserver = FoldingWindowObserver(mActivity)
        var isNotSeparating = Whitebox.invokeMethod<Boolean>(foldWindowObserver, "isNotSeparating", null as? FoldingFeature?)
        Assert.assertFalse(isNotSeparating)

        val feature = mock(FoldingFeature::class.java)
        `when`(feature.state).thenReturn(FLAT, HALF_OPENED)
        `when`(feature.isSeparating).thenReturn(false)

        isNotSeparating = Whitebox.invokeMethod(foldWindowObserver, "isNotSeparating", feature)
        Assert.assertTrue(isNotSeparating)

        isNotSeparating = Whitebox.invokeMethod(foldWindowObserver, "isNotSeparating", feature)
        Assert.assertFalse(isNotSeparating)
    }
}