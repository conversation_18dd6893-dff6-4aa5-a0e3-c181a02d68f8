/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: BaseApplicationTest
 Description:
 Version: 1.0
 Date: 2022/7/28
 Author: W9013333(v-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 --------------------Revision History: ---------------------
 <author> <date> <version> <desc>
 W9013333 2022/7/28 1.0 create
 */
package com.soundrecorder.base

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.shadows.ShadowFeatureOption
import com.soundrecorder.base.shadows.ShadowOS12FeatureUtil
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class BaseApplicationTest {
    @Test
    fun check_main_application() {
        Assert.assertNotNull(BaseApplication.getApplication())
        Assert.assertTrue(BaseApplication.getApplication().isMainProcess)
    }
}