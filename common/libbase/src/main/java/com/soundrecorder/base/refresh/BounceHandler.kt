/*********************************************************************************
 ** Copyright (C) 2020 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - BounceHandler.kt
 ** Description: Custom elastic processing class for controlling vertical scrolling
 **              of child view and BounceLayout conflicts
 **
 ** Version: 1.1
 ** Date: 2019-04-30
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2019-04-30   1.1         Convert this demo into Kotlin
 ********************************************************************************/

package com.soundrecorder.base.refresh

import android.os.Build
import android.view.View
import android.widget.AbsListView
import android.widget.ScrollView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView


class BounceHandler : IBounceHandler {
    override fun canChildPull(v: View): Boolean {
        // Means that you cannot scroll down
        return !canScrollDown(v)
    }

    override fun canChildDrag(v: View): Boolean {
        // Means that you cannot scroll up
        return !canScrollUP(v)
    }

    /**
     * Is it possible to scroll up in the vertical direction?
     * @param view
     * @return false if it can't scroll. Then you can pull it down
     */
    private fun canScrollUP(view: View): Boolean {
        return if (view is AbsListView) {
            (view.childCount > 0 && (view.firstVisiblePosition > 0 || view.getChildAt(0).top < view.paddingTop))
        } else if (view is RecyclerView && view.layoutManager is LinearLayoutManager) {
            val layoutManager = view.layoutManager as LinearLayoutManager
            (layoutManager.childCount > 0 && (layoutManager.findFirstVisibleItemPosition() > 0 || view.getChildAt(0).top < view.paddingTop))
        } else {
            // True if it can also be pulled in the vertical direction
            view.canScrollVertically(-1)
        }
    }

    /**
     * Think it can still slide
     *
     * @param view
     * @return
     */
    private fun canScrollDown(view: View): Boolean {
        return if (Build.VERSION.SDK_INT < 14) {
            if (view is AbsListView) {
                (view.childCount > 0
                    && (view.lastVisiblePosition < view.childCount - 1
                    || view.getChildAt(view.childCount - 1).bottom > view.paddingBottom))
            } else if (view is ScrollView) {
                if (view.childCount == 0) {
                    false
                } else {
                    view.scrollY < view.getChildAt(0).height - view.height
                }
            } else {
                false
            }
        } else {
            // True if the vertical direction can also be scrolled
            view.canScrollVertically(1)
        }
    }
}