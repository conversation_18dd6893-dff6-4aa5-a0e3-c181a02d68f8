/***********************************************************
 ** Copyright (C), 2008-2018, OPPO Mobile Comm Corp., Ltd.
 ** COLOROS_EDIT
 ** File: - StatusBarUtil.java
 ** Description: Implemented StatusBarUtil to set transparent and black-font status bar
 ** Version: 1.0
 ** Date: 2020/5/21
 ** Author: Yulong.Liu
 **
 ** ---------------------Revision History: ---------------------
 ** <author>  <date>       <version>    <desc>
 **
 ***************************************************************/
package com.soundrecorder.base.utils

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.view.View
import android.view.WindowManager
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.statusbar.COUIStatusbarTintUtil
import com.coui.appcompat.version.COUIVersionUtil
import com.soundrecorder.base.R

object StatusBarUtil {

    private const val TAG = "StatusBarUtil"

    @JvmStatic
    fun setStatusBarTransparentAndBlackFont(
        mActivity: Activity,
        @ColorRes defaultColorRes: Int = com.support.appcompat.R.color.coui_color_background
    ) {
        val window = mActivity.window
        val decorView = mActivity.window.decorView
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            window.statusBarColor = Color.TRANSPARENT
            setNavigationBarColor(mActivity, defaultColorRes)
        }
        var flag = decorView.systemUiVisibility
        val versionCode = COUIVersionUtil.getOSVersionCode()
        val white = mActivity.resources.getBoolean(R.bool.is_status_white)
        if (versionCode >= COUIVersionUtil.COUI_3_0 || versionCode == 0) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            if (COUIDarkModeUtil.isNightMode(mActivity)) {
                flag = flag and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
                flag = flag and View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR.inv()
            } else {
                flag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    if (!white) {
                        flag or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                    } else {
                        flag or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    }
                } else {
                    flag or COUIStatusbarTintUtil.SYSTEM_UI_FLAG_OP_STATUS_BAR_TINT
                }
            }
            decorView.systemUiVisibility = flag
        }
    }

    @JvmStatic
    fun hideNavigationBar(activity: Activity) {
        val decorView = activity.window.decorView
        decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
    }

    @JvmStatic
    fun setNavigationBarColor(activity: Activity?, @ColorRes color: Int) {
        if (activity == null) return
        val window = activity.window
        window.navigationBarColor = ContextCompat.getColor(activity, color)
    }

    @JvmStatic
    fun setDialogStatusBarTransparentAndBlackFont(context: Context, dialog: Dialog, isFullScreen: Boolean) {
        val window = dialog.window ?: return
        val decorView = window.decorView
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && isFullScreen) {
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        }
        var flag = decorView.systemUiVisibility
        val white = false//context.resources.getBoolean(R.bool.is_status_white)
        val versionCode = RomVersionUtil.getRomVersionCode()
        if (versionCode >= RomVersionUtil.ColorOS_3_0 || versionCode == 0) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            if (COUIDarkModeUtil.isNightMode(dialog.context)) {
                flag = flag.and(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv())
                flag = flag.and(View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR.inv())
            } else {
                flag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    if (!white) {
                        flag.or(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR)
                    } else {
                        flag.or(View.SYSTEM_UI_FLAG_LAYOUT_STABLE)
                    }
                } else {
                    flag.or(COUIStatusbarTintUtil.SYSTEM_UI_FLAG_OP_STATUS_BAR_TINT)
                }
            }
            flag.let {
                decorView.systemUiVisibility = it
            }
        }
    }

    @JvmStatic
    fun getStatusBarHeight(context: Context): Int {
        var height = 0
        val identifier = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (identifier > 0) {
            height = context.resources.getDimensionPixelSize(identifier)
        }
        return height
    }

    /**
     * 获取导航栏高度
     */
    @JvmStatic
    fun getNavigationBarHeight(context: Context): Int {
        var height = 0
        val identifier = context.resources.getIdentifier("navigation_bar_height", "dimen", "android")
        if (identifier > 0) {
            height = context.resources.getDimensionPixelSize(identifier)
        }
        return height
    }

    /**
     * Android 10+ 虚拟按键导航 切换导航栏半透明
     * @param tag: 是否禁用半透明 false:禁用半透明导航栏，会变成全透明  true:启用半透明导航栏
     */
    @JvmStatic
    fun switchNavigationBarTransparent(mActivity: Activity?, tag: Boolean) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            mActivity?.let {
                if (!it.isDestroyed) {
                    DebugUtil.i(TAG, "disableNavigationBarTransparent")
                    it.window.decorView.systemUiVisibility = (
                            it.window.decorView.systemUiVisibility
                                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                            )
                    it.window.isNavigationBarContrastEnforced = tag
                }
            }
        }
    }
}