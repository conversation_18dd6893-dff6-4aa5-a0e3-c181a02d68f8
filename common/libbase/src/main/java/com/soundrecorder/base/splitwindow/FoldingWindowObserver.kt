/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FoldingWindowObserver
 * Description:
 * Version: 1.0
 * Date: 2022/7/25
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/7/25 1.0 create
 */

package com.soundrecorder.base.splitwindow

import android.app.Activity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.window.layout.DisplayFeature
import androidx.window.layout.FoldingFeature
import androidx.window.layout.FoldingFeature.Orientation.Companion.HORIZONTAL
import androidx.window.layout.FoldingFeature.Orientation.Companion.VERTICAL
import androidx.window.layout.FoldingFeature.State.Companion.FLAT
import androidx.window.layout.FoldingFeature.State.Companion.HALF_OPENED
import androidx.window.layout.WindowInfoTracker
import androidx.window.layout.WindowLayoutInfo
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch

class FoldingWindowObserver(val activity: Activity) : DefaultLifecycleObserver {
    companion object {
        private const val TAG = "FoldingWindowObserver"

        // 横屏悬停
        const val SCREEN_HORIZONTAL_HOVER = 1

        // 横屏完全展开
        const val SCREEN_HORIZONTAL_EXPAND = 2

        // 竖屏悬停
        const val SCREEN_VERTICAL_HOVER = 3

        //竖屏展开
        const val SCREEN_VERTICAL_EXPAND = 4

        /*副屏*/
        const val SCREEN_SMALL = 5
    }


    private var mLastWindowInfo: WindowLayoutInfo? = null
    var mOnScreenFoldStateChangeListener: OnScreenFoldStateChangeListener? = null


    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        setFoldingStateChangeObserver(owner)
    }

    private fun setFoldingStateChangeObserver(owner: LifecycleOwner) {
        if (!FeatureOption.IS_SUPPORT_HOVER_MODE || FeatureOption.getIsFoldFeature().not()) {
            return
        }
        owner.lifecycleScope.launch(Dispatchers.Main) {
            /**  The block passed to repeatOnLifecycle is executed when the lifecycle
             * is at least STARTED and is cancelled when the lifecycle is STOPPED.
             * It automatically restarts the block when the lifecycle is STARTED again.*/
            owner.repeatOnLifecycle(Lifecycle.State.RESUMED) {
                /** Safely collects from windowInfoRepository when the lifecycle is STARTED
                 * and stops collection when the lifecycle is STOPPED.*/
                WindowInfoTracker.getOrCreate(activity)
                    .windowLayoutInfo(activity)
                    .catch {
                        /**
                         * monkey 红屏问题
                         */
                        DebugUtil.i(TAG, "setFoldingStateChangeObserver catch exception")
                    }
                    .collect { newLayoutInfo ->
                        DebugUtil.i(TAG, "newLayoutInfo is ${mLastWindowInfo == newLayoutInfo} $newLayoutInfo")
                        if (mLastWindowInfo != newLayoutInfo) {
                            mLastWindowInfo = newLayoutInfo
                            layoutChange(newLayoutInfo)
                        }
                    }
            }
        }
    }


    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        owner.lifecycle.removeObserver(this)
        mOnScreenFoldStateChangeListener = null
        mLastWindowInfo = null
    }

    /**
     *
     * FoldingFeature 提供：
     * state：FLAT (展开态)或 HALF_OPENED(半折态)
     * orientation：HORIZONTAL(折叠边水平对齐) 或 VERTICAL(折叠边垂直对齐)
     * occlusionType：NONE(不遮挡) 或 FULL(遮挡)，用于指示折叠边或合页是否遮住了显示屏的一部分
     * isSeparating：如果折叠功能创建了两个逻辑屏幕区域，则为 true。
     * bounds: 包含折叠功能的尺寸，可以根据该信息在屏幕上放置元素。
     * 【注意】
     * 1. HALF_OPENED 的折叠设备始终将 isSeparating=true，因为屏幕现在被分为两个窗口区域。
     * 2. 当我们在双屏设备上运行跨屏显示的应用时(OPPO 暂无涉及)，isSeparating 始终为 true。
     * 3. 当 isSeparating 为 true 时，请避免使控件与合页靠得太近，否则会难以触及这些控件。我们还可以使用 occlusionType 确定是否可以在折叠功能的 bounds 上显示信息。
     * 4. 使用 occlusionType 决定将元素放置在何处以支持双屏设备，并避免将互动元素放置在折叠功能所遮盖的区域中。
     */
    private fun layoutChange(newLayoutInfo: WindowLayoutInfo?) {
        if (newLayoutInfo?.displayFeatures?.isNullOrEmpty() == true) {
            mOnScreenFoldStateChangeListener?.onScreenFoldStateChanged(SCREEN_SMALL)
            return
        }

        val displayFeatures: List<DisplayFeature> = newLayoutInfo?.displayFeatures ?: return
        for (feature in displayFeatures) {
            if (feature is FoldingFeature) {
                DebugUtil.i(
                    TAG, "${activity.localClassName}-layoutChange: occlusionType=${feature.occlusionType}," +
                            "state=${feature.state},orientation=${feature.orientation}，isSeparating=${feature.isSeparating},bounds=${feature.bounds}"
                )
                // 横屏折叠
                if (isTableTopPosture(feature)) {
                    mOnScreenFoldStateChangeListener?.onScreenFoldStateChanged(
                        SCREEN_HORIZONTAL_HOVER
                    )
                    // 竖屏折叠
                } else if (isBookPosture(feature)) {
                    mOnScreenFoldStateChangeListener?.onScreenFoldStateChanged(SCREEN_VERTICAL_HOVER)
                } else if (isNotSeparating(feature)) {
                    // Dual-screen device 横向展开
                    if (feature.orientation == HORIZONTAL) {
                        mOnScreenFoldStateChangeListener?.onScreenFoldStateChanged(
                            SCREEN_HORIZONTAL_EXPAND
                        )
                    } else { // 竖向展开
                        mOnScreenFoldStateChangeListener?.onScreenFoldStateChanged(
                            SCREEN_VERTICAL_EXPAND
                        )
                    }
                } else {
                    DebugUtil.e(TAG, " other state $feature")
                }
            }
        }
    }

    /**
     * 横屏悬停
     */
    private fun isTableTopPosture(foldFeature: FoldingFeature?): Boolean {
        return foldFeature?.state == HALF_OPENED && foldFeature.orientation == HORIZONTAL
    }

    /**
     * 竖屏悬停
     */
    private fun isBookPosture(foldFeature: FoldingFeature?): Boolean {
        return foldFeature?.state == HALF_OPENED && foldFeature.orientation == VERTICAL
    }

    /**
     * 展开状态
     */
    private fun isNotSeparating(foldFeature: FoldingFeature?): Boolean {
        return foldFeature != null && foldFeature.state == FLAT && !foldFeature.isSeparating
    }

    interface OnScreenFoldStateChangeListener {
        fun onScreenFoldStateChanged(state: Int)
    }
}