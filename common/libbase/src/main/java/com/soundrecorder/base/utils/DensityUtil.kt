package com.soundrecorder.base.utils

import android.content.Context
import android.util.TypedValue
import androidx.annotation.DimenRes

object DensityUtil {

    @JvmStatic
    fun Context.dp2px(dp: Int): Float {
        val scale = resources.displayMetrics.density
        return (dp * scale + 0.5f)
    }

    @JvmStatic
    fun Context.px2dp(px: Int): Float {
        val scale = resources.displayMetrics.density
        return (px / scale + 0.5f)
    }

    @JvmStatic
    fun Context.getFloatValue(@DimenRes dimenResId: Int): Float {
        val ratioTypedValue = TypedValue()
        resources.getValue(dimenResId, ratioTypedValue, true)
        return ratioTypedValue.float
    }
}