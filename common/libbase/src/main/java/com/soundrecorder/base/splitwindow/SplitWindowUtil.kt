package com.soundrecorder.base.splitwindow

import android.app.Activity
import android.os.Bundle
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ScreenUtil

object SplitWindowUtil {

    private const val TAG = "SplitWindowUtil"
    private const val EXTAR_LAST_IS_WINDOW_PAR = "last_is_windowparameter"
    private const val WINDOW_HEIGHT_560 = 560
    private const val PLAY_SMALL_WINDOW_HEIGHT = 280
    private const val PLAY_WINDOW_HEIGHT_540 = 540

    @JvmStatic
    fun putWindowParameterIntoSaveInstanceState(savedInstanceState: Bundle?, mActivity: Activity) {
        val windowParameter = getCurrentSplitWindowParameter(mActivity)
        savedInstanceState?.putParcelable(EXTAR_LAST_IS_WINDOW_PAR, windowParameter)
        DebugUtil.i(TAG, "putWindowParameterIntoSaveInstanceState $windowParameter")
    }

    @JvmStatic
    fun getCurrentSplitWindowParameter(mActivity: Activity): ISplitWindChangeListener.SplitWindowParameter {
        val isInMultiWindow = mActivity.isInMultiWindowMode
        //这个地方需要判断是否处于分屏状态下，如果处于分屏状态，需要使用相关的SystemProperty判断是否处于折叠和开核状态
        val unfoldStatus = ScreenUtil.isUnFoldStatusWithMultiWindow(mActivity)
        val windowWidth = mActivity.resources.configuration.screenWidthDp
        val windowHeight = mActivity.resources.configuration.screenHeightDp
        val windowParameter = ISplitWindChangeListener.SplitWindowParameter(windowWidth, windowHeight, isInMultiWindow, unfoldStatus)
        DebugUtil.i(TAG, "getCurrentSplitWindowParameter $windowParameter")
        return windowParameter
    }

    /**
     * 判断播放页是否高度小于540
     */
    @JvmStatic
    fun spitWindowHeightLessThanForPlay540(activity: Activity?): Boolean {
        return spitSmallWindowLessThanForPlay(activity, PLAY_WINDOW_HEIGHT_540)
    }

    /**
     * 判断播放页是否高度小于280
     */
    @JvmStatic
    fun spitSmallWindowLessThanForPlay280(activity: Activity?): Boolean {
        return spitSmallWindowLessThanForPlay(activity, PLAY_SMALL_WINDOW_HEIGHT)
    }

    /**
     * 判断分屏情况下裁切界面是否有足够的空间显示下波形，整个分屏高度要大于560dp
     */
    @JvmStatic
    fun isEnoughLayoutWaveView(windowParam: ISplitWindChangeListener.SplitWindowParameter): Boolean {
        return windowParam.splitWindow && (windowParam.windowHeight < WINDOW_HEIGHT_560)
    }

    @JvmStatic
    private fun spitSmallWindowLessThanForPlay(activity: Activity?, height: Int): Boolean {
        var boo = false
        activity?.let {
            if (activity.isInMultiWindowMode) {
                val windowParameter = getCurrentSplitWindowParameter(it)
                DebugUtil.e(TAG, "onLayoutChange windowParameter == ${windowParameter.windowHeight}")
                boo = windowParameter.windowHeight < height
            }
        }
        return boo
    }
}