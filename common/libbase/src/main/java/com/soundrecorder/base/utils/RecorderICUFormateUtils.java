/************************************************************
 * Copyright 2000-2012 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : ColorICUFormateUtils.java
 * Version Number: 1.0
 * Description   :
 * Author        : huangyuanwang
 * Date          : 2019.03.1
 * History       :(ID,  2019.03.1, liyang, Description)
 ************************************************************/
package com.soundrecorder.base.utils;

import android.text.format.DateUtils;
import com.soundrecorder.base.BaseApplication;
import java.text.DateFormat;
import java.util.Date;
import java.util.Locale;

public class RecorderICUFormateUtils {
    public static final String TAG = "RecorderICUFormateUtils";
    public enum DateAndTimeFormatType {
        FULL,
        LONG,
        MEDIUM,
        SHORT
    }

    /**
     * 根据style获取 TimeFormat
     * @param style DateFormat.FULL、DateFormat.LONG、DateFormat.MEDIUM、DateFormat.SHORT
     * @return
     */
    private static DateFormat getTimeFormat(int style) {
        return DateFormat.getTimeInstance(style, Locale.getDefault());
    }

    /**
     * 根据 dateStyle 获取 DateFormat
     * @param dateStyle DateFormat.FULL、DateFormat.LONG、DateFormat.MEDIUM、DateFormat.SHORT
     * @return
     */
    private static DateFormat getDateFormat(int dateStyle) {
        return DateFormat.getDateInstance(dateStyle, Locale.getDefault());
    }

    /**
     * 获取DateTimeFormat
     * @return
     */
    private static DateFormat getDateTimeFormat() {
        return DateFormat.getDateTimeInstance(DateFormat.LONG, DateFormat.MEDIUM, Locale.getDefault());
    }

    private static DateFormat getDateTimeFormatShort() {
        return DateFormat.getDateTimeInstance(DateFormat.LONG, DateFormat.SHORT, Locale.getDefault());
    }

    private static int getDateAndTimeStyle(DateAndTimeFormatType type) {
        int style = DateFormat.DEFAULT;
        switch (type) {
            case FULL:
                style = DateFormat.FULL;
                break;
            case LONG:
                style = DateFormat.LONG;
                break;
            case MEDIUM:
                style = DateFormat.MEDIUM;
                break;
            case SHORT:
                style = DateFormat.SHORT;
                break;
            default:
                break;
        }
        return style;
    }

    public static String formatDate(Date date, DateAndTimeFormatType type) {
        DateFormat dateFormat = getDateFormat(getDateAndTimeStyle(type));
        return dateFormat.format(date);
    }


    public static String formatTime(Date date, DateAndTimeFormatType type) {
        DateFormat timeFormat = getDateFormat(getDateAndTimeStyle(type));
        return timeFormat.format(date);
    }

    public static String formatDateTime(Date date) {
        try {
            return getDateTimeFormat().format(date);
        } catch (Exception e) {
            DebugUtil.e(TAG, "formatDateTime", e);
        }
        return "";
    }

    public static String formatDateTimeShort(Date date) {
        try {
            return getDateTimeFormatShort().format(date);
        } catch (Exception e) {
            DebugUtil.e(TAG, "formatDateTime", e);
        }
        return "";
    }

    public static String formatDateTimeDateUtils(Long timeInMillis, int flags) {
        try {
            return DateUtils.formatDateTime(BaseApplication.getAppContext(), timeInMillis, flags);
        } catch (Exception e) {
            DebugUtil.e(TAG, "formatDateTimeDateUtils", e);
        }
        return "";
    }
}
