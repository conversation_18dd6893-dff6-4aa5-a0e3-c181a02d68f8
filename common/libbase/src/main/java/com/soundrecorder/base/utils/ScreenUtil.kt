/******************************************************************
 * Copyright (C), 2020-2021, OPPO Mobile Comm Corp., Ltd
 * VENDOR_EDIT
 * File: - ScreenUtil.kt
 * Description:
 * Version: 1.0
 * Date :  2020/6/11
 * Author: <EMAIL>
 *
 * ---------------- Revision History: ---------------------------
 * <author>        <data>        <version>        <desc>
 * Yongjiang,Lu      2020/6/11          1.0         build this module
 * */
package com.soundrecorder.base.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.graphics.Insets
import android.graphics.Rect
import android.os.Build
import android.provider.Settings
import android.util.DisplayMetrics
import android.view.WindowInsets
import android.view.WindowManager
import android.view.WindowMetrics
import androidx.annotation.RequiresApi
import com.coui.responsiveui.config.ResponsiveUIConfig
import com.coui.responsiveui.config.UIConfig
import com.soundrecorder.base.BaseApplication
import java.util.Objects
import kotlin.math.abs

object ScreenUtil {

    private const val TAG = "ScreenUtil"

    //折叠状态
    const val FOLD_STATUS_FOLD = 0

    //展开状态
    const val FOLD_STATUS_UNFOLD = 1

    //其他项目没有配置相关属性，默认值为-1
    const val FOLD_STATUS_UNSUPPORT = -1

    //孔雀项目上才配置该 oplus_system_folding_mode 这个SystemProperty， 其他项目没有配置, 用于判断是否处于折叠状态
    const val FOLD_SYSTEM_PROPERTIES_NAME = "oplus_system_folding_mode"

    private const val FULL_SCREEN_DISPLAY_RADIO_LIMIT: Float = 0.8f

    private const val MIN_WIDTH_MIDDLE_WINDOW = 600
    private const val MIN_WIDTH_LARGE_WINDOW = 840

    @JvmStatic
    val screenWidth: Int
        get() {
            val resources = BaseApplication.getAppContext().resources ?: return 0
            val dm = resources.displayMetrics ?: return 0
            return dm.widthPixels
        }

    @JvmStatic
    val screenHeight: Int
        get() {
            val resources = BaseApplication.getAppContext().resources ?: return 0
            val dm = resources.displayMetrics ?: return 0
            return dm.heightPixels
        }

    @JvmStatic
    fun getDiValue(dimension: Int): Int {
        return Objects.requireNonNull(BaseApplication.getAppContext()).resources.getDimension(dimension).toInt()
    }

    @SuppressLint("NewApi")
    @JvmStatic
    fun getRealScreenWidthContainSystemBars(): Int {
        var realScreenWidth = screenWidth
        if (BaseUtil.isAndroidROrLater) {
            try {
                val windowManager = BaseApplication.getAppContext().getSystemService(Context.WINDOW_SERVICE) as WindowManager
                val bounds = windowManager.currentWindowMetrics.bounds
                realScreenWidth = abs(bounds.right - bounds.left)
            } catch (e: Throwable) {
                DebugUtil.e(TAG, "getRealScreenWidthContainSystemBars error:$e")
            }
        }
        DebugUtil.i(TAG, "getRealScreenWidthContainSystemBars, realScreenWidth:$realScreenWidth")
        return realScreenWidth
    }

    @SuppressLint("NewApi")
    @JvmStatic
    fun getRealScreenWidth(): Int {
        var realScreenWidth = screenWidth
        if (BaseUtil.isAndroidROrLater) {
            try {
                val windowManager =
                    BaseApplication.getAppContext().getSystemService(Context.WINDOW_SERVICE) as WindowManager
                val bounds = windowManager.currentWindowMetrics.bounds
                val boundsWidth = abs(bounds.right - bounds.left)
                DebugUtil.i(TAG, "getRealScreenWidth: boundsWidth=$boundsWidth")

                val insetsSys: Insets =
                    windowManager.currentWindowMetrics.windowInsets.getInsetsIgnoringVisibility(WindowInsets.Type.systemBars())
                DebugUtil.i(
                    TAG, "===>insetsSys left: ${insetsSys.left}, " +
                            "top ${insetsSys.top}, rigtht ${insetsSys.right}, bottom ${insetsSys.bottom}"
                )

                realScreenWidth = boundsWidth - insetsSys.right - insetsSys.left
            } catch (e: Throwable) {
                DebugUtil.e(TAG, "getRealScreenWidth error:$e")
            }
        }
        DebugUtil.i(TAG, "getRealScreenWidth, realScreenWidth:$realScreenWidth")
        return realScreenWidth
    }

    @SuppressLint("NewApi")
    @JvmStatic
    fun getRealHeight(context: Context): Int {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val display = windowManager.defaultDisplay
        val dm = DisplayMetrics()
        if (BaseUtil.isAndroidJELLY_BEAN_MR1OrLater) {
            display.getRealMetrics(dm)
        } else {
            display.getMetrics(dm)
        }
        return dm.heightPixels
    }

    /**
     * 获取当前屏幕的尺寸
     */
    @SuppressLint("NewApi")
    @JvmStatic
    fun getRealBounds(activity: Activity): Rect {
        val rect = Rect()
        val windowManager = BaseApplication.getAppContext().getSystemService(Context.WINDOW_SERVICE) as WindowManager
        if (BaseUtil.isAndroidROrLater) {
            val windowMetrics = windowManager.currentWindowMetrics
            val bounds = windowMetrics.bounds
            rect.set(bounds)
        } else {
            var navigationBarHeight = -1
            val resourceId: Int = activity.resources.getIdentifier("navigation_bar_height", "dimen", "android")
            if (resourceId > 0) {
                navigationBarHeight = activity.resources.getDimensionPixelSize(resourceId)
            }
            rect.set(Rect(0, 0, screenWidth, screenHeight + navigationBarHeight))
        }
        DebugUtil.d(TAG, "getRealBounds: rect=$rect")
        return rect
    }


    /**
     * 该函数为非分屏状态下，返回值ok，分屏状态下unfold状态不准确，不建议后续使用
     */
    @Deprecated("后续删除")
    @JvmStatic
    fun isUnFoldStatus(context: Context): Boolean {
        val responsiveUIConfig = ResponsiveUIConfig.getDefault(context)
        DebugUtil.i(TAG, "getUiStatus: " + responsiveUIConfig.uiStatus.value)
        if (responsiveUIConfig.uiStatus.value == UIConfig.Status.UNFOLD) {
            DebugUtil.i(TAG, "isUnFoldStatus: true")
            return true
        }
        DebugUtil.i(TAG, "isUnFoldStatus: false")
        return false
    }

    @JvmStatic
    fun isUnFoldPortraitStatus(context: Context): Boolean {
        val responsiveUIConfig = ResponsiveUIConfig.getDefault(context)
        if (isUnFoldStatusWithMultiWindow(context)
            && (responsiveUIConfig.uiOrientation.value == Configuration.ORIENTATION_PORTRAIT)
        ) {
            DebugUtil.i(TAG, "isUnFoldPortraitStatus: true")
            return true
        }
        DebugUtil.i(TAG, "isUnFoldPortraitStatus: false")
        return false
    }


    /**
     *  这里新增一种场景，分屏下需要单独社设置window的fitSystemWindow=false；非分屏下需要在孔雀展开+竖屏情况下进行相关操作。
     */
    @JvmStatic
    fun checkNeedSetFitWindowsFalse(activity: Activity): Boolean {
        val result = isUnFoldPortraitStatus(activity) || activity.isInMultiWindowMode
        return result
    }

    @JvmStatic
    fun isUnFoldStatusWithMultiWindow(context: Context): Boolean {
        var foldStatus = FOLD_STATUS_UNSUPPORT
        var isUnfold = false
        try {
            foldStatus =
                Settings.Global.getInt(context.contentResolver, FOLD_SYSTEM_PROPERTIES_NAME, FOLD_STATUS_UNSUPPORT)
            DebugUtil.i(TAG, " isUnFoldStatusWithMultiWindow $foldStatus")
        } catch (e: Exception) {
            DebugUtil.e(TAG, "setDefaultDisplay error,e:" + e.message)
        }
        isUnfold = (foldStatus == FOLD_STATUS_UNFOLD)
        DebugUtil.i(TAG, " isUnFoldStatusWithMultiWindow isUnfold : $isUnfold")
        return isUnfold
    }

    @RequiresApi(Build.VERSION_CODES.R)
    @JvmStatic
    fun getStatusBarInsetsHeight(context: Context): Int {
        try {
            var windowManager: WindowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            var windowMetrics: WindowMetrics = windowManager.currentWindowMetrics
            var insets: Insets = windowMetrics.windowInsets.getInsetsIgnoringVisibility(WindowInsets.Type.statusBars())
            DebugUtil.i(
                TAG,
                "getStatusBarInsetsHeight left: ${insets.left}, top ${insets.top}, rigtht ${insets.right}, bottom ${insets.bottom}"
            )
            return Math.max(Math.max(insets.left, insets.top), Math.max(insets.right, insets.bottom))
        } catch (e: Exception) {
            DebugUtil.e(TAG, "getStatusBarInsetsHeight error", e)
        }
        return -1
    }

    @JvmStatic
    fun getStatusBarHeight(context: Context): Int {
        var result = 0
        val resId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resId > 0) {
            result = context.resources.getDimensionPixelSize(resId)
        }
        return result
    }

    /**
     * 当本地布局与系统定义不一致的时候使用
     */
    @JvmStatic
    fun isSmallScreen(context: Context?): Boolean {
        val config: Configuration = (context ?: BaseApplication.getAppContext()).resources.configuration
        return getWindowType(config) == WindowType.SMALL
    }

    @JvmStatic
    fun isBelowMiddleScreen(): Boolean {
        return getWindowType() != WindowType.LARGE
    }

    /**
     * 小屏：小于600dp
     * 中屏：大于等于600，小于840dp
     * 大屏：大于等于 840dp
     */
    @JvmStatic
    fun getWindowType(configuration: Configuration? = null): WindowType {
        val cg = configuration ?: BaseApplication.getAppContext().resources.configuration
        val wdp = cg.screenWidthDp
        DebugUtil.d(TAG, "getWindowType wdp=$wdp, height=${cg.screenHeightDp}")
        return when {
            (wdp < MIN_WIDTH_MIDDLE_WINDOW) -> WindowType.SMALL
            (wdp < MIN_WIDTH_LARGE_WINDOW) -> WindowType.MIDDLE
            else -> WindowType.LARGE
        }
    }

    @JvmStatic
    fun isFloatingWindow(activity: Activity): Boolean {
        if (activity.isInMultiWindowMode) {
            DebugUtil.d(TAG, "isFloatingWindow activity is in multi window mode.")
            return true
        }

        if (activity.isInPictureInPictureMode) {
            DebugUtil.d(TAG, "isFloatingWindow activity is in PIP mode.")
            return true
        }

        var phoneScreenHeight = 0
        var phoneScreenWidth = 0
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val windowMetricsBounds = activity.windowManager.currentWindowMetrics.bounds
            phoneScreenHeight = windowMetricsBounds.height()
            phoneScreenWidth = windowMetricsBounds.width()
        } else {
            val displayMetrics = DisplayMetrics()
            activity.windowManager.defaultDisplay.getMetrics(displayMetrics)
            phoneScreenWidth = displayMetrics.widthPixels
            phoneScreenHeight = displayMetrics.heightPixels
        }

        if (phoneScreenHeight == 0 || phoneScreenWidth == 0) {
            DebugUtil.d(TAG, "isFloatingWindow window size is 0.")
            return false
        }

        val activityVisibleRect = Rect()
        activity.window.decorView.getWindowVisibleDisplayFrame(activityVisibleRect)
        val activityHeight = activityVisibleRect.height()
        val activityWidth = activityVisibleRect.width()
        val heightRatio =  activityHeight.toFloat() / phoneScreenHeight
        val widthRatio =  activityWidth.toFloat() / phoneScreenWidth
        return (heightRatio < FULL_SCREEN_DISPLAY_RADIO_LIMIT) && (widthRatio < FULL_SCREEN_DISPLAY_RADIO_LIMIT)
    }
}

enum class WindowType {
    SMALL,
    MIDDLE,
    LARGE
}