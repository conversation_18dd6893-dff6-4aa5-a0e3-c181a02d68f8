package com.soundrecorder.base.utils

import android.app.Activity
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.appcompat.app.AlertDialog
import com.soundrecorder.base.BaseApplication

object KeyboardUtils {
    private val inputMethodManager by lazy {
        BaseApplication.getAppContext().getSystemService(InputMethodManager::class.java)
    }

    @JvmStatic
    fun View.showSoftInput() {
        requestFocus()
        inputMethodManager.showSoftInput(this, 0)
    }

    @JvmStatic
    fun hideSoftInput(activity: Activity) {
        val view = activity.window.decorView
        inputMethodManager.hideSoftInputFromWindow(
            view.windowToken,
            0
        )
    }

    @JvmStatic
    fun hideSoftInputFromDialog(dialog: AlertDialog?) {
        inputMethodManager.hideSoftInputFromWindow(
            dialog?.window?.decorView?.windowToken,
            0
        )
    }
}