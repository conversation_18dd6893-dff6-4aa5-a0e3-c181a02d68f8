/************************************************************
 * Copyright 2000-2009 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : NightModeUtil.java
 * Version Number: 1.0
 * Description   :
 * Author        : huangyuanwang
 * Date          : 2019-8-2
 * History       :(ID,  2009-12-15, zhanghr, uimode is night mode)
 ************************************************************/

package com.soundrecorder.base.utils;

import android.app.UiModeManager;
import android.content.Context;
import com.coui.appcompat.darkmode.COUIDarkModeUtil;

public class NightModeUtil {

    public static final String TAG = "NightModeUtil";
    public static final float EMPTY_ICON_ALPHA = 0.4F;

    //增强模式
    public static final int NIGHT_MODE_ENHANCED = 0;
    //适中
    public static final int NIGHT_MODE_NORMAL = 1;
    //柔和
    public static final int NIGHT_MODE_SOFT = 2;

    public static boolean isNightMode(Context context) {
        if (BaseUtil.isAndroidROrLater()) {
            return COUIDarkModeUtil.isNightMode(context);
        } else {
            UiModeManager uiModeManager = (UiModeManager) context.getSystemService(Context.UI_MODE_SERVICE);
            if (uiModeManager != null) {
                boolean result = uiModeManager.getNightMode() == UiModeManager.MODE_NIGHT_YES;
                return result;
            }
            return false;
        }
    }
}
