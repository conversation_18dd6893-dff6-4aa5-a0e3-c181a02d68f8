/***********************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ReflectionCache.java
 * * Description: ReflectionCache.java
 * * Version: 1.0
 * * Date : 2019/11/15
 * * Author: liuyulong
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version >    <desc>
 * * liuyulong  2019/11/15      1.0    build this module
 ****************************************************************/
package com.soundrecorder.base.utils;

import java.lang.reflect.Method;
import java.util.HashMap;

public class ReflectionCache {

    private static HashMap<String, ClassInfo> classInfoMap;

    private ReflectionCache() {
        classInfoMap = new HashMap<>();
    }

    public static ReflectionCache build() {
        return SingletonHolder.INSTANCE;
    }

    private void putClassInfoToCache(String key, ClassInfo classInfo) {
        classInfoMap.put(key, classInfo);
    }

    private ClassInfo getClassInfoFromCache(String key) {
        return classInfoMap.get(key);
    }

    public Class<?> forName(String className) throws ClassNotFoundException {
        return this.forName(className, true);
    }

    public Class<?> forName(String className, Boolean isCached) throws ClassNotFoundException {
        if (isCached) {
            ClassInfo classInfoFromCache = this.getClassInfoFromCache(className);
            if (classInfoFromCache != null) {
                return classInfoFromCache.mClass;
            } else {
                Class c = Class.forName(className);
                ClassInfo classInfo = new ClassInfo(c, className);
                this.putClassInfoToCache(className, classInfo);
                return c;
            }
        } else {
            return Class.forName(className);
        }
    }

    public Method getMethod(Class<?> objClass, String methodName, Class... parameterTypes) throws NoSuchMethodException {
        ClassInfo classInfoFromCache = this.getClassInfoFromCache(objClass.getName());
        StringBuilder methodKey = new StringBuilder(methodName);

        for (Class<?> c : parameterTypes) {
            methodKey.append(c.toString());
        }

        Method methodFromCache;
        if (classInfoFromCache != null) {
            methodFromCache = classInfoFromCache.getCachedMethod(methodKey.toString());
            if (methodFromCache != null) {
                return methodFromCache;
            } else {
                Method method = objClass.getMethod(methodName, parameterTypes);
                classInfoFromCache.addCachedMethod(methodKey.toString(), method);
                return method;
            }
        } else {
            methodFromCache = objClass.getMethod(methodName, parameterTypes);
            return methodFromCache;
        }
    }

    private static class SingletonHolder {
        private static final ReflectionCache INSTANCE = new ReflectionCache();

        private SingletonHolder() {
        }
    }
}
