/************************************************************
 * Copyright 2000-2012 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : Singleton.java
 * Version Number: 1.0
 * Description   :
 * Author        : mao_hongyu
 * Date          : 2019.08.28
 * History       :(ID          ,Data        ,Name       ,Description)
 *                 W9001268    ,2019.08.28  ,mao_hongyu
 ************************************************************/
package com.soundrecorder.base.utils;

import android.util.Pair;

public abstract class TypeFaceConfigure<T, S> {

    private Pair<T, S> pair;

    protected abstract Pair<T, S> create();

    public final Pair<T, S> get() {
        synchronized (this) {
            if (pair == null) {
                pair = create();
            }
            return pair;
        }
    }
}
