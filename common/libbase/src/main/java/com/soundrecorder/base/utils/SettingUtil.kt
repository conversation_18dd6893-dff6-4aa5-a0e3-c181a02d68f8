/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SettingUtil.kt
 ** Description : SettingUtil.kt
 ** Version     : 1.0
 ** Date        : 2025/07/15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/07/15     1.0      create
 ***********************************************************************/
package com.soundrecorder.base.utils

import android.content.Context
import android.graphics.Color
import android.os.Build
import android.provider.Settings
import android.view.View
import android.view.Window

object SettingUtil {
    private const val NAVIGATION_MODE_R_2 = "hide_navigationbar_enable"
    private const val NAVIGATION_MODE_S = "navigation_mode"
    private const val NAV_STATE_SWIPE_UP_GESTURE = 2
    private const val NAV_STATE_SWIPE_SIDE_GESTURE = 3
    private const val NAV_STATE_FULLY_GESTURAL_S = 2

    @JvmStatic
    fun isGestureNavMode(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.R) {
            // R版本的判断
            val navigationMode = Settings.Secure.getInt(context.contentResolver, NAVIGATION_MODE_R_2, 0)
            navigationMode == NAV_STATE_SWIPE_UP_GESTURE || navigationMode == NAV_STATE_SWIPE_SIDE_GESTURE
        } else {
            // S 版本的判断
            Settings.Secure.getInt(context.contentResolver, NAVIGATION_MODE_S, 0) == NAV_STATE_FULLY_GESTURAL_S
        }
    }

    @JvmStatic
    fun gestureNavTransparent(context: Context, window: Window) {
        if (isGestureNavMode(context)) {
            window.decorView.systemUiVisibility = (
                    window.decorView.systemUiVisibility
                            or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                            or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                            or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    )
            if (BaseUtil.isAndroidQOrLater) {
                window.isNavigationBarContrastEnforced = false
            }
            window.navigationBarColor = Color.TRANSPARENT
        }
    }
}