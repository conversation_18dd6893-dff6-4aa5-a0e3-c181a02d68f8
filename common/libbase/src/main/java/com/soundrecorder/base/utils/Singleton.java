/************************************************************
 * Copyright 2000-2012 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : Singleton.java
 * Version Number: 1.0
 * Description   :
 * Author        : mao_hongyu
 * Date          : 2019.08.28
 * History       :(ID          ,Data        ,Name       ,Description)
 *                 W9001268    ,2019.08.28  ,mao_hongyu
 ************************************************************/
package com.soundrecorder.base.utils;

public abstract class Singleton<T> {
    private T mInstance;

    protected abstract T create();

    public final T get() {
        synchronized (this) {
            if (mInstance == null) {
                mInstance = create();
            }
            return mInstance;
        }
    }
}