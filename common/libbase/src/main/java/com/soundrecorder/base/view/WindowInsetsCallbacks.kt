/***********************************************************
 * * Copyright (C), 2019-2027, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File: RootViewInsetsListener.KT
 * * Description:
 * * Version: 1.0
 * * Date : 2021/6/26
 * * Author: zengzhigang
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.soundrecorder.base.view

import android.view.View
import androidx.core.view.OnApplyWindowInsetsListener
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.SettingUtil
import java.util.*

abstract class DeDuplicateInsetsCallback : OnApplyWindowInsetsListener {

    companion object {
        private var TAG = "DeDuplicateInsetsCallback"
    }

    private var lastWindowInsets: WindowInsetsCompat? = null

    override fun onApplyWindowInsets(v: View, insets: WindowInsetsCompat): WindowInsetsCompat {
        if (!Objects.equals(lastWindowInsets, insets)) {
            DebugUtil.i(TAG, "lastWindowInsets $lastWindowInsets, insets $insets")
            lastWindowInsets = insets
            onApplyInsets(v, insets)
        }
        // Return the insets so that they keep going down the view hierarchy
        return insets
    }

    abstract fun onApplyInsets(v: View, insets: WindowInsetsCompat)
}

open class RootViewPersistentInsetsCallback : DeDuplicateInsetsCallback() {

    override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
        val systemBarInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
        val stableStatusBarInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
        val statusBarVisibility = insets.isVisible(WindowInsetsCompat.Type.statusBars())
        val navigationBarVisibility = insets.isVisible(WindowInsetsCompat.Type.navigationBars())
        val cutoutInsets = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.displayCutout())
        var marginBottom = stableStatusBarInsets.bottom
        val isGestureNavMode = SettingUtil.isGestureNavMode(BaseApplication.getAppContext())
        if (isGestureNavMode) {
            marginBottom = 0
        }

        DebugUtil.i("RootViewPersistentInsetsCallback", "onApplyInsets : systemBarInsets $systemBarInsets, " +
                "stableStatusBarInsets $stableStatusBarInsets, " +
                "cutoutInsets $cutoutInsets, " +
                "statusBarVisibility: $statusBarVisibility, " +
                "navigationBarVisibility $navigationBarVisibility")
        v.updatePadding(
                left = stableStatusBarInsets.left,
                top = stableStatusBarInsets.top,
                right = stableStatusBarInsets.right,
                bottom = marginBottom
        )
    }
}