package com.soundrecorder.base.arms

import android.app.Application
import android.util.Log

class AppDelegate(val application: Application) {

    companion object {
        const val TAG = "AppDelegate"
    }

    private var mConfigModuleList: List<ConfigModule>? = null

    init {
        val manifestParser = ManifestParser(application)
        mConfigModuleList = manifestParser.parse()
        Log.i(
            TAG,
            "init after parse mConfigModuleList $mConfigModuleList, size ${mConfigModuleList?.size}"
        )
    }

    fun onApplicationCreate() {
        mConfigModuleList?.forEach {
            it.onApplicationCreate(application)
        }
    }
}