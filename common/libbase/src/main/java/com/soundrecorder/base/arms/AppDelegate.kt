package com.soundrecorder.base.arms

import android.app.Application
import com.soundrecorder.base.utils.DebugUtil

class AppDelegate(val application: Application) {

    companion object {
        const val TAG = "AppDelegate"
    }

    private var mConfigModuleList: List<ConfigModule>? = null

    init {
        val manifestParser = ManifestParser(application)
        mConfigModuleList = manifestParser.parse()
        DebugUtil.i(
            TAG,
            "init after parse mConfigModuleList $mConfigModuleList, size ${mConfigModuleList?.size}"
        )
    }

    fun onApplicationCreate() {
        mConfigModuleList?.forEach {
            it.onApplicationCreate(application)
        }
    }
}