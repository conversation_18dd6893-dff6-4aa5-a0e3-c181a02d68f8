/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ArrayPool
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/11/3
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import java.util.Queue
import java.util.concurrent.ConcurrentLinkedQueue

abstract class ArrayPool<T> {

    companion object {
        private const val TAG = "ArrayPool"
        private const val MAX_ARRAY_SIZE = 64 * 1024 // 64 KB.
        private const val MAX_ARRAY_COUNT = 32
    }

    private val tempQueue: Queue<T> = ConcurrentLinkedQueue()
    private var mTempArraySize = MAX_ARRAY_SIZE
    private var mMaxCount = MAX_ARRAY_COUNT

    constructor() : this(-1)

    constructor(arrayMaxLength: Int, maxCount: Int = MAX_ARRAY_COUNT) {
        if (arrayMaxLength > 0) {
            mTempArraySize = arrayMaxLength
        }
        if (maxCount > 0) {
            mMaxCount = maxCount
        }
    }

    fun clear() {
        synchronized(tempQueue) { tempQueue.clear() }
    }

    /**
     * 从队列中取出一个 byte[],没有就创建新的返回
     */
    fun getArrayInstance(): T {
        var result: T?
        synchronized(tempQueue) { result = tempQueue.poll() }
        if (result == null) {
            result = newArrayInstance(mTempArraySize)
        }
        return result!!
    }

    /**
     * 创建一个array新实例
     */
    protected abstract fun newArrayInstance(arraySize: Int): T

    /**
     * 获取array的大小
     */
    protected abstract fun getArraySize(array: T): Int

    /**
     * 使用完了放回队列，但是要维持总内存不超过 max
     */
    fun releaseArray(array: T): Boolean {
        val arraySize = getArraySize(array)
        DebugUtil.d(TAG, "releaseArray, arraySize = $arraySize, mTempArraySize = $mTempArraySize")
        if (arraySize != mTempArraySize) {
            return false
        }
        var accepted = false
        synchronized(tempQueue) {
            if (tempQueue.size < mMaxCount) {
                accepted = true
                tempQueue.offer(array)
            }
        }
        return accepted
    }
}

class ByteArrayPool : ArrayPool<ByteArray> {

    companion object {
        private const val TAG = "ByteArrayPool"
        private var instances = mutableMapOf<String, ByteArrayPool>()

        @JvmStatic
        fun getInstance(tag: String, maxSize: Int): ByteArrayPool {
            var instance = instances[tag]
            if (instance == null) {
                instance = ByteArrayPool(maxSize)
                instances[tag] = instance
            }
            return instance
        }

        @JvmStatic
        fun releaseArray(tag: String, data: ByteArray) {
            instances[tag]?.releaseArray(data)
        }

        @JvmStatic
        fun clear(tag: String) {
            instances[tag]?.clear()
            instances.remove(tag)
        }
    }

    constructor() : super()

    constructor(size: Int) : super(size)

    override fun newArrayInstance(arraySize: Int): ByteArray {
        DebugUtil.d(TAG, "Created byte array, maxSize = $arraySize")
        return ByteArray(arraySize)
    }

    override fun getArraySize(array: ByteArray): Int {
        return array.size
    }
}

class ShortArrayPool : ArrayPool<ShortArray> {

    companion object {
        private const val TAG = "ShortArrayPool"
        private var instances = mutableMapOf<String, ShortArrayPool>()

        @JvmStatic
        fun getInstance(tag: String, maxSize: Int): ShortArrayPool {
            var instance = instances[tag]
            if (instance == null) {
                DebugUtil.d(TAG, "getInstance, tag = $tag, maxSize = $maxSize")
                instance = ShortArrayPool(maxSize)
                instances[tag] = instance
            }
            return instance
        }

        @JvmStatic
        fun releaseArray(tag: String, data: ShortArray) {
            instances[tag].let {
                DebugUtil.d(TAG, "releaseArray, shortArrayPool = $it, data = $data")
                it?.releaseArray(data)
            }
        }

        @JvmStatic
        fun clear(tag: String) {
            instances[tag]?.clear()
            instances.remove(tag)
        }
    }

    constructor() : super()

    constructor(size: Int) : super(size)

    override fun newArrayInstance(arraySize: Int): ShortArray {
        DebugUtil.d(TAG, "Created short array, maxSize = $arraySize")
        return ShortArray(arraySize)
    }

    override fun getArraySize(array: ShortArray): Int {
        return array.size
    }
}