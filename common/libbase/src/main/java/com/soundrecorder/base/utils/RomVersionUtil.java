/***********************************************************
 ** Copyright (C), 2008-2018, OPPO Mobile Comm Corp., Ltd.
 ** COLOROS_EDIT
 ** File: - RomVersionUtil.java
 ** Description: Implemented StatusBarUtil to set transparent and black-font status bar
 ** Version: 1.0
 ** Date: 2019/9/11
 ** Author: mao_hongyu
 **
 ** ---------------------Revision History: ---------------------
 ** <author>  <date>       <version>    <desc>
 **
 ***************************************************************/
package com.soundrecorder.base.utils;

import java.lang.reflect.Method;

public class RomVersionUtil {
    public static final int UNKNOWN = 0;
    public static final int ColorOS_1_0 = 1;
    public static final int ColorOS_1_2 = 2;
    public static final int ColorOS_1_4 = 3;
    public static final int ColorOS_2_0 = 4;
    public static final int ColorOS_2_1 = 5;
    public static final int ColorOS_3_0 = 6;



    public static int getRomVersionCode() {
        int curRomVersion = UNKNOWN;
        try {
            Class<?> romClass = Class.forName("com.oplus.os.OplusBuild");
            if (romClass == null) {
                return curRomVersion;
            }
            Method getColorOSVERSION = romClass.getDeclaredMethod("getColorOSVERSION");
            Object o = getColorOSVERSION.invoke(romClass);
            curRomVersion = ((Integer) o).intValue();

        } catch (Exception e) {
            DebugUtil.e("RomVersionUtil", "getRomVersionCode failed. error = " + e.getMessage());
        }
        return curRomVersion;
    }

}
