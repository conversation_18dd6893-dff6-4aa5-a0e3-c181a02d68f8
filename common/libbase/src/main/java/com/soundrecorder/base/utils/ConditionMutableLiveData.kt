/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConditionMutableLiveData
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/07/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.utils

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import java.util.Objects

open class ConditionMutableLiveData<T>(
    private var originLiveData: LiveData<T>?,
    setInitValue: Boolean = false
) : MutableLiveData<T>(originLiveData?.value) {

    var oldValue: T? = if (setInitValue) value else null
        private set

    open fun needDispatchValue(newValue: T?): Boolean {
        return newValue != oldValue
    }

    override fun getValue(): T? {
        return originLiveData?.value
    }

    override fun observe(owner: LifecycleOwner, observer: Observer<in T>) {
        originLiveData?.observe(owner, createObserverWrapper(observer))
    }

    override fun observeForever(observer: Observer<in T>) {
        originLiveData?.observeForever(createObserverWrapper(observer))
    }

    override fun removeObserver(observer: Observer<in T>) {
        if (observer.javaClass.isAssignableFrom(ObserverWrapper::class.java)) {
            originLiveData?.removeObserver(observer)
        } else {
            originLiveData?.removeObserver(createObserverWrapper(observer))
        }
    }

    override fun removeObservers(owner: LifecycleOwner) {
        originLiveData?.removeObservers(owner)
    }

    private fun createObserverWrapper(observer: Observer<in T>): Observer<in T> {
        return ObserverWrapper(observer)
    }

    inner class ObserverWrapper(val observer: Observer<in T>) : Observer<T> {
        override fun onChanged(value: T) {
            if (needDispatchValue(value)) {
                oldValue = value
                observer.onChanged(value)
            }
        }

        override fun equals(other: Any?): Boolean {
            if (this === other) {
                return true
            }
            if (other == null || javaClass != other.javaClass) {
                return false
            }
            return (other as? ConditionMutableLiveData<T>.ObserverWrapper)?.observer == observer
        }

        override fun hashCode(): Int {
            return Objects.hash(observer)
        }
    }
}