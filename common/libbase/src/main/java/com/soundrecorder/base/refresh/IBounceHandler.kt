/*********************************************************************************
 ** Copyright (C) 2020 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - IBounceHandler.kt
 ** Description:
 **
 ** Version: 1.1
 ** Date: 2019-04-30
 ** Author: <EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** <EMAIL>              2019-04-30   1.1         Convert this demo into Kotlin
 ********************************************************************************/


package com.soundrecorder.base.refresh

import android.view.View

/**
 * Handling sliding conflicts in the vertical direction
 */
interface IBounceHandler {
    // Whether the child can pull up
    fun canChildPull(v: View): Boolean
    // Whether the child can pull down
    fun canChildDrag(v: View): Boolean
}