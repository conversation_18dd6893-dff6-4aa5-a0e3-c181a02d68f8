/************************************************************
 * Copyright 2010-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description : ***************
 * History :( ID, Date, Author, Description)
 * v1.0, 2018/1/23, HongZ<PERSON><PERSON>Liu, create
 ************************************************************/
package com.soundrecorder.base.utils;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.widget.Toast;

import androidx.arch.core.executor.ArchTaskExecutor;

import com.soundrecorder.base.BaseApplication;

public class ToastManager {

    private static final String TAG = "ToastManager";

    private static Toast sToast = null;

    public static void showLongToast(int resId) {
        showToast(BaseApplication.getAppContext(), resId, Toast.LENGTH_LONG);
    }

    public static void showLongToast(Context context, int resId) {
        showToast(context, resId, Toast.LENGTH_LONG);
    }

    public static void showLongToast(CharSequence str) {
        showToast(BaseApplication.getAppContext(), str, Toast.LENGTH_LONG);
    }

    public static void showLongToast(Context context, String str) {
        showToast(context, str, Toast.LENGTH_LONG);
    }

    public static void showShortToast(Context context, int resId) {
        showToast(context, resId, Toast.LENGTH_SHORT);
    }

    public static void showShortToast(Context context, String str) {
        showToast(context, str, Toast.LENGTH_SHORT);
    }

    public static void showToast(Context context, int resId, int duration) {
        if (null == context) {
            return;
        }
        CharSequence res = null;
        try {
            res = context.getResources().getText(resId);
        } catch (Resources.NotFoundException e) {
            DebugUtil.e(TAG, "resId " + resId + " not found");
        }
        showToast(context, res, duration);
    }

    @SuppressLint("RestrictedApi")
    public static void showToast(Context context, final CharSequence str, final int duration) {
        if (null == context) {
            return;
        }
        if (null == str) {
            return;
        }
        final Context appContext = context.getApplicationContext();
        if (null == appContext) {
            return;
        }

        DebugUtil.d(TAG, "Schedule show toast: " + str.hashCode());
        ArchTaskExecutor.getInstance().executeOnMainThread(() -> {
            if (null != sToast) {
                sToast.cancel();
            }

            DebugUtil.d(TAG, "Actual show toast: " + str.hashCode());
            sToast = Toast.makeText(appContext, str, duration);
            sToast.show();
        });
    }

    public static void clearToast() {
        if (sToast != null) {
            sToast.cancel();
            sToast = null;
        }
    }

}
