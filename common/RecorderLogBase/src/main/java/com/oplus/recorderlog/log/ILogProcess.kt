package com.oplus.recorderlog.log

import android.content.Context
import com.soundrecorder.modulerouter.xlog.RecordLogXConfig

interface ILogProcess : ILog {

    fun initLog(context: Context)

    fun flushLog(isSync: Boolean)

    fun processPushLog(context: Context?, cloudLogConfigMsg: RecordLogXConfig)

    fun processManualReportLog()

    fun processDBPrint(context: Context?)

    fun isLogOpen(): Boolean = true
}