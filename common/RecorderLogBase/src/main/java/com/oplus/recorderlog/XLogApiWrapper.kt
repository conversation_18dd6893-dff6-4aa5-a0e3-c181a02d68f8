package com.oplus.recorderlog

import android.content.Context
import com.oplus.recorderlog.log.ILogProcess
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.modulerouter.xlog.RecordLogXConfig
import com.soundrecorder.modulerouter.xlog.RecorderLogInterface

class XLogApiWrapper : ILogProcess {

    private val logApi by lazy {
        Injector.injectFactory<RecorderLogInterface>()
    }

    override fun v(tag: String?, message: String?) {
        logApi?.v(tag, message)
    }

    override fun d(tag: String?, message: String?) {
        logApi?.d(tag, message)
    }

    override fun i(tag: String?, message: String?) {
        logApi?.i(tag, message)
    }

    override fun w(tag: String?, message: String?) {
        logApi?.w(tag, message)
    }

    override fun e(tag: String?, message: String?) {
        logApi?.e(tag, message)
    }

    override fun e(tag: String?, message: String?, e: Throwable?) {
        logApi?.e(tag, message, e)
    }

    override fun initLog(context: Context) {
        logApi?.initLog(context)
    }

    override fun flushLog(isSync: Boolean) {
        logApi?.flushLog(isSync)
    }

    override fun processPushLog(context: Context?, cloudLogConfigMsg: RecordLogXConfig) {
        logApi?.processPushLog(context, cloudLogConfigMsg)
    }

    override fun processManualReportLog() {
        logApi?.processManualReportLog()
    }

    override fun processDBPrint(context: Context?) {
        logApi?.processDBPrint(context)
    }
}