/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ImageParseResult
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/6
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.imageload.utils

data class ImageParseResult(var width: Int = -1, var height: Int = -1) {

    companion object {
        //宽高比大于4：1时，识别为横向长图
        const val LONG_PICTURE_LANDSCAPE_RATIO_LIMIT = 4.0f
        //宽高比小于1：3时，识别为纵向长图
        const val LONG_PICTURE_PORTRAIT_RATIO_LIMIT = 1f / 3f
    }


    fun getWithHeightRatio(): Float {
        return width.toFloat() / height.toFloat()
    }


    fun isLongPicture(): Boolean {
        return isLandScapLongPicture() || isPortraitLongPicture()
    }

    fun isLandScapLongPicture(): Boolean {
        return getWithHeightRatio() >= LONG_PICTURE_LANDSCAPE_RATIO_LIMIT
    }

    fun isPortraitLongPicture(): Boolean {
        return getWithHeightRatio() <= LONG_PICTURE_PORTRAIT_RATIO_LIMIT
    }
}