/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ImageLoaderUtilsTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.imageload

import android.os.Build
import android.widget.ImageView
import androidx.test.ext.junit.runners.AndroidJUnit4
import coil.Coil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.imageload.ImageLoaderUtils.into
import com.soundrecorder.imageload.shadows.ShadowFeatureOption
import com.soundrecorder.imageload.shadows.ShadowOS12FeatureUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class ImageLoaderUtilsTest {
    private lateinit var mainScope: CoroutineScope

    @Before
    fun setUp() {
        mainScope = MainScope()
    }

    @After
    fun tearDown() {
        mainScope.cancel()
    }

    @Test
    fun into() {
        val imageView = ImageView(BaseApplication.getAppContext())
        val file = FileUtils.getAppFile("img.jpg", true)
        imageView.into(ImageLoadData(file, 1, 1))
    }

    @Test
    fun getScaleBitmap() {
        mainScope.launch {
            val file = FileUtils.getAppFile("img.jpg", true)
            val bitmap = ImageLoaderUtils.getScaleBitmap(ImageLoadData(file, 720, 1080))
            Assert.assertFalse(bitmap == null)
        }
    }

    @Test
    fun clearMemoryCache() {
        ImageLoaderUtils.clearMemoryCache()
        Assert.assertTrue(Coil.imageLoader(BaseApplication.getAppContext()).memoryCache?.size == 0)
    }
}