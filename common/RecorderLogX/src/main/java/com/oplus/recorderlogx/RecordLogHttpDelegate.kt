package com.oplus.recorderlogx

import com.oplus.recorderlog.log.RecorderLogger
import com.oplus.recorderlog.util.GsonUtil
import java.io.*
import java.net.HttpURLConnection
import java.net.URL

class RecordLogHttpDelegate : com.oplus.log.uploader.IHttpDelegate {

    companion object {
        const val TAG = "RecordLogHttpDelegate"
    }


    @Throws(IOException::class)
    override fun uploadFile(url: String?, file: File?): com.oplus.log.uploader.ResponseWrapper? {
        var message = ""
        var inputStream: InputStream? = null
        var fileInputStream: FileInputStream? = null
        var dataOutputStream: DataOutputStream? = null
        try {
            val Url = URL(url)
            val con = Url.openConnection() as HttpURLConnection
            // 允许Input、Output，不使用Cache
            con.doInput = true
            con.doOutput = true
            con.useCaches = false
            // 设置以POST方式进行传送
            con.requestMethod = "POST"
            // 设置RequestProperty
            con.setRequestProperty("Connection", "Keep-Alive")
            con.setRequestProperty("Charset", "UTF-8")
            con.setRequestProperty("Content-Type", "application/octet-stream")
            con.setRequestProperty("Accept", "application/json")

            // 构造DataOutputStream流
            if (file != null) {
                dataOutputStream = DataOutputStream(con.outputStream)

                // 构造要上传文件的FileInputStream流
                fileInputStream = FileInputStream(file)
                // 设置每次写入1024bytes
                val bufferSize = 1024
                val buffer = ByteArray(bufferSize)
                var length: Int
                // 从文件读取数据至缓冲区
                while (fileInputStream.read(buffer).also { length = it } != -1) {
                    // 将资料写入DataOutputStream中
                    dataOutputStream.write(buffer, 0, length)
                }

                //关闭流
                dataOutputStream.flush()
                inputStream = con.inputStream
                val sb = StringBuilder()
                var line: String?
                val br = BufferedReader(InputStreamReader(inputStream))
                while (br.readLine().also { line = it } != null) {
                    sb.append(line)
                }
                message = sb.toString()
            } else {
                con.connect()
            }
            return com.oplus.log.uploader.ResponseWrapper(con.responseCode, message)
        } catch (e: Exception) {
            RecorderLogger.e(TAG, "uploadFile error", e, true)
        } finally {
            fileInputStream?.close()
            dataOutputStream?.close()
            inputStream?.close()
        }
        return null
    }

    @Throws(IOException::class)
    override fun uploadCode(url: String?): com.oplus.log.uploader.ResponseWrapper? {
        try {
            val Url = URL(url)
            val con = Url.openConnection() as HttpURLConnection
            // 允许Input、Output，不使用Cache
            con.doInput = true
            con.doOutput = true
            con.useCaches = false
            // 设置以POST方式进行传送
            con.requestMethod = "POST"
            // 设置RequestProperty
            con.setRequestProperty("Connection", "Keep-Alive")
            con.setRequestProperty("Charset", "UTF-8")
            con.setRequestProperty("Accept", "application/json")
            con.connect()
            return com.oplus.log.uploader.ResponseWrapper(con.responseCode)
        } catch (e: Exception) {
            RecorderLogger.e(TAG, "uploadCode error", e, true)
        }
        return null
    }

    @Throws(IOException::class)
    override fun checkUpload(url: String?): com.usertrace.cdo.usertrace.domain.dto.UserTraceConfigDto? {
        var message = ""
        var inputStream: InputStream? = null
        try {
            val urlCon = URL(url)
            val connection = urlCon.openConnection() as HttpURLConnection
            connection.requestMethod = "GET"
            connection.connectTimeout = 5 * 1000
            connection.setRequestProperty("Accept", "application/json")
            connection.connect()
            RecorderLogger.i(
                TAG,
                "executeByGET_code:" + connection.responseCode + ",message:" + connection.responseMessage,
                true
            )
            inputStream = connection.inputStream
            val sb = StringBuilder()
            var line: String?
            val br = BufferedReader(InputStreamReader(inputStream))
            while (br.readLine().also { line = it } != null) {
                sb.append(line)
            }
            message = sb.toString()
            connection.disconnect()
        } catch (e: Exception) {
            if (com.oplus.log.Logger.isDebug()) {
                e.printStackTrace()
            }
        } finally {
            inputStream?.close()
        }
        return parseJson(message, com.usertrace.cdo.usertrace.domain.dto.UserTraceConfigDto::class.java)
    }


    fun <T> parseJson(jsonData: String, type: Class<T>): T? {
        try {
            return GsonUtil.fromJson(jsonData, type)
        } catch (e: Exception) {
            RecorderLogger.e(
                TAG,
                "parseJson2 --e:" + e.message,
                true
            )
        }
        return null
    }
}