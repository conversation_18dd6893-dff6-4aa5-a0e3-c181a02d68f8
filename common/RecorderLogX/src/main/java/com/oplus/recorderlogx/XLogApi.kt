package com.oplus.recorderlogx

import android.content.Context
import com.soundrecorder.modulerouter.xlog.RecordLogXConfig
import com.soundrecorder.modulerouter.xlog.RecorderLogInterface

object XLogApi : RecorderLogInterface {

    private var recordXLog: RecorderXLog = RecorderXLog()

    override fun initLog(context: Context) {
        recordXLog.initLog(context)
    }


    override fun flushLog(isSync: Boolean) {
        recordXLog.flushLog(isSync)
    }


    override fun processPushLog(context: Context?, cloudLogConfigMsg: RecordLogXConfig) {
        recordXLog.processPushLog(context, cloudLogConfigMsg)
    }


    override fun processManualReportLog() {
        recordXLog.processManualReportLog()
    }


    override fun processDBPrint(context: Context?) {
        recordXLog.processDBPrint(context)
    }


    override fun v(tag: String?, message: String?) {
        recordXLog.v(tag, message)
    }

    override fun d(tag: String?, message: String?) {
        recordXLog.d(tag, message)
    }

    override fun i(tag: String?, message: String?) {
        recordXLog.i(tag, message)
    }

    override fun w(tag: String?, message: String?) {
        recordXLog.w(tag, message)
    }

    override fun e(tag: String?, message: String?) {
        recordXLog.e(tag, message)
    }

    override fun e(tag: String?, message: String?, e: Throwable?) {
        recordXLog.e(tag, message, e)
    }
}