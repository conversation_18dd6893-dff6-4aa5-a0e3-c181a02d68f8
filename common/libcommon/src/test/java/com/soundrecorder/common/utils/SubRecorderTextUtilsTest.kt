package com.soundrecorder.common.utils

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class SubRecorderTextUtilsTest {

    private var mContext: Context? = null
    private val testIllegalNames = arrayOf("test\\.mp3",
            "test/.mp3",
            "test/.mp3",
            "test:.mp3",
            "test?.mp3",
            "？test.mp3",
            "<test.mp3",
            "test>.mp3",
            "test|.mp3",
            "test\".mp3")
    private val testEmojiNames = arrayOf(
            "test◎.mp3",
            "test 1◆.mp3",
            "test1▼.mp3",
            "test▲.mp3",
            "test1■.mp3",
            "test ←1.mp3",
            "test ↙.mp3"
    )
    private val testNormalNames = arrayOf(
            "test.mp3",
            "test 1.mp3",
            "test-1.mp3",
            "test_1.mp3",
            "test@1.mp3"
    )

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @After
    fun tearDown() {
        mContext = null
    }

    @Test
    fun should_containsIllegalCharFileName_when_file_name_contain_illegal_char_return_true() {
        //illegal char \/*:?？<>|"
        testIllegalNames.forEach {
            Assert.assertTrue(SubRecorderTextUtils.containsIllegalCharFileName(it))
        }
        testNormalNames.forEach {
            Assert.assertFalse(SubRecorderTextUtils.containsIllegalCharFileName(it))
        }
    }

    @Test
    fun should_containsEmoji_when_file_name_contain_emoji_return_true() {
        testEmojiNames.forEach {
            Assert.assertTrue(SubRecorderTextUtils.containsEmoji(it))
        }
        testNormalNames.forEach {
            Assert.assertFalse(SubRecorderTextUtils.containsEmoji(it))
        }
    }

}