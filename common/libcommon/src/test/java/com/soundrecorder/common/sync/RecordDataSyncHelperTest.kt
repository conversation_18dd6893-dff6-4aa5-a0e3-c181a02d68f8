package com.soundrecorder.common.sync

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowOS12FeatureUtil::class])
class RecordDataSyncHelperTest {
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @After
    fun release() {
        mContext = null
    }

    @Test
    fun when_checkLastEnterInOneHour() {
        val lastEnterInOneHour = RecordDataSyncHelper.chekLastRecordSyncOverOneHour()
        Assert.assertFalse(lastEnterInOneHour)
        RecordDataSyncHelper.updateMediaCompareTime(mContext!!)
        val lastEnterInOneHour1 = RecordDataSyncHelper.chekLastRecordSyncOverOneHour()
        Assert.assertFalse(lastEnterInOneHour1)
    }

    @Test
    fun when_updateMediaCompareTime() {
        RecordDataSyncHelper.updateMediaCompareTime(mContext!!)
    }
}