/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SearchAnimTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/12/27
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.sync.encryptbox

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.common.utils.RecordModeUtil
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyString
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowOS12FeatureUtil::class])
class SyncBeanProcessorTest {
    private val TARGET_PATH = "targetPath"
    private var mContext: Context? = null
    private var mockedRecorderDbUtil: MockedStatic<CloudSyncRecorderDbUtil>? = null
    private var mockedRecordModeUtil: MockedStatic<RecordModeUtil>? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mockedRecorderDbUtil = Mockito.mockStatic(CloudSyncRecorderDbUtil::class.java)
        mockedRecordModeUtil = Mockito.mockStatic(RecordModeUtil::class.java)
    }

    @After
    fun tearDown() {
        mockedRecorderDbUtil?.close()
        mockedRecordModeUtil?.close()
        mContext = null
    }

    @Test
    fun should_invoke_when_processIncomingSyncBean() {
        val processor = SyncBeanProcessor()
        val bean = SyncBean()
        val info = SyncBean.SyncFileInfo()
        info.displayName = ""
        info.relativePath = ""
        info.md5 = ""
        bean.fileInfoList = listOf(info)
        bean.syncType = EncryptBoxConstant.TYPE_ENCRYPTION
        processor.processIncomingSyncBean(bean)
        mockedRecorderDbUtil?.verify({
            CloudSyncRecorderDbUtil.processEncryptAudioFile(anyString(), anyString(), anyString())
        }, Mockito.times(1))
    }

    @Test
    fun should_invoke_when_doProcessInputEncrypt() {
        val processor = SyncBeanProcessor()
        val bean = SyncBean()
        val info = SyncBean.SyncFileInfo()
        info.displayName = ""
        info.relativePath = ""
        info.md5 = ""
        bean.fileInfoList = listOf(info)
        Whitebox.invokeMethod<Void>(processor, "doProcessInputEncrypt", bean)
        mockedRecorderDbUtil?.verify({
            CloudSyncRecorderDbUtil.processEncryptAudioFile(anyString(), anyString(), anyString())
        }, Mockito.times(1))
    }

    @Test
    fun should_invoke_when_doProcessInputDecrypt() {
        val processor = SyncBeanProcessor()
        val bean = SyncBean()
        val info = SyncBean.SyncFileInfo()
        info.displayName = ""
        info.relativePath = ""
        info.md5 = ""
        bean.fileInfoList = listOf(info)
        bean.targetPath = TARGET_PATH
        bean.syncType = EncryptBoxConstant.TYPE_DECRYPTION
        processor.processIncomingSyncBean(bean)
        mockedRecordModeUtil?.verify({
            RecordModeUtil.containRecordRelativePath(anyString())
        }, Mockito.times(1))
    }
}