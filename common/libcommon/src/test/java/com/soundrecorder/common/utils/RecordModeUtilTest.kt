/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  RecordModeUtilTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.utils

import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.text.TextUtils
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.oplus.os.OplusUsbEnvironment
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.utils.AddonAdapterCompatUtil
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.R
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.constant.OplusCompactConstant
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.shadows.*
import com.soundrecorder.common.utils.RecordModeUtil.cleanRelativePath
import com.soundrecorder.common.utils.RecordModeUtil.ensureFoldersExist
import com.soundrecorder.common.utils.RecordModeUtil.isFromCall
import com.soundrecorder.common.utils.RecordModeUtil.isFromMessage
import com.soundrecorder.common.utils.RecordModeUtil.isFromOther
import com.soundrecorder.common.utils.RecordModeUtil.isThreeRecordJumpToCall
import java.io.File
import org.junit.*
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.api.mockito.PowerMockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowStorageManager::class, ShadowRecorderLogger::class])
class RecordModeUtilTest {

    companion object {
        private const val STANDARD_RECORDINGS = "Standard Recordings"
        private const val INTERVIEW_RECORDINGS = "Interview Recordings"
        private const val MEETING_RECORDINGS = "Meeting Recordings"
        private const val CALL_RECORDINGS = "Call Recordings"

        private const val SET_PARAMETERS = "setParameters"
        private const val INT_ZERO = 0
        private const val INT_FOUR = 4
        private const val TEST = "test"
    }

    private var mContext: Context? = null
    private var mMockedBaseApplication: MockedStatic<BaseApplication>? = null
    private var mMockStaticTextUtils: MockedStatic<TextUtils>? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mMockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockedBaseApplication?.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(mContext)
        mMockStaticTextUtils = Mockito.mockStatic(TextUtils::class.java)
        mMockStaticTextUtils?.`when`<Any> { TextUtils.isEmpty(ArgumentMatchers.anyString()) }?.thenAnswer { invocation ->
                val a = invocation.getArgument<CharSequence>(0)
                a == null || a.isEmpty()
            }
    }

    @After
    fun tearDown() {
        if (mMockedBaseApplication != null) {
            mMockedBaseApplication?.close()
            mMockedBaseApplication = null
        }
        mContext = null
        mMockStaticTextUtils?.close()
        mMockStaticTextUtils = null
    }

    @Test
    fun should_returnStringPath_when_getRelativePathByRecordType_with_RecordType() {
        val path0: String = RecordModeUtil.getRelativePathByRecordType(0, false)
        Assert.assertTrue(path0.contains(STANDARD_RECORDINGS))
        val path1: String = RecordModeUtil.getRelativePathByRecordType(1, false)
        Assert.assertTrue(path1.contains(MEETING_RECORDINGS))
        val path2: String = RecordModeUtil.getRelativePathByRecordType(2, false)
        Assert.assertTrue(path2.contains(INTERVIEW_RECORDINGS))
        val path3: String = RecordModeUtil.getRelativePathByRecordType(3, false)
        Assert.assertTrue(path3.contains(CALL_RECORDINGS))
    }

    @Test
    @Config(shadows = [ShadowBaseUtilBelowQ::class, ShadowRecordModeUtils::class])
    fun should_correct_when_isSupportMultiRecordMode_different_input() {
        val mockContext = Mockito.mock(
            Context::class.java)
        val mockAudioManager = PowerMockito.mock(AudioManager::class.java)
        PowerMockito.`when`(mockContext.getSystemService(ArgumentMatchers.anyString())).thenReturn(mockAudioManager, null)
        PowerMockito.`when`(mockAudioManager.getParameters(ArgumentMatchers.anyString())).thenReturn(
                RecordModeUtil.LOCAL_RECORDMODE_SWITCH_ENABLE)
        val supportMultiMode: Boolean? = null
        Whitebox.setInternalState(RecordModeUtil.javaClass, "supportMultiMode", supportMultiMode)
        Assert.assertTrue(RecordModeUtil.isSupportMultiRecordMode(mockContext))
        Assert.assertTrue(RecordModeUtil.isSupportMultiRecordMode(mockContext))
    }

    @Test
    @Throws(Exception::class)
    fun should_returnTrue_when_setRecordMode_different_inputs() {
        val mockContext = Mockito.mock(
            Context::class.java)
        val mockAudioManager = Mockito.mock(AudioManager::class.java)
        PowerMockito.`when`(mockContext.getSystemService(ArgumentMatchers.anyString())).thenReturn(mockAudioManager)
        PowerMockito.`when`(mockAudioManager.getParameters(ArgumentMatchers.anyString())).thenReturn(RecordModeUtil.LOCAL_RECORDMODE_SWITCH_ENABLE)
        PowerMockito.doNothing().`when`(mockAudioManager, SET_PARAMETERS, ArgumentMatchers.anyString())
        Assert.assertTrue(RecordModeUtil.setRecordMode(mockContext, INT_ZERO))
        Assert.assertFalse(RecordModeUtil.setRecordMode(mockContext, INT_FOUR))
        PowerMockito.`when`(mockAudioManager.getParameters(ArgumentMatchers.anyString())).thenReturn(TEST)
        Assert.assertFalse(RecordModeUtil.setRecordMode(mockContext, INT_ZERO))
    }

    @Test
    @Config(shadows = [ShadowStorageManager::class])
    fun should_makeFolder_when_ensureFoldersExist() {
        var storagePrefix = StorageManager.getInstance(mContext).storagePrefix + File.separatorChar
        if (BaseUtil.isAndroidQOrLater) {
            storagePrefix += Environment.DIRECTORY_MUSIC + File.separator + Constants.RECORDINGS
        }
        val prefixFolder = File(storagePrefix)
        if (!prefixFolder.exists()) {
            prefixFolder.mkdir()
        }
        mContext?.let { ensureFoldersExist(it) }
        val recordingsStandard = storagePrefix + File.separatorChar + mContext?.getString(R.string.normal_store_dir)
        val recordingsCall = storagePrefix + File.separatorChar + mContext?.getString(R.string.calling_store_dir)
        val recordingsInterview = storagePrefix + File.separatorChar + mContext?.getString(R.string.interview_store_dir)
        val standardFolder = File(recordingsStandard)
        val callFolder = File(recordingsCall)
        val interviewFolder = File(recordingsInterview)
        Assert.assertFalse(standardFolder.exists())
        Assert.assertFalse(callFolder.exists())
        Assert.assertFalse(interviewFolder.exists())
        FileUtils.deleteDirOrFile(mContext, prefixFolder)
        prefixFolder.delete()
    }

    @Test
    @Config(shadows = [ShadowRecordModeUtils::class, ShadowStorageManager::class])
    fun should_returnTrue_when_checkFoldersNotExist_with_meetingFolderNotExists() {
        val storagePrefix = StorageManager.getInstance(mContext).storagePrefix
        val prefixFolder = File(storagePrefix)
        prefixFolder.mkdirs()
        mContext?.let { ensureFoldersExist(it) } // testPath/Music/Recordings
        val b: Boolean = mContext?.let { RecordModeUtil.checkFoldersNotExist(it) } ?: true
        Assert.assertTrue(b)
        FileUtils.deleteDirOrFile(mContext, prefixFolder)
        prefixFolder.delete()
    }

    @Test
    @Ignore
    fun should_size_when_checkFoldersNotExist() {
        val mockedStatic = Mockito.mockStatic(
            OplusUsbEnvironment::class.java)
        mockedStatic.`when`<Any> {
            OplusUsbEnvironment.getExternalPath(
                ArgumentMatchers.any<Any>() as Context)
        }.thenReturn("test")
        Assert.assertTrue(mContext?.let { RecordModeUtil.checkFoldersNotExist(it) } ?: false)
        mockedStatic.close()
    }

    @Test
    fun should_equals_when_cleanRelativePath() {
        val origin = "Music/REcordings"
        var result = origin.cleanRelativePath()
        Assert.assertEquals(RecordModeConstant.RELATIVE_PATH_RECORDINGS_NO_SLASH, result)

        var cleanRelativePath = "$origin/Standard Recordings"
        result = cleanRelativePath.cleanRelativePath()
        Assert.assertEquals(RecordModeConstant.RELATIVE_PATH_STANDARD_NO_SLASH, result)

        cleanRelativePath = "$origin/Meeting Recordings"
        result = cleanRelativePath.cleanRelativePath()
        Assert.assertEquals(RecordModeConstant.RELATIVE_PATH_MEETING_NO_SLASH, result)

        cleanRelativePath = "$origin/Interview Recordings"
        result = cleanRelativePath.cleanRelativePath()
        Assert.assertEquals(RecordModeConstant.RELATIVE_PATH_INTERVIEW_NO_SLASH, result)

        cleanRelativePath = "$origin/Call Recordings"
        result = cleanRelativePath.cleanRelativePath()
        Assert.assertEquals(RecordModeConstant.RELATIVE_PATH_CALL_NO_SLASH, result)

        cleanRelativePath = "$origin/Call Recordings2"
        result = cleanRelativePath.cleanRelativePath()
        Assert.assertNotEquals(RecordModeConstant.RELATIVE_PATH_CALL_NO_SLASH, result)
    }

    @Test
    fun should_equals_when_containRecordRelativePath() {
        var data = ""
        var result = RecordModeUtil.containRecordRelativePath(data)
        Assert.assertFalse(result)

        data = "hahaha${Environment.DIRECTORY_MUSIC}${File.separator}${Constants.RECORDINGS}${File.separator}CALL"
        result = RecordModeUtil.containRecordRelativePath(data)
        Assert.assertFalse(result)

        data = "hahaha${Environment.DIRECTORY_MUSIC}${File.separator}${Constants.RECORDINGS}${File.separator}STANDARD Recordings"
        result = RecordModeUtil.containRecordRelativePath(data)
        Assert.assertTrue(result)

        data = "hahaha${Environment.DIRECTORY_MUSIC}${File.separator}${Constants.RECORDINGS}${File.separator}INTERVIEW Recordings"
        result = RecordModeUtil.containRecordRelativePath(data)
        Assert.assertTrue(result)

        data = "hahaha${Environment.DIRECTORY_MUSIC}${File.separator}${Constants.RECORDINGS}${File.separator}MEETING Recordings"
        result = RecordModeUtil.containRecordRelativePath(data)
        Assert.assertTrue(result)

        data = "hahaha${Environment.DIRECTORY_MUSIC}${File.separator}${Constants.RECORDINGS}${File.separator}CALL Recordings"
        result = RecordModeUtil.containRecordRelativePath(data)
        Assert.assertTrue(result)
    }

    @Test
    fun should_when_getRecordTypeForMediaRecord() {
        var recordType = RecordModeUtil.getRecordTypeForMediaRecord(null)
        Assert.assertEquals(0, recordType)

        recordType = RecordModeUtil.getRecordTypeForMediaRecord(Record().apply { relativePath = "" })
        Assert.assertEquals(0, recordType)

        recordType = RecordModeUtil.getRecordTypeForMediaRecord(Record().apply {
            relativePath = "Recordings${File.separatorChar}Standard Recordings"
        })
        Assert.assertEquals(0, recordType)

        recordType = RecordModeUtil.getRecordTypeForMediaRecord(Record().apply {
            relativePath = "Recordings${File.separatorChar}Meeting Recordings"
        })
        Assert.assertEquals(1, recordType)

        recordType = RecordModeUtil.getRecordTypeForMediaRecord(Record().apply {
            relativePath = "Recordings${File.separatorChar}Interview Recordings"
        })
        Assert.assertEquals(2, recordType)


        recordType = RecordModeUtil.getRecordTypeForMediaRecord(Record().apply {
            relativePath = "Recordings${File.separatorChar}Call Recordings"
        })
        Assert.assertEquals(3, recordType)
    }

    @Test
    fun should_when_isNeedHideRecord() {
        val result = RecordModeUtil.isNeedHideRecord(null)
        Assert.assertFalse(result)
    }

    @Test
    fun should_when_getModeValue() {
        RecordModeUtil.setModeValue(1)
        Assert.assertEquals(1, RecordModeUtil.getModeValue())

        RecordModeUtil.setModeValue(0)
        Assert.assertEquals(0, RecordModeUtil.getModeValue())
    }

    @Test
    fun should_correct_when_virtualcommDeviceType() {
        val context = mContext ?: return
        val mockStaticAddonAdapterCompatUtil = Mockito.mockStatic(AddonAdapterCompatUtil::class.java)
        mockStaticAddonAdapterCompatUtil.`when`<Int> { AddonAdapterCompatUtil.getVirtualcommDeviceType(context) }
            .thenReturn(0, RecordModeUtil.VIRTUALCOMM_DEVICE_CONSUMER)
        Assert.assertFalse(RecordModeUtil.virtualcommDeviceType(context))
        Assert.assertTrue(RecordModeUtil.virtualcommDeviceType(context))

        mockStaticAddonAdapterCompatUtil.close()
    }

    @Test
    fun should_correct_when_isFromCall() {
        val intent = Intent()
        Assert.assertFalse(intent.isFromCall())

        intent.action = OplusCompactConstant.START_BROWSE_ACTION_BEFOR
        Assert.assertTrue(intent.isFromCall())

        intent.action = OplusCompactConstant.START_BROWSE_ACTION_AFTER
        Assert.assertTrue(intent.isFromCall())

        val emptyIntent: Intent? = null
        Assert.assertFalse(emptyIntent.isFromCall())
    }

    @Test
    fun should_correct_when_isFromThreadRecord() {
        val intent = Intent()
        Assert.assertFalse(intent.isThreeRecordJumpToCall())

        intent.action = OplusCompactConstant.START_BROWSE_ACTION_THREAD_RECORD
        Assert.assertTrue(intent.isThreeRecordJumpToCall())

        val emptyIntent: Intent? = null
        Assert.assertFalse(emptyIntent.isThreeRecordJumpToCall())
    }

    @Test
    fun should_correct_when_isFromMessage() {
        val intent = Intent()
        Assert.assertFalse(intent.isFromMessage())

        intent.action = Intent.ACTION_GET_CONTENT
        Assert.assertTrue(intent.isFromMessage())

        intent.action = Intent.ACTION_PICK
        Assert.assertTrue(intent.isFromMessage())

        intent.action = MediaStore.Audio.Media.RECORD_SOUND_ACTION
        Assert.assertTrue(intent.isFromMessage())

        val emptyIntent: Intent? = null
        Assert.assertFalse(emptyIntent.isFromMessage())
    }

    @Test
    fun should_correct_when_isFromOther() {
        val intent = Intent()
        Assert.assertFalse(intent.isFromOther())

        intent.action = Intent.ACTION_GET_CONTENT
        Assert.assertTrue(intent.isFromOther())

        intent.action = OplusCompactConstant.START_BROWSE_ACTION_BEFOR
        Assert.assertTrue(intent.isFromOther())

        val emptyIntent: Intent? = null
        Assert.assertFalse(emptyIntent.isFromOther())
    }
}