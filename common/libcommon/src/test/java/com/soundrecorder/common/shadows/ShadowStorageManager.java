/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ShadowStorageManager
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/07
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.shadows;

import com.soundrecorder.base.StorageManager;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(StorageManager.class)
public class ShadowStorageManager {
    private static final String TEST_PATH = "testPath";

    @Implementation
    public String getStoragePrefix() {
        return TEST_PATH;
    }
}