/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CommonApiTest
 * Description:
 * Version: 1.0
 * Date: 2023/9/20
 * Author: W9020254
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9020254 2023/9/20 1.0 create
 */

package com.soundrecorder.common.api

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.HoverStaticsUtil
import com.soundrecorder.common.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CommonApiTest {

    private var mockedStatic: MockedStatic<HoverStaticsUtil>? = null

    @Before
    @Throws(Exception::class)
    fun setUp() {
        mockedStatic = Mockito.mockStatic(HoverStaticsUtil::class.java)
    }

    @After
    @Throws(Exception::class)
    fun tearDown() {
        mockedStatic?.close()
        mockedStatic = null
    }

    @Test
    fun check_addHoverNumEvent() {
        CommonApi.hoverBuryingPoint("Recorder")
        mockedStatic?.verify({
            HoverStaticsUtil.addHoverNumEvent("Recorder")
        }, Mockito.times(1))
    }

    @Test
    fun check_addRecordDurationMessageEvent() {
        CommonApi.useRecordDurationMessage(1)
        mockedStatic?.verify({
            BuryingPoint.addRecordDurationMessage(1)
        }, Mockito.times(1))
    }

    @Test
    fun check_addRecordEntryLaunchEvent() {
        CommonApi.useRecordEntryLaunch("test", 1)
        mockedStatic?.verify({
            BuryingPoint.addRecordEntryLaunch("test", 1)
        }, Mockito.times(1))
    }
}