/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.fileoperator.delete

import android.app.Activity
import android.os.Build
import androidx.appcompat.app.AlertDialog
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.shadows.ShadowCOUIMaxHeightScrollView
import com.soundrecorder.common.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.common.shadows.ShadowFeatureOption

import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class,
    ShadowCOUIMaxHeightScrollView::class, ShadowCOUIVersionUtil::class])
class DeleteFileDialogTest {

    private var mActivity: Activity? = null
    private var mDeleteFileDialog: DeleteFileDialog? = null

    @Before
    fun setUp() {
        mActivity = Robolectric.buildActivity(Activity::class.java).get()
        mDeleteFileDialog =
            DeleteFileDialog(mActivity!!, "delete file title", "delete file message", "delete file content")
    }

    @After
    fun tearDown() {
        mActivity = null
        mDeleteFileDialog = null
    }

    @Test
    fun should_equals_when_showDeleteDialog() {
        var deleteDialog =
            Whitebox.getInternalState<AlertDialog>(mDeleteFileDialog, "mDeleteDialog")
        Assert.assertNull(deleteDialog)
        val deleteRecords = mutableListOf<Record>(
            Record()
        )
        mDeleteFileDialog?.showDeleteDialog(deleteRecords)
        deleteDialog = Whitebox.getInternalState<AlertDialog>(mDeleteFileDialog, "mDeleteDialog")
        Assert.assertEquals(true, deleteDialog.isShowing)
    }

    @Test
    fun should_not_null_when_deleteWithPermission() {
        var deleteFileDialogUtil = Whitebox.getInternalState<DeleteFileDialogUtil>(
            mDeleteFileDialog,
            "mDeleteFileDialogUtil"
        )
        Assert.assertNull(deleteFileDialogUtil)
        val selectedRecordList = ArrayList<Record>()
        selectedRecordList.add(Record().also { it.fileId = "1" })
        mDeleteFileDialog?.deleteWithPermission(mActivity!!, selectedRecordList, true, false)
        deleteFileDialogUtil = Whitebox.getInternalState<DeleteFileDialogUtil>(
            mDeleteFileDialog,
            "mDeleteFileDialogUtil"
        )
        Assert.assertNotNull(deleteFileDialogUtil)
    }

    @Test
    fun should_not_null_when_deleteRecord() {
        Whitebox.invokeMethod<Void>(mDeleteFileDialog, "deleteRecord", ArrayList<Record>(), true, false)

        val selectedRecordList = ArrayList<Record>()
        selectedRecordList.add(Record().also { it.fileId = "1" })
        Whitebox.invokeMethod<Void>(mDeleteFileDialog, "deleteRecord", selectedRecordList, true, false)
        val deleteFileDialogUtil = Whitebox.getInternalState<DeleteFileDialogUtil>(
            mDeleteFileDialog,
            "mDeleteFileDialogUtil"
        )
        Assert.assertNotNull(deleteFileDialogUtil)
    }

    @Test
    fun should_equals_when_release() {
        mDeleteFileDialog?.release()
        val deleteDialog =
            Whitebox.getInternalState<AlertDialog>(mDeleteFileDialog, "mDeleteDialog")
        Assert.assertNull(deleteDialog)
    }

    @Test
    fun should_equals_when_dismiss() {
        mDeleteFileDialog?.showDeleteDialog(Record())
        mDeleteFileDialog?.dismiss()
        val deleteDialog =
            Whitebox.getInternalState<AlertDialog>(mDeleteFileDialog, "mDeleteDialog")
        Assert.assertNotEquals(true, deleteDialog.isShowing)
    }

    @Test
    fun should_equals_when_isShowing() {
        val deleteRecords = mutableListOf<Record>(
            Record()
        )
        mDeleteFileDialog?.showDeleteDialog(deleteRecords)
        Assert.assertEquals(true, mDeleteFileDialog?.isShowing())
    }

    @Test
    fun should_equals_when_getOperating() {
        val selectedRecordList = ArrayList<Record>()
        selectedRecordList.add(Record().also { it.fileId = "1" })
        mDeleteFileDialog?.deleteWithPermission(mActivity!!, selectedRecordList, true, false)
        mDeleteFileDialog?.resetOperating()
        Assert.assertEquals(false, mDeleteFileDialog?.getOperating())
    }
}