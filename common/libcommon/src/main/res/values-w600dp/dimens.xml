<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="common_mark_list_margin_horizontal_small_window">24dp</dimen>
    <item name="screen_width_percent" type = "dimen" format = "float">0.75</item>
    <dimen name="circle_record_button_margin_bottom">40dp</dimen>
    <dimen name="circle_playback_button_margin_bottom">20dp</dimen>
    <dimen name="circle_browsefile_button_margin_bottom">20dp</dimen>
    <dimen name="circle_record_button_diam">64dp</dimen>
    <dimen name="circle_record_button_shadow">93dp</dimen>
    <dimen name="circle_record_button_radius">32dp</dimen>
    <!--录制、裁切页面，标记、图片标记、提取、删除按钮大小-->
    <dimen name="circle_record_second_button_width">56dp</dimen>
    <!--录制、裁切页面，标记、图片标记、提取、删除按钮中心的小图标大小-->
    <dimen name="circle_record_second_button_icon_width">26dp</dimen>
    <dimen name="botton_text_size">12dp</dimen>
    <dimen name="real_time_view_margin">48dp</dimen>
    <dimen name="real_time_item_data_padding">24dp</dimen>
    <dimen name="speaker_container_marginStart">0dp</dimen>

    <dimen name="play_convert_margin_top">118dp</dimen>
</resources>