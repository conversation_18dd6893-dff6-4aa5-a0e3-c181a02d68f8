<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:paddingBottom="@dimen/dp10"
    android:paddingHorizontal="@dimen/dp16"
    android:background="@drawable/bg_edit_mark_input_view"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.coui.appcompat.edittext.COUIEditText
        android:id="@+id/coui_edit"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp0"
        android:background="@color/coui_color_container4"
        android:maxLength="50"
        android:gravity="top"
        android:minHeight="@dimen/dp22"
        app:cornerRadius="@dimen/dp12"
        android:textSize="@dimen/sp16"
        android:textFontWeight="400"
        android:textColor="@color/coui_color_label_primary"
        app:couiBackgroundMode="noLine"
        android:maxLines="3"
        android:hint="@string/edit_mark_dialog_hint_text"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/image_layout"
        app:quickDelete="false" />

    <com.coui.appcompat.cardview.COUICardView
        android:id="@+id/image_layout"
        app:couiCardRoundCornerRadius="@dimen/dp8"
        app:cardCornerRadius="@dimen/dp8"
        app:couiCardCornerWeight="?attr/couiRoundCornerLWeight"
        app:cardElevation="0dp"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="@dimen/dp48"
        android:layout_height="@dimen/dp48">
        <ImageView
            android:id="@+id/image"
            android:scaleType="centerCrop"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
    </com.coui.appcompat.cardview.COUICardView>

    <ImageView
        android:id="@+id/btn_delete"
        android:visibility="invisible"
        android:layout_width="@dimen/dp16"
        android:layout_height="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp2_5"
        android:layout_marginTop="@dimen/dp2_5"
        android:src="@drawable/icon_delete_image"
        app:layout_constraintEnd_toEndOf="@id/image_layout"
        app:layout_constraintTop_toTopOf="@id/image_layout" />

    <TextView
        android:id="@+id/text_input_count"
        android:background="@null"
        android:textFontWeight="400"
        android:textSize="@dimen/sp12"
        android:textColor="@color/coui_color_label_tertiary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

</androidx.constraintlayout.widget.ConstraintLayout>