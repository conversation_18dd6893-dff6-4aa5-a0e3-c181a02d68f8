<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="@dimen/dp16"
    android:paddingBottom="@dimen/dp8"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <com.soundrecorder.common.widget.EditTextWithImage
        android:id="@+id/edit_text"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp145"/>

    <ImageView
        android:id="@+id/btn_select_photo"
        android:src="@drawable/icon_select_photo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/btn_complete"
        app:layout_constraintBottom_toBottomOf="@id/btn_complete"
        android:layout_width="@dimen/dp24"
        android:layout_height="@dimen/dp24"/>

    <ImageView
        android:id="@+id/btn_take_photo"
        android:src="@drawable/icon_take_photo"
        android:layout_marginStart="@dimen/dp12"
        app:layout_constraintStart_toEndOf="@id/btn_select_photo"
        app:layout_constraintTop_toTopOf="@id/btn_complete"
        app:layout_constraintBottom_toBottomOf="@id/btn_complete"
        android:layout_width="@dimen/dp24"
        android:layout_height="@dimen/dp24"/>

    <com.coui.appcompat.button.COUIButton
        android:id="@+id/btn_complete"
        android:text="@string/record_play_setting_complete"
        android:padding="@dimen/dp0"
        android:textColor="@color/coui_color_white"
        style="@style/Widget.COUI.Button.Small"
        android:layout_marginTop="@dimen/dp14"
        app:drawableRadius="@dimen/dp14"
        android:layout_marginEnd="@dimen/dp0"
        app:drawableColor="@color/coui_color_container_theme_red"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/edit_text"
        android:layout_width="@dimen/dp52"
        android:layout_height="@dimen/dp28"/>

</androidx.constraintlayout.widget.ConstraintLayout>