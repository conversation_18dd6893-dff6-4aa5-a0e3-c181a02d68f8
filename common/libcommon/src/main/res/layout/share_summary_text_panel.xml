<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/dp16"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp32">

    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/tv_play_setting_play_speed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="sans-serif-medium"
        android:text="格式"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textSize="@dimen/sp12"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="MissingConstraints" />
</androidx.constraintlayout.widget.ConstraintLayout>
