<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical" >

    <TextView
        android:id="@+id/text_permission_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="@style/couiTextAppearanceHeadline6"
        android:textColor="@color/coui_color_primary_neutral"
        android:layout_marginTop="@dimen/dp10"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"/>

    <TextView
        android:id="@+id/text_permission_summary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/coui_color_secondary_neutral"
        android:textSize="@dimen/sp14"
        android:layout_marginBottom="@dimen/dp10"
        android:layout_marginTop="@dimen/dp2"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"/>

    <ImageView
        android:id="@+id/item_divider"
        android:layout_width="match_parent"
        android:layout_height="@dimen/coui_list_divider_height"
        android:background="@color/coui_color_divider"
        android:forceDarkAllowed="false"
        android:visibility="gone"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"/>
</LinearLayout>