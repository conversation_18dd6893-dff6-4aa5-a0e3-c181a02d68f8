<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/dp24"
        android:layout_marginEnd="@dimen/dp24">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <com.coui.appcompat.edittext.COUIInputView
                android:id="@+id/coui_edit"
                android:drawablePadding="@dimen/coui_input_button_layout_margin_start"
                android:paddingBottom="@dimen/dp12"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:cursorVisible="true"
                android:inputType="text"
                android:textAlignment="viewStart"
                app:couiEnableInputCount="true"
                app:couiInputMaxCount="50"
                app:quickDelete="true" />

            <TextView
                android:id="@+id/dlg_rename_note"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp6"
                android:gravity="start|center_vertical"
                android:textColor="@color/couiRedTintControlNormal"
                android:visibility="gone"
                tools:text="@string/error_none_filename"
                tools:visibility="visible" />

            <com.coui.appcompat.checkbox.COUICheckBox
                android:id="@+id/cb_summary"
                android:layout_marginTop="@dimen/dp6"
                android:layout_marginBottom="@dimen/dp6"
                android:paddingStart="@dimen/dp6"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lines="1"
                android:ellipsize="end"
                style="@style/couiTextAppearanceDescription"
                android:textColor="?attr/couiColorSecondNeutral"
                android:checkMark="?android:attr/listChoiceIndicatorSingle"
                android:visibility="gone"
                android:text="@string/generate_ai_summary_simultaneously"/>
        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>