<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.scrollview.COUIScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fadingEdgeLength="@dimen/coui_full_page_statement_scroll_fade_length"
    android:forceDarkAllowed="false"
    android:requiresFadingEdge="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <FrameLayout
            android:paddingHorizontal="24dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.oplus.anim.EffectiveAnimationView
                android:id="@+id/img_enhance_guide"
                android:layout_width="match_parent"
                android:layout_height="198dp"
                app:anim_autoPlay="true"
                app:anim_cacheComposition="false"
                app:anim_loop="true"
                app:anim_rawRes="@raw/record_ai_title_guide" />

        </FrameLayout>

        <com.coui.appcompat.dialog.widget.COUIDialogTitle
            android:id="@+id/tv_enhance_guide_status"
            style="@style/COUIAlertDialogTitleStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/coui_alert_dialog_title_margin_top"
            android:text="@string/ai_recording_assistant" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_enhance_guide_describe"
            style="@style/COUIAlertDialogMessageStyle"
            android:layout_marginTop="@dimen/dp12"
            android:gravity="center_horizontal"
            android:text="@string/ai_recording_assistant_guide_description"
            android:textColor="?attr/couiColorSecondNeutral" />

    </LinearLayout>
</com.coui.appcompat.scrollview.COUIScrollView>