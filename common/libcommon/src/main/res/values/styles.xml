<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="TitleStyle">
        <item name="android:textAppearance">@style/couiTextAppearanceHeadline1</item>
        <item name="android:textSize">@dimen/dp32</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="SubtitleStyle">
        <item name="android:textAppearance">@style/couiTextAppearanceBody</item>
        <item name="android:textSize">@dimen/dp14</item>
        <item name="android:textColor">@color/coui_color_secondary_neutral</item>
    </style>

    <style name="customSearchViewAnimate" parent="@style/Widget.COUI.COUISearchViewAnimate">
        <item name="functionalButtonTextColor">?attr/couiColorPrimary</item>
    </style>

</resources>