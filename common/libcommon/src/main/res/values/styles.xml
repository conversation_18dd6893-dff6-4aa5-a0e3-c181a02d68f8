<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="TitleStyle">
        <item name="android:textAppearance">@style/couiTextAppearanceHeadline1</item>
        <item name="android:textSize">@dimen/dp32</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="SubtitleStyle">
        <item name="android:textAppearance">@style/couiTextAppearanceBody</item>
        <item name="android:textSize">@dimen/dp14</item>
        <item name="android:textColor">@color/coui_color_secondary_neutral</item>
    </style>

    <style name="customSearchViewAnimate" parent="@style/Widget.COUI.COUISearchViewAnimate">
        <item name="functionalButtonTextColor">?attr/couiColorPrimary</item>
    </style>

    <declare-styleable name="ShadowViewDraw">
        <attr name="shadowBlurRadius" format="dimension" />
        <attr name="shadowOffsetX" format="dimension" />
        <attr name="shadowOffsetY" format="dimension" />
        <attr name="shadowColor" format="color" />
        <attr name="spread" format="float" />
        <attr name="drawType" format="integer" />
    </declare-styleable>
    <style name="CustomAlertDialogMessageScrollViewStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fadingEdgeLength">@dimen/coui_full_page_statement_scroll_fade_length</item>
        <item name="android:overScrollMode">ifContentScrolls</item>
        <item name="android:requiresFadingEdge">vertical</item>
        <item name="android:scrollbarStyle">insideOverlay</item>
        <item name="android:scrollbarThumbVertical">@drawable/coui_scrollbar_handle_vertical</item>
        <item name="android:scrollbars">none</item>
        <item name="android:forceDarkAllowed">false</item>
    </style>

    <!--  公共控件弹窗推荐添加，详见bug：ID:7636727 弹窗闪现 -->
    <style name="Animation.COUI.Dialog.NoEnterAnimation">
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@anim/coui_bottom_dialog_exit</item>
    </style>

    <style name="CustomAlertDialogMessageStyle" parent="@style/COUIAlertDialogMessageStyle">
        <item name="android:textColor">?attr/couiColorLabelSecondary</item>
        <item name="android:textColorLink">?attr/couiColorContainerTheme</item>
        <item name="android:fontFamily">sans-serif-regular</item>
    </style>
</resources>