/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : EditTextWithImage.kt
 ** Description : EditTextWithImage.kt
 ** Version     : 1.0
 ** Date        : 2025/07/02
 ** Author      : renjiahao
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  renjiahao       2025/07/02      1.0      create
 ***********************************************************************/
package com.soundrecorder.common.widget

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.coui.appcompat.cardview.COUICardView
import com.coui.appcompat.edittext.COUIEditText
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.R
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.imageload.ImageLoaderUtils.into
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerInterface
import com.soundrecorder.modulerouter.utils.Injector

class EditTextWithImage @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr, defStyleRes), TextWatcher {

    private var couiEditText: COUIEditText? = null
    private var image: ImageView? = null
    private var imageDelete: ImageView? = null
    private var imageLayout: COUICardView? = null
    private var textInputCount: TextView? = null
    private var selectedImagePath: String = ""
    private var onContentChangeListener: ((String, String) -> Unit)? = null
    private var lastTextLength = 0

    private val photoViewerApi by lazy {
        Injector.injectFactory<PhotoViewerInterface>()
    }

    init {
        val view = LayoutInflater.from(context).inflate(R.layout.layout_edit_with_image, this, false)
        addView(view)
        initView()
    }

    private fun initView() {
        couiEditText = findViewById(R.id.coui_edit)
        image = findViewById(R.id.image)
        imageDelete = findViewById(R.id.btn_delete)
        textInputCount = findViewById(R.id.text_input_count)
        imageLayout = findViewById(R.id.image_layout)
        couiEditText?.addTextChangedListener(this)
        imageDelete?.setOnClickListener {
            removeImage()
        }
        image?.setOnClickListener {
            val data = MarkDataBean(timeInMills = System.currentTimeMillis())
            data.pictureFilePath = selectedImagePath
            photoViewerApi?.startWithBigImage(context, listOf(data))
        }
    }

    fun addContentChangeListener(listener: (String, String) -> Unit) {
        onContentChangeListener = listener
    }

    fun removeContentChangeListener() {
        onContentChangeListener = null
    }

    private fun removeImage() {
        selectedImagePath = ""
        image?.setImageDrawable(null)
        imageLayout?.visibility = INVISIBLE
        imageDelete?.visibility = INVISIBLE
        onContentChangeListener?.invoke(couiEditText?.text?.toString() ?: "", selectedImagePath)
    }

    fun addImage(path: String?) {
        if (path.isNullOrEmpty()) return
        if (selectedImagePath.isNotEmpty()) {
            ToastManager.showShortToast(context, R.string.toast_edit_mark_dialog_limit)
            return
        }
        selectedImagePath = path
        val data = ImageLoadData(
            FileUtils.getAppFile(path, false),
            ViewUtils.dp2px(NumberConstant.NUM_F48_0, true).toInt(),
            ViewUtils.dp2px(NumberConstant.NUM_F48_0, true).toInt()
        )
        image?.into(data)
        imageLayout?.visibility = VISIBLE
        imageDelete?.visibility = VISIBLE
        onContentChangeListener?.invoke(couiEditText?.text?.toString() ?: "", selectedImagePath)
    }

    fun getEditText(): COUIEditText? {
        return couiEditText
    }

    fun getImagePath(): String {
        return selectedImagePath
    }

    override fun afterTextChanged(s: Editable?) {
        val length = s?.length ?: 0
        textInputCount?.text = "$length/50"
        if (length == NumberConstant.NUM_50) {
            textInputCount?.setTextColor(context.getColor(com.support.appcompat.R.color.coui_color_label_theme_red))
        } else if (lastTextLength == NumberConstant.NUM_50) {
            textInputCount?.setTextColor(context.getColor(com.support.appcompat.R.color.coui_color_label_tertiary))
        }
        if (lastTextLength == 0 || length == 0) {
            onContentChangeListener?.invoke(couiEditText?.text?.toString() ?: "", selectedImagePath)
        }
        lastTextLength = length
    }

    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
    }

    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
    }
}