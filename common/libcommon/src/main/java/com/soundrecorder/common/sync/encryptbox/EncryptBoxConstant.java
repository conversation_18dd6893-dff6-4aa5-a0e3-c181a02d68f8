package com.soundrecorder.common.sync.encryptbox;

public class EncryptBoxConstant {

    public static final boolean ENCRYBOX_FEATURE_ON = true;

    public static final int TYPE_ENCRYPTION = 1000;
    public static final int TYPE_DECRYPTION = 1001;
    public static final int TYPE_DELETE = 1002;
    public static final int TYPE_DATA_LARGE = 1003;

    public static final String ENCRYPTBOX_ACTION = "com.coloros.encryption.action.AUDIO_DATA_CHANGED";
    public static final String ENCRYPTBOX_ACTION_OPLUS = "com.oplus.encryption.action.AUDIO_DATA_CHANGED";
    public static final String ENCRYPTBOX_ACTION_INTENT_EXTRA_KEY = "ENCRYPTION_SYNC_DATA";

    //public static final String ENCRYPT_BOX_PROVIDER_URI = "content://com.coloros.encyptionserver/audio_data";
    public static final String ENCRYPT_BOX_PROVIDER_URI = "content://com.coloros.encyptionserver/file";
    public static final String ENCRYPT_BOX_PROVIDER_OPLUS_URI = "content://com.oplus.encyptionserver/file";

    public static final String ENCRYPTBOX_COLUMN_NAME_ID = "_id";
    public static final String ENCRYPTBOX_COLUMN_NAME_DISPLAYNAME = "_display_name";
    public static final String ENCRYPTBOX_COLUMN_NAME_RELATIEPATH = "relative_path";
    public static final String ENCRYPTBOX_COLUMN_NAME_MD5 = "fileMd5";
    public static final String ENCRYPTBOX_COLUMN_NAME_SIZE = "orignal_size";

    public static final String[] ENCRYPTBOX_QUERY_PROJECTION = new String[]{
            ENCRYPTBOX_COLUMN_NAME_ID,
            ENCRYPTBOX_COLUMN_NAME_DISPLAYNAME,
            ENCRYPTBOX_COLUMN_NAME_RELATIEPATH,
            ENCRYPTBOX_COLUMN_NAME_MD5,
            ENCRYPTBOX_COLUMN_NAME_SIZE
    };


}
