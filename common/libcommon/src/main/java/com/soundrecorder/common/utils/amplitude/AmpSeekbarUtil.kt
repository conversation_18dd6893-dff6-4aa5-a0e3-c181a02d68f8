/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AmpWaveUtil.kt
 * Description: AmpWaveUtil.kt
 * Version: 1.0
 * Date: 2025/7/26
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>             2025/7/26      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.common.utils.amplitude

object AmpSeekbarUtil {

    private const val TAG = "AmpSeekbarUtil"
    private const val FLOAT_TWO = 2f
    private const val FLOAT_THREE = 3f
    private const val FLOAT_FIVE = 5f
    const val PERCENT_80 = 0.8f

    /**
     * 设x为音波柱宽度 y为音波柱之间间隔宽度，音波柱宽度比音波柱之间间隔宽度为 6:4，allW为进度条可绘制总宽，设波形段数为a
     * 则
     * allW = a*x + (a-1)*y
     * x/y = 6/4 => x/y = 3/2 => x = 3*y / 2
     *
     * 将x = 3*y/2 代入第一个式子得
     * allW = a * (3*y/2) + (a-1) * y
     * 化简合并得
     * allW = (3*a/2 + a - 1) * y
     * 再化简得
     * allW = ((5*a-2)/2) * y
     * 求出x和y
     * y = (2*allW) / (5*a - 2)
     * x = (3/2) * y
     *
     * @param allW 总宽度
     * @param ampCount 波形段数
     */
    @JvmStatic
    fun calculateAmpWidthAndGapWidth(allW: Float, ampCount: Int): Pair<Float, Float> {
        var ampWidth = 0f
        var ampGapWidth = 0f
        if (allW > 0 && ampCount > 0) {
            ampGapWidth = (FLOAT_TWO * allW) / (FLOAT_FIVE * ampCount - FLOAT_TWO)
            ampWidth = (FLOAT_THREE / FLOAT_TWO) * ampGapWidth
        }
        return Pair(ampWidth, ampGapWidth)
    }

    /**
     * 等比例计算波形宽度与间隙宽度 音波柱比音波柱之间间隔宽度为 1:1
     * @param allW 总宽度
     * @param ampCount 波形段数
     */
    @JvmStatic
    fun calculateAmpWidthAndGapWidthEqualProportion(allW: Float, ampCount: Int): Pair<Float, Float> {
        var ampWidth = 0f
        var ampGapWidth = 0f
        if (allW > 0 && ampCount > 0) {
           ampWidth = allW / (ampCount + ampCount - 1)
           ampGapWidth = ampWidth
        }
        return Pair(ampWidth, ampGapWidth)
    }
}