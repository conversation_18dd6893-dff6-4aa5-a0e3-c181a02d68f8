/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CollectionInfo.kt
 * * Description : 个人信息收集明示清单
 * * Version     : 1.0
 * * Date        : 2024/8/20
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.databean

class CollectionInfo(
    var id: Int = 0,
    var type: String,
    var content: String? = null,
    var dateCreated: Long = 0,
    var count: Int = 0
) {
    companion object {
        const val COLLECTION_TYPE_NONE = "none" /* compiled code */
        /*设备品牌*/
        const val COLLECTION_TYPE_BRAND = "brand" /* compiled code */
        /*QQ号/手机号/邮箱*/
        const val COLLECTION_TYPE_CONTACT = "contact"
        /*反馈内容附件（文字、图片）*/
        const val COLLECTION_TYPE_FEEDBACK: String = "feedback" /* compiled code */
        /*错误日志报告*/
        const val COLLECTION_TYPE_LOG: String = "log" /* compiled code */
        /*机型*/
        const val COLLECTION_TYPE_MODEL: String = "model" /* compiled code */
        /*IMEI/OpenID*/
        const val COLLECTION_TYPE_OPEN_ID: String = "open_id" /* compiled code */
        /*OS版本*/
        const val COLLECTION_TYPE_OS: String = "os" /* compiled code */
        /*埋点信息*/
        const val COLLECTION_TYPE_STATISTICS: String = "statistics" /* compiled code */
        /*音频*/
        const val COLLECTION_TYPE_RECORD_AUDIO: String = "record_audio" /* compiled code */
    }

    fun isBrand(): Boolean {
        return type == COLLECTION_TYPE_BRAND
    }

    fun isContact(): Boolean {
        return type == COLLECTION_TYPE_CONTACT
    }

    fun isFeedback(): Boolean {
        return type == COLLECTION_TYPE_FEEDBACK
    }

    fun isLog(): Boolean {
        return type == COLLECTION_TYPE_LOG
    }

    fun isAudio(): Boolean {
        return type == COLLECTION_TYPE_RECORD_AUDIO
    }

    fun isOpenId(): Boolean {
        return type == COLLECTION_TYPE_OPEN_ID
    }

    override fun toString(): String {
        return "CollectionInfo(type='$type', content='$content', dateCreated='$dateCreated', count='$count')"
    }
}

data class CollectionMenuTime(val desc: String, val value: Int) {

    companion object {
        const val COLLECTION_TIME_DAY_7 = 7 //7天
        const val COLLECTION_TIME_MONTH_1 = 30 //一个月
        const val COLLECTION_TIME_MONTH_3 = 90 //3个月
        const val COLLECTION_TIME_YEAR = 365 //一年
    }
}