package com.soundrecorder.common.dialog

import android.app.Activity
import android.app.Dialog
import android.content.DialogInterface
import android.os.Build
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.TextView
import android.window.OnBackInvokedCallback
import android.window.OnBackInvokedDispatcher
import androidx.annotation.RequiresApi
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.edittext.COUIEditText
import com.coui.appcompat.edittext.COUIInputView
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.dialog.BaseWindowCallback
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.KeyboardUtils.hideSoftInput
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.R
import com.soundrecorder.common.utils.ViewUtils.isOutOfBounds
import com.soundrecorder.common.utils.ViewUtils.updateWindowLayoutParams

abstract class AbsEditAlertDialog(val activity: Activity) : DefaultLifecycleObserver,
    ViewTreeObserver.OnWindowAttachListener {
    companion object {
        private const val DOUBLE_CHECK_TIME = 2000
        private const val SHOW_INPUT_DELAY = 300L
    }

    protected val customView by lazy {
        LayoutInflater.from(activity).inflate(getLayoutId(), null)
    }
    private var dialog: AlertDialog? = null
    private var controller: EditNoteController? = null

    private var lastClickTime = 0L
    private var lastBackPressTime = 0L

    open fun onCreateDialog(builder: COUIAlertDialogBuilder) {}

    abstract fun onSave()
    abstract fun onCancel()

    open fun getLayoutId(): Int {
        return R.layout.dialog_save_recording
    }


    abstract fun getOriginalContent(): String

    fun getNewContent(): String {
        return getEditText()?.text.toString().trim()
    }

    @StringRes
    open fun getTitleText(): Int {
        return R.string.recorder_save_file
    }

    @StringRes
    open fun getCancelText(): Int {
        return R.string.cancel
    }

    @StringRes
    open fun getSaveText(): Int {
        return R.string.rename_save
    }

    open fun getEditText(): COUIEditText? {
        return customView.findViewById<COUIInputView?>(R.id.coui_edit).editText
    }

    open fun getTextNoteView(): TextView? {
        return customView.findViewById(R.id.dlg_rename_note)
    }

    open fun onInitCustomView(customView: View) {
    }

    fun show() {
        if (isShowing()) {
            return
        }
        dialog = createDefaultDialog().apply {
            onCreateDialog(this)
        }.show().apply {
            findViewById<View>(com.support.dialog.R.id.customPanel)?.setWeight(1f)
            updateWindowLayoutParams(window)
            setOnClick()
            clickOnBackKey()
            clickOnOutArea()
        }
        onInitCustomView(customView)
        initController()
    }

    /**
     * 根据UI规范，弹窗有最大高度限制，当自定义view高度过大时，底部按钮会显示不全，因此需要设置customPanel的weight为1
     */
    private fun View.setWeight(weight: Float) {
        layoutParams = layoutParams.let {
            (it as? LinearLayout.LayoutParams)?.weight = weight
            (it as? LinearLayoutCompat.LayoutParams)?.weight = weight
            it
        }
    }

    private fun AlertDialog.setOnClick() {
        window?.decorView?.viewTreeObserver?.addOnWindowAttachListener(this@AbsEditAlertDialog)
        getButton(DialogInterface.BUTTON_POSITIVE).setOnClickListener {
            onSave()
        }
        getButton(DialogInterface.BUTTON_NEGATIVE).setOnClickListener {
            onCancel()
        }
    }

    private fun createDefaultDialog(): COUIAlertDialogBuilder {
        (customView.parent as? ViewGroup)?.removeAllViews()
        val builder = COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_BottomAssignment)
        builder.setBlurBackgroundDrawable(true).setTitle(getTitleText())
            .setView(customView)
            .setPositiveButton(getSaveText(), null)
            .setNegativeButton(getCancelText(), null)
            .setCancelable(false)
        return builder
    }

    private fun initController() {
        val editText = getEditText() ?: return
        val textNoteView = getTextNoteView() ?: return
        controller = EditNoteController().apply {
            colorEditText = editText
            textNote = textNoteView
            title = getOriginalContent()
            this.saveEnabledBlock = ::checkSaveBtn

            init()
            initEditTalkBack()
        }
    }

    private fun checkSaveBtn(enable: Boolean) {
        dialog?.getButton(DialogInterface.BUTTON_POSITIVE)?.isEnabled = enable
    }

    private fun AlertDialog.clickOnBackKey() {
        setOnBackPressedListener {
            val time = System.currentTimeMillis()
            val moreCheckTime = time - lastBackPressTime > DOUBLE_CHECK_TIME
            if (isTitleChange() && moreCheckTime) {
                dialog?.setCancelable(false)
                ToastManager.showShortToast(
                    BaseApplication.getAppContext(),
                    R.string.panel_back_toast
                )
                lastBackPressTime = time
            } else {
                dialog?.setCancelable(true)
            }
        }
    }

    private fun AlertDialog.clickOnOutArea() {
        val function: () -> Boolean = {
            val time = System.currentTimeMillis()
            val moreCheckTime = time - lastClickTime > DOUBLE_CHECK_TIME
            if (isTitleChange() && moreCheckTime) {
                dialog?.setCancelable(false)
                ToastManager.showShortToast(
                    BaseApplication.getAppContext(),
                    R.string.panel_click_outside_view_toast
                )
                lastClickTime = time
                true
            } else {
                dialog?.setCancelable(true)
                onCancel()
            }
            false
        }
        findViewById<View>(android.R.id.content)?.setOnClickListener {
            function()
        }
        window?.apply {
            callback = object : BaseWindowCallback(callback) {
                override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
                    val action = event?.action ?: return superDispatchTouchEvent(event)
                    val view = peekDecorView() ?: return superDispatchTouchEvent(event)
                    val isOutOfBounds =
                        action == MotionEvent.ACTION_DOWN && view.isOutOfBounds(event)
                    if (<EMAIL>() && isOutOfBounds) {
                        if (function()) {
                            return true
                        }
                    }
                    return superDispatchTouchEvent(event)
                }
            }
        }
    }

    fun isShowing() = dialog?.isShowing == true

    private fun showInputKeyboard() {
        getEditText()?.let {
            it.isFocusable = true
            it.isFocusableInTouchMode = true
            it.requestFocus()
            dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        }
    }

    private fun showInputKeyboardWhenWindowAttached() {
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
    }

    private fun hideInputKeyBoard() {
        getEditText()?.apply {
            hideSoftInput(activity)
        }
    }

    fun onConfigurationChanged() {
        DebugUtil.d("onConfigurationChanged", this.javaClass.simpleName)
        if (isShowing()) {
            dialog?.window?.decorView?.requestLayout()
        }
    }

    protected fun isTitleChange(): Boolean {
        val newContent = getNewContent()
        return newContent.isNotEmpty() && newContent != getOriginalContent()
    }

    fun dismiss() {
        if (isShowing()) {
            hideInputKeyBoard()
            dialog?.dismiss()
        }
    }

    override fun onResume(owner: LifecycleOwner) {
        showInputKeyboard()
    }

    override fun onWindowAttached() {
        getEditText()?.selectAll()
        showInputKeyboardWhenWindowAttached()
        if (activity is LifecycleOwner) {
            activity.lifecycle.addObserver(this)
        }
    }

    override fun onWindowDetached() {
        hideInputKeyBoard()
        if (activity is LifecycleOwner) {
            activity.lifecycle.removeObserver(this)
        }
        controller?.release()
        controller = null
        dialog?.window?.decorView?.viewTreeObserver?.removeOnWindowAttachListener(this@AbsEditAlertDialog)
        dialog = null
    }

    fun showTextNote(resId: Int) {
        controller?.showTextNote(resId)
    }

    fun getNameEdit(): Boolean = (controller?.isNameEdit == true)
}

@RequiresApi(Build.VERSION_CODES.TIRAMISU)
class CustomBackInvokeCallBack(var callback: (() -> Unit)? = null) : OnBackInvokedCallback {
    override fun onBackInvoked() {
        callback?.invoke()
    }
}

fun Dialog.setOnBackPressedListener(clickBack: () -> Unit) {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        val backInvokedCallback = CustomBackInvokeCallBack().apply {
            callback = clickBack
        }
        onBackInvokedDispatcher.registerOnBackInvokedCallback(OnBackInvokedDispatcher.PRIORITY_DEFAULT, backInvokedCallback)
        setOnDismissListener {
            onBackInvokedDispatcher.unregisterOnBackInvokedCallback(backInvokedCallback)
        }
    } else {
        setOnKeyListener { _, keyCode, event ->
            val isBack =
                keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP
            if (isShowing && isBack) {
                clickBack.invoke()
            }
            false
        }
    }
}