/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertTaskAction.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/29
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.flexible

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.preference.Preference
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.coui.appcompat.preference.COUICheckBoxPreference
import com.coui.appcompat.preference.COUIPreferenceFragment
import com.soundrecorder.common.R

class ShareLinkDialogFragment : COUIPanelFragment() {
    companion object {
        const val TAG = "ShareLinkDialogFragment"
    }

    private var mLinkPreferenceDialog = ShareLinkDialogPreferenceFragment()
    private var mShareLinkCallBack: ((Boolean, Boolean, Boolean) -> Unit)? = null
    private var mIsShowTxt: Boolean = false
    private var mIsShowSummary: Boolean = false

    override fun initView(panelView: View?) {
        super.initView(panelView)
        initObserves()
        initToolbar()
        initPreference()
        initDialogContent(panelView)
    }

    private fun initObserves() {
        mLinkPreferenceDialog.setCallBack(
            mIsShowTxt,
            mIsShowSummary
        ) { isSelectAudio, isSelectTXT, isSelectSummary ->
            mShareLinkCallBack?.invoke(isSelectAudio, isSelectTXT, isSelectSummary)
        }
    }

    @SuppressLint("CommitTransaction")
    private fun initPreference() {
        childFragmentManager.beginTransaction().replace(contentResId, mLinkPreferenceDialog)
            .commit()
    }

    private fun initDialogContent(panelView: View?) {
        hideDragView()
    }

    private fun initToolbar() {
        val toolbarTitle = resources.getString(R.string.share_link)
        toolbar = toolbar?.apply {
            title = toolbarTitle
            isTitleCenterStyle = false
            setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
        }
        toolbar.setNavigationOnClickListener {
            dismissDialog()
        }
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        //设置dialogFragment的背景
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.setPanelBackgroundTintColor(
            COUIContextUtil.getAttrColor(
                context, com.support.appcompat.R.attr.couiColorSurfaceWithCard
            )
        )
    }

    private fun dismissDialog() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    fun setShareLinkCallBack(
        isShowTxt: Boolean,
        isShowSummary: Boolean,
        callBack: (Boolean, Boolean, Boolean) -> Unit
    ) {
        mIsShowTxt = isShowTxt
        mIsShowSummary = isShowSummary
        mShareLinkCallBack = callBack
    }

    override fun onDestroy() {
        super.onDestroy()
        mShareLinkCallBack = null
    }
}

class ShareLinkDialogPreferenceFragment : COUIPreferenceFragment() {
    companion object {
        private const val TAG = "ShareLinkDialogPreferenceFragment"
        private const val SHARE_LINK_AUDIO = "share_link_audio"
        private const val SHARE_LINK_TXT = "share_link_text"
        private const val SHARE_LINK_SUMMARY = "share_link_summary"
        private const val SHARE_SUMMARY_BUTTON = "share_summary_dialog_button"
    }

    private var mShareLinkAudio: COUICheckBoxPreference? = null
    private var mShareLinkTxt: COUICheckBoxPreference? = null
    private var mShareLinkSummary: COUICheckBoxPreference? = null
    private var mShareSummaryButton: ShareSummaryAndTextPreference? = null
    private val mShareLinkMap = hashMapOf<String, Boolean>()
    private var mCallBack: ((Boolean, Boolean, Boolean) -> Unit)? = null
    private var mIsShowTxt: Boolean = false
    private var mIsShowSummary: Boolean = false
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val onCreateView = super.onCreateView(inflater, container, savedInstanceState)
        //去掉原本的toolbar
        onCreateView?.let {
            (it as? ViewGroup)?.apply {
                removeView(it.findViewById(com.support.preference.R.id.appbar_layout))
            }
        }
        return onCreateView
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        view.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val height = view.measuredHeight
                val minHeight = view.resources.getDimension(R.dimen.dp374)
                if (height < minHeight) {
                    val layoutParams = view.layoutParams
                    layoutParams.height = minHeight.toInt()
                    view.layoutParams = layoutParams
                }
            }
        })
    }

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        addPreferencesFromResource(R.xml.share_link_dialog_preference)
        mShareLinkAudio = findPreference(SHARE_LINK_AUDIO)
        mShareLinkTxt = findPreference(SHARE_LINK_TXT)
        mShareLinkSummary = findPreference(SHARE_LINK_SUMMARY)
        mShareSummaryButton = findPreference(SHARE_SUMMARY_BUTTON)
        mShareLinkMap.put(SHARE_LINK_AUDIO, true)
        mShareLinkTxt?.let {
            it.isVisible = mIsShowTxt
            it.isChecked = mIsShowTxt
            mShareLinkMap.put(SHARE_LINK_TXT, it.isChecked)
        }
        mShareLinkSummary?.let {
            it.isVisible = mIsShowSummary
            it.isChecked = mIsShowSummary
            mShareLinkMap.put(SHARE_LINK_SUMMARY, it.isChecked)
        }
        updateShareSummaryButtonStatus()

        mShareSummaryButton?.setListener(object :
            ShareSummaryAndTextPreference.OnBindOrClickListener {
            override fun onClick() {
                val isSelectAudio = mShareLinkMap[SHARE_LINK_AUDIO] ?: false
                val isSelectTXT = mShareLinkMap[SHARE_LINK_TXT] ?: false
                val isSelectSummary = mShareLinkMap[SHARE_LINK_SUMMARY] ?: false
                mCallBack?.invoke(isSelectAudio, isSelectTXT, isSelectSummary)
            }
        })
    }

    override fun onPreferenceTreeClick(preference: Preference?): Boolean {
        if (preference == null) {
            return super.onPreferenceTreeClick(preference)
        }
        when (preference.key) {
            SHARE_LINK_AUDIO -> {
                mShareLinkAudio?.let {
                    mShareLinkMap[SHARE_LINK_AUDIO] = it.isChecked
                }
                updateShareSummaryButtonStatus()
            }

            SHARE_LINK_TXT -> {
                mShareLinkTxt?.let {
                    mShareLinkMap[SHARE_LINK_TXT] = it.isChecked
                }
                updateShareSummaryButtonStatus()
            }

            SHARE_LINK_SUMMARY -> {
                mShareLinkSummary?.let {
                    mShareLinkMap[SHARE_LINK_SUMMARY] = it.isChecked
                }
                updateShareSummaryButtonStatus()
            }
        }
        return super.onPreferenceTreeClick(preference)
    }

    fun updateShareSummaryButtonStatus() {
        val isSelectAudio = mShareLinkMap[SHARE_LINK_AUDIO] ?: false
        val isSelectTXT = mShareLinkMap[SHARE_LINK_TXT] ?: false
        val isSelectSummary = mShareLinkMap[SHARE_LINK_SUMMARY] ?: false
        mShareSummaryButton?.isEnabled = !(!isSelectAudio && !isSelectTXT && !isSelectSummary)
    }

    fun setCallBack(
        isShowTxt: Boolean,
        isShowSummary: Boolean,
        callBack: (Boolean, Boolean, Boolean) -> Unit
    ) {
        mIsShowTxt = isShowTxt
        mIsShowSummary = isShowSummary
        mCallBack = callBack
        mShareLinkTxt?.isVisible = mIsShowTxt
        mShareLinkSummary?.isVisible = mIsShowSummary
    }

    override fun onDestroy() {
        super.onDestroy()
        mCallBack = null
    }
}
