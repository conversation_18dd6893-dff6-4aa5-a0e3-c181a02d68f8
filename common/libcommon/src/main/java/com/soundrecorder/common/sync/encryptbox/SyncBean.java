package com.soundrecorder.common.sync.encryptbox;

import java.util.List;

public class SyncBean {
    private int mSyncType;
    //TYPE_ENCRYPTION = 1000
    // TYPE_DECRYPTION = 1001
    // TYPE_DELETE = 1002
    // TYPE_DATA_LARGE = 1003

    private String mMessage;

    private String mTargetPath;

    private List<SyncFileInfo> mFileInfoList;

    public int getSyncType() {
        return mSyncType;
    }

    public String getMessage() {
        return mMessage;
    }

    public String getTargetPath() {
        return mTargetPath;
    }

    public List<SyncFileInfo> getFileInfoList() {
        return mFileInfoList;
    }

    public void setSyncType(int mSyncType) {
        this.mSyncType = mSyncType;
    }

    public void setMessage(String mMessage) {
        this.mMessage = mMessage;
    }

    public void setTargetPath(String mTargetPath) {
        this.mTargetPath = mTargetPath;
    }

    public void setFileInfoList(List<SyncFileInfo> mFileInfoList) {
        this.mFileInfoList = mFileInfoList;
    }

    public static class SyncFileInfo {
        private String mVolumeName;//文件被设为私密前的存储器卷积名，如storage/emulated/0,在R上有对应的转换规则
        private String mRelativePath;//文件被设为私密前的相对路径
        private String mDisplayName;//文件被设为私密前的文件名称（带后缀）
        private String mMd5; // 文件的md5

        public String getVolumeName() {
            return mVolumeName;
        }

        public String getRelativePath() {
            return mRelativePath;
        }

        public String getDisplayName() {
            return mDisplayName;
        }

        public String getMd5() {
            return mMd5;
        }

        public void setVolumeName(String mVolumeName) {
            this.mVolumeName = mVolumeName;
        }

        public void setRelativePath(String mRelativePath) {
            this.mRelativePath = mRelativePath;
        }

        public void setDisplayName(String mDisplayName) {
            this.mDisplayName = mDisplayName;
        }

        public void setMd5(String mMd5) {
            this.mMd5 = mMd5;
        }

        @Override
        public String toString() {
            return "SyncFileInfo{" +
                    "mVolumeName='" + mVolumeName + '\'' +
                    ", mRelativePath='" + mRelativePath + '\'' +
                    ", mDisplayName='" + mDisplayName + '\'' +
                    ", mMd5='" + mMd5 + '\'' +
                    '}';
        }
    }


    @Override
    public String toString() {
        return "SyncBean{" +
                "mSyncType=" + mSyncType +
                ", mMessage='" + mMessage + '\'' +
                ", mTargetPath='" + mTargetPath + '\'' +
                ", mFileInfoList=" + mFileInfoList +
                '}';
    }
}
