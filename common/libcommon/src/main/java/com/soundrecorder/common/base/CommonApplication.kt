/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CommonApplication
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.base

import android.util.Log
import com.oplus.recorderlog.log.RecorderLogger.initLog
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.OpenIdUtils
import com.soundrecorder.common.task.ActivityTaskUtils.initActivityTaskManager
import com.soundrecorder.modulerouter.notification.NotificationInterface
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

open class CommonApplication : BaseApplication() {

    companion object {
        var sLaunchedByProvider = false
        private const val TAG = "CommonApplication"
    }


    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }
    private val aiAsrManagerAction by lazy {
        Injector.injectFactory<AIAsrManagerAction>()
    }

    override fun onCreateInit() {
        super.onCreateInit()
        initSdk()
        this.initActivityTaskManager()
        Injector.injectFactory<NotificationInterface>()?.cancelAllNotification()
        sLaunchedByProvider = false
    }

    private fun initSdk() {
        val scope = MainScope()
        //耗时的，不是非常重要的放下面，部分机器获取duid非常耗时，甚至会卡住
        scope.launch(Dispatchers.Default) {
            Log.e(TAG, "initSdk 1 start")
            //必须先初始化OpenIdSdk
            OpenIdUtils.INSTANCE.init(getAppContext())
            //初始化XLog sdk
            initLog(getAppContext())
            Log.e(TAG, "initSdk 1 end")
        }

        Log.d(TAG, "isLaunchedByProvider = $sLaunchedByProvider")
        if (sLaunchedByProvider.not()) {
            scope.launch(Dispatchers.Default) {
                Log.e(TAG, "initSdk 2 start")
                // 获取是否支持录音摘要能力
                summaryApi?.initSupportSummary(true)
                // 获取是否支持AI ASR能力
                aiAsrManagerAction?.loadSupportAIAsr(getAppContext(), true)
                Log.e(TAG, "initSdk 2 end")
            }
        }
    }
}