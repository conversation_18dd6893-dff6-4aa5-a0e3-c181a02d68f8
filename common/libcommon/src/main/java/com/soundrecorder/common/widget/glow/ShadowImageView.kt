/*
 * *******************************************************************
 *  * Copyright (C), 2025 -2035 Oplus. All rights reserved..
 *  * VENDOR_EDIT
 *  * File        : ShadowImageView.kt
 *  * Description :
 *  * Version     : 1.0
 *  * Date        : 2025/7/16
 *  * Author      : 80414876
 *  * OPLUS Java File Skip Rule:IllegalCatch,ParameterNumber,LineLength
 *  * ---------------------Revision History: ----------------------------
 *  *  <author>        <data>       <version>  <desc>
 *  * 80414876        2025/7/16       1.0      create
 *  ********************************************************************
 */

package com.soundrecorder.common.widget.glow

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import com.soundrecorder.common.R
import com.soundrecorder.common.widget.AnimatedCircleButton
import androidx.core.content.withStyledAttributes
import com.soundrecorder.base.utils.DebugUtil

class ShadowImageView @JvmOverloads constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int = 0) :
    AppCompatImageView(context, attrs, defStyleAttr) {

    private var referenceViewId = View.NO_ID
    private var referenceView: View? = null
    private var shadowOffsetX: Float = DEFAULT_SHADOW_OFFSET_X
    private var shadowOffsetY: Float = DEFAULT_SHADOW_OFFSET_Y
    private var shadowRadius: Float = DEFAULT_SHADOW_BLUR_RADIUS
    private var shadowColor: Int = Color.TRANSPARENT
    private val shadowPaint = Paint(Paint.ANTI_ALIAS_FLAG)

    companion object {
        private const val TAG = "ShadowImageView"
        private const val DEFAULT_SHADOW_CIRCLE_COLOR = 0x3DDB382C
        private const val DEFAULT_SHADOW_OFFSET_X = 0.0f
        private const val DEFAULT_SHADOW_OFFSET_Y  = 0.0f
        private const val DEFAULT_SHADOW_BLUR_RADIUS = 0.0f
    }

    init {
        context.withStyledAttributes(set = attrs, attrs = R.styleable.ShadowImageEffect, defStyleAttr = defStyleAttr, defStyleRes = 0) {
            referenceViewId = getResourceId(R.styleable.ShadowImageEffect_shadow_reference, View.NO_ID)
            shadowOffsetX = getDimension(R.styleable.ShadowImageEffect_shadow_offsetX, DEFAULT_SHADOW_OFFSET_X)
            shadowOffsetY = getDimension(R.styleable.ShadowImageEffect_shadow_offsetY, DEFAULT_SHADOW_OFFSET_Y)
            shadowRadius = getDimension(R.styleable.ShadowImageEffect_shadow_blur_radius, DEFAULT_SHADOW_BLUR_RADIUS)
            shadowColor = getColor(R.styleable.ShadowImageEffect_shadow_color, DEFAULT_SHADOW_CIRCLE_COLOR)
        }
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        referenceView = (parent as View).findViewById<View>(referenceViewId)
        DebugUtil.i(TAG, "onAttachedToWindow referenceViewId:$referenceViewId")
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        referenceView = null
    }

    override fun onDraw(canvas: Canvas) {
        drawShadow(canvas)
    }

    private fun drawShadow(canvas: Canvas) {
        referenceView?.let {
            shadowPaint.color = Color.TRANSPARENT
            var refViewWidth = it.width.toFloat()
            val refViewHeight = it.height.toFloat()
            if (it is AnimatedCircleButton) {
                refViewWidth = it.mCircleScale * it.mCircleRadius * 2
            }
            shadowPaint.setShadowLayer(shadowRadius, shadowOffsetX, shadowOffsetY, shadowColor)
            canvas.drawCircle(refViewWidth / 2, refViewHeight / 2, refViewWidth / 2, shadowPaint)
        }
    }
}