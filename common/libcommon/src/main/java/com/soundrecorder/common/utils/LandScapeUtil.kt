package com.soundrecorder.common.utils

import android.app.Activity

/**
 * 横屏的工具类
 */
object LandScapeUtil {

    const val EXTAR_LAST_IS_WINDOW_PAR = "last_is_windowparameter"
    const val TAG = "LandScapeUtil"
    const val WINDOW_HEIGHT_560 = 560
    private const val PLAY_SMALL_WINDOW_HEIGHT = 280
    private const val PLAY_WINDOW_HEIGHT_540 = 450


    /**
     * 判断屏幕高度是否高小于450
     */
    @JvmStatic
    fun spitWindowHeightLessThanForPlay450(activity: Activity?): Boolean {
        return spitSmallWindowLessThanForPlay(activity, PLAY_WINDOW_HEIGHT_540)
    }

    /**
     * 判断屏幕高度是否小于280
     */
    @JvmStatic
    fun spitSmallWindowLessThanForPlay280(activity: Activity?): Boolean {
        return spitSmallWindowLessThanForPlay(activity, PLAY_SMALL_WINDOW_HEIGHT)
    }

    @JvmStatic
    private fun spitSmallWindowLessThanForPlay(activity: Activity?, height: Int): Bo<PERSON>an {
        var boo = false
        activity?.let {
            val windowHeight = it.resources.configuration.screenHeightDp
            return windowHeight < height
        }
        return boo
    }
}