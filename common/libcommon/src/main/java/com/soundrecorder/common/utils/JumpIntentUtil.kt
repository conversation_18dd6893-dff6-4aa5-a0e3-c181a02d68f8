/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryModel
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.os.SystemClock
import android.provider.CalendarContract
import android.provider.ContactsContract
import android.util.Log
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.base.utils.ZoomWindowUtil
import com.soundrecorder.common.flexible.FollowHandPanelUtils
import com.soundrecorder.common.share.ShareSummaryCopy
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.CoroutineScope
import java.util.regex.Pattern
import androidx.core.net.toUri

object JumpIntentUtil {
    private const val TAG = "JumpIntentUtil"

    private const val TEXT_INTENT_PACKAGE_NAME = "com.coloros.sceneservice"
    private const val CALENDA_RPROVIDER_AUTHORITY = "com.android.providers.calendar"

    private const val PARAM_FROM_PACKAGE_NAME = "from"
    private const val PARAM_MSG_BODY = "msgBody"
    private const val SUPPORT_TEXT_DEEPLINK = "support_text_deeplink"
    private const val SUPPORT_TASK = "isSupportTask"

    //短信
    private const val SMS_VIEW_MODEL = "sms_view_model"
    private const val SMS = "sms"

    //日程
    private const val TEXT_INTENT_URI = "ti://com.oplus.textintent"
    private const val BEGIN_TIME = "beginTime"
    private const val END_TIME = "endTime"
    private const val ALL_DAY = "allDay"
    private const val ACTION = "android.intent.action.INSERT"

    //待办
    private const val INSERT_TYPE = "insert_type"
    private const val CONTENT = "content"
    private const val DTSTART = "dtstart"
    private const val ACTION_NOTE_TO_DO = "com.oplus.note.action.edit_todo"
    private const val ACTION_FROM = "action_from"
    private const val CONTENT_NOTE = "content"
    private const val ALARM_TIME = "alarm_time"

    //网址
    private const val HTTP = "http://"
    private const val HTTPS = "https://"
    private const val REGEX = "^((https?|ftp)://)?(([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\\.)+[a-zA-Z]{2,}|localhost)(:\\d+)?(/.*)?$"
    private val VALID_URL_PATTERN = Pattern.compile(REGEX)
    //号码
    private val VALID_PHONE_REGEX = Regex("^(\\+\\d{1,3}[- ]?)?\\d{10,15}$")

    private val shareAction by lazy {
        Injector.injectFactory<ShareAction>()
    }


    /**
     * 是否支持文本意图接口
     */
    @JvmStatic
    fun isSupportTextIntent(context: Context): Boolean {
        return runCatching {
            val info = context.packageManager.getApplicationInfo(
                TEXT_INTENT_PACKAGE_NAME,
                PackageManager.GET_META_DATA
            )
            val isSupportTextIntent = info.metaData.getBoolean(SUPPORT_TEXT_DEEPLINK)
            DebugUtil.d(TAG, "isSupportTextIntent: $isSupportTextIntent")
            isSupportTextIntent
        }.getOrDefault(false)
    }

    /**
     * 是否支持日历待办
     */
    @JvmStatic
    fun isSupportTask(context: Context): Boolean {
        return runCatching {
            val info = context.packageManager.getApplicationInfo(
                CALENDA_RPROVIDER_AUTHORITY,
                PackageManager.GET_META_DATA
            )
            val isSupportTask = info.metaData.getBoolean(SUPPORT_TASK)
            DebugUtil.d(TAG, "isSupportTask: $isSupportTask")
            isSupportTask
        }.getOrDefault(false)
    }

    @JvmStatic
    fun isValidUrl(url: String): Boolean {
        return VALID_URL_PATTERN.matcher(url).matches()
    }
    @JvmStatic
    fun isValidPhoneNumber(input: String): Boolean {
        return VALID_PHONE_REGEX.matches(input)
    }

    @JvmStatic
    fun createScheduleByTextIntent(
        context: Context,
        time: String,
        timeInitialed: Boolean,
        schedule: String? = null,
        address: String? = null
    ): Boolean {
        if (timeInitialed) {
            val bundle = Bundle()
            val msg = if (schedule.isNullOrEmpty()) {
                "{\"text\":\"{\\\"startTimeMills\\\":${time.toLong()},\\\"location\\\":\\\"${address ?: ""}\\\"}\",\"type\":5}"
            } else {
                "{\"text\":\"{\\\"startTimeMills\\\":${time.toLong()}," +
                        "\\\"location\\\":\\\"${address ?: ""}\\\",\\\"title\\\":\\\"${schedule}\\\"}\",\"type\":5}"
            }
            bundle.putString(PARAM_MSG_BODY, msg)
            bundle.putString(PARAM_FROM_PACKAGE_NAME, context.packageName)
            val intent = Intent(Intent.ACTION_VIEW, TEXT_INTENT_URI.toUri())
            intent.setPackage(TEXT_INTENT_PACKAGE_NAME)
            intent.putExtras(bundle)
            return startActivity(context, intent)
        } else {
            return createScheduleIntent(context, time, schedule, address)
        }
    }

    /**
     * 文本意图跳转不支持打开全天开关，使用隐式跳转到新建日程页面
     */
    @JvmStatic
    private fun createScheduleIntent(
        context: Context,
        time: String,
        schedule: String? = null,
        address: String? = null
    ): Boolean {
        val uri = "content://com.android.calendar/events/2"
        val uriBefore = "content://com.coloros.calendar/events/2"
        runCatching {
            return createScheduleIntentByUri(uri, context, time, schedule, address)
        }.onFailure {
            DebugUtil.d(TAG, "createScheduleIntent error.")
            runCatching {
                return createScheduleIntentByUri(uriBefore, context, time, schedule, address)
            }.onFailure {
                DebugUtil.d(TAG, "createScheduleIntent error.")
            }
        }
        return false
    }

    @JvmStatic
    private fun createScheduleIntentByUri(
        uri: String,
        context: Context,
        time: String,
        schedule: String? = null,
        address: String? = null
    ): Boolean {
        val intent = Intent(ACTION, uri.toUri())
        val bundle = Bundle()
        bundle.putLong(BEGIN_TIME, time.toLong())
        bundle.putLong(END_TIME, time.toLong())
        bundle.putBoolean(ALL_DAY, true)
        bundle.putString(CalendarContract.Events.TITLE, schedule)
        bundle.putString(CalendarContract.Events.EVENT_LOCATION, address)
        intent.putExtras(bundle)
        context.startActivity(intent)
        return true
    }

    @JvmStatic
    fun intentToAddress(context: Context, address: String) {
        if (address.isEmpty()) return
        val bundle = Bundle()
        val msg = "{\"text\":\"$address\",\"type\":4}"
        bundle.putString(PARAM_MSG_BODY, msg)
        bundle.putString(PARAM_FROM_PACKAGE_NAME, context.packageName)
        val intent = Intent(Intent.ACTION_VIEW, TEXT_INTENT_URI.toUri()).apply {
            putExtras(bundle)
            setPackage(TEXT_INTENT_PACKAGE_NAME)
        }
        startActivity(context, intent)
    }

    @JvmStatic
    fun callAddTodo(context: Context, schedule: String, time: Long = 0) {
        //16跳日历， 以前跳便签
        val realTime = if (time >= 0) {
            time
        } else {
            SystemClock.elapsedRealtimeNanos()
        }
        if (OS12FeatureUtil.isColorOS16OrLater() && isSupportTask(context)) {
            val intent = Intent(Intent.ACTION_INSERT).apply {
                type = "vnd.android.cursor.item/task"
                putExtra(INSERT_TYPE, 1)
                if (schedule.isNotEmpty()) {
                    putExtra(CONTENT, schedule)
                }
                putExtra(DTSTART, time)
            }
            startActivity(context, intent)
        } else {
            val intent = Intent(ACTION_NOTE_TO_DO).apply {
                putExtra(ACTION_FROM, context.packageName)
                putExtra(CONTENT_NOTE, schedule)
                putExtra(ALARM_TIME, realTime)
            }
            startActivity(context, intent)
        }
    }

    @JvmStatic
    fun shareText(context: Context, content: String) {
        val sharingIntent = Intent(Intent.ACTION_SEND)
        sharingIntent.type = "text/plain"
        sharingIntent.removeExtra(Intent.EXTRA_TEXT)
        sharingIntent.putExtra(Intent.EXTRA_TEXT, content)
        val intent = Intent.createChooser(sharingIntent, null)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(context, intent)
    }

    @JvmStatic
    fun copyText(context: Activity, lifecycle: CoroutineScope, content: String) {
        shareAction?.share(
            context,
            ShareTextContent(-1, false, "", 0, emptyList()),
            ShareSummaryCopy(content),
            lifecycle,
            null
        )
    }

    @JvmStatic
    fun callPhone(context: Context, number: String) {
        if (number.isEmpty()) return
        val bundle = Bundle()
        val msg = "{\"text\":\"$number\",\"type\":0}"
        bundle.putString(PARAM_MSG_BODY, msg)
        bundle.putString(PARAM_FROM_PACKAGE_NAME, context.packageName)
        val intent = Intent(Intent.ACTION_VIEW, TEXT_INTENT_URI.toUri()).apply {
            putExtras(bundle)
            setPackage(TEXT_INTENT_PACKAGE_NAME)
        }
        startActivity(context, intent)
    }

    /**
     * 保存电话
     */
    @SuppressLint("ImplicitIntentDetector")
    @JvmStatic
    fun savePhoneNumber(
        context: Context,
        number: String,
        name: String? = null,
        company: String? = null,
        title: String? = null
    ): Boolean {
        val intent = Intent(Intent.ACTION_INSERT_OR_EDIT)
        intent.type = ContactsContract.Contacts.CONTENT_ITEM_TYPE
        intent.putExtra(ContactsContract.Intents.Insert.PHONE, number)
        intent.putExtra(ContactsContract.Intents.Insert.NAME, name)
        intent.putExtra(ContactsContract.Intents.Insert.COMPANY, company)
        intent.putExtra(ContactsContract.Intents.Insert.JOB_TITLE, title)
        return startActivity(context, intent)
    }

    /**
     * 发送短信
     */
    @JvmStatic
    fun sendSms(context: Context, number: String) {
        if (number.isEmpty()) return
        val intent = Intent(Intent.ACTION_SENDTO, Uri.fromParts(SMS, number, null))
        intent.putExtra(SMS_VIEW_MODEL, false)
        startActivity(context, intent)
    }

    @JvmStatic
    fun callExpress(context: Context, number: String) {
        if (number.isEmpty()) return
        val bundle = Bundle()
        val msg = "{\"text\":\"$number\",\"type\":8}"
        bundle.putString(PARAM_MSG_BODY, msg)
        bundle.putString(PARAM_FROM_PACKAGE_NAME, context.packageName)
        val intent = Intent(Intent.ACTION_VIEW, TEXT_INTENT_URI.toUri()).apply {
            putExtras(bundle)
            setPackage(TEXT_INTENT_PACKAGE_NAME)
        }
        startActivity(context, intent)
    }

    @JvmStatic
    fun callEmail(context: Context, email: String) {
        if (email.isEmpty()) return
        val bundle = Bundle()
        val msg = "{\"text\":\"$email\",\"type\":1}"
        bundle.putString(PARAM_MSG_BODY, msg)
        bundle.putString(PARAM_FROM_PACKAGE_NAME, context.packageName)
        val intent = Intent(Intent.ACTION_VIEW, TEXT_INTENT_URI.toUri()).apply {
            putExtras(bundle)
            setPackage(TEXT_INTENT_PACKAGE_NAME)
        }
        startActivity(context, intent)
    }

    /**
     * 保存email
     */
    @JvmStatic
    fun saveEmailAddress(context: Context, email: String) {
        if (email.isEmpty()) return
        val intent = Intent(Intent.ACTION_INSERT_OR_EDIT)
        intent.type = ContactsContract.Contacts.CONTENT_ITEM_TYPE
        intent.putExtra(ContactsContract.Intents.Insert.EMAIL, email)
        Log.d(TAG, "saveEmailAddress success.")
        startActivity(context, intent)
    }

    @JvmStatic
    fun callWebUrl(context: Context, url: String, handPanel: Boolean = false) {
        if (url.isEmpty()) return
        val uri = if (url.startsWith(HTTP) || url.startsWith(HTTPS)) {
            url.toUri()
        } else {
            "$HTTPS$url".toUri()
        }
        val intent = Intent(Intent.ACTION_VIEW, uri)
        if (handPanel) {
            ZoomWindowUtil.startActivityAsZoomWindow(context, intent) {
                DebugUtil.w(TAG, "fail zoom open window")
                FollowHandPanelUtils.startActivity(context, intent)
            }
        } else {
            startActivity(context, intent)
        }
    }

    @JvmStatic
    private fun startActivity(context: Context, intent: Intent): Boolean {
        return runCatching {
            context.startActivity(intent)
            Log.d(TAG, "start intent success")
            return@runCatching true
        }.onFailure {
            Log.e(TAG, "start intent failed ", it)
        }.getOrDefault(false)
    }
}