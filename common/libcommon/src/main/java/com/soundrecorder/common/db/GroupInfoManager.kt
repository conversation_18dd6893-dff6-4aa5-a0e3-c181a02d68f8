/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description : 分组管理类
 * * Version     : 1.0
 * * Date        : 2025/01/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.db

import android.annotation.SuppressLint
import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.MD5Utils
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.constant.RecordConstant.RECORD_DELETED
import com.soundrecorder.common.constant.RecordConstant.RECORD_DIRTY_MEGA_ONLY
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.executor.ExecutorManager
import com.soundrecorder.common.fileoperator.delete.DeleteFileDialogUtil
import com.soundrecorder.common.fileoperator.delete.OnFileDeleteListener
import com.soundrecorder.common.sync.db.RecordBulkInsert
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.RecordModeUtil.getRecordTypeForMediaRecord
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.utils.Injector
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.UUID
import java.util.concurrent.CopyOnWriteArrayList

class GroupInfoManager private constructor(val mContext: Context) {

    companion object {
        private const val TAG = "GroupInfoManager"
        const val RESULT_CODE_SUCCESS = 1
        const val RESULT_CODE_FAILED = -1
        const val RESULT_CODE_EXISTS = 0

        const val MOVE_GROUP_STATE_SUCCESS = 1
        const val MOVE_GROUP_STATE_FAILED = 0
        const val DELAY_TIME = 50L


        @SuppressLint("StaticFieldLeak")
        @Volatile
        private var sInstance: GroupInfoManager? = null
        private var mHasCalculatedMediaCount = false
        private var mTotalMediaCount = 0
        private var mDeleteFileDialogUtil: DeleteFileDialogUtil? = null
        private var checkJob: Job? = null

        @Synchronized
        @JvmStatic
        fun getInstance(context: Context): GroupInfoManager =
            sInstance ?: synchronized(GroupInfoManager::class.java) {
                sInstance ?: GroupInfoManager(context.applicationContext).apply {
                    sInstance = this
                }
            }

        @Synchronized
        @JvmStatic
        fun release() {
            sInstance = null
        }
    }
    private var mDefaultGroupInfoCalling: GroupInfo? = null
    private var mDefaultGroupInfoCommon: GroupInfo? = null
    private var mDefaultGroupInfoAll: GroupInfo? = null
    private var mDefaultGroupInfoDeleted: GroupInfo? = null
    var mAllGroupInfoList: MutableLiveData<MutableList<GroupInfo>> = MutableLiveData<MutableList<GroupInfo>>()

    private var mAllGroupInfoListCache = CopyOnWriteArrayList<GroupInfo>()
    private var mAllCustomGroupInfoList: MutableList<GroupInfo> = mutableListOf()

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    /**
     * 对比缓存的列表和当前列表MD5值，不相等则表示数据库列表有更新
     */
    private fun checkGroupListChanged(list: MutableList<GroupInfo>): Boolean {
        if (mAllGroupInfoListCache.isEmpty() && list.isNotEmpty()) {
            return true
        }
        if (mAllGroupInfoListCache.isNotEmpty() && list.isNotEmpty()) {
            val groupListMd5 = getGroupListMd5(list)
            val cacheListMd5 = getGroupListMd5(mAllGroupInfoListCache)
            DebugUtil.d(TAG, "groupListMd5 = $groupListMd5, cacheListMd5 = $cacheListMd5")
            if (!TextUtils.isEmpty(groupListMd5)
                && !TextUtils.isEmpty(cacheListMd5)
                && groupListMd5 != cacheListMd5
            ) {
                return true
            }
        }
        return false
    }
    private fun cacheGroupInfoList() {
        mAllGroupInfoList.value?.let {
            mAllGroupInfoListCache.clear()
            mAllGroupInfoListCache.addAll(it.toMutableList())
            DebugUtil.d(TAG, "cacheGroupInfoList = ${mAllGroupInfoListCache.size}")
        }
    }
    private fun getGroupListMd5(list: MutableList<GroupInfo>): String? {
        val stringBuffer = StringBuffer()
        list.forEach {
            stringBuffer.append(it.stringTag()).append(",")
        }
        return MD5Utils.calcMd5(stringBuffer.toString())
    }
    /**
     * 对未分组的旧数据，进行默认分组设置
     * @param record
     */
    fun tryToInitGroupInfoForRecord(record: Record) {
        if (record.groupUuid.isNullOrEmpty()) {
            resetGroupInfoForRecord(record)
        }
    }

    fun resetGroupInfoForRecord(record: Record) {
        DebugUtil.d(TAG, "resetGroupInfoForRecord record=$record")
        val recordType = getRecordTypeForMediaRecord(record)
        var defaultGroupType = GroupInfo.INT_DEFAULT_COMMON
        if (recordType == RecordModeConstant.RECORD_TYPE_CALL) {
            defaultGroupType = GroupInfo.INT_DEFAULT_CALLING
        }
        val groupInfo = getDefaultGroupItem(defaultGroupType)
        if (groupInfo != null) {
            record.groupId = groupInfo.mId
            record.groupUuid = groupInfo.mUuId
        }
    }

    /**
     * 考虑性能问题，在调用新增分组，修改分组以及分组排序方法后，需要调用此方法，刷新分组信息
     * 其他则调用refreshAllGroupInfoAfterDataChanged()方法刷新分组
     */
    private fun refreshAllGroupInfoList() {
        val allList = GroupInfoDbUtil.getAllGroupInfoList(mContext)
        if (allList.isNotEmpty()) {
            mAllGroupInfoList.postValueSafe(allList)
            cacheGroupInfoList()
        }
    }
    /**
     * 在分组信息变化后跟录音表分组信息有关联时，调用此方法以确保对应的分组文件数量正确
     * 在分组有变化后强制刷新一下group_info的所有信息
     */
    fun refreshAllGroupInfoAfterDataChanged(bForceUpdate: Boolean? = false) {
        DebugUtil.d(TAG, "refreshAllGroupInfoAfterDataChanged")
        //首先要更新的是每个分组下的文件数量 group_count
        val allList = GroupInfoDbUtil.getAllGroupInfoList(mContext)
        if (allList.isNotEmpty()) {
            val tmpResultList = GroupInfoDbUtil.updateGroupRecordsCount(mContext, allList)
            val resultList = tmpResultList?.toMutableList()
            val list = resultList ?: allList
            if (checkGroupListChanged(list) || bForceUpdate == true) {
                mAllGroupInfoList.postValueSafe(list)
                cacheGroupInfoList()
            }
        }
    }

    fun setTotalMediaCount(count: Int) {
        mTotalMediaCount = count
    }

    fun getTotalMediaCount(): Int {
        return mTotalMediaCount
    }

    fun refreshDefaultGroupInfoListFromMedia(values: Map<Int, Int>) {
        var allList = GroupInfoDbUtil.getAllGroupInfoList(mContext)
        if (allList.isEmpty()) {
            allList = GroupInfoDbUtil.genDefaultGroups()
        }

        allList.forEach { groupInfo ->
            if (groupInfo.isDefaultGroup()) {
                values.forEach {
                    if (it.key == groupInfo.mGroupType) {
                        groupInfo.mGroupCount = it.value
                        updateGroupInfo(groupInfo)
                    }
                }
            }
        }
       if (checkGroupListChanged(allList)) {
           mAllGroupInfoList.postValueSafe(allList)
           cacheGroupInfoList()
       }
    }

    private fun updateRecordWhenMoveGroup(
        targetRecord: Record,
        filePathList: List<String>?,
        bNeedCheckInDB: Boolean = false
    ): Record? {
        val canUpdate = if (!bNeedCheckInDB) {
            true
        } else {
            filePathList?.contains(targetRecord.data)
        }

        if (canUpdate == true) {
            val updatedCount = RecorderDBUtil.getInstance(mContext)
                .updateRecordDbByPath(targetRecord)
            DebugUtil.i(TAG,
                "moveRecordsToNewGroup: update local db result = $updatedCount, record: ${targetRecord.data}"
            )
            return if (updatedCount > 0) {
                null
            } else {
                targetRecord
            }
        }
        return targetRecord
    }

    /**
     * 移动到目标分组
     * @param recordList 被选中的records
     * @param isRecordListFromMedia 记录列表是否从媒体库获取
     * @param targetGroupInfo 目标分组的信息
     * @param listener 移动分组的回调
     */
    fun moveRecordsByGroupInfo(
        recordList: List<Record>?,
        isRecordListFromMedia: Boolean,
        targetGroupInfo: GroupInfo?,
        listener: MoveGroupListener?
    ) {
        val startTime = System.currentTimeMillis()
        if ((recordList.isNullOrEmpty()
                || targetGroupInfo == null
                || !GroupInfoDbUtil.checkGroupExists(mContext, targetGroupInfo)) || targetGroupInfo.isCallingGroup()
        ) {
            DebugUtil.e(TAG, "moveRecordsToNewGroup: params are illegal")
            listener?.onPostMoveGroup(MOVE_GROUP_STATE_FAILED)
            return
        }
        CoroutineScope(Dispatchers.IO).launch {
            var bNeedContinueCompare = false
            if (cloudKitApi?.isMediaComparing() == true) {
                cloudKitApi?.doStopMediaCompare(true)
                DebugUtil.i(TAG, "mediaComparing, need to stop diff compare when moveRecordsToNewGroup!")
                delay(DELAY_TIME)
                bNeedContinueCompare = true
            }
            //recordList从媒体库获取则需要先查records表
            var dbRecordList: List<Record>? = null
            if (isRecordListFromMedia) {
                dbRecordList = RecorderDBUtil.getInstance(mContext)
                    .getRecordsByPathList(recordList.map { it.data }.toTypedArray())
            }
            val filePathList = dbRecordList?.map { it.data }
            // records表查出来的记录和传入的recordList不一致时，说明有records表中不存在的record，此时显示转圈等待插入
            var showWaitingDialog = false
            if (isRecordListFromMedia) {
                showWaitingDialog = filePathList.isNullOrEmpty() || filePathList.size < recordList.size
            }
            listener?.onPreMoveGroup(showWaitingDialog)
            var index = 0
            val bulkInsert = RecordBulkInsert(mContext.contentResolver,
                DatabaseConstant.RecordUri.RECORD_CONTENT_URI)
            kotlin.runCatching {
                recordList.forEach {
                    if (it.groupUuid != targetGroupInfo.mUuId) {
                        it.groupId = targetGroupInfo.mId
                        it.groupUuid = targetGroupInfo.mUuId
                        it.dirty = RECORD_DIRTY_MEGA_ONLY
                        //先尝试更新，如果更新失败，则批量插入
                        val result =
                            updateRecordWhenMoveGroup(it, filePathList, isRecordListFromMedia)
                        result?.let { record ->
                            if (!record.uuid.isNullOrEmpty()) {
                                DebugUtil.e(
                                    TAG,
                                    "moveRecordsToNewGroup, update failed but record exists in db, skip insert: $record"
                                )
                                return@let
                            }
                            val uuID = UUID.randomUUID().toString()
                            record.uuid = uuID
                            record.checkMd5()
                            val recordType = getRecordTypeForMediaRecord(record)
                            if (TextUtils.isEmpty(record.relativePath)) {
                                val relativePath = RecorderDBUtil.getRelativePathForData(record.data,
                                    record.displayName)
                                record.relativePath = relativePath
                            }
                            record.dateCreated = record.dateModied
                            record.recordType = recordType
                            record.syncType = RecordConstant.SYNC_TYPE_BACKUP
                            record.setSyncUploadStatus(RecordConstant.SYNC_STATUS_BACKUP_START)
                            record.dirty = RecordConstant.RECORD_DIRTY_FILE_AND_MEGA
                            DebugUtil.d(TAG,
                                "moveRecordsToNewGroup, update failed, try to insert: $record"
                            )
                            bulkInsert.insert(record.convertToContentValues())
                        }
                    }
                    index++
                    listener?.onMovingGroup(index, recordList.size)
                }
            }.onFailure {
                DebugUtil.e(TAG, "moveRecordsToNewGroup, t1=$it")
            }
            kotlin.runCatching {
                bulkInsert.flush()
            }.onFailure {
                DebugUtil.e(TAG, "moveRecordsToNewGroup, t2=$it")
            }

            refreshAllGroupInfoAfterDataChanged()
            listener?.onPostMoveGroup(MOVE_GROUP_STATE_SUCCESS, null, showWaitingDialog)
            cloudKitApi?.trigBackupNow(BaseApplication.getAppContext())
            if (bNeedContinueCompare) {
                ExecutorManager.recordDataSyncExecutor?.execute {
                    cloudKitApi?.doMediaCompare(false)
                    DebugUtil.i(TAG,
                        "mediaComparing, Continue to diff compare after operate batch: deleteRecords!"
                    )
                }
            }
            DebugUtil.i(TAG, "moveRecordsToNewGroup cost time = ${System.currentTimeMillis() - startTime} ms")
        }
    }

    /**
     * 根据目标分组的名称 移动
     * @param recordList 被选中的records
     * @param targetGroupName 目标分组的组名
     * @param listener 移动分组的回调
     */
    fun moveRecordsByGroupName(
        recordList: List<Record>?,
        isRecordListFromMedia: Boolean,
        targetGroupName: String?,
        listener: MoveGroupListener?
    ) {
        if (recordList.isNullOrEmpty() || TextUtils.isEmpty(targetGroupName)) {
            DebugUtil.e(TAG, "moveRecordsToNewGroupByName: params are illegal")
            return
        }
        val groupInfo = GroupInfoDbUtil.getGroupInfoByName(mContext, targetGroupName)
        moveRecordsByGroupInfo(recordList, isRecordListFromMedia, groupInfo, listener)
    }

    /**
     * 根据目标分组的id 移动
     * @param recordList 被选中的records
     * @param targetGroupId 目标分组的id
     * @param listener 移动分组的回调
     */
    fun moveRecordsByGroupId(
        recordList: List<Record>?,
        isRecordListFromMedia: Boolean,
        targetGroupId: Int?,
        listener: MoveGroupListener?
    ) {
        if (recordList.isNullOrEmpty() || targetGroupId == null) {
            DebugUtil.e(TAG, "moveRecordsToNewGroupByName: params are illegal")
            return
        }
        val groupInfo = GroupInfoDbUtil.getGroupInfoByGroupId(mContext, targetGroupId)
        moveRecordsByGroupInfo(recordList, isRecordListFromMedia, groupInfo, listener)
    }

    /**
     * 新建分组
     * @param groupInfo 构建的分组信息详情
     * @return RESULT_CODE_FAILED 创建失败
     *         RESULT_CODE_EXISTS 分组名称已存在
     *         RESULT_CODE_SUCCESS 创建成功
     */
    fun createNewGroup(groupInfo: GroupInfo?): Int {
        if (groupInfo == null) {
            return RESULT_CODE_FAILED
        }
        val existsGroupInfo = GroupInfoDbUtil.getGroupInfoByName(mContext, groupInfo.mGroupName)
        return if (existsGroupInfo == null) {
            /*这里要不要标注 groupInfo dirty 以便后续云同步?*/
            groupInfo.mDirty = RECORD_DIRTY_MEGA_ONLY
            val result = GroupInfoDbUtil.insertGroupInfo(mContext, groupInfo)
            if (result) {
                refreshAllGroupInfoList()
                cloudKitApi?.trigBackupNow(mContext)
                RESULT_CODE_SUCCESS
            } else {
                RESULT_CODE_FAILED
            }
        } else {
            RESULT_CODE_EXISTS
        }
    }

    /**
     * 删除分组: 仅针对删除自定义分组时调用，默认分组（通话录音和普通录音）不可编辑
     * @param toDeleteList 选中的要被删除的分组对象
     * @param deleteGroupListener 删除分组时的回调
     */
    fun deleteGroups(toDeleteList: List<GroupInfo>?, deleteGroupListener: DeleteGroupListener?) {
        if (toDeleteList.isNullOrEmpty()) {
            return
        }
        deleteGroupListener?.onPreDeleteGroup()
        CoroutineScope(Dispatchers.IO).launch {
            val deletedList: MutableList<GroupInfo> = ArrayList()
            //先在group_info表把记录删除
            toDeleteList.forEach {
                var bDeleted = false
                if (it.mGroupGlobalId.isNullOrEmpty()) {
                    //mGroupGlobalId为空，并未云同步，直接删除
                    bDeleted = GroupInfoDbUtil.deleteByGroupId(mContext, it.mId)
                } else {
                    it.mIsDeleted = RECORD_DELETED
                    it.mDirty = RECORD_DIRTY_MEGA_ONLY
                    bDeleted = GroupInfoDbUtil.updateGroupInfoDeleteState(mContext, it)
                }

                if (bDeleted) {
                    deletedList.add(it)
                }
            }
            if (toDeleteList.size == deletedList.size && deletedList.size > 0) {
                deleteGroupListener?.onPostDeleteGroup(deletedList)
            }
        }
    }

    /**
     * 删除已云同步的记录
     */
    fun deleteSyncedGroupRecords() {
        val where = "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID} is not null or " +
                "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID} !='' "
        GroupInfoDbUtil.deleteRecords(mContext, where, null)
    }

    /**
     * 清理本地同步状态
     */
    fun clearLocalSyncStatusForLogout() {
        val where = "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID} is not null or " +
                "${DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID} !='' "
        val contentValues = ContentValues()
        val v: String? = null
        contentValues.put(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID, v)
        //contentValues.put(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY)
        val updateCount = mContext.contentResolver.update(
            DatabaseConstant.GroupInfoUri.GROUP_INFO_URI,
            contentValues,
            where,
            null
        )
        DebugUtil.i(TAG, "clearLocalSyncStatusForLogout --> updateCount = $updateCount")
    }

    fun handleRecordsAfterGroupsDeleted(activity: Activity, deletedGroups: List<GroupInfo>, fileDeleteListener: OnFileDeleteListener?) {
        if (deletedGroups.isEmpty()) {
            return
        }
        DebugUtil.d(TAG, "handleRecordsAfterGroupsDeleted")
        val idsList = ArrayList<String>()
        deletedGroups.forEach {
            idsList.add(it.mId.toString())
        }
        val recordList = RecorderDBUtil.getInstance(mContext).getRecordsByGroupIds(idsList.toTypedArray())
        if (!recordList.isNullOrEmpty()) {
            recordList.forEach { record ->
                //恢复到默认分组：通话录音
                if (record.recordType == RecordModeConstant.RECORD_TYPE_CALL) {
                    if (mDefaultGroupInfoCalling == null) {
                        mDefaultGroupInfoCalling = GroupInfoDbUtil.getCallingGroupItemInfo(mContext)
                    }
                    mDefaultGroupInfoCalling?.let {
                        record.groupId = it.mId
                        record.groupUuid = it.mUuId
                    }
                } else {
                    //恢复到默认分组：普通录音
                    if (mDefaultGroupInfoCommon == null) {
                        mDefaultGroupInfoCommon = GroupInfoDbUtil.getCommonGroupItemInfo(mContext)
                    }
                    mDefaultGroupInfoCommon?.let {
                        record.groupId = it.mId
                        record.groupUuid = it.mUuId
                    }
                }
                record.dirty = RECORD_DIRTY_MEGA_ONLY
            }
            if (mDeleteFileDialogUtil == null) {
                mDeleteFileDialogUtil = DeleteFileDialogUtil(fileDeleteListener)
            }
            /*标记 因为删除了录音分组，所以录音记录表需要更新原始分组信息*/
            FileDealUtil.mDeleteRecordsByGroupDeleted = true
            mDeleteFileDialogUtil?.delete(activity, recordList, false, false)
        } else {
            DebugUtil.d(TAG, "handleRecordsAfterGroupsDeleted: no records need to delete, notify refresh group list")
            refreshAllGroupInfoList()
            fileDeleteListener?.onDeleteFileResult(true)
        }
    }

    /**
     * 分组信息更新
     * @param groupInfo 构建的分组对象，包含了要更新的字段内容
     * @return RESULT_CODE_FAILED 更新失败
     *         RESULT_CODE_SUCCESS 更新成功
     */
    fun updateGroup(groupInfo: GroupInfo?): Int {
        if (groupInfo == null) {
            return RESULT_CODE_FAILED
        }
        val existsGroupInfo = GroupInfoDbUtil.getGroupInfoByName(mContext, groupInfo.mGroupName)
        /*这里要不要标注 groupInfo dirty 以便后续云同步?*/
        return if (existsGroupInfo != null) {
            val bUpdated = GroupInfoDbUtil.updateGroupInfo(mContext, groupInfo)
            if (bUpdated) RESULT_CODE_SUCCESS else RESULT_CODE_FAILED
        } else {
            RESULT_CODE_FAILED
        }
    }

    /**
     * 更新分组的名称和颜色
     * @param groupInfo 要操作的分组对象
     * @param newGroupName 新的分组名称
     * @param newGroupColor 新的分组颜色
     * @return RESULT_CODE_FAILED 更新失败
     *         RESULT_CODE_EXISTS 分组名称已存在
     *         RESULT_CODE_SUCCESS 更新成功
     */
    fun updateGroupNameAndColor(groupInfo: GroupInfo?, newGroupName: String?, newGroupColor: Int?): Int {
        if (groupInfo == null || TextUtils.isEmpty(newGroupName)) {
            DebugUtil.e(TAG, "updateGroupNameAndColor: params are illegal")
            return RESULT_CODE_FAILED
        }
        val isSameName = groupInfo.mGroupName == newGroupName
        val isSameColor = groupInfo.mGroupColor == newGroupColor
        //组名和颜色都未改变，直接返回
        if (isSameName && isSameColor) {
            return RESULT_CODE_FAILED
        }

        //检查新名字是否有记录存在
        val existsGroupInfo = if (isSameColor) {
            //组颜色一样，则认为需要更新组名。
            GroupInfoDbUtil.getGroupInfoByName(mContext, newGroupName)
        } else {
            //组颜色不一样，则认为需要更新颜色。查询是否存在该组时，需排除自己。
            GroupInfoDbUtil.getGroupInfoByNameExcludeSelf(groupInfo, mContext, newGroupName)
        }
        if (existsGroupInfo != null) {
            return RESULT_CODE_EXISTS
        }

        if (newGroupName != null && !isSameName) {
            groupInfo.mGroupName = newGroupName
        }
        if (newGroupColor != null && !isSameColor) {
            groupInfo.mGroupColor = newGroupColor
        }
        groupInfo.mDirty = RECORD_DIRTY_MEGA_ONLY
        val bUpdated = GroupInfoDbUtil.updateGroupInfo(mContext, groupInfo)
        return if (bUpdated) {
            refreshAllGroupInfoList()
            cloudKitApi?.trigBackupNow(mContext)
            RESULT_CODE_SUCCESS
        } else {
            RESULT_CODE_FAILED
        }
    }

    /**
     * 类别拖拽后重新排序
     * @param sortedList 已在列表排好序的list
     */
    fun updateGroupSort(sortedList: MutableList<GroupInfo>): Boolean {
        val bSuccess = GroupInfoDbUtil.updateGroupInfoSort(mContext, sortedList)
        DebugUtil.d(TAG, "updateGroupSort: $bSuccess")
        if (bSuccess) {
            refreshAllGroupInfoList()
        }
        return bSuccess
    }

    /**
     * 获取分组下的所有records记录
     * @param groupInfo : null 查询所有录音记录
     */
    fun getRecordsByGroupInfo(groupInfo: GroupInfo? = null): List<Record> {
        return RecorderDBUtil.getInstance(mContext).getRecordsByGroupInfo(groupInfo)
    }

    /**
     * 获取本地数据库所有records记录
     */
    fun getAllRecordsFromLocalDb(): List<Record> {
        return RecorderDBUtil.getInstance(mContext).getRecordsByGroupInfo(null)
    }

    /**
     * 获取分组下的所有records记录
     * @param groupName : 指定分组的名称
     */
    fun getRecordsByGroupName(groupName: String): List<Record>? {
        if (TextUtils.isEmpty(groupName)) {
            return null
        }
        val existsGroupInfo = GroupInfoDbUtil.getGroupInfoByName(mContext, groupName)
        return getRecordsByGroupInfo(existsGroupInfo)
    }

    /**
     * 获取未上传的分组元数据
     */
    fun getDirtyDataForUnCommitMetadata(): MutableList<GroupInfo> {
        return GroupInfoDbUtil.getDirtyDataForUnCommitMetadata(mContext)
    }

    /**
     * 获取已上传但是本地又有改变的分组元数据
     */
    fun getDirtyDataForChangedMetadata(): MutableList<GroupInfo> {
        return GroupInfoDbUtil.getDirtyDataForChangedMetadata(mContext)
    }

    fun getDefaultGroupItem(defaultType: Int): GroupInfo? {
        when (defaultType) {
            GroupInfo.INT_DEFAULT_ALL -> {
                if (mDefaultGroupInfoAll == null) {
                    mDefaultGroupInfoAll = GroupInfoDbUtil.getAllGroupItemInfo(mContext)
                }
                return mDefaultGroupInfoAll
            }
            GroupInfo.INT_DEFAULT_CALLING -> {
                if (mDefaultGroupInfoCalling == null) {
                    mDefaultGroupInfoCalling = GroupInfoDbUtil.getCallingGroupItemInfo(mContext)
                }
                return mDefaultGroupInfoCalling
            }
            GroupInfo.INT_DEFAULT_COMMON -> {
                if (mDefaultGroupInfoCommon == null) {
                    mDefaultGroupInfoCommon = GroupInfoDbUtil.getCommonGroupItemInfo(mContext)
                }
                return mDefaultGroupInfoCommon
            }
            GroupInfo.INT_DEFAULT_RECENTLY_DELETED -> {
                if (mDefaultGroupInfoDeleted == null) {
                    mDefaultGroupInfoDeleted = GroupInfoDbUtil.getDeletedGroupItemInfo(mContext)
                }
                return mDefaultGroupInfoDeleted
            }
        }
      return null
    }

    /**
     * 批量插入GroupInfo
     */
    fun batchInsertGroupInfo(list: MutableList<GroupInfo>): Int {
      return GroupInfoDbUtil.batchInsertGroupInfo(mContext, list)
    }

    fun deleteByGroupName(groupName: String?): Boolean {
      return GroupInfoDbUtil.deleteByGroupName(mContext, groupName)
    }

    fun deleteByUUId(uuid: String?): Boolean {
      return GroupInfoDbUtil.deleteByUUId(mContext, uuid)
    }

    fun deleteByGroupId(groupId: Int): Boolean {
        return GroupInfoDbUtil.deleteByGroupId(mContext, groupId)
    }

    fun updateGroupInfoDeleteState(groupInfo: GroupInfo): Boolean {
        return GroupInfoDbUtil.updateGroupInfoDeleteState(mContext, groupInfo)
    }

    fun updateGroupInfo(groupInfo: GroupInfo): Boolean {
       return GroupInfoDbUtil.updateGroupInfo(mContext, groupInfo)
    }

    fun updateGroupRecordsCount(context: Context?, groupInfoList: List<GroupInfo>): List<GroupInfo>? {
        return GroupInfoDbUtil.updateGroupRecordsCount(mContext, groupInfoList)
    }

    fun getGroupInfoByGroupId(groupId: Int): GroupInfo? {
        return GroupInfoDbUtil.getGroupInfoByGroupId(mContext, groupId)
    }
    fun getGroupInfoByUuid(uuid: String?): GroupInfo? {
        return GroupInfoDbUtil.getGroupInfoByUuid(mContext, uuid)
    }

    fun getGroupInfoByGlobalId(globalId: String?): GroupInfo? {
        return GroupInfoDbUtil.getGroupInfoByGlobalId(mContext, globalId)
    }

    fun getGroupInfoByName(context: Context?, groupName: String?): GroupInfo? {
        return GroupInfoDbUtil.getGroupInfoByName(mContext, groupName)
    }

    /**
     * 云同步后，检查录音表的分组信息，
     * 以防账号切换或者其他异常情况导致表的分组UUID与分组表里的UUID不对应
     * 备用？？
     */
    fun checkAndResetRecordsGroupInfoAfterSync() {
        DebugUtil.e(TAG, "checkAndResetRecordsGroupInfoAfterSync start")
        CoroutineScope(Dispatchers.IO).launch {
            //录音表的所有记录
            val allRecordsList = getAllRecordsFromLocalDb()
            //分组表的所有记录
            val allGroupInfoList = GroupInfoDbUtil.getAllGroupInfoList(mContext)
            if (allRecordsList.isEmpty() || allGroupInfoList.isEmpty()) {
                return@launch
            }
            kotlin.runCatching {
                DebugUtil.e(TAG, "checkAndResetRecordsGroupInfoAfterSync do_start:" +
                        " records size = ${allRecordsList.size}, group size = ${allGroupInfoList.size}")
                var bShouldRefresh = false
                allRecordsList.forEach {
                    //判断分组是否有效，否则初始其分组信息
                    val bValidRecord = isRecordGroupInfoValid(allGroupInfoList, it)
                    if (!bValidRecord) {
                        val recordType = getRecordTypeForMediaRecord(it)
                        var defaultGroupType = GroupInfo.INT_DEFAULT_COMMON
                        if (recordType == RecordModeConstant.RECORD_TYPE_CALL) {
                            defaultGroupType = GroupInfo.INT_DEFAULT_CALLING
                        }
                        val groupInfo = getDefaultGroupItem(defaultGroupType)
                        if (groupInfo != null) {
                            it.groupId = groupInfo.mId
                            it.groupUuid = groupInfo.mUuId
                            it.dirty = RECORD_DIRTY_MEGA_ONLY
                        }

                        RecorderDBUtil.updateRecordData(
                            mContext,
                            it.convertToContentValues(),
                            DatabaseConstant.RecorderColumn.COLUMN_NAME_ID + "=?",
                            arrayOf<String>(it.id.toString())
                        )
                        bShouldRefresh = true
                    }
                }
                if (bShouldRefresh) {
                    refreshAllGroupInfoAfterDataChanged()
                }
                DebugUtil.e(TAG, "checkAndResetRecordsGroupInfoAfterSync " +
                        "do_end: bShouldRefresh = $bShouldRefresh")
            }.onFailure {
                DebugUtil.e(TAG, "checkAndResetRecordsGroupInfoAfterSync error: ", it)
            }
        }
    }

    private fun isRecordGroupInfoValid(validGroupInfoList: List<GroupInfo>, record: Record): Boolean {
        validGroupInfoList.forEach {
            if (!TextUtils.isEmpty(record.groupUuid) && it.mUuId == record.groupUuid) {
                return true
            }
        }
        return false
    }


    /**
     * 本地db已同步到云端的count
     *
     * @return
     */
    fun getLocalGroupInfoCloudDataCount(): Int {
        return GroupInfoDbUtil.getLocalGroupInfoCloudDataCount(mContext)
    }
    fun getLocalGroupInfoInDbByCloudGroupInfo(cloudGroupInfo: GroupInfo?): GroupInfo? {
       return GroupInfoDbUtil.getLocalGroupInfoInDbByCloudGroupInfo(mContext, cloudGroupInfo)
    }

    /**
     * 从group_info表里获取所有分组列表
     */
    fun getAllGroupInfoList(): MutableList<GroupInfo> {
        return GroupInfoDbUtil.getAllGroupInfoList(mContext)
    }

    /**
     * 校准分组统计的文件数， 避免录音库和媒体库数据不一致时，文件数目显示不一致
     * 复现场景：清除应用数据后启动 或者 启动后通过文件管理器移入大量文件时。
     * 该方法再录音列表数据填充后调用
     */
    fun verifyGroupCount() {
        if (checkJob?.isCompleted == false) {
            DebugUtil.e(TAG, "verifyGroupCount: checkJob is Running")
            return
        }
        checkJob = CoroutineScope(Dispatchers.IO).launch {
            DebugUtil.e(TAG, "verifyGroupCount: refreshAllGroupInfoAfterDataChanged")
            GroupInfoDbUtil.setStopCalculateGroupCount(false)
            refreshAllGroupInfoAfterDataChanged(true)
        }
    }

    fun stopVerifyGroupCount() {
        GroupInfoDbUtil.setStopCalculateGroupCount(true)
    }

    fun queryAllCustomGroupInfoList() {
        mAllCustomGroupInfoList = GroupInfoDbUtil.getCustomGroupsFromDB(mContext)
    }

    fun checkGroupExistByUUID(groupUUID: String): Boolean {
        mAllCustomGroupInfoList.forEach { groupInfo ->
            if (groupInfo.mUuId == groupUUID) {
                return true
            }
        }
        return false
    }


    interface MoveGroupListener {
        fun onPreMoveGroup(showWaitingDialog: Boolean = false)
        fun onMovingGroup(currentIndex: Int, totalCount: Int)
        fun onPostMoveGroup(movedState: Int, movedRecordList: ArrayList<Record>? = null, showWaitingDialog: Boolean = false)
    }

    interface DeleteGroupListener {
        fun onPreDeleteGroup()
        fun onPostDeleteGroup(deletedGroupInfoList: List<GroupInfo>)
    }
}