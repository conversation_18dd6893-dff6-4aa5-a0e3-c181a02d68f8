/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/9/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.utils

import android.os.Looper
import androidx.annotation.MainThread
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.soundrecorder.base.utils.DebugUtil
import kotlinx.coroutines.*

object CoroutineUtils {
    val mainScope by lazy { MainScope() }

    private const val TAG = "CoroutineUtils"
    private const val TIMEOUT_CHECK = 500L

    /**
     * The method is used to invoke the java file interface in main thread for avoid ANR
     *
     * @param method the checked method
     * @param default the default value when checking timeout, you should set it depend on your logic
     */
    @MainThread
    fun <T> safeCheck(method: () -> T, default: T): T {
        DebugUtil.d(TAG, "safeCheck start")
        return try {
            runBlocking {
                withTimeoutOrNull(TIMEOUT_CHECK) {
                    // Don't use witContext(Dispatchers.IO), maybe cannot cancel when timeout
                    async(Dispatchers.IO) {
                        method.invoke()
                    }.await()
                }.let {
                    if (it == null) {
                        DebugUtil.d(TAG, "safeCheck timeout: default=$default")
                        default
                    } else it
                }
            }
        } catch (e: Exception) {
            DebugUtil.d(TAG, "safeCheck failed: default=$default, ${e.message}")
            default
        }
    }

    fun <T> doInIOThread(conditionAction: () -> T, coroutineScope: CoroutineScope) {
        ioToMain(conditionAction, null, coroutineScope)
    }

    fun <T> ioToMain(conditionAction: () -> T, resultAction: ((T) -> Unit)?) {
        ioToMain(conditionAction, resultAction, CoroutineScope(Dispatchers.Main))
    }

    fun <T> ioToMain(
        conditionAction: () -> T,
        resultAction: ((T) -> Unit)?,
        coroutineScope: CoroutineScope
    ) {
        DebugUtil.d(TAG, "ioToMain start")
        coroutineScope.launch {
            val result = async(Dispatchers.IO) {
                conditionAction.invoke()
            }
            resultAction?.invoke(result.await())
        }
    }

    fun doInMain(function: (() -> Unit)) {
        if (Looper.getMainLooper() != Looper.myLooper()) {
            MainScope().launch {
                function.invoke()
            }
        } else {
            function.invoke()
        }
    }

    fun delayInIoDoInMain(
        delayDuration: Long,
        coroutineScope: CoroutineScope,
        funDoInMain: () -> Unit
    ) {
        DebugUtil.d(TAG, "delayInIoDoInMain start")
        coroutineScope.launch {
            withContext(Dispatchers.IO) {
                delay(delayDuration)
            }
            withContext(Dispatchers.Main) {
                funDoInMain.invoke()
            }
        }
    }

    @JvmStatic
    fun delayInMain(activity: AppCompatActivity, delayDuration: Long, function: () -> Unit) {
        activity.lifecycleScope.launch {
            delay(delayDuration)
            function.invoke()
        }
    }
}
