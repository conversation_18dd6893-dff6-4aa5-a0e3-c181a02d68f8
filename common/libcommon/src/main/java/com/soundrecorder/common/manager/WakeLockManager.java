package com.soundrecorder.common.manager;

import android.content.Context;
import android.os.PowerManager;
import android.text.TextUtils;
import com.soundrecorder.base.utils.DebugUtil;

public class WakeLockManager {

    private static final String TAG = "WakeLockManager";
    private static final String WAKE_NAME = "RecorderService";

    private volatile static WakeLockManager sInstance = null;
    private PowerManager.WakeLock mWakeLock = null;
    private static final long WAKE_LOCK_TIMEOUT = 60L * 1000;

    public static WakeLockManager getInstance(Context context, String tagName) {
        if (getsInstance() == null) {
            synchronized (WakeLockManager.class) {
                if (getsInstance() == null) {
                    if (TextUtils.isEmpty(tagName)) {
                        setsInstance(new WakeLockManager(context, WAKE_NAME));
                    } else {
                        setsInstance(new WakeLockManager(context, tagName));
                    }
                }
            }
        }
        return getsInstance();
    }

    public static void setsInstance(WakeLockManager sInstance) {
        WakeLockManager.sInstance = sInstance;
    }

    public static WakeLockManager getsInstance() {
        return sInstance;
    }

    private WakeLockManager(Context context, String tagName) {
        PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        mWakeLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK | PowerManager.ON_AFTER_RELEASE,
                tagName);
        mWakeLock.setReferenceCounted(false);
    }

    public void acquireWakeLock(int maxLimitDuration) {
        try {
            if (mWakeLock != null) {
                if (maxLimitDuration > 0) {
                    mWakeLock.acquire(maxLimitDuration);
                } else {
                    mWakeLock.acquire();
                }
                DebugUtil.i(TAG, "wakelock aquired " + mWakeLock + ", timeout = " + maxLimitDuration);
            }
        } catch (Exception e) {
            DebugUtil.i(TAG, e.getMessage());
        }
    }

    public void releaseWakeLock() {
        try {
            if (mWakeLock != null) {
                mWakeLock.release();
                DebugUtil.i(TAG, "wakelock released " + mWakeLock);
            }
        } catch (Exception e) {
            DebugUtil.i(TAG, e.getMessage());
        }
    }

    public void reset() {
        releaseWakeLock();
        mWakeLock = null;

        sInstance = null;
    }
}
