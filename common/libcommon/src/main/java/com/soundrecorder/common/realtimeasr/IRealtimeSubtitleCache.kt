/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: IRealtimeSubtitleCache
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: W9085798
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9085798 2025/5/18 1.0 create
 */
package com.soundrecorder.common.realtimeasr

import com.soundrecorder.common.databean.ConvertContentItem

interface IRealtimeSubtitleCache {
    /**
     * 获取已完成转写的字幕列表记录，用于界面上列表展示及存储为转文本
     */
    fun getGeneratedSubtitles(): List<ConvertContentItem>

    /**
     * 获取转写过程中的字幕，作为最新的识别中的字幕逐字上屏展示
     */
    fun getTemporySubtitles(): List<ConvertContentItem>
}