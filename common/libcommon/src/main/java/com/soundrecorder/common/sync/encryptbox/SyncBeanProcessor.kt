/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  SyncBeanProcessor.kt
 * * Description : SyncBeanProcessor
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.common.sync.encryptbox

import android.os.Environment
import android.text.TextUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil.processEncryptAudioFile
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.sync.db.RecordDataSync
import com.soundrecorder.common.utils.RecordModeUtil.containRecordRelativePath
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.SYNC_TYPE_BACKUP
import com.soundrecorder.modulerouter.utils.Injector
import java.io.File

class SyncBeanProcessor {

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    fun processIncomingSyncBean(inputBean: SyncBean?) {
        if (inputBean == null) {
            DebugUtil.e(TAG, "processIncomingSyncBean: inputBean null, return")
            return
        }
        val syncType = inputBean.syncType
        when (syncType) {
            EncryptBoxConstant.TYPE_ENCRYPTION -> doProcessInputEncrypt(inputBean)
            EncryptBoxConstant.TYPE_DECRYPTION -> doProcessInputDecrypt(inputBean)
            EncryptBoxConstant.TYPE_DELETE -> doProcessInputDelete(inputBean)
            EncryptBoxConstant.TYPE_DATA_LARGE -> doProcessInputLargeData()
            else -> doOtherTypeMsg(inputBean)
        }
    }


    private fun doProcessInputEncrypt(inputSyncBean: SyncBean) {
        DebugUtil.i(TAG, "doProcessInputEncrypt: $inputSyncBean")
        val syncFileInfoList = inputSyncBean.fileInfoList
        var processSuccess = false
        if (syncFileInfoList != null && syncFileInfoList.size > 0) {
            for (syncFileInfo in syncFileInfoList) {
                val displayName = syncFileInfo.displayName
                val relativePath = syncFileInfo.relativePath
                val mD5 = syncFileInfo.md5
                processSuccess = processSuccess or processEncryptAudioFile(displayName, relativePath, mD5)
            }
            if (processSuccess) {
                cloudKitApi?.trigBackupNow(BaseApplication.getAppContext())
                DebugUtil.i(TAG, "doProcessInputEncrypt: triBackUpNow ")
            }
        }
    }

    private fun doProcessInputDecrypt(inputSyncBean: SyncBean) {
        DebugUtil.i(TAG, "doProcessInputDecrypt: $inputSyncBean")
        val syncFileInfoList = inputSyncBean.fileInfoList
        val targetPath = inputSyncBean.targetPath
        var processSuccess = false
        if (TextUtils.isEmpty(targetPath) || (!containRecordRelativePath(targetPath))) {
            DebugUtil.i(TAG, "doProcessInputDecrypt, input targetName: " + FileUtils.getDisplayNameByPath(targetPath) + ", not qualified, return")
            return
        }
        if (syncFileInfoList != null && syncFileInfoList.size > 0) {
            for (syncFileInfo in syncFileInfoList) {
                val displayName = syncFileInfo.displayName
                val relativePath = syncFileInfo.relativePath
                val mD5 = syncFileInfo.md5
                processSuccess = processSuccess or RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                    .processDecrypAudioFile(displayName, relativePath, mD5)
            }
            if (processSuccess) {
                cloudKitApi?.trigBackupNow(BaseApplication.getAppContext())
                DebugUtil.i(TAG, "doProcessInputDecrypt: trigBackupNow")
            }
        }
    }

    private fun doProcessInputDelete(inputSyncBean: SyncBean) {
        DebugUtil.i(TAG, "doProcessInputDelete: $inputSyncBean")
        val syncFileInfoList = inputSyncBean.fileInfoList
        var processSuccess = false
        val recorderRelativePath = Environment.DIRECTORY_MUSIC + File.separator + Constants.RECORDINGS
        if (syncFileInfoList != null && syncFileInfoList.size > 0) {
            for (syncFileInfo in syncFileInfoList) {
                val displayName = syncFileInfo.displayName
                val relativePath = syncFileInfo.relativePath
                if (TextUtils.isEmpty(relativePath) || !relativePath.contains(recorderRelativePath)) {
                    continue
                }
                val mD5 = syncFileInfo.md5
                processSuccess = processSuccess or RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                    .processEncrypDeleteAudioFile(displayName, relativePath, mD5)
            }
        }
        DebugUtil.i(TAG, "doProcessInputDelete: success: $processSuccess")
    }

    private fun doProcessInputLargeData() {
        //trigLargeData, need to just this is the last broadcast
        RecordDataSync.getInstance().syncAllRecordDataFromMedia(
            BaseApplication.getAppContext(),
            false,
            true,
            SYNC_TYPE_BACKUP
        )
    }


    private fun doOtherTypeMsg(inputSyncBean: SyncBean) {
        // show toast to notifyUsers
        val msg = inputSyncBean.message
        if (!TextUtils.isEmpty(msg)) {
            ToastManager.showShortToast(BaseApplication.getAppContext(), msg)
        }
    }

    companion object {
        private const val TAG = "SyncBeanProcessor"
    }
}
