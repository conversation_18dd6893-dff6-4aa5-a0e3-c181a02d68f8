/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : UriContentObserver.kt
 * Version Number: 1.0
 * Description   :
 * Author        : chenlipeng
 * Date          : 2020-05-29
 * History       :(ID,  2020-05-29, chenlipeng, Description)
 ************************************************************/
package com.soundrecorder.common.fileobserve

import android.database.ContentObserver
import android.net.Uri
import android.os.Handler
import androidx.annotation.NonNull
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil

class UriContentObserver(handler: <PERSON><PERSON>?) : ContentObserver(handler) {
    companion object {
        private const val TAG = "UriContentObserver"
    }
    private val mContext = BaseApplication.getAppContext()
    private var mUriChangeObserver: UriChangeObserver? = null

    fun registerObserver(@NonNull uri: Uri) {
        DebugUtil.i(TAG, "registerObserver the uri is $uri")
        mContext.contentResolver.registerContentObserver(uri, false, this)
    }

    fun unRegisterObserver() {
        mContext.contentResolver.unregisterContentObserver(this)
    }

    override fun onChange(selfChange: Boolean, uri: Uri?) {
        super.onChange(selfChange, uri)
        if (uri != null) {
            mUriChangeObserver?.uriChange(uri)
        }
    }

    fun setUriChange(uriChange: UriChangeObserver?) {
        mUriChangeObserver = uriChange
    }

    interface UriChangeObserver {
        fun uriChange(uri: Uri?)
    }
}