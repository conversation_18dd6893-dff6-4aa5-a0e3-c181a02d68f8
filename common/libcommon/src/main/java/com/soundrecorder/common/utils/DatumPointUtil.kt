/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DatumPointUtils.kt
 ** Description : DatumPointUtils.kt
 ** Version     : 1.0
 ** Date        : 2025/07/07
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/07/07      1.0      create
 ***********************************************************************/

package com.soundrecorder.common.utils

import kotlin.math.max
import kotlin.math.min

object DatumPointUtil {

    private const val FLOAT_ACCURACY = 0.00001f

    /**
     * 计算文本的的基准点（标记插入、高亮范围）
     *
     * 根据文本内容、ASR文本的开始和结束时间偏移量以及当前播放时间偏移量，计算基准点的位置。
     *
     * @param asrContent ASR文本内容
     * @param asrStartTimeOffset ASR文本的开始时间偏移量（毫秒）
     * @param asrEndTimeOffset ASR文本的结束时间偏移量（毫秒）
     * @param playbackTimeOffset 播放时间的偏移量（毫秒）
     * @return ASR文本中，基准点的位置，如果无法计算返回-1
     */
    @JvmStatic
    fun calculateMarkFlagInsertPosition(
        asrContent: String,
        asrStartTimeOffset: Long,
        asrEndTimeOffset: Long,
        playbackTimeOffset: Long
    ): Int {
        if (asrEndTimeOffset <= asrStartTimeOffset) {
            return -1
        }
        if (playbackTimeOffset <= asrStartTimeOffset) {
            return 0
        }
        if (playbackTimeOffset >= asrEndTimeOffset) {
            return asrContent.length
        }
        val asrDuration = (asrEndTimeOffset - asrStartTimeOffset).toFloat()
        if (asrDuration == 0f) {
            return  -1
        }
        val playbackTimePos = (playbackTimeOffset - asrStartTimeOffset).toFloat()

        val pos = (playbackTimePos * asrContent.length / asrDuration + FLOAT_ACCURACY).toInt()
        return max(0, min(pos, asrContent.length))
    }

    /**
     * 根据文本索引位置计算对应的播放时间点
     * @param asrText ASR文本内容
     * @param asrStartTime ASR文本起始时间戳
     * @param asrEndTime ASR文本结束时间戳
     * @param baseIndex 基准点（文本索引位置）
     * @return 对应的播放时间点
     */
    @JvmStatic
    fun calculatePlayTimeFromIndex(
        asrText: String,
        asrStartTime: Long,
        asrEndTime: Long,
        baseIndex: Int
    ): Long {
        if (asrText.isEmpty() || asrEndTime <= asrStartTime || baseIndex == 0) {
            return asrStartTime
        }

        // 确保索引在有效范围内
        val validIndex = baseIndex.coerceIn(0, asrText.length - 1)

        // 直接按比例计算时间点
        val progressRatio = validIndex.toDouble() / asrText.length.toDouble()
        val totalTime = asrEndTime - asrStartTime
        return asrStartTime + (totalTime * progressRatio).toLong()
    }
}