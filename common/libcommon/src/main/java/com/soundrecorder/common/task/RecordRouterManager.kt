/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  RecordRouterManager.kt
 * * Description : RecordRouterManager
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.common.task

import android.app.ActivityManager
import android.app.ActivityManager.RunningTaskInfo
import android.content.Context
import android.provider.Settings
import android.text.TextUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.recorder.RecordInterface
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.modulerouter.utils.Injector
import kotlin.concurrent.Volatile

class RecordRouterManager private constructor() {
    /**
     * 录制界面从哪儿启动的
     */
    private var recordFrom = ""

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val recordApi by lazy {
        Injector.injectFactory<RecordInterface>()
    }

    fun setRecordFrom(from: String) {
        this.recordFrom = from
    }

    fun resetRecordFrom() {
        this.recordFrom = ""
    }

    /**
     * 根据正在录音的启动来源和当前的启动来源，来判断是否拦截启动录制界面
     *
     * 拦截、不拦截的场景：
     * 1.之前没有启动录制，点击录制按钮时，则不需要拦截
     * 2.上一次是通话启动录制，这次是从侧边栏，桌面进入录制，需要进行拦截
     * 3.上一次是通话启动录制，这次也是从通话启动录制，不需要拦截（实测该场景第二次启动通话时，上一次的录制销毁）
     * 4.上一次是从侧边栏启动录制，这次从桌面进入，不需要拦截，直接进入录制界面
     * 5.上一次是从侧边栏启动录制，没有进入过录制界面，这次从通话进入，点击录制按钮时，需要进行拦截
     * 6.上一次是从侧边栏启动录制，点击状态栏录制图标，进入过录制界面，这次从通话进入，点击录制按钮时，需要进行拦截
     * 7.上一次是从桌面启动录制，这次从通话进入，点击录制按钮时，需要进行拦截
     * 8.上一次是从桌面启动录制，这次从侧边栏启动，不需要拦截，直接进入录制界面
     *
     * 综上，不拦截的情况：
     * 1.之前没有启动过录制
     * 2.上一次是侧边栏或桌面启动录制，这次也是侧边栏或桌面启动录制
     * 3.上一次通话启动录制，这次也是通话启动录制（该场景实测不存在）
     *
     * @param comeFrom 启动来源(可空)
     * @return
     */
    fun interceptStartRecord(comeFrom: String): Boolean {
        if (TextUtils.isEmpty(this.recordFrom)) { // 没有录制,则不进行拦截
            return false
        }
        DebugUtil.d(TAG, "interceptStartRecord recordFrom " + this.recordFrom)
        return if (RecorderDataConstant.PAGE_FROM_CALL == comeFrom) {
            //从通话启动，则判断之前录制来源是否是通话，如果不是，则进行拦截
            !TextUtils.equals(this.recordFrom, RecorderDataConstant.PAGE_FROM_CALL)
        } else { //从侧边栏或者小布或者桌面启动，则判断录制来源是否是通话，如果是通话，则进行拦截
            TextUtils.equals(this.recordFrom, RecorderDataConstant.PAGE_FROM_CALL)
        }
    }

    /**
     * 超级省电模式是否打开
     *
     * @param context
     * @return   false: 0，关闭   true: 1，打开
     */
    fun isSuperPowerSaveModeState(context: Context): Boolean {
        return Settings.System.getInt(
            context.contentResolver,
            SUPER_POWERSAVE_MODE_STATE, SUPER_POWERSAVE_MODE_CLOSE
        ) == SUPER_POWERSAVE_MODE_OPEN
    }

    /**
     * The activity at the bottom of the stack determines whether the desktop or sidebar start record
     */
    fun getBaseActivityIsSelf(context: Context): Boolean {
        val manager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        var runningTaskInfos: List<RunningTaskInfo>? = null
        runningTaskInfos = manager.getRunningTasks(1)

        if ((runningTaskInfos != null) && (runningTaskInfos.size != 0)) {
            val baseActivity = (runningTaskInfos[0].baseActivity).toString()
            DebugUtil.d(TAG, "baseActivity is $baseActivity")
            val browseFileClass = browseFileApi?.getBrowseFileClass()
            val recorderClass: Class<*>? = recordApi?.getRecorderActivityClass()
            return (browseFileClass != null && baseActivity.contains(browseFileClass.name))
                    || (recorderClass != null && baseActivity.contains(recorderClass.name))
        } else {
            return false
        }
    }

    companion object {
        const val TAG: String = "RecordRouterManager"
        const val PAGE_FROM_NAME: String = "from"
        const val PAGE_FROM_LAUNCHER: String = "launcher"
        const val PAGE_FROM_SLIDE_BAR: String = "slideBar"
        const val PAGE_FROM_BREENO: String = "breeno"
        const val PAGE_FROM_SMALL_CARD: String = "smallCard"

        const val PAGE_FROM_CUBE_BUTTON: String = "cubeButton"
        const val PAGE_FROM_BRACKET_SPACE: String = "bracketSpace"
        const val PAGE_FROM_DRAGON_FLY: String = "dragonFly"

        const val PAGE_FROM_LOCK_SCREEN: String = "lockScreen"

        const val PAGE_FROM_SEEDLING_CARD: String = "seedlingCard"

        /**
         * 录音摘要卡
         */
        const val PAGE_FROM_SUMMARY_CARD: String = "summaryCard"
        private const val SUPER_POWERSAVE_MODE_STATE = "super_powersave_mode_state"
        private const val SUPER_POWERSAVE_MODE_CLOSE = 0
        private const val SUPER_POWERSAVE_MODE_OPEN = 1


        @Volatile
        private var sInstance: RecordRouterManager? = null

        @JvmStatic
        val instance: RecordRouterManager?
            get() {
                if (sInstance == null) {
                    synchronized(RecordRouterManager::class.java) {
                        if (sInstance == null) {
                            sInstance = RecordRouterManager()
                        }
                    }
                }
                return sInstance
            }
    }
}
