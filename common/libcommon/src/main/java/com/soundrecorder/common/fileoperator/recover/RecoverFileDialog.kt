/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecoverFileDialog
 * Description:
 * Version: 1.0
 * Date: 2024/10/20
 * Author: W9035969(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9035969 2024/10/20 1.0 create
 */

package com.soundrecorder.common.fileoperator.recover

import android.app.Activity
import android.content.DialogInterface
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.utils.ViewUtils

class RecoverFileDialog(val mActivity: Activity, private val title: String, private val buttonMessage: String) {

    private var mLogTag = "RecoverFileDialog"
    private var mRecoverDialog: AlertDialog? = null
    private var mRecoverFileDialogUtil: RecoverFileDialogUtil? = null
    private var mBeRecoverRecordList: MutableList<Record>? = null
    var mOnFileRecoverListener: OnRecoverFileListener? = null
    var mHideListener: DialogInterface.OnDismissListener? = null

    fun showRecoverDialog(recoverRecord: Record) {
        showRecoverDialog(listOf(recoverRecord), false)
    }

    fun showRecoverDialog(recoverRecords: List<Record>, isDeleteAll: Boolean = false) {
        if (mRecoverDialog?.isShowing() == true) {
            return
        }
        mBeRecoverRecordList = mutableListOf()
        mBeRecoverRecordList?.addAll(recoverRecords)

        mRecoverDialog = COUIAlertDialogBuilder(mActivity, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setPositiveButton(buttonMessage
            ) { _, _ -> recoverRecord(recoverRecords, isDeleteAll) }
            .setBlurBackgroundDrawable(true)
            .setTitle(title)
//            .setMessage(message)
            .setNegativeButton(R.string.cancel, null)
            .setCancelable(true)
            .setOnDismissListener { dialog -> mHideListener?.onDismiss(dialog) }
            .show()
        ViewUtils.updateWindowLayoutParams(mRecoverDialog?.window)
    }

    fun recoverWithPermission(mActivity: Activity, selectedRecordList: ArrayList<Record>, deleteHasPermission: Boolean) {
        if (mRecoverFileDialogUtil == null) {
            mRecoverFileDialogUtil = RecoverFileDialogUtil(mOnFileRecoverListener)
        }
        mRecoverFileDialogUtil?.recoverWithPermission(activity = mActivity, selectedRecordList)
    }

    private fun recoverRecord(recoverRecords: List<Record>?, isRecoverAll: Boolean) {
        if (recoverRecords.isNullOrEmpty()) {
            return
        }
        if (mRecoverFileDialogUtil == null) {
            mRecoverFileDialogUtil = RecoverFileDialogUtil(mOnFileRecoverListener)
        }
        mRecoverFileDialogUtil?.recover(mActivity, recoverRecords, isRecoverAll)
    }

    fun release() {
        DebugUtil.e(mLogTag, "release")
        dismiss()
        mBeRecoverRecordList?.clear()
        mBeRecoverRecordList = null
        mRecoverDialog = null
    }

    fun dismiss() {
        mRecoverDialog?.dismiss()
    }

    fun isShowing(): Boolean = mRecoverDialog?.isShowing == true

    fun getOperating(): Boolean = mRecoverFileDialogUtil?.getOperating() ?: false

    fun resetOperating() {
        mRecoverFileDialogUtil?.resetOperating()
    }
}
