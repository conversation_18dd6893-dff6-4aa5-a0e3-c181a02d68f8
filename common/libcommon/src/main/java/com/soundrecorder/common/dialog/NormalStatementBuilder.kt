/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : NormalStatementBuilder.kt
 ** Description : User statement alert dialog builder
 ** Version     : 1.0
 ** Date        : 2025/07/15
 ** Author      : <PERSON><EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/07/15     1.0      create
 ***********************************************************************/
package com.soundrecorder.common.dialog

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.statement.COUILinkMovementMethod
import com.soundrecorder.base.utils.BaseUtil.isAndroidVOrLater
import com.soundrecorder.base.utils.SettingUtil.gestureNavTransparent
import com.soundrecorder.base.utils.StatusBarUtil
import com.soundrecorder.common.R

class NormalStatementBuilder @JvmOverloads constructor(
    context: Context,
    theme: Int = com.support.dialog.R.style.COUIAlertDialog_BottomWarning
) : COUIAlertDialogBuilder(context, theme) {
    private var contentView: View? = null
    private var describeMessage: TextView? = null
    private var detailsLink: TextView? = null

    init {
        createCustomView()
        setView(contentView)
        setBlurBackgroundDrawable(true)
    }

    private fun createCustomView() {
        contentView = LayoutInflater.from(context).inflate(R.layout.dialog_normal_statement_view, null).apply {
            describeMessage = this.findViewById<TextView>(R.id.security_describe_message).apply {
                movementMethod = COUILinkMovementMethod
            }
            detailsLink = this.findViewById<TextView>(R.id.security_details_link).apply {
                movementMethod = COUILinkMovementMethod
            }
        }
    }

    override fun show(): AlertDialog {
        val dialog = create().apply {
            if (isAndroidVOrLater) {
                window?.let {
                    gestureNavTransparent(context, it)
                }
            }
            setOnShowListener {
                // 公共控件弹窗推荐添加，详见bug：ID:7636727 弹窗闪现
                window?.setWindowAnimations(R.style.Animation_COUI_Dialog_NoEnterAnimation)
            }
            StatusBarUtil.setDialogStatusBarTransparentAndBlackFont(context, this, false)
            setCanceledOnTouchOutside(false)
        }
        dialog.show()
        return dialog
    }

    /**
     * 描述信息
     */
    fun setDescribeMessage(text: CharSequence?): NormalStatementBuilder {
        describeMessage?.text = text
        return this
    }

    /**
     * 详情查看
     */
    fun setDetailsLink(text: CharSequence?): NormalStatementBuilder {
        detailsLink?.let {
            it.visibility = View.VISIBLE
            it.text = text
        }
        return this
    }
}