/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : CheckPermissionBeforeOperate.kt
 * Version Number: 1.0
 * Description   :
 * Author        : chenlipeng
 * Date          : 2020-05-29
 * History       :(ID,  2020-05-29, chenlipeng, Description)
 ************************************************************/
package com.soundrecorder.common.fileoperator

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.permission.PermissionDialogUtils
import com.soundrecorder.common.permission.PermissionUtils

class CheckPermissionBeforeOperate(activity: Activity?) {
    companion object {
        private const val TAG = "CheckPermissionBeforeOperate"
    }

    private var mCallback: Callback? = null
    private var mActivity: Activity? = activity
    private var allFileDialog: AlertDialog? = null

    fun checkOpsPermission() {
        if (mActivity == null) {
            DebugUtil.d(TAG, "activity is null so return")
            return
        }
        if (BaseUtil.isAndroidROrLater && !PermissionUtils.hasAllFilePermission()) {
            allFileDialog = PermissionDialogUtils.showPermissionAllFileAccessDialog(
                mActivity!!,
                object : PermissionDialogUtils.PermissionDialogListener {
                    override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
                        if (isOk) {
                            PermissionUtils.goToAppAllFileAccessConfigurePermissions(mActivity!!)
                            mCallback?.onNext()
                        } else {
                            mCallback?.onCancel()
                        }
                    }
                })
        } else {
            mCallback?.onCancel()
        }
    }

    fun setCallback(callback: Callback?) {
        mCallback = callback
    }

    interface Callback {
        fun onNext()
        fun onCancel()
    }

    fun release() {
        dismissAllFileDialog()
        mActivity = null
        mCallback = null
    }

    fun dismissAllFileDialog() {
        allFileDialog?.dismiss()
        allFileDialog = null
    }
}