/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CircleTextImageUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/1/15
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.widget

object CircleTextImageUtil {
    val randomColor: String
        /**
         * Get the random color.
         * @return
         */
        get() {
            val colorList: MutableList<String> = ArrayList()
            colorList.add("#E56E65")
            colorList.add("#E58F45")
            colorList.add("#E5AF2E")
            colorList.add("#4BBD57")
            colorList.add("#39BD91")
            colorList.add("#39AFBD")
            colorList.add("#45ABE5")
            colorList.add("#5C93E5")
            colorList.add("#7E73E5")
            colorList.add("#B873E5")
            return colorList[(Math.random() * colorList.size).toInt()]
        }

    /**
     * Interception of the first string of characters.
     * @param str
     * @return
     */
    @JvmStatic
    fun subFirstCharacter(str: String?): String {
        if (str.isNullOrEmpty()) {
            return ""
        }
        return if (Character.isLetter(str[0])) {
            str[0].uppercaseChar().toString() + ""
        } else {
            str[0].toString() + ""
        }
    }
}