/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.note

import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.net.Uri
import com.soundrecorder.base.utils.DebugUtil
object ExportNoteUtil {

    private const val TAG = "ExportNoteUtil"
    private const val NOTE_INSERT_URI = "content://com.nearme.note/text_note"

    /**
     * 插入便签数据库
     */
    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    fun insertToNote(context: Context, values: ContentValues): Uri? {
        val result = try {
            context.contentResolver?.insert(Uri.parse(NOTE_INSERT_URI), values)
        } catch (e: Exception) {
            DebugUtil.w(TAG, "insertToNote error: $e")
            null
        }

        val isSuccess = result?.getQueryParameter("result")
        DebugUtil.i(TAG, "insert to Note uri is: $result isSuccess: $isSuccess")
        return if (isSuccess?.equals("success") == true) {
            result
        } else {
            val errorMsg = result?.getQueryParameter("message")
            DebugUtil.e(TAG, "insert to Note $errorMsg")
            null
        }
    }

    /**
     * 删除便签
     */
    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    fun deleteNote(context: Context, insertUris: MutableList<Uri>) {
        insertUris.forEach {
            val localId = getLocalId(it)
            if (localId.isEmpty()) {
                return
            }
            try {
                val uri = Uri.parse(NOTE_INSERT_URI)
                    .buildUpon().appendQueryParameter("caller_package", context.packageName)
                    .appendQueryParameter("local_id", localId)
                    .build()
                val rows = context.contentResolver.delete(uri, null, null)
                DebugUtil.i(TAG, "deleteNote rows=$rows uri=$uri")
            } catch (e: Exception) {
                DebugUtil.e(TAG, "deleteNote error: $e")
            }
        }
    }

    /**
     * 获取便签笔记 id
     */
    @JvmStatic
    fun getLocalId(uri: Uri): String {
        var localId = ""
        try {
            localId = uri.getQueryParameter("localId") ?: return localId
        } catch (e: Exception) {
            DebugUtil.e(TAG, "get localId error", e)
        }
        return localId
    }

    /**
     * 授予 Uri 权限
     */
    @JvmStatic
    fun grantUriPermission(context: Context, pkgName: String, uri: Uri) {
        context.grantUriPermission(pkgName, uri, Intent.FLAG_GRANT_READ_URI_PERMISSION)
    }

    /**
     * 插入便签数据库返回 Uri 之后（无论成功 or 失败），回收授予便签的 Uri 权限
     */
    @JvmStatic
    fun revokeUriPermission(context: Context, imgUris: MutableList<Uri>) {
        DebugUtil.i(TAG, "revokeUriPermission,imgUris: $imgUris")
        if (imgUris.isNotEmpty()) {
            imgUris.forEach {
                context.revokeUriPermission(it, Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
        }
    }
}