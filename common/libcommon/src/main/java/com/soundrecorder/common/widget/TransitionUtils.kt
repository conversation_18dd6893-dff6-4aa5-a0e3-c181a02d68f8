/******************************************************************
 * Copyright (C), 2008-2021, OPlus Mobile Comm Corp., Ltd
 * VENDOR_EDIT
 * File: - TransitionUtils.kt
 * Version: 1.0
 * Date : 2021-01-20
 * Author: tianjun
 *
 * ---------------- Revision History: ---------------------------
 * <author>        <data>        <version>        <desc>
 * tianjun     2021-01-20          1.0         build this module
</desc></version></data></author> */
package com.soundrecorder.common.widget

import android.animation.*
import android.graphics.Bitmap
import android.graphics.Canvas
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.FrameLayout
import android.widget.ImageView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.common.utils.PathInterpolatorHelper
import com.soundrecorder.common.utils.ViewUtils

object TransitionUtils {
    const val TAG = "TransitionUtils"
    const val SHARED_VIEW = "sharedView"
    const val SHARED_RED_CIRCLE_VIEW = "sharedRedCircleView"
    const val MARK_VIEW = "markView"
    const val STOP_VIEW = "stopView"
    const val WAVE_RECYCLER_VIEW = "waveGradientView"
    const val TIMER_VIEW = "timerView"
    const val TOOLBAR = "toolBar"
    const val TAG_DIRECT_RECORDING_VIEW = "directRecordingView"
    const val MAX_DURATION_OF_RECORDER = 400L
    const val DURATION_VIEW_ALPHA = 230L
    const val VALUE_TRANSITION_Y_DEFAULT = 90f
    var panelHeight = 0f
    var startScale = 0.9f
    private var animMap: HashMap<String, View?>? = HashMap()
    private var mRedCircleView: ImageView? = null
    private var mMarkView: View? = null
    private var mStopView: View? = null
    private var mSharedView: View? = null
    private var mWaveRecyclerView: View? = null
    private var mTimerView: View? = null
    private var mToolBar: View? = null
    private var directRecordingView: View? = null

    @JvmStatic
    fun runExitAnimation(
        animatorLayer: ViewGroup?,
        rootView: ViewGroup?,
        listener: Animator.AnimatorListener?
    ): AnimatorSet? {
        if (animatorLayer == null || rootView == null || listener == null) {
            return null
        }

        findNeedAnimView(animatorLayer)

        val enterAnimGroup = AnimatorSet()
        enterAnimGroup.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                DebugUtil.i(TAG, "<<< runExitAnimation onAnimationStart")
                listener.onAnimationStart(animation)
            }

            override fun onAnimationEnd(animation: Animator) {
                DebugUtil.i(TAG, "<<< runExitAnimation onAnimationEnd")
                listener.onAnimationEnd(animation)
            }
        })
        val mutableList = mutableListOf<ValueAnimator>()
        createSharedViewAnim()?.let {
            mutableList.add(it)
        }
        createShareViewAlphaAnim()?.let {
            mutableList.add(it)
        }
        createMarkViewAnim(rootView)?.let {
            mutableList.add(it)
        }
        createStopViewAnim()?.let {
            mutableList.add(it)
        }
        createTimerAlphaAnim()?.let {
            mutableList.add(it)
        }
        createTimerTransAnim()?.let {
            mutableList.add(it)
        }
        createWaveAlphaAnim()?.let {
            mutableList.add(it)
        }
        createWaveTransAnim()?.let {
            mutableList.add(it)
        }
        createToolBarAlphaAnim()?.let {
            mutableList.add(it)
        }
        createToolBarTransAnim()?.let {
            mutableList.add(it)
        }
        createDirectRecordingViewAnim()?.let {
            mutableList.addAll(it)
        }
        enterAnimGroup.playTogether(mutableList as Collection<Animator>)
        enterAnimGroup.reverse()
        return enterAnimGroup
    }

    @JvmStatic
    fun release() {
        animMap?.clear()
        animMap = null
        mRedCircleView = null
        mMarkView = null
        mStopView = null
        mSharedView = null
        mWaveRecyclerView = null
        mTimerView = null
        mToolBar = null
        directRecordingView = null
    }

    @JvmStatic
    fun runEnterAnimation(
        animatorLayer: ViewGroup?,  //android.R.id.content
        rootView: ViewGroup?,
        listener: Animator.AnimatorListener?
    ): AnimatorSet? {
        if (animatorLayer == null || rootView == null || listener == null) {
            return null
        }

        findNeedAnimView(animatorLayer)

        val enterAnimGroup = AnimatorSet()
        enterAnimGroup.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                DebugUtil.i(TAG, "<<< runEnterAnimation onAnimationStart")
                listener.onAnimationStart(animation)
            }

            override fun onAnimationEnd(animation: Animator) {
                DebugUtil.i(TAG, "<<< runEnterAnimation onAnimationEnd")
                listener.onAnimationEnd(animation)
            }
        })

        animatorLayer.viewTreeObserver?.addOnPreDrawListener(object :
            ViewTreeObserver.OnPreDrawListener {
            override fun onPreDraw(): Boolean {
                animatorLayer.viewTreeObserver?.removeOnPreDrawListener(this)
                DebugUtil.i(TAG, "<<< rootView onPreDraw")
                val mutableList = mutableListOf<ValueAnimator>()
                createSharedViewAnim()?.let {
                    mutableList.add(it)
                }
                createShareViewAlphaAnim()?.let {
                    mutableList.add(it)
                }
                createMarkViewAnim(rootView)?.let {
                    mutableList.add(it)
                }
                createStopViewAnim()?.let {
                    mutableList.add(it)
                }
                createTimerAlphaAnim()?.let {
                    mutableList.add(it)
                }
                createTimerTransAnim()?.let {
                    mutableList.add(it)
                }
                createWaveAlphaAnim()?.let {
                    mutableList.add(it)
                }
                createWaveTransAnim()?.let {
                    mutableList.add(it)
                }
                createToolBarAlphaAnim()?.let {
                    mutableList.add(it)
                }
                createToolBarTransAnim()?.let {
                    mutableList.add(it)
                }
                createDirectRecordingViewAnim()?.let {
                    mutableList.addAll(it)
                }
                enterAnimGroup.playTogether(mutableList as Collection<Animator>)
                enterAnimGroup.start()
                return true
            }
        })
        return enterAnimGroup
    }

    @JvmStatic
    private fun initContainer() {
        if (animMap == null) {
            animMap = HashMap()
        }
    }

    @JvmStatic
    private fun findNeedAnimView(rootView: View) {
        mRedCircleView = rootView.findViewWithTag(SHARED_RED_CIRCLE_VIEW)
        mSharedView = rootView.findViewWithTag(SHARED_VIEW)
        mStopView = rootView.findViewWithTag(STOP_VIEW)
        mMarkView = rootView.findViewWithTag(MARK_VIEW)
        mWaveRecyclerView = rootView.findViewWithTag(WAVE_RECYCLER_VIEW)
        mTimerView = rootView.findViewWithTag(TIMER_VIEW)
        mToolBar = rootView.findViewWithTag(TOOLBAR)
        directRecordingView = rootView.findViewWithTag(TAG_DIRECT_RECORDING_VIEW)
    }

    @JvmStatic
    private fun addShadowView(view: View?, container: ViewGroup): ImageView? {
        if (view == null) {
            return null
        }
        val location = IntArray(2)
        view.getLocationOnScreen(location)
        DebugUtil.i(TAG, "<<< lx = ${location[0]} , ly = ${location[1]}")
        val bmp = convertViewToBitmap(view) ?: return null
        val shadowView = ImageView(container.context)
        shadowView.setImageBitmap(bmp)
        val shadowViewLayoutParams = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.WRAP_CONTENT,
            FrameLayout.LayoutParams.WRAP_CONTENT
        )
        if (container.context.resources.configuration.layoutDirection == View.LAYOUT_DIRECTION_RTL) {
            shadowViewLayoutParams.rightMargin = ScreenUtil.screenWidth - view.width - location[0]
        } else {
            shadowViewLayoutParams.leftMargin = location[0]
        }
        shadowViewLayoutParams.topMargin = location[1]
        container.addView(shadowView, shadowViewLayoutParams)
        return shadowView
    }

    @JvmStatic
    private fun createShadowCurrentModeLayoutAnim(targetView: View?): ValueAnimator? {
        val target = targetView ?: return null
        DebugUtil.i(TAG, "<<< createShadowCurrentModeLayoutAnim")
        val alphaHolder: PropertyValuesHolder = PropertyValuesHolder.ofFloat(View.ALPHA, 1f, 0f)
        val anim = ObjectAnimator.ofPropertyValuesHolder(target, alphaHolder)
        anim.interpolator = PathInterpolatorHelper.couiEaseInterpolator
        anim.duration = 180
        return anim
    }

    @JvmStatic
    private fun createSharedViewAnim(): ValueAnimator? {
        val shareView = mSharedView ?: return null
        val startY = shareView.height - panelHeight - shareView.paddingBottom
        val endY = 0f
        DebugUtil.i(TAG, "<<< createSharedViewAnim shared view startY = $startY , endY = $endY ")
        val holder: PropertyValuesHolder =
            PropertyValuesHolder.ofFloat(View.TRANSLATION_Y, startY, endY)
        val anim = ObjectAnimator.ofPropertyValuesHolder(shareView, holder)
        anim.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
        anim.duration = MAX_DURATION_OF_RECORDER
        return anim
    }

    @JvmStatic
    private fun createShareViewAlphaAnim(): ValueAnimator? {
        val shareView = mSharedView ?: return null
        DebugUtil.i(TAG, "<<< createShareViewAlphaAnim shared view")
        val holder: PropertyValuesHolder = PropertyValuesHolder.ofFloat(View.ALPHA, 0f, 1f)
        val anim = ObjectAnimator.ofPropertyValuesHolder(shareView, holder)
        anim.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
        anim.duration = 100
        return anim
    }

    @JvmStatic
    private fun createStopViewAnim(): ValueAnimator? {
        val stopView = mStopView ?: return null
        val redView = (mRedCircleView?.parent as? View) ?: return null
        val startX = redView.left + redView.width / 2f - (stopView.left + stopView.width / 2f)
        val endX = 0f
        DebugUtil.i(TAG, "<<< createStopViewAnim stop view startX = $startX , endX = $endX")
        val alphaHolder: PropertyValuesHolder = PropertyValuesHolder.ofFloat(View.ALPHA, 0f, 1f)
        val translationXHolder: PropertyValuesHolder =
            PropertyValuesHolder.ofFloat(View.TRANSLATION_X, startX, endX)
        val anim = ObjectAnimator.ofPropertyValuesHolder(stopView, alphaHolder, translationXHolder)
        anim.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
        anim.duration = MAX_DURATION_OF_RECORDER
        return anim
    }

    @JvmStatic
    private fun createMarkViewAnim(rootView: View): ValueAnimator? {
        val markView = mMarkView ?: return null
        val redView = (mRedCircleView?.parent as? View) ?: return null
        val startX = redView.left + redView.width / 2f - (markView.left + markView.width / 2f)
        val endX = 0f
        DebugUtil.i(TAG, "<<< createMarkViewAnim mark view startX = $startX , endX = $endX")
        val alphaHolder: PropertyValuesHolder = PropertyValuesHolder.ofFloat(View.ALPHA, 0f, 1f)
        val translationXHolder: PropertyValuesHolder =
            PropertyValuesHolder.ofFloat(View.TRANSLATION_X, startX, endX)
        val anim = ObjectAnimator.ofPropertyValuesHolder(markView, alphaHolder, translationXHolder)
        anim.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
        anim.duration = MAX_DURATION_OF_RECORDER
        return anim
    }

    @JvmStatic
    private fun createTimerAlphaAnim(): ValueAnimator? {
        val timerView = mTimerView ?: return null
        DebugUtil.i(TAG, "<<< createTimerAlphaAnim")
        val alphaHolder: PropertyValuesHolder = PropertyValuesHolder.ofFloat(View.ALPHA, 0f, 1f)
        val anim = ObjectAnimator.ofPropertyValuesHolder(timerView, alphaHolder)
        anim.interpolator = PathInterpolatorHelper.couiEaseInterpolator
        anim.startDelay = 50
        anim.duration = DURATION_VIEW_ALPHA
        return anim
    }

    @JvmStatic
    private fun createTimerTransAnim(): ValueAnimator? {
        val timerView = mTimerView ?: return null
        DebugUtil.i(TAG, "<<< createWaveTransAnim")
        val translationXHolder: PropertyValuesHolder =
            PropertyValuesHolder.ofFloat(View.TRANSLATION_Y, ViewUtils.dp2px(90f), 0f)
        val anim = ObjectAnimator.ofPropertyValuesHolder(timerView, translationXHolder)
        anim.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
        anim.startDelay = 0
        anim.duration = MAX_DURATION_OF_RECORDER
        return anim
    }

    @JvmStatic
    private fun createDirectRecordingViewAnim(): List<ValueAnimator>? {
        val view = directRecordingView ?: return null
        DebugUtil.i(TAG, "<<< createDirectRecordingViewAnim")
        val animList = arrayListOf<ValueAnimator>()
        val alphaHolder: PropertyValuesHolder = PropertyValuesHolder.ofFloat(View.ALPHA, 0f, 1f)
        val anim = ObjectAnimator.ofPropertyValuesHolder(view, alphaHolder)
        anim.interpolator = PathInterpolatorHelper.couiEaseInterpolator
        anim.startDelay = NumberConstant.NUM_100.toLong()
        anim.duration = DURATION_VIEW_ALPHA
        animList.add(anim)

        val translationXHolder: PropertyValuesHolder =
            PropertyValuesHolder.ofFloat(View.TRANSLATION_Y, ViewUtils.dp2px(VALUE_TRANSITION_Y_DEFAULT), 0f)
        val transAnim = ObjectAnimator.ofPropertyValuesHolder(view, translationXHolder)
        transAnim.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
        transAnim.startDelay = 0
        transAnim.duration = MAX_DURATION_OF_RECORDER
        animList.add(transAnim)
        return animList
    }

    @JvmStatic
    private fun createWaveAlphaAnim(): ValueAnimator? {
        val waveRecyclerView = mWaveRecyclerView ?: return null
        DebugUtil.i(TAG, "<<< createWaveAlphaAnim")
        val alphaHolder: PropertyValuesHolder = PropertyValuesHolder.ofFloat(View.ALPHA, 0f, 1f)
        val anim = ObjectAnimator.ofPropertyValuesHolder(waveRecyclerView, alphaHolder)
        anim.interpolator = PathInterpolatorHelper.couiEaseInterpolator
        anim.startDelay = 100
        anim.duration = DURATION_VIEW_ALPHA
        return anim
    }

    @JvmStatic
    private fun createWaveTransAnim(): ValueAnimator? {
        val waveRecyclerView = mWaveRecyclerView ?: return null
        DebugUtil.i(TAG, "<<< createWaveTransAnim")
        val translationXHolder: PropertyValuesHolder =
            PropertyValuesHolder.ofFloat(View.TRANSLATION_Y, ViewUtils.dp2px(90f), 0f)
        val anim = ObjectAnimator.ofPropertyValuesHolder(waveRecyclerView, translationXHolder)
        anim.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
        anim.startDelay = 0
        anim.duration = MAX_DURATION_OF_RECORDER
        return anim
    }

    @JvmStatic
    private fun createToolBarAlphaAnim(): ValueAnimator? {
        val toolBar = mToolBar ?: return null
        DebugUtil.i(TAG, "<<< createToolBarAlphaAnim")
        val alphaHolder: PropertyValuesHolder = PropertyValuesHolder.ofFloat(View.ALPHA, 0f, 1f)
        val anim = ObjectAnimator.ofPropertyValuesHolder(toolBar, alphaHolder)
        anim.interpolator = PathInterpolatorHelper.couiEaseInterpolator
        anim.startDelay = 150
        anim.duration = DURATION_VIEW_ALPHA
        return anim
    }

    @JvmStatic
    private fun createToolBarTransAnim(): ValueAnimator? {
        val toolBar = mToolBar ?: return null
        DebugUtil.i(TAG, "<<< createToolBarTransAnim")
        val translationXHolder: PropertyValuesHolder =
            PropertyValuesHolder.ofFloat(View.TRANSLATION_Y, ViewUtils.dp2px(90f), 0f)
        val anim = ObjectAnimator.ofPropertyValuesHolder(toolBar, translationXHolder)
        anim.interpolator = PathInterpolatorHelper.couiMoveEaseInterpolator
        anim.startDelay = 0
        anim.duration = MAX_DURATION_OF_RECORDER
        return anim
    }

    @JvmStatic
    fun putAnimView(transitionName: String, view: View?) {
        initContainer()
        animMap?.let {
            it[transitionName] = view
        }
    }

    @JvmStatic
    private fun convertViewToBitmap(v: View): Bitmap? {
        if ((v.width <= 0) || (v.height <= 0)) {
            return null
        }
        val bitmap = Bitmap.createBitmap(v.width, v.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        v.draw(canvas)
        return bitmap
    }
}