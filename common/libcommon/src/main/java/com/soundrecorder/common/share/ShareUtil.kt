/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ShareUtil
 ** Description : ShareUtil
 ** Version     : 1.0
 ** Date        : 2025/06/06
 ** Author      : renjiahao
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  renjiahao       2025/06/06       1.0      create
 ***********************************************************************/
package com.soundrecorder.common.share

import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.common.constant.Constants

object ShareUtil {
    val dirOShare: String = getOShareDir()

    val relativePathOShare: String = getOShareRelativePath()

    val relativePathOShareText: String = getOShareTextRelativePath()

    @JvmStatic
    fun isOShareFile(convertTextFilePath: String?): Boolean {
        if (convertTextFilePath.isNullOrEmpty()) {
            return false
        }
        return convertTextFilePath.contains(relativePathOShareText)
    }

    @JvmStatic
    private fun getOShareDir(): String {
        if (BaseUtil.isRealme()) {
            return Constants.OPPO_SHARE_REALME_RECORDINGS
        }
        if (BaseUtil.isOnePlus()) {
            return Constants.OPPO_SHARE_ONE_PLUS_RECORDINGS
        }
        return Constants.OPPO_SHARE_RECORDINGS
    }

    @JvmStatic
    private fun getOShareRelativePath(): String {
        if (BaseUtil.isRealme()) {
            return Constants.OPPO_SHARE_REALME_RECORDINGS_RELATIVE_PATH
        }
        if (BaseUtil.isOnePlus()) {
            return Constants.OPPO_SHARE_ONE_PLUS_RECORDINGS_RELATIVE_PATH
        }
        return Constants.OPPO_SHARE_RECORDINGS_RELATIVE_PATH
    }

    @JvmStatic
    private fun getOShareTextRelativePath(): String {
        if (BaseUtil.isRealme()) {
            return Constants.OPPO_SHARE_REALME_RECORDINGS_TEXT
        }
        if (BaseUtil.isOnePlus()) {
            return Constants.OPPO_SHARE_ONE_PLUS_RECORDINGS_TEXT
        }
        return Constants.OPPO_SHARE_RECORDINGS_TEXT
    }
}