/*
Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
File: - ShareSummaryAndTextPreference
Description: 录音分享录音弹窗工具类
Version: 1.0
Date : 2025/6/3
Author: W9067780
--------------------- Revision History: ---------------------
<author>	<data> 	  <version >	   <desc>
W9067780  2025/6/3     1.0      create this file
*/
package com.soundrecorder.common.flexible

import android.content.DialogInterface
import androidx.fragment.app.FragmentManager
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.soundrecorder.base.utils.DebugUtil

class ShareRecordCOUIDialog(val childFragmentManager: FragmentManager?) {
    companion object {
        private const val TAG = "ShareRecordCOUIDialog"
    }

    private var bottomSheetDialogFragment: COUIBottomSheetDialogFragment? = null

    fun showShareRecordDialog(hasSummary: Boolean, hasConvertContent: <PERSON><PERSON><PERSON>, callBack: (type: Int) -> Unit) {
        if (isShowing()) {
            DebugUtil.i(TAG, "bottomSheetDialogFragment is showing")
            return
        }
        bottomSheetDialogFragment = COUIBottomSheetDialogFragment().apply {
            setMainPanelFragment(ShareRecordDialogFragment().apply {
                setOnShareRecordDialogClickEvent(hasSummary, hasConvertContent, callBack)
            })
        }
        childFragmentManager?.let {
            it.findFragmentByTag(ShareRecordDialogFragment.SHARE_TAG)?.let {
                childFragmentManager.beginTransaction()
                    .remove(it)
                    .commitAllowingStateLoss()
            }
            bottomSheetDialogFragment?.show(childFragmentManager, ShareRecordDialogFragment.SHARE_TAG)
        }
    }

    fun isShowing(): Boolean {
        return bottomSheetDialogFragment?.dialog?.isShowing == true
    }

    fun dismiss() {
        DebugUtil.d(TAG, "dismiss")
        bottomSheetDialogFragment?.dismiss()
        bottomSheetDialogFragment = null
    }

    fun setOnDismissListener(listener: DialogInterface.OnDismissListener) {
        DebugUtil.i(TAG, "bottomSheetDialogFragment setOnDismissListener")
        bottomSheetDialogFragment?.setOnDismissListener {
            listener
        }
    }
}