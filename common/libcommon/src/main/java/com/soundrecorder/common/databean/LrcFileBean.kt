/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: LrcFileBean
 * Description:
 * Version: 1.0
 * Date: 2024/2/27
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/2/27 1.0 create
 */

package com.soundrecorder.common.databean

import android.net.Uri
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import com.soundrecorder.common.db.NoteDbUtils.SummaryType.SUMMARY_TYPE_COMMON_RECORD

/**
 * 开始摘要传递给语音转文字的lrc文件内容
 */
@Keep
class LrcFileBean {

    @SerializedName("t")
    var type = SUMMARY_TYPE_COMMON_RECORD
    var s = 0L  //录音开始相对时间

    //录音文本摘要跳转到字幕的时候，需要传递文本的句子和对应的标记，截图信息
    @SerializedName("l")
    var convertSentenceList = arrayListOf<Sentence>()

    //录音ast摘要跳转到字幕的时候，需要传递音频文件打的标记，截图信息
    @SerializedName("f")
    var textFlagList = arrayListOf<FlagInfo>()

    @SerializedName("p")
    var picFlagList = arrayListOf<PicInfo>()

    /*录音自己记录要授权的图片uriList*/
    @Transient
    var picUriList: List<Uri>? = null
}

/*s:标记相对开始时间点; n:标记TEXT*/
@Keep
data class FlagInfo(val s: Long, val n: String)

/*s:标记相对开始时间点; p:图片uri*/
@Keep
data class PicInfo(val s: Long, val n: String, val p: String?)

@Keep
class Sentence {
    @SerializedName("k")
    var roleId: Int = -1 //角色序号
        set(value) {
            if (value <= 0) {
                /*不支持讲话人，需要将传递-1给便签
                * 不支持讲话人，转文本中roleId为0，所以这里需要处理（正常有讲话人的话，是从1开始累加的）*/
                field = -1
            } else {
                field = value
            }
        }

    @SerializedName("n")
    var roleName: String = "" //角色名字

    @SerializedName("s")
    var sTime: Long = 0  //开始时间  句子相对开始时间

    @SerializedName("e")
    var eTime: Long = 0 //结束时间   句子相对结束时间

    @SerializedName("c")
    var content: String = "" //文本内容

    @SerializedName("p")
    var picMark = arrayListOf<PicInfo>() //图片标记

    @SerializedName("f")
    var textMark = arrayListOf<FlagInfo>() //文本标记
}
