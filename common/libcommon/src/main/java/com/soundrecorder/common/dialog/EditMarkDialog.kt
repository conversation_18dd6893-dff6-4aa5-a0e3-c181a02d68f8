/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : EditMarkDialog.kt
 ** Description : EditMarkDialog.kt
 ** Version     : 1.0
 ** Date        : 2025/07/01
 ** Author      : renjiahao
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  renjiahao       2025/07/01      1.0      create
 ***********************************************************************/
package com.soundrecorder.common.dialog

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.view.WindowManager
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.edittext.COUIEditText
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.KeyboardUtils.hideSoftInput
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.R
import com.soundrecorder.common.utils.ViewUtils.updateWindowLayoutParams
import com.soundrecorder.common.widget.EditTextWithImage

class EditMarkDialog(
    val activity: AppCompatActivity,
    private val markText: String,
    private val markImage: String,
    private val callback: (String, String) -> Unit
) : DefaultLifecycleObserver,
    ViewTreeObserver.OnWindowAttachListener {
    companion object {
        private const val TAG = "EditMarkDialog"
    }

    private val customView by lazy {
        val view = LayoutInflater.from(activity).inflate(R.layout.dialog_edit_mark_layout, null)
        btnSelectImage = view.findViewById(R.id.btn_select_photo)
        btnTakePhoto = view.findViewById(R.id.btn_take_photo)
        btnDone = view.findViewById(R.id.btn_complete)
        editText = view.findViewById(R.id.edit_text)
        view
    }

    private var btnSelectImage: ImageView? = null
    private var btnTakePhoto: ImageView? = null
    private var btnDone: COUIButton? = null
    private var editText: EditTextWithImage? = null
    private var dialog: AlertDialog? = null
    private var clickListener: IEditMarkDialogListener? = null

    fun show() {
        if (isShowing()) {
            return
        }
        dialog = createDefaultDialog().show().apply {
            getEditText()?.setText(markText)
            addImage(markImage)
            findViewById<View>(com.support.dialog.R.id.customPanel)?.setWeight(1f)
            updateWindowLayoutParams(window)
            setOnClick()
        }
        initClickListener()
    }

    private fun initClickListener() {
        btnDone?.setOnClickListener {
            callback.invoke(getNewContent(), getNewImage())
            dismiss()
        }
        btnTakePhoto?.setOnClickListener {
            if (getNewImage().isNotEmpty()) {
                ToastManager.showShortToast(activity, R.string.toast_edit_mark_dialog_limit)
                return@setOnClickListener
            }
            clickListener?.onClick(ButtonType.TAKE_PHOTO)
        }
        btnSelectImage?.setOnClickListener {
            if (getNewImage().isNotEmpty()) {
                ToastManager.showShortToast(activity, R.string.toast_edit_mark_dialog_limit)
                return@setOnClickListener
            }
            clickListener?.onClick(ButtonType.SELECT_PICTURE)
        }
        editText?.addContentChangeListener { text, path ->
            btnDone?.isEnabled = !(text.isEmpty() && path.isEmpty())
        }
    }

    /**
     * 根据UI规范，弹窗有最大高度限制，当自定义view高度过大时，底部按钮会显示不全，因此需要设置customPanel的weight为1
     */
    private fun View.setWeight(weight: Float) {
        layoutParams = layoutParams.let {
            (it as? LinearLayout.LayoutParams)?.weight = weight
            (it as? LinearLayoutCompat.LayoutParams)?.weight = weight
            it
        }
    }

    private fun AlertDialog.setOnClick() {
        window?.decorView?.viewTreeObserver?.addOnWindowAttachListener(this@EditMarkDialog)
    }

    private fun createDefaultDialog(): COUIAlertDialogBuilder {
        (customView.parent as? ViewGroup)?.removeAllViews()
        val builder = COUIAlertDialogBuilder(activity, com.support.dialog.R.style.COUIAlertDialog_BottomAssignment)
        builder.setBlurBackgroundDrawable(true)
            .setView(customView)
        return builder
    }

    fun isShowing(): Boolean = dialog?.isShowing == true

    private fun showInputKeyboard() {
        getEditText()?.let {
            it.isFocusable = true
            it.isFocusableInTouchMode = true
            it.requestFocus()
            dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
        }
    }

    private fun showInputKeyboardWhenWindowAttached() {
        dialog?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_VISIBLE)
    }

    private fun hideInputKeyBoard() {
        getEditText()?.apply {
            hideSoftInput(activity)
        }
    }

    fun onConfigurationChanged() {
        DebugUtil.d("onConfigurationChanged", this.javaClass.simpleName)
        if (isShowing()) {
            dialog?.window?.decorView?.requestLayout()
        }
    }

    fun getEditText(): COUIEditText? {
        return customView.findViewById<EditTextWithImage>(R.id.edit_text).getEditText()
    }

    fun getNewContent(): String {
        return getEditText()?.text.toString().trim()
    }

    fun getNewImage(): String {
        return editText?.getImagePath() ?: ""
    }

    fun setClickListener(listener: IEditMarkDialogListener?) {
        clickListener = listener
    }

    fun addImage(path: String?) {
        editText?.addImage(path)
    }

    fun dismiss() {
        if (isShowing()) {
            hideInputKeyBoard()
            dialog?.dismiss()
        }
    }

    override fun onResume(owner: LifecycleOwner) {
        showInputKeyboard()
    }

    override fun onWindowAttached() {
        getEditText()?.selectAll()
        showInputKeyboardWhenWindowAttached()
        activity.lifecycle.addObserver(this)
    }

    override fun onWindowDetached() {
        clickListener = null
        editText?.removeContentChangeListener()
        hideInputKeyBoard()
        activity.lifecycle.removeObserver(this)
        dialog?.window?.decorView?.viewTreeObserver?.removeOnWindowAttachListener(this@EditMarkDialog)
        dialog = null
    }
}

enum class ButtonType {
    TAKE_PHOTO, SELECT_PICTURE
}

fun interface IEditMarkDialogListener {
    fun onClick(type: ButtonType)
}