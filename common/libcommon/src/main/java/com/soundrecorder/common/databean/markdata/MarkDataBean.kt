package com.soundrecorder.common.databean.markdata

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Bitmap
import android.graphics.RectF
import android.os.Build
import android.os.Parcel
import android.os.Parcelable
import android.os.SystemClock
import android.view.View
import android.view.animation.LinearInterpolator
import androidx.annotation.DrawableRes
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.model.TimeStampGetter
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.NumberConstant
import com.soundrecorder.base.utils.TimeUtils
import com.soundrecorder.common.R
import com.soundrecorder.common.databean.MarkDataSource
import com.soundrecorder.common.utils.MarkSerializUtil
import com.soundrecorder.common.utils.MarkSerializUtil.VERSION_NEW
import com.soundrecorder.common.utils.MarkSerializUtil.VERSION_OLD
import com.soundrecorder.common.utils.MarkSerializUtil.VERSION_PICTURE
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.imageload.ImageLoadData
import com.soundrecorder.imageload.ImageLoaderUtils
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerData
import java.io.File
import java.io.Serializable
import java.lang.ref.WeakReference

data class MarkDataBean(var timeInMills: Long, var version: Int = VERSION_NEW) :
    PictureMarkTarget(), Comparable<MarkDataBean>, Serializable, PhotoViewerData, TimeStampGetter {
    var isDefault: Boolean = true
    var defaultNo: Int = 0
    var markText: String = ""
    var pictureFilePath = ""
        set(value) {
            field = value
            smallImageLoadData = ImageLoadData(
                FileUtils.getAppFile(pictureFilePath, false),
                ViewUtils.dp2px(NumberConstant.NUM_F9_0, true).toInt(),
                ViewUtils.dp2px(NumberConstant.NUM_F9_0, true).toInt()
            )
            middleImageLoadData = ImageLoadData(
                FileUtils.getAppFile(pictureFilePath, false),
                ViewUtils.dp2px(NumberConstant.NUM_F30_0, true).toInt(),
                ViewUtils.dp2px(NumberConstant.NUM_F30_0, true).toInt()
            )
        }

    //标记添加时的时间
    var addTimeMills: Long = -1L

    //标记当前的画笔透明度
    var colorAlpha: Int = NUM_255
    //标记绘制动画
    private var colorAlphaAnimation: ValueAnimator? = null
    //标记绘制动画是否已执行
    private var colorAlphaAnimFinished: Boolean = false

    /**
     * 图片标记图片的宽度，用于图文混排imageView的宽度计算
     */
    var pictureWith: Int = 0

    /**
     * 图片标记图片的高度，用于图文混排imageView高度的计算
     */
    var pictureHeight: Int = 0

    var keyId = ""

    /**
     * 校正后的时间
     */
    var correctTime: Long = 0

    /**
     * 用于adapter数据计算diff
     */
    var isUpdate = false

    var rect = RectF()

    // 标记来源
    var markSource: MarkDataSource = MarkDataSource.Converting

    private var smallImageLoadData = ImageLoadData(
        FileUtils.getAppFile(pictureFilePath, false),
        ViewUtils.dp2px(NumberConstant.NUM_F9_0, true).toInt(),
        ViewUtils.dp2px(NumberConstant.NUM_F9_0, true).toInt()
    )

    private var middleImageLoadData = ImageLoadData(
        FileUtils.getAppFile(pictureFilePath, false),
        ViewUtils.dp2px(NumberConstant.NUM_F30_0, true).toInt(),
        ViewUtils.dp2px(NumberConstant.NUM_F30_0, true).toInt()
    )


    init {
        correctTime = timeInMills
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    constructor(parcel: Parcel) : this(
        timeInMills = parcel.readLong(),
        version = parcel.readInt()
    ) {
        this.isDefault = parcel.readBoolean()
        this.defaultNo = parcel.readInt()
        this.markText = parcel.readString().toString()
        this.pictureFilePath = parcel.readString().toString()
        this.keyId = parcel.readString().toString()
        this.correctTime = parcel.readLong()
        this.pictureWith = parcel.readInt()
        this.pictureHeight = parcel.readInt()
        this.addTimeMills = parcel.readLong()
    }

    /**
     * 校正时间
     */
    fun correctTime(time: Long) {
        DebugUtil.d(TAG, "数据库时间:$timeInMills 校正时间：$time")
        correctTime = time
    }

    fun getIcon(
        context: Context,
        @DrawableRes defaultRes: Int,
        @DrawableRes loadDefaultRes: Int
    ): Bitmap? {
        val d = when {
            !fileExists() -> {
                ContextCompat.getDrawable(context, defaultRes)?.toBitmap()
            }

            else -> {
                start(loadDefaultRes)
            }
        }
        return d
    }

    fun getShowTime(): String = TimeUtils.getFormatTimeExclusiveMill(correctTime)

    fun fileExists(): Boolean {
        return pictureFilePath.isNotEmpty()
    }

    override fun getWaveImageLoadData(): ImageLoadData {
        return smallImageLoadData
    }

    /**
     * 标记列表加载图片Key对象，coil的Key
     */
    fun getMarkListImageLoadData(): ImageLoadData {
        return middleImageLoadData
    }

    fun toStoreString(): String {
        val result: String = if (version == VERSION_NEW) {
            if (isDefault) {
                (timeInMills.toString() + MarkSerializUtil.SPLIT_BETWEEN_TIME_AND_MARK + defaultNo.toString() + MarkSerializUtil.SPLIT_BETWEEN_MARK_AND_MARK_TYPE
                        + MarkSerializUtil.MARK_TYPE_DEFAULT)
            } else {
                (timeInMills.toString() + MarkSerializUtil.SPLIT_BETWEEN_TIME_AND_MARK + markText + MarkSerializUtil.SPLIT_BETWEEN_MARK_AND_MARK_TYPE
                        + MarkSerializUtil.MARK_TYPE_EDIT)
            }
        } else if (version == VERSION_OLD) {
            timeInMills.toString()
        } else {
            ""
        }
        return result
    }


    override fun compareTo(other: MarkDataBean): Int {
        return (this.timeInMills - other.timeInMills).toInt()
    }

    companion object CREATOR : Parcelable.Creator<MarkDataBean> {

        private const val TAG = "MarkDataBean"
        const val TIME_1000 = 1000L
        const val ALPHA_FLOAT_1_0 = 1.0F
        const val ALPHA_FLOAT_0_3 = 0.3F
        const val ANIM_DURATION_TIME_300 = 300L

        const val NUM_255 = 255

        @RequiresApi(Build.VERSION_CODES.Q)
        override fun createFromParcel(parcel: Parcel): MarkDataBean {
            return MarkDataBean(parcel)
        }

        override fun newArray(i: Int): Array<MarkDataBean?> {
            return arrayOfNulls(i)
        }
    }

    @RequiresApi(Build.VERSION_CODES.Q)
    override fun writeToParcel(dest: Parcel, flags: Int) {
        dest.apply {
            writeLong(timeInMills)
            writeInt(version)
            writeBoolean(isDefault)
            writeInt(defaultNo)
            writeString(markText)
            writeString(pictureFilePath)
            writeString(keyId)
            writeLong(correctTime)
            writeInt(pictureWith)
            writeInt(pictureHeight)
            writeLong(addTimeMills)
        }
    }

    override fun describeContents(): Int {
        return 0
    }


    fun isTextType(): Boolean {
        return (version == VERSION_NEW) || (version == VERSION_OLD)
    }

    fun isPictureType(): Boolean {
        return (version == VERSION_PICTURE)
    }


    override fun toString(): String {
        return "$TAG{timeInMills=$timeInMills, version='$version', isDefault='$isDefault', defaultNo=$defaultNo, markText='$markText},relativePath=$pictureFilePath, keyId=$keyId"
    }

    override fun src(): File {
        return FileUtils.getAppFile(pictureFilePath, false)
    }

    override fun getTimeStamp(): Long {
        return this.timeInMills
    }

    override fun clearMemoryCacheByKey() {
        super.clearMemoryCacheByKey()
        ImageLoaderUtils.clearMemoryCacheByKey(getMarkListImageLoadData())
    }

    fun getRealMarkText(): String {
        return markText.ifEmpty {
            if (defaultNo > 0) {
                BaseApplication.getAppContext().resources.getString(R.string.default_flag_new, defaultNo)
            } else {
                BaseApplication.getAppContext().getString(R.string.custom_mark_description)
            }
        }
    }

    /**
     * 标记的添加时间与当前时间比较，小于1s则表示时刚新增的标记
     */
    fun isNewMark(): Boolean {
        return SystemClock.elapsedRealtime() - addTimeMills < TIME_1000
    }

    /**
     * 检查是否为新增标记,
     * 如果是，则利用ValueAnimator 实现其画笔透明度在300ms内从1.0 - 0.3的变化，
     * 以便绘制时表现为透明度渐变的动画效果
     */
    fun checkIfNeedUpdatePaintAlpha(view: View) {
        val weakView = WeakReference(view)
        updatePaintAlpha {
            weakView.get()?.postInvalidateOnAnimation()
        }
    }

    fun updatePaintAlpha(callback: (() -> Unit)?): Boolean {
        if (isNewMark() && !colorAlphaAnimFinished && colorAlphaAnimation == null) {
            colorAlphaAnimation = ValueAnimator.ofFloat(ALPHA_FLOAT_1_0, ALPHA_FLOAT_0_3)
            colorAlphaAnimation?.let {
                it.setDuration(ANIM_DURATION_TIME_300)
                it.interpolator = LinearInterpolator()
                it.addUpdateListener { listener ->
                    val alpha = listener.animatedValue as Float
                    colorAlpha = (alpha * NUM_255).toInt()
                    callback?.invoke()
                    if (alpha == ALPHA_FLOAT_0_3) {
                        colorAlphaAnimFinished = true
                    }
                }
                it.start()
            }
            return true
        }
        return false
    }
}