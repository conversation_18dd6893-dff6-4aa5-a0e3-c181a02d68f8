/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.soundrecorder.common.utils;

import android.content.Context;
import android.os.Environment;
import android.preference.PreferenceManager;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.AddonAdapterCompatUtil;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.R;

import java.io.File;
import java.util.HashMap;

public class SettingsAdapter {
    public static final int QUALITY_NORMAL = 2;
    public static final int QUALITY_HIGH = 3;
    private static final String TAG = "SettingsAdapter";
    private static volatile SettingsAdapter sInstance = null;
    private String mStorageSdcard;
    private String mStoragePhone;
    private HashMap<Integer, Integer> mDefaultMap;
    private HashMap<String, Object> mMap;

    public static SettingsAdapter getInstance() {
        if (sInstance == null) {
            synchronized (SettingsAdapter.class) {
                if (sInstance == null) {
                    sInstance = new SettingsAdapter();
                }
            }
        }

        return sInstance;
    }
    public SettingsAdapter() {
        initStorageSdcard();
        initStoragePhone();
        initMap();
        initDefaultMap();
    }

    private void initStorageSdcard() {
        if (AddonAdapterCompatUtil.getExternalSdDirectory(BaseApplication.getAppContext()) != null) {
            mStorageSdcard = AddonAdapterCompatUtil.getExternalSdDirectory(BaseApplication.getAppContext()).getPath();
        } else {
            mStorageSdcard = AddonAdapterCompatUtil.getExternalStorageDirectory().getPath();
        }
    }

    private void initStoragePhone() {
        mStoragePhone = AddonAdapterCompatUtil.getInternalSdDirectory(BaseApplication.getAppContext()).getPath();
    }

    private void initDefaultMap() {
        mDefaultMap = new HashMap<Integer, Integer>();
        mDefaultMap.put(R.string.key_format, R.string.default_format);
        mDefaultMap.put(R.string.key_quality, R.string.default_quality);
        mDefaultMap.put(R.string.key_storage, R.string.default_storage);
        mDefaultMap.put(R.string.key_file_prefix, R.string.default_prefix);
    }

    private void initMap() {
        mMap = new HashMap<String, Object>();
        String storeDir = BaseApplication.getAppContext().getString(R.string.store_dir);

        if (BaseUtil.isAndroidQOrLater()) {
            mMap.put("phone", getStoragePhone() + File.separatorChar + Environment.DIRECTORY_MUSIC + File.separatorChar + storeDir);
            mMap.put("sdcard", getStorageSdcard() + File.separatorChar + Environment.DIRECTORY_MUSIC + File.separatorChar + storeDir);
        } else {
            mMap.put("phone", getStoragePhone()  + File.separatorChar + storeDir);
            mMap.put("sdcard", getStorageSdcard() + File.separatorChar + storeDir);
        }
        // quality
        mMap.put("normal", QUALITY_NORMAL);
        mMap.put("high", QUALITY_HIGH);
    }

    public Object get(int keyId) {
        String theKey = BaseApplication.getAppContext().getString(keyId);
        String value = PreferenceManager.getDefaultSharedPreferences(BaseApplication.getAppContext())
                .getString(theKey, BaseApplication.getAppContext().getString(getDefaultMap().get(keyId)));
        if (keyId == R.string.key_file_prefix) {
            return value;
        }
        return getMap().get(value);
    }

    public Object get(String key) {
        DebugUtil.v(TAG, "get key= " + key);
        if (key.equals(BaseApplication.getAppContext().getString(R.string.key_file_prefix))) {
            return PreferenceManager.getDefaultSharedPreferences(BaseApplication.getAppContext()).getString(key,
                    BaseApplication.getAppContext().getString(getDefaultMap().get(R.string.key_file_prefix)));
        }
        return getMap().get(key);
    }

    public HashMap<Integer, Integer> getDefaultMap() {
        if (mDefaultMap == null || mDefaultMap.size() == 0) {
            initDefaultMap();
        }
        return mDefaultMap;
    }

    public HashMap<String, Object> getMap() {
        if (mMap == null || mMap.size() == 0) {
            initMap();
        }
        return mMap;
    }

    public String getStorageSdcard() {
        if (mStorageSdcard == null) {
            initStorageSdcard();
        }
        return mStorageSdcard;
    }

    public String getStoragePhone() {
        if (mStoragePhone == null) {
            initStoragePhone();
        }
        return mStoragePhone;
    }

    public void release() {
        if (mMap != null) {
            mMap.clear();
            mMap = null;
        }
        if (mDefaultMap != null) {
            mDefaultMap.clear();
            mDefaultMap = null;
        }
        mStorageSdcard = null;
        mStoragePhone = null;
    }
}

