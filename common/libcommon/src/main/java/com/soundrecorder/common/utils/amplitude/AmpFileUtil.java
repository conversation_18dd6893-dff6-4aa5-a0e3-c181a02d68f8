/**********************************************************
 * Copyright 2010-2017 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description : AmpFileUtil
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-3-12, huang<PERSON><PERSON>, create
 ***********************************************************/

package com.soundrecorder.common.utils.amplitude;

import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA;

import android.content.Context;
import android.database.Cursor;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.base.utils.NumberUtil;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.DatabaseConstant;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class AmpFileUtil {

    public static final Long MAX_UPLOAD_AUDIO_FILE_SIZE_MB = 200L;
    private static final String TAG = "AmpFileUtil";
    private static final int AMP_FILE_COPY_BUF_SIZE = 4 * 1024;
    private static final Long BYTES_IN_MB = 1024 * 1024L;

    public static File saveAmpFile(Context context, String ampString) {
        if (TextUtils.isEmpty(ampString)) {
            DebugUtil.i(TAG, "input ampStri" +
                    "ng is empty, return ");
            return null;
        }
        File ampFile = genAmpFilePath(context);
        byte[] ampBytes = ampString.getBytes(Constants.UTF_8);
        FileOutputStream dstOutStream = null;
        InputStream is = null;
        byte[] buff = new byte[AMP_FILE_COPY_BUF_SIZE];
        int len = 0;
        try {
            dstOutStream = new FileOutputStream(ampFile);
            is = new ByteArrayInputStream(ampBytes);
            while ((len = is.read(buff)) != -1) {
                dstOutStream.write(buff, 0, len);
            }
        } catch (FileNotFoundException fne) {
            DebugUtil.e(TAG, "saveAmpFile file not found exception:", fne);
            return null;
        } catch (IOException ioe) {
            DebugUtil.e(TAG, "saveAmpFile copy byte to file id exception:", ioe);
            return null;
        } finally {
            try {
                if (dstOutStream != null) {
                    dstOutStream.close();
                }
                if (is != null) {
                    is.close();
                }
            } catch (IOException ioe) {
                DebugUtil.e(TAG, "close stream io error:", ioe);
            }
        }
        return ampFile;
    }

    private static File genAmpFilePath(Context context) {
        long currentTime = System.currentTimeMillis();
        String uuidString = UUID.randomUUID().toString();
        String fileName = uuidString + "_" + currentTime + Constants.AMP_FILE_SUFFIX;
        String ampFilePath = context.getFilesDir().getAbsolutePath() + File.separator + Constants.AMP_FOLDER;
        File ampDir = new File(ampFilePath);
        if (!ampDir.exists()) {
            boolean created = ampDir.mkdirs();
            if (created) {
                DebugUtil.i(TAG, "genAmpFilePath() amp directory mkdirs success !");
                return new File(ampFilePath, fileName);
            } else {
                DebugUtil.e(TAG, "genAmpFilePath() amp directory mkdirs failed !");
            }
        }
        return new File(ampDir, fileName);
    }


    public static String parseAmpFile(Context context, String ampFilePath) {
        if (TextUtils.isEmpty(ampFilePath)) {
            DebugUtil.w(TAG, "parseAmpFile ampFilePath is empty, return");
            return null;
        }
        if (FileUtils.getSuffix(ampFilePath).equals(Constants.AMP_FILE_TMP_SUFFIX)) {
            // old amp file is .tmp suffix, to get old amp data.
            String oldAmpString = getFileStringByFilePath(ampFilePath);

            // delete old amp file
            File ampFile = new File(ampFilePath);
            if (!ampFile.delete()) {
                DebugUtil.w(TAG, "parseAmpFile() delete old amp file failed ! ampFilePath :" + ampFilePath);
            }
            return oldAmpString;
        } else {
            return getFileStringByFilePath(ampFilePath);
        }
    }


    /**
     * get file string by file path
     * <p>
     * @param ampFilePath: file path
     * @return file content string
     */
    @NonNull
    private static String getFileStringByFilePath(String ampFilePath) {
        File ampFile = new File(ampFilePath);
        InputStream inputStream = null;
        BufferedReader bf = null;
        StringBuilder sb = new StringBuilder();
        try {
            inputStream = new FileInputStream(ampFile.getAbsoluteFile());
            bf = new BufferedReader(new InputStreamReader(inputStream, Constants.UTF_8));
            String str = null;
            while ((str = bf.readLine()) != null) {
                sb.append(str);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "read buffer:", e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (bf != null) {
                    bf.close();
                }
            } catch (Exception e) {
                DebugUtil.e(TAG, "close stream io error:", e);
            }
        }
        return sb.toString();
    }

    public static boolean ampFileIsExists(Context context, String path) {
        if (TextUtils.isEmpty(path)) {
            return false;
        }
        String where = COLUMN_NAME_DATA + " COLLATE NOCASE = ?";
        String[] selectionArgs = new String[]{path};
        Cursor cursor = null;
        try {
            String ampFilePath = null;
            cursor = context.getContentResolver().query(DatabaseConstant.RecordUri.RECORD_CONTENT_URI,
                    new String[]{DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_FILE_PATH}, where, selectionArgs, null);
            if (cursor != null && cursor.moveToFirst()) {
                int index = cursor.getColumnIndex(DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_FILE_PATH);
                if (index >= 0) {
                    ampFilePath = cursor.getString(index);
                }
            }
            if (ampFilePath == null) {
                return false;
            }
            File file = new File(ampFilePath);
            return file.exists();
        } catch (Exception e) {
            DebugUtil.d(TAG, e.toString());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return false;
    }

    public static boolean isFileSizeLargerThanOrEqualToLimit(Long fileSizeByte, Long limitInMB ) {
        if (fileSizeByte == null) {
            DebugUtil.e(TAG, "isFileSizeLargerThanOrEqualToLimit fileSizeByte is null");
            return true;
        }
        boolean isFileSizeLargerThanOrEqualToLimit = (fileSizeByte / BYTES_IN_MB) > limitInMB;
        DebugUtil.i(TAG, "isFileSizeLargerThanOrEqualToLimit=" + isFileSizeLargerThanOrEqualToLimit);
        return isFileSizeLargerThanOrEqualToLimit;
    }

    /**
     * convert string to int list
     * @param str: string
     * @return List<Integer>
     */
    public static List<Integer> convertStringToInt(String str) {
        if (!TextUtils.isEmpty(str)) {
            String[] amps = str.split(",");
            List<Integer> ampList = new ArrayList<>(amps.length);
            for (String amp : amps) {
                ampList.add(NumberUtil.parseStr2Int(amp, 0));
            }
            return ampList;
        }
        return new ArrayList<>();
    }
}
