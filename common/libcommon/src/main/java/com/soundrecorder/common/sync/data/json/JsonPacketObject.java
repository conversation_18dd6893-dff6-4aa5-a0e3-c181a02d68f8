/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: JsonPacketObject
 ** Description:
 ** Version: 1.0
 ** Date : 2019-07-11
 ** Author: huangyuanwang
 **
 ** v1.0, 2019-3-12, huangyuanwang, create
 ****************************************************************/

package com.soundrecorder.common.sync.data.json;

import android.text.TextUtils;


import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.HashSet;
import java.util.Map.Entry;
import java.util.Set;
import com.soundrecorder.common.sync.data.Packet;
import com.soundrecorder.common.sync.data.PacketArray;

public class JsonPacketObject implements Packet<JsonElement> {
    JsonObject mJsonObject = new JsonObject();

    @Override
    public void putString(String key, String v) {
        mJsonObject.addProperty(key, v);

    }

    @Override
    public void putKV(String key, Packet<JsonElement> v) {
        if (null != v) {
            mJsonObject.add(key, v.toT());
        }
    }

    @Override
    public void putNumber(String key, Number v) {
        mJsonObject.addProperty(key, v);
    }

    @Override
    public void putBoolean(String key, Boolean v) {
        mJsonObject.addProperty(key, v);
    }

    @Override
    public Number getNumber(String key) {
        JsonElement element = getJsonElement(key);
        return (element != null && !element.isJsonNull()) ? element.getAsNumber() : null;
    }

    @Override
    public Boolean getBoolean(String key) {
        JsonElement element = getJsonElement(key);
        return (element != null && !element.isJsonNull()) ? element.getAsBoolean() : false;
    }

    @Override
    public String getString(String key) {
        JsonElement element = getJsonElement(key);
        return (element != null && !element.isJsonNull()) ? element.getAsString() : null;
    }

    @Override
    public Packet<JsonElement> getKV(String key) {
        JsonElement element = getJsonElement(key);
        JsonPacketObject jsonKv = null;
        if (element != null && element.isJsonObject()) {
            jsonKv = new JsonPacketObject();
            jsonKv.mJsonObject = element.getAsJsonObject();
        }
        return jsonKv;
    }

    @Override
    public void putKVAsArray(String key, PacketArray<JsonElement> v) {
        if (null != v) {
            mJsonObject.add(key, v.toT());
        }
    }

    @Override
    public PacketArray<JsonElement> getKVAsArray(String key) {
        JsonPacketArray jsonKv = null;
        try {
            JsonElement element = getJsonElement(key);
            if (element != null && element.isJsonArray()) {
                JsonArray jsonArray = element.getAsJsonArray();
                if (jsonArray != null) {
                    jsonKv = new JsonPacketArray();
                    jsonKv.parse(jsonArray);
                }
            }
        } catch (java.lang.IllegalStateException e) {
            // When is null, throw java.lang.IllegalStateException: This
            // is not a JSON Array.
            return null;
        }
        return jsonKv;
    }

    @Override
    public Set<String> keySet() {
        final Set<Entry<String, JsonElement>> entrySet = mJsonObject.entrySet();
        final int size = entrySet.size();
        @SuppressWarnings("unchecked") final Entry<String, JsonElement>[] array = new Entry[size];
        entrySet.toArray(array);
        final Set<String> set = new HashSet<String>(size);
        for (int i = 0; i < size; i++) {
            set.add(array[i].getKey());
        }
        return set;
    }

    @Override
    public JsonObject toT() {
        return mJsonObject;
    }

    @Override
    public Packet<JsonElement> parse(JsonElement jsonObject) {
        this.mJsonObject = (JsonObject) jsonObject;
        return this;
    }

    private JsonElement getJsonElement(String key) {
        if (mJsonObject.has(key)) {
            return mJsonObject.get(key);
        }
        return null;
    }

    @Override
    public boolean isAllValueEmpty() {
        boolean allEmpty = true;

        if (null == mJsonObject || mJsonObject.isJsonNull()) {
            return allEmpty;
        }

        final Set<Entry<String, JsonElement>> entrySet = mJsonObject.entrySet();
        final int size = entrySet.size();
        @SuppressWarnings("unchecked") final Entry<String, JsonElement>[] array = new Entry[size];
        entrySet.toArray(array);

        for (int i = 0; i < size; i++) {
            JsonElement element = array[i].getValue();

            if (!TextUtils.isEmpty((element != null && !element.isJsonNull())
                    ? element.getAsString() : null)) {
                allEmpty = false;
                break;
            }
        }

        return allEmpty;
    }

    @Override
    public String toString() {
        return "JsonPacketObject{" +
                "mJsonObject=" + mJsonObject +
                '}';
    }
}
