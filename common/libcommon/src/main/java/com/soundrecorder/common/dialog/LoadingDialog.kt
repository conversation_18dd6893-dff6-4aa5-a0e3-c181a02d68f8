package com.soundrecorder.common.dialog

import android.app.Activity
import android.view.ViewTreeObserver
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.ViewUtils.updateWindowLayoutParams

class LoadingDialog(var activity: Activity?) : ViewTreeObserver.OnWindowAttachListener {

    companion object {
        private const val TAG = "LoadingDialog"
    }

    private var mDialog: AlertDialog? = null

    override fun onWindowDetached() {
        DebugUtil.i(TAG, "onWindowDetached")
        mDialog?.window?.decorView?.apply {
            findViewById<EffectiveAnimationView>(com.support.dialog.R.id.progress).pauseAnimation()
            viewTreeObserver?.removeOnWindowAttachListener(this@LoadingDialog)
        }
        activity = null
        mDialog = null
    }

    override fun onWindowAttached() {
        DebugUtil.i(TAG, "onWindowAttached")
        mDialog?.window?.decorView?.findViewById<EffectiveAnimationView>(com.support.dialog.R.id.progress)?.playAnimation()
    }

    fun isShowing(): Boolean = mDialog?.isShowing ?: false

    fun show(titleId: Int, cancelable: Boolean = false, canceledOnTouchOutside: Boolean = false) {
        activity?.let { act ->
            if (!act.isFinishing && !act.isDestroyed) {
                mDialog = COUIRotatingDialogBuilder(act, act.getString(titleId)).let {
                    DebugUtil.i(TAG, "show")
                    it.setBlurBackgroundWindow(true)
                    it.show()
                }.apply {
                    setCancelable(cancelable)
                    setCanceledOnTouchOutside(canceledOnTouchOutside)
                    addOnWindowAttachListener()
                    updateWindowLayoutParams(window)
                }
            } else {
                DebugUtil.e(TAG, "activity is not valid!")
            }
        }
    }

    fun resetActivity(activity: Activity?) {
        this.activity = activity
    }

    fun isActivityNull(): Boolean {
        return this.activity == null
    }

    private fun AlertDialog?.addOnWindowAttachListener() {
        this?.window?.decorView?.viewTreeObserver?.addOnWindowAttachListener(this@LoadingDialog)
    }

    fun dismiss() {
        DebugUtil.i(TAG, "dismiss")
        mDialog?.dismiss()
    }
}