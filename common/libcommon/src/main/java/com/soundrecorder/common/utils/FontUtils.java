/************************************************************
 * Copyright 2000-2012 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : FontUtils.java
 * Version Number: 1.0
 * Description   :
 * Author        : mao_hongyu
 * Date          : 2019.08.28
 * History       :(ID          ,Data        ,Name       ,Description)
 *                 W9001268    ,2019.08.28  ,mao_hongyu
 ************************************************************/
package com.soundrecorder.common.utils;

import android.graphics.Typeface;
import com.soundrecorder.base.utils.Singleton;

public class FontUtils {

    public static final String TAG = "FontUtils";

    private static Singleton<Typeface> sNewSysSansEnNormal = null;

    public static Typeface getNewSysSansEnNormal() {
        if (sNewSysSansEnNormal == null) {
            sNewSysSansEnNormal = new Singleton<Typeface>() {
                @Override
                protected Typeface create() {
                    return Typeface.create("sys-sans-en",Typeface.NORMAL);
                }
            };
        }
        return sNewSysSansEnNormal.get();
    }
}
