/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SafeAsyncLayoutInflater.kt
 ** Description : Safe clas for Async LayoutInflater
 ** Version     : 1.0
 ** Date        : 2025/05/19
 ** Author      : <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/05/19     1.0      create
 ***********************************************************************/
package com.soundrecorder.common.utils

import android.app.Activity
import android.view.ViewGroup
import androidx.annotation.LayoutRes
import androidx.asynclayoutinflater.view.AsyncLayoutInflater
import com.soundrecorder.base.utils.DebugUtil
import java.lang.ref.WeakReference

class SafeAsyncLayoutInflater(activity: Activity) {
    companion object {
        private const val TAG = "SafeAsyncLayoutInflater"
    }

    private val activityRef: WeakReference<Activity> = WeakReference<Activity>(activity)

    fun inflate(@LayoutRes resId: Int, parent: ViewGroup?, listener: AsyncLayoutInflater.OnInflateFinishedListener) {
        val activity: Activity? = activityRef.get()
        if (activity == null || activity.isDestroyed) {
            DebugUtil.e(TAG, "inflate failed due to the invalid activity ")
            return
        }

        val inflater = AsyncLayoutInflater(activity)
        inflater.inflate(resId, parent) { view, i, viewGroup ->
            val updatedActivity: Activity? = activityRef.get()
            if (updatedActivity != null && !updatedActivity.isDestroyed) {
                listener.onInflateFinished(view, i, viewGroup)
            } else {
                DebugUtil.e(TAG, "inflate callback failed due to the invalid activity")
            }
        }
    }
}