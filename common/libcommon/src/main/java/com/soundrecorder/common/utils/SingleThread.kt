/*********************************************************************
 * * Copyright (C), 2024, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SingleThread
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/09/19
 * * Author      : ********@@TAG1.TAG2.TAG3.TAG4[.TAG5.TAG6.TAG7]
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.utils

import android.os.Handler
import android.os.HandlerThread
import com.soundrecorder.base.utils.DebugUtil

class SingleThread(private val threadName: String) {

    companion object {
        private const val TAG = "SingleThread"
    }

    private var mAsyncHandlerThread: HandlerThread? = null
    private var mIOHandler: Handler? = null

    @Synchronized
    private fun checkThread() {
        if (mAsyncHandlerThread != null) {
            return
        }
        mAsyncHandlerThread = HandlerThread(threadName).apply {
                    start()
                }
        mAsyncHandlerThread?.let {
            mIOHandler = Handler(it.looper)
        }
        DebugUtil.d(TAG, "checkThread")
    }

    fun post(runnable: Runnable) {
        checkThread()
        mIOHandler?.post(runnable)
    }

    fun postDelay(runnable: Runnable, delay: Long = 0L) {
        checkThread()
        mIOHandler?.removeCallbacks(runnable)
        mIOHandler?.postDelayed(runnable, delay)
    }

    fun quitThread() {
        kotlin.runCatching {
            mIOHandler?.removeCallbacksAndMessages(null)
        }.onFailure {
            DebugUtil.e(TAG, "quitThread ${it.message}")
        }
        kotlin.runCatching {
            mAsyncHandlerThread?.quitSafely()
        }.onFailure {
            DebugUtil.e(TAG, "quitThread ${it.message}")
        }
        mIOHandler = null
        mAsyncHandlerThread = null
        DebugUtil.i(TAG, "quitThread")
    }

    fun clearQueue() {
        kotlin.runCatching {
            mIOHandler?.removeCallbacksAndMessages(null)
        }.onFailure {
            DebugUtil.e(TAG, "clearQueue ${it.message}")
        }
    }

    fun removeCallBack(runnable: Runnable) {
        DebugUtil.e(TAG, "removeCallBack")
        mIOHandler?.removeCallbacks(runnable)
    }
}