/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: FollowHandDialogUtils
 Description:
 Version: 1.0
 Date: 2023/03/13
 Author: W9010241(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 22023/03/13 1.0 create
 */

package com.soundrecorder.common.flexible

import android.content.Intent
import android.view.View
import com.soundrecorder.base.utils.DebugUtil

object FollowHandDialogUtils {

    private const val TAG = "FollowHandDialogUtils"
    private const val START_CHOOSER_ANCHOR_X = "start_chooser_anchor_x"
    private const val START_CHOOSER_ANCHOR_Y = "start_chooser_anchor_y"
    private const val START_CHOOSER_ANCHOR_WIDTH = "start_chooser_anchor_width"
    private const val START_CHOOSER_ANCHOR_HEIGHT = "start_chooser_anchor_height"

    /**
     * 分享跟手弹窗
     * 文档：https://odocs.myoas.com/docs/8Nk6MwMREYfnRJqL
     */
    @JvmStatic
    fun addShareDialogAnchor(anchor: View?, intent: Intent) {
        if (!FollowCOUIAlertDialog.RECORDER_DIALOG_SUPPORT_FOLLOW) {
            DebugUtil.d(TAG, "addShareDialogAnchor: recorder not support follow")
            return
        }
        anchor?.let {
            val width: Int = it.width
            val height: Int = it.height
            val location = IntArray(2)
            it.getLocationInWindow(location)
            val clickX = location[0].toFloat()
            val clickY = location[1] - height / 2f
            intent.putExtra(START_CHOOSER_ANCHOR_X, clickX.toInt())
            intent.putExtra(START_CHOOSER_ANCHOR_Y, clickY.toInt())
            intent.putExtra(START_CHOOSER_ANCHOR_WIDTH, width)
            intent.putExtra(START_CHOOSER_ANCHOR_HEIGHT, height)
            DebugUtil.d(
                TAG,
                "locationX == " + location[0] + " locationY == " + location[1] + " clickX == $clickX" +
                        " clickY == $clickY" + " width == " + width + " height == " + height
            )
        }
    }
}