/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  FunctionOption
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.utils

import android.content.Context
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil.isAndroidTOrLater
import com.soundrecorder.base.utils.BaseUtil.isLightOS
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.base.utils.OS12FeatureUtil.isSuperSoundRecorderEpicEffective
import com.soundrecorder.common.permission.PermissionUtils

object FunctionOption {

    const val OPLUS_SPEED_FEATURE = true

    const val IS_SUPPORT_SEEKPLAY_FEATURE = true

    const val IS_SUPPORT_NEW_UI_OF13 = true

    //是否支持图文混排
    const val IS_SUPPORT_MIX_LAYOUT = false

    //是否支持页面内搜索
    const val IS_SUPPORT_CONVERT_SEARCH = IS_SUPPORT_NEW_UI_OF13

    //是否支持提取关键词
    const val IS_SUPPORT_EXTRACT_KEY_WORDS = IS_SUPPORT_NEW_UI_OF13 && false

    /**
     * 保存至便签是否支持图文混排
     */
    const val IS_SUPPORT_SAVE_TO_NOTE_MIX = IS_SUPPORT_NEW_UI_OF13

    //是否支持图文导出DOC
    const val IS_SUPPORT_MIX_EXPORT_DOC = IS_SUPPORT_NEW_UI_OF13

    /**
     * is open feature: photo mark recommend
     */
    const val IS_SUPPORT_PHOTO_RECOMMEND = true

    /**
     * wav、aac录制需求
     */
    const val IS_SUPPORT_WAV_AND_AAC = true

    // 手机是否需要横屏
    var PHONE_NEED_HORIZONTAL = false

    /*OPPO_MULTIPLE_ENTRANCE_FEATURE is used to determine whether to enter the
    recording application from the three-way entrance*/
    private var OPLUS_MULTIPLE_ENTRANCE_FEATURE = false

    /*FLAG_ENTRANCE_FEATURE_RECORD is used to record whether the last time you
    entered the interface from the three-way entrance or from the recording app.*/
    private var FLAG_ENTRANCE_FEATURE_RECORD = false

    /**
     * Read the feature of speech-to-text,
     * and determine if you need to access the speech-to-text feature
     */
    @JvmStatic
    fun loadSpeechToTextFeature(isMainProcess: Boolean = true): Boolean {
        val isLightOS = isLightOS()
        val enable =
            !FeatureOption.OPLUS_VERSION_EXP && isMainProcess && !isLightOS
        DebugUtil.d(FeatureOption.TAG, "loadSpeechToTextFeature: $enable")
        return enable
    }

    @JvmStatic
    fun setFlagEntranceFeatureRecord(record: Boolean) {
        FLAG_ENTRANCE_FEATURE_RECORD = record
    }

    @JvmStatic
    fun getFlagEntranceFeatureRecord(): Boolean {
        return FLAG_ENTRANCE_FEATURE_RECORD
    }

    @JvmStatic
    fun isSupportPhotoMark(): Boolean {
        return isSuperSoundRecorderEpicEffective()
    }

    @JvmStatic
    fun isSupportPhotoMarkRecommend(): Boolean {
        if (!IS_SUPPORT_PHOTO_RECOMMEND) {
            return false
        }
        val sp = BaseApplication.getAppContext().getSharedPreferences(
            BaseApplication.getAppContext().packageName + "_preferences",
            Context.MODE_PRIVATE
        )
        val isOpened = sp.getBoolean(FeatureOption.SETTING_RECORD_PICTURE_RECOMMENDATION, true)
        if (!isOpened) {
            return false
        }
        return if (isAndroidTOrLater) {
            PermissionUtils.hasReadImagesPermission()
        } else {
            true
        }
    }

    @JvmStatic
    fun putSupportPhotoMarkRecommend(isOpen: Boolean) {
        val sp = BaseApplication.getAppContext().getSharedPreferences(
            BaseApplication.getAppContext().packageName + "_preferences",
            Context.MODE_PRIVATE
        )
        if (sp != null) {
            val editor = sp.edit()
            editor?.putBoolean(FeatureOption.SETTING_RECORD_PICTURE_RECOMMENDATION, isOpen)?.apply()
        }
    }

    /**
     * FindX5默认使用讯飞转文本服务，并且每一次转文本成功后都会保存转服务转文本服务code
     * Epic 7594746: 【录音】和【语音转文字】ASR能力全部切换为自研 后，讯飞切换为自研
     */
    @JvmStatic
    fun hasSupportXunFei(): Boolean {
        return OS12FeatureUtil.isFindX4()
    }

    /**
     * konka、yala、dodge 使用字节转文本服务
     */
    @JvmStatic
    fun hasSupportByte(): Boolean {
        return OS12FeatureUtil.isFindKYD()
    }

    /**
     * 是否卖场模式：
     * 1、有卖场模式feature
     * 2、折叠屏手机：蜻蜓、孔雀、白天鹅
     */
    @JvmStatic
    fun isSupportSellMode(): Boolean {
        return FeatureOption.isHasSellMode() && FeatureOption.getIsFoldFeature()
    }
}