package com.soundrecorder.common.buryingpoint

import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.utils.FoldStateLiveData
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.modulerouter.recorder.IBuryingPointRealTimeSubtitle

object BuryingPoint : RecorderUserAction() {

    private val TAG = "BuryingPoint"
    private val context = BaseApplication.getAppContext()

    @JvmStatic
    fun addRecordMode(mode: String) {
        val info = HashMap<String, String>()
        val modeCode = if (RecordModeUtil.isSupportMultiRecordMode(context)) mode else "-1"
        info[KEY_RECORD_MODE] = modeCode
        addCommonUserAction(context, USER_ACTION_RECORDING_FINISH_TAG, EVENT_FINISH_RECORDING_SAVE, info, false)
        DebugUtil.i(TAG, "addRecordMode")
    }

    @JvmStatic
    fun addRecordMode(mode: String, nameEdited: String) {
        val info = HashMap<String, String>()
        val modeCode = if (RecordModeUtil.isSupportMultiRecordMode(context)) mode else "-1"
        info[KEY_RECORD_MODE] = modeCode
        info[KEY_IS_EDITED] = nameEdited
        addCommonUserAction(context, USER_ACTION_RECORDING_FINISH_TAG, EVENT_FINISH_RECORDING_SAVE, info, false)
        DebugUtil.i(TAG, "addRecordMode")
    }

    @JvmStatic
    fun addRecordType(typePosition: Int) {
        if (RecordModeUtil.isSupportMultiRecordMode(context)) {
            val info = HashMap<String, String>()
            info[KEY_RECORD_TYPE] = typePosition.toString()
            addNewCommonUserAction(context, USER_ACTION_RECORDER_TYPE, EVENT_RECORDER_TYPE, info, false)
            DebugUtil.i(TAG, "addRecordType")
        }
    }

    /**
     * 录音使用总时长
     */
    @JvmStatic
    fun addRecordDurationMessage(duration: Long) {
        val info = HashMap<String, String>()
        info[KEY_DUR_MS] = duration.toString()
        addNewCommonUserAction(context, USER_ACTION_RECORDER_DURATION, FUNCTION_USAGE_INFO, info, false)
        DebugUtil.i(TAG, "addRecordDurationMessage")
    }

    @JvmStatic
    fun addRecordDuration(duration: Long) {
        val info = HashMap<String, String>()
        info[KEY_RECORD_DURATION] = duration.toString()
        addNewCommonUserAction(context, USER_ACTION_RECORDER_DURATION, EVENT_RECORDER_DURATION, info, false)
        DebugUtil.i(TAG, "addRecordDuration")
    }

    /**
     * 单次完整录制音频时间
     * record_duration: 单次录音时长
     * file_id:录音编号
     * entry_from: 入口来源 Launcher -> 0  sidebar -> 1 card -> 2 cube_button -> 3
     * rec_type: 录音模式 standard -> 0 meeting -> 1 interview -> 2
     * return: 保存 -> 1 取消 -> 2 杀掉进程 -> 0
     */
    @JvmStatic
    fun addRecordDurationNew(duration: Long, recordType: Int, fileId: String) {
        val info = HashMap<String, String>()
        info[KEY_RECORD_DURATION] = duration.toString()
        info[KEY_ENTRY_FROM] = RecorderUserActionKt.sValueEntryFrom
        info[KEY_REC_TYPE] = recordType.toString()
        info[KEY_RETURN] = RecorderUserActionKt.sSaveRecord.toString()
        info[KEY_FILE_ID] = fileId
        addNewCommonUserAction(context, USER_ACTION_RECORDER_DURATION, EVENT_RECORDER_DURATION_NEW, info, false)
        DebugUtil.i(TAG, "addRecordDurationNew")
    }

    /**
     * 入口启动
     * entryFrom: 入口来源 Launcher -> 0  sidebar-> 1 card-> 2 cube_button-> 3
     * entryType: 启动类型 进入录音app -> 1 启动录制音频 -> 2
     * landingType: 落地页前后台 前台 -> 1 后台 -> 2
     */
    @JvmStatic
    fun addRecordEntryLaunch(entryType: String, landingType: Int) {
        val info = HashMap<String, String>()
        info[KEY_ENTRY_FROM] = RecorderUserActionKt.sValueEntryFrom
        info[KEY_ENTRY_TYPE] = entryType
        info[KEY_LANDING_TYPE] = landingType.toString()
        addNewCommonUserAction(context, USER_ACTION_ENTERING_RECORDING, ENTRY_LAUNCH, info, false)
        DebugUtil.i(TAG, "addRecordEntryLaunch")
    }

    /**
     * 设置页按钮点击
     * name: 按钮名称
     * option: 按钮选项
     */
    @JvmStatic
    fun addRecordSettingIconClick(name: String, option: String) {
        val info = HashMap<String, String>()
        info[KEY_NAME] = name
        info[KEY_OPTION] = option
        addNewCommonUserAction(context, USER_ACTION_SETTING, SETTING_ICON_CLICK, info, false)
        DebugUtil.i(TAG, "addRecordSettingIconClick")
    }

    /**
     * 搜索
     * search: 搜索场景 首页搜索 -> 0 详情页搜索 -> 1
     * playCount: 点击快捷播放次数（仅首页上报）
     * infoCount: 点击播放详情页次数（仅首页上报）
     */
    @JvmStatic
    fun addRecordSearchDurRecords(search: String, playCount: Int, infoCount: Int) {
        val info = HashMap<String, String>()
        info[KEY_SEARCH_SCENE] = search
        info[KEY_PLAY_COUNT] = playCount.toString()
        info[KEY_INFO_COUNT] = infoCount.toString()
        addNewCommonUserAction(context, USER_ACTION_SEARCH_RECORDS, SEARCH_DUR_RECORD, info, false)
        DebugUtil.i(TAG, "addRecordSearchDurRecords")
    }

    /**
     * 讲话人编辑
     * file_id:录音编号
     * from： 来源：录音转文本 -> 0
     * appliedAll： 应用到全部讲话人
     * editCount: 编辑次数
     */
    @JvmStatic
    fun addRecordSpeakerEdit(fileId: String, from: String, appliedAll: String, editCount: Long) {
        val info = HashMap<String, String>()
        info[KEY_FILE_ID] = fileId
        info[KEY_EDIT_FROM] = from
        info[KEY_IF_APPLIED_ALL] = appliedAll
        info[KEY_EDIT_COUNT] = editCount.toString()
        addNewCommonUserAction(context, USER_ACTION_ENTERING_RECORDING, SPEAKER_EDIT, info, false)
        DebugUtil.i(TAG, "addRecordSpeakerEdit")
    }

    /**
     * 录音分组状态
     */
    @JvmStatic
    fun addRecordGroupState(group: String, names: String) {
        val info = HashMap<String, String>()
        info[KEY_GROUP_INFO] = group
        info[KEY_GROUP_NAMES] = names
        addNewCommonUserAction(context, USER_ACTION_RECORD_GROUP, RECORDING_GROUPING_STATE, info, false)
        DebugUtil.i(TAG, "addRecordGroupState")
    }

    /**
     * 录音分组创建
     *
     * @JvmStatic
     *     fun addRecordGroupCreate(action: String, type: String, names: String) {
     *         val info = HashMap<String, String>()
     *         info[KEY_GROUP_ACTION] = VALUE_GROUP_ACTION
     *         info[KEY_GROUP_TYPE] = type
     *         addNewCommonUserAction(context, EVENT_PLAY_DEVICE_CHANGE, RECORDING_GROUPING_CREATE, info, false)
     *         DebugUtil.i(TAG, "addRecordGroupCreate")
     *     }
     */


    /**
     * 录音整体分享
     *
     * @JvmStatic
     *     fun addRecordShare(action: String, type: String, returnType: Int, failReason: String) {
     *         val info = HashMap<String, String>()
     *         addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, RECORDING_WHOLE_SHARE, info, false)
     *         DebugUtil.i(TAG, "addRecordShare")
     *     }
     */

    @JvmStatic
    fun addRecordDelete(mode: String) {
        val info = HashMap<String, String>()
        val modeCode = if (RecordModeUtil.isSupportMultiRecordMode(context)) mode else "-1"
        info[KEY_RECORD_MODE] = modeCode
        addCommonUserAction(context, USER_ACTION_RECORDING_FINISH_TAG, EVENT_FINISH_RECORDING_DELETE, info, false)
        DebugUtil.i(TAG, "addRecordDelete")
    }

    @JvmStatic
    fun addRecordPlayDeviceBrowse(value: String) {
        val info = HashMap<String, String>()
        info[KEY_RECORD_PLAY_DEVICE_BROWSE] = value
        addCommonUserAction(context, USER_ACTION_PLAY_DEVICE_CHANGE, EVENT_PLAY_DEVICE_CHANGE, info, true)
        DebugUtil.i(TAG, "addRecordPlayDeviceBrowse")
    }

    @JvmStatic
    fun addRecordPlayDevice(value: String) {
        val info = HashMap<String, String>()
        info[KEY_RECORD_PLAY_DEVICE] = value
        addCommonUserAction(context, USER_ACTION_PLAY_DEVICE_CHANGE, EVENT_PLAY_DEVICE_CHANGE, info, true)
        DebugUtil.i(TAG, "addRecordPlayDevice")
    }

    @JvmStatic
    fun addRecordStartType(recordFrom: String) {
        val info = HashMap<String, String>()
        info[KEY_START_RECORD_TYPE] = recordFrom
        addNewCommonUserAction(context, USER_ACTION_START_RECORD_TYPE, EVENT_START_RECORD_FROM, info, true)
    }

    @JvmStatic
    fun addSyncSwitch(switchState: String) {
        val info = HashMap<String, String>()
        info[KEY_SYNC_SWITCH] = switchState
        addNewCommonUserAction(context, USER_ACTION_SYNC_SWITCH, EVENT_SYNC_SWITCH, info, true)
    }

    @JvmStatic
    fun addRecordPlayState(playState: String) {
        val info = HashMap<String, String>()
        info[KEY_RECORD_PLAY_STATUS] = playState
        addNewCommonUserAction(context, USER_ACTION_RECORD_PLAY_STATUS, EVENT_RECORD_PLAY_STATUS, info, true)
    }

    @JvmStatic
    fun addMarkDelete(mRecordType: Int) {
        val eventInfo = java.util.HashMap<String?, String?>()
        if (RecordModeUtil.isSupportMultiRecordMode(BaseApplication.getAppContext())) {
            eventInfo["recordmode"] = mRecordType.toString()
        } else {
            eventInfo["recordmode"] = "-1"
        }
        addCommonUserAction(
            BaseApplication.getAppContext(),
                USER_ACTION_MARK_TAG,
                EVENT_DELETE_MARK_TAG_FROM_PLAYBACK,
                eventInfo, false)
    }

    @JvmStatic
    fun addMarkRename(mRecordType: Int) {
        val eventInfo = java.util.HashMap<String?, String?>()
        if (RecordModeUtil.isSupportMultiRecordMode(BaseApplication.getAppContext())) {
            eventInfo["recordmode"] = mRecordType.toString()
        } else {
            eventInfo["recordmode"] = "-1"
        }
        addCommonUserAction(
            BaseApplication.getAppContext(),
                USER_ACTION_MARK_TAG,
                EVENT_RENAME_MARK_TAG_FROM_PLAYBACK,
                eventInfo, false)
    }

    @JvmStatic
    fun addMarkAdd(mRecordType: Int) {
        val eventInfo = java.util.HashMap<String?, String?>()
        if (RecordModeUtil.isSupportMultiRecordMode(BaseApplication.getAppContext())) {
            eventInfo["mode"] = mRecordType.toString()
        } else {
            eventInfo["mode"] = "-1"
        }
        addCommonUserAction(
            BaseApplication.getAppContext(),
                USER_ACTION_MARK_TAG,
                EVENT_MARK_TAG_FROM_PLAYBACK,
                eventInfo, false)
    }

//    fun addSeekToMark(mRecordType: Int) {
//        val eventInfo = HashMap<String?, String?>()
//        if (BaseUtil.isSupportMultiRecordMode(RecorderApplication.getAppContext())) {
//            eventInfo["recordmode"] = mRecordType.toString()
//        } else {
//            eventInfo["recordmode"] = "-1"
//        }
//        addCommonUserAction(RecorderApplication.getAppContext(),
//                RecorderUserAction.USER_ACTION_MARK_TAG,
//                RecorderUserAction.EVENT_SEEK_TO_MARK_TAG_WHEN_PLAYBACK,
//                eventInfo, false)
//    }

    @JvmStatic
    fun addMarkWhenUnplay() {
        val newEventInfo: MutableMap<String?, String?> = java.util.HashMap()
        newEventInfo[KEY_MARK_UNPLAY] = VALUE_MARK_UNPLAY
        addNewCommonUserAction(
            BaseApplication.getAppContext(), USER_ACTION_MARK_TAG,
                EVENT_MARK_UNPLAY, newEventInfo, false)
    }

    @JvmStatic
    fun addMarkBtnClickFromNotification(isFromLockScreen: Boolean, recordStatus: Int) {
        val isFoldCloseScreen = FeatureOption.isHasSupportDragonfly() && FoldStateLiveData.hasFoldingClose()
        val eventInfo: MutableMap<String?, String?> = HashMap()
        when (recordStatus) {
            NotificationUtils.NOTIFICATION_PAGE_RECORD -> {
                if (isFoldCloseScreen) {
                    if (isFromLockScreen) {
                        eventInfo[KEY_NOTIFICATION_MARK_LOCK] = VALUE_NOTIFICATION_MARK_LOCK_SECOND_RECORDING
                    } else {
                        eventInfo[KEY_NOTIFICATION_MARK_UNLOCK] = VALUE_NOTIFICATION_MARK_UNLOCK_SECOND_RECORDING
                    }
                } else {
                    if (isFromLockScreen) {
                        eventInfo[KEY_NOTIFICATION_MARK_LOCK] = VALUE_NOTIFICATION_MARK_LOCK_NORMAL_RECORDING
                    } else {
                        eventInfo[KEY_NOTIFICATION_MARK_UNLOCK] = VALUE_NOTIFICATION_MARK_UNLOCK_NORMAL_RECORDING
                    }
                }
            }
            else -> {
                if (isFoldCloseScreen) {
                    if (isFromLockScreen) {
                        eventInfo[KEY_NOTIFICATION_MARK_LOCK] = VALUE_NOTIFICATION_MARK_LOCK_SECOND_PLAYBACK
                    } else {
                        eventInfo[KEY_NOTIFICATION_MARK_UNLOCK] = VALUE_NOTIFICATION_MARK_UNLOCK_SECOND_PLAYBACK
                    }
                } else {
                    if (isFromLockScreen) {
                        eventInfo[KEY_NOTIFICATION_MARK_LOCK] = VALUE_NOTIFICATION_MARK_LOCK_NORMAL_PLAYBACK
                    } else {
                        eventInfo[KEY_NOTIFICATION_MARK_UNLOCK] = VALUE_NOTIFICATION_MARK_UNLOCK_NORMAL_PLAYBACK
                    }
                }
            }
        }
        addCommonUserAction(
            BaseApplication.getAppContext(), USER_ACTION_NOTIFICATION,
                if (isFromLockScreen) {
                    EVENT_NOTIFICATION_LOCK_SCREEN_MARK_BTN_CLICK
                } else {
                    EVENT_NOTIFICATION_STATUS_BAR_MARK_BTN_CLICK
                }, eventInfo, false)
    }

    @JvmStatic
    fun addPlayBtnClickFromNotification(
        isFromLockScreen: Boolean,
        isPausedClick: Boolean,
        fromRecord: Int
    ) {
        val isFoldCloseScreen = FeatureOption.isHasSupportDragonfly() && FoldStateLiveData.hasFoldingClose()
        val eventInfo = playBtnClickFromNotification(
            isFromLockScreen,
            isPausedClick,
            fromRecord,
            isFoldCloseScreen
        )
        val eventId = getPlayBtnClickFromNotificationEventId(isFromLockScreen, isPausedClick)
        addCommonUserAction(
            BaseApplication.getAppContext(),
            USER_ACTION_NOTIFICATION,
            eventId,
            eventInfo,
            false
        )
    }

    private fun getPlayBtnClickFromNotificationEventId(
        isFromLockScreen: Boolean,
        isPausedClick: Boolean
    ): Int {
        return if (isFromLockScreen) {
            if (isPausedClick) {
                EVENT_NOTIFICATION_LOCK_SCREEN_PUASE_BTN_CLICK
            } else {
                EVENT_NOTIFICATION_LOCK_SCREEN_PLAY_BTN_CLICK
            }
        } else {
            if (isPausedClick) {
                EVENT_NOTIFICATION_STATUS_BAR_PAUSE_BTN_CLICK
            } else {
                EVENT_NOTIFICATION_STATUS_BAR_PLAY_BTN_CLICK
            }
        }
    }

    private fun playBtnClickFromNotification(
        isFromLockScreen: Boolean,
        isPausedClick: Boolean,
        fromRecord: Int,
        isFoldCloseScreen: Boolean,
    ): MutableMap<String?, String?> {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        when (fromRecord) {
            NotificationUtils.NOTIFICATION_PAGE_RECORD -> {
                if (isFoldCloseScreen) {
                    if (isFromLockScreen && isPausedClick) {
                        eventInfo[KEY_NOTIFICATION_PAUSE_LOCK] = VALUE_NOTIFICATION_PAUSE_LOCK_SECOND_RECORDING
                    } else if (isFromLockScreen) {
                        eventInfo[KEY_NOTIFICATION_PLAY_LOCK] = VALUE_NOTIFICATION_PLAY_LOCK_SECOND_RECORDING
                    } else if (isPausedClick) {
                        eventInfo[KEY_NOTIFICATION_PAUSE_UNLOCK] = VALUE_NOTIFICATION_PAUSE_UNLOCK_SECOND_RECORDING
                    } else {
                        eventInfo[KEY_NOTIFICATION_PLAY_UNLOCK] = VALUE_NOTIFICATION_PLAY_UNLOCK_SECOND_RECORDING
                    }
                } else {
                    if (isFromLockScreen && isPausedClick) {
                        eventInfo[KEY_NOTIFICATION_PAUSE_LOCK] = VALUE_NOTIFICATION_PAUSE_LOCK_NORMAL_RECORDING
                    } else if (isFromLockScreen) {
                        eventInfo[KEY_NOTIFICATION_PLAY_LOCK] = VALUE_NOTIFICATION_PLAY_LOCK_NORMAL_RECORDING
                    } else if (isPausedClick) {
                        eventInfo[KEY_NOTIFICATION_PAUSE_UNLOCK] = VALUE_NOTIFICATION_PAUSE_UNLOCK_NORMAL_RECORDING
                    } else {
                        eventInfo[KEY_NOTIFICATION_PLAY_UNLOCK] = VALUE_NOTIFICATION_PLAY_UNLOCK_NORMAL_RECORDING
                    }
                }
            }

            else -> {
                if (isFoldCloseScreen) {
                    if (isFromLockScreen && isPausedClick) {
                        eventInfo[KEY_NOTIFICATION_PAUSE_LOCK] = VALUE_NOTIFICATION_PAUSE_LOCK_SECOND_PLAYBACK
                    } else if (isFromLockScreen) {
                        eventInfo[KEY_NOTIFICATION_PLAY_LOCK] = VALUE_NOTIFICATION_PLAY_LOCK_SECOND_PLAYBACK
                    } else if (isPausedClick) {
                        eventInfo[KEY_NOTIFICATION_PAUSE_UNLOCK] = VALUE_NOTIFICATION_PAUSE_UNLOCK_SECOND_PLAYBACK
                    } else {
                        eventInfo[KEY_NOTIFICATION_PLAY_UNLOCK] = VALUE_NOTIFICATION_PLAY_UNLOCK_SECOND_PLAYBACK
                    }
                } else {
                    if (isFromLockScreen && isPausedClick) {
                        eventInfo[KEY_NOTIFICATION_PAUSE_LOCK] = VALUE_NOTIFICATION_PAUSE_LOCK_NORMAL_PLAYBACK
                    } else if (isFromLockScreen) {
                        eventInfo[KEY_NOTIFICATION_PLAY_LOCK] = VALUE_NOTIFICATION_PLAY_LOCK_NORMAL_PLAYBACK
                    } else if (isPausedClick) {
                        eventInfo[KEY_NOTIFICATION_PAUSE_UNLOCK] = VALUE_NOTIFICATION_PAUSE_UNLOCK_NORMAL_PLAYBACK
                    } else {
                        eventInfo[KEY_NOTIFICATION_PLAY_UNLOCK] = VALUE_NOTIFICATION_PLAY_UNLOCK_NORMAL_PLAYBACK
                    }
                }
            }
        }
        return eventInfo
    }

    @JvmStatic
    fun addPlayFromAndDuration(from: String, recordMode: String, duration: Long) {
        val info = HashMap<String, String>()
        info[KEY_PLAY_FROM] = from
        info[KEY_PLAY_DURATION] = duration.toString()
        info[KEY_PLAY_MODE] = recordMode
        addCommonUserAction(context, USER_ACTION_MAIN_VIEW_TAG, EVENT_BROWSERFILE_FILE_PLAY, info, true)
    }

    /**
     * 统计用户倍速播放情况
     */
    @JvmStatic
    fun addMultipleSpeed(position: Int) {
        val info = HashMap<String, String>()
        info[KEY_MULTIPLE_SPEED] = when (position) {
            0 -> VALUE_MULTIPLE_SPEED_HALF
            1 -> VALUE_MULTIPLE_SPEED_NORMAL
            2 -> VALUE_MULTIPLE_SPEED_ONE_DOT_FIVE
            else -> VALUE_MULTIPLE_SPEED_TWICE
        }
        addNewCommonUserAction(context, USER_ACTION_DOUBLE_SPEED, EVENT_DOUBLE_SPEED, info, true)
    }

    /**
     * "0":"用户点击搜索的次数"
     * "1":"取消搜索的次数",
     * "2":"用户搜索后点击搜索结果快捷播放的次数",
     * "3":"用户搜索后点击搜索结果详情页播放的次数"
     */
    @JvmStatic
    fun addSearch(search: String) {
        val info = HashMap<String, String>()
        info[KEY_SEARCH_RECORDS] = search
        addNewCommonUserAction(context, USER_ACTION_SEARCH_RECORDS, EVENT_SEARCH_RECORDS, info, true)
    }

    @JvmStatic
    fun recordingAddPictureByCamera() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_RECORDING_TAKE_PICTURE] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_RECORDING, eventInfo, false)
    }

    @JvmStatic
    fun recordingAddPictureByCameraOk() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_RECORDING_PICTURE_OK] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_RECORDING, eventInfo, false)
    }

    @JvmStatic
    fun recordingAddPictureByCameraCancel() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_RECORDING_PICTURE_CANCEL] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_RECORDING, eventInfo, false)
    }

    @JvmStatic
    fun playingAddPictureByCamera() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_PLAY_TAKE_PICTURE] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_PLAYBACK, eventInfo, false)
    }

    @JvmStatic
    fun playingAddPictureByCameraCancel() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_PLAY_PICTURE_CANCEL] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_PLAYBACK, eventInfo, false)
    }

    @JvmStatic
    fun playingAddPictureByCameraNumber() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_PLAY_TAKE_PICTURE_NUM] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_PLAYBACK, eventInfo, false)
    }

    @JvmStatic
    fun playingAddPictureByAlbum() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_PLAY_ALBUM_PICTURE_NUM] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_PLAYBACK, eventInfo, false)
    }

    @JvmStatic
    fun playingAddPictureByAlbumCancel() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_PLAY_ALBUM_PICTURE_CANCEL] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_PLAYBACK, eventInfo, false)
    }

    @JvmStatic
    fun playingAddPictureByAlbumOk() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_PLAY_ALBUM_PICTURE_OK] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_PLAYBACK, eventInfo, false)
    }

    @JvmStatic
    fun playingAddPictureDialogNotOnClickNumber() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_PLAY_PICTURE_DIALOG_SHOW_NOT_ONCLICK_NUM] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_PLAYBACK, eventInfo, false)
    }

    @JvmStatic
    fun pictureMarkOnClickNumber() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_PICTURE_ONCLICK_NUM] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_PLAYBACK, eventInfo, false)
    }

    /**
     * @param
     * @see VALUE_RECORD_AUDIO_FORMAT_WAV
     * @see VALUE_RECORD_AUDIO_FORMAT_AAC
     * @see VALUE_RECORD_AUDIO_FORMAT_MP3
     */
    @JvmStatic
    fun selectAudioFormatClicked(formatValue: String) {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_RECORD_AUDIO_FORMAT] = formatValue
        addNewCommonUserAction(context, USER_ACTION_RECORD_AUDIO_FORMAT, EVENT_RECORD_AUDIO_FORMAT, eventInfo, false)
    }

    @JvmStatic
    fun addSkipMuteSwitch(switchState: String) {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_SKIP_MUTE] = switchState
        addNewCommonUserAction(context, USER_ACTION_PLAY_SETTING, EVENT_SKIP_MUTE, eventInfo, true)
    }

    /**
     * 智能图片标记弹窗显示次数统计
     */
    @JvmStatic
    fun shouPopNumber() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_COUNT_SHOW_POP] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_SHOW_SMART_MARK, EVENT_COUNT_SHOW_POP, eventInfo, false)
    }

    /**
     * 用户点击智能图片标记弹窗次数
     */
    @JvmStatic
    fun clickOnPopNumber() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_COUNT_CLICK_ON_POP] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_SHOW_SMART_MARK, EVENT_COUNT_CLICK_ON_POP, eventInfo, false)
    }

    /**
     * 智能图片标记弹窗显示次数，不包含用户点击后消失次数
     */
    @JvmStatic
    fun dismissPopNumberExcludeUserAction() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_COUNT_DISMISS_POP_EXCLUDE_USER_ACTION] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_SHOW_SMART_MARK, EVENT_COUNT_DISMISS_POP_EXCLUDE_USER_ACTION, eventInfo, false)
    }

    /**
     * 智能图片标记取消添加图片标记次数
     */
    @JvmStatic
    fun cancelAddPictureMarkNumber() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_COUNT_CANCEL_WHEN_ADD_PICTURE_MARK] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_SHOW_SMART_MARK, EVENT_COUNT_CANCEL_WHEN_ADD_PICTURE_MARK, eventInfo, false)
    }

    /**
     * 智能图片标记确认添加图片标记次数
     */
    @JvmStatic
    fun oKAddPictureMarkNumber() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_COUNT_OK_WHEN_ADD_PICTURE_MARK] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_SHOW_SMART_MARK, EVENT_COUNT_OK_WHEN_ADD_PICTURE_MARK, eventInfo, false)
    }

    @JvmStatic
    fun showPhotoViewerNumber() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_COUNT_SHOW_PHOTO_VIEWER] = DEFAULT_VALUE
        addCommonUserAction(context, USER_ACTION_SHOW_SMART_MARK, EVENT_COUNT_SHOW_PHOTO_VIEWER, eventInfo, false)
    }

    @JvmStatic
    fun seekToMarkTagWhenPlayback(mRecordType: Int?) {
        val eventInfo = java.util.HashMap<String?, String?>()
        if (RecordModeUtil.isSupportMultiRecordMode(context)) {
            eventInfo["recordmode"] = mRecordType?.toString()
        } else {
            eventInfo["recordmode"] = "-1"
        }
        addCommonUserAction(
            context, USER_ACTION_MARK_TAG, EVENT_SEEK_TO_MARK_TAG_WHEN_PLAYBACK, eventInfo, false
        )
    }

    @JvmStatic
    fun seekToMarkTagWhenCutting(mRecordType: Int?) {
        val eventInfo = java.util.HashMap<String?, String?>()
        if (RecordModeUtil.isSupportMultiRecordMode(context)) {
            eventInfo["recordmode"] = mRecordType?.toString()
        } else {
            eventInfo["recordmode"] = "-1"
        }
        addNewCommonUserAction(context, USER_ACTION_TRIM, EVENT_TRIM, eventInfo, false)
    }

    /**
     * 点击用户须知弹窗“同意并使用”
     */
    @JvmStatic
    fun doClickOkOnUserNoticeDialog() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_COUNT_CLICK_OK_ON_USER_NOTICE_DIALOG] = DEFAULT_VALUE
        addCommonUserAction(
            BaseApplication.getAppContext(),
            USER_ACTION_PERSONAL_PRIVACY_POLICY,
            EVENT_COUNT_CLICK_OK_ON_USER_NOTICE_DIALOG,
            eventInfo,
            false
        )
    }

    /**
     * 点击使用基本功能弹窗“开始使用”
     */
    @JvmStatic
    fun doClickOkOnUserNoticeBasicDialog() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_COUNT_CLICK_OK_ON_USER_NOTICE_BASIC_DIALOG] = DEFAULT_VALUE
        addCommonUserAction(
            BaseApplication.getAppContext(),
            USER_ACTION_PERSONAL_PRIVACY_POLICY,
            EVENT_COUNT_CLICK_OK_ON_USER_NOTICE_BASIC_DIALOG,
            eventInfo,
            false
        )
    }

    /**
     * 点击仍可以使用基本功能弹窗“开始使用”
     */
    @JvmStatic
    fun doClickOkOnUserNoticeBasicStillDialog() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_COUNT_CLICK_OK_ON_USER_NOTICE_BASIC_STILL_DIALOG] = DEFAULT_VALUE
        addCommonUserAction(
            BaseApplication.getAppContext(),
            USER_ACTION_PERSONAL_PRIVACY_POLICY,
            EVENT_COUNT_CLICK_OK_ON_USER_NOTICE_BASIC_STILL_DIALOG,
            eventInfo,
            false
        )
    }

    /**
     * 点击转文本权限弹窗“同意并使用”
     */
    @JvmStatic
    fun doClickOkOnConvertPermissionDialog() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_COUNT_CLICK_OK_ON_CONVERT_PERMISSION_DIALOG] = DEFAULT_VALUE
        addCommonUserAction(
            BaseApplication.getAppContext(),
            USER_ACTION_PERSONAL_PRIVACY_POLICY,
            EVENT_COUNT_CLICK_OK_ON_CONVERT_PERMISSION_DIALOG,
            eventInfo,
            false
        )
    }

    /**
     * 点击转文本撤回权限弹窗“撤回”
     */
    @JvmStatic
    fun doClickOkOnConvertPermissionWithdrawnDialog() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_COUNT_CLICK_OK_ON_CONVERT_PERMISSION_WITHDRAWN_DIALOG] = DEFAULT_VALUE
        addCommonUserAction(
            BaseApplication.getAppContext(),
            USER_ACTION_PERSONAL_PRIVACY_POLICY,
            EVENT_COUNT_CLICK_OK_ON_CONVERT_PERMISSION_WITHDRAWN_DIALOG,
            eventInfo,
            false
        )
    }

    @JvmStatic
    fun addActionForPlaybackRename() {
        val eventInfoRename: MutableMap<String?, String?> = HashMap()
        eventInfoRename[KEY_MORE_RENAME] = VALUE_MORE_RENAME
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfoRename, false)
    }

    @JvmStatic
    fun addActionForPlaybackRenameSuccess() {
        val eventInfoRename: MutableMap<String?, String?> = HashMap()
        eventInfoRename[KEY_PLAY_MORE_RENAME_SUCCESS] = DEFAULT_VALUE
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfoRename, false)
    }

    @JvmStatic
    fun addActionForRenameBtnClick(value: String) {
        val eventInfoRename: MutableMap<String?, String?> = HashMap()
        eventInfoRename[KEY_PLAY_MORE_RENAME_BTN_CLICK] = value
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfoRename, false)
    }

    /**
     * 点击主页更多中的编辑
     */
    @JvmStatic
    fun addClickMoreEdit() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_PLAY_MORE_EDIT] = VALUE_PLAY_MORE_EDIT
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfo, false)
    }

    /**
     * 点击主页更多中的设置
     */
    @JvmStatic
    fun addClickMoreSetting() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_PLAY_MORE_SETTING] = VALUE_PLAY_MORE_SETTING
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfo, false)
    }

    /**
     * 点击主页标题或者倒三角形呼出录音分组面板
     */
    @JvmStatic
    fun addClickRecordGroupPanel(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_RECORD_GROUP_PANEL] = value
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_MAIN_VIEW_TAG, EVENT_RECORD_GROUP_PANEL, eventInfo, false)
    }

    /**
     * 点击主页中的录音文件
     */
    @JvmStatic
    fun addClickBrowseRecordFile(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_BROWSE_RECORD_FILE] = value
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_MAIN_VIEW_TAG, EVENT_BROWSERFILE_FILE_PLAY, eventInfo, false)
    }

    /**
     * 录音暂停、继续
     */
    @JvmStatic
    fun addClickRecordState(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_RECORD_STATE] = value
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_ENTERING_RECORDING, EVENT_START_RECORD_AUDIO, eventInfo, false)
    }

    /**
     * 定向录音按钮开启、关闭
     * @param from 来源，0——recorderActivity
     */
    @JvmStatic
    fun addClickDirectRecordStatus(value: String, fileId: String, from: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[EVENT_DIRECT_RECORD_BUTTON_STATE] = value
        eventInfo[EVENT_DIRECT_RECORD_FILE_ID] = fileId
        eventInfo[EVENT_DIRECT_RECORD_FROM] = from
        addNewCommonUserAction(
            BaseApplication.getAppContext(),
            USER_ACTION_ENTERING_RECORDING,
            EVENT_DIRECT_RECORD_BUTTON_CLICK,
            eventInfo,
            false
        )
    }

    /**
     * 保存录音
     */
    @JvmStatic
    fun addClickSaveRecord(recording: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_SAVE_RECORD] = recording
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_RECORDING_FINISH_TAG, EVENT_FINISH_RECORDING_CANCEL, eventInfo, false)
    }

    /**
     * 取消录音
     */
    @JvmStatic
    fun addClickCancelRecord(recording: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_CANCEL_RECORD] = recording
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_RECORDING_FINISH_TAG, EVENT_FINISH_RECORDING_CANCEL, eventInfo, false)
    }

    /**
     * 点击音频tab-标记
     */
    @JvmStatic
    fun addClickPlayTextMark() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_PLAY_TEXT_MARK] = DEFAULT_VALUE
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_PLAYBACK, eventInfo, false)
    }

    /**
     * 点击桌面卡片开始录音
     */
    @JvmStatic
    fun addClickSmallCardToRecord() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_SMALL_CARD_START_RECORD] = DEFAULT_VALUE
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_ENTERING_RECORDING, EVENT_SMALL_CARD_START_RECORD, eventInfo, false)
    }

    /**
     * 应用中，录音过程中打文字标记
     */
    @JvmStatic
    fun addClickRecordTextMark() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_RECORD_TEXT_MARK] = VALUE_CLICK_RECORD_TEXT_MARK
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_RECORDING, eventInfo, false)
    }

    /**
     * 录音详情页-更多-分享
     */
    @JvmStatic
    fun addClickMoreShare() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_MORE_SHARE] = DEFAULT_VALUE
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfo, false)
    }

    /**
     * 录音详情页-更多-分享-分享录音、分享文本
     */
    @JvmStatic
    fun addSelectMoreRecordToShare(toShare: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_PLAY_MORE_SHARE_RECORD_TXT] = toShare
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfo, false)
    }

    /**
     * 点击桌面卡片“标记”按钮
     */
    @JvmStatic
    fun addClickRecordTextMarkInSmallCard() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_RECORD_TEXT_MARK_IN_SMALL_CARD] = VALUE_CLICK_RECORD_TEXT_MARK_IN_SMALL_CARD
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_RECORDING, eventInfo, false)
    }

    /**
     * 点击副屏主页面保存录音后的"查看"按钮
     */
    @JvmStatic
    fun addClickBtnForLook() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_MINI_CARD_LOOK] = VALUE_CLICK_MINI_CARD_LOOK
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_ENTERING_RECORDING, EVENT_MINI_APP_CLICK_LOOK, eventInfo, false)
    }

    /**
     * 录音过程中，点击副屏主页中“标记”按钮
     */
    @JvmStatic
    fun addClickRecordTextMarkInMiniCard() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_RECORD_TEXT_MARK_IN_MINI_CARD] = VALUE_CLICK_RECORD_TEXT_MARK_IN_MINI_CARD
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_RECORDING, eventInfo, false)
    }

    /**
     * 录音过程中，点击火烈鸟副屏中的录制按钮，切换暂停、继续录音状态
     */
    @JvmStatic
    fun addClickRecordStateInMiniCard(recordStatus: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_RECORD_STATE_IN_MINI_CARD] = recordStatus
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_ENTERING_RECORDING, EVENT_START_RECORD_AUDIO, eventInfo, false)
    }

    /**
     * 录音过程中，点击蜻蜓副屏中的录制按钮，切换暂停、继续录音状态
     */
    @JvmStatic
    fun addClickRecordStateInDragonFlyCard(recordStatus: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_RECORD_STATE_IN_DRAGONFLY_CARD] = recordStatus
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_ENTERING_RECORDING, EVENT_START_RECORD_AUDIO, eventInfo, false)
    }

    /**
     * 录音过程中，点击桌面卡片中的录制按钮，切换暂停、继续录音状态
     */
    @JvmStatic
    fun addClickRecordStateInSmallCard(recordStatus: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CLICK_RECORD_STATE_IN_SMALL_CARD] = recordStatus
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_ENTERING_RECORDING, EVENT_START_RECORD_AUDIO, eventInfo, false)
    }

    /**
     * 音频tab、文本tab切换
     */
    @JvmStatic
    fun addPlaySwitchTab(position: Int) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        val value = when (position) {
            0 -> VALUE_PLAY_SWITCH_TAB_AUDIO
            else -> VALUE_PLAY_SWITCH_TAB_TEXT
        }
        eventInfo[KEY_PLAY_SWITCH_TAB] = value
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_MAIN_VIEW_TAG, EVENT_BROWSERFILE_FILE_PLAY, eventInfo, false)
    }

    /**
     * 打开录音设置界面
     */
    @JvmStatic
    fun addEnterSettingOpen() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_ENTER_SETTING_OPEN] = VALUE_ENTER_SETTING_OPEN
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfo, false)
    }

    /**
     * 播放录音-快退
     */
    @JvmStatic
    fun addFastBack() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_FAST_BACK] = VALUE_FAST_BACK
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_FAST_BACK_FAST_FORWARD, EVENT_FAST_BACK_CLICK, eventInfo, false)
    }

    /**
     * 播放录音-快进
     */
    @JvmStatic
    fun addFastForward() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_FAST_FORWARD] = VALUE_FAST_FORWARD
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_FAST_BACK_FAST_FORWARD, EVENT_FAST_FORWARD_CLICK, eventInfo, false)
    }

    /**
     * 录音文件-音频tab-点击“标记列表”
     */
    @JvmStatic
    fun addPlayMarkListButton(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_PLAY_MARK_LIST_BUTTON] = value
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_MARK_TAG, EVENT_MARK_TAG_FROM_PLAYBACK, eventInfo, false)
    }

    /**
     * 录音文件-音频tab-点击左下角播放设置按钮
     */
    @JvmStatic
    fun addPlaySetting() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_PLAY_SETTING] = DEFAULT_VALUE
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_SETTING, EVENT_PLAY_SETTING, eventInfo, false)
    }

    /**
     * 录音文件-音频tab-点击左下角播放设置按钮-弹出播放设置页面-点击全部还原按钮
     */
    @JvmStatic
    fun addRestoreAll() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_RESTORE_ALL] = DEFAULT_VALUE
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_SETTING, EVENT_RESTORE_ALL, eventInfo, false)
    }

    /**
     * 录音详情页-点击更多-点击删除-删除成功次数
     */
    @JvmStatic
    fun addPlayMoreDeleteSuccess() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_PLAY_MORE_DELETE_SUCCESS] = DEFAULT_VALUE
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfo, false)
    }

    /**
     *
     * 回收站录音详情页-点击删除-彻底删除成功次数
     */
    @JvmStatic
    fun addPlayRecycleDeleteSuccess() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_PLAY_RECYCLE_DELETE_SUCCESS] = DEFAULT_VALUE
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfo, false)
    }

    /**
     *
     * 回收站录音详情页-点击恢复-恢复成功次数
     */
    @JvmStatic
    fun addPlayRecycleRecoverSuccess() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_PLAY_RECYCLE_RECOVER_SUCCESS] = DEFAULT_VALUE
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfo, false)
    }

    /**
     *
     * 录音详情页-更多-移动
     */
    @JvmStatic
    fun addPlayMoreRecordMove() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_PLAY_MORE_MOVE] = DEFAULT_VALUE
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_PLAY_MORE, EVENT_PLAY_MORE, eventInfo, false)
    }

    /**
     * 录音详情页-文本tab-点击内容搜索按钮次数
     */
    @JvmStatic
    fun addClickContentSearch() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CONTENT_SEARCH] = DEFAULT_VALUE
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_CONVERT, EVENT_CONVERT_SEARCH, eventInfo, false)
    }

    /**
     * 录音详情页-文本tab-点击内容搜索按钮-内容搜索页面-点击取消次数
     */
    @JvmStatic
    fun addClickCancelContentSearch() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CANCEL_CONTENT_SEARCH] = DEFAULT_VALUE
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_CONVERT, EVENT_CONVERT_SEARCH, eventInfo, false)
    }

    /**
     * 录音详情页-文本tab-点击内容搜索按钮-搜索内容位置切换次数
     * 0：左箭头
     * 1：右箭头
     */
    @JvmStatic
    fun addContentSearchPosChange(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_CONTENT_SEARCH_POS_CHANGE] = value
        addNewCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_CONVERT, EVENT_CONVERT_SEARCH, eventInfo, false)
    }

    /**
     * 从通话-设置-通话录音-查看通话录音，进入通话录音界面
     */
    @JvmStatic
    fun addThroughCallRecording() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_THROUGH_CALL_RECORDING] = DEFAULT_VALUE
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_MAIN_VIEW_TAG, EVENT_LAUNCH_RECORDER_APP, eventInfo, false)
    }

    /**
     * 桌面卡片启动
     */
    @JvmStatic
    fun addLaunchAppSmallCard(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_LAUNCH_APP_SMALL_CARD] = value
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_MAIN_VIEW_TAG, EVENT_LAUNCH_RECORDER_APP, eventInfo, false)
    }

    /**
     * 小布启动录制
     */
    @JvmStatic
    fun addFromBreeno() {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_FROM_BREENO] = DEFAULT_VALUE
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_ENTERING_RECORDING, EVENT_ENTERING_RECORDING, eventInfo, false)
    }

    /**
     * 播放详情页-更多-点击设为铃声
     */
    @JvmStatic
    fun addClickSetRingtone(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_FROM] = value
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_BROWSEFILE_TAG, EVENT_BROWSERFILE_ASRINGTONG, eventInfo, false)
    }

    @JvmStatic
    fun addActionMiniCancelRecord(value: String) {
        val eventInfo: MutableMap<String?, String?> = HashMap()
        eventInfo[KEY_MINI_CANCEL_RECORD] = value
        addCommonUserAction(BaseApplication.getAppContext(), USER_ACTION_RECORDING_FINISH_TAG,
            EVENT_FINISH_RECORDING_CANCEL, eventInfo, false)
    }

    /**
     * 录音分组-新建分组
     */
    @JvmStatic
    fun addCreateNewGroup() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_FROM] = DEFAULT_VALUE
        addCommonUserAction(
            context,
            USER_ACTION_RECORD_GROUP,
            EVENT_GROUP_NEW_GROUP,
            eventInfo, false
        )
    }

    /**
     * 录音分组-重命名分组
     */
    @JvmStatic
    fun addRenameGroup() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_FROM] = DEFAULT_VALUE
        addCommonUserAction(
            context,
            USER_ACTION_RECORD_GROUP,
            EVENT_GROUP_RENAME,
            eventInfo, false
        )
    }

    /**
     * 录音分组-删除分组
     */
    @JvmStatic
    fun addDeleteGroup() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_FROM] = DEFAULT_VALUE
        addCommonUserAction(
            context,
            USER_ACTION_RECORD_GROUP,
            EVENT_GROUP_DELETE,
            eventInfo, false
        )
    }

    /**
     * 点击录音分组
     */
    @JvmStatic
    fun addClickGroup(groupInfo: GroupInfo?) {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_FROM] = DEFAULT_VALUE
        groupInfo?.mGroupName?.let {
            eventInfo[KEY_GROUP_NAME] = it
        }
        addNewCommonUserAction(
            context,
            USER_ACTION_RECORD_GROUP,
            EVENT_CLICK_RECORD_GROUP,
            eventInfo, false
        )
    }

    /**
     * 通话录音-按联系人分组
     */
    @JvmStatic
    fun addCallGroupingByContact() {
        val eventInfo = HashMap<String, String>()
        eventInfo[KEY_FROM] = DEFAULT_VALUE
        addNewCommonUserAction(
            context,
            USER_ACTION_MAIN_VIEW_TAG,
            EVENT_CLICK_CALL_GROUP_BY_CONTACT,
            eventInfo, false
        )
    }

    /**
     * real_time_subtitle 实时字幕
     *    uni_file_id	录音编号	STRING
     *    state	点击开启和停止的次数	                STRING{'0':'xx', '1':'xx'} 0代表停止，1代表开启
     *    f_ms	首字上屏耗时(ms)	LONG                0-86400000
     *    return	字幕转写结果：成功、失败 对应次数	INT     {'0':'xx', '1':'xx'} 0代表失败，1代表成功
     *    fail_reason	转写失败原因对应次数	STRING      {'1':'xx', '2':'xx', '3':'xx', ..., 'others':'xx'}
     */
    @JvmStatic
    fun addRealTimeSubtitle(bprt: IBuryingPointRealTimeSubtitle) {
        if (bprt is BuryingPointRealTimeSubtitle) {
            val eventInfo = HashMap<String, String>()
            eventInfo[KEY_UNI_FILE_ID] = bprt.uniFileId
            eventInfo[KEY_STATE] = GsonUtil.toJson(bprt.state)
            eventInfo[KEY_F_MS] = bprt.fMs.toString()
            eventInfo[KEY_RETURN_1] = GsonUtil.toJson(bprt.returnResult)
            eventInfo[KEY_FAIL_REASON] = GsonUtil.toJson(bprt.failReason)
            DebugUtil.i(TAG, "addRealTimeSubtitle eventInfo= $eventInfo")
            addNewCommonUserAction(context, USER_ACTION_ENTERING_RECORDING, EVENT_REAL_TIME_SUBTITLE, eventInfo, false)
        }
    }
}