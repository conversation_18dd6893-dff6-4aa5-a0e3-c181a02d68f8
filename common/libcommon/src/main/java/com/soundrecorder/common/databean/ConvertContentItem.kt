/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.databean

import android.graphics.drawable.Drawable
import com.soundrecorder.base.model.TimeStampGetter
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.utils.FunctionOption
import java.io.Serializable
import java.util.function.Consumer

data class ConvertContentItem(
    var startTime: Long = 0,
    var endTime: Long = 0,
    var textContent: String = "",
    var isFocusItem: Boolean = false,
    var roleId: Int = 0,
    var roleName: String? = "",
    var roleAvatar: Drawable? = null,
    var textWithWords: List<String>? = null,
    var textWithWordsTimeStamp: List<String>? = null,
    var listSubSentence: MutableList<SubSentence>? = null, // 子句List
    var textType: String? = null  //实时ASR转写的类型

) : Serializable {

    companion object {
        private val TAG = "ConvertContentItem"
    }

    var mTextOrImageItems: MutableList<ItemMetaData>? = null

    constructor(
        startTime: Long = 0,
        endTime: Long = 0,
        textContent: String = "",
        isFocusItem: Boolean = false,
        roleId: Int = 0,
        roleName: String? = "",
        roleAvatar: Drawable? = null,
        textWithWords: List<String>? = null,
        textWithWordsTimeStamp: List<String>? = null,
    ) : this(
        startTime,
        endTime,
        textContent,
        isFocusItem,
        roleId,
        roleName,
        roleAvatar,
        textWithWords,
        textWithWordsTimeStamp,
        null
    )


    //根据marklist和sentence分类,进行整体混排分类,
    /**
     * needJustFirstImageItems true = 表明需要将该段落内部的第一个分句之前的图片标记人为调整到第一个分句之后,  false = 直接按照时间顺排即可
     */
    @Suppress("LongMethod")
    fun parceNewTextOrImageItems(needJustFirstImageItems: Boolean = true) {
        DebugUtil.i(TAG, "parceNewTextOrImageItems begin: $this, needJustFirstImageItems $needJustFirstImageItems")
        mTextOrImageItems = mutableListOf()
        //加入一个TimeDivider
        mTextOrImageItems?.add(TimerDividerMetaData(startTime, roleId, roleName))
        if (!FunctionOption.IS_SUPPORT_MIX_LAYOUT) {
            //不支持图文混排时，相关数据分类就只有一个Text类型的Item
            var currentTextItem = TextItemMetaData(listSubSentence)
            currentTextItem.textParagraph = listSubSentence
            mTextOrImageItems?.add(currentTextItem)
            return
        }
        //支持图文混排时,定义排序规则
        val timeComparator =
            Comparator<TimeStampGetter> { item1: TimeStampGetter, item2: TimeStampGetter -> (item1.getTimeStamp() - item2.getTimeStamp()).toInt() }
        val timeGettings = mutableListOf<TimeStampGetter>()
        //拿到当前ConvertContentItem中所有的图片标记列表
        val allPictureMarks = getAllPictureMarks()
        timeGettings.addAll(allPictureMarks)
        val allSentences = listSubSentence
        if (allSentences != null) {
            timeGettings.addAll(allSentences)
        }
        //图片标记列表和分句排序
        timeGettings.sortWith(timeComparator)
        val tmpFirstImageItems = mutableListOf<ItemMetaData>()
        if (!timeGettings.isNullOrEmpty()) {
            var currentTextItem: TextItemMetaData = TextItemMetaData()
            var currentTimeStampGetter: TimeStampGetter
            var lastTimeStampGetter: TimeStampGetter? = null
            var iterator: ListIterator<TimeStampGetter> = timeGettings.listIterator()
            var indexIterator = iterator.withIndex()
            //表明时间顺序上第一个item为图片标记
            var firstIsImage = false
            while (indexIterator.hasNext()) {
                val indexValue = indexIterator.next()
                currentTimeStampGetter = indexValue.value
                val currentIndex = indexValue.index
                if (currentTimeStampGetter is MarkDataBean) {
                    if (currentIndex == 0) {
                        firstIsImage = true
                    }
                    //当前是图片标记, 检测是否是currentTextItem是否是上一个段落的末尾
                    val textEnd = checkCurrentImageItemIsTextLayoutEnd(
                        currentTimeStampGetter,
                        lastTimeStampGetter
                    )
                    if (textEnd) {
                        mTextOrImageItems?.add(currentTextItem)
                        //第一个TextItem放入列表之后，检查是否当前sentence之前的图片标记是否存在
                        //DebugUtil.i(TAG, "parceNewTextOrImageItems firstIsImage $firstIsImage, tmpFirstImageItems.size ${tmpFirstImageItems.size}")
                        if (needJustFirstImageItems && tmpFirstImageItems.size > 0 && firstIsImage) {
                            //DebugUtil.i(TAG, "parceNewTextOrImageItems add tmpFirstImageItems ${tmpFirstImageItems} to resultItems")
                            mTextOrImageItems?.addAll(tmpFirstImageItems)
                            //当前item为text，表明firstImage不是image类型，将firstIsImage状态回执
                            firstIsImage = false
                        }
                        //DebugUtil.i(TAG, "parceNewTextOrImageItems image end text, add currentTextItem ${currentTextItem}")
                    }
                    val currentImageItem = ImageMetaData()
                    currentImageItem.imageItem = currentTimeStampGetter
                    //DebugUtil.i(TAG, "parceNewTextOrImageItems needJustFirstImageItems $needJustFirstImageItems firstIsImage $firstIsImage")
                    if (needJustFirstImageItems && firstIsImage) {
                        //需要调整时，并且第一个item为图片标记时，将当前图片标记放在tmp列表中，直到后续遇到第一个sentence类型时，结束tmpFirstImageItems
                        //DebugUtil.i(TAG, "parceNewTextOrImageItems tmpImageItem add $currentImageItem")
                        tmpFirstImageItems.add(currentImageItem)
                    } else {
                        //不需要调整时，直接添加图片标记到列表中
                        mTextOrImageItems?.add(currentImageItem)
                    }
                } else {
                    //当前是分句,检测是否是分句开头
                    val textBegin = checkCurrentItemIsTextLayoutBegin(
                        currentTimeStampGetter,
                        lastTimeStampGetter
                    )
                    val textEnd = checkCurrentTextItemIsTextLayoutEnd(
                        currentTimeStampGetter,
                        !iterator.hasNext()
                    )
                    if (textBegin) {
                        //创建一个TextItem，新的CurrentTextItem开始，这个new对象的地方必须在textParagraph的clear之前，不然会操作上一个currentTextItem引用对应的textParagraph段落
                        currentTextItem = TextItemMetaData()
                        //清楚之前的列表中的内容,新建分句列表
                        currentTextItem.textParagraph = mutableListOf()
                        //textParagraph.clear()
                        currentTextItem.textParagraph?.add(currentTimeStampGetter as SubSentence)
                    } else {
                        //非分句开头，将当前sentence加入到段落中
                        currentTextItem.textParagraph?.add(currentTimeStampGetter as SubSentence)
                    }
                    //检测是否是段落末尾
                    if (textEnd) {
                        //段落末尾时将当前currentTextItem放在列表中
                        mTextOrImageItems?.add(currentTextItem)
                        //第一个TextItem放入列表之后，检查是否当前sentence之前的图片标记是否存在
                        //DebugUtil.i(TAG, "parceNewTextOrImageItems firstIsImage $firstIsImage, tmpFirstImageItems.size ${tmpFirstImageItems.size}")
                        if (needJustFirstImageItems && tmpFirstImageItems.size > 0 && firstIsImage) {
                            //DebugUtil.i(TAG, "parceNewTextOrImageItems add tmpFirstImageItems ${tmpFirstImageItems} to resultItems")
                            mTextOrImageItems?.addAll(tmpFirstImageItems)
                            //当前item为text，表明firstImage不是image类型，将firstIsImage状态回执
                            firstIsImage = false
                        }
                    }
                }
               /*DebugUtil.i(
                    TAG,
                    "parceNewTextOrImageItems ing lastTimeStampGetter: ${lastTimeStampGetter}, currentTimeStampGetter $currentTimeStampGetter, " +
                            " mTextOrImageItems ${mTextOrImageItems}"
                )*/
                lastTimeStampGetter = currentTimeStampGetter
            }
        }
        DebugUtil.i(TAG, "parceNewTextOrImageItems end:  mTextOrImageItems$mTextOrImageItems")
    }


    fun getTextStringLengthBeforeTextImageItemIndex(itemIndex: Int): Int {
        var resultLength = 0
        mTextOrImageItems?.let {
            var sb = StringBuffer()
            for (currentIndex in it.indices) {
                var currentItem = it[currentIndex]
                var currentItemText = currentItem.getTextString()
                if ((currentItemText != null) && (currentIndex < itemIndex)) {
                    sb.append(currentItem.getTextString())
                }
            }
            resultLength = sb.length
        }
        DebugUtil.i(
            TAG,
            "getTextStringLengthBeforeTextImageItemIndex input itemIndex $itemIndex, result $resultLength"
        )
        return resultLength
    }


    private fun checkCurrentItemIsTextLayoutBegin(
        currentTimeStampGetter: TimeStampGetter,
        lastTimeStampGetter: TimeStampGetter?
    ): Boolean {
        //当前item为分句类型，上一个item为空或图片标记类型
        val result =
            (currentTimeStampGetter is SubSentence) && ((lastTimeStampGetter == null) || (lastTimeStampGetter is MarkDataBean))
        DebugUtil.i(TAG, "checkCurrentItemIsTextLayoutBegin input currentTimeStampGetter $currentTimeStampGetter" +
                ", lastTimeStampGetter $lastTimeStampGetter, output : $result")
        return result
    }

    private fun checkCurrentTextItemIsTextLayoutEnd(
        currentTimeStampGetter: TimeStampGetter,
        isLastItem: Boolean
    ): Boolean {
        //最后一个item且当前item为分句，判定为当前段落末尾
        val lastIsText = isLastItem && currentTimeStampGetter is SubSentence
        DebugUtil.i(TAG, "checkCurrentTextItemIsTextLayoutEnd input currentTimeStampGetter $currentTimeStampGetter" +
                ", isLastItem $isLastItem, output: lastIsText $lastIsText ")
        return lastIsText
    }

    private fun checkCurrentImageItemIsTextLayoutEnd(
        currentTimeStampGetter: TimeStampGetter,
        lastTimeStampGetter: TimeStampGetter?
    ): Boolean {
        //当前为图片标记，上一个为分句，判定为上一个分句段落末尾
        val imageAfterText =
            (currentTimeStampGetter is MarkDataBean) && (lastTimeStampGetter is SubSentence)
        DebugUtil.i(TAG, "checkCurrentImageItemIsTextLayoutEnd input currentTimeStampGetter $currentTimeStampGetter" +
                ", lastTimeStampGetter $lastTimeStampGetter, output: imageAfterText $imageAfterText ")
        return imageAfterText
    }


    fun getAllPictureMarks(): List<MarkDataBean> {
        val result = mutableListOf<MarkDataBean>()
        listSubSentence?.forEach(Consumer { sentence: SubSentence ->
            result.addAll(sentence.getPictureMarkData())
        })
        return result
    }

    fun getTextOrImageItemIndexAccordingToSentenceIndex(sentenceIndex: Int): Int {
        var resultIndex = 0
        mTextOrImageItems?.let {
            var sentenceCnt = 0
            for (i in it.indices) {
                var textOrImageItem = it[i]
                resultIndex = i
                val diff = textOrImageItem.getTextSentenceSize()
                //整个分句逻辑需要去掉保证前闭后开，才能保证分段itemIndex获取正确
                if ((sentenceIndex >= sentenceCnt) && (sentenceIndex < sentenceCnt + diff)) {
                    break
                }
                sentenceCnt += diff
            }
        }
        return resultIndex
    }


    fun changeRoleNameForTimeDivider(newRoleName: String?): Boolean {
        mTextOrImageItems?.let {
            if (it.size > 0) {
                val timerDividerMetaData = it[0] as TimerDividerMetaData?
                timerDividerMetaData?.roleName = newRoleName
                return true
            }
            DebugUtil.i(TAG, "changeRoleNameForTimeDivider mTextOrImageItems size == 0")
        }
        DebugUtil.i(TAG, "changeRoleNameForTimeDivider mTextOrImageItems null")
        return false
    }

    override fun toString(): String {
        return "ConvertContentItem(startTime=$startTime, endTime=$endTime, roleId=$roleId, " +
                "roleName=$roleName,listSubSentence.size=${listSubSentence?.size}, textContent=$textContent, textType=$textType)"
    }


    data class SubSentence(
        val startCharSeq: Int, // 子句的起始字符下标
        var endCharSeq: Int, // 子句的结束字符下标  闭区间
        val time: Float, // 子句的起始字符对应的时间戳
        val text: String, // 子句的文本内容
        var isFocused: Boolean = false, // 子句是否是焦点
        var onlyHasSimpleMark: Boolean = false, //表示子句是否存在文本标记
        var markDataBeanList: List<MarkDataBean?>? = null  //字句所包含的标记信息
    ) : TimeStampGetter {

        fun hasPictureMarkData(): Boolean {
            val pictureMarks = getPictureMarkData()
            return !pictureMarks.isEmpty()
        }


        fun getPictureMarkData(): MutableList<MarkDataBean> {
            var result = mutableListOf<MarkDataBean>()
            val markDataBeanIterator = markDataBeanList?.iterator()
            while (markDataBeanIterator?.hasNext() == true) {
                val markDataBean = markDataBeanIterator.next()
                if (markDataBean?.isPictureType() == true) {
                    result.add(markDataBean)
                }
            }
            result.sortBy { markDataBean -> markDataBean.timeInMills }
            return result
        }


        override fun getTimeStamp(): Long {
            return time.toLong()
        }

        override fun toString(): String {
            return "SubSentence(startCharSeq=$startCharSeq, endCharSeq=$endCharSeq, time=$time, " +
                    "isFocused=$isFocused, onlyHasSimpleMark=$onlyHasSimpleMark, markDataBeanList=$markDataBeanList)"
        }
    }


    interface ItemMetaData {

        companion object {
            const val TYPE_TEXT = 0
            const val TYPE_IMAGE = 1
            const val TYPE_TIME_DIVIDER = 2
        }

        fun getTextString(): String? {
            return null
        }

        fun hasTextMark(): Boolean {
            return false
        }

        fun isFocuse(): Boolean {
            return false
        }

        fun getTextSentenceSize(): Int {
            return 0
        }
    }

    data class TextItemMetaData(var textParagraph: MutableList<SubSentence>? = null) : ItemMetaData {


        /**
         * 获取全部内部的文字字符串
         */
        override fun getTextString(): String? {
            val sb: StringBuffer = StringBuffer()
            return if (textParagraph != null) {
                textParagraph?.forEach {
                    sb.append(it.text)
                }
                sb.toString()
            } else {
                null
            }
        }

        /**
         * 本段内是否存在文本标记
         */
        override fun hasTextMark(): Boolean {
            var result = false
            if (textParagraph != null) {
                textParagraph?.forEach {
                    if (it.onlyHasSimpleMark) {
                        result = true
                        return result
                    }
                }
            }
            return result
        }


        /**
         * 本段落是否时焦点
         */
        override fun isFocuse(): Boolean {
            var result = false
            if (textParagraph != null) {
                textParagraph?.forEach {
                    if (it.isFocused) {
                        result = true
                        return result
                    }
                }
            }
            return result
        }


        override fun getTextSentenceSize(): Int {
            return textParagraph?.size ?: 0
        }


        fun getBeginTime(): Long {
            return textParagraph?.get(0)?.time?.toLong() ?: 0
        }

        fun getTextStringBeforeIndex(beforeIndex: Int): String? {
            val sb: StringBuffer = StringBuffer()
            return if (textParagraph != null) {
                run breaking@{
                    textParagraph?.forEachIndexed { index, item ->
                        if (index < beforeIndex) {
                            sb.append(item.text)
                        } else {
                            return@breaking
                        }
                    }
                }
                sb.toString()
            } else {
                null
            }
        }
    }

    data class ImageMetaData(var imageItem: MarkDataBean? = null) : ItemMetaData

    data class TimerDividerMetaData(
        var startTime: Long = 0,
        var roleId: Int = 0,
        var roleName: String? = ""
    ) : ItemMetaData {
        var focused = false
        override fun isFocuse(): Boolean {
            return focused
        }
    }

    data class ConvertFileData(
        var mediaRecordId: Long = 0,
        var startTime: Long = 0
    )
}