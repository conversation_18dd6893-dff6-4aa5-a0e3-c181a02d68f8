package com.soundrecorder.common.sync.encryptbox;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.sync.CommonIntentService;

/**
 * 私密保险箱 解除/增加私密录音文件发送广播到录音
 * 指定了录音包名
 */
public class EncryptBoxDataChangeReceiver extends BroadcastReceiver {

    private static final String TAG = "EncryptBoxDataChangeReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent != null) {
            String action = intent.getAction();
            DebugUtil.i(TAG, "onReceive: action: " + action);
            if (!TextUtils.isEmpty(action) && (EncryptBoxConstant.ENCRYPTBOX_ACTION.equalsIgnoreCase(action)) || EncryptBoxConstant.ENCRYPTBOX_ACTION_OPLUS.equalsIgnoreCase(action)) {
                String jsonString = "";
                try {
                    jsonString = intent.getStringExtra(EncryptBoxConstant.ENCRYPTBOX_ACTION_INTENT_EXTRA_KEY);
                } catch (Exception e) {
                    DebugUtil.e(TAG, "intent cannot get EncryptBoxConstant.ENCRYPTBOX_ACTION_INTENT_EXTRA_KEY");
                }
                Intent serviceIntent = new Intent(CommonIntentService.LOCAL_ENCRYPTBOX_ACTION);
                serviceIntent.setPackage(context.getPackageName());
                serviceIntent.putExtra(EncryptBoxConstant.ENCRYPTBOX_ACTION_INTENT_EXTRA_KEY, jsonString);
                CommonIntentService.enqueueWork(context, serviceIntent);
            }
        }
    }
}
