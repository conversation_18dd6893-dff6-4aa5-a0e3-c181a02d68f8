/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** File        : ExportDialogUtils.kt
 ** Description : ExportDialogUtils.kt
 ** Version     : 1.0
 ** Date        : 2025/07/11
 ** Author      : W9085798
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9085798       2025/07/11      1.0      create
 ***********************************************************************/
package com.soundrecorder.common.exportfile.ui

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.snackbar.COUISnackBar
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.exportfile.SummaryContentViewUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ExportTipsManager {

    companion object {
        private const val TAG = "ExportTipsManager"
        private const val LOADING_TIPS_DELAY = 1000L
        private const val DURATION = 300L
        private const val DURATION_TIME = 5000
    }

    private var processDialogJob: Job? = null
    private var currentSnackBar: COUISnackBar? = null
    private var snackBarJob: Job? = null

    /**
     * 显示导出loading框
     */
    fun showExportProcessDialog(context: Context, resId: Int, onComplete: () -> Unit) {
        processDialogJob?.cancel()
        val lifecycle = (context as? AppCompatActivity)?.lifecycleScope
        processDialogJob = lifecycle?.launch(Dispatchers.Main) {
            val processDialog = SummaryContentViewUtil.showProcessDialog(context, resId)
            delay(LOADING_TIPS_DELAY)
            processDialog?.dismiss()
            onComplete()
        }
    }

    /**
     * 显示导出错误消息
     */
    fun showExportError(message: String) {
        ToastManager.showShortToast(BaseApplication.getAppContext(), message)
    }

    /**
     * 统一的SnackBar显示方法
     * 使用Job管理生命周期，避免显示冲突
     */
    fun showSnackBar(
        context: Context?,
        view: View?,
        message: String,
        actionText: String,
        actionClickListener: View.OnClickListener? = null
    ) {
        DebugUtil.d(TAG, "showSnackBar: message=$message, actionText=$actionText")
        // 先dismiss当前的SnackBar并取消之前的Job
        currentSnackBar?.dismiss(true)
        // 使用Job管理SnackBar的显示时序
        snackBarJob?.cancel()
        val lifecycle = (context as? AppCompatActivity)?.lifecycleScope
        snackBarJob = lifecycle?.launch {
            delay(DURATION)
            withContext(Dispatchers.Main) {
                view?.let {
                    currentSnackBar = COUISnackBar.make(it, message, DURATION_TIME).apply {
                        (parent as? ViewGroup)?.clipChildren = false
                        setOnAction(actionText, actionClickListener)
                        show()
                    }
                }
            }
        }
    }

    fun release() {
        processDialogJob?.cancel()
        processDialogJob = null
        currentSnackBar?.dismiss()
        currentSnackBar = null
        snackBarJob?.cancel()
        snackBarJob = null
    }
}