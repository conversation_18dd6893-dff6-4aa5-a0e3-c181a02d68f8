/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AIErrorBuryingPoint
 * Description:AI录音云侧算法服务异常埋点
 * Version: 1.0
 * Date: 2025/7/11
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/7/11      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.common.buryingpoint

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import org.json.JSONObject

object AIErrorBuryingPoint {

    private const val TAG = "AIErrorBuryingPoint"

    private const val EVENT_AI_SUMMARY = "summary"

    private const val EVENT_AI_ASR_REALTIME = "20007010"

    /* 摘要算法服务异常监测 */
    private const val EVENT_ID_ERR_MONITOR_AI_SUMMARY = "err_monitor_ai_summary"

    /* 摘要生成类型 */
    private const val KEY_SUMMARY_TYPE = "summary_type"

    /* 异常的触发方式 */
    private const val KEY_ERR_TRIGGER = "err_trigger"

    /* 异常描述：0 -> 初始化异常; 1 -> 生成摘要异常 */
    const val VALUE_ERR_INIT_KIT = 0
    const val VALUE_ERR_GET_SUMMARY = 1

    /* 生成摘要请求ID */
    private const val KEY_SESSION_ID = "session_id"

    /* 异常信息 */
    private const val KEY_ERR_INFO = "err_info"
    private const val VALUE_ERROR_CODE = "errorCode"
    private const val VALUE_ERROR_MSG = "errorMsg"
    private const val VALUE_ERROR_EXPENDS = "expends"

    /* 实时ASR算法服务异常监测 */
    private const val EVENT_ID_ERR_MONITOR_AI_ASR_REALTIME = "err_monitor_ai_asr_realtime"

    /* 发起实时ASR请求时在参数中指定的channel_id和record_id */
    private const val KEY_CHANNEL_ID = "channel_id"

    /* 异常描述：0 -> 初始化及转写异常(initAsr); 1 -> 获取语种异常(getTranslationConfig) */
    const val VALUE_ERR_REAL_TIME_INIT_ASR = 0
    const val VALUE_ERR_REAL_TIME_GET_TRANSLATION_CONFIG = 1

    /**
     * 上报摘要算法服务异常
     * summaryType: 摘要算法的生成类型,由SpeechSummaryRequest的summaryType和themeCode通过下划线“_”拼接而成
     * trigger: 摘要算法服务异常的触发方式 0 -> 初始化异常; 1 -> 生成摘要异常
     * session_id: 摘要生成的会话请求ID
     * errorCode: onError错误码
     * errorMsg: onError错误信息
     * */
    @JvmStatic
    fun addAISummaryErrorMsg(summaryType: Int?, themeCode: Int?, trigger: Int, sessionId: String, errorCode: Int, errorMsg: String?) {
        val info = HashMap<String, Any>()
        info[KEY_SUMMARY_TYPE] = summaryType.toString() + "_" + themeCode
        info[KEY_ERR_TRIGGER] = trigger
        info[KEY_SESSION_ID] = sessionId
        val jsonObject = JSONObject().apply {
            put(VALUE_ERROR_CODE, errorCode)
            put(VALUE_ERROR_MSG, errorMsg)
        }.toString()
        info[KEY_ERR_INFO] = jsonObject
        DebugUtil.i(TAG, "addAISummaryErrorMsg info $info")
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(),
            EVENT_AI_SUMMARY,
            EVENT_ID_ERR_MONITOR_AI_SUMMARY,
            info,
            false
        )
    }

    /**
     * 实时ASR算法服务异常
     * channelId: 发起实时ASR请求时在参数中指定的channel_id和record_id
     * trigger: 0 -> 初始化及转写异常(initAsr); 1 -> 获取语种异常(getTranslationConfig)
     * errorCode: onError错误码
     * errorMsg: onError错误信息
     * expends: onError附加信息(可选，为空时不包含该字段)
     * */
    @JvmStatic
    fun addAIAsrRealtimeErrorMsg(channelId: String?, trigger: Int, errorCode: Int, errorMsg: String?, expends: String?) {
        val info = HashMap<String, Any>()
        info[KEY_CHANNEL_ID] = channelId ?: ""
        info[KEY_ERR_TRIGGER] = trigger
        val jsonObject = JSONObject().apply {
            put(VALUE_ERROR_CODE, errorCode)
            put(VALUE_ERROR_MSG, errorMsg)
            if (!expends.isNullOrEmpty()) {
                put(VALUE_ERROR_EXPENDS, expends)
            }
        }.toString()
        info[KEY_ERR_INFO] = jsonObject
        DebugUtil.i(TAG, "addAIAsrRealtimeErrorMsg info $info")
        RecorderUserAction.addNewCommonUserAction(
            BaseApplication.getAppContext(),
            EVENT_AI_ASR_REALTIME,
            EVENT_ID_ERR_MONITOR_AI_ASR_REALTIME,
            info,
            false
        )
    }
}