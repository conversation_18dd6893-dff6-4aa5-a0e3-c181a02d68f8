/*
Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
File: - ShareSummaryAndTextPreference
Description: 录音分享摘要分享原文弹窗工具类
Version: 1.0
Date : 2025/6/3
Author: W9067780
--------------------- Revision History: ---------------------
<author>	<data> 	  <version >	   <desc>
W9067780  2025/6/3     1.0      create this file
*/
package com.soundrecorder.common.flexible

import androidx.fragment.app.FragmentManager
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.soundrecorder.base.utils.DebugUtil

class ShareLinkCOUIDialog(val childFragmentManager: FragmentManager?) {
    companion object {
        private const val TAG = "ShareLinkCOUIDialog"
    }

    private var bottomSheetDialogFragment: COUIBottomSheetDialogFragment? = null

    fun showShareLinkDialog(isShowText: <PERSON>olean, isShowSummary: <PERSON><PERSON><PERSON>, callBack: (<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>olean) -> Unit) {
        if (isShowing()) {
            DebugUtil.i(TAG, "bottomSheetDialogFragment is showing")
            return
        }
        bottomSheetDialogFragment = COUIBottomSheetDialogFragment().apply {
            setMainPanelFragment(ShareLinkDialogFragment().apply {
                setShareLinkCallBack(isShowText, isShowSummary, callBack)
            })
        }
        childFragmentManager?.let {
            it.findFragmentByTag(ShareLinkDialogFragment.TAG)?.let {
                childFragmentManager.beginTransaction()
                    .remove(it)
                    .commitAllowingStateLoss()
            }
            bottomSheetDialogFragment?.show(childFragmentManager, ShareLinkDialogFragment.TAG)
        }
    }

    fun isShowing(): Boolean {
        return bottomSheetDialogFragment?.dialog?.isShowing == true
    }

    fun dismiss() {
        bottomSheetDialogFragment?.dismiss()
        bottomSheetDialogFragment = null
    }
}