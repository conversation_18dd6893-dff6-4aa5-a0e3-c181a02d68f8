package com.soundrecorder.common.utils

import android.content.Context
import android.content.SharedPreferences
import com.soundrecorder.common.constant.RecorderConstant

/**
 * 三品牌融合，兼容一加外销原有SP内容
 */
object OPSettingUtils {
    /**
     * 一加外销原有录音机APP的SP-NAME
     */
    const val OPSOUND_SP_NAME = "opsoundrecorder_share"

    /**
     * 一加外销原有录音机APP的SP-KEY: 选择默认格式
     */
    const val OPSOUND_AUDIO_FORMAT_TYPE = "audio_format_type"

    /**
     * 原录音机AAC、WAV 存到SP对应的值
     */
    const val OP_AUDIO_FORMAT_AAC = 1
    const val OP_AUDIO_FORMAT_WAV = 2
    const val OP_AUDIO_FORMAT_MP3 = 3


    private var sOpSharedPreferences: SharedPreferences? = null


    /**
     * 保存录音格式
     * @param context
     * @param format 真实OutputFormat
     * @see RecorderConstant.OutputFormat
     */
    fun saveOPAudioFormat(context: Context, format: Int) {
        if (checkInitPref(context.applicationContext)) {
            sOpSharedPreferences?.edit()
                ?.putInt(OPSOUND_AUDIO_FORMAT_TYPE, convertAudioFormatToSaveValue(format))
                ?.apply()
        }
    }

    /**
     * 获取存录音格式
     * @param context
     * @return 真实OutputFormat 默认AAC @see OplusRecorder.OutputFormat.AAC_ADTS
     * @see RecorderConstant.OutputFormat
     */
    @JvmStatic
    fun getOPAudioFormat(context: Context): Int {
        // 默认格式，统一为 mp3
        var saveValue = OP_AUDIO_FORMAT_MP3
        if (checkInitPref(context.applicationContext)) {
            saveValue = sOpSharedPreferences?.getInt(OPSOUND_AUDIO_FORMAT_TYPE, saveValue) ?: saveValue
        }

        return convertSaveValueToAudioFormat(saveValue)
    }

    private fun checkInitPref(context: Context?): Boolean {
        if (context == null) {
            return false
        }
        if (sOpSharedPreferences == null) {
            sOpSharedPreferences = context.getSharedPreferences(OPSOUND_SP_NAME, Context.MODE_PRIVATE)
        }
        return sOpSharedPreferences != null
    }


    /**
     * 将录音格式转换成存到SP的值，该转换是为了兼容原录音机APP
     * @audioFormat 真实OutputFormat
     * @see RecorderConstant.OutputFormat
     * @return 保存到SP的值
     */
    private fun convertAudioFormatToSaveValue(audioFormat: Int): Int {
        return when (audioFormat) {
            RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS -> OP_AUDIO_FORMAT_AAC
            RecorderConstant.RECORDER_AUDIO_FORMAT_WAV -> OP_AUDIO_FORMAT_WAV
            RecorderConstant.RECORDER_AUDIO_FORMAT_MP3 -> OP_AUDIO_FORMAT_MP3
            else -> OP_AUDIO_FORMAT_MP3
        }
    }

    /**
     * 将存到SP的值转换成录音格式
     * @param saveValue 存到SP的值
     * @return 真实OutputFormat
     * @see RecorderConstant.OutputFormat
     */
    private fun convertSaveValueToAudioFormat(saveValue: Int): Int {
        return when (saveValue) {
            OP_AUDIO_FORMAT_AAC -> RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS
            OP_AUDIO_FORMAT_WAV -> RecorderConstant.RECORDER_AUDIO_FORMAT_WAV
            OP_AUDIO_FORMAT_MP3 -> RecorderConstant.RECORDER_AUDIO_FORMAT_MP3
            else -> RecorderConstant.RECORDER_AUDIO_FORMAT_MP3
        }
    }
}