/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: RecorderUserActionKT
 * Description:
 * Version: 1.0
 * Date: 2025/4/25
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/4/25      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.common.buryingpoint

object RecorderUserActionKt {

    /*讲话人编辑次数*/
    @JvmField
    var sEditCount: Long = 0

    /*是否保存录音*/
    @JvmField
    var sSaveRecord: Int = RecorderUserAction.VALUE_RETURN_TYPE_SAVE

    /*讲话人编辑 是否应用到全部讲话人 默认值为 0（否）*/
    @JvmField
    var sIsAppliedAll: String = RecorderUserAction.VALUE_APPLY_ALL_NO

    /*进入详情页播放次数*/
    @JvmField
    var sInfoCount: Int = 0

    /*点击快捷播放按钮次数*/
    @JvmField
    var sPlayCount: Int = 0

    /* 入口来源 入口来源（默认值） Launcher ->0  sidebar->1 card->2 cube_button->3 */
    @JvmField
    var sValueEntryFrom: String = RecorderUserAction.VALUE_ENTRY_FROM_LAUNCHER
}