/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SweepGradientWithShapeLayer
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.drawable

import android.content.Context
import android.graphics.*
import android.graphics.drawable.Drawable
import androidx.core.graphics.toColorInt

class SweepGradientDrawable(context: Context, radius: Float) : Drawable() {

    companion object {
        private const val OPAQUE = 255

        private const val COLOR_1 = "#0FA7FF"
        private const val COLOR_2 = "#3D8BFF"
        private const val COLOR_3 = "#BE5DFF"
        private const val COLOR_4 = "#FF5447"
        private const val COLOR_5 = "#FFBA19"
        private const val COLOR_6 = "#45E555"
        private const val COLOR_7 = "#0FA7FF"

        private const val POSITION_1 = 29 / 360F
        private const val POSITION_2 = 87 / 360F
        private const val POSITION_3 = 130 / 360F
        private const val POSITION_4 = 190 / 360F
        private const val POSITION_5 = 260 / 360F
        private const val POSITION_6 = 320 / 360F
        private const val POSITION_7 = 1F
    }

    private var colors: IntArray = intArrayOf(
        COLOR_1.toColorInt(),
        COLOR_2.toColorInt(),
        COLOR_3.toColorInt(),
        COLOR_4.toColorInt(),
        COLOR_5.toColorInt(),
        COLOR_6.toColorInt(),
        COLOR_7.toColorInt()
    )

    private val paint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
    }

    // 圆角半径（16dp转换为像素）
    private val cornerRadius = radius
    private val rectF = RectF()
    private val positions = floatArrayOf(
        POSITION_1,
        POSITION_2,
        POSITION_3,
        POSITION_4,
        POSITION_5,
        POSITION_6,
        POSITION_7
    )

    private var sweepGradient: SweepGradient? = null
    private var lastCenterX = -1f
    private var lastCenterY = -1f

    override fun draw(canvas: Canvas) {
        // 获取绘制区域
        rectF.set(bounds)

        // 计算中心坐标
        val centerX = rectF.centerX()
        val centerY = rectF.centerY()

        // 创建扇形渐变
        if (sweepGradient == null || centerX != lastCenterX || centerY != lastCenterY) {
            sweepGradient = SweepGradient(centerX, centerY, colors, positions)
            lastCenterX = centerX
            lastCenterY = centerY
            paint.shader = sweepGradient
        }


        // 绘制带圆角的矩形
        canvas.drawRoundRect(rectF, cornerRadius, cornerRadius, paint)
    }

    override fun setAlpha(alpha: Int) {
        if (paint.alpha != alpha) {
            paint.alpha = alpha
            invalidateSelf()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun getOpacity(): Int {
        return if (paint.alpha == OPAQUE) PixelFormat.OPAQUE else PixelFormat.TRANSLUCENT
    }

    override fun setColorFilter(colorFilter: ColorFilter?) {
        if (paint.colorFilter != colorFilter) {
            paint.colorFilter = colorFilter
            invalidateSelf()
        }
    }

    override fun onBoundsChange(bounds: Rect) {
        super.onBoundsChange(bounds)
        sweepGradient = null
    }
}
