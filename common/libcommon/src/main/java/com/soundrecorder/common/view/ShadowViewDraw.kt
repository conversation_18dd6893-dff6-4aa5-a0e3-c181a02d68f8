/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ShadowViewDraw
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/6/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.RelativeLayout
import com.soundrecorder.common.R
import androidx.core.content.withStyledAttributes

@SuppressLint("ResourceAsColor")
class ShadowViewDraw@JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {

    companion object {
        const val TAG = "ShadowViewDraw"
        const val PERCEPTION_SHADOW = 1
    }

    private var shadowBlurRadius = 0f
    private var shadowOffsetX = 0f
    private var spread = 0f
    private var shadowOffsetY = 0f
    private var drawType = 0
    private var shadowColor = context.resources.getColor(R.color.record_save_btn_shadow)
    private var mScaleCircle: Float = 1f

    init {
        isForceDarkAllowed = false
        setWillNotDraw(false)
        getContext().withStyledAttributes(
            attrs,
            R.styleable.ShadowViewDraw
        ) {
            shadowOffsetX = getFloat(R.styleable.ShadowViewDraw_shadowOffsetX, 0f)
            shadowOffsetY = getFloat(R.styleable.ShadowViewDraw_shadowOffsetY, 0f)
            shadowBlurRadius = getFloat(R.styleable.ShadowViewDraw_shadowBlurRadius, 0f)
            drawType = getInt(R.styleable.ShadowViewDraw_drawType, 0)
            shadowColor = getColor(R.styleable.ShadowViewDraw_shadowColor, R.color.edit_circle_btn_shadow)
        }
    }
    private var paint: Paint = Paint().apply {
        color = Color.TRANSPARENT
        isAntiAlias = true
        setShadowLayer(shadowBlurRadius, shadowOffsetX, shadowOffsetY, shadowColor)
    }

    override fun onDraw(canvas: Canvas) {
        val centerX = width / 2
        val centerY = height / 2
        val radius = context.resources.getDimension(R.dimen.circle_record_button_diam) / 2
        paint.setShadowLayer(
            shadowBlurRadius * mScaleCircle,
            shadowOffsetX,
            shadowOffsetY,
            shadowColor
        )
        paint.let { canvas.drawCircle(centerX.toFloat(), centerY.toFloat(), radius * mScaleCircle, it) }
        super.onDraw(canvas)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        return super.onTouchEvent(event)
    }
    fun updateScaleCircle(value: Float) {
        mScaleCircle = value
        invalidate()
    }
}
