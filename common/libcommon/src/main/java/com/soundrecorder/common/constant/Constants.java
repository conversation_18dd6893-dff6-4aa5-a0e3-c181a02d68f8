/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  Constants
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.constant;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

public class Constants {
    public static final String PERMISSION_OPPO_COMPONENT_SAFE = "oppo.permission.OPPO_COMPONENT_SAFE";
    public static final String PERMISSION_OPPO_SAFE_PRIVATE = "com.oppo.permission.safe.PRIVATE";
    public static final String RECORDINGS = "Recordings";
    public static final String STANDARD_RECORDINGS = "Standard Recordings";
    public static final String INTERVIEW_RECORDINGS = "Interview Recordings";
    public static final String MEETING_RECORDINGS = "Meeting Recordings";
    public static final String CALL_RECORDINGS = "Call Recordings";
    public static final String OPPO_SHARE_RECORDINGS = "OPPO Share/Recordings";
    public static final String OPPO_SHARE_REALME_RECORDINGS = "realme Share/Recordings";
    public static final String OPPO_SHARE_ONE_PLUS_RECORDINGS = "OnePlus Share/Recordings";
    public static final String OPPO_SHARE_RECORDINGS_RELATIVE_ROOT_PATH = "Music/";
    public static final String OPPO_SHARE_RECORDINGS_RELATIVE_ROOT2_PATH = "Music/OPPO Share/";
    public static final String OPPO_SHARE_RECORDINGS_RELATIVE_PATH = "Music/OPPO Share/Recordings/";
    public static final String OPPO_SHARE_REALME_RECORDINGS_RELATIVE_PATH = "Music/realme Share/Recordings/";
    public static final String OPPO_SHARE_ONE_PLUS_RECORDINGS_RELATIVE_PATH = "Music/OnePlus Share/Recordings/";
    public static final String OPPO_SHARE_RECORDINGS_TEXT_ROOT_PATH = "Documents/";
    public static final String OPPO_SHARE_RECORDINGS_TEXT_ROOT2_PATH = "Documents/OPPO Share/";
    public static final String OPPO_SHARE_RECORDINGS_TEXT = "Documents/OPPO Share/Recordings";
    public static final String OPPO_SHARE_REALME_RECORDINGS_TEXT = "Documents/realme Share/Recordings";
    public static final String OPPO_SHARE_ONE_PLUS_RECORDINGS_TEXT = "Documents/OnePlus Share/Recordings";
    public static final String AMP_FOLDER = "amp";
    public static final String AMP_FILE_SUFFIX = ".json";
    public static final String AMP_FILE_TMP_SUFFIX = ".tmp";
    public static final int PAGE_SIZE = 500;
    public static final int REQUEST_CODE_RENAME = 997;
    public static final int REQUEST_CODE_DELETE_BARCH = 998;
    public static final int REQUEST_CODE_RSE = 999;
    public static final String KEY_IS_CLIPPED_SAVE = "is_clipped_save";
    public static final String KEY_CLIPPED_SAVE_RECORD_MEDIA_ID = "clipped_save_record_id";
    public static final int RESULT_CODE_EXIT_APP = 1001;
    public static final int REQUEST_CODE_SETTING = 1002;
    public static final int REQUEST_CODE_PLAY = 1003;
    public static final int RESULT_CODE_FILEOBSERVER_FINISH = 1004;
    public static final int EXPORT_TO_NOTE_REQUEST_CODE = 1005;
    public static final int REQUEST_CODE_SHARE_TXT = 1006;
    public static final int RESULT_CODE_PLAYBACK_SUMMARY = 2001;

    public static final int NOTES_RESULT_CODE_ERROR = -10;
    public static final int COLOR_OS_VERSION_Q = 15;

    public static final long RESERVED_SPACE = 30 * 1024 * 1024;
    public static final long TIME_ONE_MINUTE = 60 * 1000;
    public static final int PLAYER_FIX_PADDING = 20;
    public static final int THREE = 3;
    public static final String DURATION = "duration";

    public static final Charset UTF_8 = StandardCharsets.UTF_8;
    public static final int SHOW_WAITING_DIALOG_THRESHOLD = 50 * 1024;
    public static final int DEFAULT_MOVE_RECORD_ID = -1;

    public static final int WAVE_SAMPLE_INTERVAL_TIME = 100;

    public static final String EMPTY = "";
    public static final String CLOCK_PACKAGE_NAME = "com.coloros.alarmclock";
    public static final String COLORFUL_ENGINE_PACKAGE_NAME = "com.heytap.colorfulengine";
    public static final String ASSISTANSCREEN_PACKAGE_NAME = "com.coloros.assistantscreen";
    public static final String ROAMING_PACKAGE_NAME = "com.redteamobile.roaming";
    public static final String COLOROS_DIR = "ColorOS";

    public static final String ATTR_MODEL = "ro.product.name";
    public static final String ATTR_OTAVERSION = "ro.build.version.ota";
    public static final String ATTR_ROMVERSION = "ro.build.display.id";
    public static final String ATTR_COLOROSVERSION = "ro.build.version.opporom";
    public static final String ATTR_ANDROIDVERSION = "ro.build.version.release";
    public static final String ATTR_UREGION = "persist.sys.oppo.region";

    public static final long SECOND = 1000;

    public static final String FRESH_FLAG = "fresh_flag";

    public static final int SCROLL_STATE_IDLE = 0;
    public static final int SCROLL_STATE_DRAGGING = 1;
    public static final int SCROLL_STATE_SETTLING = 2;
    public static final String CONVERT_TEXT_EXTRA_JUMP_FROM_CARD = "convert_text_extra_jump_from_card";
    public static final String AI_SUMMARY_EXTRA_JUMP_FROM_CARD = "ai_summary_extra_jump_from_card";
}
