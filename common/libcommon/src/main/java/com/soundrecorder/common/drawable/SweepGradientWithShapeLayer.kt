/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SweepGradientWithShapeLayer
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.common.drawable

import android.content.Context
import android.graphics.drawable.LayerDrawable
import android.graphics.drawable.ShapeDrawable
import android.graphics.drawable.shapes.RoundRectShape

class SweepGradientWithShapeLayer(context: Context, radius: Float, color: Int) : LayerDrawable(
    arrayOf(
        SweepGradientDrawable(context, radius),
        createRoundedShapeLayer(context, radius, color)
    )
) {
    companion object {
        private fun createRoundedShapeLayer(context: Context, radius: Float, color: Int): ShapeDrawable {
            var radio = radius - context.resources.getDimension(com.soundrecorder.common.R.dimen.dp1_6)
            if (radio < 0f) {
                radio = context.resources.getDimension(com.soundrecorder.common.R.dimen.dp14_4)
            }
            val radii = floatArrayOf(
                radio, radio,
                radio, radio,
                radio, radio,
                radio, radio
            )
            // 创建圆角矩形形状
            val roundRectShape = RoundRectShape(
                radii,
                null,
                null
            )

            return ShapeDrawable(roundRectShape).apply {
                paint.color = color
                paint.isAntiAlias = true
            }
        }
    }

    init {
        val inset = context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp1_6)
        setLayerInset(1, inset, inset, inset, inset)
    }
}
