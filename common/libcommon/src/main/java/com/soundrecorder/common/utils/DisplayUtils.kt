/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: DisplayUtils
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.common.utils

import android.content.Context
import android.hardware.display.DisplayManager
import android.view.Display
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil

/**
 * 前置条件只支持一个主屏，一个副屏
 */
object DisplayUtils {

    private const val TAG = "DisplayUtils"
    private const val DISPLAY_CATEGORY_ALL_INCLUDING_DISABLED = "android.hardware.display.category.ALL_INCLUDING_DISABLED"
    private val displayManager by lazy {
        val context = BaseApplication.getAppContext()
        context.getSystemService(Context.DISPLAY_SERVICE) as DisplayManager
    }

    /**
     * 副屏displayId
     */
    @JvmStatic
    val otherId by lazy {
        val id = displayManager.getDisplays(DISPLAY_CATEGORY_ALL_INCLUDING_DISABLED).filter {
            DebugUtil.e(TAG, "$it")
            it.name == mainDisplayName
        }.firstOrNull {
            it.displayId != mainId
        }?.displayId ?: mainId
        DebugUtil.e(TAG, "副屏id：$id")
        id
    }

    /**
     * 判断是否支持副屏
     */
    @JvmStatic
    val supportOtherDisplay by lazy { otherId != mainId }

    /**
     * 主屏displayId
     */
    @JvmStatic
    val mainId by lazy { Display.DEFAULT_DISPLAY }

    /**
     * 主屏displayName
     */
    @JvmStatic
    private val mainDisplayName by lazy { displayManager.getDisplay(mainId).name }

    /**
     * 正在显示的屏幕
     * @return Display
     */
    @JvmStatic
    fun currentDisplay(): Display {
        val a = displayManager.getDisplays(DISPLAY_CATEGORY_ALL_INCLUDING_DISABLED).filter {
            it.name == mainDisplayName
        }.firstOrNull {
            it.state != Display.STATE_OFF
        }
        val b = a ?: displayManager.getDisplay(mainId)
        DebugUtil.e(TAG, "$b")
        return b
    }

    /**
     *  判断是否是主屏
     *  如果要判断主屏是否显示，可以参照如下方式
     *  @see currentDisplay  一起使用 currentDisplay().isDefaultDisplay()
     *  @return Boolean
     */
    @JvmStatic
    fun Display.isDefaultDisplay(): Boolean = displayId == Display.DEFAULT_DISPLAY

    /**
     * 获取副屏Display
     * @return Display
     */
    @JvmStatic
    fun otherDisplay(): Display = displayManager.getDisplay(otherId)

    /**
     * 判断屏幕是否息屏ScreenOff
     */
    @JvmStatic
    fun isScreenLocked(): Boolean {
        val displays = displayManager.displays
        for (display in displays) {
            if (display.state == Display.STATE_ON) {
                DebugUtil.i(TAG, "isScreenLocked = false")
                return false
            }
        }
        DebugUtil.i(TAG, "isScreenLocked = true")
        return true
    }
}