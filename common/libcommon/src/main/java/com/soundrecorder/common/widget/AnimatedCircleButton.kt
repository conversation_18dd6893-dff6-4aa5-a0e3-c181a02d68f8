/************************************************************
 * Copyright 2010-2021 Oplus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : AnimatedCircleButton.kt
 * Version Number: 1.0
 * Description   : record button support vibrate and anim
 * Author        : tianjun
 * Date          : 2021.01.15
 * History       :( ID, Date, Author, Description)
 ************************************************************/
package com.soundrecorder.common.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.PropertyValuesHolder
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.content.res.TypedArray
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Shader
import android.graphics.drawable.Drawable
import android.os.Build
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.animation.PathInterpolator
import androidx.annotation.RequiresApi
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.hapticfeedback.COUIHapticFeedbackConstants
import com.oplus.recorderlog.util.VersionUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.R
import com.soundrecorder.common.widget.glow.GlowDrawableWrapper
import com.soundrecorder.common.widget.glow.GlowDrawableWrapper.Companion.GLOW_END_ALPHA
import java.util.function.Consumer
import androidx.core.content.withStyledAttributes

open class AnimatedCircleButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    companion object {
        const val FLOAT_0 = 0.0F
        const val FLOAT_1 = 1.0F
        const val FLOAT_2 = 2.0F
        const val OPAQUE = 255
        const val HALF_NUMBER = 0.5F
        const val SCALE_100 = 1.0F
        const val FRACTION_40 = 0.4F
        const val DEFAULT_CIRCLE_SCALE = 0.9F
        const val DEFAULT_CIRCLE_RADIUS = 24F
        const val DEFAULT_CIRCLE_COLOR = 0xFFE85654.toInt()
        const val DEFAULT_CIRCLE_GRADIENT_COLOR = -1
        const val ANIM_DURATION_DOWN = 200L
        const val ANIM_DURATION_ENTER = 200L
        const val ANIM_DURATION_UP = 200L
        const val MIN_TIME_PRESSED = 50L
        const val PROPERTY_SCALE_CIRCLE = "scaleCircle"

        private const val PERCENT_0 = 0.0F
        private const val PERCENT_33 = 0.33F
        private const val PERCENT_67 = 0.67F
        private const val PERCENT_90 = 0.9F
        private const val PERCENT_100 = 1.0F

        private val INTERPOLATOR_SWITCH_PRESS =
            PathInterpolator(PERCENT_33, PERCENT_0, PERCENT_67, PERCENT_100)

        private val INTERPOLATOR_SWITCH_LIFT =
            PathInterpolator(PERCENT_33, PERCENT_0, PERCENT_67, PERCENT_100)

        private var TAG = "AnimatedCircleButton"
    }

    var animEnter = false

    var mCircleRadius = 0F

    private var mDownAnimator: ValueAnimator? = null
    private var mUpAnimator: ValueAnimator? = null
    private var mUpMinTimeAnimator: ValueAnimator? = null
    open var mCirclePaint: Paint? = null
    private var mDownTime = 0L

    private var mIsOutButtonInterruptedAnimator = false
    private var mIsDownMinTimeInterruptedAnimator = false
    private var mUpAnimatorStarted = false
    private var mDownAnimatorEnd = false
    private var mActionUp = false

    open var mScaleCircle = SCALE_100

    var mCircleScale = DEFAULT_CIRCLE_SCALE
    open var mCircleColor = DEFAULT_CIRCLE_COLOR
    private var mStateAlpha = FLOAT_1

    private var mCircleCenterX = 0F
    private var mCircleCenterY = 0F
    private var mVibrateToggle = false
    private var mStateChange = true

    open var mCircleGradientStartColor: Int = DEFAULT_CIRCLE_GRADIENT_COLOR
    open var mCircleGradientEndColor: Int = DEFAULT_CIRCLE_GRADIENT_COLOR
    open var mCircleShader: LinearGradient? = null
    open var mCircleShadowColor: Int? = null
    open var mCircleShadowSize: Float = 0F

    var mDrawablePlay: Drawable? = null
    var mDrawablePause: Drawable? = null
    private var mParentBackGround: Drawable? = null
    private var circleListener: Consumer<Float>? = null

    private var mIsGlowEffect: Boolean = false
    private var mReferenceViewId: Int = View.NO_ID
    private var mGlowDrawableWrapper: GlowDrawableWrapper? = null

    private var mPressDownGlowAnimator: ValueAnimator? = null
    private var mPressUpGlowAnimator: ValueAnimator? = null
    private var mIsTargetOutView: Boolean = false


    init {
        //set foreground image center
        foregroundGravity = Gravity.CENTER
        setLayerType(View.LAYER_TYPE_SOFTWARE, null)

        val metrics = context.resources.displayMetrics
        mCircleRadius =
            TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, DEFAULT_CIRCLE_RADIUS, metrics)

        context.withStyledAttributes(attrs, R.styleable.AnimatedCircleButton, defStyleAttr, 0) {
            mCircleRadius =
                getDimension(R.styleable.AnimatedCircleButton_circle_radius, mCircleRadius)
            mCircleColor =
                getColor(R.styleable.AnimatedCircleButton_circle_color, DEFAULT_CIRCLE_COLOR)
            mCircleScale =
                getFloat(R.styleable.AnimatedCircleButton_circle_scale, PERCENT_90)

            initGlowEffectPara(this, context)

            initMutationColor(this)

            if ((FLOAT_0 > mCircleScale) || (mCircleScale > FLOAT_1)) {
                throw IllegalArgumentException("circle scale must in (0..1)")
            }

            mVibrateToggle = getBoolean(R.styleable.AnimatedCircleButton_vibrate_toggle, false)
            mStateChange = getBoolean(R.styleable.AnimatedCircleButton_state_change, true)
            initAnimator()
            initPaint()
        }
    }

    fun initMutationColor(typedArray: TypedArray) {
        mCircleGradientStartColor = typedArray.getColor(
            R.styleable.AnimatedCircleButton_circle_gradient_start_color,
            DEFAULT_CIRCLE_GRADIENT_COLOR
        )
        mCircleGradientEndColor = typedArray.getColor(
            R.styleable.AnimatedCircleButton_circle_gradient_end_color,
            DEFAULT_CIRCLE_GRADIENT_COLOR
        )
        mCircleShadowSize =
            typedArray.getDimension(R.styleable.AnimatedCircleButton_circle_shadow_size, FLOAT_0)
        mCircleShadowColor = typedArray.getColor(
            R.styleable.AnimatedCircleButton_circle_shadow_color,
            DEFAULT_CIRCLE_GRADIENT_COLOR
        )
    }

    private fun initAnimator() {
        mDownAnimator = createDownAnimator()
        mUpAnimator = createUpAnimator(DEFAULT_CIRCLE_SCALE)
        val scaleMinTime = SCALE_100 - (SCALE_100 - mCircleScale) * FRACTION_40
        mUpMinTimeAnimator = createUpAnimator(scaleMinTime)
    }

    private fun initPaint() {
        mCirclePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    }

    override fun onDraw(canvas: Canvas) {
        mCircleCenterX = (width) * HALF_NUMBER
        mCircleCenterY = (height) * HALF_NUMBER
        drawCircle(canvas)
        drawState(canvas)
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (!animEnter) {
            dispatchVibrate(event)
            dispatchAnim(event)
        }
        if (VersionUtil.isSupportGlowEffect()) {
            toggleButtonEffect(event)
        }
        return super.onTouchEvent(event)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        registerGlowEffectLister()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        unRegisterGlowEffectLister()
        mDownAnimator = null
        mUpAnimator = null
        mUpMinTimeAnimator = null
        mCirclePaint = null
        mDrawablePlay = null
        mDrawablePause = null
    }

    open fun drawCircle(canvas: Canvas?) {
        mCirclePaint?.let {
            DebugUtil.i(TAG, "drawCircle mCircleRadius:$mCircleRadius")
             val animRadius = mCircleRadius * mScaleCircle
            val shader = getShader()
            if (shader != null) {
                it.shader = shader //颜色渐变方式绘制
            } else {
                it.color = mCircleColor //纯色方式绘制
            }
            canvas?.drawCircle(mCircleCenterX, mCircleCenterY, animRadius, it)
        }
    }

    @SuppressLint("Range")
    open fun drawState(canvas: Canvas?) {
        canvas?.let {
            drawable?.apply {
                alpha = if (mStateChange) (mStateAlpha * OPAQUE + HALF_NUMBER).toInt() else OPAQUE
                //scale offset
                val scaleY = height * (FLOAT_1 - mScaleCircle) / FLOAT_2
                val scaleX = width * (FLOAT_1 - mScaleCircle) / FLOAT_2
                //reset draw bound
                val scaleTop = (0 + scaleY).toInt()
                val scaleEnd = (width - scaleX).toInt()
                val scaleBottom = (width - scaleY).toInt()
                val scaleStart = (0 + scaleX).toInt()
                bounds = Rect(scaleStart, scaleTop, scaleEnd, scaleBottom)
                draw(it)
            }
        }
    }

    private fun dispatchAnim(event: MotionEvent?) {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> actionDown(event)
            MotionEvent.ACTION_UP -> actionUp()
            MotionEvent.ACTION_MOVE -> actionMove(event)
            MotionEvent.ACTION_CANCEL -> actionCancel()
        }
    }

    private fun dispatchVibrate(event: MotionEvent?) {
        if (!mVibrateToggle) {
            return
        }
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                if ((mDownAnimator?.isRunning == true)
                    || (mUpAnimator?.isRunning == true)
                    || (mUpMinTimeAnimator?.isRunning == true)
                ) {
                    //when anim is running return
                    DebugUtil.i(TAG, "dispatchVibrate down or up anim is running.")
                    return
                }
                vibrate()
            }

            MotionEvent.ACTION_UP -> {
                if (!mIsOutButtonInterruptedAnimator) {
                    vibrate()
                }
            }
        }
    }

    private fun vibrate() {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q) {
            performHapticFeedback(COUIHapticFeedbackConstants.GRANULAR_SHORT_VIBRATE_SYNC)
        }
    }

    private fun actionDown(event: MotionEvent?) {
        DebugUtil.i(TAG, "actionDown scaleCircle = $mScaleCircle")
        if ((mDownAnimator?.isRunning == true)
            || (mUpAnimator?.isRunning == true)
            || (mUpMinTimeAnimator?.isRunning == true)
        ) {
            //when anim is running return
            DebugUtil.i(TAG, "actionDown down or up anim is running.")
            return
        }
        mUpAnimatorStarted = false
        mDownAnimatorEnd = false
        mIsDownMinTimeInterruptedAnimator = false
        mIsOutButtonInterruptedAnimator = false
        mActionUp = false
        mDownTime = System.currentTimeMillis()
        if (isTouchTargetView(event)) {
            mDownAnimator?.start()
            hideParentBackground()
        }
    }

    private fun actionMove(event: MotionEvent?) {
        if (!isTouchTargetView(event)) {
            if (mIsOutButtonInterruptedAnimator) {
                //when out the button return
                DebugUtil.i(TAG, "actionMove is out button interrupted anim.")
                return
            }
            mIsOutButtonInterruptedAnimator = true
            actionUp()
        }
    }

    private fun actionUp() {
        DebugUtil.i(TAG, "actionUp ${hashCode()}")
        if (mUpAnimatorStarted) {
            //when up anim is started return
            DebugUtil.i(TAG, "actionUp anim up is started.")
            return
        }
        if (isMinPressedTime()) {
            mIsDownMinTimeInterruptedAnimator = true
        }
        /*When the animation is turned off in the developer mode,
        Need to judge whether the down animation ends
        And interval is pressed down-up less than 50ms*/
        if (mIsDownMinTimeInterruptedAnimator && !mDownAnimatorEnd) {
            DebugUtil.i(TAG, "actionUp is down min time and anim down end.")
            return
        }
        if (mDownAnimatorEnd) {
            mUpAnimator?.start()
        }
        mActionUp = true
        showParentBackground()
    }

    private fun actionCancel() {
        DebugUtil.i(TAG, "actionCancel")
        actionUp()
    }

    private fun hideParentBackground() {
        parent.let {
            val view = it as View
            mParentBackGround = view.background
            view.background = null
            DebugUtil.i(TAG, "hideParentBackground")
            view.invalidate()
        }
    }

    private fun showParentBackground() {
        parent.let {
            val view = it as View
            view.background = mParentBackGround
            DebugUtil.i(TAG, "showParentBackground")
            view.invalidate()
        }
    }

    private fun isTouchTargetView(event: MotionEvent?): Boolean {
        if (event == null) {
            return false
        }
        var isTouchInView = false
        val x = event.x
        val y = event.y
        val visibleRect = Rect()
        getLocalVisibleRect(visibleRect)
        visibleRect.apply {
            if ((left <= x) && (x <= right) && (top <= y) && (y <= bottom)) {
                isTouchInView = true
            }
        }
        return isTouchInView
    }

    //check pressed time is 50ms?
    private fun isMinPressedTime(): Boolean {
        val intervalTime = System.currentTimeMillis() - mDownTime
        return intervalTime <= MIN_TIME_PRESSED
    }

    private fun createDownAnimator(): ValueAnimator {
        DebugUtil.i(TAG, "createDownAnimator end scale = $mCircleScale")
        val scaleHolder =
            PropertyValuesHolder.ofFloat(PROPERTY_SCALE_CIRCLE, PERCENT_100, mCircleScale)
        val downAnimator = ValueAnimator.ofPropertyValuesHolder(scaleHolder)
        downAnimator.duration = ANIM_DURATION_DOWN
        downAnimator.interpolator = INTERPOLATOR_SWITCH_PRESS
        downAnimator.addUpdateListener {
            mScaleCircle = it.getAnimatedValue(PROPERTY_SCALE_CIRCLE) as? Float ?: SCALE_100
            val currentFraction = it.animatedFraction
            mStateAlpha = FLOAT_1 - currentFraction
            if (mIsDownMinTimeInterruptedAnimator && (currentFraction > FRACTION_40)) {
                DebugUtil.i(TAG, "createDownAnimator when down min time cancel down anim , play up anim.")
                mDownAnimator?.cancel()
                mUpMinTimeAnimator?.start()
            }
            updateCircleScale()
            invalidate()
        }
        downAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                mDownAnimatorEnd = true
                if (mActionUp && !mIsDownMinTimeInterruptedAnimator) {
                    mUpAnimator?.start()
                }
            }
        })
        return downAnimator
    }

    private fun createUpAnimator(startScale: Float): ValueAnimator {
        DebugUtil.i(TAG, "createUpAnimator start scale = $startScale")
        val scaleCircleHolder =
            PropertyValuesHolder.ofFloat(PROPERTY_SCALE_CIRCLE, startScale, SCALE_100)
        val upAnimator = ValueAnimator.ofPropertyValuesHolder(scaleCircleHolder)
        upAnimator.duration = ANIM_DURATION_UP
        upAnimator.interpolator = INTERPOLATOR_SWITCH_LIFT
        upAnimator.addUpdateListener {
            mStateAlpha = it.animatedFraction
            mScaleCircle = it.getAnimatedValue(PROPERTY_SCALE_CIRCLE) as? Float ?: SCALE_100
            updateCircleScale()
            invalidate()
        }
        upAnimator.addListener(object : AnimatorListenerAdapter() {

            override fun onAnimationStart(animation: Animator) {
                DebugUtil.i(TAG, "createUpAnimator onAnimationStart ${hashCode()}")
                mUpAnimatorStarted = true
                if (!mIsOutButtonInterruptedAnimator) {
                    val minTimeScale = PERCENT_100 - (PERCENT_100 - PERCENT_90) * FRACTION_40
                    val scale = if (mIsDownMinTimeInterruptedAnimator) minTimeScale else PERCENT_90
                    TransitionUtils.startScale = scale
                } else {
                    DebugUtil.i(TAG, "createUpAnimator when out the button, there is no need to respond to events.")
                }
            }

            override fun onAnimationEnd(animation: Animator) {
                DebugUtil.i(TAG, "createUpAnimator onAnimationEnd ${hashCode()}")
                invalidate()
            }
        })
        return upAnimator
    }

    fun createEnterAnimator(startScale: Float): ValueAnimator {
        DebugUtil.i(TAG, "createEnterAnimator start scale = $startScale")
        val scaleCircleHolder =
            PropertyValuesHolder.ofFloat(PROPERTY_SCALE_CIRCLE, startScale, SCALE_100)
        val enterAnimator = ValueAnimator.ofPropertyValuesHolder(scaleCircleHolder)
        enterAnimator.duration = ANIM_DURATION_ENTER
        enterAnimator.addUpdateListener {
            mScaleCircle = it.getAnimatedValue(PROPERTY_SCALE_CIRCLE) as? Float ?: SCALE_100
            updateCircleScale()
            invalidate()
        }
        enterAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                DebugUtil.i(TAG, "createEnterAnimator onAnimationEnd ${hashCode()}")
                animEnter = false
            }
        })
        return enterAnimator
    }

    open fun switchPlayState() {
        if (mDrawablePlay == null) {
            mDrawablePlay = ContextCompat.getDrawable(context, R.drawable.ic_play_icon)
        }
        contentDescription = context.getString(R.string.talkback_pause)
        setImageDrawable(mDrawablePlay)
    }

    open fun switchPauseState() {
        if (mDrawablePause == null) {
            mDrawablePause = ContextCompat.getDrawable(context, R.drawable.ic_pause_icon)
        }
        contentDescription = context.getString(R.string.talkback_play)
        setImageDrawable(mDrawablePause)
    }

    open fun setCircleColor(enable: Boolean) {
        mCircleColor = if (!enable) {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPressBackground)
        } else {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
        }
        invalidate()
    }

    fun refreshCircleRadius(radius: Float) {
        mCircleRadius = radius
    }

    /**
     * 按钮背景色，shader
     */
    private fun getShader(): LinearGradient? {
        if (mCircleShader != null) {
            return mCircleShader
        }
        if (mCircleGradientEndColor != DEFAULT_CIRCLE_GRADIENT_COLOR && mCircleGradientStartColor != DEFAULT_CIRCLE_GRADIENT_COLOR) {
            val colors = intArrayOf(mCircleGradientStartColor, mCircleGradientEndColor)
            mCircleShader = LinearGradient(0f, 0f, 0f, height.toFloat(), colors, null, Shader.TileMode.CLAMP)
            return mCircleShader
        }
        return null
    }

    private fun initGlowEffectPara(typedArray: TypedArray, context: Context) {
        if (!VersionUtil.isSupportGlowEffect()) {
            return
        }
        mIsGlowEffect =
            typedArray.getBoolean(R.styleable.AnimatedCircleButton_glow_enable, false)
        mReferenceViewId =
            typedArray.getResourceId(R.styleable.AnimatedCircleButton_reference_id, View.NO_ID)
        mGlowDrawableWrapper = GlowDrawableWrapper(context)
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun initPressDownGlowEffectAnimation() {
        if (mPressDownGlowAnimator == null) {
            mPressDownGlowAnimator = mGlowDrawableWrapper?.createAlphaAnimator(true)
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun initPressUpGlowEffectAnimation() {
        mPressUpGlowAnimator = mGlowDrawableWrapper?.createAlphaAnimator(GLOW_END_ALPHA, false)
        mPressDownGlowAnimator?.let {
            if (it.isRunning) {
                it.addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        mPressUpGlowAnimator?.start()
                    }
                })
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun togglePressDownEffectAnimation() {
        DebugUtil.i(TAG, "togglePressDownEffectAnimation")
        initPressDownGlowEffectAnimation()
        mPressDownGlowAnimator?.start()
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun togglePressUpEffectAnimation() {
        DebugUtil.i(TAG, "togglePressUpEffectAnimation")
        initPressUpGlowEffectAnimation()
        if (mPressDownGlowAnimator?.isRunning != true) {
            mPressUpGlowAnimator?.start()
        }
    }

    private fun registerGlowEffectLister() {
        if (!VersionUtil.isSupportGlowEffect()) {
            return
        }

        if (!configSupportGlowEffect()) {
            return
        }

        mGlowDrawableWrapper?.let {
            this.viewTreeObserver?.addOnGlobalLayoutListener(it)
            it.setTargetView((parent as? View)?.findViewById<View>(mReferenceViewId))
            it.setReferenceView(this)
        }
    }

    private fun unRegisterGlowEffectLister() {
        if (!VersionUtil.isSupportGlowEffect()) {
            return
        }
        if (!configSupportGlowEffect()) {
            return
        }
        mGlowDrawableWrapper?.let {
            this.viewTreeObserver?.removeOnGlobalLayoutListener(it)
        }
        mGlowDrawableWrapper = null
    }

    private fun toggleButtonEffect(event: MotionEvent?) {
        event?.run {
            when (event.actionMasked) {
                MotionEvent.ACTION_DOWN -> {
                    DebugUtil.d(TAG, "toggleButtonEffect ACTION_DOWN")
                    if (!isTouchTargetView(event)) {
                        return
                    }
                    if (VersionUtil.isSupportGlowEffect()) {
                        togglePressDownEffectAnimation()
                    }
                    mIsTargetOutView = isTouchTargetView(event)
                }

                MotionEvent.ACTION_MOVE -> {
                    DebugUtil.i(TAG, "toggleButtonEffect ACTION_MOVE")
                    if (isSlideOutTargetView(event) && VersionUtil.isSupportGlowEffect()) {
                        togglePressUpEffectAnimation()
                    }

                    if (isSlideInTargetView(event) && VersionUtil.isSupportGlowEffect()) {
                        togglePressDownEffectAnimation()
                    }

                    mIsTargetOutView = isTouchTargetView(event)
                }

                MotionEvent.ACTION_CANCEL,
                MotionEvent.ACTION_UP -> {
                    if (event.actionMasked == MotionEvent.ACTION_CANCEL) {
                        DebugUtil.i(TAG, "toggleButtonEffect ACTION_CANCEL")
                    } else {
                        DebugUtil.i(TAG, "toggleButtonEffect ACTION_UP")
                    }
                    if (VersionUtil.isSupportGlowEffect() && isTouchTargetView(event)) {
                        togglePressUpEffectAnimation()
                    }
                    mIsTargetOutView = isTouchTargetView(event)
                }

                else -> DebugUtil.i(TAG, "toggleButtonEffect Unknown event")
            }
        }
    }

    private fun isSlideOutTargetView(event: MotionEvent?): Boolean {
        return mIsTargetOutView && (!isTouchTargetView(event))
    }

    private fun isSlideInTargetView(event: MotionEvent?): Boolean {
        return !mIsTargetOutView && isTouchTargetView(event)
    }

    private fun configSupportGlowEffect(): Boolean {
        if (!mIsGlowEffect && mReferenceViewId == View.NO_ID) {
            return false
        }

        return if (isAttachedToWindow) {
            (parent as? View)?.findViewById<View>(mReferenceViewId) != null
        } else {
            true
        }
    }

    private fun updateCircleScale() {
        circleListener?.accept(mScaleCircle)
    }

    fun setCircleScaleListener(listener: Consumer<Float>) {
        circleListener = listener
    }
}