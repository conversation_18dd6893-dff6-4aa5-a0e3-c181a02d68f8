/************************************************************
 * Copyright 2010-2021 Oplus Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : AnimatedCircleButton.kt
 * Version Number: 1.0
 * Description   : record button support vibrate and anim
 * Author        : tianjun
 * Date          : 2021.01.15
 * History       :( ID, Date, Author, Description)
 ************************************************************/
package com.soundrecorder.common.widget

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.PropertyValuesHolder
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.content.res.TypedArray
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Shader
import android.graphics.drawable.Drawable
import android.os.Build
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.view.animation.PathInterpolator
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.hapticfeedback.COUIHapticFeedbackConstants
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.R

//open for unit test
open class AnimatedCircleButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatImageView(context, attrs, defStyleAttr) {

    companion object {
        const val FLOAT_0 = 0.0F
        const val FLOAT_1 = 1.0F
        const val FLOAT_2 = 2.0F
        const val OPAQUE = 255

        const val HALF_NUMBER = 0.5F

        const val SCALE_100 = 1.0F
        const val FRACTION_40 = 0.4F

        const val DEFAULT_CIRCLE_SCALE = 0.9F
        const val DEFAULT_CIRCLE_RADIUS = 24F
        const val DEFAULT_CIRCLE_COLOR = 0xFFE85654.toInt()
        const val DEFAULT_CIRCLE_GRADIENT_COLOR = -1

        const val ANIM_DURATION_DOWN = 200L
        const val ANIM_DURATION_ENTER = 200L
        const val ANIM_DURATION_UP = 200L

        const val MIN_TIME_PRESSED = 50L
        const val PROPERTY_SCALE_CIRCLE = "scaleCircle"

        private const val PERCENT_0 = 0.0F
        private const val PERCENT_33 = 0.33F
        private const val PERCENT_67 = 0.67F
        private const val PERCENT_90 = 0.9F
        private const val PERCENT_100 = 1.0F

        private val INTERPOLATOR_SWITCH_PRESS =
            PathInterpolator(PERCENT_33, PERCENT_0, PERCENT_67, PERCENT_100)
        private val INTERPOLATOR_SWITCH_LIFT =
            PathInterpolator(PERCENT_33, PERCENT_0, PERCENT_67, PERCENT_100)
    }

    var animEnter = false

    open var mCircleRadius = 0F
    open var logTag = "AnimatedCircleButton"
    private var mDownAnimator: ValueAnimator? = null
    private var mUpAnimator: ValueAnimator? = null
    private var mUpMinTimeAnimator: ValueAnimator? = null
    open var mCirclePaint: Paint? = null
    private var mDownTime = 0L

    private var mIsOutButtonInterruptedAnimator = false
    private var mIsDownMinTimeInterruptedAnimator = false
    private var mUpAnimatorStarted = false
    private var mDownAnimatorEnd = false
    private var mActionUp = false

    open var mScaleCircle = SCALE_100

    open var mCircleScale = DEFAULT_CIRCLE_SCALE
    open var mCircleColor = DEFAULT_CIRCLE_COLOR
    private var mStateAlpha = FLOAT_1

    private var mCircleCenterX = 0F
    private var mCircleCenterY = 0F
    private var mVibrateToggle = false
    private var mStateChange = true

    open var mCircleGradientStartColor: Int = DEFAULT_CIRCLE_GRADIENT_COLOR
    open var mCircleGradientEndColor: Int = DEFAULT_CIRCLE_GRADIENT_COLOR
    open var mCircleShader: LinearGradient? = null
    open var mCircleShadowColor: Int? = null
    open var mCircleShadowSize: Float = 0F

    var mDrawablePlay: Drawable? = null
    var mDrawablePause: Drawable? = null
    private var mParentBackGround: Drawable? = null


    init {
        //set foreground image center
        foregroundGravity = Gravity.CENTER
        setLayerType(View.LAYER_TYPE_SOFTWARE, null)

        val metrics = context.resources.displayMetrics
        mCircleRadius =
            TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, DEFAULT_CIRCLE_RADIUS, metrics)

        val typedArray =
            context.obtainStyledAttributes(attrs, R.styleable.AnimatedCircleButton, defStyleAttr, 0)

        mCircleRadius =
            typedArray.getDimension(R.styleable.AnimatedCircleButton_circle_radius, mCircleRadius)
        mCircleColor =
            typedArray.getColor(R.styleable.AnimatedCircleButton_circle_color, DEFAULT_CIRCLE_COLOR)
        mCircleScale =
            typedArray.getFloat(R.styleable.AnimatedCircleButton_circle_scale, PERCENT_90)

        initMutationColor(typedArray)

        if ((FLOAT_0 > mCircleScale) || (mCircleScale > FLOAT_1)) {
            throw IllegalArgumentException("circle scale must in (0..1)")
        }

        mVibrateToggle =
            typedArray.getBoolean(R.styleable.AnimatedCircleButton_vibrate_toggle, false)
        mStateChange = typedArray.getBoolean(R.styleable.AnimatedCircleButton_state_change, true)
        initAnimator()
        initPaint()
        typedArray.recycle()
    }

    fun initMutationColor(typedArray: TypedArray) {
        mCircleGradientStartColor = typedArray.getColor(
            R.styleable.AnimatedCircleButton_circle_gradient_start_color,
            DEFAULT_CIRCLE_GRADIENT_COLOR
        )
        mCircleGradientEndColor = typedArray.getColor(
            R.styleable.AnimatedCircleButton_circle_gradient_end_color,
            DEFAULT_CIRCLE_GRADIENT_COLOR
        )
        mCircleShadowSize =
            typedArray.getDimension(R.styleable.AnimatedCircleButton_circle_shadow_size, FLOAT_0)
        mCircleShadowColor = typedArray.getColor(
            R.styleable.AnimatedCircleButton_circle_shadow_color,
            DEFAULT_CIRCLE_GRADIENT_COLOR
        )
    }

    private fun initAnimator() {
        mDownAnimator = createDownAnimator()
        mUpAnimator = createUpAnimator(DEFAULT_CIRCLE_SCALE)
        val scaleMinTime = SCALE_100 - (SCALE_100 - mCircleScale) * FRACTION_40
        mUpMinTimeAnimator = createUpAnimator(scaleMinTime)
    }

    private fun initPaint() {
        mCirclePaint = Paint(Paint.ANTI_ALIAS_FLAG)
    }

    override fun onDraw(canvas: Canvas) {
        mCircleCenterX = (left + right) * HALF_NUMBER
        mCircleCenterY = (top + bottom) * HALF_NUMBER
        drawCircle(canvas)
        drawState(canvas)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (!animEnter) {
            dispatchVibrate(event)
            dispatchAnim(event)
        }
        return super.onTouchEvent(event)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mDownAnimator = null
        mUpAnimator = null
        mUpMinTimeAnimator = null
        mCirclePaint = null
        mDrawablePlay = null
        mDrawablePause = null
    }

    open fun drawCircle(canvas: Canvas?) {
        mCirclePaint?.let {
            val animRadius = mCircleRadius * mScaleCircle
            val shader = getShader()
            if (shader != null) {
                it.shader = shader //颜色渐变方式绘制
            } else {
                it.color = mCircleColor //纯色方式绘制
            }
            canvas?.drawCircle(mCircleCenterX, mCircleCenterY, animRadius, it)
        }
    }

    @SuppressLint("Range")
    open fun drawState(canvas: Canvas?) {
        canvas?.let {
            drawable?.apply {
                alpha = if (mStateChange) (mStateAlpha * OPAQUE + HALF_NUMBER).toInt() else OPAQUE
                //scale offset
                val scaleY = height * (FLOAT_1 - mScaleCircle) / FLOAT_2
                val scaleX = width * (FLOAT_1 - mScaleCircle) / FLOAT_2
                //reset draw bound
                val scaleTop = (top + scaleY).toInt()
                val scaleEnd = (right - scaleX).toInt()
                val scaleBottom = (bottom - scaleY).toInt()
                val scaleStart = (left + scaleX).toInt()
                bounds = Rect(scaleStart, scaleTop, scaleEnd, scaleBottom)
                draw(it)
            }
        }
    }

    private fun dispatchAnim(event: MotionEvent?) {
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> actionDown(event)
            MotionEvent.ACTION_UP -> actionUp()
            MotionEvent.ACTION_MOVE -> actionMove(event)
            MotionEvent.ACTION_CANCEL -> actionCancel()
        }
    }

    private fun dispatchVibrate(event: MotionEvent?) {
        if (!mVibrateToggle) {
            return
        }
        when (event?.action) {
            MotionEvent.ACTION_DOWN -> {
                if ((mDownAnimator?.isRunning == true)
                    || (mUpAnimator?.isRunning == true)
                    || (mUpMinTimeAnimator?.isRunning == true)
                ) {
                    //when anim is running return
                    DebugUtil.i(logTag, "dispatchVibrate down or up anim is running.")
                    return
                }
                vibrate()
            }

            MotionEvent.ACTION_UP -> {
                if (!mIsOutButtonInterruptedAnimator) {
                    vibrate()
                }
            }
        }
    }

    private fun vibrate() {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.Q) {
            performHapticFeedback(COUIHapticFeedbackConstants.GRANULAR_SHORT_VIBRATE_SYNC)
        }
    }

    private fun actionDown(event: MotionEvent?) {
        DebugUtil.i(logTag, "<<< actionDown scaleCircle = $mScaleCircle")
        if ((mDownAnimator?.isRunning == true)
            || (mUpAnimator?.isRunning == true)
            || (mUpMinTimeAnimator?.isRunning == true)
        ) {
            //when anim is running return
            DebugUtil.i(logTag, "actionDown down or up anim is running.")
            return
        }
        mUpAnimatorStarted = false
        mDownAnimatorEnd = false
        mIsDownMinTimeInterruptedAnimator = false
        mIsOutButtonInterruptedAnimator = false
        mActionUp = false
        mDownTime = System.currentTimeMillis()
        if (isInButtonView(event)) {
            mDownAnimator?.start()
            hideParentBackground()
        }
    }

    private fun actionMove(event: MotionEvent?) {
        if (!isInButtonView(event)) {
            if (mIsOutButtonInterruptedAnimator) {
                //when out the button return
                DebugUtil.i(logTag, "actionMove is out button interrupted anim.")
                return
            }
            mIsOutButtonInterruptedAnimator = true
            actionUp()
        }
    }

    private fun actionUp() {
        DebugUtil.i(logTag, "<<< actionUp ${hashCode()}")
        if (mUpAnimatorStarted) {
            //when up anim is started return
            DebugUtil.i(logTag, "actionUp anim up is started.")
            return
        }
        if (isMinPressedTime()) {
            mIsDownMinTimeInterruptedAnimator = true
        }
        /*When the animation is turned off in the developer mode,
        Need to judge whether the down animation ends
        And interval is pressed down-up less than 50ms*/
        if (mIsDownMinTimeInterruptedAnimator && !mDownAnimatorEnd) {
            DebugUtil.i(logTag, "actionUp is down min time and anim down end.")
            return
        }
        if (mDownAnimatorEnd) {
            mUpAnimator?.start()
        }
        mActionUp = true
        showParentBackground()
    }

    private fun actionCancel() {
        DebugUtil.i(logTag, "<<< actionCancel")
        actionUp()
    }

    private fun hideParentBackground() {
        parent.let {
            val view = it as View
            mParentBackGround = view.background
            view.background = null
            DebugUtil.i(logTag, "hideParentBackground")
            view.invalidate()
        }
    }

    private fun showParentBackground() {
        parent.let {
            val view = it as View
            view.background = mParentBackGround
            DebugUtil.i(logTag, "showParentBackground")
            view.invalidate()
        }
    }

    private fun isInButtonView(event: MotionEvent?): Boolean {
        var isInButtonView = false
        val x = event?.x ?: FLOAT_0
        val y = event?.y ?: FLOAT_0
        val rect = Rect()
        getLocalVisibleRect(rect)
        rect.apply {
            if ((left <= x) && (x <= right) && (top <= y) && (y <= bottom)) {
                isInButtonView = true
            }
        }
        return isInButtonView
    }

    //check pressed time is 50ms?
    private fun isMinPressedTime(): Boolean {
        val intervalTime = System.currentTimeMillis() - mDownTime
        return intervalTime <= MIN_TIME_PRESSED
    }

    private fun createDownAnimator(): ValueAnimator {
        DebugUtil.i(logTag, "<<< createDownAnimator end scale = $mCircleScale")
        val scaleHolder =
            PropertyValuesHolder.ofFloat(PROPERTY_SCALE_CIRCLE, PERCENT_100, mCircleScale)
        val downAnimator = ValueAnimator.ofPropertyValuesHolder(scaleHolder)
        downAnimator.duration = ANIM_DURATION_DOWN
        downAnimator.interpolator = INTERPOLATOR_SWITCH_PRESS
        downAnimator.addUpdateListener {
            mScaleCircle = it?.getAnimatedValue(PROPERTY_SCALE_CIRCLE) as? Float ?: SCALE_100
            val currentFraction = it?.animatedFraction ?: FLOAT_0
            mStateAlpha = FLOAT_1 - currentFraction
            DebugUtil.i(logTag, "mScaleCircle = $mScaleCircle currentFraction = $currentFraction")
            if (mIsDownMinTimeInterruptedAnimator && (currentFraction > FRACTION_40)) {
                DebugUtil.i(logTag, "when down min time cancel down anim , play up anim.")
                mDownAnimator?.cancel()
                mUpMinTimeAnimator?.start()
            }
            invalidate()
        }
        downAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                mDownAnimatorEnd = true
                if (mActionUp && !mIsDownMinTimeInterruptedAnimator) {
                    mUpAnimator?.start()
                }
            }
        })
        return downAnimator
    }

    private fun createUpAnimator(startScale: Float): ValueAnimator {
        DebugUtil.i(logTag, "<<< createUpAnimator start scale = $startScale")
        val scaleCircleHolder =
            PropertyValuesHolder.ofFloat(PROPERTY_SCALE_CIRCLE, startScale, SCALE_100)
        val upAnimator = ValueAnimator.ofPropertyValuesHolder(scaleCircleHolder)
        upAnimator.duration = ANIM_DURATION_UP
        upAnimator.interpolator = INTERPOLATOR_SWITCH_LIFT
        upAnimator.addUpdateListener {
            mStateAlpha = it?.animatedFraction ?: FLOAT_1
            mScaleCircle = it?.getAnimatedValue(PROPERTY_SCALE_CIRCLE) as? Float ?: SCALE_100
            invalidate()
        }
        upAnimator.addListener(object : AnimatorListenerAdapter() {

            override fun onAnimationStart(animation: Animator) {
                DebugUtil.i(logTag, "<<< createUpAnimator onAnimationStart ${hashCode()}")
                mUpAnimatorStarted = true
                if (!mIsOutButtonInterruptedAnimator) {
                    val minTimeScale = PERCENT_100 - (PERCENT_100 - PERCENT_90) * FRACTION_40
                    val scale = if (mIsDownMinTimeInterruptedAnimator) minTimeScale else PERCENT_90
                    TransitionUtils.startScale = scale
                } else {
                    DebugUtil.i(logTag, "when out the button, there is no need to respond to events.")
                }
            }

            override fun onAnimationEnd(animation: Animator) {
                DebugUtil.i(logTag, "<<< createUpAnimator onAnimationEnd ${hashCode()}")
                invalidate()
            }
        })
        return upAnimator
    }

    fun createEnterAnimator(startScale: Float): ValueAnimator {
        DebugUtil.i(logTag, "<<< createEnterAnimator start scale = $startScale")
        val scaleCircleHolder =
            PropertyValuesHolder.ofFloat(PROPERTY_SCALE_CIRCLE, startScale, SCALE_100)
        val enterAnimator = ValueAnimator.ofPropertyValuesHolder(scaleCircleHolder)
        enterAnimator.duration = ANIM_DURATION_ENTER
        enterAnimator.addUpdateListener {
            mScaleCircle = it?.getAnimatedValue(PROPERTY_SCALE_CIRCLE) as? Float ?: SCALE_100
            invalidate()
        }
        enterAnimator.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                DebugUtil.i(logTag, "<<< createEnterAnimator onAnimationEnd ${hashCode()}")
                animEnter = false
            }
        })
        return enterAnimator
    }

    open fun switchPlayState() {
        if (mDrawablePlay == null) {
            mDrawablePlay = ContextCompat.getDrawable(context, R.drawable.ic_play_icon)
        }
        contentDescription = context.getString(R.string.talkback_pause)
        setImageDrawable(mDrawablePlay)
    }

    open fun switchPauseState() {
        if (mDrawablePause == null) {
            mDrawablePause = ContextCompat.getDrawable(context, R.drawable.ic_pause_icon)
        }
        contentDescription = context.getString(R.string.talkback_play)
        setImageDrawable(mDrawablePause)
    }

    open fun setCircleColor(enable: Boolean) {
        mCircleColor = if (!enable) {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPressBackground)
        } else {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary)
        }
        invalidate()
    }

    fun refreshCircleRadius(radius: Float) {
        mCircleRadius = radius
    }

    /**
     * 按钮背景色，shader
     */
    private fun getShader(): LinearGradient? {
        if (mCircleShader != null) {
            return mCircleShader
        }
        if (mCircleGradientEndColor != DEFAULT_CIRCLE_GRADIENT_COLOR && mCircleGradientStartColor != DEFAULT_CIRCLE_GRADIENT_COLOR) {
            val colors = intArrayOf(mCircleGradientStartColor, mCircleGradientEndColor)
            mCircleShader = LinearGradient(0f, 0f, 0f, height.toFloat(), colors, null, Shader.TileMode.CLAMP)
            return mCircleShader
        }
        return null
    }
}