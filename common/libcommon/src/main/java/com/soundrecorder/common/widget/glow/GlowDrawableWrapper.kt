/********************************************************************
 *  * Copyright (C), 2025 -2035 Oplus. All rights reserved..
 *  * VENDOR_EDIT
 *  * File        : GlowDrawableWrapper.kt
 *  * Description :
 *  * Version     : 1.0
 *  * Date        : 2025/7/10
 *  * Author      : 80414876
 *  * ---------------------Revision History: ----------------------------
 *  *  <author>        <data>       <version>  <desc>
 *  * 80414876        2025/7/10       1.0      create
 *  ********************************************************************
 */
package com.soundrecorder.common.widget.glow

import android.animation.PropertyValuesHolder
import android.animation.ValueAnimator
import android.content.Context
import android.os.Build
import android.view.View
import android.view.ViewTreeObserver
import android.view.animation.PathInterpolator
import androidx.annotation.ChecksSdkIntAtLeast
import androidx.annotation.RequiresApi
import com.oplus.vfxsdk.rsview.COEShadowDrawable
import com.oplus.vfxsdk.rsview.RuntimeShaderOptions
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.widget.AnimatedCircleButton
import com.soundrecorder.common.widget.AnimatedCircleButton.Companion.ANIM_DURATION_ENTER

class GlowDrawableWrapper(viewContext: Context) : ViewTreeObserver.OnWindowAttachListener, ViewTreeObserver.OnGlobalLayoutListener {
    companion object {
        private const val INVALID_SIZE = 0.0f
        const val TAG = "GlowDrawableWrapper"
        const val PROPERTY_ALPHA_CIRCLE = "glowAlpha"
        const val GLOW_BEGIN_ALPHA = 0.0f
        const val GLOW_END_ALPHA = 1.0f
        const val SCALE_END = 1.0f
        private const val PERCENT_0 = 0.0F
        private const val PERCENT_33 = 0.33F
        private const val PERCENT_67 = 0.67F
        private const val PERCENT_100 = 1.0F
        private const val GLOW_CENTER_X_RATE = 0.5F
        private const val GLOW_CENTER_Y_RATE = 0.5F
        private const val GLOW_LIGHT_VALUE = 0.2F
        private const val GLOW_BASE_A_VALUE = 0.0F
        private const val GLOW_BEGIN_ALPHA_DEFAULT = 0.0F
        private const val RADIUS_RATE = 2F
        private const val GLOW_OFFSET = 4
        private const val GLOW_PADDING_RATE = 1.0F

        private const val AGSL_BASE_A_PARA = "BaseA"
        private const val AGSL_GLOW_PARA = "glow"
        private const val AGSL_PADDING_PARA = "padding"
        private const val AGSL_CENTER_X_PARA = "centerx"
        private const val AGSL_CENTER_Y_PARA = "centery"
        private const val AGSL_PADDING_ALPHA_PARA = "paddingAlpha"
        private const val AGSL_LIGHT_COLOR_PARA = "lightColor"
        private const val AGSL_BUTTON_SIZE_PARA = "buttonSize"
        private const val AGSL_BUTTON_CORNER_PARA = "buttonCorner"
        private const val AGSL_ASSERT_FILE_NAME = "cal_btn_glow_0328.coz"

        private val INTERPOLATOR_BUTTON_PRESS =
            PathInterpolator(PERCENT_33, PERCENT_0, PERCENT_67, PERCENT_100)
    }

    data class DrawableConfig(
        /**
         * 按钮宽度
         */
        val width: Float,
        /**
         * 按钮高度
         */
        val height: Float,
        /**
         * 圆角半径
         */
        val cornerRadius: Float,
        /**
         * 光的渐变区域长度，默认设置宽度乘以paddingFactor
         */
        val glowPadding: Float,
    )

    constructor(targetView: View, viewContext: Context) : this(viewContext) {
        this.mGlowTargetView = targetView
    }

    /**
     * 动画绘制区域的偏移值，需要比drawable的bounds大一点，避免边缘被裁切。
     */
    private val offset = GLOW_OFFSET

    /**
     * 按钮的发光时的颜色
     */
    private var lightColor = floatArrayOf(1f, 1f, 1f, 1f)

    /**
     * 用来设置给Button的foreground。
     */
    @Volatile
    private var mShaderDrawable: COEShadowDrawable? = null

    private var paddingAlpha: Float = 1f

    private var mGlowTargetView: View? = null

    private var mReferenceView: View? = null

    private var mIsInitGlowDrawable: Boolean = false

    init {
        initViewListener()
        if (isSupportGlowEffect()) {
            initDrawable(viewContext, AGSL_ASSERT_FILE_NAME)
        }
    }

    fun setTargetView(targetView: View?) {
        this.mGlowTargetView = targetView
    }

    private fun initViewListener() {
        mGlowTargetView?.viewTreeObserver?.let {
            it.addOnWindowAttachListener(this@GlowDrawableWrapper)
            it.addOnGlobalLayoutListener(this@GlowDrawableWrapper)
        }
    }

    fun setReferenceView(referenceView: View?) {
        mReferenceView = referenceView
    }

    /**
     * 需要先初始化drawable才能使用其他接口。
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun initDrawable(context: Context, assetsFile: String): COEShadowDrawable? {
        if (mShaderDrawable == null) {
            synchronized(this) {
                if (mShaderDrawable == null) {
                    mShaderDrawable = createShaderDrawable(context, assetsFile)
                }
            }
        }
        return mShaderDrawable
    }

    fun getDrawable(): COEShadowDrawable? {
        synchronized(this) {
            return mShaderDrawable
        }
    }

    /**
     * 设置按钮发光相关参数。
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun initGlowConfig(config: DrawableConfig) {
        setButtonSize(config.width, config.height)
        setButtonCorner(config.cornerRadius)
        setGlowPadding(config.glowPadding)
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun createShaderDrawable(context: Context, assetsFile: String): COEShadowDrawable {
        //加载文件名
        val shaderDrawable = COEShadowDrawable(
            context,
            assetsFile, options = RuntimeShaderOptions(alphaPreMultiplied = false)
        )
        //drawable的bounds设置的比View大一点，避免边缘被裁切
        shaderDrawable.setOffset(-offset, -offset)
        shaderDrawable.setParameter(AGSL_BASE_A_PARA, GLOW_BASE_A_VALUE)
        shaderDrawable.setParameter(AGSL_GLOW_PARA, GLOW_LIGHT_VALUE)
        shaderDrawable.setParameter(AGSL_CENTER_X_PARA, GLOW_CENTER_X_RATE)
        shaderDrawable.setParameter(AGSL_CENTER_Y_PARA, GLOW_CENTER_Y_RATE)
        shaderDrawable.setParameter(AGSL_PADDING_ALPHA_PARA, GLOW_BEGIN_ALPHA_DEFAULT)
        shaderDrawable.setParameter(AGSL_LIGHT_COLOR_PARA, lightColor)
        return shaderDrawable
    }


    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private fun initGlowEffectWrapper() {
        DebugUtil.d(TAG, "initGlowEffectWrapper.")
        mGlowTargetView?.let {
            val glowLayerSize = getGlowLayerSize()
            initGlowConfig(
                DrawableConfig(
                    glowLayerSize.first, glowLayerSize.second,
                    glowLayerSize.first / RADIUS_RATE, glowLayerSize.first * GLOW_PADDING_RATE
                )
            )
            DebugUtil.d(TAG, "initGlowEffectWrapper width:${glowLayerSize.first} height:${glowLayerSize.second}")
            it.foreground = getDrawable()
        }

        if (getDrawable() is COEShadowDrawable) {
            DebugUtil.d(TAG, "initGlowEffectWrapper getDrawable success.")
            mIsInitGlowDrawable = true
        }
    }

    /**
     * 设置发光形状的尺寸和圆角。如果不出圆角，cornerRadius为0.
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun initSize(width: Float, height: Float, cornerRadius: Float) {
        if (mShaderDrawable == null) {
            DebugUtil.e(TAG, "initSize error, mShaderDrawable is null")
        }
        setButtonSize(width, height)
        setButtonCorner(cornerRadius)
    }

    /**
     * 设置按钮发光的颜色，默认为白色。
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun setLightColor(red: Float, green: Float, blue: Float, alpha: Float) {
        if (mShaderDrawable == null) {
            DebugUtil.e(TAG, "setLightColor error, mShaderDrawable is null")
        }
        mShaderDrawable?.setParameter(AGSL_LIGHT_COLOR_PARA, floatArrayOf(red, green, blue, alpha))
    }

    /**
     * 设置发光的渐变区域，越小越集中在边缘，越大渐变区域越大。
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun setGlowPadding(padding: Float) {
        if (mShaderDrawable == null) {
            DebugUtil.e(TAG, "setGlowPadding error, mShaderDrawable is null")
        }
        mShaderDrawable?.setParameter(AGSL_PADDING_PARA, padding)
    }

    /**
     * 发光效果的alpha值，越接近1越亮。
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun setPaddingAlpha(alpha: Float) {
        if (mShaderDrawable == null) {
            DebugUtil.e(TAG, "setPaddingAlpha error, mShaderDrawable is null")
        }
        mShaderDrawable?.setParameter(AGSL_PADDING_ALPHA_PARA, alpha)
        paddingAlpha = alpha
    }

    /**
     * 设置光照强度，范围0.01-0.2，这个值由设计师确定。默认值已有设置
     */
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun setGlow(glow: Float) {
        if (mShaderDrawable == null) {
            DebugUtil.e(TAG, "setGlow error, mShaderDrawable is null")
        }
        mShaderDrawable?.setParameter(AGSL_GLOW_PARA, glow)
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun setButtonSize(width: Float, height: Float) {
        if (mShaderDrawable == null) {
            DebugUtil.e(TAG, "setButtonSize error, mShaderDrawable is null")
        }
        mShaderDrawable?.setParameter(AGSL_BUTTON_SIZE_PARA, width, height)
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun setButtonCorner(corner: Float) {
        if (mShaderDrawable == null) {
            DebugUtil.e(TAG, "setButtonCorner error, mShaderDrawable is null")
        }
        mShaderDrawable?.setParameter(AGSL_BUTTON_CORNER_PARA, corner)
    }

    override fun onWindowAttached() {
        mGlowTargetView?.viewTreeObserver?.addOnGlobalLayoutListener(this@GlowDrawableWrapper)
    }

    override fun onWindowDetached() {
        mGlowTargetView?.viewTreeObserver?.removeOnGlobalLayoutListener(this@GlowDrawableWrapper)
    }

    override fun onGlobalLayout() {
        if (!mIsInitGlowDrawable && isSupportGlowEffect() && isFinishMeasure()) {
            initGlowEffectWrapper()
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun createAlphaAnimator(isPressDown: Boolean): ValueAnimator {
        return if (isPressDown) {
            createAlphaAnimator(GLOW_BEGIN_ALPHA, true)
        } else {
            createAlphaAnimator(GLOW_END_ALPHA, false)
        }
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun createAlphaAnimator(glowValueBegin: Float, isPressDown: Boolean): ValueAnimator {
        DebugUtil.d(TAG, "createAlphaAnimator isPressDown:$isPressDown")
        val glowAlphaHolder = if (isPressDown) {
            PropertyValuesHolder.ofFloat(PROPERTY_ALPHA_CIRCLE, glowValueBegin, GLOW_END_ALPHA)
        } else {
            PropertyValuesHolder.ofFloat(PROPERTY_ALPHA_CIRCLE, glowValueBegin, GLOW_BEGIN_ALPHA)
        }
        val alphaAnimator = ValueAnimator.ofPropertyValuesHolder(glowAlphaHolder)
        alphaAnimator.duration = ANIM_DURATION_ENTER
        alphaAnimator.interpolator = INTERPOLATOR_BUTTON_PRESS
        alphaAnimator.addUpdateListener {
            val glowAlpha = it.getAnimatedValue(PROPERTY_ALPHA_CIRCLE) as? Float ?: GLOW_BEGIN_ALPHA
            mGlowTargetView?.let { view ->
                val glowSize = getGlowLayerSize()
                setButtonSize(glowSize.first, glowSize.second)
                setButtonCorner(glowSize.first / RADIUS_RATE)
                setGlowPadding(glowSize.first)
                setPaddingAlpha(glowAlpha)
                view.foreground.invalidateSelf()
            }
        }
        return alphaAnimator
    }

    private fun getGlowLayerSize(): Pair<Float, Float> {
        var glowSize = mReferenceView?.let {
            var sizeScale = SCALE_END
            if (mReferenceView is AnimatedCircleButton) {
                sizeScale = (mReferenceView as AnimatedCircleButton).mScaleCircle
            }
            Pair(it.width.toFloat() * sizeScale, it.height.toFloat() * sizeScale)
        }

        if (glowSize == null) {
            glowSize = mGlowTargetView?.let { Pair(it.width.toFloat(), it.height.toFloat()) } ?: Pair(INVALID_SIZE, INVALID_SIZE)
        }
        return glowSize
    }

    private fun isFinishMeasure(): Boolean {
        val glowDrawableSize = getGlowLayerSize()
        return (glowDrawableSize.first != 0.0f) && (glowDrawableSize.second != 0.0f)
    }


    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.TIRAMISU)
    fun isSupportGlowEffect(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
    }
}