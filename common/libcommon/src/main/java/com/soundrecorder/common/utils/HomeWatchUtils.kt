/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: HomeWatchUtils
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */

package com.soundrecorder.common.utils

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.registerReceiverCompat
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.Constants

@Suppress("DEPRECATION")
class HomeWatchUtils(private val lifecycleOwner: LifecycleOwner, private var listener: ((reason: String) -> Unit)?) : DefaultLifecycleObserver {
    companion object {
        private const val TAG = "HomeListenerUtils"
        private const val SYSTEM_REASON = "reason"
        const val SYSTEM_HOME_KEY = "homekey"
        const val SYSTEM_RECENT_APPS = "recentapps"
    }

    init {
        lifecycleOwner.lifecycle.addObserver(this)
        DebugUtil.i(TAG, "init")
    }

    private val broadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            DebugUtil.i(TAG, "action = ${intent?.action}")
            if (intent?.action == Intent.ACTION_CLOSE_SYSTEM_DIALOGS) {
                val reason = intent.getStringExtra(SYSTEM_REASON)
                DebugUtil.i(TAG, "reason = $reason")
                if (reason == SYSTEM_HOME_KEY || reason == SYSTEM_RECENT_APPS) {
                    listener?.invoke(reason)
                }
            }
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        BaseApplication.getApplication().registerReceiverCompat(
            broadcastReceiver,
            IntentFilter().apply { addAction(Intent.ACTION_CLOSE_SYSTEM_DIALOGS) },
            Constants.PERMISSION_OPPO_COMPONENT_SAFE,
            null,
            Context.RECEIVER_EXPORTED
        )
        DebugUtil.i(TAG, "onCreate")
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        listener = null
        BaseApplication.getApplication().unregisterReceiver(broadcastReceiver)
        lifecycleOwner.lifecycle.removeObserver(this)
        DebugUtil.i(TAG, "onDestroy")
    }
}