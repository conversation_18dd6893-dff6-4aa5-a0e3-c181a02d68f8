<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.soundrecorder.common">

    <uses-sdk tools:overrideLibrary="com.oplus.sdk.addon.sdk" />
    <uses-permission android:name="com.oplus.permission.safe.SETTINGS"/>
    <application
        android:name="com.soundrecorder.common.base.CommonApplication"
        tools:replace="android:name">

        <activity
            android:name=".permission.PermissionProxyActivity"
            android:theme="@style/TransparentActivityTheme" />

        <!--申请权限透明界面-->
        <activity
            android:name="com.soundrecorder.common.permission.NotificationPermissionSnackBarTransparentActivity"
            android:theme="@style/TransparentActivityTheme" />

        <service
            android:name="com.soundrecorder.common.sync.CommonIntentService"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <receiver
            android:name="com.soundrecorder.common.sync.encryptbox.EncryptBoxDataChangeReceiver"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <intent-filter>
                <action android:name="com.coloros.encryption.action.AUDIO_DATA_CHANGED" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.oplus.encryption.action.AUDIO_DATA_CHANGED" />
            </intent-filter>
        </receiver>

        <receiver
            android:name="com.soundrecorder.common.sync.CallRecordingReceiver"
            android:enabled="true"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <intent-filter>
                <!--以前的通话录音实时云同步需求，由于上线后出现无法解决的问题，inCallUI回退了这部分代码，不确定后续是否要上-->
                <action android:name="coloros.intent.action.CALL_RECORD_COMPLETED" />
            </intent-filter>
        </receiver>

        <service
            android:name="com.soundrecorder.common.sync.SyncCallRecordService"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <provider
            android:name="com.soundrecorder.common.db.RecorderProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:multiprocess="false" />
    </application>
</manifest>
