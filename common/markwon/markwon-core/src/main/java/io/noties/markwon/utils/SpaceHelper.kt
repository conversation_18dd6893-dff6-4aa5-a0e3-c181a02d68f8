/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SpaceUtils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/07
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package io.noties.markwon.utils

import android.graphics.Paint
import io.noties.markwon.core.MarkwonTheme
import io.noties.markwon.prop.SpaceProps

class SpaceHelper(theme: MarkwonTheme, spaceLineLevel: Int, private val tag: String) {

    private val topSpacing = SpaceProps.getSpace(theme, spaceLineLevel)
    private var alreadyChangeFontMetrics = false
    private var initAscent: Int? = null
    private var initTop: Int? = null

    fun chooseHeight(
        text: CharSequence?,
        start: Int,
        end: Int,
        spanStartV: Int,
        v: Int,
        fontMetricsInt: Paint.FontMetricsInt?
    ) {
        text ?: return
        fontMetricsInt ?: return
        if (initAscent == null) {
            initAscent = fontMetricsInt.ascent
        }
        if (initTop == null) {
            initTop = fontMetricsInt.top
        }
        //Log.d("SpaceHelper", "$tag: spanStartV = $spanStartV, v = $v, text = ${text.substring(start, end)}, topSpacing = $topSpacing")
        if (spanStartV > 0 && spanStartV == v && topSpacing > 0) {
            fontMetricsInt.ascent -= topSpacing
            fontMetricsInt.top -= topSpacing
            alreadyChangeFontMetrics = true
        } else {
            if (alreadyChangeFontMetrics && initAscent != fontMetricsInt.ascent && initTop != fontMetricsInt.top) {
                fontMetricsInt.ascent += topSpacing
                fontMetricsInt.top += topSpacing
                alreadyChangeFontMetrics = false
            }
        }
    }
}
