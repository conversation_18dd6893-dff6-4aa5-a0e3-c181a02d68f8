/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameAction
 * * Description: SmartNameAction
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.modulerouter.smartname

import android.content.Context

interface SmartNameAction {
    companion object {
        //aiunit相关错误
        const val AIUNIT_ERROR = -2

        //参数错误；语种不支持
        const val INVALID_PARAM = 100001

        //鉴权失败
        const val AUTH_FAILED = 100002

        //触发内容安全
        const val RISK_CONTENT = 100003

        //language is not support
        const val LANGUAGE_NOT_SUPPORT = 100004

        //输入长度超限
        const val CONTENT_LENGTH_EXCEEDED = 100005

        //AI模型服务异常，如google的接口异常，超过并发等;
        const val AI_SERVICE_ERROR = 200001

        //the llm is stop Generate content
        const val AI_SERVICE_STOP = 200002
        const val AI_SERVICE_TOKEN_EXCEED = 200003

        //插件未初始化或者已被销毁需要重新初始化
        const val PLUGIN_INIT_ERROR = 100008

        //插件任务执行超时
        const val REQUEST_TIMEOUT = 100009

        //网络不可达
        const val NETWORK_ERROR = 100010

        //#文件解析失败
        const val FILE_PARSE_FAILED = 100011

        //##溯源文档摘要：文档在解析中或者向量化中
        const val FILE_NOT_PREPARED = 100012

        //录音内容较少，智能命名失败
        const val CONTENT_LESS_ERROR = 100013

        //server exception
        const val SERVER_ERROR = 100014

        //## 获取主题失败
        const val LLM_GET_THEME_FAILED = 100015

        //## 主题类型不支持
        const val LLM_THEME_NOT_SUPPORT = 100016

        //语音摘要内部错误
        const val VOICE_SUMMARY_INNER_ERROR = 300000

        //输入错误 # Unsupported language
        const val UNSUPPORTED_LANGUAGE = 300001

        //llm服务错误
        const val LLM_CLIENT_ERROR = 300002

        //ner服务内部错误
        const val NER_CLIENT_ERROR = 300003

        //无法生成标题
        const val AI_TITLE_SUPPORT_ERROR = 300004

        //内销文本类摘要错误 #摘要服务内部错误
        const val LLM_SUMMARY_INNER_ERROR = 400000
        const val SMART_EXPCETION = 60000
        const val FILE_SIZE_UNSUPPORT_ZERO = 60001
        const val FILE_SIZE_MAX_UNSUPPORT = 60002
        const val DURATION_MIN_UNSUPPORT_ZERO = 60003
        const val DURATION_MAX_UNSUPPORT = 60004
        const val FILE_FORMAT_UNSUPPORT = 60005
        const val FILE_NOT_EXIST = 60006
        const val CONVERT_TEXT_UNSUPPORT = 60007

        /*智能标题 detectorName、sceneName*/
        const val DETECT_UNIFIED_SUMMARY = "unified_summary"
        const val SCENE_UNIFIED_SUMMARY = "unified_summary"
    }

    fun newUnifiedSummaryManager(): IUnifiedSummaryCallBack?
    fun checkSupportSmartName(context: Context?,  supportConvert: Boolean? = null, forceUpdate: Boolean = false): Boolean
    fun startSmartName(mediaId: Long): Boolean
    fun startSmartName(mediaId: Long, jsonParams: String?): Boolean
    fun <T> startSmartNameByBean(mediaId: Long, smartNameParam: T?): Boolean
    fun checkHasTaskRunning(): Boolean
    fun registerCallback(mediaId: Long, callback: ISmartNameCallback)
    fun unRegisterCallback(mediaId: Long, callback: ISmartNameCallback)
    fun cancelSmartNameTask(mediaId: Long)
    fun <T> fromJson(json: String?, cls: Class<T>): T?
    fun setSmartNameSwitchStatus(mContext: Context, isOpen: Boolean, needStatistics: Boolean = true)
    fun isSmartNameSwitchOpen(mContext: Context): Boolean
    fun smartNameSwitchValue(mContext: Context): Int
    fun needShowSmartGuideDialog(mContext: Context): Boolean
    fun clearSmartRetryCacheTask()
    fun checkIsTaskRunning(mediaId: Long): Boolean
    fun checkTaskRunningMaxLimitSize(): Boolean
    fun releaseAllTask()
    fun checkIsWaitingTask(mediaId: Long): Boolean
}