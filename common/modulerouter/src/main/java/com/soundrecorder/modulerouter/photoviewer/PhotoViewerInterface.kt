/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  NotificationInterface.kt
 * * Description : NotificationInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.photoviewer

import android.content.Context
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity

interface PhotoViewerInterface {
    fun <T : PhotoViewerData> startWithBigImage(
        context: Context?,
        data: List<T>,
        startIndex: Int = 0,
        transitionView: ImageView? = null,
        imageViewListener: ((T?) -> Unit)? = null
    )

    fun <T : PhotoViewerData> startWithMultiPictureSelect(
        context: AppCompatActivity,
        data: List<T>,
        transitionView: ImageView?,
        requestCode: Int,
        resultListener: ((T?) -> Unit)?
    )

    fun updateImageView(targetView: ImageView?)
}

const val SHOW_BIG_IMAGE_REQUEST_CODE_X = 1001
