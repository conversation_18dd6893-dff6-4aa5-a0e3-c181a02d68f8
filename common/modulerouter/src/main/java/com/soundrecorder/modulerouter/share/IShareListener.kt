/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.share

/**
 * 分享回调
 */
interface IShareListener {

    /**
     * 分享准备过程耗时，需要展示 waitingDialog
     * @param mediaId 被分享音频媒体库id
     * @param ShareType 区分分享类型
     */
    fun onShowShareWaitingDialog(mediaId: Long, type: ShareType)

    /**
     * 分享成功回调
     * @param mediaId 被分享音频媒体库id
     * @param ShareType 区分分享类型
     */
    fun onShareSuccess(mediaId: Long, type: ShareType)

    /**
     * 分享失败
     * @param mediaId 被分享音频媒体库id
     * @param ShareType 区分分享类型
     * @param error 错误类型，定义在ShareAction里
     * @param message 错误信息
     */
    fun onShareFailed(mediaId: Long, type: ShareType, error: Int, message: String)
}