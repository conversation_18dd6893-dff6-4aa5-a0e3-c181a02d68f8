/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  QuestionnaireInterface.kt
 * * Description : QuestionnaireInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.questionnaire

import android.app.Application
import android.content.Context
import android.view.View
import android.view.ViewGroup
import kotlinx.coroutines.CoroutineScope

interface QuestionnaireInterface {
    fun initSDK(context: Application)
    fun initView(context: Context?, rootView: ViewGroup?): View?
    fun updateSpace(coroutineScope: CoroutineScope, cdpView: View)
    fun cdpViewIsVisible(cdpView: View): Boolean
    fun releaseSpace(cdpView: View?)
    fun setCDPCallBack(cdpView: View, callback: QuestionCDPCallback?, animCallback: QuestionCDPAnimCallback?)
}

/*
用户点击问卷上的忽略时的回调函数
*/
const val ACTION_CARD_IGNORED = 1

/*
获取数据成功时调用回调函数
 */
const val ACTION_CARD_SHOWN = 2

/*
获取数据失败时调用回调函数
 */
const val ACTION_NO_DATA = 4

/*
用户完成问卷后的回调函数
 */
const val ACTION_SURVEY_SUBMITTED = 3

/**
 * 是否真实有问卷数据可显示（拉取数据不为空，但是可能存在用户已提交/忽略所有的问卷，导致没有问卷数据可以展示）
 */
const val ACTION_DATA_VALID = 5