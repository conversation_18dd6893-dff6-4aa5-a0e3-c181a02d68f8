/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderControllerListener
 Description:
 Version: 1.0
 Date: 2022/8/3
 Author: W9013333(v-zhengt<PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/3 1.0 create
 */

package com.soundrecorder.modulerouter.convertService.listener

import android.app.RecoverableSecurityException

interface RecorderControllerListener {

    fun onReadyService() {}

    fun onCloseService() {}

    fun onRecordStatusChange(state: Int)

    fun onRecordCallConnected() {}

    fun onWaveStateChange(state: Int) {}

    fun onMarkDataChange(markAction: Int, errorOrIndex: Int) {}

    fun onSaveFileStateChange(state: Int, fileName: String = "", fullPath: String? = null, e: RecoverableSecurityException? = null) {}
    fun onConfigurationChanged() {}

    fun onRecordNameSet(recordName: String) {}

    /**
     * 定向录音由于声源变化被迫关闭
     */
    fun onDirectRecordingOffByMicChanged() {}
}