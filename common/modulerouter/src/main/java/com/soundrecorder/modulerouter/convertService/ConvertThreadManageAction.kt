/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertThreadManageAction
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.convertService


interface ConvertThreadManageAction {
    object ConvertTaskStatus {
        const val ALREADY_RUNNING = 1
        const val OVER_LIMIT = 2
        const val CAN_ADD_NEW = 3
    }
    fun cancelAllTask()
    fun cancelConvert(mediaId: Long): Boolean
    fun checkIsTaskRunning(mediaId: Long): Boolean
    fun checkCanAddNewTask(mediaId: Long): Int
    fun startOrResumeConvert(mediaId: Long, convertAbilityType: Int, convertAiTitle: Boolean): Boolean
}