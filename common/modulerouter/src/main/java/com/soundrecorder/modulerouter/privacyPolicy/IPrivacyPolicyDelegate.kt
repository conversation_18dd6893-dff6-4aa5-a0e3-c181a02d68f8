/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IPrivacyPolicyDelegate
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.privacyPolicy

import android.content.res.Configuration
import android.os.Bundle

interface IPrivacyPolicyDelegate {
    companion object {
        const val POLICY_TYPE_COMMON = 0
        const val POLICY_TYPE_MINI = 1
        /*用户须知clickSpan*/
        const val SPAN_TYPE_RECORDER_POLICY = 0 // 录音个人信息保护政策
        const val SPAN_TYPE_PERSONAL_POLICY = 1 // 个人信息保护政策
    }

    fun onSaveInstanceState(outState: Bundle) {}

    fun onRestoreInstanceState(savedInstanceState: Bundle) {}

    fun onConfigurationChanged(newConfig: Configuration) {}

    fun checkAndDismissDialog() {}

    fun resumeShowDialog(type: Int, forceShow: Boolean = false, pageFrom: Int? = null) {}

    fun hasConvertPermission(): Boolean = false

    /**
     * 是否支持撤销个保法权限
     */
    fun canShowWithdrawnPermissionConvert(): Boolean = false

    /**
     * 获取当前页面最后一次显示的弹窗类型
     */
    fun getLastShowPrivacyDialogType(): Int? = null

    fun onDestroy() {}
}

interface IPrivacyPolicyResultListener {
    fun onPrivacyPolicySuccess(type: Int, pageFrom: Int? = null) {}
    fun onPrivacyPolicyFail(type: Int, pageFrom: Int? = null) {}
    fun onClickSpan(type: Int) {}
}