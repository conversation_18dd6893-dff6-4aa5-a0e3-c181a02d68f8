/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CloudTipManagerAction
 ** Description : CloudTipManagerAction
 ** Version     : 1.0
 ** Date        : 2025/06/06
 ** Author      : renjiahao
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  renjiahao       2025/06/06       1.0      create
 ***********************************************************************/
package com.soundrecorder.modulerouter.cloudkit

import android.content.Context
import androidx.lifecycle.LiveData
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ICloudSwitchChangeListener
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ITipStatus

interface CloudTipManagerAction {
    fun initCloudState()
    fun registerCloudSwitchChangeListener(listener: ICloudSwitchChangeListener?)
    fun unregisterCloudSwitchChangeListener(listener: ICloudSwitchChangeListener?)
    fun getCloudStatusLiveData(): LiveData<ITipStatus>
    fun getCloudSyncResultCode(): Int
    fun isCloudSwitchOn(): Boolean
    fun launchCloudSettingPage(context: Context?)
    fun getRecordCloudSettingActivityName(): String
    fun getCloudPermissionActivityName(): String
    fun checkSyncAbnormalStop(context: Context): Boolean
    fun isNeedShowCloudGuide(): Boolean
    fun isRefreshUIOfSync(): Boolean
    fun isSyncing(): Boolean
    fun checkNeedSyncFullRecovery(): Boolean
    fun isLoginFromCache(): Boolean
    fun accountRequestLogin(context: Context, callback: ((Boolean) -> Unit)? = null)
    fun releaseAccountReqCallback()
}