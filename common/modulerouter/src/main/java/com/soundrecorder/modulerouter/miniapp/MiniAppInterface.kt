/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  MiniAppInterface.kt
 * * Description : MiniAppInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.miniapp

import android.content.Context
import android.content.Intent
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity

interface MiniAppInterface {
    fun createMiniAppIntent(context: Context): Intent
    fun isMiniRecorderActivity(context: Context): Boolean
    fun checkMiniAppContinueAction(context: AppCompatActivity, intent: Intent, disableDialogFun: (dialog: AlertDialog) -> Unit)
}