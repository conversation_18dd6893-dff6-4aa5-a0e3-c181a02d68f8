/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  WaveMarkInterface.kt
 * * Description : WaveMarkInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.waveMark

import android.net.Uri

interface WaveMarkInterface {

    fun decodeAmplitudeByUri(uri: Uri?): String
    fun <T, R> newPictureMarkDelegate(
        ownerProvider: IPictureMarkLifeOwnerProvider,
        isActivityRecreate: Boolean,
        listener: IIPictureMarkListener<T, R>?
    ): IPictureMarkDelegate<T>

    fun getMergeMarkList(path: String, playUri: Uri, isRecycle: Boolean): List<Any>

    fun getAmplitudeByType(
        path: String?,
        playUri: Uri?,
        isRecycle: Boolean,
        ampTypeOrSegments: String,
        doInIOThread: Boolean = true,
        ampReadyCallback: ((waveReadyDataList: List<Int>?) -> Unit)? = null,
        decodeFinishCallback: (waveDataMap: List<Int>?) -> Unit
    )
    fun asyncUpdateAmpFileAndDB(path: String, uri: Uri, amplitudeList: List<Int>)
    fun initAmplitudeListUtil(
        path: String?,
        playUri: Uri?,
        isRecycle: Boolean
    )

    fun getMarkStringByPath(path: String?): String?
    fun getMergeMarkListByPath(path: String?): List<Any>?
    fun getAmpFormDbOrCustomTagByPath(
        path: String?,
        playUri: Uri?,
        ampTypeOrSegments: String
    ): List<Int>?
    fun releaseMp3ByPath(path: String?)
    fun genAmpFromSoundFile(
        path: String?,
        playUri: Uri?,
        isRecycle: Boolean,
        decodeReadyCallback: ((List<Int>?) -> Unit),
        decodeFinishCallback: ((List<Int>?) -> Unit)
    )
    fun releaseSoundByPath(path: String?)
    fun releaseAmplitudeListUtilByPath(path: String?)
}

data object WaveConstant {
    const val FULL_AMP_TYPE: String = "full_amp"
    const val COMPRESS_90_AMP_TYPE: String = "compress_90_amp"
    const val COMPRESS_48_AMP_TYPE: String = "compress_48_amp"
}
