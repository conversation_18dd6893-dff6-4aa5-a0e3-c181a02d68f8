/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  KoinInterfaceHelper.kt
 * * Description : help use koin in java
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.utils

import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.CommonAction
import com.soundrecorder.modulerouter.FeedBackInterface
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.convertService.ConvertSupportAction
import com.soundrecorder.modulerouter.miniapp.MiniAppInterface
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerInterface
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyInterface
import com.soundrecorder.modulerouter.recorder.RecordInterface
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction

object KoinInterfaceHelper {

    val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    val miniAppApi by lazy {
        Injector.injectFactory<MiniAppInterface>()
    }

    val recorderApi by lazy {
        Injector.injectFactory<RecordInterface>()
    }

    val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    val cloudTipManagerAction by lazy {
        Injector.injectFactory<CloudTipManagerAction>()
    }

    val smartNameAction by lazy {
        Injector.injectFactory<SmartNameAction>()
    }

    val feedbackApi by lazy {
        Injector.injectFactory<FeedBackInterface>()
    }

    val settingApi by lazy {
        Injector.injectFactory<SettingInterface>()
    }
    val commonApi by lazy {
        Injector.injectFactory<CommonAction>()
    }

    val photoViewerApi by lazy {
        Injector.injectFactory<PhotoViewerInterface>()
    }

    val convertSupportAction by lazy {
        Injector.injectFactory<ConvertSupportAction>()
    }

    val seedingAction by lazy {
        Injector.injectFactory<SeedingInterface>()
    }

    val browseFileAction by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    val playbackAction by lazy {
        Injector.injectFactory<PlayBackInterface>()
    }

    val privacyPolicyInterface by lazy {
        Injector.injectFactory<PrivacyPolicyInterface>()
    }

    val aiAsrManagerAction by lazy {
        Injector.injectFactory<AIAsrManagerAction>()
    }
}