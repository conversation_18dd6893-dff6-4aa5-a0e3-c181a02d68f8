/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ISummaryCallback
 * Description:
 * Version: 1.0
 * Date: 2024/3/13
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/13 1.0 create
 */

package com.soundrecorder.modulerouter.summary

import androidx.annotation.Keep

@Keep
interface ISummaryCallback {

    /**
     * 录音预处理逻辑结果
     * @param from 来源，目前用于各页面埋点数据
     * @param code 校验结果
     */
    fun onRecordPreCheckResult(from: String, code: Int) {}

    /**
     * 开始摘要准备结果
     * @param success 是否准备成功（语言转文字的内部预处理逻辑）true：校验通过 false：校验失败，不会往下走
     */
    fun onSummaryPrepare(success: Boolean) {}

    /**
     * SDK内部逻辑验证通过，摘要流程开始
     */
    fun onSummaryStart() {}

    /**
     * 摘要流程开始后，获取到便签ID
     */
    fun onSummaryGenNotedId(noteId: String?, callUuid: String?) {}

    /**
     * 查询今日摘要剩余可用次数
     */
    fun onAvailableTimeResult(count: Long? = -1) {}

    /**
     * 已有音频正在走摘要流程
     * @param runningMediaId 正在运行的文件媒体库ID
     * @param runningMd5 正在运行文件的MD5
     * @param runningCallId 正在运行文件透传的uuid
     */
    fun onSummaryProgressing(runningMediaId: Long?, runningMd5: String?, runningCallId: String?) {}

    /**
     * 摘要流程结束，成功or失败
     * @param asrErrorCode ASR错误码，0为成功
     * @param summaryErrorCode summary 错误码，0为成功
     */
    fun onSummaryProgressEnd(asrErrorCode: Int?, summaryErrorCode: Int?) {}

    /**
     * 完全结束，摘要流程结束后，流体云卡片有显示延时，卡片消失后回调
     */
    fun onSummaryEnd() {}
}