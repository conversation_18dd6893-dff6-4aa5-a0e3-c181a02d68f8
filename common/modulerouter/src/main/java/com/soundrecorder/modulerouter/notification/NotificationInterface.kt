/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  NotificationInterface.kt
 * * Description : NotificationInterface
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.modulerouter.notification

import android.app.Service
import android.content.IntentFilter

interface NotificationInterface {

    fun showNotification(mode: Int, page: Int, notificationModel: NotificationModel?, service: Service? = null)
    fun getGroupIdByModeAndPage(mode: Int?, page: Int?): Int
    fun getNotificationIdByModeAndPage(mode: Int?, page: Int?): Int
    fun getModeAndPageByNotificationId(notificationId: Int): IntArray
    fun getNotificationMode(isFromOtherApp: Boolean): Int
    fun cancelNotificationByPage(page: Int)
    fun cancelNotificationModeAndGroup(mode: Int, page: Int)
    fun cancelAllNotification()
    fun cancelNotification(mode: Int, page: Int)
    fun getIntentFilter(): IntentFilter
    fun isLockScreen(): Boolean
}