/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.modulerouter.convertService

import android.content.Context

interface ConvertCheckUtilsAction {

    fun isFileSizeMinMet(fileSize: Long): Boolean
    fun isFileSizeMaxMet(fileSize: Long): Boolean
    fun isFileDurationMinMet(duration: Long): Boolean
    fun isFileDurationMaxMet(duration: Long): Boolean
    fun isFileFormatMet(fileFormat: String): Boolean
    fun getErrorMsgByStatus(
        context: Context,
        uploadStatus: Int,
        convertStatus: Int,
        errorMessage: String
    ): String
}