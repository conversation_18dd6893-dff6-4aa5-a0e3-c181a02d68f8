/********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveRecyclerView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2018/11/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.soundrecorder.dragonfly.view.wave

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.os.SystemClock
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import androidx.core.view.OneShotPreDrawListener
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.soundrecorder.dragonfly.R
import com.soundrecorder.dragonfly.utils.AppCardUtils.isRTL
import com.soundrecorder.dragonfly.utils.AppCardUtils.log
import com.soundrecorder.dragonfly.view.wave.WaveAdapter.Companion.VIEW_TYPE_WAVE
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.DEFAULT_SCROLL_X
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.DURATION_560
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.FLOAT_0_5
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.INT_100
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.INT_2
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.INT_4
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.INT_8
import com.soundrecorder.dragonfly.view.wave.WaveViewUtil.getOneWaveWidth
import kotlin.math.abs

internal class WaveRecyclerView @JvmOverloads constructor(ctx: Context, attrs: AttributeSet? = null, defStyle: Int = 0) :
    RecyclerView(ctx, attrs, defStyle),
    IWaveItemViewDelegate {
    private val linearLayoutManager = LinearLayoutManager(ctx).apply {
        orientation = LinearLayoutManager.HORIZONTAL
        reverseLayout = isRTL()
    }
    private val waveAdapter = WaveAdapter(this)
    private val linearInterpolator = LinearInterpolator()
    private var preTime: Int = -1
    private var hasRecording = false
    private var ampsSize = 0
    private var lastAmps = emptyList<Int>()
    private var cardWaveColor = Color.parseColor("#D9000000")
    private var cardDashWaveColor = Color.parseColor("#D9666666")
    private var doEnterAnimationTime = -1L

    init {
        overScrollMode = OVER_SCROLL_NEVER
        setWaveAdapter()
        OneShotPreDrawListener.add(this) {
            setSelectTime(ampsSize)
            val max = (width / context.getOneWaveWidth()).toInt() + INT_2
            recycledViewPool.setMaxRecycledViews(VIEW_TYPE_WAVE, if (max > 0) max else INT_100)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun doEnterAnimator() {
        doEnterAnimationTime = SystemClock.elapsedRealtime()
        waveAdapter.notifyDataSetChanged()
    }

    fun refreshColor(cardWaveColor: Int, cardDashWaveColor: Int) {
        this.cardWaveColor = cardWaveColor
        this.cardDashWaveColor = cardDashWaveColor
    }

    override fun createNewItemView(parent: ViewGroup): WaveItemView {
        return LayoutInflater.from(parent.context).inflate(R.layout.app_card_item_ruler, parent, false) as WaveItemView
    }

    override fun onBindItemView(rulerView: WaveItemView, position: Int) {
        rulerView.refreshColor(cardWaveColor, cardDashWaveColor)
        rulerView.refreshData(hasRecording, ampsSize, lastAmps, doEnterAnimationTime)
    }

    override fun halfWidth(): Int {
        return ((width - context.getOneWaveWidth()) * FLOAT_0_5).toInt()
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        return true
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(e: MotionEvent?): Boolean {
        return true
    }

    /**
     * stop wave timer
     */
    @SuppressLint("NotifyDataSetChanged")
    @Suppress("TooGenericExceptionCaught")
    fun stopRecorderMove(ampsSize: Int, lastAmps: List<Int>) {
        try {
            this.hasRecording = false
            this.ampsSize = ampsSize
            this.lastAmps = lastAmps
            waveAdapter.notifyDataSetChanged()
            setSelectTime(this.ampsSize)
        } catch (e: Exception) {
            e.log()
        }
    }

    /**
     * update wave
     */
    @SuppressLint("NotifyDataSetChanged")
    @Suppress("TooGenericExceptionCaught")
    fun recorderIntervalUpdate(ampsSize: Int, lastAmps: List<Int>, hasRefreshTime70: Boolean) {
        try {
            this.hasRecording = true
            this.ampsSize = ampsSize
            this.lastAmps = lastAmps
            waveAdapter.notifyDataSetChanged()
            startSmoothScroll(this.ampsSize, hasRefreshTime70)
        } catch (e: Exception) {
            e.log()
        }
    }

    fun setSelectTime(curTime: Int) {
        "setSelectTime".log()
        preTime = curTime
        stopScroll()
        val halfWidth = halfWidth()
        if (halfWidth > 0) {
            linearLayoutManager.scrollToPositionWithOffset(curTime, halfWidth())
        }
    }

    /**
     * v1.0，140毫秒推送一次波形数据，大概距离delta为context.getOneWaveWidth()的2倍
     * v2.0  70毫秒推送一次波形数据，大概距离delta为context.getOneWaveWidth()的1倍
     * hasRefreshTime70 录音app传送过来的，判断是否支持70毫秒刷新
     */
    private fun startSmoothScroll(curTime: Int, hasRefreshTime70: Boolean) {
        if (preTime > 0) {
            val scrolledLength = getTotalScrolledLength()
            if (scrolledLength < 0) {
                setSelectTime(curTime)
            } else {
                var delta = getDeltaScrollLength(curTime, scrolledLength)
                val rate = if (hasRefreshTime70) INT_8 else INT_4
                if (delta < context.getOneWaveWidth() * rate) {
                    delta *= rate
                    val newX = if (linearLayoutManager.reverseLayout) -delta else delta
                    smoothScrollBy(newX, 0, linearInterpolator, DURATION_560)
                } else {
                    setSelectTime(curTime)
                }
            }
        }
        preTime = curTime
    }

    /**
     * delta理论上始终大于0
     */
    private fun getDeltaScrollLength(curTime: Int, scrolledLength: Int): Int {
        val oneWaveWidth = context.getOneWaveWidth()
        val expectedLength = (curTime * oneWaveWidth).toInt()
        return expectedLength - scrolledLength
    }

    /**
     * 计算WaveRecyclerView當前已滚动的距离
     */
    @Suppress("TooGenericExceptionCaught")
    private fun getTotalScrolledLength(): Int {
        try {
            val firstVisibleItemPosition = linearLayoutManager.findFirstVisibleItemPosition()
            val firstVisibleItem = linearLayoutManager.findViewByPosition(firstVisibleItemPosition) ?: return DEFAULT_SCROLL_X
            val left = if (isRTL()) abs(firstVisibleItem.right - width) else firstVisibleItem.left
            return if (firstVisibleItemPosition == 0) {
                abs(left)
            } else {
                (abs(left) + halfWidth() + (firstVisibleItemPosition - 1) * context.getOneWaveWidth()).toInt()
            }
        } catch (e: Exception) {
            e.log()
            return DEFAULT_SCROLL_X
        }
    }

    private fun setWaveAdapter() {
        layoutManager = linearLayoutManager
        adapter = waveAdapter
    }
}