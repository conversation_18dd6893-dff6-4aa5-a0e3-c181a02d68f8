/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardUtils
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.soundrecorder.dragonfly.utils

import android.content.Context
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.annotation.Keep
import com.google.gson.Gson
import com.oplus.smartenginecustomlib.BuildConfig
import java.lang.ref.WeakReference
import java.util.*
import java.util.concurrent.Executors

@Keep
internal object AppCardUtils {
    private const val PACKAGE_NAME = "com.coloros.soundrecorder"
    const val DURATION_250 = 250L
    const val DURATION_5000 = 5_000L
    const val FLOAT_1_5 = 1.5F
    private val newSingleThreadExecutor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())
    private const val TAG = "RecorderAppCard"
    private const val CLICK_THRESHOLD = 500
    private var isDebug = BuildConfig.DEBUG
    private val gson = Gson()
    private var lastTime: Long = 0
    private var wkAppCtx: WeakReference<Context>? = null
    private var appPackageName: String? = null

    @JvmStatic
    @Suppress("unused")
    fun openDebug() {
        isDebug = true
    }

    @JvmStatic
    fun setAppPackageName(packageName: String?) {
        appPackageName = packageName
    }

    @JvmStatic
    fun appPackageName(): String {
        val packageName = if (appPackageName.isNullOrEmpty()) {
            PACKAGE_NAME
        } else {
            appPackageName.toString()
        }
        packageName.log()
        return packageName
    }

    @JvmStatic
    fun Context.appCtx(): Context {
        var ctx = wkAppCtx?.get()
        return if (ctx == null) {
            ctx = createPackageContext(appPackageName(), Context.CONTEXT_IGNORE_SECURITY)
            wkAppCtx = WeakReference(ctx)
            ctx
        } else {
            ctx
        }
    }

    @JvmStatic
    fun <T> fromJson(json: String, cls: Class<T>): T? {
        return gson.fromJson(json, cls)
    }

    @JvmStatic
    fun isFastDoubleClick(): Boolean {
        val time = SystemClock.elapsedRealtime()
        return if (time - lastTime <= CLICK_THRESHOLD) {
            true
        } else {
            lastTime = time
            false
        }
    }

    @JvmStatic
    fun Any.log() {
        if (isDebug) {
            if (this is Exception) {
                Log.i(TAG, "", this)
            } else {
                Log.i(TAG, "$this")
            }
        }
    }

    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    fun runBackground(runnable: Runnable) {
        try {
            newSingleThreadExecutor.execute(runnable)
        } catch (e: Exception) {
            e.log()
        }
    }


    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun Context.doAction(action: String, widgetCode: String): Bundle? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val bundle = Bundle()
                bundle.putString("widgetCode", widgetCode)
                contentResolver.acquireUnstableContentProviderClient("com.soundrecorder.dragonfly.AppCardWidgetProvider")
                    ?.use {
                        it.call(action, null, bundle)
                    }
            } else {
                //小于29版本不支持，目前录音最小版本30
                null
            }
        } catch (e: Exception) {
            e.log()
            null
        }
    }

    @JvmStatic
    fun isRTL(): Boolean {
        return TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL
    }
}