/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardRunnable
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: W9013333(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/29 1.0 create
 */


package com.soundrecorder.dragonfly.runnable

import android.view.View
import com.soundrecorder.dragonfly.utils.AppCardUtils.log
import java.lang.ref.WeakReference

internal class AppCardRunnable(itemView: View, private val function: View.() -> Unit) : Runnable {
    private val viewWK = WeakReference(itemView)

    @Suppress("TooGenericExceptionCaught")
    override fun run() {
        try {
            viewWK.get()?.function()
        } catch (e: Exception) {
            e.log()
        }
    }
}