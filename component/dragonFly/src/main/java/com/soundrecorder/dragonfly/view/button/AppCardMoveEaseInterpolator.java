package com.soundrecorder.dragonfly.view.button;

import android.view.animation.PathInterpolator;

public class AppCardMoveEaseInterpolator extends PathInterpolator {

    private static final float controlX1 = 0.30f;
    private static final float controlY1 = 0f;
    private static final float controlX2 = 0.10f;
    private static final float controlY2 = 1f;

    public AppCardMoveEaseInterpolator() {
        super(controlX1, controlY1, controlX2, controlY2);
    }
}
