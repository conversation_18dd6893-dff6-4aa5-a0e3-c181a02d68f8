/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: CardUtils
 Description:
 Version: 1.0
 Date: 2023/2/13
 Author: W9013333(v-zhengt<PERSON><PERSON><EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2023/2/13 1.0 create
 */

package com.soundrecorder.dragonfly.utils

import android.content.Context
import android.content.pm.PackageManager

internal object CardVersionUtils {
    private val RECORDER_PACKAGE = arrayOf("com.coloros.soundrecorder", "com.oneplus.soundrecorder")
    private const val META_DATA_DRAGON_FLY_CARD_VERSION_CODE = "com.soundRecorder.dragonFlyCard.versionCode"
    private const val VERSION_CODE_2 = 2

    /**
     * 需要manifest正确配置package包名
     */
    @Suppress("DEPRECATION")
    @JvmStatic
    fun Context.metaDataInt(packageName: String, metaDataName: String): Int {
        if (packageName.isEmpty()) return -1
        try {
            val applicationInfo = applicationContext.packageManager.getApplicationInfo(packageName, PackageManager.GET_META_DATA)
            return applicationInfo.metaData?.getInt(metaDataName, -1) ?: -1
        } catch (_: Exception) {
        }
        return -1
    }

    @JvmStatic
    fun Context.isVersionCode2OrLater(): Boolean {
        RECORDER_PACKAGE.forEach {
            if (applicationContext.metaDataInt(it, META_DATA_DRAGON_FLY_CARD_VERSION_CODE) >= VERSION_CODE_2) {
                return true
            }
        }
        return false
    }
}