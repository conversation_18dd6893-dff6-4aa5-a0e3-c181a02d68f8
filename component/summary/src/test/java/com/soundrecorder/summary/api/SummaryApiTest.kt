/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryApiTest
 * Description:
 * Version: 1.0
 * Date: 2024/3/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/18 1.0 create
 */

package com.soundrecorder.summary.api

import android.app.Activity
import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.common.databean.Record
import com.soundrecorder.modulerouter.summary.ISummaryCallback
import com.soundrecorder.summary.RecordSummaryManager
import com.soundrecorder.summary.shadow.ShadowFeatureOption
import com.soundrecorder.summary.util.SummaryConditionChecker
import org.junit.After
import org.junit.Before
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyString
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class SummaryApiTest {
    var context: Context? = null
    var staticSummaryConditionChecker: MockedStatic<SummaryConditionChecker>? = null
    var staticRecordSummaryManager: MockedStatic<RecordSummaryManager>? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        staticSummaryConditionChecker = Mockito.mockStatic(SummaryConditionChecker::class.java)
        staticRecordSummaryManager = Mockito.mockStatic(RecordSummaryManager::class.java)
    }

    @After
    fun release() {
        staticSummaryConditionChecker?.close()
        staticSummaryConditionChecker = null
        staticRecordSummaryManager?.close()
        staticRecordSummaryManager = null
        context = null
    }

    @Test
    fun should_correct_when_startSummary() {
        SummaryApi.startSummary("", Record(), arrayListOf(), arrayListOf())
    }

    @Test
    fun should_correct_when_registerSummaryCallback() {
        val callback = object : ISummaryCallback {}
        SummaryApi.registerSummaryCallback(1L, callback)
    }

    @Test
    fun should_correct_when_unregisterSummaryCallback() {
        SummaryApi.unregisterSummaryCallback(object : ISummaryCallback {})
    }

    @Ignore
    @Test
    fun should_correct_when_toNotesSummaryActivity() {
        val activity = Robolectric.buildActivity(Activity::class.java).get()
        val mockAppUtil = Mockito.mockStatic(AppUtil::class.java)
        mockAppUtil.`when`<Boolean> { AppUtil.isAppInstalled(anyString()) }.thenReturn(false)
        SummaryApi.toNotesSummaryActivity(activity, "", "", 1) { _, _ -> }

        mockAppUtil.`when`<Boolean> { AppUtil.isAppInstalled(anyString()) }.thenReturn(true)
        mockAppUtil.`when`<Boolean> { AppUtil.isBreathModeNotContainApp(any(), anyString()) }.thenReturn(true)
        SummaryApi.toNotesSummaryActivity(activity, "", "", 1) { _, _ -> }

        mockAppUtil.`when`<Boolean> { AppUtil.isBreathModeNotContainApp(any(), anyString()) }.thenReturn(false)
        staticSummaryConditionChecker?.`when`<Boolean> { SummaryConditionChecker.isNotesSupportSummary(any()) }?.thenReturn(false)
        SummaryApi.toNotesSummaryActivity(activity, "", "", 1) { _, _ -> }

        staticSummaryConditionChecker?.`when`<Boolean> { SummaryConditionChecker.isNotesSupportSummary(any()) }?.thenReturn(true)
        SummaryApi.toNotesSummaryActivity(activity, "", "", 1) { _, _ -> }

        mockAppUtil.close()
    }
}