<?xml version="1.0" encoding="utf-8"?>
<com.soundrecorder.summary.ui.SummaryCustomConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.SummaryFragment">

    <View
        android:id="@+id/top_divider_line"
        android:layout_width="match_parent"
        android:layout_height="@dimen/divider_background_height"
        android:layout_gravity="center_horizontal"
        android:background="?attr/couiColorDivider"
        android:forceDarkAllowed="false"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:importantForAccessibility="no" />

    <com.soundrecorder.summary.ui.content.SummaryContentView
        android:id="@+id/summary_content_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/stop_layout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/top_divider_line" />

    <LinearLayout
        android:id="@+id/stop_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp96"
        android:visibility="gone"
        android:gravity="center_horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/summary_content_view">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/summary_stop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp16"
            android:layout_marginBottom="@dimen/dp24"
            android:background="@drawable/bg_image_icon"
            android:src="@drawable/ic_stop_icon" />
    </LinearLayout>

</com.soundrecorder.summary.ui.SummaryCustomConstraintLayout>