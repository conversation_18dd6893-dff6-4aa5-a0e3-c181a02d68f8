<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/scene_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:padding="@dimen/dp16" />

    <com.coui.appcompat.button.COUIButton
        android:id="@+id/btn_generate"
        style="?attr/couiButtonColorfulDefaultStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="@dimen/dp40"
        android:layout_gravity="center"
        android:gravity="center"
        android:layout_marginStart="@dimen/dp32"
        android:layout_marginEnd="@dimen/dp32"
        android:layout_marginBottom="@dimen/dp16"
        android:text="@string/text_generate"
        android:enabled="false"
        app:drawableColor="@color/coui_color_disable_red_light" />

</LinearLayout>