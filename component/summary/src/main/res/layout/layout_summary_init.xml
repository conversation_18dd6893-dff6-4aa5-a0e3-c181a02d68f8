<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:forceDarkAllowed="false">

    <com.soundrecorder.common.widget.OSImageView
        android:id="@+id/init_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:forceDarkAllowed="false"
        app:anim_raw_json="@raw/summary_error_logo_json"
        app:anim_raw_json_night="@raw/summary_error_logo_json_night"
        app:img_draw="@drawable/summary_error_logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/init_msg_text"
        style="@style/couiTextHeadlineXS"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/summary"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textFontWeight="500"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/init_logo" />
    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/init_des_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textSize="@dimen/sp12"
        android:layout_marginTop="@dimen/dp2"
        android:text="@string/summary_extract_key_info"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textFontWeight="500"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/init_msg_text" />

    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/tv_start_summary"
        style="@style/couiTextButtonM"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:gravity="center"
        android:drawableStart="@drawable/ic_generate"
        android:paddingHorizontal="@dimen/dp12"
        android:paddingVertical="@dimen/dp4"
        android:text="@string/text_generate"
        android:background="@drawable/audio_turn_on_transcription"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textFontWeight="500"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/init_des_text" />

</androidx.constraintlayout.widget.ConstraintLayout>