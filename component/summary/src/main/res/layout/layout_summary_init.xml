<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:forceDarkAllowed="false">

    <com.soundrecorder.common.widget.OSImageView
        android:id="@+id/init_logo"
        android:layout_width="@dimen/os_image_def_width"
        android:layout_height="@dimen/os_image_def_height"
        android:layout_marginTop="@dimen/play_convert_margin_top"
        android:forceDarkAllowed="false"
        app:anim_raw_json="@raw/summary_error_logo_json"
        app:anim_raw_json_night="@raw/summary_error_logo_json_night"
        app:img_draw="@drawable/summary_error_logo"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/init_msg_text"
        style="@style/couiTextHeadlineXS"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/summary"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textFontWeight="500"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/init_logo" />

    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/init_des_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp8"
        android:fontFamily="sans-serif-regular"
        android:gravity="center"
        android:text="@string/summary_extract_key_info"
        android:textColor="?attr/couiColorLabelSecondary"
        android:textFontWeight="500"
        android:textSize="@dimen/sp12"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/init_msg_text" />

    <com.coui.appcompat.textview.COUITextView
        android:id="@+id/tv_start_summary"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp24"
        android:drawableStart="@drawable/ic_generate"
        android:drawablePadding="@dimen/dp4"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:includeFontPadding="false"
        android:paddingVertical="@dimen/dp9.5"
        android:paddingStart="@dimen/dp9.5"
        android:paddingEnd="@dimen/dp16"
        android:text="@string/text_generate"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textFontWeight="500"
        android:textSize="@dimen/sp12"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/init_des_text" />

</androidx.constraintlayout.widget.ConstraintLayout>