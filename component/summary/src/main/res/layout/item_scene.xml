<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_scene_item"
    android:clickable="true"
    android:focusable="true"
    android:forceDarkAllowed="false"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <ImageView
        android:id="@+id/scene_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:clickable="false"
        android:focusable="false"
        android:focusableInTouchMode="false"
        android:forceDarkAllowed="false" />

    <com.soundrecorder.summary.ui.SummaryHorizontalScrollView
        android:id="@+id/title_view_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="start">

        <TextView
            android:id="@+id/scene_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp2"
            android:clickable="false"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:forceDarkAllowed="false"
            android:gravity="start"
            android:lineSpacingExtra="@dimen/sp3"
            android:maxLines="1"
            android:textColor="?attr/couiColorLabelPrimary"
            android:textDirection="locale"
            android:textSize="@dimen/sp16"
            android:textStyle="bold" />
    </com.soundrecorder.summary.ui.SummaryHorizontalScrollView>

    <com.soundrecorder.summary.ui.SummaryHorizontalScrollView
        android:id="@+id/chip_group_wrapper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:layout_marginTop="@dimen/dp8">

        <com.soundrecorder.summary.ui.TagContainer
            android:id="@+id/scene_chip_group"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp20"
            android:orientation="horizontal"
            app:space="@dimen/dp6" />
    </com.soundrecorder.summary.ui.SummaryHorizontalScrollView>
</LinearLayout>