<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:forceDarkAllowed="false"
    android:background="@drawable/bg_scene_item"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <ImageView
        android:id="@+id/scene_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="icon"
        android:forceDarkAllowed="false" />

    <TextView
        android:id="@+id/scene_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp2"
        android:forceDarkAllowed="false"
        android:lineSpacingExtra="@dimen/sp3"
        android:textColor="?attr/couiColorLabelPrimary"
        android:textSize="@dimen/sp18"
        android:textStyle="bold" />

    <com.coui.appcompat.scrollview.COUIHorizontalScrollView
        android:id="@+id/chip_group_wrapper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="@dimen/dp8">

        <com.soundrecorder.summary.ui.TagContainer
            android:id="@+id/scene_chip_group"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp20"
            android:orientation="horizontal"
            app:space="@dimen/dp6" />
    </com.coui.appcompat.scrollview.COUIHorizontalScrollView>
</LinearLayout>