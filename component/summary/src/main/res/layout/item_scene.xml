<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_scene_item"
    android:orientation="vertical"
    android:padding="@dimen/dp8">

    <ImageView
        android:id="@+id/scene_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp12" />

    <TextView
        android:id="@+id/scene_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp6"
        android:textColor="@color/text_color"
        android:textSize="@dimen/sp18"
        android:textStyle="bold" />

    <com.coui.appcompat.scrollview.COUIHorizontalScrollView
        android:id="@+id/chip_group_wrapper"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal">

        <com.coui.appcompat.chip.COUIChipGroup
            android:id="@+id/scene_chip_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp6"
            app:chipSpacing="@dimen/dp2"
            app:singleLine="true" />
    </com.coui.appcompat.scrollview.COUIHorizontalScrollView>
</LinearLayout>