<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="24dp"
    android:height="24dp"
    android:viewportWidth="24"
    android:viewportHeight="24">
  <group>
    <clip-path
        android:pathData="M10.869,3.226C11.241,2.017 12.951,2.017 13.325,3.226L13.665,4.329C14.579,7.294 16.9,9.615 19.865,10.529L20.967,10.869C22.176,11.242 22.176,12.952 20.967,13.325L19.865,13.665C16.9,14.579 14.579,16.9 13.665,19.865L13.325,20.968C12.952,22.177 11.241,22.177 10.869,20.968L10.529,19.865C9.615,16.9 7.293,14.579 4.329,13.665L3.225,13.325C2.017,12.952 2.017,11.242 3.225,10.869L4.329,10.529C7.293,9.615 9.615,7.294 10.529,4.329L10.869,3.226Z"/>
    <path
        android:pathData="M3.121,23.707a13.48,10.34 54.117,1 0,16.755 -12.121a13.48,10.34 54.117,1 0,-16.755 12.121z"
        android:fillColor="#C25DFF"/>
    <path
        android:pathData="M9.205,21.102a12.857,13.728 0,1 0,25.715 0a12.857,13.728 0,1 0,-25.715 0z"
        android:fillColor="#2963FF"/>
    <path
        android:pathData="M6.643,-1.539a7.735,9.938 0,1 0,15.47 0a7.735,9.938 0,1 0,-15.47 0z"
        android:fillColor="#FFCE2D"/>
    <path
        android:pathData="M0.6,-0.361a14.215,11.526 0,1 0,28.43 0a14.215,11.526 0,1 0,-28.43 0z"
        android:fillColor="#FF6536"/>
    <path
        android:pathData="M16.483,9.166a8.313,9.136 124.484,1 0,15.062 10.345a8.313,9.136 124.484,1 0,-15.062 -10.345z"
        android:fillColor="#10BFF7"/>
    <path
        android:pathData="M-6.316,3.481a8.068,9.989 0,1 0,16.136 0a8.068,9.989 0,1 0,-16.136 0z"
        android:fillColor="#FF336E"/>
    <path
        android:pathData="M15.045,-0.258a10.142,11.218 0,1 0,20.285 0a10.142,11.218 0,1 0,-20.285 0z"
        android:fillColor="#FFCD2A"/>
    <path
        android:pathData="M16.351,-1.514m-6.531,0a6.531,6.531 0,1 1,13.062 0a6.531,6.531 0,1 1,-13.062 0">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="26.73"
            android:startY="-1.452"
            android:endX="12.852"
            android:endY="-1.452"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFCD2A"/>
          <item android:offset="1" android:color="#FFFF6536"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M17.093,10.652a6.967,7.171 0,1 0,13.933 0a6.967,7.171 0,1 0,-13.933 0z"
        android:strokeAlpha="0.6"
        android:fillAlpha="0.6">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="24.06"
            android:startY="3.48"
            android:endX="24.06"
            android:endY="17.823"
            android:type="linear">
          <item android:offset="0" android:color="#FF35DA51"/>
          <item android:offset="1" android:color="#FF19B4FD"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M22.114,7.63a5.788,7.837 0,1 0,11.577 0a5.788,7.837 0,1 0,-11.577 0z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="27.902"
            android:startY="-0.207"
            android:endX="27.902"
            android:endY="15.468"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFCD2A"/>
          <item android:offset="1" android:color="#FF31E0B6"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M16.735,9.115a7.633,8.094 0,1 0,15.265 0a7.633,8.094 0,1 0,-15.265 0z"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="24.317"
            android:startY="1.021"
            android:endX="24.353"
            android:endY="16.204"
            android:type="linear">
          <item android:offset="0" android:color="#FF35DA51"/>
          <item android:offset="1" android:color="#FF25DE44"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M19.809,10.909m-6.096,0a6.096,6.096 0,1 1,12.192 0a6.096,6.096 0,1 1,-12.192 0"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="27.74"
            android:startY="9.926"
            android:endX="15.549"
            android:endY="9.533"
            android:type="linear">
          <item android:offset="0" android:color="#FF90CED7"/>
          <item android:offset="0.921" android:color="#FFFF8C2C"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M5.518,17.721a9.067,8.862 0,1 0,18.134 0a9.067,8.862 0,1 0,-18.134 0z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="23.652"
            android:startY="17.773"
            android:endX="5.518"
            android:endY="17.721"
            android:type="linear">
          <item android:offset="0" android:color="#FF11B1FD"/>
          <item android:offset="1" android:color="#FFE457EE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.835,11.728m-5.942,0a5.942,5.942 0,1 1,11.884 0a5.942,5.942 0,1 1,-11.884 0"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="24.777"
            android:startY="11.792"
            android:endX="12.509"
            android:endY="11.792"
            android:type="linear">
          <item android:offset="0" android:color="#FF10BFF7"/>
          <item android:offset="1" android:color="#FFC25DFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M13.354,1.022L13.354,1.022A5.993,5.993 0,0 1,19.347 7.016L19.347,7.016A5.993,5.993 0,0 1,13.354 13.009L13.354,13.009A5.993,5.993 0,0 1,7.361 7.016L7.361,7.016A5.993,5.993 0,0 1,13.354 1.022z"
        android:strokeAlpha="0.1"
        android:fillColor="#ffffff"
        android:fillAlpha="0.1"/>
    <path
        android:pathData="M3.672,-2.666h24.588v24.588h-24.588z"
        android:fillColor="#ffffff"
        android:fillAlpha="0.01"/>
  </group>
</vector>
