<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="20dp"
    android:height="20dp"
    android:viewportWidth="20"
    android:viewportHeight="20">
  <group>
    <clip-path
        android:pathData="M13.076,1.667C13.536,1.667 13.909,2.04 13.909,2.5C13.909,2.96 13.536,3.333 13.076,3.333H6.313C5.851,3.333 5.554,3.334 5.329,3.353C5.114,3.37 5.037,3.4 5.003,3.417C4.858,3.491 4.741,3.608 4.667,3.753C4.65,3.787 4.62,3.864 4.603,4.079C4.584,4.304 4.583,4.601 4.583,5.063V14.936C4.583,15.399 4.584,15.696 4.603,15.921C4.62,16.136 4.65,16.213 4.667,16.247C4.741,16.392 4.858,16.509 5.003,16.583C5.037,16.601 5.114,16.63 5.329,16.647C5.554,16.666 5.851,16.667 6.313,16.667H13.773C14.681,16.667 15.417,15.931 15.417,15.023V10.563C15.417,10.102 15.79,9.729 16.25,9.729C16.71,9.729 17.083,10.102 17.083,10.563V15.023C17.083,16.851 15.601,18.333 13.773,18.333H6.313C5.879,18.333 5.502,18.334 5.193,18.309C4.875,18.283 4.554,18.226 4.246,18.068C3.788,17.835 3.415,17.462 3.182,17.004C3.025,16.695 2.967,16.375 2.941,16.057C2.916,15.748 2.917,15.371 2.917,14.936V5.063C2.917,4.629 2.916,4.252 2.941,3.943C2.967,3.625 3.025,3.304 3.182,2.996C3.415,2.538 3.788,2.165 4.246,1.932C4.554,1.775 4.875,1.717 5.193,1.691C5.502,1.666 5.879,1.667 6.313,1.667H13.076ZM13.468,13.93C13.771,13.992 14,14.261 14,14.583C14,14.905 13.771,15.174 13.468,15.236L13.334,15.25H6.667C6.299,15.25 6,14.951 6,14.583C6,14.215 6.299,13.916 6.667,13.916H13.334L13.468,13.93ZM10.135,11.013C10.438,11.075 10.667,11.344 10.667,11.666C10.667,11.988 10.438,12.257 10.135,12.319L10,12.333H6.667C6.299,12.333 6,12.034 6,11.666C6,11.298 6.299,10.999 6.667,10.999H10L10.135,11.013ZM13.468,11.013C13.771,11.075 14,11.344 14,11.666C14,11.988 13.771,12.257 13.468,12.319L13.333,12.333H12.5C12.132,12.333 11.833,12.034 11.833,11.666C11.833,11.298 12.132,10.999 12.5,10.999H13.333L13.468,11.013ZM13.468,7.68C13.771,7.742 14,8.011 14,8.333C14,8.655 13.771,8.924 13.468,8.986L13.334,9H6.667C6.299,9 6,8.701 6,8.333C6,7.965 6.299,7.666 6.667,7.666H13.334L13.468,7.68ZM15.831,1.977C15.958,1.564 16.542,1.564 16.669,1.977L16.784,2.353C17.096,3.363 17.888,4.154 18.898,4.466L19.274,4.582C19.686,4.709 19.686,5.292 19.274,5.419L18.898,5.535C17.888,5.847 17.096,6.638 16.784,7.648L16.669,8.024C16.542,8.437 15.958,8.437 15.831,8.024L15.716,7.648C15.404,6.638 14.612,5.847 13.602,5.535L13.226,5.419C12.814,5.292 12.814,4.709 13.226,4.582L13.602,4.466C14.612,4.154 15.404,3.363 15.716,2.353L15.831,1.977ZM10.968,4.763C11.271,4.825 11.5,5.094 11.5,5.416C11.5,5.738 11.271,6.007 10.968,6.069L10.834,6.083H6.667C6.299,6.083 6,5.784 6,5.416C6,5.048 6.299,4.749 6.667,4.749H10.834L10.968,4.763ZM13.076,1.667C13.536,1.667 13.909,2.04 13.909,2.5C13.909,2.96 13.536,3.333 13.076,3.333H6.313C5.851,3.333 5.554,3.334 5.329,3.353C5.114,3.37 5.037,3.4 5.003,3.417C4.858,3.491 4.741,3.608 4.667,3.753C4.65,3.787 4.62,3.864 4.603,4.079C4.584,4.304 4.583,4.601 4.583,5.063V14.936C4.583,15.399 4.584,15.696 4.603,15.921C4.62,16.136 4.65,16.213 4.667,16.247C4.741,16.392 4.858,16.509 5.003,16.583C5.037,16.601 5.114,16.63 5.329,16.647C5.554,16.666 5.851,16.667 6.313,16.667H13.773C14.681,16.667 15.417,15.931 15.417,15.023V10.563C15.417,10.102 15.79,9.729 16.25,9.729C16.71,9.729 17.083,10.102 17.083,10.563V15.023C17.083,16.851 15.601,18.333 13.773,18.333H6.313C5.879,18.333 5.502,18.334 5.193,18.309C4.875,18.283 4.554,18.226 4.246,18.068C3.788,17.835 3.415,17.462 3.182,17.004C3.025,16.695 2.967,16.375 2.941,16.057C2.916,15.748 2.917,15.371 2.917,14.936V5.063C2.917,4.629 2.916,4.252 2.941,3.943C2.967,3.625 3.025,3.304 3.182,2.996C3.415,2.538 3.788,2.165 4.246,1.932C4.554,1.775 4.875,1.717 5.193,1.691C5.502,1.666 5.879,1.667 6.313,1.667H13.076ZM13.468,13.93C13.771,13.992 14,14.261 14,14.583C14,14.905 13.771,15.174 13.468,15.236L13.334,15.25H6.667C6.299,15.25 6,14.951 6,14.583C6,14.215 6.299,13.916 6.667,13.916H13.334L13.468,13.93ZM10.135,11.013C10.438,11.075 10.667,11.344 10.667,11.666C10.667,11.988 10.438,12.257 10.135,12.319L10,12.333H6.667C6.299,12.333 6,12.034 6,11.666C6,11.298 6.299,10.999 6.667,10.999H10L10.135,11.013ZM13.468,11.013C13.771,11.075 14,11.344 14,11.666C14,11.988 13.771,12.257 13.468,12.319L13.333,12.333H12.5C12.132,12.333 11.833,12.034 11.833,11.666C11.833,11.298 12.132,10.999 12.5,10.999H13.333L13.468,11.013ZM13.468,7.68C13.771,7.742 14,8.011 14,8.333C14,8.655 13.771,8.924 13.468,8.986L13.334,9H6.667C6.299,9 6,8.701 6,8.333C6,7.965 6.299,7.666 6.667,7.666H13.334L13.468,7.68ZM15.831,1.977C15.958,1.564 16.542,1.564 16.669,1.977L16.784,2.353C17.096,3.363 17.888,4.154 18.898,4.466L19.274,4.582C19.686,4.709 19.686,5.292 19.274,5.419L18.898,5.535C17.888,5.847 17.096,6.638 16.784,7.648L16.669,8.024C16.542,8.437 15.958,8.437 15.831,8.024L15.716,7.648C15.404,6.638 14.612,5.847 13.602,5.535L13.226,5.419C12.814,5.292 12.814,4.709 13.226,4.582L13.602,4.466C14.612,4.154 15.404,3.363 15.716,2.353L15.831,1.977ZM10.968,4.763C11.271,4.825 11.5,5.094 11.5,5.416C11.5,5.738 11.271,6.007 10.968,6.069L10.834,6.083H6.667C6.299,6.083 6,5.784 6,5.416C6,5.048 6.299,4.749 6.667,4.749H10.834L10.968,4.763Z"/>
    <path
        android:pathData="M0.315,16.887a7.067,5.421 54.117,1 0,8.784 -6.355a7.067,5.421 54.117,1 0,-8.784 6.355z"
        android:fillColor="#C25DFF"/>
    <path
        android:pathData="M7.684,17.613a6.5,7.5 0,1 0,13 0a6.5,7.5 0,1 0,-13 0z"
        android:fillColor="#2963FF"/>
    <path
        android:pathData="M5.684,7.613a4.5,3.5 0,1 0,9 0a4.5,3.5 0,1 0,-9 0z"
        android:fillColor="#FFCE2D"/>
    <path
        android:pathData="M-3.316,5.113a7.5,6 0,1 0,15 0a7.5,6 0,1 0,-15 0z"
        android:fillColor="#FF6536"/>
    <path
        android:pathData="M11.32,11.263a4.358,4.79 124.484,1 0,7.896 5.424a4.358,4.79 124.484,1 0,-7.896 -5.424z"
        android:fillColor="#3AD4E9"/>
    <path
        android:pathData="M4.684,4.113a4,5 0,1 0,8 0a4,5 0,1 0,-8 0z"
        android:fillColor="#FF336E"/>
    <path
        android:pathData="M11.684,5.113a5.5,6 0,1 0,11 0a5.5,6 0,1 0,-11 0z"
        android:fillColor="#FFCD2A"/>
    <path
        android:pathData="M7.684,5.113a3.5,3 0,1 0,7 0a3.5,3 0,1 0,-7 0z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16.746"
            android:startY="5.142"
            android:endX="9.309"
            android:endY="5.142"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFCD2A"/>
          <item android:offset="1" android:color="#FFFF6536"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M11.684,12.113a3.5,4 0,1 0,7 0a3.5,4 0,1 0,-7 0z"
        android:strokeAlpha="0.6"
        android:fillAlpha="0.6">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="15.184"
            android:startY="8.113"
            android:endX="15.184"
            android:endY="16.113"
            android:type="linear">
          <item android:offset="0" android:color="#FF35DA51"/>
          <item android:offset="1" android:color="#FF19B4FD"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M0.315,16.887a7.067,5.421 54.117,1 0,8.784 -6.355a7.067,5.421 54.117,1 0,-8.784 6.355z"
        android:fillColor="#C25DFF"/>
    <path
        android:pathData="M14.684,10.113a3,4 0,1 0,6 0a3,4 0,1 0,-6 0z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="17.684"
            android:startY="6.113"
            android:endX="17.684"
            android:endY="14.113"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFCD2A"/>
          <item android:offset="1" android:color="#FF31E0B6"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M17.684,9.113m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0"
        android:strokeAlpha="0.3"
        android:fillAlpha="0.3">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="17.657"
            android:startY="5.113"
            android:endX="17.674"
            android:endY="12.617"
            android:type="linear">
          <item android:offset="0" android:color="#FF35DA51"/>
          <item android:offset="1" android:color="#FF25DE44"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M9.684,12.113a3.5,3 0,1 0,7 0a3.5,3 0,1 0,-7 0z"
        android:strokeAlpha="0.5"
        android:fillAlpha="0.5">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="17.738"
            android:startY="11.629"
            android:endX="10.74"
            android:endY="11.366"
            android:type="linear">
          <item android:offset="0" android:color="#FF90CED7"/>
          <item android:offset="0.921" android:color="#FFFF8C2C"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M10.184,18.613m-4.5,0a4.5,4.5 0,1 1,9 0a4.5,4.5 0,1 1,-9 0">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="14.684"
            android:startY="18.639"
            android:endX="5.684"
            android:endY="18.614"
            android:type="linear">
          <item android:offset="0" android:color="#FF11B1FD"/>
          <item android:offset="1" android:color="#FFE457EE"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M9.684,12.613a3,3.5 0,1 0,6 0a3,3.5 0,1 0,-6 0z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="15.684"
            android:startY="12.651"
            android:endX="9.49"
            android:endY="12.651"
            android:type="linear">
          <item android:offset="0" android:color="#FF10BFF7"/>
          <item android:offset="1" android:color="#FFC25DFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M8.684,10.613a3,3.5 0,1 0,6 0a3,3.5 0,1 0,-6 0z"
        android:strokeAlpha="0.4"
        android:fillAlpha="0.4">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="14.684"
            android:startY="10.651"
            android:endX="8.49"
            android:endY="10.651"
            android:type="linear">
          <item android:offset="0" android:color="#FF10BFF7"/>
          <item android:offset="1" android:color="#FFC25DFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M10,1a4,5 0,1 0,8 0a4,5 0,1 0,-8 0z"
        android:fillColor="#FF336E"/>
  </group>
</vector>
