/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryExportToNote
 * * Description :  摘要管理器实现类，负责处理摘要相关的业务逻辑
 * * Version     : 1.0
 * * Date        : 2025/07/14
 * * Author      : AI Assistant
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.net.Uri
import android.view.View
import androidx.appcompat.content.res.AppCompatResources
import androidx.lifecycle.LifecycleCoroutineScope
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.google.gson.reflect.TypeToken
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_ACTION_EXPORT
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_EXPORT_NOTE
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_NOTE
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_RETURN_FAILURE
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_RETURN_SUCCESS
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_STICKER_INSTALLATION_CANCELLED
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_STICKER_INSTALLATION_FAILED
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_TYPE_SUMMARY
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.exportfile.SummaryContentViewUtil
import com.soundrecorder.common.exportfile.ui.ExportTipsManager
import com.soundrecorder.common.note.ExportNoteUtil
import com.soundrecorder.common.removableapp.InstallResultCallback
import com.soundrecorder.common.removableapp.RemovableAppManager
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.request.database.SummaryCacheDBHelper
import com.soundrecorder.summary.util.SummaryConditionChecker
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.reflect.Type

object SummaryExportToNote {
    private const val TAG = "SummaryExportToNote"

    private const val NOTE_KEY_PACKAGE_NAME = "package_name"
    private const val NOTE_KEY_TITLE = "title"
    private const val NOTE_KEY_HTML_CONTENT = "html_content"
    private const val DELAY_TIME_INSERT_TO_NOTE = 500L
    private val notePackageName = AppUtil.getNotesPackageName()

    /**
     * 导出摘要到便签 - 统一入口方法
     * @param context 上下文
     * @param mediaId 录音文件ID
     * @param recordTitle 录音标题
     * @param recordFilePath 录音文件路径
     */
    fun exportSummaryToNote(
        context: Context,
        mediaId: Long,
        recordTitle: String?,
        recordFilePath: String?
    ) {
        exportSummaryToNote(context, mediaId, recordTitle, recordFilePath, false)
    }

    /**
     * 导出摘要到便签 - SummaryContentView专用方法
     * @param context 上下文
     * @param mediaId 录音文件ID
     * @param recordTitle 录音标题
     * @param recordFilePath 录音文件路径
     * @param lifecycle 生命周期作用域
     * @param exportTipsManager 导出提示管理器
     * @param view 显示提示视图容器
     */
    fun exportSummaryToNoteFromContentView(
        context: Context,
        mediaId: Long,
        recordTitle: String,
        recordFilePath: String,
        lifecycle: LifecycleCoroutineScope,
        exportTipsManager: ExportTipsManager,
        view: View
    ) {
        exportSummaryToNote(context, mediaId, recordTitle, recordFilePath, true, lifecycle, exportTipsManager, view)
    }

    /**
     * 导出摘要到便签 - 内部实现方法
     */
    private fun exportSummaryToNote(
        context: Context,
        mediaId: Long,
        recordTitle: String?,
        recordFilePath: String?,
        showDialog: Boolean = false,
        lifecycle: LifecycleCoroutineScope? = null,
        exportTipsManager: ExportTipsManager? = null,
        view: View? = null
    ) {
        val scope = lifecycle ?: CoroutineScope(Dispatchers.IO)
        scope.launch(Dispatchers.IO) {
            runCatching {
                val noteData = buildNoteData(context, mediaId, recordTitle, recordFilePath)
                if (noteData == null) {
                    return@launch
                }

                val install = SummaryContentViewUtil.isNoteAppInstalled(context as Activity)
                val insertUri = ExportNoteUtil.insertToNote(context, noteData.values)

                withContext(Dispatchers.Main) {
                    if (install && insertUri != null) {
                        handleExportSuccess(context, insertUri, noteData.grantedUri, mediaId, showDialog, exportTipsManager, view)
                    } else {
                        handleExportFailure(context, mediaId, recordTitle, recordFilePath, noteData.grantedUri)
                    }
                }
            }.onFailure { exception ->
                DebugUtil.e(TAG, "exportSummaryToNote error: ${exception.message}")
                withContext(Dispatchers.Main) {
                }
            }
        }
    }

    /**
     * 便签数据类
     */
    private data class NoteData(
        val values: ContentValues,
        val grantedUri: Uri
    )

    /**
     * 构建便签数据
     */
    private suspend fun buildNoteData(
        context: Context,
        mediaId: Long,
        recordTitle: String?,
        recordFilePath: String?
    ): NoteData ? {
        val summaryCacheEntity = SummaryCacheDBHelper.getChooseSummaryByMediaId(mediaId)
        if (summaryCacheEntity == null) {
            DebugUtil.w(TAG, "buildNoteData: no summary found for mediaId=$mediaId")
            return null
        }

        val noteTitle = recordTitle ?: MediaDBUtils.queryRecordById(mediaId).displayName

        val filePath = recordFilePath ?: MediaDBUtils.queryRecordById(mediaId).data

        val grantedUri = SummaryConditionChecker.grantUriPermission(context, filePath)

        val agentEvents = parseAgentEvents(summaryCacheEntity.summaryAgent)

        val entities = parseEntities(summaryCacheEntity.summaryEntity)

        // 准备HTML内容
        val noteHTMLContent = SummaryStyleHelper.prepareHtmlNoteContent(
            summaryOriginText = summaryCacheEntity.summaryContent ?: "",
            agent = agentEvents,
            summaryEntity = entities.filter { it.supportEntity() },
            uri = grantedUri,
            recordTitle = noteTitle,
            context = context
        )

        // 准备便签数据
        val values = ContentValues().apply {
            put(NOTE_KEY_PACKAGE_NAME, context.packageName)
            put(NOTE_KEY_TITLE, noteTitle)
            put(NOTE_KEY_HTML_CONTENT, noteHTMLContent)
        }

        return NoteData(values, grantedUri)
    }

    /**
     * 处理导出成功
     */
    private fun handleExportSuccess(
        context: Context,
        insertUri: Uri,
        grantedUri: Uri,
        mediaId: Long,
        showDialog: Boolean = false,
        exportTipsManager: ExportTipsManager?,
        view: View?
    ) {
        // 撤销URI权限
        SummaryConditionChecker.revokeUriPermission(context, grantedUri)
        DebugUtil.d(TAG, "handleExportSuccess: mediaId=$mediaId, showDialog=$showDialog")
        if (showDialog) {
            exportTipsManager?.showExportProcessDialog(context, com.soundrecorder.common.R.string.is_saving_talk_back) {
                exportTipsManager.showSnackBar(
                    context,
                    view,
                    context.getString(
                        com.soundrecorder.common.R.string.export_store_loacl_tips,
                        context.getString(com.soundrecorder.common.R.string.install_note_name)
                    ),
                    context.getString(com.soundrecorder.common.R.string.export_view_look)
                ) {
                    SummaryContentViewUtil.jumpToNote(insertUri, context as Activity)
                }
            }
        } else {
            SummaryContentViewUtil.jumpToNote(insertUri, context as Activity)
        }

        // 记录成功埋点
        AISummaryBuryingUtil.addRecordShareEvent(
            AISummaryBuryingUtil.ShareEventInfo(
                mediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                VALUE_EXPORT_NOTE, VALUE_FORMAT_NOTE, VALUE_RETURN_SUCCESS, ""
            )
        )
    }

    /**
     * 处理导出失败
     */
    private fun handleExportFailure(
        context: Context,
        mediaId: Long,
        recordTitle: String?,
        recordFilePath: String?,
        grantedUri: Uri
    ) {
        // 撤销URI权限
        SummaryConditionChecker.revokeUriPermission(context, grantedUri)
        // 显示便签应用安装对话框
        showInstallNotesDialog(context, mediaId, recordTitle, recordFilePath)
    }

    /**
     * 解析待办事项JSON字符串
     */
    private fun parseAgentEvents(agentJson: String?): List<SummaryAgentEvent> {
        if (agentJson.isNullOrEmpty()) {
            return emptyList()
        }
        return runCatching {
            val type: Type = object : TypeToken<List<SummaryAgentEvent>>() {}.type
            GsonUtil.getGson().fromJson<List<SummaryAgentEvent>>(agentJson, type) ?: emptyList()
        }.onFailure {
            DebugUtil.e(TAG, "parseAgentEvents error: ${it.message}, json=$agentJson")
        }.getOrDefault(emptyList())
    }

    /**
     * 解析实体JSON字符串
     */
    private fun parseEntities(entityJson: String?): List<SummaryEntity> {
        if (entityJson.isNullOrEmpty()) {
            return emptyList()
        }
        return runCatching {
            val type: Type = object : TypeToken<List<SummaryEntity>>() {}.type
            GsonUtil.getGson().fromJson<List<SummaryEntity>>(entityJson, type) ?: emptyList()
        }.onFailure {
            DebugUtil.e(TAG, "parseEntities error: ${it.message}, json=$entityJson")
        }.getOrDefault(emptyList())
    }

    /**
     * 显示便签应用安装对话框
     */
    private fun showInstallNotesDialog(
        context: Context,
        mediaId: Long,
        recordTitle: String?,
        recordFilePath: String?,
    ) {
        val title = String.format(
            context.getString(com.soundrecorder.common.R.string.summary_install_note),
            context.getString(com.soundrecorder.common.R.string.app_name_main)
        )
        COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Custom)
            .setTitle(title)
            .setCustomMessage(context.getString(com.soundrecorder.common.R.string.summary_install_note_content))
            .setCustomDrawable(AppCompatResources.getDrawable(context, com.soundrecorder.summary.R.drawable.ic_note))
            .setPositiveButton(com.soundrecorder.common.R.string.install) { _, _ ->
                reInstallNote(context, mediaId, recordTitle, recordFilePath)
            }
            .setNegativeButton(com.soundrecorder.common.R.string.cancel) { _, _ ->
                AISummaryBuryingUtil.addRecordShareEvent(
                    AISummaryBuryingUtil.ShareEventInfo(
                        mediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                        VALUE_EXPORT_NOTE, VALUE_FORMAT_NOTE, VALUE_RETURN_FAILURE, VALUE_STICKER_INSTALLATION_CANCELLED
                    )
                )
            }
            .show()
    }

    /**
     * 使用系统应用找回功能重新安装便签应用
     */
    private fun reInstallNote(
        context: Context,
        mediaId: Long,
        recordTitle: String?,
        recordFilePath: String?,
    ) {
        runCatching {
            val removableAppManager = RemovableAppManager(notePackageName)
            // 获取便签应用信息
            val removableAppInfo = removableAppManager.obtainRemovableInfo()
            // 执行重新安装
            removableAppManager.reInstallApp(
                context, removableAppInfo, callback = object : InstallResultCallback {
                    override fun onInstalledResult(success: Boolean) {
                        DebugUtil.d(TAG, "Note app reinstallation result: $success")
                        // 在主线程中处理结果
                        CoroutineScope(Dispatchers.Main).launch {
                            if (success) {
                                // 安装成功，重新尝试导出
                                handleReinstallSuccess(context, mediaId, recordTitle, recordFilePath)
                            } else {
                                // 安装失败，显示错误信息
                                handleReinstallFailure(context, mediaId)
                            }
                        }
                    }
                },
                negative = {
                    // 用户在安装过程中取消
                    DebugUtil.d(TAG, "User cancelled note app installation")
                }
            )
        }.onFailure {
            DebugUtil.e(TAG, "Error during note app reinstallation: ${it.message}")
            // 如果系统应用找回失败，回退到原有的应用恢复列表逻辑
            SummaryContentViewUtil.jumpAppRecoverList(context)
        }
    }

    /**
     * 处理重新安装成功
     */
    private suspend fun handleReinstallSuccess(
        context: Context,
        mediaId: Long,
        recordTitle: String?,
        recordFilePath: String?,
    ) {
        withContext(Dispatchers.IO) {
            // 重新构建便签数据
            val noteData = buildNoteData(context, mediaId, recordTitle, recordFilePath)
            if (noteData == null) {
                DebugUtil.w(TAG, "handleReinstallSuccess: failed to build note data")
                return@withContext
            }

            val install = SummaryContentViewUtil.isNoteAppInstalled(context as Activity)
            delay(DELAY_TIME_INSERT_TO_NOTE)
            val insertUri = ExportNoteUtil.insertToNote(context, noteData.values)

            if (install && insertUri != null) {
                withContext(Dispatchers.Main) {
                    SummaryContentViewUtil.jumpToNote(insertUri, context)
                    SummaryConditionChecker.revokeUriPermission(context, noteData.grantedUri)
                    AISummaryBuryingUtil.addRecordShareEvent(
                        AISummaryBuryingUtil.ShareEventInfo(
                            mediaId.toString(), VALUE_TYPE_SUMMARY,
                            VALUE_ACTION_EXPORT, VALUE_EXPORT_NOTE, VALUE_FORMAT_NOTE, VALUE_RETURN_SUCCESS, ""
                        )
                    )
                }
            } else {
                // 重新安装后仍然失败
                handleReinstallFailure(context, mediaId)
            }
        }
    }

    /**
     * 处理重新安装失败
     */
    private fun handleReinstallFailure(context: Context, mediaId: Long) {
        ExportTipsManager().showExportError(context.getString(com.soundrecorder.common.R.string.second_verify_failed))
        DebugUtil.e(TAG, "handleReinstallFailure: ExportHelper caller")
        AISummaryBuryingUtil.addRecordShareEvent(
            AISummaryBuryingUtil.ShareEventInfo(
                mediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                VALUE_EXPORT_NOTE, VALUE_FORMAT_NOTE, VALUE_RETURN_FAILURE, VALUE_STICKER_INSTALLATION_FAILED
            )
        )
    }
}