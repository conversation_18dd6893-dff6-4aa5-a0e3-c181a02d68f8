/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryDataParser
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.data

import android.content.Context
import com.oplus.recorderlog.util.GsonUtil
import com.oplus.soundrecorder.semantic.ssa.api.ISemanticTool
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.model.SummarySupportTheme
import com.soundrecorder.summary.model.SummaryTheme
import com.soundrecorder.summary.model.SummaryTrace
import com.soundrecorder.summary.ui.content.SummaryStyleHelper.getAgentEnd
import com.soundrecorder.summary.ui.content.SummaryStyleHelper.getAgentStart
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.json.JSONObject

object SummaryDataParser {

    private const val TAG = "SummaryDataParser"
    private const val TWO = 2
    private const val TASK_SYMBOL = "\n- [ ] "
    private const val TASK_DONE_SYMBOL = "\n- [x] "
    private const val DATA = "data"
    private const val CHOICES = "choices"
    private const val DELTA = "delta"
    private const val THEME_INFO = "themeInfo"
    private const val THEME_CODE = "themeCode"
    private const val THEME_VERSION = "themeVersion"
    private const val ABSTRACT_STYLE = "abstractStyle"
    private const val TITLE = "title"
    private const val SUPPORT_THEMES = "supportThemes"
    private const val NAMES = "names"
    private const val CONTENT = "content"
    private const val LAST_SUMMARY = "last_summary"
    private const val SUMMARY_ENTITIES = "summaryEntities"
    private const val EXTEND = "extend"
    private const val THEME = "theme"
    private const val TIME_STAMP = "timeStamp"

    private val SENTENCE_START_STAR_REGEX = Regex("^\\*\\*")
    private val STAR_REGEX_WITH_FRONT_SPACE = Regex("(?<= )\\*\\*")
    private val STAR_REGEX_WITH_BACK_SPACE = Regex("\\*\\*(?= )")
    private val SENTENCE_STAR_END_REGEX = Regex("\\*\\*(?=\\n)")
    private val STAR_NEXT_LINE_REGEX = Regex("(?m)^(\\s*)\\*\\*")
    private val START_WITH_COLON_REGEX = Regex("\\*\\*:")
    private val SENTENCE_START_POUND_REGEX = Regex("(?m)^(\\s*)##")
    private val POINT_DOT_STAR_REGEX = Regex("(?<=\\d\\.)\\*\\*")

    private val semanticTool by lazy {
        Injector.injectFactory<ISemanticTool>()
    }

    private val mainScope by lazy {
        MainScope()
    }

    @JvmStatic
    fun initSemanticTool() {
        mainScope.launch(Dispatchers.Default) {
            semanticTool?.install(BaseApplication.getAppContext())
            semanticTool?.initModel(BaseApplication.getAppContext())
        }
    }

    @JvmStatic
    fun parseContentInStream(jsonString: String): String {
        return kotlin.runCatching {
            val json = JSONObject(jsonString)
            val choices = json.getJSONArray(CHOICES)
            choices.getJSONObject(0).getJSONObject(DELTA).getString(CONTENT)
        }.onFailure {
            DebugUtil.e(TAG, "parseContent it = ${it.message}")
        }.getOrDefault("")
    }

    @JvmStatic
    fun parseContentInFinish(jsonString: String): String {
        return kotlin.runCatching {
            JSONObject(jsonString).getString(LAST_SUMMARY)
        }.onFailure {
            DebugUtil.e(TAG, "parseContent it = ${it.message}")
        }.getOrDefault("")
    }

    @JvmStatic
    fun parseTheme(jsonString: String): String {
        return kotlin.runCatching {
            JSONObject(jsonString).getString(THEME)
        }.onFailure {
            DebugUtil.e(TAG, "parseTheme it = ${it.message}")
        }.getOrDefault("")
    }

    @JvmStatic
    fun parseStyle(jsonString: String): Int {
        return kotlin.runCatching {
            JSONObject(jsonString).getJSONObject(DATA).getJSONObject(EXTEND).getInt("theme")
        }.onFailure {
            DebugUtil.e(TAG, "parseStyle it = ${it.message}")
        }.getOrDefault(-1)
    }

    @JvmStatic
    fun parseTime(jsonString: String): Long {
        return kotlin.runCatching {
            JSONObject(jsonString).getLong(TIME_STAMP)
        }.onFailure {
            DebugUtil.e(TAG, "parseTime it = ${it.message}")
        }.getOrDefault(0L)
    }

    @JvmStatic
    fun parseAgent(context: Context, originText: String): List<SummaryAgentEvent> {
        val agentStart = getAgentStart(context, originText) ?: return emptyList()
        val startIndex = originText.indexOf(agentStart)
        if (startIndex == -1) {
            return emptyList()
        }
        val endIndex = getAgentEnd(context, originText, startIndex + agentStart.length)?.let {
            originText.indexOf(it, startIndex + agentStart.length)
        } ?: -1
        val subStringB = if (endIndex == -1) {
            originText.substring(startIndex + agentStart.length)
        } else {
            originText.substring(startIndex + agentStart.length, endIndex)
        }
        // 2. 将子字符串 B 去掉开头和结尾，并以换行符为分割点切割成多个子串
        val lines = subStringB.split("\n").filter { it.isNotEmpty() }
        val result = lines.map { line ->
            val agentText =
                line.trimStart { it !in 'a'..'z' && it !in 'A'..'Z' && it !in '\u4e00'..'\u9fa5' }
            SummaryAgentEvent(agentText, false)
        }.toList().filter { it.agent.isNotEmpty() && it.agent.isNotBlank() }
        return result
    }

    @JvmStatic
    fun parseSummaryEntity(jsonString: String): List<SummaryEntity> {
        val entities = mutableListOf<SummaryEntity>()
        kotlin.runCatching {
            val summaryEntitiesJsonArray = JSONObject(jsonString).getJSONArray(SUMMARY_ENTITIES)
            for (i in 0 until summaryEntitiesJsonArray.length()) {
                val entityJson = summaryEntitiesJsonArray.getJSONObject(i)
                val name = entityJson.optString("name", "")
                var type = entityJson.optString("type", "")
                val turnId = entityJson.optInt("turnId", -1)
                val indexStart = entityJson.optInt("index_start", -1)
                val indexEnd = entityJson.optInt("index_end", -1)
                var timestamp = entityJson.optLong("timestamp", -1)
                val summaryIndex = entityJson.optInt("summaryindex", -1)
                if (SummaryEntity.typeTime(type)) {
                    val time = semanticTime(name, type)
                    type = time.first
                    timestamp = time.second
                }
                entities.add(
                    SummaryEntity(
                        name, turnId, indexStart, indexEnd, timestamp, type, summaryIndex
                    )
                )
            }
        }.onFailure {
            DebugUtil.e(TAG, "parseSummaryEntity e = ${it.message}")
        }
        return removeUselessEntity(entities)
    }

    @JvmStatic
    private fun semanticTime(name: String, type: String): Pair<String, Long> {
        return when (type) {
            SummaryEntity.SCHEDULE -> {
                val semantic = semanticTool?.process(BaseApplication.getAppContext(), name)
                    ?: return Pair(SummaryEntity.SCHEDULE, SummaryEntity.TIME_INVALID)
                DebugUtil.i(TAG, "semanticTime ${semantic.isNoTimeDateValid()}")
                if (semantic.isNoTimeDateValid()) {
                    val time = semantic.translate2Date().time
                    DebugUtil.i(TAG, "semanticTime ${semantic.getTimeInitialed()}")
                    if (semantic.getTimeInitialed()) {
                        Pair(SummaryEntity.SCHEDULE, time)
                    } else {
                        Pair(SummaryEntity.NO_TIME_SCHEDULE, time)
                    }
                } else {
                    Pair(SummaryEntity.SCHEDULE, SummaryEntity.TIME_INVALID)
                }
            }

            else -> Pair(type, SummaryEntity.TIME_INVALID)
        }
    }

    @JvmStatic
    private fun removeUselessEntity(entities: List<SummaryEntity>): List<SummaryEntity> {
        return entities.filter { (it.name.isEmpty()  || it.type.isEmpty()).not() }.distinctBy { it.name }
    }

    @JvmStatic
    private fun parseOriginText(formatText: String): String {
        // 1. 去除格式为 [`B`](#C) 的字符串
        val removePattern1 = Regex("\\[`.*?`]\\(#.*?\\)")
        val step1 = formatText.replace(removePattern1, "")

        // 2. 将格式为 [D](F) 的字符串替换为 D
        val replacePattern2 = Regex("\\[(.*?)\\]\\(.*?\\)")
        val step2 = step1.replace(replacePattern2) { match ->
            match.groupValues[1]
        }

        // 3. 将格式为 - [] 或 - [x] 的字符串替换为 *
        val replacePattern3 = Regex("\\n - \\[ ?x?\\] ")
        val result = step2.replace(replacePattern3, "\n* ")

        return result
    }

    @JvmStatic
    private fun parseFormatAgent(context: Context, formatText: String): List<SummaryAgentEvent> {
        val pattern = Regex("(\\n - \\[ ?x?\\] .+?)(?=\\n - \\[ ?x?\\] |$)")
        val matches = pattern.findAll(formatText)
        return matches.map { match ->
            val fullMatch = match.groupValues[0].trim()
            val isDone = fullMatch.startsWith(TASK_DONE_SYMBOL)
            val prefix = if (fullMatch.startsWith("\n - [x] ")) TASK_DONE_SYMBOL else TASK_SYMBOL
            val content = fullMatch.substring(prefix.length)
            SummaryAgentEvent(content, isDone)
        }.toList()
    }

    /**
     * 从格式化后的文本获取实体
     */
    @JvmStatic
    private fun parseFormatSummaryEntity(formatText: String): List<SummaryEntity> {
        val pattern = Regex("\\[.*?]\\((.*?)\\)")
        val matches = pattern.findAll(formatText)
        return matches.map {
            GsonUtil.getGson().fromJson(it.groupValues[1], SummaryEntity::class.java)
        }.toList()
    }

    @JvmStatic
    private fun parseFormatSummaryTrace(formatText: String): List<SummaryTrace> {
        val pattern = Regex("\\[`.*?`]\\(#(.*?)\\)")
        val matches = pattern.findAll(formatText)

        return matches.map {
            GsonUtil.getGson().fromJson(it.groupValues[1], SummaryTrace::class.java)
        }.toList()
    }

    @JvmStatic
    fun replaceEntity(content: String): String {
        val regex = Regex("\\[(.*?)\\]\\((.*?)\\)")
        return regex.replace(content) { matchResult ->
            matchResult.groupValues[1]
        }
    }

    @JvmStatic
    fun parseAbstractStyle(jsonString: String): String {
        return kotlin.runCatching {
            val json = JSONObject(jsonString)
            json.getJSONObject(THEME_INFO).getString(ABSTRACT_STYLE)
        }.onFailure {
            DebugUtil.e(TAG, "parseAbstractStyle it = ${it.message}")
        }.getOrDefault("")
    }

    @JvmStatic
    fun parseTitle(jsonString: String): String {
        return kotlin.runCatching {
            val json = JSONObject(jsonString)
            json.getJSONObject(THEME_INFO).getString(TITLE)
        }.onFailure {
            DebugUtil.e(TAG, "parseTitle it = ${it.message}")
        }.getOrDefault("")
    }

    @JvmStatic
    fun parseThemeCode(jsonString: String): Int {
        return kotlin.runCatching {
            val json = JSONObject(jsonString)
            json.getJSONObject(THEME_INFO).getInt(THEME_CODE)
        }.onFailure {
            DebugUtil.e(TAG, "parseThemeCode it = ${it.message}")
        }.getOrDefault(SummaryTheme.NORMAL)
    }

    @JvmStatic
    fun parseThemeInfo(jsonString: String): String {
        return kotlin.runCatching {
            val json = JSONObject(jsonString)
            json.getString(THEME_INFO)
        }.onFailure {
            DebugUtil.e(TAG, "parseThemeInfo it = ${it.message}")
        }.getOrDefault("")
    }

    @JvmStatic
    fun parseThemeSupport(jsonString: String): List<SummarySupportTheme> {
        return kotlin.runCatching {
            val json = JSONObject(jsonString).getJSONArray(SUPPORT_THEMES)
            val result = mutableListOf<SummarySupportTheme>()
            for (i in 0 until json.length()) {
                val entityJson = json.getJSONObject(i)
                val code = entityJson.getInt(THEME_CODE)
                val name = entityJson.getJSONObject(NAMES)
                val nameMap = HashMap<String, String>()
                name.keys().forEach {
                    nameMap[it] = name.getString(it)
                }
                val supportTheme = SummarySupportTheme(code, nameMap)
                result.add(supportTheme)
            }
            result
        }.onFailure {
            DebugUtil.e(TAG, "parseThemeSupport it = ${it.message}")
        }.getOrDefault(emptyList())
    }

    @JvmStatic
    fun parseSummaryContentText(summaryContent: String?): String? {
        if (summaryContent.isNullOrEmpty()) {
            return summaryContent
        }

        val result = summaryContent.replace(SENTENCE_START_STAR_REGEX, "")
            .replace(STAR_REGEX_WITH_FRONT_SPACE, "")
            .replace(STAR_REGEX_WITH_BACK_SPACE, "")
            .replace(SENTENCE_STAR_END_REGEX, "")
            .replace(STAR_NEXT_LINE_REGEX, "")
            .replace(START_WITH_COLON_REGEX, ":")
            .replace(SENTENCE_START_POUND_REGEX, "")
            .replace(POINT_DOT_STAR_REGEX, "")
            .replace("\n", " ")
        return result
    }
}