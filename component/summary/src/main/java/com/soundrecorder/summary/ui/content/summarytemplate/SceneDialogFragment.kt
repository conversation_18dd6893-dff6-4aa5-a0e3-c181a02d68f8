/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SceneAdapter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/08
 * * Author      : w9099028
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content.summarytemplate

import android.graphics.Rect
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.soundrecorder.summary.R

class SceneDialogFragment : COUIPanelFragment() {
    private var currentThemeId: Int = -1
    private var pendingSceneList: List<SceneItem> = emptyList()
    private var listener: ((Int) -> Unit)? = null
    private var adapter: SceneAdapter? = null
    private var selectedPosition = -1
    private var btnGenerate: COUIButton? = null
    var onGenerateClickListener: (() -> Unit)? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val root = super.onCreateView(inflater, container, savedInstanceState)!!
        (contentView as ViewGroup).apply {
            removeAllViews()
            addView(inflater.inflate(R.layout.fragment_scene_dialog, this, false))
        }
        return root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initRecyclerView(view)
        initButton(view)
        initToolbar()
    }

    private fun initButton(view: View) {
        btnGenerate = view.findViewById<COUIButton>(R.id.btn_generate).apply {
            isEnabled = false
            setOnClickListener {
                onGenerateClickListener?.invoke()
            }
        }
    }

    fun getSelectedSceneItem(): SceneItem? {
        return when {
            selectedPosition == -1 -> null
            adapter != null -> adapter?.getItemAt(selectedPosition)
            pendingSceneList.isNotEmpty() -> pendingSceneList.getOrNull(selectedPosition)
            else -> null
        }
    }

    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        //设置dialogFragment的背景
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.setPanelBackgroundTintColor(
            COUIContextUtil.getAttrColor(
                context,
                com.support.appcompat.R.attr.couiColorSurfaceWithCard
            )
        )
    }

    private fun initRecyclerView(view: View) {
        if (pendingSceneList.isEmpty()) {
            view.postDelayed({
                dismissDialog()
            }, AUTO_DISMISS_DELAY_MS)
            return
        }
        val sceneAdapter = SceneAdapter(requireContext()).apply {
            setOnItemClickListener { position ->
                selectedPosition = position
                // 激活生成按钮
                updateGenerateButtonState()
            }
            setData(pendingSceneList, selectedPosition)
        }
        this.adapter = sceneAdapter
        val spacing = view.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp12)
        view.findViewById<RecyclerView>(R.id.scene_recycler_view).apply {
            layoutManager = GridLayoutManager(context, 2, GridLayoutManager.VERTICAL, false)
            // 添加间距控制
            addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect,
                    view: View,
                    parent: RecyclerView,
                    state: RecyclerView.State
                ) {
                    val position = parent.getChildAdapterPosition(view)
                    val isRtl = parent.layoutDirection == View.LAYOUT_DIRECTION_RTL
                    // 左右间距分配
                    if (position % 2 == 0) {
                        // 左列：右边距为spacing/2
                        if (isRtl) {
                            outRect.left = spacing / 2
                            outRect.right = 0
                        } else {
                            outRect.left = 0
                            outRect.right = spacing / 2
                        }
                    } else {
                        // 右列：左边距为spacing/2
                        if (isRtl) {
                            outRect.right = spacing / 2
                            outRect.left = 0
                        } else {
                            outRect.left = spacing / 2
                            outRect.right = 0
                        }
                    }
                    // 统一底部间距
                    outRect.bottom = spacing
                }
            })
            adapter = sceneAdapter
            setHasFixedSize(true)
            itemAnimator = null
        }
    }

    private fun initToolbar() {
        toolbar?.apply {
            title = context.getString(com.soundrecorder.common.R.string.summary_select_template)
            visibility = View.VISIBLE
            isTitleCenterStyle = true
            inflateMenu(R.menu.menu_scene_cancel)
            menu.findItem(R.id.item_cancel_scene_dialog).apply {
                setOnMenuItemClickListener {
                    dismissDialog()
                    true
                }
            }
        }
    }

    fun setData(sceneList: List<SceneItem>, currentThemeId: Int) {
        this.currentThemeId = currentThemeId
        pendingSceneList = sceneList
        selectedPosition = sceneList.indexOfFirst { it.id == currentThemeId }
        adapter?.setData(sceneList, selectedPosition)
        updateGenerateButtonState()
    }

    private fun updateGenerateButtonState() {
        if (btnGenerate == null || pendingSceneList.isEmpty()) return
        val isDifferentTheme = selectedPosition != -1 &&
                pendingSceneList.getOrNull(selectedPosition)?.id != currentThemeId
        btnGenerate?.apply {
            isEnabled = isDifferentTheme
            drawableColor = COUIContextUtil.getColor(
                context,
                if (isDifferentTheme) {
                    com.support.appcompat.R.color.coui_color_red_dark
                } else {
                    com.support.appcompat.R.color.coui_color_disable_red_light
                }
            )
        }
    }

    fun clearSceneList() {
        pendingSceneList = emptyList()
    }

    fun setOnItemClickListener(listener: (Int) -> Unit) {
        this.listener = listener
    }

    private fun dismissDialog() {
        (parentFragment as? COUIBottomSheetDialogFragment)?.dismiss()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        listener = null
    }

    companion object {
        private const val AUTO_DISMISS_DELAY_MS = 100L
    }
}