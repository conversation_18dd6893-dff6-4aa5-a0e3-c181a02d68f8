/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SceneAdapter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/08
 * * Author      : w9099028
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content.summarytemplate

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.chip.COUIChip
import com.coui.appcompat.chip.COUIChipGroup
import com.soundrecorder.summary.R
import com.soundrecorder.summary.model.SummaryTheme

class SceneAdapter(
    private val context: Context
) : RecyclerView.Adapter<SceneAdapter.ViewHolder>() {
    private var sceneList: List<SceneItem> = emptyList()
    private var itemClickListener: ((Int) -> Unit)? = null
    private var selectedPosition = -1
    private val selectedBg by lazy {
        ContextCompat.getDrawable(context, R.drawable.audio_turn_on_transcription)
    }
    private val defaultBg by lazy {
        ContextCompat.getDrawable(context, R.drawable.bg_scene_item)
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val title: TextView = itemView.findViewById(R.id.scene_title)
        val icon: ImageView = itemView.findViewById(R.id.scene_icon)
        val chipGroup: COUIChipGroup = itemView.findViewById(R.id.scene_chip_group)

        init {
            itemView.setOnClickListener {
                val position = adapterPosition
                Log.d("SceneDebug", "🖱️ Item点击 | pos:$position")
                if (position != RecyclerView.NO_POSITION) {
                    itemClickListener?.invoke(position)
                    // 更新选中位置
                    setSelectedPosition(position)
                }
            }
        }
    }

    fun setSelectedPosition(position: Int) {
        val oldPosition = selectedPosition
        selectedPosition = position
        if (oldPosition != -1) notifyItemChanged(oldPosition)
        if (position != -1) notifyItemChanged(position)
    }

    private fun resetChipsBackground(group: COUIChipGroup) {
        for (i in 0 until group.childCount) {
            (group.getChildAt(i) as? COUIChip)?.apply {
                uncheckedBackgroundColor =
                    context.getColor(com.soundrecorder.common.R.color.disable_color)
                checkedBackgroundColor =
                    context.getColor(com.soundrecorder.common.R.color.disable_color)
            }
        }
    }

    fun getItemAt(position: Int): SceneItem? {
        return sceneList.getOrNull(position)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_scene, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val item = sceneList[position]
        // 如果是当前已选中的item且没有数据变化，直接返回
        if (position == selectedPosition && holder.itemView.background == selectedBg) {
            return
        }
        holder.title.text = item.title
        holder.chipGroup.removeAllChips()

        holder.itemView.background = if (position == selectedPosition) {
            selectedBg
        } else {
            defaultBg
        }

        val iconRes = when (item.id) {
            SummaryTheme.NORMAL -> R.drawable.ic_normal
            SummaryTheme.MEETING -> R.drawable.ic_meeting
            SummaryTheme.PHONE -> R.drawable.ic_phone
            SummaryTheme.INTERVIEW -> R.drawable.ic_interview
            SummaryTheme.CLASSROOM -> R.drawable.ic_classroom
            else -> null
        }
        iconRes?.let {
            holder.icon.setImageResource(it)
        } ?: holder.icon.setImageDrawable(null)

        val chipTexts = getChipTextsForTheme(holder.itemView.context, item.id)
        addChipsToGroup(holder.chipGroup, chipTexts)
    }

    private fun getChipTextsForTheme(context: Context, themeId: Int): Array<String> {
        return when (themeId) {
            SummaryTheme.NORMAL -> context.resources.getStringArray(R.array.standard_action_chip)
            SummaryTheme.MEETING -> context.resources.getStringArray(R.array.meeting_action_chip)
            SummaryTheme.PHONE -> context.resources.getStringArray(R.array.call_action_chip)
            SummaryTheme.INTERVIEW -> context.resources.getStringArray(R.array.interviews_action_chip)
            SummaryTheme.CLASSROOM -> context.resources.getStringArray(R.array.notes_action_chip)
            else -> emptyArray()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun addChipsToGroup(group: COUIChipGroup, texts: Array<String>) {
        texts.forEach { text ->
            val chip = COUIChip(group.context).apply {
                this.text = text
                setTextSize(TypedValue.COMPLEX_UNIT_DIP, CHIP_TEXT_SIZE_DP)
                isChecked = false
                isCheckable = false
                isClickable = false
                isFocusable = false
                isFocusableInTouchMode = false
                isDuplicateParentStateEnabled = false

                uncheckedBackgroundColor =
                    context.getColor(com.soundrecorder.common.R.color.disable_color)
                checkedBackgroundColor =
                    context.getColor(com.soundrecorder.common.R.color.disable_color)

                // 拦截所有触摸事件
                setOnTouchListener { _, _ -> true }
            }
            group.addView(chip)
        }
    }

    override fun getItemCount(): Int = sceneList.size

    fun setData(list: List<SceneItem>) {
        sceneList = list
        notifyDataSetChanged()
    }

    fun setOnItemClickListener(listener: (Int) -> Unit) {
        itemClickListener = listener
    }

    companion object {
        private const val CHIP_TEXT_SIZE_DP = 10.0f
    }
}

data class SceneItem(
    val id: Int,
    val title: String,
    val iconRes: Int
)