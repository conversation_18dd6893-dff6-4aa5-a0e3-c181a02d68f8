/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryHorizontalScrollView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.ViewConfiguration
import com.coui.appcompat.scrollview.COUIHorizontalScrollView
import kotlin.math.abs

class SummaryHorizontalScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : COUIHorizontalScrollView(context, attrs, defStyleAttr) {

    private val touchSlop = ViewConfiguration.get(context).scaledTouchSlop
    private var startX = 0f
    private var startY = 0f

    var onClick: (() -> Unit)? = null

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                startX = ev.x
                startY = ev.y
                return true
            }

            MotionEvent.ACTION_MOVE -> {
                super.onTouchEvent(ev)
                return true
            }

            MotionEvent.ACTION_UP -> {
                val dx = abs(ev.x - startX)
                val dy = abs(ev.y - startY)
                if (dx < touchSlop && dy < touchSlop) {
                    onClick?.invoke() // 触发点击回调
                }
                super.onTouchEvent(ev)
                return true
            }

            MotionEvent.ACTION_CANCEL -> {
                super.onTouchEvent(ev)
                return true
            }
        }
        return super.onTouchEvent(ev)
    }

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        return super.onInterceptTouchEvent(ev)
    }
}