/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryContentView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.animation.Animator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.text.method.LinkMovementMethod
import android.text.style.URLSpan
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.ScrollView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.coui.appcompat.segmentbutton.COUISegmentButtonLayout
import com.coui.appcompat.textview.COUITextView
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.coui.appcompat.tips.def.COUIDefaultTopTips
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.PrefUtil.SUMMARY_SUPPORT_THEME
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_ACTION_EXPORT
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_EXPORT_DOCUMENT
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FILE_GENERATION_FAILED
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_PDF
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_FORMAT_WORD
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_RETURN_FAILURE
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_RETURN_SUCCESS
import com.soundrecorder.common.buryingpoint.AISummaryBuryingUtil.VALUE_TYPE_SUMMARY
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.exportfile.ExportDoc
import com.soundrecorder.common.exportfile.ExportPdf
import com.soundrecorder.common.exportfile.SummaryContentViewUtil
import com.soundrecorder.common.exportfile.ui.ExportTipsManager
import com.soundrecorder.common.share.ShareSummaryCopy
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.common.utils.AITextStyleUtil
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.visible
import com.soundrecorder.common.widget.OSImageView
import com.soundrecorder.modulerouter.share.ShareAction
import com.soundrecorder.modulerouter.summary.IAISummaryCallback
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.summary.R
import com.soundrecorder.summary.data.SummaryDataParser
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.model.SummaryModel
import com.soundrecorder.summary.model.SummarySupportTheme
import com.soundrecorder.summary.model.SummaryTheme
import com.soundrecorder.summary.model.SummaryTrace
import com.soundrecorder.summary.request.AISummaryProcess.Companion.IS_OVER_SIZE
import com.soundrecorder.summary.request.AISummaryProcess.Companion.THEME_CODE
import com.soundrecorder.summary.ui.content.SummaryAnimateTextView.TextAnimationListener
import com.soundrecorder.summary.ui.content.SummaryExportToNote.ContentViewParams
import com.soundrecorder.summary.ui.content.SummaryExportToNote.ExportRequest
import com.soundrecorder.summary.ui.content.callback.ISummaryFunctionCallback
import com.soundrecorder.summary.ui.content.callback.SummaryActionModeCallback
import com.soundrecorder.summary.ui.content.span.AgentClickPlugin
import com.soundrecorder.summary.ui.content.span.ConfigPlugin
import com.soundrecorder.summary.ui.content.span.SummaryLinkResolver
import com.soundrecorder.summary.ui.content.summarytemplate.SceneBottomSheetDialogFragment
import com.soundrecorder.summary.ui.content.summarytemplate.SceneDialogFragment
import com.soundrecorder.summary.ui.content.summarytemplate.SceneItem
import io.noties.markwon.Markwon
import io.noties.markwon.SoftBreakAddsNewLinePlugin
import io.noties.markwon.ext.tasklist.TaskListPlugin
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

class SummaryContentView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ScrollView(context, attrs, defStyleAttr), OnClickListener, ViewTreeObserver.OnGlobalLayoutListener {

    companion object {
        private const val TAG = "SummaryContentView"
        private const val LOADING_TIPS_DELAY = 1000L

        private const val ITEM_POSITION_0 = 0
        private const val ITEM_POSITION_1 = 1

        private const val SIZE_TWO = 2

        private const val TAG_NEXT = "next"
        private const val TAG_REFRESH = "refresh"
        private const val DOC_SUFFIX = "doc"
        private const val PDF_SUFFIX = "pdf"

        private const val DURATION = 300L
        private const val OPAQUE = 255
        private const val SCROLLER_CHECK_TIME = 50L
    }

    var currentScrollState: Int = Constants.SCROLL_STATE_IDLE

    //已经设置了数据，但是不一定动画结束了
    var isFinishUploadSummary = false

    private lateinit var container: ConstraintLayout
    private lateinit var loadingView: EffectiveAnimationView
    private lateinit var content: SummaryAnimateTextView
    private lateinit var cardContainer: LinearLayout
    private lateinit var copyRight: COUITextView
    private lateinit var divider: View
    private lateinit var toolBar: RelativeLayout
    private lateinit var copy: AppCompatImageView
    private lateinit var export: AppCompatImageView
    private lateinit var scene: COUITextView
    private lateinit var tab: COUISegmentButtonLayout
    private lateinit var previous: AppCompatImageView
    private lateinit var refresh: AppCompatImageView
    private lateinit var errorView: ConstraintLayout
    private lateinit var errorIconView: OSImageView
    private lateinit var errorMsgText: COUITextView
    private lateinit var retry: COUITextView
    private lateinit var loadTip: COUIDefaultTopTips
    private lateinit var initView: ConstraintLayout
    private lateinit var initIconView: OSImageView
    private lateinit var summaryStart: COUITextView
    private var summaryChildFragmentManager: FragmentManager? = null

    private val agentDrawable: Drawable? by lazy {
        ResourcesCompat.getDrawable(
            context.resources,
            com.soundrecorder.summary.R.drawable.ic_agent_un_check,
            context.theme
        )
    }
    private val agentUnCheckDrawable: Drawable? by lazy {
        ResourcesCompat.getDrawable(
            context.resources,
            com.soundrecorder.summary.R.drawable.ic_agent_check,
            context.theme
        )
    }

    private val contentMarginLoadTip: Int by lazy {
        context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp16)
    }
    private val toolbarDefaultHeight: Int by lazy {
        context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp52)
    }

    private val summaryEntityHelper: SummaryEntityHelper by lazy {
        SummaryEntityHelper(context)
    }

    private val shareAction by lazy {
        Injector.injectFactory<ShareAction>()
    }
    private var sceneBottomSheet: SceneBottomSheetDialogFragment? = null

    private val exportTipsManager: ExportTipsManager by lazy { ExportTipsManager() }

    private var scenePopList: COUIPopupListWindow? = null
    private var exportPopList: COUIPopupListWindow? = null
    private var entityPopList: COUIPopupListWindow? = null

    private val activity = context as Activity
    private val lifecycle = (context as? AppCompatActivity)?.lifecycleScope
    private var loadingJob: Job? = null
    private var summaryFunctionCallback: ISummaryFunctionCallback? = null
    private var summaryContentText: String = ""        // 渲染后的文本内容
    private var summaryOriginText: String = ""         // 原始摘要文本

    // 录音文件信息
    private var currentMediaId: Long = 0L
    private var currentRecordTitle: String = ""
    private var currentRecordFilePath: String = ""
    private var currentRecordDuration: Long = 0

    //识别出的主题、标题、语言等
    private var identifiedTheme: SummaryTheme = SummaryTheme(SummaryTheme.NORMAL)
    private var identifiedTitle: String = ""

    private var currentTheme: SummaryTheme? = null
    private var agent = mutableListOf<SummaryAgentEvent>()
    private var summaryEntity = mutableListOf<SummaryEntity>()

    //结束加载，包括动画
    private var isLoadingFinish = false
    private var bgAnimator: ValueAnimator? = null
    private var supportThemes = mutableListOf<SummarySupportTheme>()
    private var lastScrollY = 0
    private var gestureDetector: GestureDetector? = null
    private var markwon: Markwon? = null
    private var summaryCallback: IAISummaryCallback? = null


    private fun initChild() {
        initContainer()
        initStartView()
        initLoading()
        initContent()
        initTools()
        initCardContainer()
        initOther()
        initErrorView()
        initLoadTip()
        initGestureDetector()
    }

    private fun initContainer() {
        LayoutInflater.from(context)
            .inflate(com.soundrecorder.summary.R.layout.layout_summary_container_view, this, true)
        container = findViewById(com.soundrecorder.summary.R.id.container_view)
    }

    private fun initStartView() {
        initView = findViewById(com.soundrecorder.summary.R.id.layout_init)
        initIconView = findViewById(com.soundrecorder.summary.R.id.init_logo)
        summaryStart = findViewById(com.soundrecorder.summary.R.id.tv_start_summary)
        summaryStart.setOnClickListener(this)
    }

    private fun initLoading() {
        loadingView = findViewById(com.soundrecorder.summary.R.id.summary_loading)
    }

    private fun showLoadingTips() {
        copyRight.setText(com.soundrecorder.base.R.string.summary_loading_tips)
        val lp = copyRight.layoutParams as? ConstraintLayout.LayoutParams
        lp?.topToBottom = com.soundrecorder.summary.R.id.summary_loading
        copyRight.visible()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initContent() {
        content = findViewById(com.soundrecorder.summary.R.id.summary_content)
        content.highlightColor = Color.TRANSPARENT
        val actionModeCallback = SummaryActionModeCallback(content, true)
        content.customSelectionActionModeCallback = actionModeCallback
        content.setOnLongClickListener {
            content.highlightColor = actionModeCallback.getColor()
            false
        }
    }

    private fun initTools() {
        toolBar = findViewById(com.soundrecorder.summary.R.id.summary_tool_bar)
        copy = findViewById(com.soundrecorder.summary.R.id.copy)
        export = findViewById(com.soundrecorder.summary.R.id.export)
        scene = findViewById(com.soundrecorder.summary.R.id.summary_scene)
        tab = findViewById(com.soundrecorder.summary.R.id.tab_layout)
        previous = findViewById(com.soundrecorder.summary.R.id.previous)
        previous.gone()
        refresh = findViewById(com.soundrecorder.summary.R.id.refresh)

        copy.setOnClickListener(this)
        export.setOnClickListener(this)
        scene.setOnClickListener(this)
        tab.setOnSelectedSegmentChangeListener(::handleTabChange)
        previous.setOnClickListener(this)
        refresh.setOnClickListener(this)
    }
    private fun handleTabChange(lastPos: Int, newPos: Int, progress: Float) {
        if (lastPos == newPos || lastPos < 0) return
        // 等待动画完成 (progress == 1f)
        if (progress < 1f) return
        when (newPos) {
            0 -> switchTheme(SummaryTheme.NORMAL_SIMPLE)
            1 -> switchTheme(SummaryTheme.NORMAL)
        }
    }
    private fun switchTheme(id: Int) {
        val themeChange = id != currentTheme?.style
        currentTheme = SummaryTheme(id)
        if (themeChange) {
            summaryFunctionCallback?.onClickScene(currentTheme)
        }
        updateSceneIcons()
    }
    private fun updateSceneIcons() {
        scene.text = getThemeTitle(currentTheme)
        val themeDrawable = ContextCompat.getDrawable(
            context,
            SummaryTheme.getDrawableRes(currentTheme?.style ?: SummaryTheme.NORMAL)
        )
        val expandIconRes = if (isSceneBottomSheetShowing()) {
            com.soundrecorder.summary.R.drawable.ic_scene_select_collapsed
        } else {
            com.soundrecorder.summary.R.drawable.ic_scene_select_expand
        }
        scene.setCompoundDrawablesWithIntrinsicBounds(
            themeDrawable,
            null,
            ContextCompat.getDrawable(context, expandIconRes),
            null
        )
    }
    private fun isSceneBottomSheetShowing(): Boolean {
        return sceneBottomSheet?.dialog?.isShowing == true
    }
    private fun initCardContainer() {
        cardContainer = findViewById(com.soundrecorder.summary.R.id.card_container)
    }

    private fun initOther() {
        copyRight = findViewById(com.soundrecorder.summary.R.id.copyright)
        divider = findViewById(com.soundrecorder.summary.R.id.divider_line)
    }

    private fun initErrorView() {
        errorView = findViewById(com.soundrecorder.summary.R.id.layout_error)
        errorIconView = findViewById(com.soundrecorder.summary.R.id.error_logo)
        errorMsgText = findViewById(com.soundrecorder.summary.R.id.error_msg_text)
        retry = findViewById(com.soundrecorder.summary.R.id.retry)
        retry.setOnClickListener(this)
        COUITextViewCompatUtil.setPressRippleDrawable(retry)
    }

    private fun initLoadTip() {
        loadTip = findViewById(com.soundrecorder.summary.R.id.load_tips)
        loadTip.setCloseDrawable(ContextCompat.getDrawable(context, com.support.tips.R.drawable.coui_ic_toptips_close))
        loadTip.setStartIcon(ContextCompat.getDrawable(context, com.soundrecorder.summary.R.drawable.ic_detail_floating_layer))
        loadTip.setAnimatorDismissListener(object : Animator.AnimatorListener {
            override fun onAnimationEnd(animation: Animator) {
                loadTipsGone()
            }

            override fun onAnimationStart(animation: Animator) {
            }

            override fun onAnimationCancel(animation: Animator) {
            }

            override fun onAnimationRepeat(animation: Animator) {
            }
        })
        loadTip.setCloseBtnListener {
            loadTip.dismissWithAnim()
        }
    }

    private fun initGestureDetector() {
        gestureDetector = GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onScroll(
                e1: MotionEvent?,
                e2: MotionEvent,
                distanceX: Float,
                distanceY: Float
            ): Boolean {
                // 当用户手指拖动时触发
                setScrollState(Constants.SCROLL_STATE_DRAGGING)
                return super.onScroll(e1, e2, distanceX, distanceY)
            }
        })
    }

    init {
        initChild()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        onStopLoading()
        content.cancelAnimation()
        scenePopList?.dismiss()
        exportPopList?.dismiss()
        entityPopList?.dismiss()
        bgAnimator?.cancel()
        exportTipsManager.release()
        gestureDetector = null
        errorIconView.release()
        initIconView.release()
    }

    override fun onClick(v: View?) {
        v ?: return
        DebugUtil.d(TAG, "isLoadingFinish = $isLoadingFinish v is ${v == summaryStart}")
        if (isLoadingFinish.not() && (v != summaryStart)) {
            return
        }
        if (ClickUtils.isQuickClick()) {
            DebugUtil.d(TAG, "isQuickClick")
            return
        }
        when (v) {
            copy -> copySummary()
            export -> showExportPopMenu()
            scene -> showScenePopMenu()
            previous -> clickPrevious()
            refresh -> clickRefreshOrNext()
            retry -> clickRetry()
            summaryStart -> clickSummaryStart()
            else -> DebugUtil.w(TAG, "click what ? v $v")
        }
    }

    private fun copySummary() {
        summaryFunctionCallback?.onClickCopy()
        lifecycle ?: return
        lifecycle.launch {
            val title = getSummaryTitle()
            val time = withContext(Dispatchers.IO) {
                summaryCallback?.getSummaryTime() ?: ""
            }
            val shareTextContent = "$title\n$time\n${content.text}"
            if (shareTextContent.isEmpty()) {
                DebugUtil.w(TAG, "why is empty? Check again carefully")
                return@launch
            }
            shareAction?.share(
                activity,
                ShareTextContent(-1, false, "", 0, emptyList()),
                ShareSummaryCopy(shareTextContent),
                lifecycle,
                null
            )
        }
    }

    private fun showExportPopMenu() {
        exportPopList?.dismiss()
        lifecycle?.launch {
            val isSupportExportDoc = withContext(Dispatchers.Default) {
                ExportDoc.isSupportExport(context)
            }
            val exportItemList = mutableListOf<PopupListItem>().apply {
                val builder = PopupListItem.Builder()
                if (isSupportExportDoc) {
                    builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_doc)
                        .setTitle(
                            context.getString(
                                com.soundrecorder.common.R.string.summary_export_to,
                                context.getString(com.soundrecorder.common.R.string.word)
                            )
                        )
                        .setGroupId(com.soundrecorder.common.R.id.group1)
                    add(builder.build())
                }
                builder.reset()

                builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_pdf)
                    .setTitle(
                        context.getString(
                            com.soundrecorder.common.R.string.summary_export_to,
                            context.getString(com.soundrecorder.common.R.string.pdf)
                        )
                    )
                    .setGroupId(com.soundrecorder.common.R.id.group2)
                add(builder.build())


                builder.setIconId(com.soundrecorder.summary.R.drawable.ic_export_note)
                    .setTitle(context.getString(com.soundrecorder.common.R.string.summary_export_to_note))
                    .setGroupId(com.soundrecorder.common.R.id.group3)
                add(builder.build())
            }

            exportPopList = COUIPopupListWindow(context).apply {
                this.itemList = exportItemList
                this.anchorView = export
                this.resetOffset()
                this.setOnItemClickListener { _, _, pos, _ ->
                    if (ClickUtils.isQuickClick()) {
                        return@setOnItemClickListener
                    }
                    when {
                        itemList.size > SIZE_TWO -> {
                            when (pos) {
                                ITEM_POSITION_0 -> exportToDoc()
                                ITEM_POSITION_1 -> exportToPdf()
                                else -> exportToNote()
                            }
                        }

                        else -> {
                            when (pos) {
                                ITEM_POSITION_0 -> exportToPdf()
                                else -> exportToNote()
                            }
                        }
                    }
                    this.dismiss()
                }
                this.show()
            }
        }
    }

    private fun exportToDoc() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_WORD)
        lifecycle?.launch(Dispatchers.IO) {
            runCatching {
                val title = <EMAIL>()
                // 准备导出数据
                val exportData = SummaryContentViewUtil.prepareExportData(context, title, getOriginSummaryText())
                // 生成文件路径
                val targetPath = SummaryContentViewUtil.generateExportFilePath(context, DOC_SUFFIX, title)
                if (targetPath.isEmpty()) {
                    exportTipsManager.showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))
                    return@launch
                }

                // 执行导出
                val success = ExportDoc.saveToWord(context, targetPath, exportData)

                withContext(Dispatchers.Main) {
                    //导出成功 弹窗 button 点击跳转文档预览文件
                    if (success) {
                        exportTipsManager.showExportProcessDialog(context, com.soundrecorder.common.R.string.summary_export_loading) {
                            exportTipsManager.showSnackBar(
                                context,
                                this@SummaryContentView,
                                context.getString(com.soundrecorder.common.R.string.summary_export_note_finish),
                                context.getString(com.soundrecorder.common.R.string.export_view_look)
                            ) {
                                SummaryContentViewUtil.openDocumentFile(context, targetPath)
                            }
                            /*添加埋点事件*/
                            AISummaryBuryingUtil.addRecordShareEvent(
                                AISummaryBuryingUtil.ShareEventInfo(
                                    currentMediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                                    VALUE_EXPORT_DOCUMENT, VALUE_FORMAT_WORD, VALUE_RETURN_SUCCESS, ""
                                )
                            )
                        }
                    } else {
                        exportTipsManager.showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))
                        AISummaryBuryingUtil.addRecordShareEvent(
                            AISummaryBuryingUtil.ShareEventInfo(
                                currentMediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                                VALUE_EXPORT_DOCUMENT, VALUE_FORMAT_WORD, VALUE_RETURN_FAILURE, VALUE_FILE_GENERATION_FAILED
                            )
                        )
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "exportFile error: ${it.message}")
            }
        }
    }

    private fun exportToPdf() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_PDF)
        lifecycle?.launch(Dispatchers.IO) {
            runCatching {
                val title = <EMAIL>()
                // 准备导出数据
                val exportData = SummaryContentViewUtil.prepareExportData(context, title, getOriginSummaryText())
                // 生成文件路径
                val targetPath = SummaryContentViewUtil.generateExportFilePath(context, PDF_SUFFIX, title)

                if (targetPath.isEmpty()) {
                    exportTipsManager.showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))
                    return@launch
                }

                // 执行导出
                val success = ExportPdf.saveToPdf(context, targetPath, exportData)

                withContext(Dispatchers.Main) {
                    if (success) {
                        exportTipsManager.showExportProcessDialog(context, com.soundrecorder.common.R.string.summary_export_loading) {
                            exportTipsManager.showSnackBar(
                                context,
                                this@SummaryContentView,
                                context.getString(com.soundrecorder.common.R.string.summary_export_note_finish),
                                context.getString(com.soundrecorder.common.R.string.export_view_look)
                            ) {
                                SummaryContentViewUtil.openDocumentFile(context, targetPath)
                            }
                            /*添加埋点事件*/
                            AISummaryBuryingUtil.addRecordShareEvent(
                                AISummaryBuryingUtil.ShareEventInfo(
                                    currentMediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                                    VALUE_EXPORT_DOCUMENT, VALUE_FORMAT_PDF, VALUE_RETURN_SUCCESS, ""
                                )
                            )
                        }
                    } else {
                        exportTipsManager.showExportError(context.getString(com.soundrecorder.common.R.string.save_failed))

                        AISummaryBuryingUtil.addRecordShareEvent(
                            AISummaryBuryingUtil.ShareEventInfo(
                                currentMediaId.toString(), VALUE_TYPE_SUMMARY, VALUE_ACTION_EXPORT,
                                VALUE_EXPORT_DOCUMENT, VALUE_FORMAT_PDF, VALUE_RETURN_FAILURE, VALUE_FILE_GENERATION_FAILED
                            )
                        )
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "exportToPdf error: ${it.message}")
            }
        }
    }

    private fun exportToNote() {
        summaryFunctionCallback?.onClickExport(ISummaryFunctionCallback.ExportType.TO_NOTE)
        val exportRequest = ExportRequest(context, currentMediaId, getSummaryTitle(), currentRecordFilePath)
        val contentViewParams = ContentViewParams(
            lifecycle ?: return,
            exportTipsManager,
            WeakReference(this@SummaryContentView)
        )
        SummaryExportToNote.exportSummaryToNoteFromContentView(exportRequest, contentViewParams)
    }

    fun setChildFragmentManager(fm: FragmentManager) {
        this.summaryChildFragmentManager = fm
    }
    private fun showScenePopMenu() {
        val sceneList = getSceneList()
            .filter { it.id != SummaryTheme.NORMAL_SIMPLE }
            .map { scene ->
                SceneItem(scene.id, scene.title, 0)
            }
        sceneBottomSheet?.dismiss()
        sceneBottomSheet = null

        sceneBottomSheet = SceneBottomSheetDialogFragment().apply {
            setMainPanelFragment(SceneDialogFragment().apply {
                // 设置生成按钮点击回调
                onGenerateClickListener = {
                    getSelectedSceneItem()?.let { item ->
                        switchTheme(item.id)
                        dismiss()
                    }
                }
                setOnDismissListener {
                    refreshExpandIcon(false)
                }
                setData(sceneList)
            })

            summaryChildFragmentManager?.let { manager ->
                if (!manager.isDestroyed) {
                    show(manager, "SceneBottomSheet")
                } else {
                    DebugUtil.d(TAG, "FragmentManager is destroyed")
                }
            }
        }
        refreshExpandIcon(true)
    }

    private fun refreshExpandIcon(isIconUp: Boolean) {
        val leftDrawable = scene.compoundDrawables[0]
        scene.setCompoundDrawablesRelativeWithIntrinsicBounds(
            leftDrawable,
            null,
            ContextCompat.getDrawable(
                context,
                if (isIconUp) {
                    com.soundrecorder.summary.R.drawable.ic_scene_select_collapsed
                } else {
                    com.soundrecorder.summary.R.drawable.ic_scene_select_expand
                }
            ),
            null
        )
    }

    private fun getSceneList(): List<PopupListItem> {
        return mutableListOf<PopupListItem>().apply {
            val builder = PopupListItem.Builder()
            //先添加识别出的场景
            DebugUtil.i(TAG, "getSceneList supportThemes = $supportThemes, identifiedTheme = $identifiedTheme")
            if (supportThemes.isEmpty()) {
                val normalDetail = SummaryTheme(SummaryTheme.NORMAL)
                builder.reset()
                builder.setId(SummaryTheme.NORMAL)
                builder.setTitle(getThemeTitle(normalDetail))
                    .setIsChecked(currentTheme == normalDetail)
                add(builder.build())
            } else {
                supportThemes.forEach {
                    val code = it.code
                    if (code == SummaryTheme.NORMAL) {
                        val normalDetail = SummaryTheme(SummaryTheme.NORMAL)
                        builder.reset()
                        builder.setId(SummaryTheme.NORMAL)
                        builder.setTitle(getThemeTitle(normalDetail)).setIsChecked(currentTheme == normalDetail)
                        add(builder.build())

                        val simple = SummaryTheme(SummaryTheme.NORMAL_SIMPLE)
                        //如果是通用场景，则添加简单和详细，只有通用场景才有简单和详细
                        builder.reset()
                        builder.setId(SummaryTheme.NORMAL_SIMPLE)
                        builder.setTitle(getThemeTitle(simple)).setIsChecked(currentTheme == simple)
                        add(builder.build())
                    } else {
                        val summaryTheme = SummaryTheme(code)
                        builder.reset()
                        builder.setId(code)
                        builder.setTitle(getThemeTitle(summaryTheme)).setIsChecked(currentTheme == summaryTheme)
                        add(builder.build())
                    }
                }
            }
        }
    }

    private fun clickPrevious() {
        summaryFunctionCallback?.onClickPrevious()
        refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
    }

    private fun clickRefreshOrNext() {
        DebugUtil.d(TAG, "clickRefreshOrNext ${refresh.tag}")
        if (refresh.tag == TAG_REFRESH) {
            summaryFunctionCallback?.onClickRefresh()
        } else {
            summaryFunctionCallback?.onClickNext()
        }
    }

    private fun clickRetry() {
        summaryFunctionCallback?.onClickRetry()
    }

    private fun clickSummaryStart() {
        summaryFunctionCallback?.onClickSummaryStart()
    }

    fun setSummaryFunctionCallback(callback: ISummaryFunctionCallback) {
        summaryFunctionCallback = callback
    }

    /**
     * 设置录音文件信息
     * 从SummaryFragment传递录音文件的基本信息
     */
    fun setRecordInfo(mediaId: Long, recordTitle: String, recordFilePath: String, duration: Long) {
        this.currentMediaId = mediaId
        this.currentRecordTitle = recordTitle
        this.currentRecordFilePath = recordFilePath
        this.currentRecordDuration = duration
    }

    fun setSummaryContent(summaryModel: SummaryModel) {
        onStopLoading()
        showCopyright()
        divider.visible()
        toolBar.visible()
        scene.visible()
        tab.visible()
        content.visible()
        updateContentConstraints()
        loadTipsGone()
        agent.clear()
        agent.addAll(summaryModel.agentEvents)
        summaryEntity.clear()
        summaryEntity.addAll(summaryModel.entities.filter { it.supportEntity() })
        updateScene(summaryModel.theme)
        val markdown = buildMarkDown()
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context,
            summaryModel.summary,
            summaryModel.summaryTrace,
            summaryEntity,
            summaryModel.agentEvents
        )
        val spanned = markdown.toMarkdown(formatStream)
        DebugUtil.d(TAG, "setSummaryContent = $formatStream, $summaryEntity,$agent")
        content.text = spanned
        summaryContentText = formatStream
        summaryOriginText = summaryModel.summary
        isLoadingFinish = true
        isFinishUploadSummary = true
        setContentSelectable()
    }

    private fun updateContentConstraints() {
        val constraintSet = ConstraintSet().apply {
            clone(container)
            clear(R.id.summary_content, ConstraintSet.TOP)
            clear(R.id.summary_content, ConstraintSet.BOTTOM)

            connect(
                R.id.summary_content,
                ConstraintSet.TOP,
                R.id.summary_scene,
                ConstraintSet.BOTTOM,
                resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp16)
            )

            connect(
                R.id.summary_content,
                ConstraintSet.START,
                ConstraintSet.PARENT_ID,
                ConstraintSet.START,
                0
            )
            connect(
                R.id.summary_content,
                ConstraintSet.END,
                ConstraintSet.PARENT_ID,
                ConstraintSet.END,
                0
            )
        }
        container.post {
            constraintSet.applyTo(container as ConstraintLayout)
            container.requestLayout()
        }
    }
    private fun setContentSelectable() {
        content.setTextIsSelectable(true)
        content.movementMethod = LinkMovementMethod.getInstance()
        content.revealOnFocusHint = false
    }

    /**
     * 检测是否是最新的摘要，如果是，视觉上会有一些不同的UI
     */
    fun checkCurrentState(isLastSummary: Boolean, isOnly: Boolean, isFirstSummary: Boolean) {
        DebugUtil.d(
            TAG,
            "isLastSummary = $isLastSummary, isOnly = $isOnly, isFirstSummary = $isFirstSummary"
        )
        previous.clearFocus()
        refresh.clearFocus()
        when {
            isOnly -> {
                previous.gone()
                refresh.setImageResource(com.soundrecorder.common.R.drawable.ic_refresh)
                refresh.tag = TAG_REFRESH
            }

            isLastSummary -> {
                previous.visible()
                previous.isEnabled = true
                previous.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_perious)
                refresh.setImageResource(com.soundrecorder.common.R.drawable.ic_refresh)
                refresh.tag = TAG_REFRESH
            }

            isFirstSummary -> {
                previous.visible()
                previous.isEnabled = false
                previous.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_perious_disable)
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
                refresh.tag = TAG_NEXT
            }

            else -> {
                previous.visible()
                previous.isEnabled = true
                previous.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_perious)
                refresh.setImageResource(com.soundrecorder.summary.R.drawable.ic_summary_next)
                refresh.tag = TAG_NEXT
            }
        }
    }

    fun onStartLoading() {
        //startLoadingBgAnim()
        val hasContent = summaryOriginText.isNotEmpty()
        DebugUtil.d(TAG, "onStartLoading hasContent = $hasContent")
        if (hasContent) {
            return
        }
        loading()
        if (copyRight.isVisible.not()) {
            loadingJob?.cancel()
            loadingJob = lifecycle?.launch {
                delay(LOADING_TIPS_DELAY)
                DebugUtil.d(TAG, "loadingJob showLoadingTips")
                showLoadingTips()
            }
        }
    }

    fun onStopLoading() {
        stopLoading()
        stopLoadingBgAnim()
        loadingJob?.cancel()
        loadingJob = null
    }

    private fun loading() {
        if (loadingView.isVisible.not()) {
            loadingView.visible()
            loadingView.playAnimation()
        }
    }

    private fun stopLoading() {
        loadingView.cancelAnimation()
        loadingView.gone()
    }

    fun onInitSummary() {
        reset()
        loadTipsGone()
        content.gone()
        cardContainer.gone()
        copyRight.gone()
        divider.gone()
        toolBar.gone()
        scene.gone()
        tab.gone()
        errorView.gone()
        loadingView.gone()
        updateInitAnim(true)
    }

    fun onStartSummary() {
        reset()
        loadTipsGone()
        content.gone()
        content.reset()
        cardContainer.gone()
        copyRight.gone()
        divider.gone()
        toolBar.gone()
        scene.gone()
        tab.gone()
        errorView.gone()
        content.setTextIsSelectable(false)
        initView.gone()
        onStartLoading()
        updateInitAnim(false)
    }

    private fun stopLoadingBgAnim() {
        if (BaseUtil.isLightOS().not()) {
            kotlin.runCatching {
                val parent = this.parent as? ViewGroup ?: return@runCatching
                bgAnimator?.cancel()
                bgAnimator = ValueAnimator.ofInt(OPAQUE, 0).apply {
                    duration = DURATION
                    interpolator = COUIEaseInterpolator()
                    doOnStart {
                        parent.background?.alpha = OPAQUE
                    }
                    doOnEnd {
                        parent.background?.alpha = 0
                        //shadowDrawable?.stop()
                        parent.background = null
                    }
                    addUpdateListener {
                        parent.background?.alpha = it.animatedValue as Int
                    }
                }
                bgAnimator?.start()
            }.onFailure {
                DebugUtil.e(TAG, "stopLoadingBgAnim ${it.message}")
            }
        }
    }

    private fun reset() {
        summaryContentText = ""
        summaryOriginText = ""
        agent.clear()
        summaryEntity.clear()
        isLoadingFinish = false
        isFinishUploadSummary = false
        currentTheme = SummaryTheme(-1)
        identifiedTheme = SummaryTheme(-1)
    }

    fun updateStream(stream: String, animator: Boolean = true, extra: Map<String, Any>? = null) {
        val isEmpty = stream.isEmpty()
        DebugUtil.d(TAG, "updateStream animator = $animator, isEmpty = $isEmpty")
        if (isEmpty) {
            return
        }
        stopLoading()
        toolBar.gone()
        scene.gone()
        tab.gone()
        divider.gone()
        errorView.gone()
        initView.gone()
        copyRight.gone()
        val isOverSize = (extra?.get(IS_OVER_SIZE) as? Boolean) ?: false
        updateLoadTips(isOverSize)
        content.visible()
        updateContentConstraints()
        val markdown = buildMarkDown()
        val summaryAgent = SummaryDataParser.parseAgent(context, stream)
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context,
            stream,
            emptyList(),
            emptyList(),
            summaryAgent
        )
        val spanned = markdown.toMarkdown(formatStream)
        summaryContentText = formatStream
        summaryOriginText = stream
        if (animator) {
            content.setAnimateText(spanned, false)
            content.setAnimationListener(object : TextAnimationListener {
                override fun onAnimationEnd() {
                    //content.text = spanned
                }

                override fun onAnimationUpdate(curReadyPos: Int) {
                    super.onAnimationUpdate(curReadyPos)
                    smoothScrollToVisibleText(curReadyPos)
                }
            })
        } else {
            content.text = spanned
        }
    }

    private fun getSummaryTitle(): String {
        val summaryTitle = summaryCallback?.getSummaryTitle()
        return when {
            (summaryTitle?.isNotEmpty() == true) -> summaryTitle
            currentRecordTitle.isNotEmpty() -> currentRecordTitle.title() ?: identifiedTitle
            else -> identifiedTitle
        }
    }

    private fun smoothScrollToVisibleText(curReadyPos: Int) {
        val layout = content.layout ?: return
        val startLine = layout.getLineForOffset(0)
        val endLine = layout.getLineForOffset(curReadyPos)
        val height = layout.getLineBottom(endLine) - layout.getLineTop(startLine)
        val offset = context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp12)
        val scrollDistance = height + content.top - measuredHeight
        if (scrollDistance > 0) {
            smoothScrollTo(0, scrollDistance + offset)
        }
    }

    private fun buildMarkDown(): Markwon {
        return markwon ?: run {
            val markDown = Markwon.builder(context)
                .usePlugin(
                    if (agentDrawable != null && agentUnCheckDrawable != null) {
                        TaskListPlugin.create(agentDrawable, agentUnCheckDrawable)
                    } else {
                        TaskListPlugin.create(context)
                    }
                )
                .usePlugin(SoftBreakAddsNewLinePlugin())
                .usePlugin(AgentClickPlugin(context) { isDone, task ->
                    agentClick(isDone, task)
                })
                .usePlugin(ConfigPlugin())
                .applyLinkResolver(SummaryLinkResolver(context) { view, entry, span ->
                    entityClick(view, entry, span)
                })

                .build()
            markwon = markDown
            markDown
        }
    }

    private fun agentClick(isDone: Boolean, task: String) {
        agent.find { it.agent == task }?.isDone = isDone
        summaryFunctionCallback?.onClickAgent(agent)
        summaryContentText = SummaryStyleHelper.updateStyleAfterTaskStateChange(summaryContentText, isDone, task)
        val markdown = buildMarkDown()
        DebugUtil.d(TAG, "agentClick = $summaryContentText")
        val spanned = markdown.toMarkdown(summaryContentText)
        content.text = spanned
    }

    private fun entityClick(view: View, entity: SummaryEntity, span: URLSpan) {
        lifecycle?.launch {
            val popupListWindow = withContext(Dispatchers.Default) {
                summaryEntityHelper.createSummaryEntityPopMenu(entity)
            }
            if (popupListWindow == null) {
                DebugUtil.e(TAG, "linkClick not support")
                return@launch
            }
            val position = summaryEntityHelper.calculateEntitySpanPosition(view, span, popupListWindow) ?: run {
                DebugUtil.e(TAG, "linkClick position is null")
                return@launch
            }
            popupListWindow.show(view, false, position[0], position[1])
        }
    }

    private fun showCopyright() {
        loadingJob?.cancel()
        copyRight.visible()
        val lp = copyRight.layoutParams as? ConstraintLayout.LayoutParams
        lp?.topToBottom = com.soundrecorder.summary.R.id.summary_content

        // 使用AITextStyleUtil统一处理AI文本样式
        AITextStyleUtil.setAITextStyle(
            copyRight,
            context,
            com.soundrecorder.base.R.string.summary_copyright,
            com.soundrecorder.summary.R.string.summary_oppo_ai,
        )
    }

    fun onTaskAlreadyRun(stream: String, extra: Map<String, Any>? = null) {
        DebugUtil.i(TAG, "onTaskAlreadyRun ${stream.isEmpty()}")
        if (stream.isEmpty()) {
            onStartSummary()
            return
        }
        //startLoadingBgAnim()
        updateStream(stream, animator = false, extra)
    }

    fun onFinishSummary(summaryModel: SummaryModel, animator: Boolean = true) {
        stopLoading()
        content.visible()
        updateContentConstraints()
        updateEmptyAnim(false)
        val isOverSize = (summaryModel.extra?.get(IS_OVER_SIZE) as? Boolean) ?: false
        updateLoadTips(isOverSize)
        agent.clear()
        agent.addAll(summaryModel.agentEvents)
        summaryEntity.clear()
        summaryEntity.addAll(summaryModel.entities.filter { it.supportEntity() })
        DebugUtil.d(TAG, "onFinishSummary animator = $animator, ${summaryModel.agentEvents}, $summaryEntity")
        val markdown = buildMarkDown()
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context = context,
            originText = summaryModel.summary,
            trace = emptyList(),
            entities = summaryEntity,
            agents = summaryModel.agentEvents
        )
        DebugUtil.i(TAG, "formatStream : $formatStream")
        val spanned = markdown.toMarkdown(formatStream)
        summaryContentText = formatStream
        summaryOriginText = summaryModel.summary
        val loadFinish = {
            content.text = spanned
            divider.visible()
            toolBar.visible()
            scene.visible()
            tab.visible()
            isLoadingFinish = true
            setContentSelectable()
            showCopyright()
            stopLoadingBgAnim()
            scrollToTop()
            content.cancelAnimation()
            summaryFunctionCallback?.onFinishAnimator()
        }
        if (animator) {
            content.setAnimateText(spanned, false)
            content.setAnimationListener(object : TextAnimationListener {
                override fun onAnimationEnd() {
                    DebugUtil.d(TAG, "onFinishSummary onAnimationEnd")
                    loadFinish.invoke()
                }

                override fun onAnimationUpdate(curReadyPos: Int) {
                    super.onAnimationUpdate(curReadyPos)
                    smoothScrollToVisibleText(curReadyPos)
                }
            })
        } else {
            loadFinish.invoke()
        }
        updateScene(summaryModel.theme)
        isFinishUploadSummary = true
    }

    private fun updateLoadTips(isOverSize: Boolean) {
        DebugUtil.i(TAG, "updateLoadTips isOverSize = $isOverSize")
        val needShowLoadTips = isOverSize
        val msg = when {
            isOverSize -> resources.getString(com.soundrecorder.common.R.string.summary_tips_content_over_size)
            else -> ""
        }
        loadTip.setTipsText(msg)
        when {
            needShowLoadTips && loadTip.isVisible.not() -> loadTipsVisible()
            needShowLoadTips.not() && loadTip.isVisible -> loadTipsGone()
            else -> DebugUtil.d(TAG, "do nothing")
        }
    }

    private fun loadTipsVisible() {
        val constraintSet = ConstraintSet()
        constraintSet.clone(container)
        constraintSet.clear(loadTip.id, ConstraintSet.TOP)
        constraintSet.connect(loadTip.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, 0)
        constraintSet.clear(content.id, ConstraintSet.TOP)
        constraintSet.connect(content.id, ConstraintSet.TOP, loadTip.id, ConstraintSet.BOTTOM, contentMarginLoadTip)
        constraintSet.applyTo(container)
        loadTip.visible()
    }

    private fun loadTipsGone() {
        val constraintSet = ConstraintSet()
        constraintSet.clone(container)
        constraintSet.clear(loadTip.id, ConstraintSet.TOP)
        constraintSet.clear(content.id, ConstraintSet.TOP)
        constraintSet.connect(content.id, ConstraintSet.TOP, ConstraintSet.PARENT_ID, ConstraintSet.TOP, contentMarginLoadTip)
        constraintSet.applyTo(container)
        loadTip.gone()
    }

    private fun scrollToTop() {
        if (getChildAt(0).height > height) {
            /*
            val run = Runnable {
                DebugUtil.d(TAG, "scrollToTop $scrollY")
                scrollTo(0, 0)
            }
            postDelayed(run ,DELAY_SCROLL)
             */
            scrollTo(0, 0)
        }
    }

    fun onError(canRetry: Boolean, errorMsg: String) {
        DebugUtil.d(TAG, "onError canRetry")
        onStopLoading()
        loadTipsGone()
        toolBar.gone()
        divider.gone()
        errorView.gone()
        copyRight.gone()
        content.gone()
        content.cancelAnimation()
        initView.gone()
        updateEmptyAnim(true)
        errorMsgText.text = errorMsg
        if (canRetry) {
            retry.visible()
        } else {
            retry.gone()
        }
        isLoadingFinish = true
        isFinishUploadSummary = true
    }

    fun onRetryError(errorMsg: String) {
        ToastManager.showShortToast(context, errorMsg)
        isFinishUploadSummary = true
    }

    fun onStop(stream: String, extra: Map<String, Any>? = null) {
        DebugUtil.d(TAG, "onStop stream ${stream.isEmpty()}")
        if (stream.isEmpty()) {
            return
        }
        onStopLoading()
        loadTipsGone()
        content.visible()
        val markdown = buildMarkDown()
        val summaryAgent = SummaryDataParser.parseAgent(context, stream)
        val formatStream = SummaryStyleHelper.formatSummaryContent(
            context,
            stream,
            emptyList(),
            emptyList(),
            summaryAgent
        )
        val spanned = markdown.toMarkdown(formatStream)
        content.text = spanned
        summaryContentText = formatStream
        summaryOriginText = stream

        divider.visible()
        toolBar.visible()
        showCopyright()
        scrollToTop()

        val theme = extra?.get(THEME_CODE) as? SummaryTheme
        updateScene(theme)

        isLoadingFinish = true
        isFinishUploadSummary = true
        setContentSelectable()
    }

    fun updateSummaryTrace(summaryTrace: List<SummaryTrace>) {
        DebugUtil.d(TAG, "updateSummaryTrace summaryTrace = $summaryTrace:")
    }

    fun setSummaryCallback(callback: IAISummaryCallback) {
        summaryCallback = callback
    }

    fun isSummaryAnimator(): Boolean {
        return content.isAnimating
    }

    private fun updateEmptyAnim(isEmpty: Boolean) {
        if (isEmpty) {
            errorView.visible()
            container.viewTreeObserver.addOnGlobalLayoutListener(this)
            errorIconView.initImageResource()
        } else {
            errorView.gone()
            container.viewTreeObserver.removeOnGlobalLayoutListener(this)
        }
    }

    private fun updateInitAnim(isInit: Boolean) {
        DebugUtil.d(TAG, "updateInitAnim isInit = $isInit")
        if (isInit) {
            initView.visible()
            container.viewTreeObserver.addOnGlobalLayoutListener(this)
            initIconView.initImageResource()
        } else {
            initView.gone()
            container.viewTreeObserver.removeOnGlobalLayoutListener(this)
        }
    }

    fun stopAnimator() {
        onStopLoading()
        content.cancelAnimation()
    }

    private fun updateScene(theme: SummaryTheme?) {
        DebugUtil.d(TAG, "updateScene theme = $theme")
        identifiedTheme = theme ?: SummaryTheme(SummaryTheme.NORMAL)
        currentTheme = when {
            ((theme?.style ?: -1) == -1) -> SummaryTheme(SummaryTheme.NORMAL)
            else -> theme
        }
        lifecycle?.launch {
            val supportTheme = withContext(Dispatchers.Default) {
                val themeInfo = PrefUtil.getString(BaseApplication.getAppContext(), SUMMARY_SUPPORT_THEME, "")
                SummaryDataParser.parseThemeSupport(themeInfo)
            }
            supportThemes.clear()
            supportThemes.addAll(supportTheme.filter { it.isSupportCode() })
            withContext(Dispatchers.Main) {
                updateSceneIcons()
            }
        }
    }

    private fun getThemeTitle(theme: SummaryTheme?): String {
        theme ?: return SummaryTheme.getTitle(context, SummaryTheme.NORMAL)
        when {
            theme.style == SummaryTheme.NORMAL -> return SummaryTheme.getTitle(context, SummaryTheme.NORMAL)

            theme.style == SummaryTheme.NORMAL_SIMPLE -> return SummaryTheme.getTitle(context, SummaryTheme.NORMAL_SIMPLE)

            else -> {
                val localLanguage = LanguageUtil.getLocalLanguage()
                val names = supportThemes.find { it.code == theme.style }?.name
                    ?: return SummaryTheme.getTitle(context, theme.style)
                return names[localLanguage] ?: SummaryTheme.getTitle(context, theme.style)
            }
        }
    }

    private fun getOriginSummaryText(): String {
        val summary = summaryOriginText.replace("*", "").replace("#", "")
        return summary
    }

    private fun setScrollState(newState: Int) {
        if (currentScrollState != newState) {
            summaryFunctionCallback?.onScrollStateChanged(newState)
        }
        currentScrollState = newState
    }

    override fun onScrollChanged(x: Int, y: Int, oldX: Int, oldY: Int) {
        super.onScrollChanged(x, y, oldX, oldY)
        // 计算滚动距离
        val dx = x - oldX
        val dy = y - oldY
        lastScrollY = y
        // 回调滚动距离变化
        summaryFunctionCallback?.onScrolled(dx, dy)

        if (currentScrollState == Constants.SCROLL_STATE_SETTLING) {
            checkSettlingState()
        }
    }

    private fun checkSettlingState() {
        // 移除之前的检测任务
        removeCallbacks(scrollStateChecker)
        // 检查是否停止滚动
        postDelayed(scrollStateChecker, SCROLLER_CHECK_TIME)
    }

    private val scrollStateChecker = Runnable {
        val currentY = scrollY
        if (lastScrollY == currentY) {
            setScrollState(Constants.SCROLL_STATE_IDLE)
        } else {
            lastScrollY = currentY
            checkSettlingState()
        }
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        gestureDetector?.onTouchEvent(ev)
        when (ev.action) {
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> setScrollState(Constants.SCROLL_STATE_SETTLING)
        }
        return super.onTouchEvent(ev)
    }

    override fun onGlobalLayout() {
        container.viewTreeObserver.removeOnGlobalLayoutListener(this)
        if (errorView.isVisible) {
            errorIconView.setScaleByEmptySize(activity.px2dp(container.width).toInt(), activity.px2dp(container.height).toInt(), "onGlobalLayout")
        }
        if (initView.isVisible) {
            initIconView.setScaleByEmptySize(activity.px2dp(container.width).toInt(), activity.px2dp(container.height).toInt(), "onGlobalLayout")
        }
    }

    fun setPaddingBottom(paddingBottom: Int) {
        toolBar.updateLayoutParams {
            height = toolbarDefaultHeight + paddingBottom
        }
    }
}