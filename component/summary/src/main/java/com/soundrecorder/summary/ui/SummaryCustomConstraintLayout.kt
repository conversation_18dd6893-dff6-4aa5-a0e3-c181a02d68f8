/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryCustomConstraintLayout
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/28
 * * Author      : w9099028
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui

import android.content.Context
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import com.soundrecorder.base.utils.DebugUtil

class SummaryCustomConstraintLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var heightCallback: ((height: Int) -> Unit)? = null
    private var lastHeight = 0

    /**
     * 注册高度变化监听
     */
    fun observeHeightChange(callback: (height: Int) -> Unit) {
        if (lastHeight > 0) {
            callback(lastHeight)
        }
    }

    /**
     * 取消高度监听
     */
    fun unObserveHeightChange() {
        this.heightCallback = null
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (lastHeight != h) {
            DebugUtil.d("CustomConstraintLayout", "Height changed: $h (old: $oldh)")
            heightCallback?.invoke(h)
            lastHeight = h
        }
    }

    /**
     * 获取最后记录的高度
     */
    fun getLastHeight(): Int = lastHeight
}