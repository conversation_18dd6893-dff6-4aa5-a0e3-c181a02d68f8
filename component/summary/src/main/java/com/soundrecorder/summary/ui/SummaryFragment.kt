/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryFragment
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/06
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.textview.COUITextView
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.DensityUtil.px2dp
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.drawable.SweepGradientWithShapeLayer
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.gone
import com.soundrecorder.common.utils.visible
import com.soundrecorder.common.widget.OSImageView
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate.Companion.POLICY_TYPE_COMMON
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_SUMMARY_MIND
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyInterface
import com.soundrecorder.modulerouter.summary.BUNDLE_MEDIA_ID
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_CALL_NAME
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_FILE_DURATION
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_FILE_PATH
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_MODIFY_TIME
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_TITLE
import com.soundrecorder.modulerouter.summary.BUNDLE_RECORD_TYPE
import com.soundrecorder.modulerouter.summary.IAISummaryCallback
import com.soundrecorder.modulerouter.summary.IAISummaryInterface
import com.soundrecorder.modulerouter.summary.IImmersiveCallback
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.summary.databinding.LayoutSummaryFragmentBinding
import com.soundrecorder.summary.model.AISummaryViewModel
import com.soundrecorder.summary.model.SummaryAgentEvent
import com.soundrecorder.summary.model.SummaryTheme
import com.soundrecorder.summary.request.AISummaryProcess.Companion.TIME
import com.soundrecorder.summary.request.database.SummaryCacheDao.Companion.MAX_COUNT
import com.soundrecorder.summary.ui.content.SummaryContentView
import com.soundrecorder.summary.ui.content.callback.ISummaryFunctionCallback
import com.soundrecorder.summary.util.AIUnitApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class SummaryFragment : Fragment(), IAISummaryInterface {
    companion object {
        private const val TAG = "SummaryFragment"
    }

    private var binding: LayoutSummaryFragmentBinding? = null
    private var viewModel: AISummaryViewModel? = null
    private var summaryContentView: SummaryContentView? = null
    private var summaryStopLayout: LinearLayout? = null
    private var topDivider: View? = null

    private var mediaId: Long = 0L
    private var recordType: Int = 0
    private var recordCreateTime: Long = 0L
    private var recordTitle: String = ""
    private var recordFilePath: String = ""
    private var recordDuration: Long = 0
    private var recordCallerName: String = ""
    private var isSelected = false
    private var warningDialog: AlertDialog? = null
    private var currentChooseTheme: SummaryTheme? = null
    private var summaryCallback: IAISummaryCallback? = null
    private var immersiveCallback: IImmersiveCallback? = null
    private var insetsBottomValue = 0
    private var paddingBottomValue = 0
    private var alreadyEnter = false
    private var initView: View? = null
    private var initIconView: OSImageView? = null
    private var summaryStart: COUITextView? = null

    private var recoveryDetector: CheckUpdateObserver? = null

    private val privacyAction by lazy {
        Injector.injectFactory<PrivacyPolicyInterface>()
    }
    private val cloudTipManager by lazy {
        Injector.injectFactory<CloudTipManagerAction>()
    }
    private val selectedBg by lazy {
        SweepGradientWithShapeLayer(
            requireContext(),
            requireContext().resources.getDimension(com.soundrecorder.common.R.dimen.dp40),
            ContextCompat.getColor(requireContext(), com.soundrecorder.summary.R.color.start_button_bg_color)
        )
    }
    private val callback: ISummaryFunctionCallback by lazy {
        object : ISummaryFunctionCallback {
            override fun onClickAgent(agents: List<SummaryAgentEvent>) {
                DebugUtil.i(TAG, "onClickAgent $agents")
                viewModel?.updateSummaryAgent(agents)
            }

            override fun onClickRefresh() {
                DebugUtil.i(TAG, "onClickRefresh")
                viewModel?.regenerateSummary(true)
            }

            override fun onClickRetry() {
                DebugUtil.i(TAG, "onClickRetry")
                viewModel?.regenerateSummary()
            }

            override fun onClickPrevious() {
                DebugUtil.i(TAG, "onClickPrevious")
                viewModel?.switchToPreviousSummary()
            }

            override fun onClickSummaryStart() {
                DebugUtil.i(TAG, "onClickSummaryStart")
                checkSummaryAvailable { success ->
                    if (success) {
                        viewModel?.startSummary()
                    } else {
                        DebugUtil.i(TAG, "checkSummaryAvailable failed")
                    }
                }
            }

            override fun onClickScene(theme: SummaryTheme?) {
                DebugUtil.i(TAG, "onClickScene theme ${theme != null}")
                theme ?: return
                currentChooseTheme = theme
                summaryContentView?.onStartLoading()
                viewModel?.regenerateSummary(true, theme = theme)
            }

            override fun onClickNext() {
                DebugUtil.i(TAG, "onClickNext")
                viewModel?.switchToNextSummary()
            }

            override fun onFinishAnimator() {
                DebugUtil.d(TAG, "onFinishAnimator")
                super.onFinishAnimator()
                //效果是文字上屏动画结束之后，才把暂停按钮消失，所以不能是finish的状态直接就处理，需要等view回调回来处理
                summaryCallback?.onSummaryEnd()
                summaryStopGone()
            }

            override fun onScrolled(dx: Int, dy: Int) {
                updateDividerVisible()
                if (summaryContentView?.currentScrollState == Constants.SCROLL_STATE_DRAGGING) {
                    immersiveCallback?.updateIsImmersive(dx, dy)
                }
            }
        }
    }

    override fun getFragment(): Fragment {
        return this
    }

    override fun summaryFragmentSelected() {
        DebugUtil.d(TAG, "summaryFragmentSelected alreadyEnter = $alreadyEnter")
        isSelected = true
        if (alreadyEnter.not()) {
            loadSummary(activity)
        }
        alreadyEnter = true
    }

    override fun summaryFragmentUnSelected() {
        DebugUtil.d(TAG, "summaryFragmentUnSelected")
        isSelected = false
    }

    override fun summaryFragmentScrollEnd(select: Boolean) {
        DebugUtil.d(TAG, "summaryFragmentScrollEnd select = $select")
        if (select) {
            summaryStopVisible()
        } else {
            summaryStopGone()
        }
    }

    override fun summaryFragmentScroll(positionOffset: Float) {
        if (summaryStopLayout?.isVisible == true) {
            summaryStopLayout?.alpha = positionOffset
        }
    }

    override fun isSummaryRunning(): Boolean {
        /*
        1，loading中（三个点的动画），
        2，流式返回中，
        3，数据已经完了，但是还在播放动画中
        这三个条件都属于要正在summary，需要界面做反馈
         */
        return viewModel?.isStartLoading() == true || viewModel?.isRunning() == true || summaryContentView?.isSummaryAnimator() == true
    }

    override fun setSummaryCallback(callback: IAISummaryCallback) {
        summaryCallback = callback
        summaryContentView?.setSummaryCallback(callback)
    }

    override fun setImmersiveCallback(callback: IImmersiveCallback) {
        immersiveCallback = callback
    }

    override fun setBottomMargin(marginBottom: Int) {
        this.insetsBottomValue = marginBottom
        summaryStopVisible()
    }

    override fun setPaddingBottom(paddingBottom: Int) {
        this.paddingBottomValue = paddingBottom
        if (summaryContentView?.isSummaryAnimator() != true) {
            summaryStopGone()
            summaryContentView?.setPaddingBottom(paddingBottom)
        }
    }

    override fun onRecordNameStatusChange(display: Boolean?, resultName: String?) {
        DebugUtil.i(TAG, "onRecordNameStatusChange resultName = $resultName")
        arguments?.putString(BUNDLE_RECORD_TITLE, resultName)
    }

    private fun summaryStopVisible() {
        val root = binding?.root ?: return
        val summaryStopLayout = this.summaryStopLayout ?: return
        val summaryContentView = this.summaryContentView ?: return
        if (summaryContentView.isSummaryAnimator()) {
            summaryStopLayout.alpha = 1f
            val constraintSet = ConstraintSet()
            constraintSet.clone(root)
            constraintSet.clear(summaryStopLayout.id, ConstraintSet.TOP)
            constraintSet.clear(summaryStopLayout.id, ConstraintSet.BOTTOM)
            constraintSet.clear(summaryContentView.id, ConstraintSet.BOTTOM)
            constraintSet.connect(summaryStopLayout.id, ConstraintSet.TOP, summaryContentView.id, ConstraintSet.BOTTOM, 0)
            constraintSet.connect(summaryStopLayout.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, insetsBottomValue)
            constraintSet.connect(summaryContentView.id, ConstraintSet.BOTTOM, summaryStopLayout.id, ConstraintSet.TOP, 0)
            constraintSet.applyTo(root)
            summaryStopLayout.visible()
        }
    }

    private fun summaryStopGone() {
        val viewModel = viewModel ?: return
        summaryStopLayout?.gone()
        val root = binding?.root ?: return
        val summaryContentView = this.summaryContentView ?: return
        val constraintSet = ConstraintSet()
        constraintSet.clone(root)
        constraintSet.clear(summaryContentView.id, ConstraintSet.BOTTOM)
        val margin = if (viewModel.isStartLoading()) {
            insetsBottomValue
        } else {
            0
        }
        constraintSet.connect(summaryContentView.id, ConstraintSet.BOTTOM, ConstraintSet.PARENT_ID, ConstraintSet.BOTTOM, margin)
        constraintSet.applyTo(root)
    }

    override fun onResume() {
        super.onResume()
        DebugUtil.i(TAG, "onResume")
    }

    override fun onAttach(context: Context) {
        DebugUtil.d(TAG, "onAttach")
        super.onAttach(context)
        initBundleData()
    }

    private fun loadSummary(activity: Activity?) {
        activity ?: return
        viewModel?.loadSummary(
            mediaId = mediaId,
            recordType = recordType,
            recordTime = recordCreateTime,
            duration = recordDuration,
            callName = recordCallerName
        )
    }

    private fun initBundleData() {
        val bundle = arguments ?: return
        mediaId = bundle.getLong(BUNDLE_MEDIA_ID)
        recordType = bundle.getInt(BUNDLE_RECORD_TYPE)
        recordCreateTime = bundle.getLong(BUNDLE_RECORD_MODIFY_TIME)
        recordTitle = bundle.getString(BUNDLE_RECORD_TITLE, "")
        recordFilePath = bundle.getString(BUNDLE_RECORD_FILE_PATH, "")
        recordDuration = bundle.getLong(BUNDLE_RECORD_FILE_DURATION, 0)
        recordCallerName = bundle.getString(BUNDLE_RECORD_CALL_NAME, "")
        DebugUtil.e(TAG, "initBundleData mediaId:$mediaId," +
                "recordType:$recordType," +
                "recordCreateTime:$recordCreateTime," +
                "recordDuration = $recordDuration, " +
                "recordCallerName = $recordCallerName"
        )
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        DebugUtil.d(TAG, "onCreateView")
        val binding = LayoutSummaryFragmentBinding.bind(
            inflater.inflate(
                com.soundrecorder.summary.R.layout.layout_summary_fragment,
                container,
                false
            )
        )
        this.binding = binding
        viewModel = ViewModelProvider(this)[AISummaryViewModel::class.java]
        recoveryDetector = CheckUpdateObserver(this.lifecycle)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        DebugUtil.d(TAG, "onViewCreated")
        super.onViewCreated(view, savedInstanceState)
        initView()
        initObserver()
        loadSummaryPreselect()
    }

    private fun initView() {
        summaryContentView = binding?.root?.findViewById(com.soundrecorder.summary.R.id.summary_content_view)
        summaryContentView?.setSummaryFunctionCallback(callback)
        // 设置录音文件信息，确保导出功能能正确获取录音文件信息
        summaryContentView?.setRecordInfo(mediaId, recordTitle, recordFilePath, recordDuration)
        summaryContentView?.setChildFragmentManager(childFragmentManager)
        initStopView()
        initDivider()
    }

    private fun initStopView() {
        summaryStopLayout = binding?.root?.findViewById(com.soundrecorder.summary.R.id.stop_layout)
        val summaryStopIcon = binding?.root?.findViewById<AppCompatImageView>(com.soundrecorder.summary.R.id.summary_stop)
        summaryStopIcon?.setOnClickListener {
            if (ClickUtils.isQuickClick()) {
                return@setOnClickListener
            }
            val summaryState = viewModel?.summaryState?.value
            DebugUtil.i(TAG, "stopSummary summaryState = $summaryState")
            when (summaryState) {
                AISummaryViewModel.SummaryState.START,
                AISummaryViewModel.SummaryState.TASK_ALREADY_RUN,
                AISummaryViewModel.SummaryState.WAIT,
                AISummaryViewModel.SummaryState.STREAM -> {
                    summaryContentView?.stopAnimator()
                    viewModel?.stopSummary(context)
                }

                AISummaryViewModel.SummaryState.FINISH,
                AISummaryViewModel.SummaryState.STOPPED -> {
                    currentChooseTheme = null
                    if (summaryContentView?.isSummaryAnimator() == true) {
                        summaryContentView?.stopAnimator()
                    } else {
                        summaryStopLayout?.gone()
                        summaryCallback?.onSummaryEnd()
                    }
                }

                else -> {
                    DebugUtil.i(TAG, "summaryState = $summaryState")
                    summaryStopLayout?.gone()
                    summaryCallback?.onSummaryEnd()
                }
            }
        }
        summaryStopGone()
    }

    private fun loadSummaryPreselect() {
        val currentSummaryState = viewModel?.summaryState ?: AISummaryViewModel.SummaryState.EMPTY
        if (isSelected && currentSummaryState != AISummaryViewModel.SummaryState.FINISH) {
            DebugUtil.d(TAG, "loadSummaryPreselect isSelect:$isSelected current:$currentSummaryState")
            loadSummary(activity)
        }
    }

    private fun initDivider() {
        topDivider = binding?.root?.findViewById(com.soundrecorder.summary.R.id.top_divider_line)
    }

    private fun updateDividerVisible() {
        summaryContentView?.apply {
            if (this.scrollY > this.paddingTop) {
                topDivider.visible()
            } else {
                topDivider.gone()
            }
        }
    }

    private fun initObserver() {
        initSummaryStreamObserver()
        initSummaryFullModelObserver()
        initSummaryStateObserver()
        initSummaryCountObserver()
        initSummaryRetryObserver()
    }

    private fun initSummaryStreamObserver() {
        viewModel?.summaryStream?.observe(viewLifecycleOwner) {
            summaryContentView?.updateStream(it.stream, animator = true, it.extra)
        }
    }

    private fun initSummaryFullModelObserver() {
        viewModel?.summaryFullModel?.observe(viewLifecycleOwner) {
            summaryContentView?.setSummaryContent(it)
            summaryStopGone()
        }
    }
    private fun addInitView() {
        if (initView?.parent != null) return
        initView = createInitView().also { view ->
            binding?.root?.apply {
                addView(view)
                observeHeightChange { height ->
                    initIconView?.apply {
                        setScaleByEmptySize(
                            activity?.px2dp(binding?.root?.width ?: 0)?.toInt() ?: 0,
                            activity?.px2dp(height)?.toInt() ?: 0,
                            "heightChange"
                        )
                        initImageResource()
                    }
                }
            }
        }
    }

    private fun removeInitView() {
        (initView?.parent as? ViewGroup)?.removeView(initView)
        initView = null
    }

    private fun createInitView(): View {
        return LayoutInflater.from(context).inflate(
            com.soundrecorder.summary.R.layout.layout_summary_init,
            null,
            false
        ).apply {
            layoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                ConstraintLayout.LayoutParams.WRAP_CONTENT
            ).apply {
                startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
            }
            initIconView = findViewById(com.soundrecorder.summary.R.id.init_logo)
            summaryStart = findViewById(com.soundrecorder.summary.R.id.tv_start_summary)
            summaryStart?.background = selectedBg
            summaryStart?.setOnClickListener {
                if (!ClickUtils.isQuickClick()) {
                    checkSummaryAvailable { success ->
                        if (success) {
                            viewModel?.startSummary()
                        } else {
                            DebugUtil.i(TAG, "checkSummaryAvailable failed")
                        }
                    }
                }
            }
        }
    }
    private fun initSummaryStateObserver() {
        fun loadInit() {
            addInitView()
            summaryContentView?.onInitSummary()
            summaryStopGone()
            summaryCallback?.onSummaryStart()
        }

        fun loadStart() {
            removeInitView()
            summaryContentView?.onStartSummary()
            summaryStopGone()
            summaryCallback?.onSummaryStart()
        }

        fun loadWait() {
            removeInitView()
            summaryContentView?.onStartSummary()
            summaryStopGone()
            summaryCallback?.onSummaryStart()
        }

        fun loadStream() {
            removeInitView()
            summaryCallback?.onSummaryStart()
            summaryStopVisible()
        }

        fun loadTaskAlreadyRun() {
            viewModel?.currentStream?.let { currentStream ->
                summaryContentView?.onTaskAlreadyRun(currentStream.stream, extra = currentStream.extra)
            }
            summaryCallback?.onSummaryStart()
            summaryStopVisible()
        }

        fun loadFinish() {
            //如果是从后台切回来的，就不需要做动画了
            val isRecoveryUpdate = recoveryDetector?.checkAndReset() ?: false
            DebugUtil.d(TAG, "loadFinish isRecoveryUpdate = $isRecoveryUpdate")
            val summaryModel = viewModel?.summaryFinishModel ?: return
            removeInitView()
            summaryContentView?.onFinishSummary(summaryModel, isRecoveryUpdate.not())
        }

        fun loadError() {
            val summaryError = viewModel?.summaryError ?: return
            removeInitView()
            val msg = requireContext().getString(summaryError.errorMsgRes)
            summaryContentView?.onError(summaryError.canRetry, msg)
            summaryCallback?.onSummaryEnd()
            summaryStopGone()
        }

        fun loadUserStop() {
            val currentStream = viewModel?.summaryStream?.value ?: return
            val time = currentStream.extra?.get(TIME) as? Long ?: System.currentTimeMillis()
            viewModel?.checkSummaryCount(time)
            summaryContentView?.onStop(currentStream.stream, currentStream.extra)

            summaryCallback?.onSummaryEnd()
            summaryStopGone()
        }

        viewModel?.summaryState?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initSummaryStateObserver = $it")
            when (it) {
                AISummaryViewModel.SummaryState.INIT -> loadInit()
                AISummaryViewModel.SummaryState.START -> loadStart()
                AISummaryViewModel.SummaryState.WAIT -> loadWait()
                AISummaryViewModel.SummaryState.STREAM -> loadStream()
                AISummaryViewModel.SummaryState.TASK_ALREADY_RUN -> loadTaskAlreadyRun()
                AISummaryViewModel.SummaryState.OVER_TIME -> showWarningOverSummaryTimes()
                AISummaryViewModel.SummaryState.FINISH -> loadFinish()
                AISummaryViewModel.SummaryState.ERROR -> loadError()
                AISummaryViewModel.SummaryState.STOPPED -> loadUserStop()
                else -> DebugUtil.e(TAG, "initSummaryStateObserver other it = $it")
            }
        }
    }

    private fun initSummaryCountObserver() {
        viewModel?.summaryCount?.observe(viewLifecycleOwner) {
            DebugUtil.d(TAG, "initSummaryCountObserver = $it")
            val isLastSummary = it.currentPosition == it.count
            val isOnly = it.count <= 1
            val isFirstSummary = it.currentPosition == 1
            summaryContentView?.checkCurrentState(isLastSummary, isOnly, isFirstSummary)
        }
    }


    private fun initSummaryRetryObserver() {
        viewModel?.summaryRetryError?.observe(viewLifecycleOwner) { state ->
            if (state.not()) {
                return@observe
            }
            DebugUtil.d(TAG, "initSummaryRetryObserver = $state")
            val summaryError = viewModel?.summaryError ?: return@observe
            val msg = requireContext().getString(summaryError.errorMsgRes)
            summaryContentView?.onRetryError(msg)
            summaryCallback?.onSummaryEnd()
            summaryStopGone()
            viewModel?.summaryRetryError?.value = false
        }
    }

    private fun showWarningOverSummaryTimes() {
        val message = requireActivity().resources.getQuantityString(
            com.soundrecorder.common.R.plurals.summary_refresh_notice_dialog_msg,
            MAX_COUNT,
            MAX_COUNT
        )
        warningDialog = COUIAlertDialogBuilder(requireActivity())
            .setTitle(com.soundrecorder.common.R.string.summary_refresh_notice_dialog_title)
            .setMessage(message)
            .setPositiveButton(com.soundrecorder.common.R.string.summary_refresh_notice_dialog_yes) { dialog, _ ->
                if (currentChooseTheme != null) {
                    viewModel?.regenerateSummary(false, currentChooseTheme)
                } else {
                    viewModel?.regenerateSummary(false)
                }
                dialog.dismiss()
            }.setNeutralButton(com.soundrecorder.common.R.string.summary_refresh_notice_dialog_no) { dialog, _ ->
                dialog.dismiss()
                summaryContentView?.onCancelWarningDialog()
                currentChooseTheme = null
            }.show()
    }

    override fun onStop() {
        super.onStop()
        DebugUtil.i(TAG, "onStop")
        //退出界面保存数据库
        viewModel?.updateSummaryAgentDB()
    }

    private fun checkCloudLoginAndProceed(activity: FragmentActivity, callback: (Boolean) -> Unit) {
        lifecycleScope.launch(Dispatchers.IO) {
            if (false == cloudTipManager?.isLoginFromCache()) {
                lifecycleScope.launch(Dispatchers.Main) {
                    COUIAlertDialogBuilder(activity)
                        .setTitle(com.soundrecorder.common.R.string.sound_record_user_login_first)
                        .setMessage(com.soundrecorder.common.R.string.sound_record_user_login_des)
                        .setPositiveButton(com.soundrecorder.common.R.string.sound_record_user_to_login) { dialog, _ ->
                            dialog.dismiss()
                            lifecycleScope.launch(Dispatchers.IO) {
                                cloudTipManager?.accountRequestLogin(BaseApplication.getAppContext()) { loginSuccess ->
                                    if (loginSuccess) {
                                        lifecycleScope.launch(Dispatchers.Main) {
                                            startPrivacyGuideAndProceed(activity, callback)
                                        }
                                    } else {
                                        lifecycleScope.launch(Dispatchers.Main) {
                                            callback.invoke(false)
                                        }
                                    }
                                }
                            }
                        }
                        .setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
                        .setCancelable(false)
                        .show()
                }
            } else {
                lifecycleScope.launch(Dispatchers.Main) {
                    startPrivacyGuideAndProceed(activity, callback)
                }
            }
        }
    }

    private fun startPrivacyGuideAndProceed(activity: FragmentActivity, callback: (Boolean) -> Unit) {
        AIUnitApi.startPrivacyGuide(activity) {
            DebugUtil.d(TAG, "startPrivacyGuide result: $it")
            if (it) {
                AIUnitApi.handleAiSummaryPlugin(activity, callback)
            } else {
                callback.invoke(false)
            }
        }
    }

    private fun checkSummaryAvailable(callback: (Boolean) -> Unit) {
        val fragmentActivity = activity as? AppCompatActivity
        if (fragmentActivity == null) {
            DebugUtil.i(TAG, "checkSummaryAvailable invalid activity")
            callback.invoke(false)
            return
        }

        val hasSummaryFunction = PermissionUtils.hasFuncTypePermission(TYPE_PERMISSION_SUMMARY_MIND)
        DebugUtil.i(TAG, "checkSummaryAvailable hasSummaryFunction: $hasSummaryFunction")
        if (hasSummaryFunction) {
            checkCloudLoginAndProceed(fragmentActivity, callback)
        } else {
            val privacyDelegate = privacyAction?.newPrivacyPolicyDelegate(
                fragmentActivity,
                POLICY_TYPE_COMMON,
                resultListener = object : IPrivacyPolicyResultListener {
                    override fun onPrivacyPolicySuccess(type: Int, pageFrom: Int?) {
                        checkCloudLoginAndProceed(fragmentActivity, callback)
                    }

                    override fun onPrivacyPolicyFail(type: Int, pageFrom: Int?) {
                        callback.invoke(false)
                    }
                }
            )
            if (privacyDelegate == null) {
                callback.invoke(false)
                return
            }
            privacyDelegate.resumeShowDialog(TYPE_PERMISSION_SUMMARY_MIND, true)
        }
    }

    override fun onDetach() {
        super.onDetach()
        warningDialog?.dismiss()
        warningDialog = null
    }

    override fun onDestroyView() {
        initIconView?.release()
        binding?.root?.unObserveHeightChange()
        (initView?.parent as? ViewGroup)?.removeView(initView)
        initView = null
        initIconView = null
        summaryStart = null
        super.onDestroyView()
    }

    override fun onDestroy() {
        super.onDestroy()
        currentChooseTheme = null
    }
}