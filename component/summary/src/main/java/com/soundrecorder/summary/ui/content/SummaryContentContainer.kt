/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryContentContainer
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.content.Context
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.children
import androidx.core.view.isVisible
import androidx.core.view.updatePaddingRelative
import com.soundrecorder.base.utils.DebugUtil

class SummaryContentContainer @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "SummaryContentContainer"
        private const val ANCHOR_ARGS = 0.45
    }

    private var errorView: ConstraintLayout? = null

    override fun onFinishInflate() {
        super.onFinishInflate()
        errorView = findViewById(com.soundrecorder.summary.R.id.layout_error)
        initPadding()
    }

    private fun initPadding() {
        val paddingStart =
            context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp28)
        updatePaddingRelative(paddingStart, 0, paddingStart, 0)
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        errorView?.let {
            if (it.isVisible) {
                val itLeft = (measuredWidth - it.measuredWidth) / 2
                val itTop = (measuredHeight * ANCHOR_ARGS - it.measuredHeight / 2).toInt()
                it.layout(itLeft, itTop, itLeft + it.measuredWidth, itTop + it.measuredHeight)
            }
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        measureChildren(widthMeasureSpec, heightMeasureSpec)
        var totalHeight = 0
        children.forEach {
            if (it.isVisible) {
                val childHeight = it.measuredHeight
                val topMargin = (it.layoutParams as? LayoutParams)?.topMargin ?: 0
                val bottomMargin = (it.layoutParams as? LayoutParams)?.bottomMargin ?: 0
                totalHeight += (childHeight + topMargin + bottomMargin)
            }
        }

        var heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
        /*
        * 如何是错误视图显示，则全屏显示
         */
        if (errorView?.isVisible == true) {
            heightMode = MeasureSpec.EXACTLY
        }

        val measuredHeight = when (heightMode) {
            MeasureSpec.EXACTLY -> heightSize
            MeasureSpec.AT_MOST -> totalHeight.coerceAtMost(heightSize)
            MeasureSpec.UNSPECIFIED -> totalHeight
            else -> totalHeight
        }
        DebugUtil.d(TAG, "onMeasure measuredHeight = $measuredHeight, heightMode = $heightMode ,heightSize = $heightSize")
        setMeasuredDimension(MeasureSpec.getSize(widthMeasureSpec), measuredHeight)
    }
}