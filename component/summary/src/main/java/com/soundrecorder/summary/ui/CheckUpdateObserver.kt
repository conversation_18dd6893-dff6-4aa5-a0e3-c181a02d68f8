/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  CheckUpdateObserver
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent

class CheckUpdateObserver(lifecycle: Lifecycle) : LifecycleObserver {
    var isJustRecovered = false

    init {
        lifecycle.addObserver(this)
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_START)
    fun onStart() {
        // 宿主从停止状态恢复时（ON_START 会在恢复活跃时触发）
        isJustRecovered = true
    }

    /**
     * 检查并重置状态（调用一次后自动置为 false）
      */
    fun checkAndReset(): Boolean {
        val result = isJustRecovered
        isJustRecovered = false
        return result
    }
}