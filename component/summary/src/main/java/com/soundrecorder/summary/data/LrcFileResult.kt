/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: LrcFileResult
 * Description:
 * Version: 1.0
 * Date: 2024/3/6
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/3/6 1.0 create
 */

package com.soundrecorder.summary.data

import android.net.Uri
import com.soundrecorder.common.databean.NoteData
import com.soundrecorder.common.db.NoteDbUtils.SummaryType.SUMMARY_TYPE_CALL_RECORD
import com.soundrecorder.common.db.NoteDbUtils.SummaryType.SUMMARY_TYPE_COMMON_RECORD

class LrcFileResult {
    /*摘要类型，字段同便签一致*/
    var summaryType: Int = SUMMARY_TYPE_COMMON_RECORD
    /*lrc文件uri*/
    var lrcFileUri: Uri? = null
    /*lrc文件名称*/
    var lrcFileName: String? = null
    /*图片标记中图片uri列表*/
    var picMarkUriList: List<Uri>? = null
    /*音频文件uri*/
    var mediaUri: Uri? = null
    /*音频文件md5信息*/
    var md5: String? = null
    var callUUID: String? = null
    /*音频文件全路径*/
    var mediaPath: String? = null
    /*电话本来源flag，true：这条数据能从电话本查到*/
    var contactFlag: Boolean? = null

    var fromWhere: String? = null

    fun toNoteData(callId: String?, noteId: String?, mediaId: String): NoteData {
        return NoteData(callId ?: (callUUID ?: ""), summaryType, noteId, null, mediaId, mediaPath ?: "").apply {
            this.contactFlag = <EMAIL>
        }
    }

    /**
     * 是否是电话本通话录音
     */
    fun isContactCallType(): Boolean = isCallType() && contactFlag == true

    fun isCallType(): Boolean = summaryType == SUMMARY_TYPE_CALL_RECORD
}