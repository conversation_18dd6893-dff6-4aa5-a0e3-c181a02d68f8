/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SummaryStateResult
 * Description:
 * Version: 1.0
 * Date: 2024/3/11
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2024/3/11 1.0 create
 */

package com.soundrecorder.summary.data

import android.os.Bundle
import com.soundrecorder.summary.client.RecordSummaryServiceClient

class SummaryStateResult {
    companion object {
        @JvmStatic
        fun fromBundle(state: Int, bundle: Bundle?): SummaryStateResult? {
            bundle ?: return null
            return SummaryStateResult().apply {
                this.state = state
                if (state != RecordSummaryServiceClient.SUMMARY_STATE_IN_PROGRESS) {
                    callId = bundle.getString("callId")
                    mediaId = bundle.getString("mediaId")?.toLongOrNull()
                    md5 = bundle.getString("md5")
                    noteId = bundle.getString("noteId")
                }
                when (state) {
                    RecordSummaryServiceClient.SUMMARY_STATE_IN_PROGRESS -> {
                        // 正在摘要的文件信息
                        runningCallId = bundle.getString("callId")
                        runningMediaId = bundle.getString("mediaId")?.toLongOrNull()
                        runningMd5 = bundle.getString("md5")
                        //当前调用开始摘要的文件信息
                        bundle.getBundle("curr_summary")?.run {
                            callId = getString("callId")
                            mediaId = getString("mediaId")?.toLongOrNull()
                            md5 = getString("md5")
                        }
                    }
                    RecordSummaryServiceClient.SUMMARY_STATE_SUMMARY_END -> {
                        errorInfo = getSummaryErrorInfoFromBundle(bundle)
                    }
                    RecordSummaryServiceClient.SUMMARY_STATE_AVAILABLE_TIMES -> count = bundle.getLong("count", -1) // 请求接口数据
                    RecordSummaryServiceClient.SUMMARY_STATE_ENABLE -> {
                        /*是内存缓存的数量*/
                        count = bundle.getLong("count", -1)
                        errorMessage = bundle.getString("error_message")
                    }
                }
            }
        }


        @JvmStatic
        fun getSummaryErrorInfoFromBundle(bundle: Bundle): SummaryErrorInfo {
            return SummaryErrorInfo().apply {
                asrError = bundle.getInt("asrError")
                summaryError = bundle.getInt("summaryError")
                isOver14kSize = bundle.getBoolean("isOver14kSize")
                isContentSafe = bundle.getBoolean("isContentSafe")
            }
        }
    }
    var state: Int? = null
    /*透传回来当前开始摘要的callId、mediaId、md5*/
    var callId: String? = null
    var md5: String? = null
    var mediaId: Long? = null
    /*便签生成noteId后，从便签那边透传过来的noteId，目前仅4.5.8状态值下有该值返回*/
    var noteId: String? = null
    /*查询到当日剩余可用次数，开始摘要，会去查询一遍回调过来，若满足则走流程，不满足则中断，-1：代表未查询到次数，为无效数量，不处理*/
    var count: Long? = null
    var errorInfo: SummaryErrorInfo? = null
    /*开始摘要，若当前已有正在摘要的文件，返回正在摘要的文件信息*/
    var runningMediaId: Long? = null
    var runningCallId: String? = null
    var runningMd5: String? = null
    var errorMessage: String? = null

    override fun toString(): String {
        return "SummaryStateResult(state=$state, callId=$callId, md5=$md5, mediaId=$mediaId, noteId=$noteId,count=$count, " +
                "runningMediaId=$runningMediaId, runningCallId=$runningCallId, runningMd5=$runningMd5)" +
                "error_message=$errorMessage, errorInfo=$errorInfo"
    }
}

class SummaryErrorInfo {
    /*asrError：asr错误码；summaryError 摘要流程的错误码*/
    var asrError: Int? = null
    var summaryError: Int? = null
    var isOver14kSize: Boolean? = null
    var isContentSafe: Boolean? = null

    fun getErrorCode(): Int? {
        val isAsrError = asrError != 0
        return if (isAsrError) asrError else summaryError
    }

    override fun toString(): String {
        return "SummaryErrorInfo(asrError=$asrError, summaryError=$summaryError, isOver14kSize=$isOver14kSize, isContentSafe=$isContentSafe)"
    }
}