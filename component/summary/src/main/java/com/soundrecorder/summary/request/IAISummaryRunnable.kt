/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: IAISummaryRunnable
 * Description:
 * Version: 1.0
 * Date: 2025/5/13
 * Author: W9021607
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9021607                         2025/5/13      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request

interface IAISummaryRunnable {

    fun startAISummary()

    fun cancelAISummary()

    fun release()

    fun getCurrentSteam(): String

    fun getStreamExtra(): Map<String, Any>?
}