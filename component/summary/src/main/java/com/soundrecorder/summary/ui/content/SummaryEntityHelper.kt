/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SummaryEntityUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/06/05
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content

import android.content.Context
import android.graphics.Rect
import android.text.SpannableString
import android.text.style.URLSpan
import android.view.View
import android.widget.AdapterView.OnItemClickListener
import android.widget.TextView
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.COUITouchListView
import com.coui.appcompat.poplist.PopupListItem
import com.soundrecorder.base.utils.AppUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.base.utils.ScreenUtil
import com.soundrecorder.common.utils.EnableAppUtil
import com.soundrecorder.common.utils.EnableAppUtil.APP_IS_ENABLE
import com.soundrecorder.common.utils.JumpIntentUtil
import com.soundrecorder.summary.model.SummaryEntity
import com.soundrecorder.summary.model.SummaryEntity.Companion.ADDRESS
import com.soundrecorder.summary.model.SummaryEntity.Companion.EMAIL
import com.soundrecorder.summary.model.SummaryEntity.Companion.EXPORT_ADDRESS
import com.soundrecorder.summary.model.SummaryEntity.Companion.EXPRESS
import com.soundrecorder.summary.model.SummaryEntity.Companion.NO_TIME_SCHEDULE
import com.soundrecorder.summary.model.SummaryEntity.Companion.PHONE
import com.soundrecorder.summary.model.SummaryEntity.Companion.SCHEDULE
import com.soundrecorder.summary.model.SummaryEntity.Companion.TIME
import com.soundrecorder.summary.model.SummaryEntity.Companion.WEB
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class SummaryEntityHelper(private val context: Context) {

    companion object {
        private const val TAG = "SummaryEntityUtil"

        private const val ITEM_POSITION_0 = 0
        private const val ITEM_POSITION_1 = 1
        private const val ITEM_POSITION_2 = 2
    }

    private val activity = context as FragmentActivity
    private var lifecycle = activity.lifecycleScope

    private val navigationBarMargin: Int by lazy {
        context.resources.getDimensionPixelOffset(com.support.poplist.R.dimen.coui_popup_list_window_bottom_navigation_bar_margin)
    }

    fun calculateEntitySpanPosition(
        view: View,
        span: URLSpan,
        popupWindow: COUIPopupListWindow
    ): IntArray? {
        val clickSpanRect = calculateClickSpanRect(view, span)
        if (clickSpanRect == null) {
            DebugUtil.e(TAG, "calculateEntitySpanPosition clickSpanRect is null")
            return null
        }
        val textViewRect = Rect()
        val screenHeight = ScreenUtil.getRealHeight(context)

        textViewRect.set(0, 0, view.width, view.height)
        val location = IntArray(2)
        view.getLocationInWindow(location)
        textViewRect.offset(location[0], location[1])

        // 计算PopupWindow的尺寸
        popupWindow.measurePopupWindow(false)
        val popupHeight = getPopWindowHeight(popupWindow)
        DebugUtil.i(TAG, "calculateEntitySpanPosition clickSpanRect = $clickSpanRect," +
                "textViewRect = $textViewRect," +
                "popupHeight = $popupHeight," +
                "screenHeight = $screenHeight," +
                "navigationBarMargin = $navigationBarMargin")
        val bottom = when {
            //如果以顶部为popY,超过距离的话，会往上弹，则取top
            (clickSpanRect.bottom + textViewRect.top + popupHeight > screenHeight - navigationBarMargin) -> clickSpanRect.top
            else -> clickSpanRect.bottom
        }
        DebugUtil.d(TAG, "calculateEntitySpanPosition bestPosition = ${clickSpanRect.left}, $bottom")
        return intArrayOf(clickSpanRect.left, bottom)
    }

    private fun getPopWindowHeight(popupWindow: COUIPopupListWindow): Int {
        return kotlin.runCatching {
            val privateProperty =
                COUITouchListView::class.java.getDeclaredField("mTotalHeight")
            privateProperty.isAccessible = true
            privateProperty.get(popupWindow.mainMenuListView) as? Int ?: 0
        }.onFailure {
            DebugUtil.e(TAG, "${it.message}")
        }.getOrDefault(context.resources.getDimensionPixelSize(com.soundrecorder.common.R.dimen.dp196))
    }

    private fun calculateClickSpanRect(view: View, span: URLSpan): Rect? {
        val textView = view as? TextView ?: return null
        val spannableString = textView.text as? SpannableString ?: run {
            DebugUtil.e(TAG, "calculateEntitySpanPosition get spannable string, error")
            return null
        }
        val layout = textView.layout ?: return null
        val start = spannableString.getSpanStart(span)
        val end = spannableString.getSpanEnd(span)
        // 获取ClickSpan的起始和结束行
        val startLine = layout.getLineForOffset(start)
        val endLine = layout.getLineForOffset(end)
        // 获取ClickSpan的起始和结束X坐标
        val startX = layout.getPrimaryHorizontal(start)
        val endX = layout.getPrimaryHorizontal(end)
        // 获取ClickSpan的顶部Y坐标
        val topY = layout.getLineTop(startLine)
        // 获取ClickSpan的底部Y坐标
        val bottomY = layout.getLineBottom(endLine)
        // 设置矩形区域
        val rect = Rect()
        rect.set(startX.toInt(), topY, endX.toInt(), bottomY)
        return rect
    }

    fun createSummaryEntityPopMenu(entity: SummaryEntity): COUIPopupListWindow? {
        val itemArrayRes = when (entity.type) {
            NO_TIME_SCHEDULE, SCHEDULE, TIME -> com.soundrecorder.summary.R.array.time_list
            ADDRESS, EXPORT_ADDRESS -> com.soundrecorder.summary.R.array.address_list
            PHONE -> com.soundrecorder.summary.R.array.phone_list
            EXPRESS -> com.soundrecorder.summary.R.array.express_list
            EMAIL -> com.soundrecorder.summary.R.array.email_list
            WEB -> com.soundrecorder.summary.R.array.web_url_list
            else -> -1
        }
        if (itemArrayRes == -1) {
            return null
        }
        val itemList = getItemList(itemArrayRes)
        if (itemList.isEmpty()) {
            DebugUtil.i(TAG, "current entity not support pop, ${entity.type}")
            return null
        }
        val popWindow = COUIPopupListWindow(context)
        val onItemClickListener = getItemClickListener(popWindow, itemArrayRes, itemList, entity) ?: run {
            DebugUtil.i(TAG, "current entity not support listener, ${entity.type}")
            return null
        }
        return popWindow.apply {
            resetOffset()
            this.setDismissTouchOutside(true)
            this.itemList = itemList
            this.setOnItemClickListener(onItemClickListener)
        }
    }

    private fun getItemList(itemArray: Int): List<PopupListItem> {
        val menuItem = context.resources.getStringArray(itemArray)
        val build = PopupListItem.Builder()
        val result = mutableListOf<PopupListItem>()
        menuItem.forEach {
            if (filterUnSupport(it)) {
                build.reset()
                build.setTitle(it)
                result.add(build.build())
            }
        }
        return result
    }

    private fun filterUnSupport(itemId: String): Boolean {
        return when (itemId) {
            context.getString(com.soundrecorder.common.R.string.summary_entity_time_add_todo) -> {
                (OS12FeatureUtil.isColorOS16OrLater() && JumpIntentUtil.isSupportTask(context))
                        && EnableAppUtil.isAppInstallEnabled(context, AppUtil.getCalendarPackageName()) == APP_IS_ENABLE
                        || EnableAppUtil.isAppInstallEnabled(context, AppUtil.getNotesPackageName()) == APP_IS_ENABLE
            }
            context.getString(com.soundrecorder.common.R.string.summary_entity_address_go),
            context.getString(com.soundrecorder.common.R.string.summary_entity_phone_call),
            context.getString(com.soundrecorder.common.R.string.summary_entity_express_find),
            context.getString(com.soundrecorder.common.R.string.summary_entity_email_write) -> JumpIntentUtil.isSupportTextIntent(context)
            else -> true
        }
    }

    private fun getItemClickListener(
        popWindow: COUIPopupListWindow,
        itemArrayRes: Int,
        itemList: List<PopupListItem>,
        entity: SummaryEntity
    ): OnItemClickListener? {
        val menuItems = context.resources.getStringArray(itemArrayRes)
        val title = itemList.map { it.title }
        return when (entity.type) {
            NO_TIME_SCHEDULE, SCHEDULE, TIME -> getTimeClickListener(popWindow, menuItems, title, entity)
            ADDRESS, EXPORT_ADDRESS -> getAddressClickListener(popWindow, menuItems, title, entity)
            PHONE -> getPhoneClickListener(popWindow, menuItems, title, entity)
            EXPRESS -> getExpressClickListener(popWindow, menuItems, title, entity)
            EMAIL -> getEmailClickListener(popWindow, menuItems, title, entity)
            WEB -> getWebClickListener(popWindow, menuItems, title, entity)
            else -> null
        }
    }

    private fun getTimeClickListener(
        popWindow: COUIPopupListWindow,
        menuItems: Array<String>,
        title: List<String>,
        entity: SummaryEntity
    ): OnItemClickListener {
        return OnItemClickListener { _, _, position, _ ->
            when (menuItems.indexOf(title[position])) {
                ITEM_POSITION_0 -> {
                    lifecycle.launch {
                        val isSupportOIntent = withContext(Dispatchers.Default) {
                            JumpIntentUtil.isSupportTextIntent(context)
                        }
                        JumpIntentUtil.createScheduleByTextIntent(
                            context,
                            time = entity.timestamp.toString(),
                            timeInitialed = (entity.type == SCHEDULE) && isSupportOIntent
                        )
                    }
                }

                ITEM_POSITION_1 -> {
                    lifecycle.launch(Dispatchers.Default) {
                        JumpIntentUtil.callAddTodo(context, entity.name, entity.timestamp)
                    }
                }

                else -> JumpIntentUtil.copyText(activity, lifecycle, entity.name)
            }
            popWindow.dismiss()
        }
    }

    private fun getAddressClickListener(
        popWindow: COUIPopupListWindow,
        menuItems: Array<String>,
        title: List<String>,
        entity: SummaryEntity
    ): OnItemClickListener {
        return OnItemClickListener { _, _, position, _ ->
            when (menuItems.indexOf(title[position])) {
                ITEM_POSITION_0 -> JumpIntentUtil.intentToAddress(context, entity.name)
                ITEM_POSITION_1 -> JumpIntentUtil.shareText(context, entity.name)
                else -> JumpIntentUtil.copyText(activity, lifecycle, entity.name)
            }
            popWindow.dismiss()
        }
    }

    private fun getPhoneClickListener(
        popWindow: COUIPopupListWindow,
        menuItems: Array<String>,
        title: List<String>,
        entity: SummaryEntity
    ): OnItemClickListener {
        return OnItemClickListener { _, _, position, _ ->
            when (menuItems.indexOf(title[position])) {
                ITEM_POSITION_0 -> JumpIntentUtil.callPhone(context, entity.name)
                ITEM_POSITION_1 -> JumpIntentUtil.sendSms(context, entity.name)
                ITEM_POSITION_2 -> JumpIntentUtil.savePhoneNumber(context, number = entity.name)
                else -> JumpIntentUtil.copyText(activity, lifecycle, entity.name)
            }
            popWindow.dismiss()
        }
    }

    private fun getExpressClickListener(
        popWindow: COUIPopupListWindow,
        menuItems: Array<String>,
        title: List<String>,
        entity: SummaryEntity
    ): OnItemClickListener {
        return OnItemClickListener { _, _, position, _ ->
            when (menuItems.indexOf(title[position])) {
                ITEM_POSITION_0 -> JumpIntentUtil.callExpress(context, entity.name)
                ITEM_POSITION_1 -> JumpIntentUtil.shareText(context, entity.name)
                else -> JumpIntentUtil.copyText(activity, lifecycle, entity.name)
            }
            popWindow.dismiss()
        }
    }

    private fun getEmailClickListener(
        popWindow: COUIPopupListWindow,
        menuItems: Array<String>,
        title: List<String>,
        entity: SummaryEntity
    ): OnItemClickListener {
        return OnItemClickListener { _, _, position, _ ->
            when (menuItems.indexOf(title[position])) {
                ITEM_POSITION_0 -> JumpIntentUtil.callEmail(context, entity.name)
                ITEM_POSITION_1 -> JumpIntentUtil.saveEmailAddress(context, entity.name)
                else -> JumpIntentUtil.copyText(activity, lifecycle, entity.name)
            }
            popWindow.dismiss()
        }
    }

    private fun getWebClickListener(
        popWindow: COUIPopupListWindow,
        menuItems: Array<String>,
        title: List<String>,
        entity: SummaryEntity
    ): OnItemClickListener {
        return OnItemClickListener { _, _, position, _ ->
            when (menuItems.indexOf(title[position])) {
                ITEM_POSITION_0 -> JumpIntentUtil.callWebUrl(context, entity.name, handPanel = true)
                ITEM_POSITION_1 -> JumpIntentUtil.callWebUrl(context, entity.name, handPanel = false)
                else -> JumpIntentUtil.copyText(activity, lifecycle, entity.name)
            }
            popWindow.dismiss()
        }
    }
}