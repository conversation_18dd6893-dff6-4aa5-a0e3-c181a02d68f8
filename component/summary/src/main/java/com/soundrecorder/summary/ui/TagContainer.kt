/*********************************************************************
 * * Copyright (C), 2030, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  TagContainer
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/23
 * * Author      : keweiwei
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui

import android.content.Context
import android.util.AttributeSet
import android.widget.LinearLayout
import androidx.core.view.children

class TagContainer @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "TagContainer"
    }

    private var space: Int = 0

    init {
        loadFromAttributes(context, attrs, defStyleAttr)
    }

    private fun loadFromAttributes(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
    ) {
        val array = context.theme.obtainStyledAttributes(
            attrs,
            com.soundrecorder.summary.R.styleable.SummaryTag,
            defStyleAttr,
            0
        )
        space = array.getDimensionPixelSize(com.soundrecorder.summary.R.styleable.SummaryTag_space, 0)
        array.recycle()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        measureChildren(widthMeasureSpec, heightMeasureSpec)
        var totalWidth = 0
        children.forEachIndexed { index, view ->
            val width = view.measuredWidth
            totalWidth += if (index == 0) {
                width
            } else {
                width + space
            }
        }
        val widthMode = MeasureSpec.getMode(widthMeasureSpec)
        val widthSize = MeasureSpec.getSize(widthMeasureSpec)
        val measuredWidth = when (widthMode) {
            MeasureSpec.EXACTLY -> widthSize
            MeasureSpec.AT_MOST -> totalWidth.coerceAtMost(widthSize)
            MeasureSpec.UNSPECIFIED -> totalWidth
            else -> totalWidth
        }
        setMeasuredDimension(measuredWidth, MeasureSpec.getSize(heightMeasureSpec))
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        val isRtl = layoutDirection == LAYOUT_DIRECTION_RTL
        val targetChildRen = if (isRtl) children.toList().reversed() else children.toList()
        var left = 0
        targetChildRen.forEach { view ->
            val width = view.measuredWidth
            val height = view.measuredHeight
            val right = left + width
            view.layout(left, t, right, t + height)
            left = right + space
        }
    }
}