/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: AISummaryTaskManager
 * Description:录音摘要任务队列管理器，多个录音摘要同时进行，建立Task队列进行管理
 * Version: 1.0
 * Date: 2025/5/13
 * Author: ********
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * ********                         2025/5/13      1.0         Create this module
 *********************************************************************************/
package com.soundrecorder.summary.request

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.summary.model.SummaryRequestModel
import java.util.LinkedList
import java.util.Queue
import java.util.concurrent.ConcurrentHashMap

object AISummaryTaskManager : IAISummaryProcess {

    const val TAG = "AISummaryTaskManager"

    /* 任务管理器 */
    var jobManagerLifeCycleCallback: IJobManagerLifeCycleCallback? = null

    /* 同时转换数量 */
    private const val LIMIT_SIZE = 3

    /* 默认为空 */
    const val DEFAULT_NULL = 0

    /* 已在运行 */
    private const val ALREADY_RUNNING = 1

    /* 超过限制 */
    private const val OVER_LIMIT = 2

    /* 添加新任务 */
    private const val CAN_ADD_NEW = 3

    /* 任务map */
    private var taskMaps: ConcurrentHashMap<Long, IAISummaryRunnable> =
        ConcurrentHashMap(LIMIT_SIZE)

    /* Ui回调函数 map */
    private var uiCallback: ConcurrentHashMap<Long, IAISummaryCallback> =
        ConcurrentHashMap(LIMIT_SIZE)

    /* 等待队列 */
    private var mWaitingTasks: Queue<SummaryRequestModel> = LinkedList()

    private fun removeTask(mediaId: Long) {
        if (taskMaps.containsKey(mediaId)) {
            DebugUtil.d(TAG, "removeTask, mediaId:$mediaId")
            taskMaps.remove(mediaId)
        }
    }

    private fun waitTaskQueuePoll() {
        synchronized(AISummaryTaskManager::class) {
            if (!mWaitingTasks.isEmpty()) {
                val model = mWaitingTasks.poll()
                DebugUtil.d(TAG, "waitTaskQueuePoll, waitRecord:${model?.mediaId}")
                if (model != null) {
                    addOrResumeAISummaryTask(model)
                }
            }
        }
    }

    /**
     * 检查最终Task是否结束
     */
    private fun checkFinalTaskEnd(mediaId: Long) {
        if (taskMaps.isEmpty()) {
            DebugUtil.i(TAG, "checkFinalTaskEnd: onFinalJobEnd $mediaId")
            jobManagerLifeCycleCallback?.onFinalJobEnd(mediaId)
        }
    }

    private fun getConvertUiCallback(): IAISummaryCallback {
        return object : IAISummaryCallback {

            override fun onAISummaryStart(mediaId: Long, extras: Map<String, Any>?) {
                uiCallback[mediaId]?.onAISummaryStart(mediaId, extras)
            }

            override fun onAISummaryStop(mediaId: Long, extras: Map<String, Any>?) {
                uiCallback[mediaId]?.onAISummaryStop(mediaId, extras)
                //当停止了检测是否要下一个，无论成功或者失败都会回调stop的
                DebugUtil.i(TAG, "onAISummaryStop mediaId = $mediaId")
                postAISummary(mediaId)
            }

            override fun onAISummaryDataAvailable(
                mediaId: Long,
                stream: String,
                extras: Map<String, Any>?
            ) {
                uiCallback[mediaId]?.onAISummaryDataAvailable(mediaId, stream, extras)
            }

            override fun onAISummaryFinished(
                mediaId: Long,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                uiCallback[mediaId]?.onAISummaryFinished(mediaId, jsonResult, extras)
                DebugUtil.i(TAG, "onAISummaryFinished mediaId = $mediaId")
                postAISummary(mediaId)
            }

            override fun onAISummaryError(mediaId: Long, errorCode: Int, errorMsg: String?) {
                uiCallback[mediaId]?.onAISummaryError(mediaId, errorCode, errorMsg)
                DebugUtil.i(TAG, "onAISummaryError mediaId = $mediaId")
                postAISummary(mediaId)
            }
        }
    }

    private fun postAISummary(mediaId: Long) {
        DebugUtil.d(TAG, "postAISummary, mediaId:$mediaId")
        taskMaps[mediaId]?.release()
        removeTask(mediaId)
        waitTaskQueuePoll()
        checkFinalTaskEnd(mediaId)
    }

    /**
     * 取消任务
     */
    private fun cancelTask(mediaId: Long): Boolean {
        val result = if (!taskMaps.containsKey(mediaId)) {
            DebugUtil.i(TAG, "maps not contains mediaId: $mediaId, no need to add task again")
            false
        } else {
            taskMaps[mediaId]?.cancelAISummary()
            true
        }
        return result
    }

    /**
     * 取消所有任务
     */
    fun cancelAllTask() {
        taskMaps.forEach { (_, value) ->
            value.cancelAISummary()
        }
    }

    /**
     * 当前mediaId是否在进行
     */
    fun checkIsTaskRunning(mediaId: Long): Boolean {
        return taskMaps.containsKey(mediaId)
    }

    /**
     * 当前没有Task在进行
     */
    fun checkNoTaskRunning(): Boolean {
        DebugUtil.i(TAG, "checkNoTaskRunning = ${taskMaps.isEmpty()}")
        return taskMaps.isEmpty()
    }

    /**
     * 当前有Task在进行
     */
    fun checkHasTaskRunning(): Boolean {
        DebugUtil.i(TAG, "checkHasTaskRunning = ${taskMaps.isNotEmpty()}")
        return taskMaps.isNotEmpty()
    }

    /**
     * 释t放当前mediaId的ask
     */
    fun release(mediaId: Long) {
        DebugUtil.i(TAG, "release: $mediaId")
        taskMaps[mediaId]?.release()
    }

    private fun checkCanAddNewTask(mediaId: Long): Int {
        if (taskMaps.containsKey(mediaId)) {
            DebugUtil.i(TAG, "checkCanAddNewTask: $mediaId already running, no need to add")
            return ALREADY_RUNNING
        } else if (taskMaps.size >= LIMIT_SIZE) {
            DebugUtil.i(TAG, "maps limit reached : runnable $mediaId, add waiting")
            return OVER_LIMIT
        } else {
            return CAN_ADD_NEW
        }
    }

    fun releaseAll() {
        DebugUtil.i(TAG, "release all")
        uiCallback.clear()
        taskMaps.values.forEach { it.release() }
        taskMaps.clear()
        mWaitingTasks.clear()
    }

    override fun startAISummary(model: SummaryRequestModel): Boolean {
        return addOrResumeAISummaryTask(model)
    }

    /**
     * 添加录音摘要任务
     */
    private fun addOrResumeAISummaryTask(model: SummaryRequestModel): Boolean {
        val result = when (checkCanAddNewTask(model.mediaId)) {
            ALREADY_RUNNING -> {
                val task = taskMaps[model.mediaId]
                DebugUtil.i(TAG, "maps already contains mediaId : ${model.mediaId}, task = ${task != null}, no need to add Task again")
                val currentStream = task?.getCurrentSteam() ?: ""
                val currentStreamExtra = task?.getStreamExtra()
                uiCallback[model.mediaId]?.onAISummaryTaskRunning(mediaId = model.mediaId, content = currentStream, extras = currentStreamExtra)
                true
            }

            OVER_LIMIT -> {
                DebugUtil.i(TAG, "maps limit reached : runnable ${model.mediaId}, add to waiting list")
                addWaitingTasks(model)
                uiCallback[model.mediaId]?.onAISummaryWait(mediaId = model.mediaId)
                false
            }

            else -> {
                val aiSummaryRunnable = AISummaryRunnable(model, getConvertUiCallback())
                taskMaps[model.mediaId] = aiSummaryRunnable
                aiSummaryRunnable.startAISummary()
                DebugUtil.i(TAG, "aiSummaryRunnable ${model.mediaId} start run")
                true
            }
        }
        return result
    }


    private fun addWaitingTasks(model: SummaryRequestModel) {
        if (checkIsTaskRunning(model.mediaId)) {
            return
        }
        if (mWaitingTasks.find { it.mediaId == model.mediaId } != null) {
            return
        }
        DebugUtil.i(TAG, "addWaitingTasks, mediaId: ${model.mediaId}")
        mWaitingTasks.add(model)
    }

    override fun cancelAISummary(mediaId: Long): Boolean {
        return cancelTask(mediaId)
    }

    override fun releaseAISummary(mediaId: Long) {
        release(mediaId)
    }

    override fun registerAISummaryCallback(mediaId: Long, callback: IAISummaryCallback) {
        if (uiCallback[mediaId] != null) {
            DebugUtil.i(TAG, "register ui callback $callback")
        }
        uiCallback[mediaId] = callback
    }

    override fun unregisterAISummaryCallback(mediaId: Long, callback: IAISummaryCallback) {
        if (uiCallback[mediaId] == null) {
            DebugUtil.i(TAG, "unregisterAISummaryCallback ui callback is null")
        }
        uiCallback.remove(mediaId)
    }
}