/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SceneAdapter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/08
 * * Author      : w9099028
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content.summarytemplate

import android.app.Dialog
import android.os.Bundle
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment

class SceneBottomSheetDialogFragment : COUIBottomSheetDialogFragment() {
    private var sceneDialogFragment: SceneDialogFragment? = null
    private var animationListener:  COUIBottomSheetDialog.OnAnimationListener? = null

    override fun setMainPanelFragment(panelFragment: COUIPanelFragment?) {
        super.setMainPanelFragment(panelFragment)
        sceneDialogFragment = panelFragment as? SceneDialogFragment
    }

    fun setOnItemClickListener(listener: (Int) -> Unit) {
        sceneDialogFragment?.setOnItemClickListener(listener)
    }

    fun setAnimationListener(animationListener: COUIBottomSheetDialog.OnAnimationListener?) {
        this.animationListener = animationListener
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState)
        (dialog as? COUIBottomSheetDialog)?.setAnimationListener(animationListener)
        return dialog
    }

    override fun onDestroyView() {
        super.onDestroyView()
        animationListener = null
    }
}