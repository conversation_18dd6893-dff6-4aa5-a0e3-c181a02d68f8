/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: PrivacyPolicyDelegateTest
 Description:
 Version: 1.0
 Date: 2023/05/25 1.0
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2023/05/25 1.0 create
 */

package com.soundrecorder.privacypolicy

import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.privacypolicy.shadows.ShadowCOUIMaxHeightScrollView
import com.soundrecorder.privacypolicy.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.privacypolicy.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.Robolectric
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowFeatureOption::class,
        ShadowCOUIMaxHeightScrollView::class, ShadowCOUIVersionUtil::class]
)
class PrivacyPolicyDelegateTest {

    private var context: Context? = null
    private var mActivity: AppCompatActivity? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mActivity = Robolectric.buildActivity(AppCompatActivity::class.java).get()
    }

    @Test
    fun check_onSaveInstanceState() {
        val resultCallback = Mockito.mock(IPrivacyPolicyResultListener::class.java)
        val delegate = PrivacyPolicyDelegate(mActivity!!, IPrivacyPolicyDelegate.POLICY_TYPE_COMMON, resultCallback)
        val build = Bundle()
        delegate.onSaveInstanceState(build)
        Assert.assertTrue(build.getIntegerArrayList("key_show_dialog_array_list") != null)
        Assert.assertTrue(!build.getBoolean("function_has_permission"))
        delegate.onRestoreInstanceState(build)
    }

    @Test
    fun check_onConfigurationChanged() {
        val resultCallback = Mockito.mock(IPrivacyPolicyResultListener::class.java)
        val delegate = PrivacyPolicyDelegate(mActivity!!, IPrivacyPolicyDelegate.POLICY_TYPE_COMMON, resultCallback)
        val config = Configuration()
        delegate.onConfigurationChanged(config)
    }

    @Test
    fun check_test() {
        val resultCallback = Mockito.mock(IPrivacyPolicyResultListener::class.java)
        val delegate = PrivacyPolicyDelegate(mActivity!!, IPrivacyPolicyDelegate.POLICY_TYPE_COMMON, resultCallback)
        delegate.checkAndDismissDialog()
        delegate.resumeShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE, false)
        Assert.assertFalse(delegate.hasConvertPermission())
        Assert.assertFalse(delegate.canShowWithdrawnPermissionConvert())
        delegate.onDestroy()
    }

    @Test
    fun should_showDialog_when_resumeShowDialog() {
        val resultCallback = Mockito.mock(IPrivacyPolicyResultListener::class.java)
        val delegate = PrivacyPolicyDelegate(mActivity!!, IPrivacyPolicyDelegate.POLICY_TYPE_MINI, resultCallback)
        delegate.checkAndDismissDialog()

        delegate.resumeShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE, false)

        val dialogManager = Whitebox.getInternalState<PrivacyPolicyDialogManager>(delegate, "dialogManager")
        Assert.assertTrue(dialogManager.isShowing(PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE))

        delegate.onDestroy()
    }

    @After
    fun tearDown() {
        context = null
        mActivity = null
    }
}