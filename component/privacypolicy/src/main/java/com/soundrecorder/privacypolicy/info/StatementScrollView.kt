/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: StatementScrollView
 * Description:
 * Version: 1.0
 * Date: 2023/10/27
 * Author: W9058795
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9058795 2023/10/27 1.0 create
 */
package com.soundrecorder.privacypolicy.info

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.LinearLayout
import androidx.core.view.marginBottom
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.statement.COUIMaxHeightScrollView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.privacypolicy.R

class StatementScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet?,
    defStyleAttr: Int = 0
) : COUIMaxHeightScrollView(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "StatementScrollView"
    }
    private var addBottomCanScroll = 0
    private var maxY: Int = 0

    init {
        val a = context.obtainStyledAttributes(attrs, R.styleable.StatementScrollView)
        addBottomCanScroll = a.getDimensionPixelOffset(R.styleable.StatementScrollView_marginBottomCanScroll, 0)
        a.recycle()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        maxY = getChildAt(0).measuredHeight - measuredHeight
        if (maxY > 0 && (addBottomCanScroll > 0 && marginBottom != addBottomCanScroll)) {
            DebugUtil.d(TAG, "marginBottom = $marginBottom, addBottomCanScroll = $addBottomCanScroll")
            updateLayoutParams<LinearLayout.LayoutParams> {
                bottomMargin = addBottomCanScroll
            }
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        ev?.let {
            when (ev.action) {
                MotionEvent.ACTION_MOVE -> {
                    if (scrollY in 1 until maxY) {
                        parent.requestDisallowInterceptTouchEvent(true)
                    } else {
                        parent.requestDisallowInterceptTouchEvent(false)
                    }
                }

                MotionEvent.ACTION_UP ->
                    parent.parent.requestDisallowInterceptTouchEvent(false)

                MotionEvent.ACTION_DOWN ->
                    parent.parent.requestDisallowInterceptTouchEvent(true)
            }
        }
        return super.dispatchTouchEvent(ev)
    }
}
