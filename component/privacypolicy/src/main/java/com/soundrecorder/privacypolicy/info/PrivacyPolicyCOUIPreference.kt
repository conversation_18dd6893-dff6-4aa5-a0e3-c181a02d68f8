/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyCOUIPreference
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/5/16
 * * Author      : z<PERSON><PERSON><PERSON><EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.privacypolicy.info

import android.content.Context
import android.content.res.ColorStateList
import android.util.AttributeSet
import android.widget.TextView
import androidx.preference.PreferenceViewHolder
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.preference.COUIPreference


class PrivacyPolicyCOUIPreference(
    context: Context?,
    attrs: AttributeSet?
) : COUIPreference(context, attrs) {
    override fun onBindViewHolder(holder: PreferenceViewHolder) {
        super.onBindViewHolder(holder)
        val titleView = holder.findViewById(android.R.id.title) as TextView
        val color = COUIContextUtil.getAttrColor(
            context,
            com.support.appcompat.R.attr.couiColorPrimary,
            0
        )
        val disableColor = COUIContextUtil.getAttrColor(
            context,
            com.support.appcompat.R.attr.couiColorDisabledNeutral,
            0
        )
        val colorStateList = ColorStateList(
            arrayOf(
                intArrayOf(android.R.attr.state_enabled),
                intArrayOf()
            ),
            intArrayOf(color, disableColor)
        )
        titleView.setTextColor(colorStateList)
    }
}