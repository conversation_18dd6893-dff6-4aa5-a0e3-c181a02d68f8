/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : PrivacyPolicyApiExt.kt
 ** Description : PrivacyPolicyApiExt.kt
 ** Version     : 1.0
 ** Date        : 2025/07/24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/07/24     1.0      create
 ***********************************************************************/
package com.soundrecorder.privacypolicy

import androidx.appcompat.app.AppCompatActivity
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyInterface

fun PrivacyPolicyInterface?.resumeCommonShowDialog(
    activity: AppCompatActivity,
    type: Int,
    forceShow: Boolean = false,
    pageFrom: Int? = null,
    successCallback: (() -> Unit)? = null,
    failCallback: (() -> Unit)? = null
) {
    if (this == null) {
        return
    }
    val privacyDelegate = newPrivacyPolicyDelegate(
        activity,
        IPrivacyPolicyDelegate.POLICY_TYPE_COMMON,
        object : IPrivacyPolicyResultListener {
            override fun onPrivacyPolicySuccess(type: Int, pageFrom: Int?) {
                successCallback?.invoke()
            }

            override fun onPrivacyPolicyFail(type: Int, pageFrom: Int?) {
                failCallback?.invoke()
            }

            override fun onClickSpan(type: Int) {
            }
        }
    )
    privacyDelegate.resumeShowDialog(type, forceShow, pageFrom)
}