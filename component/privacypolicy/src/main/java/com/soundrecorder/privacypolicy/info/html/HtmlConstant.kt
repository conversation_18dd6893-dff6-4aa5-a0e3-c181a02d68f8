/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/11/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.privacypolicy.info.html

object HtmlConstant {
    /**
     * Flag indicating that texts inside &lt;p&gt; elements will be separated from other texts with
     * one newline character by default.
     */
    const val FROM_HTML_SEPARATOR_LINE_BREAK_PARAGRAPH = 0x00000001

    /**
     * Flag indicating that texts inside &lt;h1&gt;~&lt;h6&gt; elements will be separated from
     * other texts with one newline character by default.
     */
    const val FROM_HTML_SEPARATOR_LINE_BREAK_HEADING = 0x00000002

    /**
     * Flag indicating that texts inside &lt;li&gt; elements will be separated from other texts
     * with one newline character by default.
     */
    const val FROM_HTML_SEPARATOR_LINE_BREAK_LIST_ITEM = 0x00000004

    /**
     * Flag indicating that texts inside &lt;ul&gt; elements will be separated from other texts
     * with one newline character by default.
     */
    const val FROM_HTML_SEPARATOR_LINE_BREAK_LIST = 0x00000008

    /**
     * Flag indicating that texts inside &lt;div&gt; elements will be separated from other texts
     * with one newline character by default.
     */
    const val FROM_HTML_SEPARATOR_LINE_BREAK_DIV = 0x00000010

    /**
     * Flag indicating that texts inside &lt;blockquote&gt; elements will be separated from other
     * texts with one newline character by default.
     */
    const val FROM_HTML_SEPARATOR_LINE_BREAK_BLOCKQUOTE = 0x00000020
}