/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyDelegate
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.privacypolicy.info

import android.content.res.Configuration
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener
import com.soundrecorder.modulerouter.utils.Injector

class PrivacyPolicyDelegate(
    private val context: AppCompatActivity,
    policyType: Int,
    resultCallback: IPrivacyPolicyResultListener? = null
) : IPrivacyPolicyDelegate {

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    companion object {
        const val TAG = "PrivacyPolicyDelegate"

        private const val KEY_SHOW_DIALOG_ARRAY_LIST = "key_show_dialog_array_list"
    }

    private val dialogManager: PrivacyPolicyDialogManager = PrivacyPolicyDialogManager.newInstance(policyType, context, resultCallback)

    override fun onSaveInstanceState(outState: Bundle) {
        val keys = dialogManager.onSaveShowingDialog()
        DebugUtil.i(TAG, "onSaveInstanceState,current show dialog list = $keys")
        outState.putIntegerArrayList(KEY_SHOW_DIALOG_ARRAY_LIST, keys)
        outState.putBoolean("function_has_permission", dialogManager.hasConvertPermission)
    }

    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        val keys = savedInstanceState.getIntegerArrayList(KEY_SHOW_DIALOG_ARRAY_LIST)
        DebugUtil.i(TAG, "onRestoreInstanceState,current show dialog list = $keys")
        dialogManager.onRestoreShowingDialog(keys)

        dialogManager.hasConvertPermission =
            savedInstanceState.getBoolean("function_has_permission", false)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        DebugUtil.i(TAG, "onConfigurationChanged")
        /*用户须知等弹窗activity走configurationChange时重建*/
        dialogManager.reCreateStateDialog()
    }

    override fun checkAndDismissDialog() {
        dialogManager.checkAndDismissDialog()
    }

    override fun resumeShowDialog(type: Int, forceShow: Boolean, pageFrom: Int?) {
        dialogManager.resumeShowDialog(type, forceShow, pageFrom)
    }

    override fun hasConvertPermission(): Boolean {
        return dialogManager.hasConvertPermission
    }

    override fun canShowWithdrawnPermissionConvert(): Boolean {
        return PermissionUtils.isStatementConvertGranted(context)
                || (cloudKitApi?.isSupportCloudArea() == true
                && cloudKitApi?.isSupportSwitch() == true
                && cloudKitApi?.isStatementCloudGranted(context) == true)
    }

    override fun getLastShowPrivacyDialogType(): Int? {
        return dialogManager.getShowingPolicyDialogType()
    }

    override fun onDestroy() {
        dialogManager.clearAll()
    }
}