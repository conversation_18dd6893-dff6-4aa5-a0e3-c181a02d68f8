/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DirectSummaryGuideDialog
 * Description:
 * Version: 1.0
 * Date: 2024/5/30
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2024/5/30 1.0 create
 */

package com.soundrecorder.privacypolicy.functionguide

import android.animation.ValueAnimator
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.ViewPager2
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.indicator.COUIPageIndicator
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.UniDirectionalRecordUtils
import com.soundrecorder.common.utils.ViewUtils.onRelease
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.privacypolicy.R
import com.soundrecorder.privacypolicy.databinding.LayoutDiretSummaryGuideBinding

class DirectSummaryGuideDialog(private val functionClickOk: (() -> Unit)?) {
    private var guideDialog: AlertDialog? = null

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    fun showDialog(context: Context): AlertDialog? {
        if (guideDialog?.isShowing == true) {
            DebugUtil.w("DirectSummaryGuideDialog", "showDialog isShowing")
            return guideDialog
        }
        COUIAlertDialogBuilder(context).apply {
            val customView = LayoutDiretSummaryGuideBinding.inflate(LayoutInflater.from(context))
            initCustomView(customView)
            setView(customView.root)
            setPositiveButton(com.soundrecorder.base.R.string.button_ok, { _, _ -> functionClickOk?.invoke() }, true)
            setCancelable(false)
            setBlurBackgroundDrawable(true)
            guideDialog = show()
        }
        return guideDialog
    }

    fun releaseDialog() {
        if (guideDialog?.isShowing == true) {
            guideDialog?.dismiss()
        }
        guideDialog = null
    }

    private fun initCustomView(binding: LayoutDiretSummaryGuideBinding) {
        val list = mutableListOf<FunctionGuideData>()

        if (UniDirectionalRecordUtils.isSupportDirectionalRecording(BaseApplication.getAppContext())) {
            list.add(
                FunctionGuideData(
                    R.raw.component_direct_recording_guide,
                    com.soundrecorder.base.R.string.specified_direct_record,
                    com.soundrecorder.base.R.string.specified_direct_guide_content,
                    true))
        }
        if (summaryApi?.getSupportRecordSummaryValue()?.value == true) {
            list.add(
                FunctionGuideData(
                    R.drawable.ic_guide_summary,
                    com.soundrecorder.base.R.string.summary_guide_title,
                    com.soundrecorder.base.R.string.summary_guide_content,
                    false))
        }
        binding.guideContentContainer.adapter = ImagePagerAdapter(list)
        if (list.size > 1) {
            binding.guideContentIndicator.isVisible = true
            binding.guideContentIndicator.dotsCount = list.size
            binding.guideContentIndicator.setOnDotClickListener { position ->
                binding.guideContentContainer.currentItem = position
            }
            setPagerCallback(binding.guideContentContainer, binding.guideContentIndicator)
        } else {
            binding.guideContentIndicator.isVisible = false
        }
    }

    private fun setPagerCallback(viewPager: ViewPager2, indicator: COUIPageIndicator) {
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels)
                indicator.onPageScrolled(position, positionOffset, positionOffsetPixels)
            }

            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                indicator.onPageSelected(position)
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                indicator.onPageScrollStateChanged(state)
            }
        })
    }

    inner class ImagePagerAdapter(private val dataList: List<FunctionGuideData>) : RecyclerView.Adapter<ImagePagerAdapter.ViewHolder>() {

        inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
            var tileView = itemView.findViewById<TextView>(R.id.guide_tips_title)
            var descriptionView = itemView.findViewById<TextView>(R.id.guide_tips_description)
            var imageView = itemView.findViewById<EffectiveAnimationView>(R.id.pager_item_im)

            /*init {
                COUIChangeTextUtil.adaptFontSize(tileView, COUIChangeTextUtil.G4)
                COUIChangeTextUtil.adaptFontSize(descriptionView, COUIChangeTextUtil.G4)
            }*/
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view: View = LayoutInflater.from(parent.context).inflate(R.layout.pager_item_direct_summary, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val res = holder.tileView.resources
            holder.tileView.text = res.getText(dataList[position].titleRes)
            holder.descriptionView.text = res.getText(dataList[position].contentRes)
            if (dataList[position].isAnimate) {
                holder.imageView.setAnimation(dataList[position].imageRes)
                holder.imageView.repeatCount = ValueAnimator.INFINITE
                holder.imageView.setCacheComposition(false)
                holder.imageView.playAnimation()
            } else {
                holder.imageView.setImageResource(dataList[position].imageRes)
            }
        }

        override fun getItemCount(): Int = dataList.size

        override fun onViewRecycled(holder: ViewHolder) {
            super.onViewRecycled(holder)
            holder.imageView.onRelease()
        }
    }
}