/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/11/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.privacypolicy.info.html

import android.graphics.Color
import android.graphics.Typeface
import android.text.Editable
import android.text.Html
import android.text.Html.ImageGetter
import android.text.Html.TagHandler
import android.text.Layout
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AlignmentSpan
import android.text.style.BackgroundColorSpan
import android.text.style.BulletSpan
import android.text.style.ForegroundColorSpan
import android.text.style.ParagraphStyle
import android.text.style.QuoteSpan
import android.text.style.RelativeSizeSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.text.style.SubscriptSpan
import android.text.style.SuperscriptSpan
import android.text.style.TypefaceSpan
import android.text.style.URLSpan
import android.text.style.UnderlineSpan
import com.soundrecorder.base.utils.DebugUtil
import org.ccil.cowan.tagsoup.Parser
import org.xml.sax.Attributes
import org.xml.sax.ContentHandler
import org.xml.sax.InputSource
import org.xml.sax.Locator
import org.xml.sax.SAXException
import org.xml.sax.XMLReader
import java.io.IOException
import java.io.StringReader
import java.util.regex.Pattern

class HtmlToSpannedConverter(
    private val mSource: String,
    imageGetter: ImageGetter?,
    tagHandler: TagHandler?,
    parser: Parser,
    flags: Int
) : ContentHandler {
    companion object {
        private const val TAG = "HtmlToSpannedConverter"

        private val HEAD_IN_SIZES = floatArrayOf(1.5f, 1.4f, 1.3f, 1.2f, 1.1f, 1f)
        private const val RELATIVE_SIZE_SPAN_1_2_5 = 1.25f
        private const val RELATIVE_SIZE_SPAN_0_8 = 0.8f
        private const val NUM_8 = 8
        private const val NUM_10 = 10
        private const val NUM_16 = 16
    }

    private val mReader: XMLReader
    private val mSpannableStringBuilder: SpannableStringBuilder = SpannableStringBuilder()
    private val mImageGetter: ImageGetter?
    private val mTagHandler: TagHandler?
    private val mFlags: Int

    private val sColorMap: MutableMap<String, Int> = HashMap()
    private val sColorNameMap: HashMap<String, Int> = HashMap()

    init {
        mImageGetter = imageGetter
        mTagHandler = tagHandler
        mReader = parser
        mFlags = flags

        sColorNameMap["black"] = Color.BLACK
        sColorNameMap["darkgray"] = Color.DKGRAY
        sColorNameMap["gray"] = Color.GRAY
        sColorNameMap["lightgray"] = Color.LTGRAY
        sColorNameMap["white"] = Color.WHITE
        sColorNameMap["red"] = Color.RED
        sColorNameMap["green"] = Color.GREEN
        sColorNameMap["blue"] = Color.BLUE
        sColorNameMap["yellow"] = Color.YELLOW
        sColorNameMap["cyan"] = Color.CYAN
        sColorNameMap["magenta"] = Color.MAGENTA
        sColorNameMap["aqua"] = -0xff0001
        sColorNameMap["fuchsia"] = -0xff01
        sColorNameMap["darkgrey"] = Color.DKGRAY
        sColorNameMap["grey"] = Color.GRAY
        sColorNameMap["lightgrey"] = Color.LTGRAY
        sColorNameMap["lime"] = -0xff0100
        sColorNameMap["maroon"] = -0x800000
        sColorNameMap["navy"] = -0xffff80
        sColorNameMap["olive"] = -0x7f8000
        sColorNameMap["purple"] = -0x7fff80
        sColorNameMap["silver"] = -0x3f3f40
        sColorNameMap["teal"] = -0xff7f80

        sColorMap["darkgray"] = -0x565657
        sColorMap["gray"] = -0x7f7f80
        sColorMap["lightgray"] = -0x2c2c2d
        sColorMap["darkgrey"] = -0x565657
        sColorMap["grey"] = -0x7f7f80
        sColorMap["lightgrey"] = -0x2c2c2d
        sColorMap["green"] = -0xff8000
    }

    private var sTextAlignPattern: Pattern? = null
    private var sForegroundColorPattern: Pattern? = null
    private var sBackgroundColorPattern: Pattern? = null
    private var sTextDecorationPattern: Pattern? = null

    private val textAlignPattern: Pattern?
        get() {
            if (sTextAlignPattern == null) {
                sTextAlignPattern = Pattern.compile("(?:\\s+|\\A)text-align\\s*:\\s*(\\S*)\\b")
            }
            return sTextAlignPattern
        }
    private val foregroundColorPattern: Pattern?
        get() {
            if (sForegroundColorPattern == null) {
                sForegroundColorPattern = Pattern.compile(
                        "(?:\\s+|\\A)color\\s*:\\s*(\\S*)\\b")
            }
            return sForegroundColorPattern
        }
    private val backgroundColorPattern: Pattern?
        get() {
            if (sBackgroundColorPattern == null) {
                sBackgroundColorPattern = Pattern.compile(
                        "(?:\\s+|\\A)background(?:-color)?\\s*:\\s*(\\S*)\\b")
            }
            return sBackgroundColorPattern
        }
    private val textDecorationPattern: Pattern?
        get() {
            if (sTextDecorationPattern == null) {
                sTextDecorationPattern = Pattern.compile(
                        "(?:\\s+|\\A)text-decoration\\s*:\\s*(\\S*)\\b")
            }
            return sTextDecorationPattern
        }


    fun convert(): Spanned {
        mReader.contentHandler = this
        try {
            mReader.parse(InputSource(StringReader(mSource)))
        } catch (e: IOException) {
            DebugUtil.e(TAG, "convert IOException", e)
        } catch (e: SAXException) {
            DebugUtil.e(TAG, "convert SAXException", e)
        }

        // Fix flags and range for paragraph-type markup.
        val obj = mSpannableStringBuilder.getSpans(0, mSpannableStringBuilder.length, ParagraphStyle::class.java)
        for (anObj in obj) {
            val start = mSpannableStringBuilder.getSpanStart(anObj)
            var end = mSpannableStringBuilder.getSpanEnd(anObj)

            // If the last line of the range is blank, back off by one.
            if (end - 2 >= 0 && (mSpannableStringBuilder[end - 1] == '\n' && mSpannableStringBuilder[end - 2] == '\n')) {
                end--
            }
            if (end == start) {
                mSpannableStringBuilder.removeSpan(anObj)
            } else {
                mSpannableStringBuilder.setSpan(anObj, start, end, Spannable.SPAN_PARAGRAPH)
            }
        }
        return mSpannableStringBuilder
    }

    private fun handleStartTag(tag: String, attributes: Attributes) {
        if (tag.equals("br", ignoreCase = true)) {
            /*We don't need to handle this. TagSoup will ensure that there's a </br> for each <br>
            so we can safely emit the linebreaks when we handle the close tag.*/
        } else if (tag.equals("p", ignoreCase = true)) {
            startBlockElement(mSpannableStringBuilder, attributes, marginParagraph)
            startCssStyle(mSpannableStringBuilder, attributes)
        } else if (tag.equals("ul", ignoreCase = true)) {
            startBlockElement(mSpannableStringBuilder, attributes, marginList)
        } else if (tag.equals("li", ignoreCase = true)) {
            startLi(mSpannableStringBuilder, attributes)
        } else if (tag.equals("div", ignoreCase = true)) {
            startBlockElement(mSpannableStringBuilder, attributes, marginDiv)
        } else if (tag.equals("span", ignoreCase = true)) {
            startCssStyle(mSpannableStringBuilder, attributes)
        } else if (tag.equals("strong", ignoreCase = true)) {
            start(mSpannableStringBuilder, Bold())
        } else if (tag.equals("b", ignoreCase = true)) {
            start(mSpannableStringBuilder, Medium())
        } else if (tag.equals("em", ignoreCase = true)) {
            start(mSpannableStringBuilder, Italic())
        } else if (tag.equals("cite", ignoreCase = true)) {
            start(mSpannableStringBuilder, Italic())
        } else if (tag.equals("dfn", ignoreCase = true)) {
            start(mSpannableStringBuilder, Italic())
        } else if (tag.equals("i", ignoreCase = true)) {
            start(mSpannableStringBuilder, Italic())
        } else if (tag.equals("big", ignoreCase = true)) {
            start(mSpannableStringBuilder, Big())
        } else if (tag.equals("small", ignoreCase = true)) {
            start(mSpannableStringBuilder, Small())
        } else if (tag.equals("font", ignoreCase = true)) {
            startFont(mSpannableStringBuilder, attributes)
        } else if (tag.equals("blockquote", ignoreCase = true)) {
            startBlockquote(mSpannableStringBuilder, attributes)
        } else if (tag.equals("tt", ignoreCase = true)) {
            start(mSpannableStringBuilder, Monospace())
        } else if (tag.equals("a", ignoreCase = true)) {
            startA(mSpannableStringBuilder, attributes)
        } else if (tag.equals("u", ignoreCase = true)) {
            start(mSpannableStringBuilder, Underline())
        } else if (tag.equals("del", ignoreCase = true)) {
            start(mSpannableStringBuilder, Strikethrough())
        } else if (tag.equals("s", ignoreCase = true)) {
            start(mSpannableStringBuilder, Strikethrough())
        } else if (tag.equals("strike", ignoreCase = true)) {
            start(mSpannableStringBuilder, Strikethrough())
        } else if (tag.equals("sup", ignoreCase = true)) {
            start(mSpannableStringBuilder, Super())
        } else if (tag.equals("sub", ignoreCase = true)) {
            start(mSpannableStringBuilder, Sub())
        } else if (tag.length == 2 && tag[0].lowercaseChar() == 'h' && tag[1] >= '1' && tag[1] <= '6') {
            startHeading(mSpannableStringBuilder, attributes, tag[1].code - '1'.code)
        } else mTagHandler?.handleTag(true, tag, mSpannableStringBuilder, mReader)
    }

    private fun handleEndTag(tag: String) {
        if (tag.equals("br", ignoreCase = true)) {
            handleBr(mSpannableStringBuilder)
        } else if (tag.equals("p", ignoreCase = true)) {
            endCssStyle(mSpannableStringBuilder)
            endBlockElement(mSpannableStringBuilder)
        } else if (tag.equals("ul", ignoreCase = true)) {
            endBlockElement(mSpannableStringBuilder)
        } else if (tag.equals("li", ignoreCase = true)) {
            endLi(mSpannableStringBuilder)
        } else if (tag.equals("div", ignoreCase = true)) {
            endBlockElement(mSpannableStringBuilder)
        } else if (tag.equals("span", ignoreCase = true)) {
            endCssStyle(mSpannableStringBuilder)
        } else if (tag.equals("strong", ignoreCase = true)) {
            end(mSpannableStringBuilder, Bold::class.java, StyleSpan(Typeface.BOLD))
        } else if (tag.equals("b", ignoreCase = true)) {
            end(mSpannableStringBuilder, Medium::class.java, TypefaceSpan("sans-serif-medium"))
        } else if (tag.equals("em", ignoreCase = true)) {
            end(mSpannableStringBuilder, Italic::class.java, StyleSpan(Typeface.ITALIC))
        } else if (tag.equals("cite", ignoreCase = true)) {
            end(mSpannableStringBuilder, Italic::class.java, StyleSpan(Typeface.ITALIC))
        } else if (tag.equals("dfn", ignoreCase = true)) {
            end(mSpannableStringBuilder, Italic::class.java, StyleSpan(Typeface.ITALIC))
        } else if (tag.equals("i", ignoreCase = true)) {
            end(mSpannableStringBuilder, Italic::class.java, StyleSpan(Typeface.ITALIC))
        } else if (tag.equals("big", ignoreCase = true)) {
            end(mSpannableStringBuilder, Big::class.java, RelativeSizeSpan(RELATIVE_SIZE_SPAN_1_2_5))
        } else if (tag.equals("small", ignoreCase = true)) {
            end(mSpannableStringBuilder, Small::class.java, RelativeSizeSpan(RELATIVE_SIZE_SPAN_0_8))
        } else if (tag.equals("font", ignoreCase = true)) {
            endFont(mSpannableStringBuilder)
        } else if (tag.equals("blockquote", ignoreCase = true)) {
            endBlockquote(mSpannableStringBuilder)
        } else if (tag.equals("tt", ignoreCase = true)) {
            end(mSpannableStringBuilder, Monospace::class.java, TypefaceSpan("monospace"))
        } else if (tag.equals("a", ignoreCase = true)) {
            endA(mSpannableStringBuilder)
        } else if (tag.equals("u", ignoreCase = true)) {
            end(mSpannableStringBuilder, Underline::class.java, UnderlineSpan())
        } else if (tag.equals("del", ignoreCase = true)) {
            end(mSpannableStringBuilder, Strikethrough::class.java, StrikethroughSpan())
        } else if (tag.equals("s", ignoreCase = true)) {
            end(mSpannableStringBuilder, Strikethrough::class.java, StrikethroughSpan())
        } else if (tag.equals("strike", ignoreCase = true)) {
            end(mSpannableStringBuilder, Strikethrough::class.java, StrikethroughSpan())
        } else if (tag.equals("sup", ignoreCase = true)) {
            end(mSpannableStringBuilder, Super::class.java, SuperscriptSpan())
        } else if (tag.equals("sub", ignoreCase = true)) {
            end(mSpannableStringBuilder, Sub::class.java, SubscriptSpan())
        } else if (tag.length == 2 && tag[0].lowercaseChar() == 'h' && tag[1] >= '1' && tag[1] <= '6') {
            endHeading(mSpannableStringBuilder)
        } else mTagHandler?.handleTag(false, tag, mSpannableStringBuilder, mReader)
    }

    private val marginParagraph: Int
        get() = getMargin(HtmlConstant.FROM_HTML_SEPARATOR_LINE_BREAK_PARAGRAPH)
    private val marginHeading: Int
        get() = getMargin(HtmlConstant.FROM_HTML_SEPARATOR_LINE_BREAK_HEADING)
    private val marginListItem: Int
        get() = getMargin(HtmlConstant.FROM_HTML_SEPARATOR_LINE_BREAK_LIST_ITEM)
    private val marginList: Int
        get() = getMargin(HtmlConstant.FROM_HTML_SEPARATOR_LINE_BREAK_LIST)
    private val marginDiv: Int
        get() = getMargin(HtmlConstant.FROM_HTML_SEPARATOR_LINE_BREAK_DIV)
    private val marginBlockquote: Int
        get() = getMargin(HtmlConstant.FROM_HTML_SEPARATOR_LINE_BREAK_BLOCKQUOTE)

    /**
     * Returns the minimum number of newline characters needed before and after a given block-level
     * element.
     *
     * @param flag the corresponding option flag defined in [Html] of a block-level element
     */
    private fun getMargin(flag: Int): Int {
        return if (flag and mFlags != 0) {
            1
        } else 2
    }

    private fun startLi(text: Editable, attributes: Attributes) {
        startBlockElement(text, attributes, marginListItem)
        start(text, Bullet())
        startCssStyle(text, attributes)
    }

    private fun startBlockquote(text: Editable, attributes: Attributes) {
        startBlockElement(text, attributes, marginBlockquote)
        start(text, Blockquote())
    }

    private fun startHeading(text: Editable, attributes: Attributes, level: Int) {
        startBlockElement(text, attributes, marginHeading)
        start(text, Heading(level))
    }

    private fun startCssStyle(text: Editable, attributes: Attributes) {
        val style = attributes.getValue("", "style")
        if (style != null) {
            var m = foregroundColorPattern!!.matcher(style)
            if (m.find()) {
                val c = m.group(1)?.let { getHtmlColor(it) }
                if (c != -1 && c != null) {
                    start(text, Foreground(c or -0x1000000))
                }
            }
            m = backgroundColorPattern!!.matcher(style)
            if (m.find()) {
                val c = m.group(1)?.let { getHtmlColor(it) }
                if (c != -1 && c != null) {
                    start(text, Background(c or -0x1000000))
                }
            }
            m = textDecorationPattern!!.matcher(style)
            if (m.find()) {
                val textDecoration = m.group(1)
                if (textDecoration.equals("line-through", ignoreCase = true)) {
                    start(text, Strikethrough())
                }
            }
        }
    }

    private fun startFont(text: Editable, attributes: Attributes) {
        val color = attributes.getValue("", "color")
        val face = attributes.getValue("", "face")
        if (!TextUtils.isEmpty(color)) {
            val c = getHtmlColor(color)
            if (c != -1) {
                start(text, Foreground(c or -0x1000000))
            }
        }
        if (!TextUtils.isEmpty(face)) {
            start(text, Font(face))
        }
    }

    private fun getHtmlColor(color: String): Int {
        if (mFlags and Html.FROM_HTML_OPTION_USE_CSS_COLORS
                == Html.FROM_HTML_OPTION_USE_CSS_COLORS) {
            val i = sColorMap[color.lowercase()]
            if (i != null) {
                return i
            }
        }
        return innerGetHtmlColor(color)
    }


    override fun setDocumentLocator(locator: Locator) {}

    @Throws(SAXException::class)
    override fun startDocument() {
    }

    @Throws(SAXException::class)
    override fun endDocument() {
    }

    @Throws(SAXException::class)
    override fun startPrefixMapping(prefix: String, uri: String) {
    }

    @Throws(SAXException::class)
    override fun endPrefixMapping(prefix: String) {
    }

    @Throws(SAXException::class)
    override fun startElement(uri: String, localName: String, qName: String, attributes: Attributes) {
        handleStartTag(localName, attributes)
    }

    @Throws(SAXException::class)
    override fun endElement(uri: String, localName: String, qName: String) {
        handleEndTag(localName)
    }

    @Throws(SAXException::class)
    override fun characters(ch: CharArray, start: Int, length: Int) {
        val sb = StringBuilder()

        /*
         * Ignore whitespace that immediately follows other whitespace;
         * newlines count as spaces.
         */for (i in 0 until length) {
            val c = ch[i + start]
            if (c == ' ' || c == '\n') {
                var pred: Char
                var len = sb.length
                if (len == 0) {
                    len = mSpannableStringBuilder.length
                    pred = if (len == 0) {
                        '\n'
                    } else {
                        mSpannableStringBuilder[len - 1]
                    }
                } else {
                    pred = sb[len - 1]
                }
                if (pred != ' ' && pred != '\n') {
                    sb.append(' ')
                }
            } else {
                sb.append(c)
            }
        }
        mSpannableStringBuilder.append(sb)
    }

    @Throws(SAXException::class)
    override fun ignorableWhitespace(ch: CharArray, start: Int, length: Int) {
    }

    @Throws(SAXException::class)
    override fun processingInstruction(target: String, data: String) {
    }

    @Throws(SAXException::class)
    override fun skippedEntity(name: String) {
    }

    private class Bold
    private class Medium
    private class Italic
    private class Underline
    private class Strikethrough
    private class Big
    private class Small
    private class Monospace
    private class Blockquote
    private class Super
    private class Sub
    private class Bullet
    private data class Font(var mFace: String)
    private data class Href(var mHref: String?)
    private data class Foreground(val mForegroundColor: Int)
    private data class Background(val mBackgroundColor: Int)
    private data class Heading(val mLevel: Int)
    private data class Newline(val mNumNewlines: Int)
    private data class Alignment(val mAlignment: Layout.Alignment)


    private fun appendNewlines(text: Editable, minNewline: Int) {
        val len = text.length
        if (len == 0) {
            return
        }
        var existingNewlines = 0
        var i = len - 1
        while (i >= 0 && text[i] == '\n') {
            existingNewlines++
            i--
        }
        for (j in existingNewlines until minNewline) {
            text.append("\n")
        }
    }

    private fun startBlockElement(text: Editable, attributes: Attributes, margin: Int) {
        if (margin > 0) {
            appendNewlines(text, margin)
            start(text, Newline(margin))
        }
        val style = attributes.getValue("", "style")
        if (style != null) {
            val m = textAlignPattern!!.matcher(style)
            if (m.find()) {
                val alignment = m.group(1)
                if (alignment.equals("start", ignoreCase = true)) {
                    start(text, Alignment(Layout.Alignment.ALIGN_NORMAL))
                } else if (alignment.equals("center", ignoreCase = true)) {
                    start(text, Alignment(Layout.Alignment.ALIGN_CENTER))
                } else if (alignment.equals("end", ignoreCase = true)) {
                    start(text, Alignment(Layout.Alignment.ALIGN_OPPOSITE))
                }
            }
        }
    }

    private fun endBlockElement(text: Editable) {
        val n = getLast(text, Newline::class.java)
        if (n != null) {
            appendNewlines(text, n.mNumNewlines)
            text.removeSpan(n)
        }
        val a = getLast(text, Alignment::class.java)
        if (a != null) {
            setSpanFromMark(text, a, AlignmentSpan.Standard(a.mAlignment))
        }
    }

    private fun handleBr(text: Editable) {
        text.append('\n')
    }

    private fun endLi(text: Editable) {
        endCssStyle(text)
        endBlockElement(text)
        end(text, Bullet::class.java, BulletSpan())
    }

    private fun endBlockquote(text: Editable) {
        endBlockElement(text)
        end(text, Blockquote::class.java, QuoteSpan())
    }

    private fun endHeading(text: Editable) {
        /*RelativeSizeSpan and StyleSpan are CharacterStyles
        Their ranges should not include the newlines at the end*/
        val h = getLast(text, Heading::class.java)
        if (h != null) {
            setSpanFromMark(text, h, RelativeSizeSpan(HEAD_IN_SIZES[h.mLevel]),
                    StyleSpan(Typeface.BOLD))
        }
        endBlockElement(text)
    }

    private fun <T> getLast(text: Spanned, kind: Class<T>): T? {
        /*
     * This knows that the last returned object from getSpans()
     * will be the most recently added.
     */
        val objs = text.getSpans(0, text.length, kind)
        return if (objs.isEmpty()) {
            null
        } else {
            objs[objs.size - 1]
        }
    }

    private fun setSpanFromMark(text: Spannable, mark: Any, vararg spans: Any) {
        val where = text.getSpanStart(mark)
        text.removeSpan(mark)
        val len = text.length
        if (where != len) {
            for (span in spans) {
                text.setSpan(span, where, len, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }
    }

    private fun start(text: Editable, mark: Any) {
        val len = text.length
        text.setSpan(mark, len, len, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
    }

    private fun end(text: Editable, kind: Class<*>, repl: Any) {
        val obj = getLast(text, kind)
        if (obj != null) {
            setSpanFromMark(text, obj, repl)
        }
    }

    private fun endCssStyle(text: Editable) {
        val s = getLast(text, Strikethrough::class.java)
        if (s != null) {
            setSpanFromMark(text, s, StrikethroughSpan())
        }
        val b = getLast(text, Background::class.java)
        if (b != null) {
            setSpanFromMark(text, b, BackgroundColorSpan(b.mBackgroundColor))
        }
        val f = getLast(text, Foreground::class.java)
        if (f != null) {
            setSpanFromMark(text, f, ForegroundColorSpan(f.mForegroundColor))
        }
    }

    private fun endFont(text: Editable) {
        val font = getLast(text, Font::class.java)
        if (font != null) {
            setSpanFromMark(text, font, TypefaceSpan(font.mFace))
        }
        val foreground = getLast(text, Foreground::class.java)
        if (foreground != null) {
            setSpanFromMark(text, foreground,
                    ForegroundColorSpan(foreground.mForegroundColor))
        }
    }

    private fun startA(text: Editable, attributes: Attributes) {
        val href = attributes.getValue("", "href")
        start(text, Href(href))
    }

    private fun endA(text: Editable) {
        val h = getLast(text, Href::class.java)
        if (h?.mHref != null) {
            setSpanFromMark(text, h, URLSpan(h.mHref))
        }
    }

    fun innerGetHtmlColor(color: String): Int {
        val i = sColorNameMap[color.lowercase()]
        return i
                ?: try {
                    convertValueToInt(color, -1)
                } catch (nfe: NumberFormatException) {
                    -1
                }
    }

    fun convertValueToInt(charSeq: CharSequence?, defaultValue: Int): Int {
        if (null == charSeq) return defaultValue
        val nm = charSeq.toString()
        /* XXX This code is copied from Integer.decode() so we don't
        have to instantiate an Integer! */
        var sign = 1
        var index = 0
        val len = nm.length
        var base = NUM_10
        if ('-' == nm[0]) {
            sign = -1
            index++
        }
        if ('0' == nm[index]) {
            //  Quick check for a zero by itself
            if (index == len - 1) return 0
            val c = nm[index + 1]
            if ('x' == c || 'X' == c) {
                index += 2
                base = NUM_16
            } else {
                index++
                base = NUM_8
            }
        } else if ('#' == nm[index]) {
            index++
            base = NUM_16
        }
        return nm.substring(index).toInt(base) * sign
    }
}