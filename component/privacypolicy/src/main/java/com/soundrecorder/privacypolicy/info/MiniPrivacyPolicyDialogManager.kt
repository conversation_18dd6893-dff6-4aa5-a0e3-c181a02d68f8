/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniPrivacyPolicyDialogManager
 * Description:
 * Version: 1.0
 * Date: 2023/8/18
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/8/18 1.0 create
 */
package com.soundrecorder.privacypolicy.info

import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDialog
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.statement.COUIFullPageStatement
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.common.dialog.setOnBackPressedListener
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener
import com.soundrecorder.privacypolicy.R

class MiniPrivacyPolicyDialogManager(
    context: AppCompatActivity,
    resultCallback: IPrivacyPolicyResultListener?
) : PrivacyPolicyDialogManager(context, resultCallback) {

    override fun createDialog(
        title: String,
        message: CharSequence,
        protocol: CharSequence,
        ok: String,
        cancel: String,
        clickOk: () -> Unit,
        clickCancel: () -> Unit,
        clickBack: () -> Unit,
        cancelable: Boolean
    ): AppCompatDialog {
        return COUIBottomSheetDialog(context, com.support.panel.R.style.DefaultBottomSheetDialog).apply {
            setIsInTinyScreen(true, false)
            setCanceledOnTouchOutside(cancelable)
            setCancelable(true)
            setIsShowInMaxHeight(true)
            behavior.isDraggable = false
            val statement = COUIFullPageStatement(context, com.support.statement.R.style.Widget_COUI_COUIFullPageStatement_Tiny).apply {
                setTitleText(title)
                appStatement.movementMethod = LinkMovementMethod.getInstance()
                val statementContent = SpannableStringBuilder(message).append("\n").append(protocol)
                setAppStatement(statementContent)
                setButtonText(ok)
                setExitButtonText(cancel)
                // talkBack播报错误，此处先使用该方法规避，后续coui控件库修复该问题后，会删除获取控件方法
                confirmButton?.contentDescription = ok
                exitButton?.contentDescription = cancel
                setButtonListener(object : COUIFullPageStatement.OnButtonClickListener {
                    override fun onBottomButtonClick() {
                        clickOk.invoke()
                    }

                    override fun onExitButtonClick() {
                        clickCancel.invoke()
                    }
                })
            }
            contentView = statement
            dragableLinearLayout.dragView.visibility = View.INVISIBLE
            dragableLinearLayout.panelBarView.visibility = View.INVISIBLE
            setOnBackPressedListener(clickBack)
            setOnCancelListener {
                clickBack.invoke()
            }
        }
    }

    override fun onClickSpan(type: Int, link: String) {
        when (link) {
            context.resources.getString(R.string.privacy_policy_privacy_policy) -> {
                resultCallback?.onClickSpan(IPrivacyPolicyDelegate.SPAN_TYPE_RECORDER_POLICY)
            }

            context.resources.getString(com.soundrecorder.common.R.string.setting_user_info_proctol_privacy) -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }
                resultCallback?.onClickSpan(IPrivacyPolicyDelegate.SPAN_TYPE_PERSONAL_POLICY)
            }

            else -> super.onClickSpan(type, link)
        }
    }
}