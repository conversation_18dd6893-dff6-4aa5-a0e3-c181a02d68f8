/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/11/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.privacypolicy.info.html

import android.text.Html.ImageGetter
import android.text.Html.TagHandler
import android.text.Spanned
import com.soundrecorder.base.utils.DebugUtil
import org.ccil.cowan.tagsoup.HTMLSchema
import org.ccil.cowan.tagsoup.Parser
import org.xml.sax.SAXNotRecognizedException
import org.xml.sax.SAXNotSupportedException

object HtmlParser {
    private const val TAG = "HtmlParser"
    private val schema = HTMLSchema()
    fun fromHtml(source: String?, flags: Int, imageGetter: ImageGetter?, tagHandler: TagHandler?): Spanned {
        val parser = Parser()
        try {
            parser.setProperty(Parser.schemaProperty, schema)
        } catch (e: SAXNotRecognizedException) {
            DebugUtil.e(TAG, "fromHtml SAXNotRecognizedException: $e")
        } catch (e: SAXNotSupportedException) {
            DebugUtil.e(TAG, "fromHtml SAXNotSupportedException: $e")
        }
        val converter = HtmlToSpannedConverter(source!!, imageGetter, tagHandler, parser, flags)
        return converter.convert()
    }
}