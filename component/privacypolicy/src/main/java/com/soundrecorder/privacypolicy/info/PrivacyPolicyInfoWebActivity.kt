/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : PrivacyPolicyInfoWebActivity.kt
 ** Description : 个人信息保护政策WebView页面展示
 ** Version     : 1.0
 ** Date        : 2024/2/2
 ** Author      : W9049609
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  YueZhen      2024/2/2      1.0        create
 ***********************************************************************/
package com.soundrecorder.privacypolicy.info

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityManager
import android.content.ActivityNotFoundException
import android.content.ClipData
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.provider.Settings
import android.view.KeyEvent
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.animation.LinearInterpolator
import android.webkit.JavascriptInterface
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.RelativeLayout
import android.window.OnBackInvokedCallback
import android.window.OnBackInvokedDispatcher
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.emptyview.COUIEmptyStateView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.anim.EffectiveAnimationView
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.NightModeUtil
import com.soundrecorder.base.utils.XORUtil
import com.soundrecorder.base.view.RootViewPersistentInsetsCallback
import com.soundrecorder.common.utils.FunctionOption.hasSupportByte
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.privacypolicy.BuildConfig
import com.soundrecorder.privacypolicy.R

class PrivacyPolicyInfoWebActivity : BaseActivity() {

    companion object {
        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "PrivacyPolicyInfoWeb"
        private const val TAG = "PrivacyPolicyInfoWebActivity"
        private const val JS_INTERFACE_NAME = "nativeApiBridge"
        private const val ZH_CN = "zh-CN"
        private const val ZH_HK = "zh-HK"
        private const val ZH_TW = "zh-TW"
        private const val EN_US = "en-US"
        private const val SCHEME_NAME = "privacy://openbridgebctivity"
        private const val XOR_KEY = 8
        private const val TYPE_OPPO = 1
        private const val TYPE_REALME = 2
        private const val TYPE_ONEPLUS = 3
        private const val ANIM_DURATION = 100L
        private const val PROGRESS_DONE = 100
        private const val MESSAGE_HIDE_LOADING = 1
        private const val MESSAGE_SHOW_LOADING = 2
    }

    private var webView: WebView? = null
    private var networkDisconnectView: COUIEmptyStateView? = null
    private var loadFailedView: COUIEmptyStateView? = null
    private var loadingLayout: RelativeLayout? = null
    private var loadingAnimView: EffectiveAnimationView? = null
    private var alphaAnimation: ObjectAnimator? = null
    private var isReceiveError = false
    private var isLoadingDone = false
    private val targetUrl by lazy { XORUtil.enOrDecrypt(BuildConfig.HOST, XOR_KEY) }

    private val handler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            when (msg.what) {
                MESSAGE_HIDE_LOADING -> alphaAnimHide(loadingLayout)
                MESSAGE_SHOW_LOADING -> alphaAnimShow(loadingLayout)
            }
        }
    }

    private val backInvokedCallback = OnBackInvokedCallback {
        if (webView?.canGoBack() == true) {
            //返回上一页面
            webView?.goBack()
        } else {
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupWebViewDataDirectorySuffix()
        setContentView(R.layout.activity_privacy_policy_info_web)
        initView()
        setUpWebView()
        loadUrlWithParams()
    }

    private fun initView() {
        initToolbar()
        loadingLayout = findViewById(R.id.loading_layout)
        loadingAnimView = findViewById(com.soundrecorder.common.R.id.loadingView)
        webView = findViewById(R.id.web_view)
        loadFailedView = findViewById(R.id.load_failed_view)
        networkDisconnectView = findViewById(R.id.network_disconnect_view)
        networkDisconnectView?.setOnButtonClickListener {
            startToWifiSetting()
        }
        loadFailedView?.setOnButtonClickListener {
            isReceiveError = false
            isLoadingDone = false
            loadFailedView?.visibility = View.GONE
            loadUrlWithParams()
        }
    }

    private fun initToolbar() {
        val toolbar = findViewById<COUIToolbar>(R.id.toolbar)
        toolbar.title = ""
        setSupportActionBar(toolbar)
        supportActionBar?.setHomeButtonEnabled(true)
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        initiateWindowInsets()
    }

    private fun initiateWindowInsets() {
        WindowCompat.setDecorFitsSystemWindows(window, false)
        val root = findViewById<View>(R.id.root_layout)
        val callback: RootViewPersistentInsetsCallback =
            object : RootViewPersistentInsetsCallback() {
                override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                    super.onApplyInsets(v, insets)
                    DebugUtil.i(TAG, "onApplyInsets")
                    TaskBarUtil.setNavigationColorOnSupportTaskBar(
                        navigationHeight = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars()).bottom,
                        activity = this@PrivacyPolicyInfoWebActivity,
                        defaultNoTaskBarColor = navigationBarColor()
                    )
                }
            }
        ViewCompat.setOnApplyWindowInsetsListener(root, callback)
    }

    private fun setupWebViewDataDirectorySuffix() {
        try {
            val processName = getProcessName(this)
            processName?.let {
                DebugUtil.d(TAG, "setupWebViewDataDirectorySuffix processName: $processName")
                WebView.setDataDirectorySuffix(it)
            }
        } catch (e: IllegalStateException) {
            DebugUtil.e(TAG, "setupWebViewDataDirectorySuffix error: $e")
        }
    }

    private fun getProcessName(context: Context?): String? {
        var result: String? = null
        context?.let {
            val activityManager: ActivityManager =
                it.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            for (processInfo in activityManager.runningAppProcesses) {
                if (processInfo.pid == android.os.Process.myPid()) {
                    result = processInfo.processName
                    break
                }
            }
        }
        return result
    }

    private fun loadUrlWithParams() {
        if (!NetworkUtils.isNetworkInvalid(this)) {
            networkDisconnectView?.visibility = View.GONE
            showLoadingUi()
            val language = when {
                LanguageUtil.isZHCN() -> ZH_CN
                LanguageUtil.isZHHK() -> ZH_HK
                LanguageUtil.isZHTW() -> ZH_TW
                else -> EN_US
            }
            // true第三方高亮,false不高亮(三方共享清单)
            val isThirdHighLight = hasSupportByte()
            // true暗色模式，false正常模式
            val isNightMode = COUIDarkModeUtil.isNightMode(this)
            // 1:oppo,2:realme,3:oneplus(参数 t)
            val productType = when {
                BaseUtil.isOPPO() -> TYPE_OPPO
                BaseUtil.isRealme() -> TYPE_REALME
                BaseUtil.isOnePlus() -> TYPE_ONEPLUS
                else -> TYPE_OPPO
            }
            val targetUrl = XORUtil.enOrDecrypt(BuildConfig.PRIVACY_POLICY_URL, XOR_KEY)
            val params =
                "?language=$language&isThirdHighLight=$isThirdHighLight&isNightMode=$isNightMode&t=$productType"
            webView?.loadUrl(targetUrl + params)
        } else {
            networkDisconnectView?.visibility = View.VISIBLE
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun setUpWebView() {
        webView?.settings?.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
        }
        webView?.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                DebugUtil.d(TAG, "onProgressChanged $newProgress")
                if (newProgress == PROGRESS_DONE && !isLoadingDone) {
                    isLoadingDone = true
                    hideLoadingUi()
                }
            }
        }
        webView?.webViewClient = object : WebViewClient() {
            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                super.onReceivedError(view, request, error)
                DebugUtil.d(TAG, "onReceivedError ${error?.description}")
                if (!isReceiveError) {
                    isReceiveError = true
                }
            }

            override fun shouldOverrideUrlLoading(
                view: WebView?,
                request: WebResourceRequest?
            ): Boolean {
                val url = request?.url?.toString()
                url?.let {
                    if (SCHEME_NAME == it) {
                        jumpToBrowser(it)
                        return true
                    } else if (!it.startsWith(targetUrl)) {
                        jumpToBrowser(it)
                        return true
                    }
                }
                return false
            }
        }
        webView?.addJavascriptInterface(GetFileDownloadUrlJsInterface(this), JS_INTERFACE_NAME)
    }

    private fun jumpToBrowser(url: String) {
        kotlin.runCatching {
            val intent = Intent()
            intent.action = Intent.ACTION_VIEW
            intent.clipData = ClipData.newPlainText(null, null)
            intent.addCategory(Intent.CATEGORY_BROWSABLE)
            intent.component = null
            intent.selector = null
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
            intent.data = Uri.parse(url)
            startActivity(intent)
        }.onFailure {
            DebugUtil.e(TAG, "jumpToBrowser error", it)
        }
    }

    private fun playAnimationView() {
        loadingAnimView?.apply {
            clearAnimation()
            if (NightModeUtil.isNightMode(this@PrivacyPolicyInfoWebActivity)) {
                setAnimation(com.soundrecorder.common.R.raw.loading_night)
            } else {
                setAnimation(com.soundrecorder.common.R.raw.loading)
            }
            playAnimation()
        }
    }

    private fun hideLoadingUi() {
        handler.sendEmptyMessage(MESSAGE_HIDE_LOADING)
        if (isReceiveError) {
            webView?.visibility = View.GONE
            networkDisconnectView?.visibility = View.GONE
            loadFailedView?.visibility = View.VISIBLE
        } else {
            loadFailedView?.visibility = View.GONE
            networkDisconnectView?.visibility = View.GONE
            webView?.visibility = View.VISIBLE
        }
    }

    private fun showLoadingUi() {
        handler.sendEmptyMessage(MESSAGE_SHOW_LOADING)
    }

    private fun alphaAnimHide(view: View?) {
        alphaAnimation = ObjectAnimator.ofFloat(view, "alpha", 1f, 0f)
        alphaAnimation?.duration = ANIM_DURATION
        alphaAnimation?.interpolator = LinearInterpolator()
        alphaAnimation?.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                loadingAnimView?.cancelAnimation()
                view?.visibility = View.GONE
            }
        })
        alphaAnimation?.start()
    }

    private fun alphaAnimShow(view: View?) {
        alphaAnimation = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        alphaAnimation?.duration = ANIM_DURATION
        alphaAnimation?.interpolator = LinearInterpolator()
        alphaAnimation?.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                view?.visibility = View.VISIBLE
                playAnimationView()
            }
        })
        alphaAnimation?.start()
    }

    private fun startToWifiSetting() {
        try {
            startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
        } catch (e: ActivityNotFoundException) {
            DebugUtil.e(TAG, "startToWifiSetting", e)
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (ClickUtils.isFastDoubleClick()) {
            DebugUtil.e(TAG, "onOptionsItemSelected() isFastDoubleClick return")
            return false
        }
        when (item.itemId) {
            android.R.id.home -> {
                if (webView?.canGoBack() == true) {
                    //返回上一页面
                    webView?.goBack()
                    return true
                } else {
                    finish()
                }
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onResume() {
        super.onResume()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            onBackInvokedDispatcher.registerOnBackInvokedCallback(OnBackInvokedDispatcher.PRIORITY_DEFAULT, backInvokedCallback)
        }
        if (networkDisconnectView?.visibility == View.VISIBLE) {
            loadUrlWithParams()
        }
    }

    override fun onStop() {
        super.onStop()
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            onBackInvokedDispatcher.unregisterOnBackInvokedCallback(backInvokedCallback)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        (webView?.parent as? ViewGroup)?.removeAllViews()
        webView?.stopLoading()
        webView?.webChromeClient = null
        webView?.clearFormData()
        webView?.clearHistory()
        webView?.clearFocus()
        webView?.destroy()
        handler.removeCallbacksAndMessages(null)
        alphaAnimation?.cancel()
        loadingAnimView?.cancelAnimation()
        alphaAnimation = null
        loadingAnimView = null
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (webView?.canGoBack() == true) {
                //返回上一页面
                webView?.goBack()
                return true
            } else {
                finish()
            }
        }
        return super.onKeyDown(keyCode, event)
    }
}

class GetFileDownloadUrlJsInterface(var activity: Activity) {
    @SuppressLint("JavascriptInterface")
    @JavascriptInterface
    fun downloadPrivacyPolicyFile(url: String?) {
        url?.let {
            kotlin.runCatching {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.addCategory(Intent.CATEGORY_BROWSABLE)
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                intent.data = Uri.parse(it)
                activity.startActivity(intent)
            }.onFailure {
                DebugUtil.e("GetFileDownloadUrl", "downloadPrivacyPolicyFile error ${it.message}")
            }
        }
    }
}