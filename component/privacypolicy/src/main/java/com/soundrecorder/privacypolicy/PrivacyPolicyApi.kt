/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.privacypolicy

import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.soundrecorder.modulerouter.privacyPolicy.IFunctionGuideDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyInterface
import com.soundrecorder.privacypolicy.functionguide.FunctionGuideDelegate
import com.soundrecorder.privacypolicy.info.PrivacyPolicyFragment
import com.soundrecorder.privacypolicy.info.PrivacyPolicyInfoFragment

object PrivacyPolicyApi : PrivacyPolicyInterface {

    override fun newPrivacyPolicyFragment(): Fragment {
        return PrivacyPolicyFragment()
    }

    override fun newPrivacyPolicyInfoFragment(type: Int): Fragment {
        return PrivacyPolicyInfoFragment.newInstance(type)
    }

    /**
     * 收集个人信息明示清单
     */
    override fun newCollectionInfoFragment(type: Int): Fragment {
        return CollectionInfoFragment.newInstance(type)
    }

    /**
     * 收集个人信息明示清单-详情页
     */
    override fun newCollectionInfoContentFragment(title: String?, type: Int, collectionType: Int): Fragment {
        return CollectionInfoContentFragment.newInstance(title, type, collectionType)
    }

    override fun newPrivacyPolicyDelegate(
        context: AppCompatActivity,
        type: Int,
        resultListener: IPrivacyPolicyResultListener?
    ): IPrivacyPolicyDelegate {
        return PrivacyPolicyDelegate(context, type, resultListener)
    }

    override fun newFunctionGuideDelegate(context: AppCompatActivity, functionClickOk: ((fromUserNotice: Boolean) -> Unit)?): IFunctionGuideDelegate {
        return FunctionGuideDelegate(context, functionClickOk)
    }
}