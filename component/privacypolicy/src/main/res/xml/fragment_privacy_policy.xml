<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:title="@string/app_name_main">

    <androidx.preference.Preference
        android:layout="@layout/preference_header"
        android:selectable="false" />

    <com.coui.appcompat.preference.COUIPreferenceCategory app:isFirstCategory="true">

        <com.coui.appcompat.preference.COUIJumpPreference
            app:isPreferenceVisible="false"
            android:key="@string/privacy_policy_settings_policy_key"
            android:title="@string/privacy_policy_privacy_policy_new" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/privacy_policy_settings_user_agreement_key"
            android:title="@string/privacy_policy_user_agreement" />

        <com.soundrecorder.privacypolicy.info.PrivacyPolicyCOUIPreference
            app:isPreferenceVisible="false"
            android:key="@string/privacy_policy_settings_withdrawn_key"
            android:title="@string/privacy_policy_privacy_policy_close" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>

</androidx.preference.PreferenceScreen>