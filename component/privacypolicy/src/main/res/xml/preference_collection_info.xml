<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:title="@string/app_name_main">

    <androidx.preference.Preference
        android:layout="@layout/preference_header"
        android:selectable="false" />

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:key="key_pref_record_audio_category"
        app:isFirstCategory="true"
        android:title="@string/collection_info_media_files">

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/key_pref_collection_record_audio"
            android:title="@string/collection_info_audio" />

    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <!--设备信息-->
    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:title="@string/collection_info_device_info">

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/key_pref_collection_device_duid"
            android:title="@string/collection_info_device_duid" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/key_pref_collection_device_brand"
            android:title="@string/collection_info_device_brand" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/key_pref_collection_os_version"
            android:title="@string/collection_info_device_os_version" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/key_pref_collection_device_model"
            android:title="@string/collection_info_device_model" />

    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:title="@string/collection_info_contact_details">

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/key_pref_collection_contact_qq"
            android:title="@string/collection_info_contact_qq_phone_email" />

    </com.coui.appcompat.preference.COUIPreferenceCategory>

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:title="@string/collection_info_data">

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/key_pref_collection_data_error_log_report"
            android:title="@string/collection_info_data_error_log_report" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/key_pref_collection_data_buried"
            android:title="@string/collection_info_data_buried" />

        <com.coui.appcompat.preference.COUIJumpPreference
            android:key="@string/key_pref_collection_data_feedback_content"
            android:title="@string/collection_info_data_feedback_text_image" />

    </com.coui.appcompat.preference.COUIPreferenceCategory>

</androidx.preference.PreferenceScreen>