<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:nestedScrollingEnabled="true"
        android:overScrollMode="always"
        android:paddingHorizontal="@dimen/privacy_policy_margin"
        android:paddingTop="@dimen/dp50"
        android:scrollbarSize="0dp"
        android:scrollbars="none">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />

    </ScrollView>

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/common_background_color"
        app:elevation="0dp"
        app:layout_behavior="@string/privacy_behavior">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp50"
            android:minHeight="@dimen/dp50"
            app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

        <View
            android:id="@+id/divider_line"
            android:layout_width="match_parent"
            android:layout_height="@dimen/divider_background_height"
            android:layout_gravity="center_horizontal"
            android:layout_marginLeft="@dimen/common_margin"
            android:layout_marginRight="@dimen/common_margin"
            android:alpha="0"
            android:background="?attr/couiColorDivider"
            android:forceDarkAllowed="false"
            tools:ignore="UnusedAttribute" />

    </com.google.android.material.appbar.AppBarLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>