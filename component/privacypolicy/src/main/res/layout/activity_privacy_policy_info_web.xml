<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/couiColorBackground">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/couiColorBackground"
        app:elevation="0dp"
        app:layout_constraintTop_toTopOf="parent">

        <com.coui.appcompat.toolbar.COUIToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp50"
            android:minHeight="@dimen/dp50"
            app:supportTitleTextAppearance="@style/textAppearanceSecondTitle" />

    </com.google.android.material.appbar.AppBarLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBarLayout">

        <WebView
            android:id="@+id/web_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <include
            android:id="@+id/loading_layout"
            layout="@layout/loading_animation_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

    </FrameLayout>

    <com.coui.appcompat.emptyview.COUIEmptyStateView
        android:id="@+id/network_disconnect_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:actionText="@string/app_name_settings"
        app:anim_autoPlay="true"
        app:anim_rawRes="@raw/no_network"
        app:layout_constraintBottom_toBottomOf="parent"
        app:titleText="@string/network_disconnect" />

    <com.coui.appcompat.emptyview.COUIEmptyStateView
        android:id="@+id/load_failed_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:actionText="@string/refresh"
        app:anim_autoPlay="true"
        app:anim_rawRes="@raw/no_network"
        app:layout_constraintBottom_toBottomOf="parent"
        app:titleText="@string/load_failed_tips" />

</androidx.constraintlayout.widget.ConstraintLayout>