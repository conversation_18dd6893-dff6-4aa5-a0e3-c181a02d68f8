<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.scrollview.COUINestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/dp24">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/guide_tips_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.oplus.anim.EffectiveAnimationView
                android:id="@+id/pager_item_im"
                android:layout_width="match_parent"
                android:layout_height="@dimen/dp284"
                android:forceDarkAllowed="false" />

            <View
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@drawable/component_direct_guide_dialog_image_mask" />
        </FrameLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/guide_tips_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/dp24"
            android:layout_marginBottom="@dimen/dp12"
            android:fontFamily="sans-serif-medium"
            android:gravity="center_horizontal"
            android:textColor="?attr/couiColorPrimaryNeutral"
            android:textSize="@dimen/sp18" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/guide_tips_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:fontFamily="sans-serif"
            android:gravity="center_horizontal"
            android:textColor="?attr/couiColorSecondNeutral"
            android:textSize="@dimen/sp14" />

    </androidx.appcompat.widget.LinearLayoutCompat>

</com.coui.appcompat.scrollview.COUINestedScrollView>
