<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="36dp"
    android:height="36dp"
    android:viewportWidth="36"
    android:viewportHeight="36">
  <path
      android:pathData="M18,0L18,0A18,18 0,0 1,36 18L36,18A18,18 0,0 1,18 36L18,36A18,18 0,0 1,0 18L0,18A18,18 0,0 1,18 0z"
      android:fillColor="#EB3B2F"/>
  <path
      android:pathData="M24.5,18.675V24.028C24.5,25.67 23.17,27 21.528,27V27H19.301H12.576C11.499,27 10.961,27 10.55,26.791C10.188,26.606 9.894,26.312 9.71,25.95C9.5,25.539 9.5,25.001 9.5,23.924V12.076C9.5,10.999 9.5,10.461 9.71,10.05C9.894,9.688 10.188,9.394 10.55,9.21C10.961,9 11.499,9 12.576,9H17.441H20.691"
      android:strokeWidth="1.6"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M13,12.5L18,12.5"
      android:strokeAlpha="0.4"
      android:strokeWidth="1.6"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.4"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M13,16L21,16"
      android:strokeWidth="1.6"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M20,20L21,20"
      android:strokeAlpha="0.4"
      android:strokeWidth="1.6"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.4"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M13,20L17,20"
      android:strokeWidth="1.6"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M13,23.5L21,23.5"
      android:strokeAlpha="0.4"
      android:strokeWidth="1.6"
      android:fillColor="#00000000"
      android:strokeColor="#ffffff"
      android:fillAlpha="0.4"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M24.368,8.062C24.437,7.979 24.563,7.979 24.632,8.062C24.653,8.087 24.673,8.145 24.712,8.263C24.911,8.857 25.01,9.155 25.143,9.422C25.559,10.261 26.239,10.941 27.078,11.358C27.345,11.49 27.642,11.59 28.236,11.788C28.354,11.828 28.414,11.847 28.439,11.868C28.521,11.937 28.521,12.063 28.439,12.132C28.414,12.153 28.354,12.173 28.236,12.212C27.642,12.41 27.345,12.51 27.078,12.643C26.239,13.059 25.559,13.739 25.143,14.578C25.01,14.845 24.911,15.143 24.712,15.737C24.673,15.855 24.653,15.913 24.632,15.939C24.563,16.021 24.437,16.021 24.368,15.939C24.347,15.914 24.327,15.855 24.288,15.737C24.09,15.143 23.99,14.845 23.858,14.578C23.441,13.739 22.761,13.059 21.922,12.643C21.655,12.51 21.357,12.411 20.763,12.212C20.645,12.173 20.587,12.153 20.562,12.132C20.479,12.063 20.479,11.937 20.562,11.868C20.587,11.847 20.645,11.827 20.763,11.788C21.357,11.59 21.655,11.49 21.922,11.358C22.761,10.941 23.441,10.261 23.858,9.422C23.99,9.155 24.09,8.857 24.288,8.263C24.327,8.145 24.347,8.087 24.368,8.062Z"
      android:fillColor="#ffffff"/>
</vector>
