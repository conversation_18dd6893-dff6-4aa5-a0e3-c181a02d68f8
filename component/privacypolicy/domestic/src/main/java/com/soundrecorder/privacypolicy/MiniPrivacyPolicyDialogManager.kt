/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MiniPrivacyPolicyDialogManager
 * Description:
 * Version: 1.0
 * Date: 2023/8/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/18 1.0 create
 */
package com.soundrecorder.privacypolicy

import android.os.Build
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.view.KeyEvent
import android.view.View
import android.window.OnBackInvokedDispatcher
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDialog
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.statement.COUIFullPageStatement
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.common.dialog.CustomBackInvokeCallBack
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener

class MiniPrivacyPolicyDialogManager(
    context: AppCompatActivity,
    resultCallback: IPrivacyPolicyResultListener?
) : PrivacyPolicyDialogManager(context, resultCallback) {
    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private val backInvokedCallback = CustomBackInvokeCallBack()

    override fun createDialog(
        title: String,
        message: CharSequence,
        protocol: CharSequence,
        ok: String,
        cancel: String,
        clickOk: () -> Unit,
        clickCancel: () -> Unit,
        clickBack: () -> Unit,
        cancelable: Boolean
    ): AppCompatDialog {
        return COUIBottomSheetDialog(context, com.support.panel.R.style.DefaultBottomSheetDialog).apply {
            setIsInTinyScreen(true, false)
            setCanceledOnTouchOutside(cancelable)
            setCancelable(true)
            setIsShowInMaxHeight(true)
            behavior.isDraggable = false
            val statement = COUIFullPageStatement(context, com.support.statement.R.style.Widget_COUI_COUIFullPageStatement_Tiny).apply {
                setTitleText(title)
                appStatement.movementMethod = LinkMovementMethod.getInstance()
                val statementContent = SpannableStringBuilder(message).append("\n").append(protocol)
                setAppStatement(statementContent)
                setButtonText(ok)
                setExitButtonText(cancel)
                // talkBack播报错误，此处先使用该方法规避，后续coui控件库修复该问题后，会删除获取控件方法
                confirmButton?.contentDescription = ok
                exitButton?.contentDescription = cancel
                setButtonListener(object : COUIFullPageStatement.OnButtonClickListener {
                    override fun onBottomButtonClick() {
                        clickOk.invoke()
                    }

                    override fun onExitButtonClick() {
                        clickCancel.invoke()
                    }
                })
            }
            contentView = statement
            dragableLinearLayout.dragView.visibility = View.INVISIBLE
            dragableLinearLayout.panelBarView.visibility = View.INVISIBLE
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                backInvokedCallback.callback = clickBack
                onBackInvokedDispatcher.registerOnBackInvokedCallback(OnBackInvokedDispatcher.PRIORITY_DEFAULT, backInvokedCallback)
            } else {
                setOnKeyListener { _, keyCode, event ->
                    val isBack = keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP
                    if (isShowing && isBack) {
                        clickBack.invoke()
                    }
                    false
                }
            }
            setOnDismissListener {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    window?.onBackInvokedDispatcher?.unregisterOnBackInvokedCallback(backInvokedCallback)
                }
            }
            setOnCancelListener {
                clickBack.invoke()
            }
        }
    }

    override fun onClickSpan(type: Int, link: String) {
        when (link) {
            context.resources.getString(R.string.privacy_policy_privacy_policy) -> {
                resultCallback?.onClickSpan(IPrivacyPolicyDelegate.SPAN_TYPE_RECORDER_POLICY)
            }

            context.resources.getString(com.soundrecorder.common.R.string.setting_user_info_proctol_privacy) -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }
                resultCallback?.onClickSpan(IPrivacyPolicyDelegate.SPAN_TYPE_PERSONAL_POLICY)
            }

            else -> super.onClickSpan(type, link)
        }
    }
}