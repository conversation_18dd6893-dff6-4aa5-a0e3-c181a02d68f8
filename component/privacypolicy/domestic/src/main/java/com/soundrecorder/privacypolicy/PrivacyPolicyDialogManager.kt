/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyInfoManager
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.privacypolicy

import android.content.Context
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDialog
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.statement.COUIStatementClickableSpan
import com.coui.appcompat.statement.COUIUserStatementDialog
import com.coui.appcompat.statement.COUIUserStatementDialog.COUIUserStatementListItem
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity.Companion.syncPrivacyDialogShowingType
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.common.dialog.NormalStatementBuilder
import com.soundrecorder.common.dialog.setOnBackPressedListener
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.permission.PermissionUtils.setAllFuncTypePermission
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.convertService.ConvertSupportAction
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_AI_PRIVACY_GUIDE
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_FROM_CARD
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_SEARCH
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_FEEDBACK
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_SMART_SHORTHAND
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_SUMMARY_MIND
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_USER_NOTICE
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_USER_NOTICE_DEFAULT
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.translate.AIAsrManagerAction
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.summary.util.AIUnitApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

open class PrivacyPolicyDialogManager(
    protected val context: AppCompatActivity,
    protected val resultCallback: IPrivacyPolicyResultListener?
) {
    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val settingApi by lazy {
        Injector.injectFactory<SettingInterface>()
    }

    private val smartNameAction by lazy {
        Injector.injectFactory<SmartNameAction>()
    }

    private val convertAction by lazy {
        Injector.injectFactory<ConvertSupportAction>()
    }

    private val aiAsrManagerAction by lazy {
        Injector.injectFactory<AIAsrManagerAction>()
    }

    companion object {
        private const val TAG = "PrivacyPolicyDialogManager"

        @JvmStatic
        fun newInstance(
            type: Int = IPrivacyPolicyDelegate.POLICY_TYPE_COMMON,
            context: AppCompatActivity,
            resultListener: IPrivacyPolicyResultListener?
        ): PrivacyPolicyDialogManager {
            return when (type) {
                IPrivacyPolicyDelegate.POLICY_TYPE_COMMON -> PrivacyPolicyDialogManager(context, resultListener)
                IPrivacyPolicyDelegate.POLICY_TYPE_MINI -> MiniPrivacyPolicyDialogManager(context, resultListener)
                else -> PrivacyPolicyDialogManager(context, resultListener)
            }
        }
    }

    private val dialogMap = mutableMapOf<Int, AppCompatDialog>()

    /*记录当前最后一次显示的用户弹窗Type*/
    private var currentShowingDialogType: Int? = null
    private val showDialogKeys = arrayListOf<Int>()
    private val statementView by lazy {
        View(context).apply {
            setBackgroundColor(
                ContextCompat.getColor(
                    context,
                    com.support.appcompat.R.color.coui_color_white
                )
            )
        }
    }
    private var isReCreate = false
    var hasConvertPermission = false
    private var disableDialog: AlertDialog? = null

    private fun addStatementView() {
        if (statementView.parent == null) {
            context.addContentView(
                statementView, ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            )
        }
    }


    private fun isRegionAndSupportCloud(): Boolean = cloudKitApi?.isSupportCloudArea() == true && cloudKitApi?.isSupportSwitch() == true

    private fun removeStatementView() {
        (statementView.parent as? ViewGroup)?.removeView(statementView)
    }

    /**
     * 是否支持撤销个保法权限
     */
    private fun canShowWithdrawnPermissionConvert(context: Context): Boolean {
        return PermissionUtils.isStatementConvertGranted(context) ||
                (isRegionAndSupportCloud() && cloudKitApi?.isStatementCloudGranted(context) == true)
    }

    @Suppress("LongParameterList")
    protected open fun createDialog(
        title: String,
        message: CharSequence,
        protocol: CharSequence,
        ok: String,
        cancel: String,
        clickOk: () -> Unit,
        clickCancel: () -> Unit,
        clickBack: () -> Unit = {},
        cancelable: Boolean = false
    ): AppCompatDialog {
        val statementBuilder = NormalStatementBuilder(context).apply {
            setTitle(title)
            setDescribeMessage(message)
            setDetailsLink(protocol)
            setPositiveButton(ok) { _, _ -> clickOk.invoke() }
            setNegativeButton(cancel) { _, _ -> clickCancel.invoke() }
        }

        return statementBuilder.show().apply {
            bottomDialog(this, cancelable, clickBack)
        }
    }

    @Suppress("LongParameterList")
    protected open fun createUserNoticeDialog(
        title: String,
        message: CharSequence,
        protocol: CharSequence,
        ok: String,
        cancel: String,
        clickOk: () -> Unit,
        clickCancel: () -> Unit,
        clickBack: () -> Unit,
        isUserNoticeMain: Boolean = false
    ): COUIBottomSheetDialog {
        val statementDialogType = arrayListOf(TYPE_USER_NOTICE_DEFAULT, TYPE_USER_NOTICE, TYPE_PERMISSION_USER_NOTICE_UPDATE)
        val statementListItem = if (currentShowingDialogType in statementDialogType) {
            createStatementItems()
        } else {
            null
        }
        val dialog = COUIUserStatementDialog(context).apply {
            titleText = title
            bottomButtonText = ok
            exitButtonText = cancel
            statement = message
            protocolText = protocol
            listItems = statementListItem
            onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onBottomButtonClick() {
                    clickOk.invoke()
                }

                override fun onExitButtonClick() {
                    clickCancel.invoke()
                }
            }
            setOnCancelListener { clickBack.invoke() }
            setOnBackPressedListener(clickBack)
        }
        if (isUserNoticeMain) {
            dialog.logoDrawable = context.getDrawable(com.soundrecorder.common.R.drawable.ic_user_notice)
            dialog.appMessage = context.resources.getString(com.soundrecorder.common.R.string.audio_recording_playback)
            dialog.appName = context.getString(com.soundrecorder.common.R.string.app_name_main)
        } else {
            dialog.setIsShowInMaxHeight(false)
        }
        return dialog
    }

    private fun createStatementItems(): MutableList<COUIUserStatementListItem> {
        return mutableListOf<COUIUserStatementListItem>().apply {
            add(
                COUIUserStatementListItem(
                    ContextCompat.getDrawable(context, R.drawable.intro_convert_text),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_convert_text_title),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_convert_text_desc)
                )
            )
            add(
                COUIUserStatementListItem(
                    ContextCompat.getDrawable(context, R.drawable.intro_record_assistant),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_record_assistant_title),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_record_assistant_desc)
                )
            )
            add(
                COUIUserStatementListItem(
                    ContextCompat.getDrawable(context, R.drawable.intro_text_summary),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_text_summary_title),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_text_summary_desc)
                )
            )
            add(
                COUIUserStatementListItem(
                    ContextCompat.getDrawable(context, R.drawable.intro_content_share),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_content_share_title),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_content_share_desc)
                )
            )
        }
    }

    private fun bottomDialog(dialog: AppCompatDialog, cancelable: Boolean, clickBack: () -> Unit) {
        if (dialog !is AlertDialog) {
            return
        }
        dialog.apply {
            setCanceledOnTouchOutside(cancelable)
            setCancelable(true)
            setOnBackPressedListener(clickBack)
            setOnCancelListener {
                clickBack.invoke()
            }
        }
    }

    private fun doDismiss(type: Int, isPerformAnim: Boolean = true, remove: Boolean = false) {
        val dialog = dialogMap[type] ?: return
        if (dialog.isShowing) {
            if (dialog is COUIBottomSheetDialog) {
                dialog.dismiss(isPerformAnim)
            } else {
                dialog.dismiss()
            }
        }
        /*重建、每次onresume隐藏再显示都是用的该map,所以弹窗消失不代表没有弹窗，不能随便remove*/
        if (remove) {
            dialogMap.remove(type)
        }
    }

    private fun AppCompatDialog.addAndShowDialog(type: Int) {
        show()
        DebugUtil.d(TAG, "addAndShowDialog: dialog type = $type, show() has been called!")
        doDismiss(type)
        dialogMap[type] = this
        syncPrivacyDialogShowingType = type
    }

    /**
     * 用户须知-总概览-欢迎使用
     */
    private fun doShowDialogUserNotice() {
        val title = context.resources.getString(com.soundrecorder.common.R.string.welcome_use)
        val message = createSpan(
            TYPE_USER_NOTICE,
            if (BaseUtil.isAndroidTOrLater) {
                R.string.privacy_policy_user_notice_all_t_v6
            } else {
                R.string.privacy_policy_user_notice_all_v2
            },
            R.string.privacy_policy_basic,
        )

        val protocol = createSpan(
            TYPE_USER_NOTICE,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info_v2,
            R.string.privacy_policy_privacy_policy,
            com.soundrecorder.common.R.string.sound_record_user_agreement_span
        )

        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel_new)
        val dialog = createUserNoticeDialog(
            title = title,
            message = message,
            protocol = protocol,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(TYPE_USER_NOTICE)
                BuryingPoint.doClickOkOnUserNoticeDialog()
                handleUserStatementChange(true)
                onClickFromPrivacyPolicy(
                    TYPE_USER_NOTICE,
                    arrayListOf(TYPE_PERMISSION_SMART_SHORTHAND, TYPE_PERMISSION_SUMMARY_MIND, TYPE_PERMISSION_CLOUD, TYPE_PERMISSION_FEEDBACK)
                )
                PermissionUtils.setUserStatementGranted(true)
            },
            clickCancel = {
                onClickCancel(TYPE_USER_NOTICE)
                doShowDialog(TYPE_USER_NOTICE_BASIC_STILL)
                doDismiss(TYPE_USER_NOTICE)
            },
            clickBack = {
                context.finish()
            },
            isUserNoticeMain = true
        )
        dialog.addAndShowDialog(TYPE_USER_NOTICE)
    }

    /**
     * 用户须知-使用基本功能
     */
    private fun doShowDialogUserNoticeBasic() {
        val title = context.resources.getString(R.string.privacy_policy_user_notice_basic_title)
        val messageId = if (BaseUtil.isAndroidTOrLater) {
            R.string.privacy_policy_user_notice_basic_t_v5
        } else {
            R.string.privacy_policy_user_notice_basic_v3
        }
        val message = context.resources.getString(messageId)
        val protocol = createSpan(
            TYPE_USER_NOTICE_BASIC,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info_v2,
            R.string.privacy_policy_privacy_policy,
            com.soundrecorder.common.R.string.sound_record_user_agreement_span
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_start_use)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.back)
        val dialog = createDialog(
            title = title,
            message = message,
            protocol = protocol,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(TYPE_USER_NOTICE_BASIC)
                BuryingPoint.doClickOkOnUserNoticeBasicDialog()
                onClickFromPrivacyPolicy(TYPE_USER_NOTICE_BASIC, emptyList())
            },
            clickCancel = {
                doShowDialog(TYPE_USER_NOTICE)
                doDismiss(TYPE_USER_NOTICE_BASIC)
            },
            clickBack = {
                doShowDialog(TYPE_USER_NOTICE)
                doDismiss(TYPE_USER_NOTICE_BASIC)
            }
        )
        dialog.addAndShowDialog(TYPE_USER_NOTICE_BASIC)
    }

    /**
     * 用户须知-仍然使用基本功能
     * 仍可以使用基本功能
     */
    private fun doShowDialogUserNoticeStill() {
        val title = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_deny_title)
        val messageId = if (BaseUtil.isAndroidTOrLater) {
            R.string.privacy_policy_user_notice_basic_still_t_v5
        } else {
            R.string.privacy_policy_user_notice_basic_still_v3
        }
        val message = context.resources.getString(messageId)
        val protocol = createSpan(
            TYPE_USER_NOTICE_BASIC_STILL,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info_v2,
            R.string.privacy_policy_privacy_policy,
            com.soundrecorder.common.R.string.sound_record_user_agreement_span
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_start_use)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel)
        val dialog = createDialog(
            title = title,
            message = message,
            protocol = protocol,
            ok = ok,
            cancel = cancel,
            clickOk = {
                DebugUtil.e(TAG, "doShowDialogUserNoticeStill clickOk")
                BuryingPoint.doClickOkOnUserNoticeBasicStillDialog()
                doDismiss(TYPE_USER_NOTICE_BASIC_STILL)
                onClickFromPrivacyPolicy(TYPE_USER_NOTICE_BASIC_STILL, emptyList())
            },
            clickCancel = {
                DebugUtil.e(TAG, "doShowDialogUserNoticeStill clickCancel")
                context.finish()
            },
            clickBack = {
                DebugUtil.e(TAG, "doShowDialogUserNoticeStill clickBack")
                doShowDialog(TYPE_USER_NOTICE)
                doDismiss(TYPE_USER_NOTICE_BASIC_STILL)
            }
        )
        dialog.addAndShowDialog(TYPE_USER_NOTICE_BASIC_STILL)
    }

    /**
     * 用户须知-轻量
     */
    private fun doShowDialogUserNoticeLight() {
        val title = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_statement_title)
        val message = context.resources.getString(
            if (BaseUtil.isAndroidTOrLater) {
                R.string.privacy_policy_user_notice_for_light_t_v2
            } else {
                R.string.privacy_policy_user_notice_for_light
            }
        )
        val protocolText = createSpan(
            TYPE_USER_NOTICE_LIGHT_OS,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_start_use)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel)
        val dialog = createUserNoticeDialog(
            title = title,
            message = message,
            protocol = protocolText,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(TYPE_USER_NOTICE_LIGHT_OS)
                handleUserStatementChange(true)
                onClickFromPrivacyPolicy(
                    TYPE_USER_NOTICE_LIGHT_OS,
                    arrayListOf(TYPE_PERMISSION_SMART_SHORTHAND, TYPE_PERMISSION_SUMMARY_MIND, TYPE_PERMISSION_CLOUD, TYPE_PERMISSION_FEEDBACK),
                )
            },
            clickCancel = {
                context.finish()
            },
            clickBack = {
                context.finish()
            },
            isUserNoticeMain = true
        )
        dialog.addAndShowDialog(TYPE_USER_NOTICE_LIGHT_OS)
    }

    /**
     * 用户须知-升级弹窗
     */
    private fun doShowDialogUserNoticeUpdate() {
        val title = context.resources.getString(com.soundrecorder.common.R.string.welcome_use)
        val message = context.resources.getString(R.string.user_notice_update_content_for_ck_v5)
        val protocol = createSpan(
            TYPE_PERMISSION_USER_NOTICE_UPDATE,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info_v2,
            R.string.privacy_policy_privacy_policy,
            com.soundrecorder.common.R.string.sound_record_user_agreement_span
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel_new)
        val dialog = createUserNoticeDialog(
            title = title,
            message = message,
            protocol = protocol,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(TYPE_PERMISSION_USER_NOTICE_UPDATE)
                //开启全量功能+网络权限且弹窗消失
                onClickFromPrivacyPolicy(
                    TYPE_PERMISSION_USER_NOTICE_UPDATE,
                    arrayListOf(TYPE_PERMISSION_SMART_SHORTHAND, TYPE_PERMISSION_CLOUD, TYPE_PERMISSION_SUMMARY_MIND, TYPE_PERMISSION_FEEDBACK)
                )
                PermissionUtils.setUserStatementUpdateGranted(true)
            },
            clickCancel = {
                doDismiss(TYPE_PERMISSION_USER_NOTICE_UPDATE)
                //进入应用   关闭云服务
                onClickCancel(TYPE_PERMISSION_USER_NOTICE_UPDATE)
            },
            clickBack = {
                context.finish()
            },
            isUserNoticeMain = true
        )
        dialog.addAndShowDialog(TYPE_PERMISSION_USER_NOTICE_UPDATE)
    }

    /**
     * 转文本声明-转文本/云同步
     * 同意此功能对信息的处理
     */
    private fun doShowDialogPermissionConvert(type: Int, pageFrom: Int?) {
        val title = context.resources.getString(
            R.string.privacy_policy_convert_permission_notice_title
        )
        val message = context.resources.getString(R.string.privacy_policy_convert_permission_notice_v2)
        val protocol = createSpan(
            type,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel_new)
        val dialog = createDialog(
            title = title,
            message = message,
            protocol = protocol,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(type)
                if (type == TYPE_PERMISSION_CLOUD) {
                    cloudKitApi?.setCloudGrantedStatus(context)
                } else {
                    // 转文本的同意此功能更对信息的处理
                    PermissionUtils.setConvertGrantedStatus(BaseApplication.getAppContext())
                    PermissionUtils.setNetWorkGrantedStatus(BaseApplication.getAppContext(), true)
                    BuryingPoint.doClickOkOnConvertPermissionDialog()
                }
                PermissionUtils.setNetWorkGrantedStatus(context, true)
                resultCallback?.onPrivacyPolicySuccess(type, pageFrom)
            },
            clickCancel = {
                doDismiss(type)
                resultCallback?.onPrivacyPolicyFail(type, pageFrom)
            },
            clickBack = {
                doDismiss(type)
                resultCallback?.onPrivacyPolicyFail(type, pageFrom)
            },
            cancelable = type != TYPE_PERMISSION_CONVERT_FROM_CARD
        )
        dialog.addAndShowDialog(type)
    }

    /**
     * 撤销转文本
     */
    private fun doShowDialogPermissionConvertWithdrawn(isRegionAndSupportCloud: Boolean = false) {
        DebugUtil.d(TAG, "doShowDialogPermissionConvertWithdrawn, supportCloud=$isRegionAndSupportCloud")
        val title = context.resources.getString(
            R.string.privacy_policy_privacy_policy_close
        )
        val messageId = if (isRegionAndSupportCloud) {
            R.string.privacy_policy_privacy_policy_close_notice_v2_with_ck_v3
        } else {
            R.string.privacy_policy_privacy_policy_close_notice_v2
        }

        val message = context.resources.getString(messageId)
        val protocol = createSpan(
            TYPE_PERMISSION_CONVERT_WITHDRAWN,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info_v2,
            R.string.privacy_policy_privacy_policy,
            com.soundrecorder.common.R.string.sound_record_user_agreement_span
        )
        val ok = context.resources.getString(R.string.privacy_policy_privacy_policy_not_withdrawn)
        val cancel = context.resources.getString(R.string.privacy_policy_privacy_policy_withdrawn)
        val dialog = createDialog(
            title = title,
            message = message,
            protocol = protocol,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(TYPE_PERMISSION_CONVERT_WITHDRAWN)
            },
            clickCancel = {
                resultCallback?.onPrivacyPolicyFail(TYPE_PERMISSION_CONVERT_WITHDRAWN)
                handleUserStatementChange(false)
                BuryingPoint.doClickOkOnConvertPermissionWithdrawnDialog()
                doDismiss(TYPE_PERMISSION_CONVERT_WITHDRAWN)
            },
            clickBack = {
                doDismiss(TYPE_PERMISSION_CONVERT_WITHDRAWN)
            },
            cancelable = true
        )
        dialog.addAndShowDialog(TYPE_PERMISSION_CONVERT_WITHDRAWN)
    }

    /**
     * 转文本声明-关键词提取信息收集
     */
    private fun doShowDialogPermissionConvertSearch() {
        val title = context.resources.getString(
            R.string.privacy_policy_convert_permission_notice_title
        )
        val message = context.resources.getString(com.soundrecorder.common.R.string.extract_keywords_statement_content_v2)
        val protocol = createSpan(
            TYPE_PERMISSION_CONVERT_SEARCH,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.refuse)
        val dialog = createDialog(
            title = title,
            message = message,
            protocol = protocol,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(TYPE_PERMISSION_CONVERT_SEARCH)
                resultCallback?.onPrivacyPolicySuccess(TYPE_PERMISSION_CONVERT_SEARCH)
            },
            clickCancel = {
                doDismiss(TYPE_PERMISSION_CONVERT_SEARCH)
            },
            clickBack = {
                doDismiss(TYPE_PERMISSION_CONVERT_SEARCH)
            },
            cancelable = true
        )
        dialog.addAndShowDialog(TYPE_PERMISSION_CONVERT_SEARCH)
    }

    private fun doShowDialogAIEngine() {
        if (!PermissionUtils.isUserStatementGranted()) {
            DebugUtil.i(TAG, "doShowDialogAIEngine user statement not allowed")
            return
        }

        AIUnitApi.handleSmartNamePlugin(context) { result ->
            DebugUtil.i(TAG, "doShowDialogAIEngine handleSmartNamePlugin result: $result")
        }
    }

    /**
     * onAgreeClick
     */
    private fun onClickFromPrivacyPolicy(type: Int, grantedTypeList: List<Int>) {
        DebugUtil.i(TAG, "onClickFromPrivacyPolicy type: $type, grantedTypeList:${grantedTypeList.toList()}")
        hasConvertPermission = TYPE_PERMISSION_SMART_SHORTHAND in grantedTypeList
        if (hasConvertPermission) {
            PermissionUtils.setConvertGrantedStatus(context)
            BuryingPoint.doClickOkOnConvertPermissionDialog()
        }
        //勾选了云同步/转文本/帮助与反馈其中一个，则授权联网权限
        if (PermissionUtils.shouldNetworkGrantedByFunc(grantedTypeList)) {
            PermissionUtils.setNetWorkGrantedStatus(context, true)
        }

        PermissionUtils.setStatementUpdateStatus(context)
        removeStatementView()

        // 更新用户须知SP值
        val updateSpRequestPermission = if (BaseUtil.isAndroidROrLater) {
            PermissionUtils.hasAllFilePermission()
        } else {
            true
        }
        DebugUtil.d(TAG, "onClickFromPrivacyPolicy, updateSpRequestPermission:$updateSpRequestPermission")
        if (updateSpRequestPermission) {
            PermissionUtils.setNextActionForRequestPermission(context)
        } else {
            PermissionUtils.setNextActionForShowAllFileDialog(context)
        }

        if (TYPE_PERMISSION_CLOUD !in grantedTypeList) {
            doCloseCloudSwitchByDisagree()
        } else {
            cloudKitApi?.setCloudGrantedStatus(context)
        }

        resultCallback?.onPrivacyPolicySuccess(type)
    }

    /**
     * 弹窗点击不同意
     */
    private fun onClickCancel(type: Int) {
        when (type) {
            // 类型：版本更新弹窗
            TYPE_PERMISSION_USER_NOTICE_UPDATE -> {
                //设置update
                PermissionUtils.setStatementUpdateStatus(context)
                //清除转文本
                PermissionUtils.clearConvertGrantedStatus()
                doCloseCloudSwitchByDisagree()
                removeStatementView()
            }
        }

        resultCallback?.onPrivacyPolicyFail(type)
    }

    private fun doCloseCloudSwitchByDisagree() {
        if (isRegionAndSupportCloud()) {
            context.lifecycleScope.launch(Dispatchers.IO) {
                if ((cloudKitApi?.queryCloudSwitchState(false) ?: -1) > CloudSwitchState.CLOSE) {
                    //开关关闭
                    val isSuccess = cloudKitApi?.setSyncSwitch(CloudSwitchState.CLOSE, false) ?: false
                    if (isSuccess) {
                        //清除云同步权限已授予状态
                        cloudKitApi?.clearCloudGrantedStatus()
                        //清除云同步DB数据
                        CloudSyncRecorderDbUtil.clearLocalSyncStatusForLogingout(BaseApplication.getAppContext())
                    }
                    withContext(Dispatchers.Main) {
                        if (isSuccess) {
                            resultCallback?.onPrivacyPolicySuccess(TYPE_PERMISSION_USER_NOTICE_UPDATE)
                        }
                    }
                }
            }
        }
    }

    private fun doShowDialog(type: Int, fromType: Int = TYPE_USER_NOTICE_DEFAULT, pageFrom: Int? = null) {
        currentShowingDialogType = type
        DebugUtil.d(TAG, "doShowDialog $type: $fromType")
        when (type) {
            TYPE_USER_NOTICE -> doShowDialogUserNotice()
            TYPE_USER_NOTICE_BASIC -> doShowDialogUserNoticeBasic()
            TYPE_USER_NOTICE_BASIC_STILL -> doShowDialogUserNoticeStill()
            TYPE_USER_NOTICE_LIGHT_OS -> doShowDialogUserNoticeLight()
            TYPE_PERMISSION_CONVERT -> doShowDialogPermissionConvert(TYPE_PERMISSION_CONVERT, pageFrom)
            TYPE_PERMISSION_CONVERT_FROM_CARD -> {
                // 通过摘要卡点击显示的对话框无法点击外部取消
                doShowDialogPermissionConvert(TYPE_PERMISSION_CONVERT_FROM_CARD, pageFrom)
            }

            TYPE_PERMISSION_CONVERT_WITHDRAWN -> doShowDialogPermissionConvertWithdrawn(cloudKitApi?.isSupportCloudArea() == true)

            TYPE_PERMISSION_CONVERT_SEARCH -> doShowDialogPermissionConvertSearch()
            // 版本升级弹窗
            TYPE_PERMISSION_USER_NOTICE_UPDATE -> doShowDialogUserNoticeUpdate()
            // 附件功能弹框
            TYPE_PERMISSION_SMART_SHORTHAND,
            TYPE_PERMISSION_SUMMARY_MIND,
            TYPE_PERMISSION_FEEDBACK,
            TYPE_PERMISSION_CLOUD -> doShowDialogAdditionalFeature(type, pageFrom)
            // AI服务隐私
            TYPE_PERMISSION_AI_PRIVACY_GUIDE -> doShowDialogAIEngine()
        }
    }

    private fun doShowDialogAdditionalFeature(type: Int, pageFrom: Int?) {
        val messageId = when (type) {
            TYPE_PERMISSION_SMART_SHORTHAND -> R.string.privacy_policy_smart_generation_permission_notice
            TYPE_PERMISSION_SUMMARY_MIND -> R.string.privacy_policy_summary_permission_notice
            TYPE_PERMISSION_CLOUD -> R.string.privacy_policy_cloud_sync_permission_notice
            TYPE_PERMISSION_FEEDBACK -> R.string.privacy_policy_feedback_permission_notice
            else -> {
                DebugUtil.e(TAG, "doShowDialogAdditionalFeature invalid funcType: $type")
                return
            }
        }
        val resource = context.resources
        val title = resource.getString(R.string.privacy_policy_convert_permission_notice_title)
        val message = context.resources.getString(messageId)
        val protocol = createSpan(
            type,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info,
            R.string.privacy_policy_privacy_policy
        )

        val ok = resource.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = resource.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel_new)
        val dialog = createDialog(
            title = title,
            message = message,
            protocol = protocol,
            ok = ok,
            cancel = cancel,
            clickOk = {
                setAllFuncTypePermission(true)
                doDismiss(type)
                onClickFromPrivacyPolicy(type, arrayListOf(type))
            },
            clickCancel = {
                doDismiss(type)
                onClickCancel(type)
            },
            clickBack = {
                doDismiss(type)
                onClickCancel(type)
            }
        )
        dialog.addAndShowDialog(type)
    }

    @Suppress("SpreadOperator")
    private fun createSpan(
        type: Int,
        messageId: Int,
        vararg links: Int
    ): SpannableStringBuilder {
        val args = links.map { context.resources.getString(it) }
        DebugUtil.d(TAG, "createSpan: ${args.toList()}")
        val value = context.resources.getString(messageId, *args.toTypedArray())
        DebugUtil.d(TAG, "createSpan value: ${args.toList()}")
        return innerCreateSpanBuilder(type, value, *links)
    }

    private fun innerCreateSpanBuilder(type: Int, value: String, vararg links: Int): SpannableStringBuilder {
        val args = links.map { context.resources.getString(it) }
        val builder = SpannableStringBuilder(value)
        args.forEach { link ->
            val start = value.indexOf(link)
            val end = start + link.length
            if (start >= 0) {
                builder.setSpan(object : COUIStatementClickableSpan(context) {
                    override fun onClick(widget: View) {
                        onClickSpan(type, link)
                        widget.clearFocus()
                        widget.isPressed = false
                        widget.isSelected = false
                    }
                }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }
        return builder
    }

    protected open fun onClickSpan(type: Int, link: String) {
        DebugUtil.d(TAG, "onClickSpan, type:$type, link:$link")
        when (link) {
            context.resources.getString(R.string.privacy_policy_basic) -> {
                doShowDialog(TYPE_USER_NOTICE_BASIC)
                doDismiss(type)
            }

            context.resources.getString(R.string.privacy_policy_privacy_policy) -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }

                settingApi?.launchRecordPrivacy(
                    context,
                    com.soundrecorder.common.R.string.privacy_policy_settings_policy_key
                )
            }

            context.resources.getString(com.soundrecorder.common.R.string.setting_user_info_proctol_privacy) -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }
                settingApi?.launchBootRegPrivacy(context) {
                    disableDialog = it
                }
            }

            context.resources.getString(com.soundrecorder.common.R.string.sound_record_user_agreement_span) -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }

                settingApi?.launchRecordPrivacy(
                    context,
                    com.soundrecorder.common.R.string.privacy_policy_settings_policy_key
                )
            }
        }
    }

    fun isShowing(type: Int): Boolean = dialogMap[type]?.isShowing == true

    fun getShowingPolicyDialogType(): Int? = if (dialogMap[currentShowingDialogType]?.isShowing == true) currentShowingDialogType else null

    /**
     * onSaveInstance时获取showing的弹窗key
     */
    fun onSaveShowingDialog(): ArrayList<Int> {
        val keys = arrayListOf<Int>()
        dialogMap.map {
            if (isShowing(it.key)) {
                keys.add(it.key)
            }
        }
        showDialogKeys.clear()

        return keys
    }

    /**
     * onRestoreInstance时还原showing的key
     */
    fun onRestoreShowingDialog(dialogKeys: ArrayList<Int>?) {
        isReCreate = true
        if (dialogKeys != null) {
            showDialogKeys.addAll(dialogKeys)
        }
    }

    /**
     * 用户须知等弹窗activity走configurationChange时重建逻辑；
     * 单独抽取方法，若有页面需要在用户须知弹窗重建前执行，可在该方法super之前调用；
     * 目前这里主要针对RecorderActivity onConfigurationChanged 需要在setContentView后执行弹窗重建
     */
    fun reCreateStateDialog() {
        dialogMap.forEach {
            if (isShowing(it.key)) {
                doDismiss(it.key, false)
                doShowDialog(it.key)
            }
        }
    }

    fun clearAll() {
        dialogMap.forEach {
            if (isShowing(it.key)) {
                doDismiss(it.key, false)
            }
        }
        dialogMap.clear()
        showDialogKeys.clear()
        disableDialog.dismissWhenShowing()
        disableDialog = null
    }

    /**
     * 取消所有用户须知弹窗
     */
    fun checkAndDismissDialog() {
        if (PermissionUtils.getNextAction() != PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
            doDismiss(TYPE_USER_NOTICE, false)
            doDismiss(TYPE_USER_NOTICE_BASIC, false)
            doDismiss(TYPE_USER_NOTICE_BASIC_STILL, false)
            doDismiss(TYPE_PERMISSION_USER_NOTICE_FUNCTION, false)
            doDismiss(TYPE_USER_NOTICE_LIGHT_OS, false)
            //已经授予过更新弹窗权限
            if (PermissionUtils.checkPermissionUpdateAlreadyApply(context)) {
                removeStatementView()
            }
        }

        if (cloudKitApi?.isStatementCloudGranted(context) == true) {
            doDismiss(TYPE_PERMISSION_CLOUD, false)
        }

        if (PermissionUtils.isStatementConvertGranted(context)) {
            doDismiss(TYPE_PERMISSION_CONVERT, false)
            doDismiss(TYPE_PERMISSION_CONVERT_SEARCH, false)
        }

        if (!canShowWithdrawnPermissionConvert(context)) {
            doDismiss(TYPE_PERMISSION_CONVERT_WITHDRAWN, false)
        }
    }

    fun resumeShowDialog(checkType: Int, forceShow: Boolean, pageFrom: Int?) {
        DebugUtil.d(TAG, "resumeShowDialog, checkType:$checkType, forceShow:$forceShow")
        when (checkType) {
            TYPE_USER_NOTICE_DEFAULT,
            TYPE_USER_NOTICE,
            TYPE_USER_NOTICE_BASIC,
            TYPE_USER_NOTICE_BASIC_STILL,
            TYPE_PERMISSION_USER_NOTICE_FUNCTION,
            TYPE_USER_NOTICE_LIGHT_OS -> resumeShowUserNotice(checkType)

            TYPE_PERMISSION_USER_NOTICE_UPDATE -> resumeShowUserNoticeUpdate()
            TYPE_PERMISSION_SMART_SHORTHAND,
            TYPE_PERMISSION_SUMMARY_MIND,
            TYPE_PERMISSION_FEEDBACK,
            TYPE_PERMISSION_CLOUD -> resumeShowAdditionalFunc(checkType, forceShow, pageFrom)

            TYPE_PERMISSION_CONVERT,
            TYPE_PERMISSION_CONVERT_FROM_CARD -> resumeShowPermissionConvert(checkType, forceShow, pageFrom)

            TYPE_PERMISSION_CONVERT_SEARCH -> resumeShowPermissionConvertSearch(forceShow)
            TYPE_PERMISSION_CONVERT_WITHDRAWN -> resumeShowWithdrawnPermissionConvert(forceShow)
            TYPE_PERMISSION_AI_PRIVACY_GUIDE -> doShowDialogAIEngine()
        }
    }

    /**
     * show用户须知弹窗
     */
    private fun resumeShowUserNotice(checkType: Int) {
        addStatementView()
        if (isReCreate) {
            DebugUtil.d(TAG, "resumeShowUserNotice:$checkType, isReCreate,showDialogKeys=$showDialogKeys")
            if (showDialogKeys.contains(checkType)) {
                showDialogKeys.forEach {
                    doShowDialog(it)
                }
            } else {
                doShowDialog(checkType)
            }
            isReCreate = false
        } else {
            DebugUtil.d(TAG, "resumeShowUserNotice:$checkType, dialogMap=${dialogMap.keys}")
            if (checkType == TYPE_USER_NOTICE_DEFAULT) {
                // 默认情况，根据内销、轻量显示对应第一个基本弹窗
                val none = dialogMap.none { isShowing(it.key) }
                if (none) {
                    if (BaseUtil.isLightOS()) {
                        doShowDialog(TYPE_USER_NOTICE_LIGHT_OS)
                    } else {
                        doShowDialog(TYPE_USER_NOTICE)
                    }
                }
            } else if (dialogMap.containsKey(checkType) && isShowing(checkType)) {
                // 当前显示的dialog正在显示
                DebugUtil.d(TAG, "target dialog is showing")
            } else {
                // 正在显示的dialog同目标dialog不匹配
                dialogMap.forEach {
                    doDismiss(it.key, false)
                }
                doShowDialog(checkType)
            }
        }
    }

    /**
     * 显示个保法升级声明
     */
    private fun resumeShowUserNoticeUpdate() {
        addStatementView()
        if (isReCreate) {
            showDialogKeys.forEach {
                doShowDialog(it)
            }
            isReCreate = false
        } else {
            val none = dialogMap.none { isShowing(it.key) }
            if (none) {
                doShowDialog(TYPE_PERMISSION_USER_NOTICE_UPDATE)
            }
        }
    }

    /**
     * 恢复展示转文本权限弹窗
     */
    private fun resumeShowPermissionConvert(checkType: Int, forceShow: Boolean = false, pageFrom: Int?) {
        DebugUtil.i(TAG, "resumeShowPermissionConvert isReCreate = $isReCreate , forceShow = $forceShow")
        if (isReCreate) {
            showDialogKeys.forEach {
                doShowDialog(it, pageFrom = pageFrom)
            }
            isReCreate = false
        } else {
            if (forceShow) {
                val none = dialogMap.none { isShowing(it.key) }
                if (none) {
                    //doShowDialog(TYPE_PERMISSION_CONVERT)
                    doShowDialog(checkType, pageFrom = pageFrom)
                }
            }
        }
    }

    private fun resumeShowAdditionalFunc(checkType: Int, forceShow: Boolean = false, pageFrom: Int?) {
        DebugUtil.i(TAG, "resumeShowAdditionalFunc isReCreate = $isReCreate , forceShow = $forceShow")
        if (isReCreate) {
            showDialogKeys.forEach {
                doShowDialog(it, pageFrom = pageFrom)
            }
            isReCreate = false
        } else {
            if (forceShow) {
                val none = dialogMap.none { isShowing(it.key) }
                if (none) {
                    doShowDialog(checkType, pageFrom = pageFrom)
                }
            }
        }
    }

    /**
     * 恢复展示转文本关键词提取信息弹窗
     */
    private fun resumeShowPermissionConvertSearch(forceShow: Boolean = false) {
        DebugUtil.i(TAG, "isReCreate = $isReCreate , forceShow = $forceShow")
        if (isReCreate) {
            showDialogKeys.forEach {
                doShowDialog(it)
            }
            isReCreate = false
        } else {
            if (forceShow) {
                val none = dialogMap.none { isShowing(it.key) }
                if (none) {
                    doShowDialog(TYPE_PERMISSION_CONVERT_SEARCH)
                }
            }
        }
    }

    private fun resumeShowWithdrawnPermissionConvert(forceShow: Boolean) {
        DebugUtil.i(TAG, "isReCreate = $isReCreate , forceShow = $forceShow")
        if (isReCreate) {
            showDialogKeys.forEach {
                doShowDialog(it)
            }
            isReCreate = false
        } else {
            if (forceShow) {
                val none = dialogMap.none { isShowing(it.key) }
                if (none) {
                    doShowDialog(TYPE_PERMISSION_CONVERT_WITHDRAWN)
                }
            }
        }
    }

    /**
     * 处理用户须知点击同意和用户撤回个人信息保护政策同意事件
     * Turn off smart name switch if user withdraws the User's Personal Information Protection Policy
     * Turn on smart name switch if user agrees to the User's Personal Information Protection Policy
     *
     * @param allow true if allow user statement. false if Withdraw the User's Personal Information Protection Policy
     */
    private fun handleUserStatementChange(allow: Boolean) {
        setAllFuncTypePermission(allow)
        smartNameAction?.setSmartNameSwitchStatus(context, allow, true)
    }
}