package com.soundrecorder.privacypolicy

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.Typeface.create
import android.icu.text.SimpleDateFormat
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.provider.MediaStore
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.text.style.ForegroundColorSpan
import android.text.style.TypefaceSpan
import android.text.style.URLSpan
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.annotation.StringRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.core.content.FileProvider
import androidx.core.text.HtmlCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment
import com.coui.appcompat.clickablespan.COUIClickableSpan
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.snackbar.COUISnackBar
import com.coui.appcompat.toolbar.COUIToolbar
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.ext.isInMultiWindowMode
import com.soundrecorder.base.ext.isLandscape
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.LanguageUtil
import com.soundrecorder.common.utils.FunctionOption.hasSupportByte
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.privacypolicy.html.HtmlParser
import kotlinx.coroutines.CoroutineName
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.plus
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.text.DateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class PrivacyPolicyInfoFragment : Fragment() {
    companion object {
        private const val TAG = "PrivacyPolicyInfoFragment"
        private const val KEY_FRAGMENT_TYPE_VALUE = "key_fragment_type_value"
        private const val KEY_STORAGE_FEEDBACK_NET = "storage_switch_feedback_net"
        private const val CHINESE_DATE = "2022年6月21日"
        private const val CHINESE_DATE_FORMAT = "yyyy年M月d日"
        private const val TAI_WAN_DATE_FORMAT = "yyyy 年 M 月 d 日"
        private const val CHECK_LIST_UPDATE_DATE = "2024年9月25日"
        private const val CHECK_LIST_VERSION = "12.30.1"
        private const val SANS_SERIF_MEDIUM = "sans-serif-medium"
        private const val TYPE = "text/plain"
        private const val MAX_TRY_DOWNLOAD_TIMES = 20
        private const val LINE_SPACE_8 = 8f
        private const val LINE_SPACE_12 = 12f
        private const val LINE_SPACE_16 = 16f
        private const val LINE_SPACE_24 = 24f
        private const val LINE_SPACE_34 = 34f
        private const val LINE_SPACE_48 = 48f

        fun newInstance(
            type: Int = PrivacyPolicyConstant.TYPE_PERSONAL_INFORMATION_PROTECTION_POLICY
        ): PrivacyPolicyInfoFragment {
            val args = Bundle().apply {
                putInt(KEY_FRAGMENT_TYPE_VALUE, type)
            }
            val fragment = PrivacyPolicyInfoFragment()
            fragment.arguments = args
            return fragment
        }
    }

    private var internetDialog: AlertDialog? = null
    private var disableDialog: AlertDialog? = null
    private var index = 0
    private var downloadContent: StringBuffer = StringBuffer()

    private val mainScope by lazy { MainScope() + CoroutineName("PrivacyPolicy") }
    private val black = Color.argb(0.85f, 0f, 0f, 0f)
    private val mediumTypeface = create(SANS_SERIF_MEDIUM, Typeface.NORMAL)
    private val customResources by lazy {
        val config = BaseApplication.getApplication().resources.configuration
        config.setLocale(Locale.ENGLISH)
        BaseApplication.getApplication().createConfigurationContext(config).resources
    }

    private val settingApi by lazy {
        Injector.injectFactory<SettingInterface>()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? = inflater.inflate(R.layout.fragment_privacy_policy_info, container, false)

    /**
     * 设置到状态栏的间隔
     */
    private fun doOnApplyWindowInsets() {
        val activity = activity ?: return
        activity.window?.let {
            WindowCompat.setDecorFitsSystemWindows(it, false)
        }
        val rootView = activity.findViewById<View>(R.id.root_layout)
        ViewCompat.setOnApplyWindowInsetsListener(rootView) { _, insets ->
            val systemBar =
                insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.systemBars())
            rootView.setPadding(
                systemBar.left,
                systemBar.top,
                systemBar.right,
                systemBar.bottom
            )
            TaskBarUtil.setNavigationColorOnSupportTaskBar(
                navigationHeight = systemBar.bottom,
                activity = activity,
                defaultNoTaskBarColor = (activity as? BaseActivity)?.navigationBarColor()
            )
            WindowInsetsCompat.CONSUMED
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        view.findViewById<LinearLayoutCompat>(R.id.container).apply {
            removeAllViews()
            when (arguments?.getInt(KEY_FRAGMENT_TYPE_VALUE)) {
                PrivacyPolicyConstant.TYPE_THIRD_PARTY_INFORMATION_SHARING_CHECKLIST ->
                    doShowThirdPartyInformationSharingCheckList()
                PrivacyPolicyConstant.TYPE_COLLECTION_OF_PERSONAL_INFORMATION_EXPRESS_CHECKLIST ->
                    doShowCollectionOfPersonalInformationExpressCheckList()
                else -> doShowPersonalInformationProtectionPolicy()
            }
        }
        super.onViewCreated(view, savedInstanceState)
        val toolbar = view.findViewById<COUIToolbar>(R.id.toolbar)
        toolbar.apply {
            setNavigationIcon(com.support.appcompat.R.drawable.coui_back_arrow)
            setNavigationContentDescription(com.soundrecorder.common.R.string.back)
            setNavigationOnClickListener {
                activity?.onBackPressed()
            }
        }
        doOnApplyWindowInsets()
    }

    private fun LinearLayoutCompat.doShowThirdPartyInformationSharingCheckList() {
        addSpace().addTitleView {
            center()
            append(R.string.privacy_policy_share_title_no_translatable)
        }.addSpace(LINE_SPACE_34).addTextView {
            lineSpace(LINE_SPACE_8)
            append(R.string.privacy_policy_sharing_check_list_1, CHECK_LIST_UPDATE_DATE)
            //appendLine(R.string.privacy_policy_sharing_check_list_2, CHECK_LIST_VERSION)
        }.addSpace(LINE_SPACE_24).addTextView {
            append(R.string.privacy_policy_sharing_check_list_3)
        }.addSpace().addTitleView {
            medium()
            append(R.string.privacy_policy_sharing_other_third_list)
        }.addSpace(LINE_SPACE_24).addTextView {
            appendHtml(R.string.privacy_policy_sharing_service_type)
            appendLine()
            appendHtml(R.string.privacy_policy_sharing_third_party_company)
            appendLine()
            appendHtml(R.string.privacy_policy_sharing_third_collect_info)
            appendLine()
            appendHtml(R.string.privacy_policy_sharing_third_collect_content)
            appendLine()
            appendHtml(R.string.privacy_policy_sharing_function_scene)
            appendLine()
            appendHtml(R.string.privacy_policy_sharing_purpose)
            appendLine()
            appendHtml(R.string.privacy_policy_sharing_processing)
            appendLine()
            appendHtml(R.string.privacy_policy_sharing_third_privacy_link) {
                try {
                    startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(it)))
                } catch (e: Exception) {
                    DebugUtil.e(TAG, "doShowThirdParty error:", e)
                }
            }
        }
    }

    private fun LinearLayoutCompat.doShowCollectionOfPersonalInformationExpressCheckList() {
        if (BaseUtil.isLightOS()) {
            addSpace(LINE_SPACE_12).addTitleView {
                center()
                medium()
                append(R.string.privacy_policy_collection_title_no_translatable)
            }.addSpace(LINE_SPACE_34).addTextView {
                lineSpace(LINE_SPACE_8)
                append(R.string.privacy_policy_express_1, CHECK_LIST_UPDATE_DATE)
                appendLine(R.string.privacy_policy_express_2, CHECK_LIST_VERSION)
            }.addSpace(LINE_SPACE_24).addTextView {
                append(R.string.privacy_policy_express_3)
            }.addSpace().addTextView {
                medium()
                appendHtml(R.string.privacy_policy_express_12)
            }.addSpace().addTextView {
                medium()
                appendHtml(R.string.privacy_policy_express_13_account)
            }
        } else {
            addSpace(LINE_SPACE_12).addTitleView {
                center()
                medium()
                append(R.string.privacy_policy_collection_title_no_translatable)
            }.addSpace(LINE_SPACE_34).addTextView {
                lineSpace(LINE_SPACE_8)
                append(R.string.privacy_policy_express_1, CHECK_LIST_UPDATE_DATE)
                appendLine(R.string.privacy_policy_express_2, CHECK_LIST_VERSION)
            }.addSpace(LINE_SPACE_24).addTextView {
                append(R.string.privacy_policy_express_3)
            }.addSpace().addTextView {
                medium()
                appendHtml(R.string.privacy_policy_express_11)
            }.addSpace().addTextView {
                medium()
                appendHtml(R.string.privacy_policy_express_12)
            }.addSpace().addTextView {
                medium()
                appendHtml(R.string.privacy_policy_express_13_account)
            }
        }
    }

    private fun LinearLayoutCompat.doShowPersonalInformationProtectionPolicy() {
        addSpace(LINE_SPACE_16)
        addTitleView {
            center()
            medium()
            sp16()
            append(R.string.privacy_policy_title_v1)
        }
        addSpace(LINE_SPACE_24)
        addTextView {
            val date = dateString()
            appendHtml(R.string.privacy_policy_download_v1, date, date) {
                if (BaseUtil.isAndroidQOrLater) {
                    saveToDownload()
                }
            }
            lineSpace(LINE_SPACE_8)
        }
        addSpace(LINE_SPACE_24)
        addTitleView {
            medium()
            append(R.string.privacy_policy_update_title_v1)
        }
        addSpace(LINE_SPACE_8)
        addTextView {
            if (BaseUtil.isAndroidTOrLater) {
                appendHtml(R.string.privacy_policy_update_content_1_v6)
            } else {
                appendHtml(R.string.privacy_policy_update_content_1_v3)
            }
        }
        addSpace(LINE_SPACE_24)
        addTitleView {
            medium()
            append(R.string.privacy_policy_content_title_v1)
        }
        addSpace(LINE_SPACE_8)
        addTextView {
            when {
                BaseUtil.isRealme() -> appendHtml(R.string.privacy_policy_content_1_realme_v1)
                BaseUtil.isOnePlus() -> {
                    if (BaseUtil.isKeepOnePlusCompanyTheme()) {
                        appendHtml(R.string.privacy_policy_content_1_oneplus_v1)
                    } else {
                        appendHtml(R.string.privacy_policy_content_1_oppo_v1)
                    }
                }
                BaseUtil.isOPPO() -> appendHtml(R.string.privacy_policy_content_1_oppo_v1)
            }
            appendLine()
            appendHtml(R.string.privacy_policy_content_2_v1)
        }
        addSpace(LINE_SPACE_24)
        addTitleView {
            medium()
            append(R.string.privacy_policy_p1_title_v2)
        }
        addSpace(LINE_SPACE_16)
        addTextView {
            appendHtml(R.string.privacy_policy_p1_content_v1) {
                activity?.startActivity(com.soundrecorder.common.R.string.privacy_policy_settings_collection_key)
            }
        }
        addSpace(LINE_SPACE_16)
        addTitleView { append(R.string.privacy_policy_p1_1_title_v2) }
        addSpace(LINE_SPACE_16)
        addTitleView { append(R.string.privacy_policy_p1_1_1_title_v2) }
        addSpace(LINE_SPACE_8)
        addTextView { appendHtml(R.string.privacy_policy_p1_1_1_content_v1) }
        addSpace(LINE_SPACE_16)
        addTitleView { append(R.string.privacy_policy_p1_2_title_v2) }
        addSpace(LINE_SPACE_16)
        addTitleView { append(R.string.privacy_policy_p1_2_1_title_v2) }
        addSpace(LINE_SPACE_8)
        addTextView { appendHtml(R.string.privacy_policy_p1_2_1_content_v2) }
        addSpace(LINE_SPACE_16)
        addTitleView { append(R.string.privacy_policy_p1_2_2_title_v3) }
        addSpace(LINE_SPACE_8)
        addTextView { appendHtml(R.string.privacy_policy_p1_2_2_content_v2) }
        addSpace(LINE_SPACE_16)
        addTitleView { append(R.string.privacy_policy_p1_2_3_title_v3) }
        addSpace(LINE_SPACE_8)
        addTextView { appendHtml(R.string.privacy_policy_p1_2_3_content_v2) }
        addSpace(LINE_SPACE_16)
        addTitleView { append(R.string.privacy_policy_p1_2_4_title) }
        addSpace(LINE_SPACE_8)
        addTextView { appendHtml(R.string.privacy_policy_p1_2_4_content) }
        addSpace(LINE_SPACE_16)
        addTitleView { append(R.string.privacy_policy_p1_2_5_title) }
        addSpace(LINE_SPACE_8)
        addTextView { appendHtml(R.string.privacy_policy_p1_2_5_content) }
        addSpace(LINE_SPACE_16)
        addTitleView { append(R.string.privacy_policy_p1_3_title_v2) }
        addSpace(LINE_SPACE_8)
        addTextView {
            if (BaseUtil.isAndroidTOrLater) {
                appendHtml(R.string.privacy_policy_p1_3_content_v3)
            } else {
                appendHtml(R.string.privacy_policy_p1_3_content_v1)
            }
        }
        addSpace(LINE_SPACE_24)
        addTitleView {
            medium()
            append(R.string.privacy_policy_p2_title_v2)
        }
        addSpace(LINE_SPACE_16)
        addTitleView { append(R.string.privacy_policy_p2_1_title_v2) }
        addSpace(LINE_SPACE_8)
        addTextView { appendHtml(R.string.privacy_policy_p2_1_content_v1) }
        addSpace(LINE_SPACE_16)
        addTitleView { append(R.string.privacy_policy_p2_2_title_v2) }
        addSpace(LINE_SPACE_8)
        addTextView { appendHtml(R.string.privacy_policy_p2_2_content_v1) }
        addSpace(LINE_SPACE_24)
        addTitleView {
            medium()
            append(R.string.privacy_policy_p3_title_v2)
        }
        addSpace(LINE_SPACE_16)
        addTextView {
            appendHtml(res = R.string.privacy_policy_p3_content_v1, ignoreURLSpan = !hasSupportByte()) {
                activity?.startActivity(com.soundrecorder.common.R.string.privacy_policy_settings_share_key)
            }
        }
        addSpace(LINE_SPACE_24)
        addTitleView {
            medium()
            append(R.string.privacy_policy_p4_title_v2)
        }
        addSpace(LINE_SPACE_16)
        addTextView { appendHtml(R.string.privacy_policy_p4_content_v1) }
        addSpace(LINE_SPACE_24)
        addTitleView {
            medium()
            append(R.string.privacy_policy_p5_title_v2)
        }
        addSpace(LINE_SPACE_16)
        addTextView { appendHtml(R.string.privacy_policy_p5_content_v2) }
        addSpace(LINE_SPACE_24)
        addTitleView {
            medium()
            append(R.string.privacy_policy_p6_title_v2)
        }
        addSpace(LINE_SPACE_16)
        addTextView { appendHtml(R.string.privacy_policy_p6_content_v1) }
        addSpace(LINE_SPACE_24)
        addTitleView {
            medium()
            append(R.string.privacy_policy_p7_title_v2)
        }
        addSpace(LINE_SPACE_16)
        addTextView {
            appendHtml(R.string.privacy_policy_p7_content_1_v1) {
                disableDialog.dismissWhenShowing()
                settingApi?.launchBootRegPrivacy(activity) {
                    disableDialog = it
                }
            }
            appendLine()
            val appendOppoHtml = fun() {
                appendHtml(R.string.privacy_policy_p7_content_2_oppo_v1) {
                    try {
                        startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(it)))
                    } catch (e: Exception) {
                        DebugUtil.e(TAG, "", e)
                    }
                }
            }
            when {
                BaseUtil.isOPPO() -> appendOppoHtml.invoke()
                BaseUtil.isOnePlus() -> {
                    // 一加渠道 && (外销||(内销)T版本以下||（内销）有一加主题feature)
                    if (BaseUtil.isKeepOnePlusCompanyTheme()) {
                        appendHtml(R.string.privacy_policy_p7_content_2_oneplus_v1)
                    } else {
                        appendOppoHtml.invoke()
                    }
                }
            }
        }
        addSpace(LINE_SPACE_24)
        addTextView {
            appendHtml(R.string.privacy_policy_history_v1) {
                lunchPrivacyPolicyInfoWebPage()
            }
        }
        addSpace(LINE_SPACE_24)
        addTextView {
            lineSpace(LINE_SPACE_8)
            when {
                BaseUtil.isRealme() -> appendHtml(R.string.privacy_policy_address_realme_v1) {
                    try {
                        startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("mailto:$it")))
                    } catch (e: Exception) {
                        DebugUtil.e(TAG, "", e)
                    }
                }
                BaseUtil.isOnePlus() -> {
                    if (BaseUtil.isKeepOnePlusCompanyTheme()) {
                        appendHtml(R.string.privacy_policy_address_oneplus_v1)
                    } else {
                        appendHtml(R.string.privacy_policy_address_oppo_v1)
                    }
                }
                BaseUtil.isOPPO() -> appendHtml(R.string.privacy_policy_address_oppo_v1)
            }
        }
        addSpace(LINE_SPACE_48)
    }

    private fun LinearLayoutCompat.addTextView(callback: TextView.() -> Unit): LinearLayoutCompat {
        addView(TextView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            gravity = Gravity.START
            textSize = 14f
            setTextColor(black)
            includeFontPadding = false
            lineSpace(5f)
            callback()
        })
        return this
    }


    private fun TextView.center() {
        gravity = Gravity.CENTER
    }

    private fun TextView.sp16() {
        textSize = LINE_SPACE_16
    }

    private fun TextView.medium() {
        typeface = mediumTypeface
    }

    private fun LinearLayoutCompat.addSpace(space: Float = LINE_SPACE_16): LinearLayoutCompat {
        addView(View(context).apply {
            layoutParams = ViewGroup.LayoutParams(1, ViewUtils.dp2px(space).toInt())
        })
        return this
    }

    private fun LinearLayoutCompat.addTitleView(
        callback: TextView.() -> Unit
    ): LinearLayoutCompat {
        addView(TextView(context).apply {
            layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            gravity = Gravity.START
            sp16()
            black()
            includeFontPadding = false
            callback()
        })
        return this
    }

    private fun TextView.appendLine(res: Int, vararg values: String) {
        append("\n")
        append(res, *values)
    }

    private fun TextView.append(res: Int, vararg values: String) {
        append(getCustomString(res, *values))
    }

    private fun TextView.appendHtml(
        res: Int,
        vararg values: String,
        ignoreURLSpan: Boolean = false,
        onClick: ((String) -> Unit)? = null
    ) {
        val output = HtmlParser.fromHtml(
                getCustomString(res, *values), HtmlCompat.FROM_HTML_MODE_LEGACY, null, null
        ) as SpannableStringBuilder
        val boldSpans = output.getSpans(
                0, output.length, TypefaceSpan::class.java
        ).filter { it.typeface == mediumTypeface }
        boldSpans.forEach {
            var start = output.getSpanStart(it)
            val end = output.getSpanEnd(it)
            val flags = output.getSpanFlags(it)
            val colorSpan = ForegroundColorSpan(black)
            val urlSpans = output.getSpans(start, end, ClickableSpan::class.java)
            if (urlSpans.isEmpty()) {
                output.setSpan(colorSpan, start, end, flags)
            } else {
                urlSpans.forEach { span ->
                    val s = output.getSpanStart(span)
                    val e = output.getSpanEnd(span)
                    output.setSpan(colorSpan, start, s, flags)
                    start = e
                }
                output.setSpan(colorSpan, start, end, flags)
            }
        }
        val urlSpans = output.getSpans(0, output.length, URLSpan::class.java)
        urlSpans.forEach {
            val start = output.getSpanStart(it)
            val end = output.getSpanEnd(it)
            val flags = output.getSpanFlags(it)
            output.removeSpan(it)
            if (ignoreURLSpan) {
                output.setSpan(null, start, end, flags)
            } else {
                val clickSpan = COUIClickableSpan(context).apply {
                    setStatusBarClickListener {
                        onClick?.invoke(it.url)
                    }
                }
                output.setSpan(clickSpan, start, end, flags)
            }
        }
        append(output)
        movementMethod = LinkMovementMethod.getInstance()
    }

    private fun TextView.appendBlack(res: Int) {
        val str = getCustomString(res)
        val builder = SpannableStringBuilder(str)
        builder.setSpan(
            ForegroundColorSpan(black),
            0,
            str.length,
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        append(builder)
    }

    private fun TextView.black() {
        setTextColor(black)
    }

    private fun TextView.appendLine() {
        append("\n")
    }

    private fun Activity.startActivity(type: Int = com.soundrecorder.common.R.string.privacy_policy_settings_policy_key) {
        startActivity(
            Intent(
                this,
                javaClass
            ).putExtra(PrivacyPolicyConstant.FRAGMENT_CLASS_NAME_TYPE, type)
        )
    }

    private fun setInternetPermission(isOk: Boolean) {
        StorageManager.setIntPref(context, KEY_STORAGE_FEEDBACK_NET, if (isOk) 1 else 0)
    }

    private fun lunchPrivacyPolicyInfoWebPage() {
        //判断是否有网络权限，已经允许了，直接跳转，没有则弹窗提示
        if (StorageManager.getIntPref(context, KEY_STORAGE_FEEDBACK_NET, 0) == 1) {
            jumpPrivacyPolicyInfoActivity()
        } else {
            showInternetPermissionDialog()
        }
    }

    private fun jumpPrivacyPolicyInfoActivity() {
        startActivity(Intent(context, PrivacyPolicyInfoWebActivity::class.java))
    }

    private fun showInternetPermissionDialog() {
        activity?.let { act ->
            internetDialog = COUIAlertDialogBuilder(act).apply {
                setTitle(act.getString(R.string.network_tips_title))
                setBlurBackgroundDrawable(true)
                setPositiveButton(act.getString(R.string.allow)) { _, _ ->
                    setInternetPermission(true)
                    jumpPrivacyPolicyInfoActivity()
                }
                setNegativeButton(act.getString(com.soundrecorder.common.R.string.refuse)) { _, _ ->
                }
            }.show().apply {
                setCancelable(true)
                setCanceledOnTouchOutside(false)
                ViewUtils.updateWindowLayoutParams(window)
            }
        }
    }

    /**
     * TODO android P
     */
    @RequiresApi(Build.VERSION_CODES.Q)
    private fun LinearLayoutCompat.saveToDownload() {
        downloadContent = StringBuffer()
        for (index in 0..childCount) {
            val child = getChildAt(index)
            if (child is TextView) {
                downloadContent.append(child.text)
            } else {
                downloadContent.append("\n\n")
            }
        }
        mainScope.launch(Dispatchers.IO) {
            downLoadPrivacy(false, downloadContent.toString())
        }
    }

    private fun LinearLayoutCompat.downLoadPrivacy(random: Boolean = false, content: String) {
        val file = getPrivacyFile(random)
        try {
            FileOutputStream(file).use {
                it.write(content.toByteArray())
            }
            showSnackBar(file)
        } catch (e: FileNotFoundException) {
            DebugUtil.e(TAG, "downLoadPrivacy catch an error : ${e.message}")
            if (index < MAX_TRY_DOWNLOAD_TIMES) {
                index++
                downLoadPrivacy(true, downloadContent.toString())
            }
        }
    }

    @SuppressLint("SimpleDateFormat")
    private fun getPrivacyFile(random: Boolean = false): File {
        val c = Calendar.getInstance()
        val sdf = java.text.SimpleDateFormat("HHmmssSSS")
        val randomText = if (random) {
            ".${sdf.format(Date())}"
        } else {
            ""
        }
        kotlin.runCatching {
            val fileDir =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            if (!fileDir.exists()) {
                fileDir.mkdirs()
            }
        }.onFailure {
            DebugUtil.e(TAG, "getPrivacyFile make dir error:$it")
        }

        return File(
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
            "${getString(R.string.privacy_policy_title_v1)}-${c.get(Calendar.YEAR)}-${
                c.get(
                    Calendar.MONTH
                ) + 1
            }-${c.get(Calendar.DAY_OF_MONTH)}$randomText.txt"
        ).also {
            DebugUtil.i(TAG, "getPrivacyFile: ${it.exists()}")
        }
    }

    private fun LinearLayoutCompat.showSnackBar(file: File) {
        //针对本问题COUISnackBar内容显示异常通过修改marginBottom临时规避
        val marginBottom = if (isInMultiWindowMode() || isLandscape()) {
            resources.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp24)
        } else {
            resources.getDimensionPixelOffset(R.dimen.dp50)
        }
        mainScope.launch(Dispatchers.Main) {
            run {
                COUISnackBar.make(
                    this@showSnackBar,
                    getString(R.string.privacy_policy_download_success),
                    5000,
                    marginBottom
                ).apply {
                    show()
                    setOnAction(getString(R.string.privacy_policy_download_success_to_preview)) {
                        openFile(file)
                    }
                }
            }
        }
    }

    private fun openFile(file: File) {
        val intent = Intent(Intent.ACTION_VIEW).apply {
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            addCategory(Intent.CATEGORY_DEFAULT)
            setDataAndType(context?.let {
                FileProvider.getUriForFile(
                    it,
                    "${it.packageName}.fileProvider",
                    file,
                    ""
                )
            }, TYPE)
        }
        kotlin.runCatching {
            startActivity(intent)
        }.getOrElse {
            DebugUtil.d(TAG, "openFile catch an error : ${it.message}")
        }
    }

    private fun TextView.lineSpace(sp: Float) {
        val fontScale = resources.configuration.fontScale
        val density = resources.displayMetrics.density
        setLineSpacing(sp * density * fontScale, 1f)
    }


    @Suppress("DEPRECATION")
    private fun Uri.getFilePath(): String {
        try {
            activity?.contentResolver?.query(
                this,
                arrayOf(MediaStore.Audio.Media.DATA),
                null,
                null,
                null
            )?.use {
                it.moveToFirst()
                val index = it.getColumnIndex(MediaStore.Audio.Media.DATA)
                if (index >= 0) {
                    return it.getString(index) ?: ""
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "deleteFile error", e)
        }
        return ""
    }

    /**
     * 目前 个人信息保护政策，只支持4种语言，中文简体，繁体-台湾，繁体-香港，英文
     * 但部分词条多翻译了其他语种，所以需要自定义getString方法
     */
    private fun getCustomString(@StringRes resId: Int, vararg formatArgs: String): String {
        return if (LanguageUtil.isZHCN() || LanguageUtil.isZHHK() || LanguageUtil.isZHTW()) {
            getString(resId, *formatArgs)
        } else {
            customResources.getString(resId, *formatArgs)
        }
    }

    private fun dateString(): String {
        try {
            val date = SimpleDateFormat(CHINESE_DATE_FORMAT).parse(CHINESE_DATE)
            DebugUtil.i(TAG, "$date")
            return when {
                LanguageUtil.isZHHK() ->
                    DateFormat.getDateInstance(DateFormat.MEDIUM, Locale.getDefault()).format(date)
                LanguageUtil.isZHTW() ->
                    SimpleDateFormat(TAI_WAN_DATE_FORMAT, Locale.getDefault()).format(date)
                LanguageUtil.isZHCN() ->
                    DateFormat.getDateInstance(DateFormat.MEDIUM, Locale.getDefault()).format(date)
                else -> DateFormat.getDateInstance(DateFormat.MEDIUM, Locale.ENGLISH).format(date)
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "", e)
        }
        return CHINESE_DATE
    }

    override fun onDestroy() {
        super.onDestroy()
        if (internetDialog?.isShowing == true) {
            internetDialog?.dismiss()
        }
        internetDialog = null
        disableDialog.dismissWhenShowing()
        disableDialog = null
    }
}