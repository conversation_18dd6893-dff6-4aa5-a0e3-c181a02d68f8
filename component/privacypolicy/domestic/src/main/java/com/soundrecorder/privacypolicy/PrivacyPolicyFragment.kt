package com.soundrecorder.privacypolicy

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import com.coui.appcompat.preference.COUIJumpPreference
import com.coui.appcompat.preference.COUIPreference
import com.coui.appcompat.toolbar.COUIToolbar
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.view.RootViewPersistentInsetsCallback
import com.soundrecorder.common.base.IFragmentCallback
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity
import com.soundrecorder.common.flexible.FollowHandPanelUtils
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.common.widget.AutoIndentPreferenceFragment
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.utils.Injector

class PrivacyPolicyFragment : AutoIndentPreferenceFragment(), IFragmentCallback {

    companion object {
        private const val TAG = "PrivacyPolicyFragment"
    }
    private var mPersonalPrivacy: COUIJumpPreference? = null
    private var mWithdrawnPrivacy: PrivacyPolicyCOUIPreference? = null
    private var disableDialog: AlertDialog? = null

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val cloudTipManagerAction by lazy {
        Injector.injectFactory<CloudTipManagerAction>()
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        initActionBar(view)
        listView?.isForceDarkAllowed = false
        addPreferencesFromResource(R.xml.fragment_privacy_policy)

        mPersonalPrivacy =
            findPreference<COUIPreference>(getString(com.soundrecorder.common.R.string.privacy_policy_settings_policy_key))
                as? COUIJumpPreference
        mPersonalPrivacy?.title = getString(R.string.privacy_policy_privacy_policy)
        mPersonalPrivacy?.setOnPreferenceClickListener {
            activity?.startActivity(com.soundrecorder.common.R.string.privacy_policy_settings_policy_key)
            false
        }

        mWithdrawnPrivacy =
            findPreference<COUIPreference>(getString(com.soundrecorder.common.R.string.privacy_policy_settings_withdrawn_key))
                as? PrivacyPolicyCOUIPreference
        mWithdrawnPrivacy?.isVisible = true
        mWithdrawnPrivacy?.setOnPreferenceClickListener {
            (activity as? PrivacyPolicyBaseActivity)?.getPrivacyPolicyDelegate()
                ?.resumeShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CONVERT_WITHDRAWN, true)
            false
        }

        activity?.let { FollowHandPanelUtils.checkActivityClickOutOfBounds(it, view) }

        return view
    }

    private fun initActionBar(view: View?) {
        if (view == null) {
            return
        }
        val activity = activity as? AppCompatActivity? ?: return
        val toolbar = view.findViewById<COUIToolbar>(R.id.toolbar)
        activity.setSupportActionBar(toolbar)
        val actionBar = activity.supportActionBar
        actionBar?.setHomeButtonEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)
        actionBar?.setTitle(com.soundrecorder.common.R.string.privacy_policy_settings_title)
        initiateWindowInsets(activity)
    }

    private fun initiateWindowInsets(activity: Activity) {
        WindowCompat.setDecorFitsSystemWindows(activity.window, false)
        activity.findViewById<View>(R.id.root_layout)?.let {
            val callback: RootViewPersistentInsetsCallback = object : RootViewPersistentInsetsCallback() {
                override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                    super.onApplyInsets(v, insets)
                    DebugUtil.i(TAG, "onApplyInsets")
                    TaskBarUtil.setNavigationColorOnSupportTaskBar(
                        navigationHeight = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars()).bottom,
                        activity = activity,
                        defaultNoTaskBarColor = (activity as? BaseActivity)?.navigationBarColor()
                    )
                }
            }
            ViewCompat.setOnApplyWindowInsetsListener(it, callback)
        }
    }

    private fun Activity.startActivity(type: Int = com.soundrecorder.common.R.string.privacy_policy_settings_policy_key) {
        startActivity(Intent(this, javaClass)
            .putExtra(PrivacyPolicyConstant.FRAGMENT_CLASS_NAME_TYPE, type))
    }

    override fun onResume() {
        onFragmentRefresh()

        /*
         * 隐藏滑动条
         */
        listView.isVerticalScrollBarEnabled = false
        super.onResume()
    }

    override fun onFragmentRefresh() {
        mWithdrawnPrivacy?.apply {
            //撤销按钮可点击的条件：有转文本权限或者（支持云同步并且云同步开关打开，若不支持云同步则无需校验开关）
            isEnabled = PermissionUtils.isStatementConvertGranted(BaseApplication.getAppContext())
                    || (((cloudKitApi?.isSupportCloudArea() == true
                    && cloudKitApi?.isSupportSwitch() == true)
                    && cloudTipManagerAction?.isCloudSwitchOn() == true))
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        disableDialog.dismissWhenShowing()
        disableDialog = null
    }
}
