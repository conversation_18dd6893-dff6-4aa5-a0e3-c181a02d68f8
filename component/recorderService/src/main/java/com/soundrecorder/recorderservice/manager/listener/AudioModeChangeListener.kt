/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AudioModeChangeListener
 Description:
 Version: 1.0
 Date: 2022/10/9
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/10/9 1.0 create
 */
package com.soundrecorder.recorderservice.manager.listener

interface AudioModeChangeListener {

    fun onAudioModeChanged(audioMode: Int)
}

interface AudioRecordingChangeListener {
    fun onAudioRecordingSourceChange(isMic: Boolean)
}