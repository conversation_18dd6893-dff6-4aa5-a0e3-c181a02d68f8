/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordStatusBarUpdater
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager.statusbar

import android.app.RecoverableSecurityException
import android.content.Context
import androidx.lifecycle.Observer
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.screenstate.ScreenStateLiveData
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.DisplayUtils
import com.soundrecorder.modulerouter.STATUS_BAR_SUPPORT_FLUID_CARD
import com.soundrecorder.modulerouter.STATUS_BAR_SUPPORT_SEEDLING_CARD
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener
import com.soundrecorder.modulerouter.recorder.MarkAction
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.recorderservice.manager.RecordStatusManager
import com.soundrecorder.recorderservice.manager.RecorderViewModel
import com.soundrecorder.recorderservice.manager.statusbar.FluidCardStatusBar.shouldUpdateUi
import org.json.JSONObject

object RecordStatusBarUpdater : RecorderControllerListener {

    const val FROM_SHOW_RUNNABLE = "showRunnable"
    const val FROM_SERVICE_END = "serviceEnd"
    const val FROM_RETRY = "failedAndRetry"
    const val FROM_FOREGROUND_CHANGE = "foregroundChange"
    private const val FROM_SUPPORT_FLUID_CARD_CHANGED = "supportFluidCardChanged"
    private const val FROM_SCREEN_CHANGE = "screenChange"
    private const val FROM_SCREEN_READY = "screenReady"
    private const val FROM_CONFIGURATION_CHANGE = "configurationChange"
    private const val FROM_STATUS_CHANGE = "statusChange"
    private const val FROM_WAVE_CHANGE = "waveChange"
    private const val FROM_MARK_CHANGE = "markChange"
    private const val FROM_AUDIO_CONNECTION = "audioConnection"
    private const val FROM_SAVE_FILE_STATE = "saveFileStateChanged"
    private const val TAG = "RecordStatusBarUpdater"

    @JvmStatic
    private var hasForeground = false
    @JvmStatic
    private var isShowWithTimeout = false
    @JvmStatic
    private var isScreenOff: Boolean? = null
    private val screenStateLiveData by lazy { ScreenStateLiveData() }
    @JvmStatic
    private val screenStateObserver by lazy {
        Observer<Boolean> {
            isScreenOff = !it
            if (it) {
                refreshSeedlingData(FROM_SCREEN_CHANGE)
                DebugUtil.i(TAG, "screen on")
            } else {
                DebugUtil.i(TAG, "screen off")
            }
        }
    }

    private val seedingApi by lazy {
        Injector.injectFactory<SeedingInterface>()
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    override fun onReadyService() {
        initData()
    }

    override fun onSaveFileStateChange(
        state: Int,
        fileName: String,
        fullPath: String?,
        e: RecoverableSecurityException?
    ) {
        showOrHide(FROM_SAVE_FILE_STATE, fileName)
    }

    private fun initData() {
        //如果在上次录制结束消失倒计时还未完成再次点击开始录制需要取消上次倒计时并释放资源
        FluidCardStatusBar.releaseTimerAndData(FROM_SCREEN_READY, false)
        seedingApi?.init()
        //切换到子线程中处理
        seedingApi?.doActionInThread {
            val ctx = BaseApplication.getApplication()
            seedingApi?.initSupportFluidCardCallback(ctx) { old, new ->
                if (old != null && old != new) {
                    // 变更后尝试重新显示或者隐藏胶囊
                    showOrHide(FROM_SUPPORT_FLUID_CARD_CHANGED)
                }
            }
            if (seedingApi?.isSupportFluidCloud(ctx) == true) {
                seedingApi?.registerResultCallBack()
            }
            seedingApi?.isSupportSystemSendIntent(ctx)
        }
        screenStateLiveData.observeForever(screenStateObserver)
    }

    override fun onCloseService() {
        FluidCardStatusBar.showOrUpdateData(BaseApplication.getAppContext(), FROM_SERVICE_END)
        releaseData()
    }

    private fun releaseData() {
        //开始支持流体云注册成功，后期支持变更导致没有回调，因此此处未加判断直接解注册，由于内部实现是广播，因此加try
        seedingApi?.unRegisterResultCallBack()
        screenStateLiveData.removeObserver(screenStateObserver)
        isScreenOff = null
        setIsShowWithTimeout(false)
        FluidCardStatusBar.release()
        SeedlingStatusBar.release()
        OldStatusBar.release()
    }

    private fun releaseSeeding() {
        if (!FluidCardStatusBar.isHideCardTimerRun()) {
            seedingApi?.release()
        }
    }

    @JvmStatic
    fun onSeedlingCardStateChanged(isShow: Boolean) {
        seedingApi?.doActionInThread {
            if (seedingApi?.isSupportFluidCloud(BaseApplication.getAppContext()) == true) {
                FluidCardStatusBar.onSeedlingCardStateChanged(isShow)
            }
        }
    }

    @JvmStatic
    fun setHasForeground(flag: Boolean) {
        hasForeground = flag
        showOrHide(FROM_FOREGROUND_CHANGE)
    }

    override fun onWaveStateChange(state: Int) {
        if (isScreenLocked() && !shouldUpdateUi) {
            return
        }

        refreshSeedlingData(from = FROM_WAVE_CHANGE)
    }

    fun isScreenLocked(): Boolean  {
        if (isScreenOff == null) {
            isScreenOff = DisplayUtils.isScreenLocked()
        }

        return isScreenOff!!
    }

    private fun refreshSeedlingData(from: String, forceRefresh: Boolean = false) {
        DebugUtil.d(TAG, "refreshSeedlingData from = $from, forceRefresh = $forceRefresh", true)
        if (FluidCardStatusBar.isShowing()) {
            FluidCardStatusBar.refreshSeedlingData(from = from, forceRefresh)
        } else if (SeedlingStatusBar.isShowing) {
            if (hasForeground) return
            SeedlingStatusBar.refreshSeedlingData(from = from, forceRefresh)
        }
    }

    override fun onConfigurationChanged() {
        DebugUtil.d(TAG, "onConfigurationChanged")
        if (isScreenLocked()) return
        refreshSeedlingData(from = FROM_CONFIGURATION_CHANGE, true)
    }

    override fun onRecordStatusChange(state: Int) {
        showOrHide(FROM_STATUS_CHANGE)
    }

    override fun onMarkDataChange(markAction: Int, errorOrIndex: Int) {
        DebugUtil.d(TAG, "onMarkDataChange")
        if (markAction == MarkAction.RENAME) {
            DebugUtil.d(TAG, "onMarkDataChange action = rename, just return")
            return
        }

        if (isScreenLocked()) return
        if (!FluidCardStatusBar.isShowing()) return

        FluidCardStatusBar.refreshSeedlingData(from = FROM_MARK_CHANGE)
    }

    override fun onRecordCallConnected() {
        if (isScreenLocked()) return
        if (!FluidCardStatusBar.isShowing()) return

        FluidCardStatusBar.refreshSeedlingData(from = FROM_AUDIO_CONNECTION)
    }

    @JvmStatic
    fun getSeedlingData(): JSONObject? {
        val ctx = BaseApplication.getApplication()
        return if (seedingApi?.isSupportFluidCloud(ctx) == true) {
            FluidCardStatusBar.getSeedlingData()
        } else if (seedingApi?.isSupportSystemSendIntent(ctx) == true) {
            SeedlingStatusBar.getSeedlingData()
        } else {
            null
        }
    }

    @JvmStatic
    fun showOrHide(from: String, fileName: String? = null) {
        DebugUtil.d(
            TAG,
            "showOrHide, from = $from, isFromOther = ${
                RecorderViewModel.getInstance().isFromOtherApp()
            }, curState = ${RecordStatusManager.getCurrentStatus()}, hasForeground = $hasForeground",
            true
        )
        val ctx = BaseApplication.getApplication()
        if (RecorderViewModel.getInstance().isFromOtherApp()) return

        //如果显示泛在是超时兜底显示的老胶囊，此次录制一直走老胶囊
        if (isShowWithTimeout) {
            showOrHideWithOldStatusBar(ctx, from)
            return
        }

        seedingApi?.getStatusBarSupportType(ctx) {
            when (it) {
                STATUS_BAR_SUPPORT_FLUID_CARD -> showOrHideWithFluidCardStatusBar(ctx, from, fileName)
                STATUS_BAR_SUPPORT_SEEDLING_CARD -> showOrHideWithSeedlingStatusBar(ctx, from)
                else -> showOrHideWithOldStatusBar(ctx, from)
            }
        }
    }

    /**
     * 设置此次显示泛在是否是超时兜底显示
     */
    @JvmStatic
    fun setIsShowWithTimeout(isShowWithTimeout: Boolean) {
        this.isShowWithTimeout = isShowWithTimeout
    }

    /**
     * show or hide流体云卡片胶囊
     */
    @JvmStatic
    private fun showOrHideWithFluidCardStatusBar(ctx: Context, from: String, fileName: String? = null) {
        DebugUtil.d(TAG, "showOrHideWithFluidCardStatusBar, from = $from", true)
        when (from) {
            FROM_FOREGROUND_CHANGE -> {} //前台时由系统切换卡片状态，回调onHide，此处不做处理
            else -> {
                if (RecorderViewModel.getInstance().isRecordingOrSaving()) {
                    FluidCardStatusBar.showOrUpdateData(ctx, from, fileName)
                } else {
                    dismissStatusBar(ctx, from)
                }
            }
        }
    }

    /**
     * show or hide泛在胶囊
     */
    @JvmStatic
    private fun showOrHideWithSeedlingStatusBar(ctx: Context, from: String) {
        DebugUtil.d(TAG, "showOrHideWithSeedlingStatusBar, from = $from", true)
        if (!hasForeground && RecorderViewModel.getInstance().isAlreadyRecordingExceptSaving()) {
            SeedlingStatusBar.show(ctx, from = from)
        } else {
            dismissStatusBar(ctx, from)
        }
    }

    /**
     * show or hide老胶囊
     */
    @JvmStatic
    fun showOrHideWithOldStatusBar(ctx: Context, from: String) {
        DebugUtil.d(TAG, "showOrHideWithOldStatusBar, from = $from", true)
        if (RecordStatusManager.getCurrentStatus() == RecordStatusManager.RECORDING && !hasForeground) {
            OldStatusBar.show(ctx, from)
        } else {
            dismissStatusBar(ctx, from)
        }
    }

    private fun dismissStatusBar(ctx: Context, from: String) {
        DebugUtil.d(TAG, "dismissStatusBar, from = $from", true)
        if (!FluidCardStatusBar.isHideCardTimerRun()) {
            FluidCardStatusBar.dismiss(ctx, false, from)
            SeedlingStatusBar.dismiss(ctx, false, from)
        } else {
            SeedlingStatusBar.unRegister()
        }
        OldStatusBar.dismiss(ctx, false, from)
    }

    @JvmStatic
    fun forceDismiss(from: String) {
        val ctx = BaseApplication.getApplication()
        DebugUtil.d(TAG, "forceDismiss, from = $from", true)
        //未进入过保存成功或保存失败状态倒计时消失的情况才能直接dismiss和releaseData 比如录制中点取消
        if (!FluidCardStatusBar.isHideCardTimerRun()) {
            //13.2的胶囊和14.0的胶囊hideStatusBar api是一样的，所以此处只调用一个
            SeedlingStatusBar.dismiss(ctx, true, from)
        }
        OldStatusBar.dismiss(ctx, true, from)
        releaseData()
        releaseSeeding()
    }

    @JvmStatic
    fun fluidCardDismiss(from: String) {
        FluidCardStatusBar.releaseTimerAndData(from)
    }
}