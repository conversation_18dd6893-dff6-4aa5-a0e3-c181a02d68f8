/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        : FluidStatusBar
 * * Description : 14.0流体云胶囊
 * * Version     : 1.0
 * * Date        : 2023/6/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import android.content.Context
import android.os.Handler
import android.os.Looper
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.oplus.keyguard.OplusKeyguardStyleManager
import com.oplus.keyguard.PanoramicAODUiCallback
import androidx.annotation.VisibleForTesting
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.recorder.INIT
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.recorderservice.api.RecorderViewModelApi
import com.soundrecorder.recorderservice.manager.RecorderViewModel
import org.json.JSONObject

object FluidCardStatusBar : ISeedlingStatusBar {
    private const val TAG = "FluidCardStatusBar"
    private const val DURATION_MARK_TIME_SHOW = 1500L
    private const val MAX_RETRY_TIME = 1

    private val mainHandler by lazy { Handler(Looper.getMainLooper()) }

    private val showRunnable by lazy {
        Runnable {
            val ctx = BaseApplication.getApplication() ?: return@Runnable
            DebugUtil.d(
                TAG,
                "showRunnable, from = ${RecordStatusBarUpdater.FROM_SHOW_RUNNABLE}",
                true
            )
            show(ctx, true, RecordStatusBarUpdater.FROM_SHOW_RUNNABLE)
        }
    }
    private val checkMarkTimeRunnable by lazy {
        Runnable {
            refreshMarkTimeSeedlingData()
        }
    }

    private var lastSeedlingDataInfo: SeedlingDataInfo? = null
    private var showMarkText: Boolean = false

    @JvmStatic
    private var isHideCardTimerRun = false
    @Volatile
    var shouldUpdateUi: Boolean = false

    private val panoramicAODUiCB: PanoramicAODUiCallback = object : PanoramicAODUiCallback {
        override fun onAODUiStateChanged(updateUi: Boolean) {
            DebugUtil.d(TAG, "onAODUiStateChanged updateUi  = $updateUi")
            shouldUpdateUi = updateUi
        }
    }

    private var manager: OplusKeyguardStyleManager? = null

    @JvmStatic
    private val hideCardTimer by lazy {
        FluidStatusBarCloseTimer {
            DebugUtil.d(TAG, " FluidStatusBarCloseTimer form = $it")
            releaseTimerAndData(it)
        }
    }
    @Volatile
    var showState = FluidCardShowState.DEFAULT
        private set

    @Volatile
    private var showRetryTimes = MAX_RETRY_TIME

    @Volatile
    private var hideRetryTimes = MAX_RETRY_TIME

    private val showRetryTimer by lazy {
        SeedlingStatusBarTimer {
            processShowFailed(BaseApplication.getAppContext(), RecordStatusBarUpdater.FROM_RETRY)
        }
    }
    private val hideRetryTimer by lazy {
        SeedlingStatusBarTimer {
            processHideFailed(BaseApplication.getAppContext(), RecordStatusBarUpdater.FROM_RETRY)
        }
    }

    private val seedingApi by lazy {
        Injector.injectFactory<SeedingInterface>()
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    /**
     * 来自RecordSeedlingCardWidgetProvider生命周期回调onShow/onHide
     */
    @JvmStatic
    fun onSeedlingCardStateChanged(isShow: Boolean) {
        if (isShow) {
            refreshSeedlingData(RecordStatusBarUpdater.FROM_FOREGROUND_CHANGE, true)
        }
    }

    /**
     * 流体云卡是否显示
     * @param isStrict 是否严格校验，非严格模式下requesting\showing\hidden状态均为显示
     */
    @JvmStatic
    fun isShowing(isStrict: Boolean = true): Boolean {
        return if (isStrict) {
            showState == FluidCardShowState.SHOWING
        } else {
            showState != FluidCardShowState.DEFAULT
        }
    }

    fun isHideCardTimerRun(): Boolean {
        return isHideCardTimerRun
    }

    /**
     * show流体云卡片或者更新数据
     * showing状态更新
     * requesting或者default状态强制show
     * hidden状态暂时不处理
     */
    @JvmStatic
    fun showOrUpdateData(ctx: Context, from: String, fileName: String? = null) {
        when (showState) {
            FluidCardShowState.SHOWING -> refreshSeedlingData(from, false, fileName)
            FluidCardShowState.DEFAULT, FluidCardShowState.REQUESTING -> show(ctx, false, from)
        }
        if (!RecorderViewModel.getInstance()
                .hasInitRecorderService() || RecorderViewModel.getInstance()
                .isAlreadySaveOrSaveFail()
        ) {
            dismissAfterTimerEnd(from)
        }
    }

    @JvmStatic
    fun refreshSeedlingData(from: String, forceRefresh: Boolean = false, fileName: String? = null) {
        DebugUtil.d(
            TAG,
            "refreshSeedlingData from = $from,forceRefresh = $forceRefresh,showState = $showState"
        )

        if (!isShowing()) return
        val newSeedlingDataInfo = createSeedlingDataInfo(fileName)
        if (!forceRefresh && newSeedlingDataInfo == lastSeedlingDataInfo) return
        seedingApi?.refreshSeedlingData(getSeedlingData(newSeedlingDataInfo))
    }

    /**
     * XX已标记，显示时间结束，刷新UI隐藏控件
     */
    @VisibleForTesting
    @JvmStatic
    fun refreshMarkTimeSeedlingData(isScreenLocked: Boolean = RecordStatusBarUpdater.isScreenLocked()) {
        if (!isShowing()) return
        if (isScreenLocked) return
        val newSeedlingDataInfo = createSeedlingDataInfo()
        //1.5秒倒计时结束，判断两次数据上次标记时间相同，则将showMarkText = false
        if (lastSeedlingDataInfo?.curMarkTime == newSeedlingDataInfo.curMarkTime) {
            showMarkText = false
        }
        seedingApi?.refreshSeedlingData(getSeedlingData(newSeedlingDataInfo))
    }

    @VisibleForTesting
    @JvmStatic
    fun createSeedlingDataInfo(fileName: String? = null): SeedlingDataInfo {
        return SeedlingDataInfo(
            RecorderViewModelApi.getAmplitudeCurrentTime(),
            RecorderViewModelApi.getCurrentStatus(),
            RecorderViewModelApi.getLastMarkTime(),
            RecorderViewModelApi.isMarkEnabledFull(),
            getRecordEnableState(),
            recorderViewModelApi?.saveFileState,
            fileName
        )
    }

    @VisibleForTesting
    @JvmStatic
    fun getRecordEnableState(): Boolean {
        if (recorderViewModelApi?.saveFileState != INIT) {
            return false
        }

        if (RecorderViewModelApi.isAudioModeChangePause()) {
            return false
        }

        return true
    }

    @JvmStatic
    fun getSeedlingData(newSeedlingDataInfo: SeedlingDataInfo = createSeedlingDataInfo()): JSONObject {
        checkMarkTimeInfo(newSeedlingDataInfo, lastSeedlingDataInfo)
        val jsonData: JSONObject
        val isOOS = BaseUtil.isOnePlusExp() && BaseUtil.isAndroidVOrLater
        if (isHideCardTimerRun && !isOOS) {
            jsonData = JSONObject()
            lastSeedlingDataInfo!!.getSaveRecordData(jsonData)
        } else {
            jsonData = newSeedlingDataInfo.getChangedData(showMarkText, lastSeedlingDataInfo)
            lastSeedlingDataInfo = newSeedlingDataInfo
        }
        return jsonData
    }

    /**
     * 检查是否需要show标记信息 & 发送1.5s延时
     * 条件：本次有标记信息 & 非首次展示（onUpdateData或者首次show时的默认数据） & 本次标记信息和上次信息不同
     */
    @VisibleForTesting
    @JvmStatic
    fun checkMarkTimeInfo(
        newSeedlingDataInfo: SeedlingDataInfo,
        lastSeedlingDataInfo: SeedlingDataInfo?
    ) {
        if (newSeedlingDataInfo.curMarkTime != -1L &&
            (lastSeedlingDataInfo != null && lastSeedlingDataInfo.curMarkTime != newSeedlingDataInfo.curMarkTime)) {
            showMarkText = true
            mainHandler.removeCallbacks(checkMarkTimeRunnable)
            mainHandler.postDelayed(checkMarkTimeRunnable, DURATION_MARK_TIME_SHOW)
        }
    }

    override fun show(ctx: Context, forceShow: Boolean, from: String) {
        DebugUtil.d(
            TAG,
            "fluidStatusBar show, forceShow = $forceShow, showState = $showState, from = $from",
            true
        )
        if (forceShow || showState in arrayOf(FluidCardShowState.DEFAULT, FluidCardShowState.REQUESTING)) {
            showRetryTimes = MAX_RETRY_TIME
            showInternal(ctx, from)
            if (OS12FeatureUtil.isColorOS16OrLater()) {
                initOplusKeyguardStyleManager(ctx)
            }
            register()
        }
    }

    /**
     * 初始化OplusKeyguardStyleManager 这个SDK必须OS16及以上才能调用
     */
    private fun initOplusKeyguardStyleManager(ctx: Context) {
        kotlin.runCatching {
            if (manager == null) {
                DebugUtil.d(TAG, " initOplusKeyguardStyleManager")
                manager = OplusKeyguardStyleManager.getInstance(ctx)
            }
        }.onFailure {
            DebugUtil.e(TAG, "initOplusKeyguardStyleManager error: $it")
        }
    }

    /**
     * 发送泛在show意图，接收意图回调
     * 1、回调结果后，则取消等待timer
     * 2、首次回调失败，重试发送意图
     * 3、再次失败，展示老状态样式
     */
    @JvmStatic
    private fun showInternal(ctx: Context, from: String) {
        //runnable过来的show 状态为？？
        DebugUtil.d(TAG, "showInternal, showState = $showState, from = $from", true)
        if (!isShowing(true)) {
            // 已经在show状态下，调用发送显示意图，不会回调onShow()，会导致state错误胶囊不更新，此处增加show拦截，避免状态错误
            showState = FluidCardShowState.REQUESTING
        }
        val originData = getSeedlingData()
        val initData = JSONObject().also {
            it.put("initData", originData.toString())
        }
        seedingApi?.sendShowSeedlingStatusBar(initData) { result ->
            DebugUtil.d(TAG, "showInternal,callback result=$result,showState=$showState")
            //发送意图成功当前尚未显示，显示成功会回调provider的onShow方法
            if (result && showState == FluidCardShowState.REQUESTING) {
                showState = FluidCardShowState.SHOWING
            }
            showRetryTimer.cancelTimer()
            if (!result) {
                processShowFailed(ctx, from)
            }
        }
        hideRetryTimer.cancelTimer()
        showRetryTimer.startTimer()
    }

    /**
     * 处理卡片show失败
     * 超时或者决策失败
     */
    @JvmStatic
    private fun processShowFailed(ctx: Context, from: String) {
        DebugUtil.w(TAG, "processShowFailed, from = $from, startRetryTimes = $showRetryTimes", true)
        if (showRetryTimes > 0) {
            showRetryTimes--
            showInternal(ctx, from)
        } else {
            //超时情况调用dismiss方法降级老胶囊兜底
            showState = FluidCardShowState.DEFAULT
            dismiss(ctx, true, from)
            RecordStatusBarUpdater.setIsShowWithTimeout(true)
            RecordStatusBarUpdater.showOrHideWithOldStatusBar(ctx, from)
        }
    }

    override fun dismiss(ctx: Context, forceDismiss: Boolean, from: String) {
        DebugUtil.d(TAG, "fluidStatusBar dismiss, forceDismiss = $forceDismiss, from = $from, showState = $showState", true)
        if (forceDismiss || isShowing(isStrict = false)) {
            hideRetryTimes = MAX_RETRY_TIME
            dismissInternal(ctx, from, forceDismiss)
            unRegister()
        }
    }

     private fun dismissAfterTimerEnd(from: String) {
        DebugUtil.d(TAG, "dismissAfterTimerEnd from = $from, isHideCardTimerRun = $isHideCardTimerRun", true)
        if (!isHideCardTimerRun) {
            isHideCardTimerRun = true
            hideCardTimer.startTimer(from)
        }
    }

    /**
     * 发送泛在hide意图，接收意图回调
     * 1、回调结果后，则取消等待timer
     * 2、首次回调失败，重试发送意图
     * 3、再次失败，展示老状态样式
     */
    @JvmStatic
    private fun dismissInternal(ctx: Context, from: String, forceDismiss: Boolean = false) {
        DebugUtil.w(
            TAG,
            "dismissInternal, showState = $showState, endRetryTimes = $hideRetryTimes, from = $from",
            true
        )
        seedingApi?.sendHideSeedlingStatusBar(forceDismiss) { result ->
            DebugUtil.w(
                TAG,
                "dismissInternal, result = $result, showState = $showState"
            )
            //dismiss成功或者本身在request状态都将状态置为DEFAULT
            if (result) {
                showState = FluidCardShowState.DEFAULT
            }
            hideRetryTimer.cancelTimer()

            if (!result) {
                processHideFailed(ctx, from)
            }
        }
        hideRetryTimer.startTimer()
    }

    /**
     * 处理卡片hide失败
     * 超时或者决策失败
     */
    @JvmStatic
    private fun processHideFailed(ctx: Context, from: String) {
        DebugUtil.w(TAG, "processHideFailed, endRetryTimes = $hideRetryTimes", true)
        if (hideRetryTimes > 0) {
            hideRetryTimes--
            dismissInternal(ctx, from)
        }
    }

    @VisibleForTesting
    override fun register() {
        mainHandler.removeCallbacks(showRunnable)
        mainHandler.postDelayed(showRunnable, SeedlingStatusBar.MAX_SHOW_DURATION)
        // 流体云业务开始时，如录音开始、计时器开始时，注册Callback
        manager?.registerPanoramicAODUiCallback(panoramicAODUiCB)
    }

    @VisibleForTesting
    override fun unRegister() {
        mainHandler.removeCallbacksAndMessages(null)
        lastSeedlingDataInfo = null
        showMarkText = false
        // 流体云业务结束时，如录音结束、计时器结束时，反注册Callback
        manager?.unregisterPanoramicAODUiCallback(panoramicAODUiCB)
    }

    override fun release() {
        showRetryTimer.cancelTimer()
        hideRetryTimer.cancelTimer()
        if (!isHideCardTimerRun) {
            showState = FluidCardShowState.DEFAULT
        }
    }

    fun releaseTimerAndData(from: String, dismissCard: Boolean = true) {
        DebugUtil.w(TAG, "releaseTimerAndData, from = $from ,isHideCardTimerRun = $isHideCardTimerRun", true)
        val ctx = BaseApplication.getApplication()
        if (isHideCardTimerRun) {
            isHideCardTimerRun = false
            showState = FluidCardShowState.DEFAULT
            hideCardTimer.cancelTimer()
            if (dismissCard) {
                dismiss(ctx, true, from)
            } else {
                unRegister()
            }
            seedingApi?.release()
        }
    }

    /**
     * DEFAULT————未显示
     * REQUESTING——请求展示
     * SHOWING——正在显示
     * HIDDEN——意图决策成功，未展示或者在前台不显示胶囊
     */
    enum class FluidCardShowState {
        DEFAULT, REQUESTING, SHOWING
    }
}