/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordRecorderFactory
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.recorder

import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.recorderservice.recorder.listener.IBizRecorder

object RecordRecorderFactory {

    fun newRecorder(format: Int): IBizRecorder {
        DebugUtil.i("RecordRecorderFactory", "recorderAduioFormat $format")
        return when (format) {
            RecorderConstant.RECORDER_AUDIO_FORMAT_AMR_NB,
            RecorderConstant.RECORDER_AUDIO_FORMAT_AMR_WB,
            RecorderConstant.RECORDER_AUDIO_FORMAT_MP3 -> {
                getDefaultRecorder(format)
            }
            RecorderConstant.RECORDER_AUDIO_FORMAT_WAV -> {
                // 一加仅高通平台底层定制了wav录制
//                if (BaseUtil.isOnePlusExp()) {
//                    AndroidMediaRecorder(format)
//                }
                getDefaultRecorder(format)
            }
            RecorderConstant.RECORDER_AUDIO_FORMAT_AAC_ADTS -> AndroidMediaRecorder(format)

            else -> OPlusRecorderExpanded(RecorderConstant.RECORDER_AUDIO_FORMAT_MP3)
        }
    }

    private fun getDefaultRecorder(format: Int): IBizRecorder {
        return if (BaseUtil.isAndroidROrLater) {
            OPlusRecorderExpanded(format)
        } else {
            RecorderExpanded(format)
        }
    }
}