/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderBroadCastReceivers
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.AudioManager
import com.soundrecorder.base.ext.registerReceiverCompat
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.constant.OplusCompactConstant
import com.soundrecorder.recorderservice.manager.listener.MuteModeChangeListener

class RecorderBroadCastReceivers {

    companion object {
        const val TAG = "RecorderBroadCastReceivers"
    }

    var needRegisterCameraReceiver: Boolean = true


    private var mReceiver: BroadcastReceiver? = null
    private var mCameraReceiver: BroadcastReceiver? = null

    var mMuteModeChangeListener: MuteModeChangeListener? = null
    var onBroadCastReceiveCallback: IBroadCastReceiveCallback? = null

    fun registerBroadcastReceivers(context: Context) {
        registerCameraReceiver(context)
        registerSystemReceiver(context)
    }

    private fun registerSystemReceiver(context: Context) {
        if (mReceiver == null) {
            var intentFilter = IntentFilter()
            //监听关机广播
            intentFilter.addAction(Intent.ACTION_SHUTDOWN)
            //监听用户设置RING_MODE广播
            intentFilter.addAction(AudioManager.RINGER_MODE_CHANGED_ACTION)
            DebugUtil.i(
                TAG, "registerBroadcastReceivers needRegisterCameraReceiver == $needRegisterCameraReceiver")

            mReceiver = object : BroadcastReceiver() {

                override fun onReceive(context: Context?, intent: Intent?) {
                    val action: String? = intent?.action
                    DebugUtil.i(TAG, "onReceive action $action")
                    //收到用户更改
                    if (AudioManager.RINGER_MODE_CHANGED_ACTION == action) {
                        DebugUtil.i(TAG, "receive ring mode change action $action")
                        mMuteModeChangeListener?.onRingModeChanged()
                    } else if (Intent.ACTION_SHUTDOWN == action) {
                        val isShutDown = intent.getBooleanExtra("isShutDown", false)
                        DebugUtil.i(
                            TAG, "receive shutdown action $action, isShutDown: $isShutDown")
                        if (isShutDown) {
                            RecordStopExceptionProcessor.dispatchStopEvent(
                                RecordStopExceptionProcessor.STOP_EVENT_TYPE_SHUT_DOWN) //onBroadCastReceiveCallback?.onShutDown()
                        }
                    }
                }
            }
            /**
             * (1) 原生sticky广播
             *接收者需要去除接收器中设定的permission组件安全权限
             */
            context.registerReceiver(mReceiver, intentFilter, null, null)
            DebugUtil.i(
                TAG, "registerBroadcastReceivers needRegisterCameraReceiver $needRegisterCameraReceiver")
        }
    }

    /**
     * 注册相机发送广播
     */
    private fun registerCameraReceiver(context: Context) {
        if ((!needRegisterCameraReceiver) && (mCameraReceiver == null)) {
            //监听相机发送过来的停止录音的广播
            val intentFilter = IntentFilter().apply {
                // befor os12.0
                addAction(OplusCompactConstant.STOP_RECORDER_NORMAL_BEFOR)
                // after os12.0
                addAction(OplusCompactConstant.STOP_RECORDER_AFTER)
            }

            mCameraReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    val action: String? = intent?.action
                    //收到相机发送的停止录音的广播
                    if (OplusCompactConstant.STOP_RECORDER_NORMAL_BEFOR == action
                        || OplusCompactConstant.STOP_RECORDER_AFTER == action
                    ) {
                        DebugUtil.i(TAG, "receive camera action $action")
                        //RecorderUtil.sendLocalBroadcast(context, Intent(RecorderService.ACTION_RECORDER_STOP_RECORDER_ABNORMAL))
                        //onBroadCastReceiveCallback?.onStopRecord()
                        RecordStopExceptionProcessor.dispatchStopEvent(RecordStopExceptionProcessor.STOP_EVENT_TYPE_CAMERA)
                        //收到用户更改
                    }
                }
            }
            context.registerReceiverCompat(
                mCameraReceiver, intentFilter,
                Constants.PERMISSION_OPPO_COMPONENT_SAFE, null
            )
        }
    }

    fun unRegisterBroadcastReceivers(context: Context) {
        if (mReceiver != null) {
            context.unregisterReceiver(mReceiver)
            mReceiver = null
        }
        if (mCameraReceiver != null) {
            context.unregisterReceiver(mCameraReceiver)
            mCameraReceiver = null
        }
    }

    interface IBroadCastReceiveCallback {

        //收到关机广播
        /*stop(false)
        <EMAIL>()*/
        fun onShutDown()


        // 收到相机广播
        /*stop(false)
        <EMAIL>()*/
        fun onStopRecord()
    }
}