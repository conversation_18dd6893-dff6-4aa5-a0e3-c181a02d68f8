/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  OldStatusBarTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/7/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.manager.statusbar

import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.OplusCompactUtil
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.recorderservice.shadows.ShadowOplusCompactUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyString
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowOplusCompactUtil::class]
)
class OldStatusBarTest {

    private var ctx: Context? = null
    private var oplusCompactUtil: MockedStatic<OplusCompactUtil>? = null

    @Before
    fun setUp() {
        ctx = ApplicationProvider.getApplicationContext()
        oplusCompactUtil = Mockito.mockStatic(OplusCompactUtil::class.java)
    }

    @After
    fun reset() {
        ctx = null
        oplusCompactUtil?.close()
        oplusCompactUtil = null
        Whitebox.setInternalState(OldStatusBar::class.java, "isShowing", false)
    }

    @Test
    @Synchronized
    fun should_invoke_method_when_show() {
        val ctx = this.ctx ?: return
        Whitebox.setInternalState(OldStatusBar::class.java, "isShowing", false)
        OldStatusBar.show(ctx, "test")
        oplusCompactUtil?.verify({
            OplusCompactUtil.getActionForIntent(any(Intent::class.java), anyString(), anyString())
        }, Mockito.times(1))
    }

    @Test
    @Synchronized
    fun should_return_false_when_dismiss() {
        val ctx = this.ctx ?: return
        OldStatusBar.dismiss(ctx, true, "test")
        Assert.assertFalse(Whitebox.getInternalState(OldStatusBar::class.java, "isShowing"))
    }

    private fun <T> any(type: Class<T>): T = Mockito.any<T>(type)
}