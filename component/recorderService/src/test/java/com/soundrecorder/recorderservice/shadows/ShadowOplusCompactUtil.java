/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderBinder.java
 Description:
 Version: 1.0
 Date: 2022/6/9
 Author: <EMAIL>
 -----------Revision History-----------
 <author> <date> <version> <desc>
 <EMAIL> 2022/6/9 1.0 create
 */

package com.soundrecorder.recorderservice.shadows;

import com.soundrecorder.base.utils.OplusCompactUtil;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;

@Implements(OplusCompactUtil.class)
public class ShadowOplusCompactUtil {

    @Implementation
    public static boolean isOver11dot3() {
        return true;
    }
}
