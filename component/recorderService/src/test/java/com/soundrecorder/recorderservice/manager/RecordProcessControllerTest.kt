/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecordProcessControllerTest
 Description:
 Version: 1.0
 Date: 2022/10/9
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/10/9 1.0 create
 */

package com.soundrecorder.recorderservice.manager

import android.content.Context
import android.os.Build
import android.os.Handler
import android.util.Log
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.constant.RecorderConstant
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.recorderservice.RecordResult
import com.soundrecorder.recorderservice.RecorderService
import com.soundrecorder.recorderservice.controller.observer.RecordInfoSaveObserver
import com.soundrecorder.recorderservice.controller.observer.WaveObserver
import com.soundrecorder.recorderservice.manager.RecordStatusManager.HALT_ON
import com.soundrecorder.recorderservice.manager.RecordStatusManager.INIT
import com.soundrecorder.recorderservice.manager.RecordStatusManager.PAUSED
import com.soundrecorder.recorderservice.manager.RecordStatusManager.RECORDING
import com.soundrecorder.recorderservice.manager.RecordStopExceptionProcessor.STOP_EVENT_TYPE_CAMERA
import com.soundrecorder.recorderservice.manager.RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDE_ERROR_INFO
import com.soundrecorder.recorderservice.manager.RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDE_EXCEPTION
import com.soundrecorder.recorderservice.manager.RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDE_OVERLIMIT
import com.soundrecorder.recorderservice.manager.RecordStopExceptionProcessor.STOP_EVENT_TYPE_RECORDINGFILE_DELETE
import com.soundrecorder.recorderservice.manager.RecordStopExceptionProcessor.STOP_EVENT_TYPE_SHUT_DOWN
import com.soundrecorder.recorderservice.manager.RecordStopExceptionProcessor.STOP_EVENT_TYPE_STORAGE_NOT_ENOUGH
import com.soundrecorder.recorderservice.manager.listener.RecordResultCallback
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.recorderservice.shadows.ShadowOplusCompactUtil
import com.soundrecorder.recorderservice.shadows.ShadowOplusUsbEnvironment
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowOplusUsbEnvironment::class,
        ShadowOplusCompactUtil::class]
)
class RecordProcessControllerTest {

    private var mContext: Context? = null
    private var mRecordProcessController: RecordProcessController? = null
    private var mRecorderService: RecorderService? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mRecorderService = RecorderService()
        val config = RecordExpandController.RecordConfig()
        val otherConfig = RecordExpandController.OtherConfig()
        val muteConfig = MuteModeOperator.MuteConfig()
        mRecordProcessController =
            RecordProcessController(mRecorderService, config, otherConfig, muteConfig)
        mRecordProcessController?.initArgs()
        val mRecorderExpandController = RecordExpandController(config, otherConfig, muteConfig)
        val mRecorderUIController = RecorderUIController(mRecorderExpandController)
        mRecordProcessController?.mRecorderUIController = mRecorderUIController
        mRecordProcessController?.mRecordResultCallback = object : RecordResultCallback {
            override fun onRecordResultArrived(result: RecordResult) {
                Log.d("onRecordResultArrived", "onRecordResultArrived")
            }
        }
    }

    @After
    fun tearDown() {
        mContext = null
        mRecordProcessController = null
    }

    @Test
    fun should_notNull_when_updateRecordConfig() {
        val config = RecordExpandController.RecordConfig()
        config.format = RecorderConstant.RECORDER_AUDIO_FORMAT_MP3
        config.recordType = RecordModeConstant.RECORD_TYPE_STANDARD
        config.maxSizeLimit = 1000L
        config.maxDurationLimit = 10000
        mRecordProcessController?.updateRecordConfig(config)
        Assert.assertNotNull(mRecordProcessController?.recordConfig)
        Assert.assertTrue((mRecordProcessController?.recordConfig?.maxSizeLimit ?: 0) == 1000L)
        Assert.assertTrue((mRecordProcessController?.recordConfig?.maxDurationLimit ?: 0) == 10000)
    }

    @Test
    fun should_notNull_when_updateOtherConfig() {
        val otherConfig = RecordExpandController.OtherConfig()
        otherConfig.isFromOtherApp = false
        otherConfig.recordFromType = RecordExpandController.OtherConfig.START_FROM_NORMAL
        otherConfig.isNeedResult = false
        mRecordProcessController?.updateOtherConfig(otherConfig)
        Assert.assertNotNull(mRecordProcessController?.otherConfig)
        Assert.assertTrue(mRecordProcessController?.otherConfig?.isFromSlidBar() == false)
        Assert.assertTrue(mRecordProcessController?.otherConfig?.isFromBreno() == false)
        Assert.assertTrue(mRecordProcessController?.otherConfig?.isFromBrenoFront() == false)
        Assert.assertTrue(mRecordProcessController?.otherConfig?.isFromAppCard() == false)
        Assert.assertTrue(mRecordProcessController?.otherConfig?.isFromSmallCard() == false)
        Assert.assertTrue(mRecordProcessController?.otherConfig?.isFromMiniApp() == false)
        Assert.assertTrue(
            mRecordProcessController?.otherConfig?.recordFromType
                    == RecordExpandController.OtherConfig.START_FROM_NORMAL
        )
    }

    @Test
    fun should_notNull_when_initHandlerThread() {
        mRecordProcessController?.initHandlerThread()
        val mWorkHandler: Handler =
            Whitebox.getInternalState(mRecordProcessController, "mWorkHandler")
        Assert.assertNotNull(mWorkHandler)
        RecordStatusManager.changeRecordStatus(RECORDING)
        mWorkHandler.sendEmptyMessage(RecordProcessController.MSG_CHECK_DISK)
        Assert.assertTrue(RecordStatusManager.getCurrentStatus() == RECORDING)
        mRecordProcessController?.startRecord()
        mRecordProcessController?.pauseRecord()
        mRecordProcessController?.resumeRecord()
        mRecordProcessController?.stopRecord()
        mRecordProcessController?.stopRecord(true)
        mRecordProcessController?.cancelRecord()
        mRecordProcessController?.release()
    }

    @Test
    fun should_notNull_when_releaseHandlerThread() {
        mRecordProcessController?.initHandlerThread()
        val mWorkHandler: Handler =
            Whitebox.getInternalState(mRecordProcessController, "mWorkHandler")
        Assert.assertNotNull(mWorkHandler)
        mRecordProcessController?.releaseHandlerThread()
    }

    @Test
    fun should_notNull_when_saveRecordInfo() {
        mRecordProcessController?.initHandlerThread()
        val markList = arrayListOf<MarkDataBean>()
        markList.add(MarkDataBean(1000, 1))
        mRecordProcessController?.saveRecordInfo("", "", markList, false)
    }

    @Test
    fun should_notNull_when_onStopEvent() {
        mRecordProcessController?.onStopEvent(STOP_EVENT_TYPE_CAMERA)
        mRecordProcessController?.onStopEvent(STOP_EVENT_TYPE_RECORDE_OVERLIMIT)
        mRecordProcessController?.onStopEvent(STOP_EVENT_TYPE_STORAGE_NOT_ENOUGH)

        mRecordProcessController?.onStopEvent(STOP_EVENT_TYPE_RECORDE_ERROR_INFO)
        mRecordProcessController?.onStopEvent(STOP_EVENT_TYPE_RECORDE_EXCEPTION)
        mRecordProcessController?.onStopEvent(STOP_EVENT_TYPE_RECORDINGFILE_DELETE)
        mRecordProcessController?.onStopEvent(STOP_EVENT_TYPE_SHUT_DOWN)
    }

    @Test
    fun should_notNull_when_initRecordUIControllerForSlidBar() {
        mRecordProcessController?.initRecordUIControllerForSlidBar()
        Assert.assertNotNull(mRecordProcessController?.mRecorderUIController)
    }

    @Test
    fun should_notNull_when_registerUICallback() {
        val callback = object : RecorderUIController.RecorderStateListener {
            override fun onRecordStatusChange(state: Int) {
            }

            override fun onRecordCallConnected() {
            }

            override fun saveDialogCallback(
                name: String?,
                fullPath: String?,
                saveRecordFromWhere: Int
            ) {
            }
        }
        mRecordProcessController?.registerUICallback(callback)
        Assert.assertNotNull(mRecordProcessController?.mRecorderUIController?.mRecorderStateListener)
        mRecordProcessController?.unregisterUICallback()
        Assert.assertNull(mRecordProcessController?.mRecorderUIController?.mRecorderStateListener)
    }

    @Test
    fun should_notNull_when_registerRecorderControlObservers() {
        val mWaveObserver = object : WaveObserver {
            override fun onStartSample() {
            }

            override fun onStopSample() {
            }

            override fun onUpdateWave() {
            }
        }
        val mRecordInfoSaveObserver = object : RecordInfoSaveObserver {
            override fun updateLoadingCallback(name: String?) {
            }

            override fun saveDialogCallback(
                name: String?,
                fullPath: String?,
                saveRecordFromWhere: Int
            ) {
            }
        }
        mRecordProcessController?.registerRecorderControlObservers(
            mWaveObserver,
            mRecordInfoSaveObserver
        )
        Assert.assertNotNull(
            Whitebox.getInternalState<WaveObserver>(
                mRecordProcessController?.mRecorderUIController,
                "mWaveObserver"
            )
        )
        Assert.assertNotNull(
            Whitebox.getInternalState<WaveObserver>(
                mRecordProcessController?.mRecorderUIController,
                "mRecordInfoSaveObserver"
            )
        )
        mRecordProcessController?.updateRecorderControlObservers(
            mWaveObserver,
            mRecordInfoSaveObserver
        )
        mRecordProcessController?.unRegisterWaveUIObserver()
        Assert.assertNull(
            Whitebox.getInternalState<WaveObserver>(
                mRecordProcessController?.mRecorderUIController,
                "mWaveObserver"
            )
        )
        Assert.assertNull(
            Whitebox.getInternalState<WaveObserver>(
                mRecordProcessController?.mRecorderUIController,
                "mRecordInfoSaveObserver"
            )
        )
    }

    @Test
    fun should_notNull_when_updateNotification() {
        mRecordProcessController?.updateNotification()
        mRecordProcessController?.cancelRecordNotification()
    }

    @Test
    fun should_notNull_when_onRecordStatusChange() {
        val callback = object : RecorderUIController.RecorderStateListener {
            override fun onRecordStatusChange(state: Int) {
            }

            override fun onRecordCallConnected() {
            }

            override fun saveDialogCallback(
                name: String?,
                fullPath: String?,
                saveRecordFromWhere: Int
            ) {
            }
        }
        mRecordProcessController?.registerUICallback(callback)
        mRecordProcessController?.onRecordStatusChange(INIT, HALT_ON)
        mRecordProcessController?.onRecordStatusChange(RECORDING, HALT_ON)
        mRecordProcessController?.onRecordStatusChange(PAUSED, HALT_ON)
        mRecordProcessController?.onRecordStatusChange(HALT_ON, RECORDING)
    }
}