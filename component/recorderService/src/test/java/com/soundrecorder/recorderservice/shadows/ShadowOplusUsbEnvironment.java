/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: ShadowOppoUsbEnvironment.java
 Description:
 Version: 1.0
 Date: 2019/8/22
 Author: LI Kun
 -----------Revision History-----------
 <author> <date> <version> <desc>
 LI Kun 2019/8/22 1.0 create
 */

package com.soundrecorder.recorderservice.shadows;

import android.content.Context;
import android.os.Environment;
import com.oplus.os.OplusUsbEnvironment;
import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;
import java.io.File;

@Implements(OplusUsbEnvironment.class)
public class ShadowOplusUsbEnvironment {
    private static final String EXTERNAL_PATH = "sdcard/of/path";
    private static final String INTERNAL_PATH = "phone/of/path";
    private static final String EXTERNAL_SD_PATH = "ExternalSd/of/path";
    private static final String INTERNAL_SD_PATH = "InternalSd/of/path";

    @Implementation
    public static String getExternalPath(Context context) {
        if (null != context) {
            return EXTERNAL_PATH;
        }
        return null;
    }

    @Implementation
    public static boolean isVolumeMounted(Context context, String path) {
        if (null != context) {
            return true;
        }
        return false;
    }

    @Implementation
    public static String getInternalPath(Context context) {
        if (null != context) {
            return INTERNAL_PATH;
        }
        return null;
    }

    @Implementation
    public static File getInternalSdDirectory(Context context) {
        File file = new File(INTERNAL_SD_PATH);
        return file;
    }

    @Implementation
    public static File getExternalSdDirectory(Context context) {
        File file = new File(EXTERNAL_SD_PATH);
        return file;
    }

    @Implementation
    public static String getExternalSdState(Context context) {
        return Environment.MEDIA_MOUNTED;
    }

    @Implementation
    public static String getInternalSdState(Context context) {
        return Environment.MEDIA_MOUNTED;
    }
}
