/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveSampleWorkerTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/5/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.recorderservice.controller.worker

import android.net.Uri
import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.recorderservice.controller.RecorderController
import com.soundrecorder.recorderservice.controller.observer.ControllerObserver
import com.soundrecorder.recorderservice.manager.RecordStatusManager
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.wavemark.wave.view.MaxAmplitudeSource
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class WaveSampleWorkerTest {

    @Test
    fun should_success_when_timer_run() {
        val controllerObserver = object : ControllerObserver<MaxAmplitudeSource> {
            override fun getAmplitudeCallback(): MaxAmplitudeSource {
                return object : MaxAmplitudeSource {
                    override fun getMaxAmplitude(): Int {
                        return 1000
                    }

                    override fun getTime(): Long {
                        return 140
                    }

                    override fun getRecorderState(): Int {
                        return RecordStatusManager.RECORDING
                    }
                }
            }

            override fun getRecordFilePath(): String {
                return "standard Recordings/标准 1.mp3"
            }

            override fun getSuffix(): String {
                return ".mp3"
            }

            override fun getRelativePath(): String {
                return "standard Recordings"
            }

            override fun getMimeType(): String {
                return RecordConstant.MIMETYPE_MP3
            }

            override fun getSampleUri(): Uri {
                return Uri.EMPTY
            }

            override fun getSampleFileName(): String {
                return "标准录音 1.mp3"
            }

            override fun getRecordType(): Int {
                return RecordModeConstant.RECORD_TYPE_STANDARD
            }

            override fun getUUID(): String {
                return ""
            }
        }
        val recordController = RecorderController.Builder()
            .setControllerObserver(controllerObserver)
            .setWaveObserver(null)
            .setRecordInfoSaveObserver(null)
            .build()
            .initSample()

        val worker = WaveSampleWorker(recordController)
        worker.run()
        Assert.assertEquals(140, recordController.recorderAmplitudeModel.currentTimeMillis)
    }

    @Test
    fun should_success_when_release() {
        val worker = WaveSampleWorker(RecorderController.Builder().build())
        worker.release()
        Assert.assertNull(Whitebox.getInternalState(worker, "mRefRecorderController"))
    }
}