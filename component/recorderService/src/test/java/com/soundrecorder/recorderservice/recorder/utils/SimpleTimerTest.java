package com.soundrecorder.recorderservice.recorder.utils;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class SimpleTimerTest {
    private static final int STATUS_PAUSED = 1;
    private static final int STATUS_RUN = 2;

    @Test
    public void should_state_when_start() {
        SimpleTimer simpleTimer = new SimpleTimer();
        simpleTimer.start();
        int mStatus = Whitebox.getInternalState(simpleTimer, "mStatus");
        Assert.assertEquals(mStatus, STATUS_RUN);
    }

    @Test
    public void should_state_when_pause() {
        SimpleTimer simpleTimer = new SimpleTimer();
        simpleTimer.pause();
        int mStatus = Whitebox.getInternalState(simpleTimer, "mStatus");
        Assert.assertEquals(mStatus, STATUS_PAUSED);
    }

    @Test
    public void should_state_when_resume() {
        SimpleTimer simpleTimer = new SimpleTimer();
        simpleTimer.resume();
        int mStatus = Whitebox.getInternalState(simpleTimer, "mStatus");
        Assert.assertEquals(mStatus, STATUS_RUN);
    }

    @Test
    public void should_state_when_getTime() {
        SimpleTimer simpleTimer = new SimpleTimer();
        Assert.assertEquals(simpleTimer.getTime(), 0);
        simpleTimer.start();
        simpleTimer.setAppendTime(1);
        Assert.assertEquals(simpleTimer.getTime(), 1);
    }

    @Test
    public void should_state_when_reset() {
        SimpleTimer simpleTimer = new SimpleTimer();
        simpleTimer.reset();
        int mStatus = Whitebox.getInternalState(simpleTimer, "mStatus");
        Assert.assertEquals(mStatus, STATUS_PAUSED);
        Assert.assertTrue(simpleTimer.isStopped());
    }
}
