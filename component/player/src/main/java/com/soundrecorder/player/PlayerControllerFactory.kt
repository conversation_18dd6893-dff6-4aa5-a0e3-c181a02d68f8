/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PlayControllerFactory
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/7/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.player

import com.soundrecorder.player.base.IPlayerCallback
import com.soundrecorder.player.base.IPlayerController
import com.soundrecorder.player.mediaplayer.MediaPlayerController

object PlayerControllerFactory {

    const val TYPE_MEDIA_PLAYER = 0
    const val TYPE_AUDIO_TRACK = 1

    fun createPlayController(typeId: Int, playerCallback: IPlayerCallback?): IPlayerController {
        return when (typeId) {
            TYPE_MEDIA_PLAYER -> MediaPlayerController(playerCallback)
            else -> MediaPlayerController(playerCallback)
        }
    }
}