package com.soundrecorder.player.status

object PlayStatus {

    // PlayStatus for PlaybackActivity
    const val PLAYER_STATE_INIT = -1
    const val PLAYER_STATE_HALTON = 0
    const val PLAYER_STATE_PLAYING = 2
    const val PLAYER_STATE_PAUSE = 3
    const val PLAYER_STATE_FAKE_WAVE_PLAYING = 4
    const val PLAYER_STATE_FAKE_WAVE_PAUSE = 5

    // PlaySpeed status
    const val PLAYBACK_SPEED_05 = 0.5f
    const val PLAYBACK_SPEED_10 = 1.0f
    const val PLAYBACK_SPEED_15 = 1.5f
    const val PLAYBACK_SPEED_20 = 2.0f

    // IPlayerController action
    const val ACTION_INIT = "init"
    const val ACTION_PREPARE = "prepare"
    const val ACTION_PLAY = "play"
    const val ACTION_PAUSE = "pause"
    const val ACTION_CONTINUE = "continue"
    const val ACTION_COMPLETE = "complete"
    const val ACTION_REPLAY_START = "replay_start"
    const val ACTION_REPLAY_END = "replay_end"
    const val ACTION_RELEASE = "release"
    private val PREPARING = arrayOf(ACTION_PREPARE, ACTION_PLAY, ACTION_PAUSE, ACTION_REPLAY_END, ACTION_CONTINUE)

    @JvmStatic
    fun String.isMediaPlayerPreparing(): Boolean {
        return this in PREPARING
    }
}