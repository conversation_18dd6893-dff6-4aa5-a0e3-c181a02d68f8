/************************************************************
 * Copyright 2000-2009 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : SpeakerReceiver.java
 * Version Number: 1.0
 * Description   :
 * Author        : SpeakerReceiver
 * Date          : 2020-03-28
 * History       :(ID,  2020-03-28, lzs, Description)
 ************************************************************/
package com.soundrecorder.player.speaker;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothHeadset;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;

import com.soundrecorder.base.utils.DebugUtil;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class Speaker<PERSON><PERSON>eiver extends BroadcastReceiver {

    private static final String TAG = "SpeakerReceiver";

    public static final int HEADSET_DISCONNECTED = 0;
    public static final int HEADSET_CONNECTED = 1;
    private static SpeakerReceiver sInstance = null;
    private ExecutorService mSingleThreadExecutor = Executors.newSingleThreadExecutor();
    private ToDoInSpeakerReceiver mToDoInSpeakerReceiver = null;

    public static synchronized SpeakerReceiver getInstance() {
        if (sInstance == null) {
            sInstance = new SpeakerReceiver();
        }
        return sInstance;
    }

    private SpeakerReceiver() {
    }

    public void setToDoInSpeakerReceiver(ToDoInSpeakerReceiver toDoInSpeakerReceiver) {
        this.mToDoInSpeakerReceiver = toDoInSpeakerReceiver;
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        if (action == null) {
            DebugUtil.d(TAG, "SpeakerReceiver, action is null");
            return;
        }

        switch (action) {
            case BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED:
                final int headset = intent.getIntExtra(BluetoothHeadset.EXTRA_STATE, 0);
                DebugUtil.d(TAG, " onReceive headset change, status: " + headset);
                if (mToDoInSpeakerReceiver != null) {
                    Runnable taskDoWithBlueTooth = new Runnable() {
                        @Override
                        public void run() {
                            switch (headset) {
                                case BluetoothHeadset.STATE_DISCONNECTED:
                                    mToDoInSpeakerReceiver.doWithBlueTooth(HEADSET_DISCONNECTED);
                                    break;
                                case BluetoothHeadset.STATE_CONNECTED:
                                    mToDoInSpeakerReceiver.doWithBlueTooth(HEADSET_CONNECTED);
                                    break;
                            }
                        }
                    };
                    execute(taskDoWithBlueTooth);
                }
                break;
            case BluetoothAdapter.ACTION_STATE_CHANGED:
                final int blueState = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, 0);
                DebugUtil.d(TAG, " onReceive bluetooth change, status: " + blueState);

                if (mToDoInSpeakerReceiver != null) {
                    Runnable taskDoWithBlueTooth = new Runnable() {
                        @Override
                        public void run() {
                            if (blueState == BluetoothAdapter.STATE_OFF) {
                                mToDoInSpeakerReceiver.doWithBlueTooth(HEADSET_DISCONNECTED);
                            }
                        }
                    };
                    execute(taskDoWithBlueTooth);
                }
                break;
            case Intent.ACTION_HEADSET_PLUG:
                final int state = intent.getIntExtra("state", HEADSET_DISCONNECTED);
                DebugUtil.d(TAG, " onReceive wired headset, status: " + state);
                if (mToDoInSpeakerReceiver != null) {
                    Runnable taskDoWithWired = new Runnable() {
                        @Override
                        public void run() {
                            mToDoInSpeakerReceiver.doWithWired(state);
                        }
                    };
                    execute(taskDoWithWired);
                }
                break;
            case AudioManager.ACTION_AUDIO_BECOMING_NOISY:
                DebugUtil.d(TAG, "onReceive ACTION_AUDIO_BECOMING_NOISY");
                if (mToDoInSpeakerReceiver != null) {
                    Runnable taskReceiveNoisy = new Runnable() {
                        @Override
                        public void run() {
                            mToDoInSpeakerReceiver.doWithReceiveNoisy();
                        }
                    };
                    execute(taskReceiveNoisy);
                }
                break;
        }

    }

    private void execute(Runnable task) {
        try {
            if (mSingleThreadExecutor != null && task != null
                    && !mSingleThreadExecutor.isShutdown()) {
                mSingleThreadExecutor.execute(task);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "execute fail", e);
        }
    }

    public static IntentFilter getIntentFilter() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Intent.ACTION_HEADSET_PLUG);
        intentFilter.addAction(BluetoothHeadset.ACTION_CONNECTION_STATE_CHANGED);
        intentFilter.addAction(BluetoothAdapter.ACTION_STATE_CHANGED);
        intentFilter.addAction(BluetoothAdapter.ACTION_CONNECTION_STATE_CHANGED);
        intentFilter.addAction(AudioManager.ACTION_AUDIO_BECOMING_NOISY);
        return intentFilter;
    }

    public synchronized static void release() {
        if (!getInstance().mSingleThreadExecutor.isShutdown()) {
            getInstance().mSingleThreadExecutor.shutdownNow();
        }
        getInstance().setToDoInSpeakerReceiver(null);
        sInstance = null;
    }
}



