/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/11/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.player

import android.net.Uri
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.os.Message
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import com.soundrecorder.base.ext.getValueWithDefault
import com.soundrecorder.base.ext.postValueSafe
import com.soundrecorder.base.utils.CustomMutableLiveData
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.base.PlayerHelperBasicCallback
import com.soundrecorder.common.base.PlayerHelperCallback
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.player.base.IPlayerCallback
import com.soundrecorder.player.base.IPlayerController
import com.soundrecorder.player.speaker.SpeakerStateManager
import com.soundrecorder.player.status.PlayStatus
import com.soundrecorder.player.status.PlayStatus.ACTION_INIT
import com.soundrecorder.player.status.PlayStatus.PLAYER_STATE_INIT
import com.soundrecorder.player.status.PlayStatus.isMediaPlayerPreparing
import java.util.concurrent.CopyOnWriteArrayList

open class WavePlayerController(private val playViewModelCallback: PlayerHelperBasicCallback?) :
    IPlayerCallback, PlayerHelperCallback, TimerCallbackListener {

    companion object {
        private const val PLAYER_ERROR_1007 = -1007
        val SPEED_VALUE =
            floatArrayOf(
                PlayStatus.PLAYBACK_SPEED_05,
                PlayStatus.PLAYBACK_SPEED_10,
                PlayStatus.PLAYBACK_SPEED_15,
                PlayStatus.PLAYBACK_SPEED_20
            )

        const val SCROLL_FROM_DEFAULT = -1
        const val SCROLL_FROM_TIMER = 0
        const val SCROLL_FROM_DRAG = 1

        private const val MESSAGE_INIT = 0x0
        private const val MESSAGE_START_PLAY = 0X1
        private const val MESSAGE_SEEK_TO_PLAY = 0X2
        private const val MESSAGE_CONTINUE_PLAY = 0X3
        private const val MESSAGE_PAUSE_PLAY = 0X4
        private const val MESSAGE_SEEK_TIME = 0X5
        private const val MESSAGE_REPLAY = 0X6
        private const val MESSAGE_RELEASE_PLAYER = 0X7
        private const val MESSAGE_RELEASE = 0X8
        private const val MESSAGE_CHANGE_SPEED = 0X9
        private const val MESSAGE_CLICK_PLAY_BUTTON = 99
        private const val MESSAGE_SET_MILLS = 100
        private const val MESSAGE_START_TIMER = 101
        private const val MESSAGE_REST_PLAY_STATE = 102
        private const val MESSAGE_SET_URI = 103
    }

    protected open val TAG = "WavePlayerController"

    protected var playController: IPlayerController? = null

    protected var timerController: WaveTimerController? = null

    val timerTickListenerList  = CopyOnWriteArrayList<TimerTickCallback>()
    var scrollFromType = SCROLL_FROM_DEFAULT

    val currentTimeMillis = MutableLiveData<Long>(0)
    var mDuration = MutableLiveData<Long>(0)
    var playSpeedIndex = CustomMutableLiveData(1)
    var isReplay = MutableLiveData(false)
    var playerState = MutableLiveData(PLAYER_STATE_INIT)
    var playerAction = ACTION_INIT
    private var mPlayStateChangeObserver: Observer<Int>? = null

    var waveDragWhenPause = false

    //不太精确的真实player时间
    var playerDuration: Long = -1L

    //是否正在拖动进度条
    var mIsTouchSeekbar = MutableLiveData(false)

    //滑动seekBar之前记录当前的播放状态
    var playStateBeforeTouchSeekBar = PlayStatus.PLAYER_STATE_HALTON
    private var handlerThread: HandlerThread? = null
    protected var workHandler: Handler? = null
    private var mainHandler: Handler? = null

    private fun initHandlerThread() {
        if (handlerThread != null) {
            DebugUtil.i(TAG, "initHandlerThread return by has init")
            return
        }
        DebugUtil.i(TAG, "initHandlerThread")
        handlerThread = HandlerThread("PlayService")
        handlerThread?.start()
        handlerThread?.looper?.let {
            workHandler = object : Handler(it) {
                override fun handleMessage(msg: Message) {
                    when (msg.what) {
                        MESSAGE_INIT -> doInit()
                        MESSAGE_START_PLAY -> doStartPlay(delay = msg.arg1.toLong())
                        MESSAGE_SEEK_TO_PLAY -> {
                            DebugUtil.i(TAG, "doSeekToPlay  ${msg.obj as? Long}")
                            playController?.seekToPlay((msg.obj as? Long) ?: 0)
                        }

                        MESSAGE_CONTINUE_PLAY -> doContinuePlay()
                        MESSAGE_PAUSE_PLAY -> doPausePlay()
                        MESSAGE_SEEK_TIME -> doSeekTime((msg.obj as? Long) ?: 0)
                        MESSAGE_REPLAY -> {
                            DebugUtil.i(TAG, "do replay, toggleAudioStream")
                            playController?.toggleAudioStream(getSeekTime())
                        }
                        MESSAGE_RELEASE_PLAYER -> doReleasePlay()
                        MESSAGE_RELEASE -> doOnRelease((msg.obj as? Boolean) == true)
                        MESSAGE_CHANGE_SPEED -> {
                            if (playerState.value == PlayStatus.PLAYER_STATE_INIT) {
                                DebugUtil.i(TAG, "do changeSpeed return by play state is init")
                                return
                            }
                            val currentPlaySpeed = SPEED_VALUE[playSpeedIndex.value ?: 1]
                            DebugUtil.d(TAG, "changePlaybackSpeed to:$currentPlaySpeed")
                            playController?.changePlayerSpeed(currentPlaySpeed, true)
                        }
                        MESSAGE_CLICK_PLAY_BUTTON -> doPlayBtnClick()
                        MESSAGE_SET_MILLS -> doSetOCurrentTimeMillis((msg.obj as? Long) ?: 0)
                        MESSAGE_START_TIMER -> startTimerNow((msg.obj as? String) ?: "")
                        MESSAGE_REST_PLAY_STATE -> doOnResetPlayState()
                        MESSAGE_SET_URI -> doSetPlayUri((msg.obj as? Uri))
                    }
                }
            }
        }
    }

    fun addTimerTickListener(callback: TimerTickCallback?) {
        callback?.let {
            timerTickListenerList.add(it)
        }
        DebugUtil.i(TAG, "add timerTickListener size ${timerTickListenerList.size} $callback")
    }

    fun removeTimerTickListener(callback: TimerTickCallback?) {
        callback?.let {
            timerTickListenerList.remove(callback)
        }
        DebugUtil.i(TAG, "remove timerTickListener size ${timerTickListenerList.size} $callback")
    }

    private fun ensureMainHandler() {
        if (mainHandler == null) {
            mainHandler = Handler(Looper.getMainLooper())
        }
    }

    fun onStartTouchSeekBar() {
        mIsTouchSeekbar.value = true
        playStateBeforeTouchSeekBar = playerState.value ?: PlayStatus.PLAYER_STATE_HALTON
    }

    fun onStopTouchSeekBar() {
        mIsTouchSeekbar.value = false
    }

    fun onResetPlayState() {
       workHandler?.sendEmptyMessage(MESSAGE_REST_PLAY_STATE)
    }

    private fun doOnResetPlayState() {
        DebugUtil.i(TAG, "doOnResetPlayState, $playStateBeforeTouchSeekBar,isPlaying()=${isPlaying()}")
        //按下之前是播放状态，松手后也应该继续播放。
        if (playStateBeforeTouchSeekBar in listOf(
                PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING,
                PlayStatus.PLAYER_STATE_PLAYING
            )
        ) {
            //松手后是真正的player在播放，则移动波形
            if (isPlaying()) {
                startTimerNow("onResetPlayState")
            } else {
                //松手后player没有播放，并且松手后的位置不是在末尾，则在当前位置重新开始播放
                if (currentTimeMillis.getValueWithDefault() < getDuration()) {
                    // 如果mediaPlayer已经prepared，不需要重复seek播放，且若刚执行seek后立即调用startPlay，currentMill还未更新
                    if (playerAction.isMediaPlayerPreparing()) {
                        playController?.continuePlay()
                    } else {
                        doStartPlay()
                    }
                }
            }
        }
    }

    fun init() {
        initHandlerThread()
        workHandler?.sendEmptyMessage(MESSAGE_INIT)
    }

    /**
     * @param delay start play after prepare
     */
    @Synchronized
    fun startPlayImmediately(delay: Int = 0) {
        workHandler?.let {
            it.obtainMessage(MESSAGE_START_PLAY).run {
                arg1 = delay
                sendToTarget()
            }
        }
    }

    fun doStartPlay(timeMill: Long = getSeekTime(), delay: Long = 0L) {
        DebugUtil.i(TAG, "doStartPlay, timeMill= $timeMill, delay=$delay")
        playController?.startPlay(timeMill, delay)
    }

    fun seekToPlay(timeMillis: Long = currentTimeMillis.getValueWithDefault()) {
        workHandler?.let {
            it.obtainMessage(MESSAGE_SEEK_TO_PLAY).run {
                obj = timeMillis
                sendToTarget()
            }
        }
    }

    fun pausePlay() {
        workHandler?.sendEmptyMessage(MESSAGE_PAUSE_PLAY)
    }

    fun continuePlay() {
        workHandler?.sendEmptyMessage(MESSAGE_CONTINUE_PLAY)
    }

    fun seekTime(timeMills: Long) {
        workHandler?.let {
            it.obtainMessage(MESSAGE_SEEK_TIME).run {
                obj = timeMills
                sendToTarget()
            }
        }
    }

    fun setOCurrentTimeMillis(oCurrentTimeMillis: Long) {
        workHandler?.let {
            it.obtainMessage(MESSAGE_SET_MILLS).run {
                obj = oCurrentTimeMillis
                sendToTarget()
            }
        }
    }

    fun changePlayerSpeed() {
        workHandler?.sendEmptyMessage(MESSAGE_CHANGE_SPEED)
    }

    fun replay() {
        workHandler?.sendEmptyMessage(MESSAGE_REPLAY)
    }

    override fun playBtnClick() {
        if (mIsTouchSeekbar.value == true) {
            DebugUtil.d(TAG, "playBtnClick return by mIsTouchSeekbar is true")
            return
        }
        DebugUtil.i(TAG, "playBtnClick ")
        workHandler?.sendEmptyMessage(MESSAGE_CLICK_PLAY_BUTTON)
    }

    open fun releasePlay() {
        workHandler?.sendEmptyMessage(MESSAGE_RELEASE_PLAYER)
    }

    fun onRelease(releaseThread: Boolean = true) {
        workHandler?.let {
            it.obtainMessage(MESSAGE_RELEASE).run {
                this.obj = releaseThread
                sendToTarget()
            }
        }
    }

    open fun doInit() {
        DebugUtil.i(TAG, "doInit")
        if (playController == null) {
            playController = PlayerControllerFactory.createPlayController(PlayerControllerFactory.TYPE_MEDIA_PLAYER, this)
        }
        if (timerController == null) {
            timerController = WaveTimerController(this)
        }
        if (mPlayStateChangeObserver == null) {
            mPlayStateChangeObserver = Observer<Int> {
                when (it) {
                    PlayStatus.PLAYER_STATE_PLAYING -> {
                        if ((mIsTouchSeekbar.value != true) && (scrollFromType == SCROLL_FROM_DEFAULT)) {
                            startTimerNow("$TAG-mPlayStateChangeObserver: $it")
                        }
                    }
                    PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
                        if ((mIsTouchSeekbar.value != true) && (scrollFromType == SCROLL_FROM_DEFAULT)) {
                            startTimerNow("$TAG-mPlayStateChangeObserver: $it")
                        }
                    }
                    PlayStatus.PLAYER_STATE_PAUSE,
                    PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
                    ->
                        stopTimerNow()
                    PlayStatus.PLAYER_STATE_HALTON ->
                        stopTimerNow()
                }

                onPlayStateChange(it)
            }
        }
        mainHandler?.removeCallbacksAndMessages(null)
        mPlayStateChangeObserver?.let {
            ensureMainHandler()
            mainHandler?.post {
                playerState.observeForever(it)
            }
        }
    }

    fun setPlayUri(playUri: Uri?, isSynchronized: Boolean = true) {
        DebugUtil.d(TAG, "setPlayUri, isSynchronized=$isSynchronized")
        if (isSynchronized) {
            doSetPlayUri(playUri)
        } else {
            workHandler?.let {
                it.obtainMessage(MESSAGE_SET_URI).run {
                    this.obj = playUri
                    sendToTarget()
                }
            }
        }
    }

    private fun doSetPlayUri(playUri: Uri?) {
        DebugUtil.d(TAG, "doSetPlayUri")
        playController?.setPlayUri(playUri)
        playerDuration = -1
    }

    fun getPlayUri(): Uri? {
        return playController?.getPlayUri()
    }

    fun doSetOCurrentTimeMillis(oCurrentTimeMillis: Long) {
        /*DebugUtil.i(
            TAG,
            "doSetOCurrentTimeMillis, oCurrentTimeMillis=$oCurrentTimeMillis, mDuration=${mDuration.value},playerDuration=$playerDuration"
        )*/
        val duration = mDuration.value ?: 0
        when {
            (duration in 1..oCurrentTimeMillis) -> {

                if (playerState.value != PlayStatus.PLAYER_STATE_HALTON) {
                    playerState.postValueSafe(PlayStatus.PLAYER_STATE_HALTON)
                    playController?.releasePlay()
                }
                //超出总时间，做校正
                currentTimeMillis.postValueSafe(duration)
                playViewModelCallback?.onCurTimeChanged(duration)
            }
            (playerDuration > oCurrentTimeMillis) -> {
                //真实播放阶段
                currentTimeMillis.postValueSafe(oCurrentTimeMillis)
                playViewModelCallback?.onCurTimeChanged(oCurrentTimeMillis)
                //1000是为了处理mPlayDuration不准确，fake_playing下seekTime执行较慢又到了假波形阶段
                if (playerDuration - oCurrentTimeMillis > 1000) {
                    when (playerState.value) {
                        PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> {
                            if (mIsTouchSeekbar.value != true) {
                                doStartPlay()
                            }
                        }
                        PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE -> {
                            playerState.postValueSafe(PlayStatus.PLAYER_STATE_PAUSE)
                            playController?.prepareToPlay(oCurrentTimeMillis)
                        }
                    }
                }
            }
            else -> {
                currentTimeMillis.postValueSafe(oCurrentTimeMillis)
                playViewModelCallback?.onCurTimeChanged(oCurrentTimeMillis)
            }
        }
    }

    protected open fun onPlayStateChange(playState: Int) {}

    protected fun getSeekTime(): Long {
        val currentTimeMillis = currentTimeMillis.getValueWithDefault()
        DebugUtil.i(TAG, "getSeekTime currentTimeMillis=$currentTimeMillis")
        return if (currentTimeMillis in 0 until getDuration()) {
            currentTimeMillis
        } else {
            -1
        }
    }

    open fun doPausePlay() {
        DebugUtil.i(TAG, "doPausePlay")
        playController?.pausePlay()
    }

    open fun doContinuePlay() {
        DebugUtil.i(TAG, "doContinuePlay")
        if (waveDragWhenPause) {
            currentTimeMillis.getValueWithDefault().let {
                playController?.seekTime(it)
                DebugUtil.i(TAG, "continue seekTo ${currentTimeMillis.value}")
            }
            waveDragWhenPause = false
        }
        playController?.continuePlay()
    }

    fun loadDuration(): Long {
        return playController?.loadDuration() ?: 0
    }

    open fun doSeekTime(timeMills: Long, correctCurTime: Boolean = true) {
        DebugUtil.i(TAG, "doSeekTime timeMills=$timeMills, correctCurTime=$correctCurTime")
        if (playerAction.isMediaPlayerPreparing()) {
            DebugUtil.d(TAG, "seekTime  isMediaPlayerPreparing")
            playController?.seekTime(timeMills)
        }
        if (correctCurTime) {
            doSetOCurrentTimeMillis(timeMills)
        }
    }

    open fun doReleasePlay() {
        DebugUtil.i(TAG, "doReleasePlay")
        playController?.releasePlay()
        playerDuration = -1
    }

    /**
     * 跳过某一个时间段
     * @param timeMills 需要跳过的时间段，大于0将向后跳过，否则向前
     */
    fun getSeekTimeByDelta(timeMills: Int): Long {
        val currentTime = currentTimeMillis.getValueWithDefault()
        if (timeMills == 0) {
            DebugUtil.i(TAG, "seekByTime: no need to seek")
            return currentTime
        }

        var seekToTime = currentTime.plus(timeMills)
        if (seekToTime < 0) {
            seekToTime = 0
        } else if ((seekToTime > getDuration()) && (getDuration() > 0)) {
            seekToTime = getDuration()
        }

        return seekToTime
    }

    override fun getCurrentPosition(): Long {
        return if ((playController != null) && isPlaying()) {
            playController?.getCurrentPosition() ?: 0
        } else {
            currentTimeMillis.getValueWithDefault()
        }
    }

    fun isPlayerComplete(): Boolean {
        return when (playerState.value) {
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
            PlayStatus.PLAYER_STATE_HALTON -> true
            else -> false
        }
    }

    fun isWholePlaying(): Boolean {
        return when (playerState.value) {
            PlayStatus.PLAYER_STATE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING,
            -> true
            else -> false
        }
    }

    fun isPlaying(): Boolean {
        return playController?.isPlaying() ?: false
    }

    override fun setDuration(duration: Long) {
        if (duration > 0 && getDuration() != duration) {
            mDuration.postValueSafe(duration)
        }
    }

    override fun setPlayerStatus(playStatus: Int) {
        if (playerState.value != playStatus) {
            playerState.postValueSafe(playStatus)
        }
    }

    override fun onActionComplete(action: String) {
        playerAction = action
        when (action) {
            PlayStatus.ACTION_COMPLETE -> {
                //mediaPlayer播放完成，后面是假波形阶段
                DebugUtil.d(
                    TAG,
                    "onPlayComplete: currentState is ${playerState.value}, playUri = ${playController?.getPlayUri()}"
                )
                when (playerState.value) {
                    PlayStatus.PLAYER_STATE_PLAYING ->
                        playerState.postValueSafe(PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING)
                    PlayStatus.PLAYER_STATE_PAUSE ->
                        playerState.postValueSafe(PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE)
                }

                playerDuration = playController?.getCurrentPosition() ?: 0
            }
            PlayStatus.ACTION_REPLAY_START -> {
                //切换扬声器/听筒开始
                isReplay.postValueSafe(true)
            }
            PlayStatus.ACTION_REPLAY_END -> {
                //切换扬声器/听筒结束
                isReplay.postValueSafe(false)
            }
        }
    }

    override fun onPlayError(extra: Int) {
        playerState.postValueSafe(PlayStatus.PLAYER_STATE_HALTON)
    }

    override fun getAudioStreamType(): Int {
        return SpeakerStateManager.getInstance().needAudioStreamType
    }

    override fun hasPaused(): Boolean {
        return when (playerState.value) {
            PlayStatus.PLAYER_STATE_INIT,
            PlayStatus.PLAYER_STATE_PLAYING,
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING -> false
            else -> true
        }
    }

    override fun getDuration(): Long {
        return mDuration.value ?: 0
    }

    override fun getCurrentPlayerTime(): Long {
        return getCurrentPosition()
    }

    override fun getPlayerName(): MutableLiveData<String>? {
        return playViewModelCallback?.getPlayerName()
    }

    override fun getPlayerMimeType(): String? {
        return playViewModelCallback?.getPlayerMimeType()
    }

    open fun doPlayBtnClick() {
        if (mIsTouchSeekbar.value == true) {
            DebugUtil.d(TAG, "doPlayBtnClick return by mIsTouchSeekbar is true")
            return
        }
        val playState = playerState.value
        DebugUtil.d(
            TAG,
            "doPlayBtnClick, the player state is $playState, time is ${currentTimeMillis.value}" +
                    ", playUri=${playController?.getPlayUri()}"
        )
        when (playState) {
            PlayStatus.PLAYER_STATE_HALTON -> doStartPlay()
            PlayStatus.PLAYER_STATE_PLAYING -> doPausePlay()
            PlayStatus.PLAYER_STATE_PAUSE -> doContinuePlay()
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING ->
                playerState.postValueSafe(PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE)
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE ->
                playerState.postValueSafe(PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING)
            else -> doStartPlay()
        }
    }

    override fun getKeyId(): String {
        return playViewModelCallback?.getKeyId() ?: ""
    }

    open fun doOnRelease(releaseThread: Boolean) {
        DebugUtil.i(TAG, "doOnRelease, releaseThread=$releaseThread")
        playController?.onRelease()
        timerController?.stopTimer()
        playController = null
        timerController = null
        mainHandler?.removeCallbacksAndMessages(null)

        if (releaseThread) {
            workHandler?.removeCallbacksAndMessages(null)
            handlerThread?.quit()
            workHandler = null
            handlerThread = null
        }

        mPlayStateChangeObserver?.let {
            mainHandler?.post {
                playerState.removeObserver(it)
                if (releaseThread) {
                    mPlayStateChangeObserver = null
                    mainHandler = null
                }
            }
        }
    }

    fun startTimerAsync(from: String) {
        workHandler?.let {
            it.obtainMessage(MESSAGE_START_TIMER).run {
                obj = from
                sendToTarget()
            }
        }
    }

    fun startTimerNow(from: String, forceRefresh: Boolean = false) {
        DebugUtil.d(TAG, "startTimerNow, from=$from, forceRefresh=$forceRefresh,timerController=${timerController != null}")
        timerController?.startTimer(forceRefresh)
        scrollFromType = SCROLL_FROM_TIMER
    }

    fun stopTimerNow() {
        DebugUtil.i(TAG, "stopTimerNow")
        timerController?.stopTimer()
        scrollFromType = SCROLL_FROM_DEFAULT
    }

    override fun getPlayStatus(): MutableLiveData<Int> {
        return playerState
    }

    override fun getTimerPeriod(): Long {
        return 70
    }

    override fun onTimerTick(timeTickMillis: Long) {
        //用liveData postValue的形式有延迟，使得刷新不及时出现波形刷新不连贯
        timerTickListenerList.forEach {
            it.onTimerTick(timeTickMillis)
        }
    }

    /**
     * 子类调用，出现AAC seek错误
     * 相关处理
     */
    fun dealAACErrorSeekToDuration(extra: Int): Boolean {
        DebugUtil.d(TAG, "dealPlayerErrorBeforeRelease extra:$extra")
        if (isAACError(extra)) {
            seekTime(getDuration())
            return true
        }
        return false
    }

    private fun isAACError(extra: Int): Boolean {
        return extra == PLAYER_ERROR_1007 && checkAACSeekError()
    }

    /**
     * AAC文件末尾seek底层报错修改
     * 方案 出现该错误时seek到总时长
     * bugID: 4758887,4750355
     */
    private fun checkAACSeekError(): Boolean {
        var isAACSeekError = false
        val mimeType = getPlayerMimeType()
        if (mimeType == null || mimeType.isEmpty()) {
            DebugUtil.d(TAG, "checkAACSeekError mimeType is null or Empty return")
            return false
        }
        val duration = getDuration()
        if (duration <= 0) {
            DebugUtil.d(TAG, "checkAACSeekError duration is 0 return")
            return false
        }
        val dealTimeMills = getSeekTime()
        if (mimeType == RecordConstant.MIMETYPE_ACC || mimeType == RecordConstant.MIMETYPE_ACC_ADTS) {
            val diff = duration.minus(dealTimeMills)
            DebugUtil.d(TAG, "checkAACSeekError diff: $diff")
            //说明需要seek到末尾
            if (diff <= RecordConstant.AAC_FRAME_DURATION) {
                isAACSeekError = true
            }
        }
        return isAACSeekError
    }
}