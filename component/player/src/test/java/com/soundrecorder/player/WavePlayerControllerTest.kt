/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WavePlayerControllerTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/7/6
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.player

import android.net.Uri
import android.os.Build
import androidx.lifecycle.MutableLiveData
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.base.PlayerHelperBasicCallback
import com.soundrecorder.player.shadows.ShadowFeatureOption
import com.soundrecorder.player.speaker.SpeakerStateManager
import com.soundrecorder.player.status.PlayStatus
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class WavePlayerControllerTest {

    @Test
    fun should_equals_when_onStartTouchSeekBar() {
        val playController = WavePlayerController(null)
        playController.playerState.value = PlayStatus.PLAYER_STATE_PLAYING
        playController.onStartTouchSeekBar()
        playController.playerState.value = PlayStatus.PLAYER_STATE_PAUSE
        Assert.assertEquals(
            PlayStatus.PLAYER_STATE_PLAYING,
            playController.playStateBeforeTouchSeekBar
        )
    }

    @Test
    fun should_false_when_onStopTouchSeekBar() {
        val playController = WavePlayerController(null)
        playController.onStopTouchSeekBar()
        Assert.assertFalse(playController.mIsTouchSeekbar.value!!)
    }

    @Test
    fun should_true_when_onResetPlayState() {
        val playController = Mockito.spy(WavePlayerController(null))
        playController.playStateBeforeTouchSeekBar = PlayStatus.PLAYER_STATE_PLAYING
        Mockito.`when`(playController.isPlaying()).thenReturn(true, false)
        Mockito.`when`(playController.getDuration()).thenReturn(2000)
        playController.onResetPlayState()
    }

    @Test
    fun should_true_when_init() {
        val playController = WavePlayerController(null)
        Assert.assertNull(Whitebox.getInternalState(playController, "mPlayStateChangeObserver"))
        Assert.assertNull(Whitebox.getInternalState(playController, "playController"))
        Assert.assertNull(Whitebox.getInternalState(playController, "timerController"))

        playController.doInit()
        Assert.assertNotNull(Whitebox.getInternalState(playController, "mPlayStateChangeObserver"))
        Assert.assertNotNull(Whitebox.getInternalState(playController, "playController"))
        Assert.assertNotNull(Whitebox.getInternalState(playController, "timerController"))
        }

    @Test
    fun should_equals_when_setPlayUri() {
        val playController = WavePlayerController(null)
        playController.setPlayUri(Uri.parse("123"))
        Assert.assertEquals(-1, Whitebox.getInternalState(playController, "playerDuration"))
    }

    @Test
    fun should_equals_when_getPlayUri() {
        val playController = WavePlayerController(null)
        playController.doInit()
        val playUri = Uri.parse("123")
        playController.setPlayUri(playUri)
        Assert.assertEquals(playUri, playController.getPlayUri())
    }

    @Test
    fun should_true_when_setOCurrentTimeMillis() {
        val playController = Mockito.spy(WavePlayerController(null))
        Mockito.doNothing().`when`(playController).startPlayImmediately()
        playController.setDuration(2000)
        playController.doSetOCurrentTimeMillis(3000)
        Assert.assertEquals(2000L, playController.currentTimeMillis.value)

        playController.playerDuration = 1998
        playController.playerState.value = PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE
        playController.doSetOCurrentTimeMillis(900)
        playController.playerState.value = PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING
        playController.doSetOCurrentTimeMillis(900)
        Assert.assertEquals(900L, playController.currentTimeMillis.value)

        playController.playerDuration = -1
        playController.doSetOCurrentTimeMillis(900)
        Assert.assertEquals(900L, playController.currentTimeMillis.value)
    }

    @Test
    @Throws(Exception::class)
    fun should_true_when_getSeekTime() {
        val playController = WavePlayerController(null)
        playController.currentTimeMillis.value = 1000
        playController.setDuration(2000)
        val seekTime = Whitebox.invokeMethod<Long>(playController, "getSeekTime")
        Assert.assertEquals(1000, seekTime)
    }

    @Test
    @Throws(Exception::class)
    fun should_false_when_continuePlay() {
        val playController = WavePlayerController(null)
        playController.waveDragWhenPause = true
        playController.doContinuePlay()
        Assert.assertFalse(playController.waveDragWhenPause)
    }

    @Test
    @Throws(Exception::class)
    fun should_equals_when_seekTime() {
        val playController = WavePlayerController(null)
        playController.doSeekTime(1000)
        Assert.assertEquals(1000L, playController.currentTimeMillis.value)

        playController.doSeekTime(2000, false)
        Assert.assertEquals(1000L, playController.currentTimeMillis.value)
    }

    @Test
    @Throws(Exception::class)
    fun should_equals_when_releasePlay() {
        val playController = WavePlayerController(null)
        playController.init()
        playController.releasePlay()
        Assert.assertEquals(-1, playController.playerDuration)
    }

    @Test
    fun should_equals_when_getSeekTimeByDelta() {
        val playerController = WavePlayerController(null)
        playerController.currentTimeMillis.value = 1000
        var seekTime = playerController.getSeekTimeByDelta(0)
        Assert.assertEquals(1000L, seekTime)

        seekTime = playerController.getSeekTimeByDelta(-2000)
        Assert.assertEquals(0L, seekTime)

        playerController.mDuration.value = 3000
        seekTime = playerController.getSeekTimeByDelta(2100)
        Assert.assertEquals(3000L, seekTime)

        seekTime = playerController.getSeekTimeByDelta(900)
        Assert.assertEquals(1900L, seekTime)
    }

    @Test
    fun should_getState_when_changePlayerSpeed() {
        val playerController = WavePlayerController(null)
        playerController.playSpeedIndex.value = 0
        playerController.playerState.value = PlayStatus.PLAYER_STATE_INIT
        playerController.changePlayerSpeed()
        Assert.assertEquals(0, playerController.playSpeedIndex.value)

        playerController.playSpeedIndex.value = 1
        playerController.playerState.value = PlayStatus.PLAYER_STATE_PLAYING
        playerController.changePlayerSpeed()
        Assert.assertEquals(1, playerController.playSpeedIndex.value)
    }

    @Test
    fun should_getState_when_getCurrentPosition() {
        val playerController = WavePlayerController(null)
        playerController.currentTimeMillis.value = 1000
        Assert.assertEquals(1000, playerController.getCurrentPosition())
    }

    @Test
    fun should_getState_when_isPlayerComplete() {
        val playerController = WavePlayerController(null)
        playerController.playerState.value = PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING
        Assert.assertTrue(playerController.isPlayerComplete())

        playerController.playerState.value = PlayStatus.PLAYER_STATE_PLAYING
        Assert.assertFalse(playerController.isPlayerComplete())
    }

    @Test
    fun should_getState_when_isWholePlaying() {
        val playerController = WavePlayerController(null)
        playerController.playerState.value = PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING
        Assert.assertTrue(playerController.isWholePlaying())

        playerController.playerState.value = PlayStatus.PLAYER_STATE_PAUSE
        Assert.assertFalse(playerController.isWholePlaying())
    }

    @Test
    fun should_getState_when_isPlaying() {
        val playerController = WavePlayerController(null)
        Assert.assertFalse(playerController.isPlaying())
    }

    @Test
    fun should_getState_when_setDuration() {
        val playerController = WavePlayerController(null)
        playerController.setDuration(1000)
        Assert.assertEquals(1000L, playerController.mDuration.value)
    }

    @Test
    fun should_getState_when_setPlayerStatus() {
        val playerController = WavePlayerController(null)
        playerController.setPlayerStatus(PlayStatus.PLAYER_STATE_PAUSE)
        Assert.assertEquals(PlayStatus.PLAYER_STATE_PAUSE, playerController.playerState.value)
    }

    @Test
    fun should_equals_when_onActionComplete() {
        val playerController = WavePlayerController(null)
        playerController.playerState.value = PlayStatus.PLAYER_STATE_PLAYING
        playerController.onActionComplete(PlayStatus.ACTION_COMPLETE)
        Assert.assertEquals(
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING,
            playerController.playerState.value
        )

        playerController.playerState.value = PlayStatus.PLAYER_STATE_PAUSE
        playerController.onActionComplete(PlayStatus.ACTION_COMPLETE)
        Assert.assertEquals(
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
            playerController.playerState.value
        )

        playerController.onActionComplete(PlayStatus.ACTION_REPLAY_START)
        Assert.assertTrue(playerController.isReplay.value!!)

        playerController.onActionComplete(PlayStatus.ACTION_REPLAY_END)
        Assert.assertFalse(playerController.isReplay.value!!)
    }

    @Test
    fun should_equals_when_onPlayError() {
        val playerController = WavePlayerController(null)
        playerController.onPlayError(-1)
        Assert.assertEquals(0L, playerController.currentTimeMillis.value)
    }

    @Test
    fun should_equals_when_getAudioStreamType() {
        val playerController = WavePlayerController(null)
        val needAudioStreamType = SpeakerStateManager.getInstance().needAudioStreamType
        Assert.assertEquals(needAudioStreamType, playerController.getAudioStreamType())
    }

    @Test
    fun should_equals_when_hasPaused() {
        val playerController = WavePlayerController(null)
        playerController.playerState.value = PlayStatus.PLAYER_STATE_PLAYING
        Assert.assertFalse(playerController.hasPaused())

        playerController.playerState.value = PlayStatus.PLAYER_STATE_HALTON
        Assert.assertTrue(playerController.hasPaused()
        )
    }

    @Test
    fun should_equals_when_getDuration() {
        val playerController = WavePlayerController(null)
        playerController.setDuration(1000)
        Assert.assertEquals(1000L, playerController.getDuration())
    }

    @Test
    fun should_equals_when_getPlayerName() {
        val playerName = MutableLiveData("hahaha")
        val playerController = WavePlayerController(object : PlayerHelperBasicCallback {
            override fun getPlayerName(): MutableLiveData<String> {
                return playerName
            }
        })
        Assert.assertEquals(playerName, playerController.getPlayerName())
    }

    @Ignore
    @Test
    fun should_equals_when_playBtnClick() {
        val playController = Mockito.spy(WavePlayerController(null))

        Mockito.`when`(playController.startPlayImmediately())
            .thenAnswer { playController.setPlayerStatus(PlayStatus.PLAYER_STATE_PLAYING) }
        Mockito.`when`(playController.doPausePlay())
            .thenAnswer { playController.setPlayerStatus(PlayStatus.PLAYER_STATE_PAUSE) }
        Mockito.`when`(playController.doContinuePlay())
            .thenAnswer { playController.setPlayerStatus(PlayStatus.PLAYER_STATE_PLAYING) }
        playController.doInit()
        playController.setPlayUri(Uri.parse("123"))

        playController.playerState.value = PlayStatus.PLAYER_STATE_HALTON
        playController.doPlayBtnClick()
        Assert.assertEquals(PlayStatus.PLAYER_STATE_PLAYING, playController.playerState.value)

        playController.playerState.value = PlayStatus.PLAYER_STATE_PLAYING
        playController.doPlayBtnClick()
        Assert.assertEquals(PlayStatus.PLAYER_STATE_PAUSE, playController.playerState.value)

        playController.playerState.value = PlayStatus.PLAYER_STATE_PAUSE
        playController.doPlayBtnClick()
        Assert.assertEquals(PlayStatus.PLAYER_STATE_PLAYING, playController.playerState.value)

        playController.playerState.value = PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING
        playController.doPlayBtnClick()
        Assert.assertEquals(
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE,
            playController.playerState.value
        )

        playController.playerState.value = PlayStatus.PLAYER_STATE_FAKE_WAVE_PAUSE
        playController.doPlayBtnClick()
        Assert.assertEquals(
            PlayStatus.PLAYER_STATE_FAKE_WAVE_PLAYING,
            playController.playerState.value
        )

        playController.playerState.value = PlayStatus.PLAYER_STATE_INIT
        playController.doPlayBtnClick()
    }

    @Test
    fun should_equals_when_getKeyId() {
        val keyId = "keyId"
        val playerController = WavePlayerController(object : PlayerHelperBasicCallback {
            override fun getPlayerName(): MutableLiveData<String> {
                return MutableLiveData()
            }

            override fun getKeyId(): String {
                return keyId
            }
        })
        Assert.assertEquals(keyId, playerController.getKeyId())
    }

    @Test
    fun should_getState_when_onRelease() {
        val playerController = WavePlayerController(null)
        playerController.init()
        playerController.onRelease()
        Assert.assertFalse(playerController.playerState.hasActiveObservers())
    }

    @Test
    fun should_getState_when_getPlayStatus() {
        val playerController = WavePlayerController(null)
        Assert.assertEquals(playerController.playerState, playerController.getPlayStatus())
    }

    @Test
    fun should_getState_when_onTimerTick() {
        var newTime: Long = -1
        val playerController = WavePlayerController(null)
        val timerTickCallback = object : TimerTickCallback {
            override fun onTimerTick(timeTickMillis: Long) {
                newTime = timeTickMillis
            }
        }
        playerController.timerTickListenerList.add(timerTickCallback)
        playerController.onTimerTick(1000)
        Assert.assertEquals(1000L, newTime)
    }
}