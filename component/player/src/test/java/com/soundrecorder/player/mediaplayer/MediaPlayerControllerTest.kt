/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  MediaPlayerControllerTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/7/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.player.mediaplayer

import android.media.AudioManager
import android.media.MediaPlayer
import android.net.Uri
import android.os.Build
import android.os.Handler
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.player.base.IPlayerCallback
import com.soundrecorder.player.shadows.ShadowFeatureOption
import com.soundrecorder.player.status.PlayStatus
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyLong
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class MediaPlayerControllerTest {

    @Test
    fun should_notNull_when_init() {
        val playerController = MediaPlayerController(null)
        Assert.assertNull(Whitebox.getInternalState(playerController, "mAudioManager"))
        Assert.assertNull(Whitebox.getInternalState(playerController, "mPlayer"))

        Whitebox.invokeMethod<Unit>(playerController, "initAudioManager")
        Assert.assertNotNull(Whitebox.getInternalState(playerController, "mAudioManager"))

        Whitebox.invokeMethod<Unit>(playerController, "initMediaPlayer")
        Assert.assertNotNull(Whitebox.getInternalState(playerController, "mPlayer"))
    }

    @Test
    fun should_equals_when_setPlayUri() {
        val playerController = MediaPlayerController(null)
        val playUri = Uri.EMPTY
        playerController.setPlayUri(playUri)
        Assert.assertEquals(playUri, playerController.getPlayUri())
    }

    @Test
    fun should_equals_when_getAudioStreamType() {
        val audioStreamType = AudioManager.STREAM_MUSIC
        val playerController = MediaPlayerController(object : IPlayerCallback {
            override fun onActionComplete(action: String) {
                //do nothing
            }

            override fun onPlayError(extra: Int) {
                //do nothing
            }

            override fun setDuration(duration: Long) {
                //do nothing
            }

            override fun setPlayerStatus(playStatus: Int) {
                //do nothing
            }

            override fun getAudioStreamType(): Int {
                return audioStreamType
            }
        })
        playerController.getAudioStreamType()
        Assert.assertEquals(audioStreamType, Whitebox.getInternalState(playerController, "mCurrentStreamType"))
    }

    @Test
    fun should_null_when_onCompletion() {
        val playerController = MediaPlayerController(null)
        playerController.onCompletion(null)
        Assert.assertNull(Whitebox.getInternalState(playerController, "mPlayer"))
    }

    @Test
    fun should_equals_when_onAudioFocusChange() {
        var playState = -1
        val playerCallback = object : IPlayerCallback {
            override fun onActionComplete(action: String) {
                //do nothing
            }

            override fun onPlayError(extra: Int) {
                //do nothing
            }

            override fun setDuration(duration: Long) {
                //do nothing
            }

            override fun setPlayerStatus(playStatus: Int) {
                playState = playStatus
            }

            override fun getAudioStreamType(): Int {
                return 0
            }
        }
        val playerController = Mockito.spy(
            MediaPlayerController(
                playerCallback
            )
        )
        Mockito.`when`(playerController.pausePlay()).thenAnswer { playerCallback.setPlayerStatus(
            PlayStatus.PLAYER_STATE_PAUSE) }
        playerController.onAudioFocusChange(AudioManager.AUDIOFOCUS_LOSS)
        Assert.assertEquals(PlayStatus.PLAYER_STATE_PAUSE, playState)
    }

    @Test
    fun should_null_when_onError() {
        val playerController = MediaPlayerController(null)
        playerController.onError(null, 0, 0)
        //Assert.assertNull(Whitebox.getInternalState(playerController, "mPlayer"))
    }
    @Test
    fun should_returnFalse_when_pausePlay() {
        var playState = -1
        val playerController = Mockito.spy(MediaPlayerController(object :
            IPlayerCallback {
            override fun onActionComplete(action: String) {
                //do nothing
            }

            override fun onPlayError(extra: Int) {
                //do nothing
            }

            override fun setDuration(duration: Long) {
                //do nothing
            }

            override fun setPlayerStatus(playStatus: Int) {
                playState = playStatus
            }

            override fun getAudioStreamType(): Int {
                return 0
            }
        }))
        val player = Mockito.spy(MediaPlayer())
        Whitebox.setInternalState(playerController, "mPlayer", player)
        Mockito.`when`(player.pause()).thenAnswer {  }
        Mockito.`when`(playerController.isPlaying()).thenReturn(true)
        playerController.pausePlay()
        Assert.assertEquals(PlayStatus.PLAYER_STATE_PAUSE, playState)
    }

    @Test
    fun should_returnFalse_when_continuePlay() {
        var playState = -1
        val playerController = MediaPlayerController(object :
            IPlayerCallback {
            override fun onActionComplete(action: String) {
                //do nothing
            }

            override fun onPlayError(extra: Int) {
                //do nothing
            }

            override fun setDuration(duration: Long) {
                //do nothing
            }

            override fun setPlayerStatus(playStatus: Int) {
                playState = playStatus
            }

            override fun getAudioStreamType(): Int {
                return AudioManager.STREAM_MUSIC
            }
        })
        val player = Mockito.spy(MediaPlayer())
        Whitebox.setInternalState(playerController, "mPlayer", player)
        Mockito.doNothing().`when`(player).start()
        playerController.continuePlay()
        Assert.assertEquals(PlayStatus.PLAYER_STATE_PLAYING, playState)
    }

    @Test
    fun should_returnFalse_when_startToPlay() {
        var playState = -1
        val playerController = Mockito.spy(MediaPlayerController(object :
            IPlayerCallback {
            override fun onActionComplete(action: String) {
                //do nothing
            }

            override fun onPlayError(extra: Int) {
                //do nothing
            }

            override fun setDuration(duration: Long) {
                //do nothing
            }

            override fun setPlayerStatus(playStatus: Int) {
                playState = playStatus
            }

            override fun getAudioStreamType(): Int {
                return AudioManager.STREAM_MUSIC
            }
        }))
        val player = Mockito.spy(MediaPlayer())
        Whitebox.setInternalState(playerController, "mPlayer", player)
        Mockito.doNothing().`when`(player).start()
        playerController.startToPlay()
        Assert.assertEquals(PlayStatus.PLAYER_STATE_PLAYING, playState)
    }

    @Test
    fun should_equals_when_seekToPlay() {
        val mediaPlayerController = Mockito.spy(MediaPlayerController(null))
        Mockito.`when`(mediaPlayerController.getCurrentPosition()).thenReturn(1000)
        mediaPlayerController.seekToPlay(1000)
        Assert.assertEquals(1000, mediaPlayerController.getCurrentPosition())

        mediaPlayerController.releasePlay()
        mediaPlayerController.seekToPlay(1000)
        Assert.assertEquals(1000, mediaPlayerController.getCurrentPosition())
    }

    @Test
    fun should_null_when_releasePlay() {
        val mediaPlayerController = MediaPlayerController(null)
        Whitebox.invokeMethod<Unit>(mediaPlayerController, "initMediaPlayer")
        var player = Whitebox.getInternalState<MediaPlayer>(mediaPlayerController, "mPlayer")
        Assert.assertNotNull(player)
        mediaPlayerController.releasePlay()
        player = Whitebox.getInternalState(mediaPlayerController, "mPlayer")
        Assert.assertNull(player)
    }

    @Test
    fun should_null_when_release() {
        val mediaPlayerController = MediaPlayerController(null)
        Whitebox.invokeMethod<Unit>(mediaPlayerController, "initMediaPlayer")
        var player = Whitebox.getInternalState<MediaPlayer>(mediaPlayerController, "mPlayer")
        Assert.assertNotNull(player)
        Whitebox.invokeMethod<Any>(mediaPlayerController, "release")
        player = Whitebox.getInternalState(mediaPlayerController, "mPlayer")
        Assert.assertNull(player)
    }

    @Test
    fun should_null_when_onRelease() {
        val mediaPlayerController = Mockito.spy(MediaPlayerController(null))
        Mockito.`when`(mediaPlayerController.prepareToPlay(anyLong())).thenReturn(true)
        Mockito.`when`(mediaPlayerController.startToPlay()).then {  }
        mediaPlayerController.startPlay(0, 200)
        mediaPlayerController.onRelease()
    }
}