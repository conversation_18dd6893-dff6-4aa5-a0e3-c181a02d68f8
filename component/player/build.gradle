apply from: "../../common_build.gradle"

android {
    sourceSets {
        main {
            res.srcDirs += ['res']
            res.srcDirs += ['../../res-strings']
        }
    }
    namespace "com.soundrecorder.player"
}
dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation libs.org.kotlin.stdlib
    implementation libs.androidx.appcompat
    implementation libs.androidx.core.ktx

    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')
}