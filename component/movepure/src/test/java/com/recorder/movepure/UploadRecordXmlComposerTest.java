package com.recorder.movepure;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;

import org.robolectric.annotation.Config;

import com.recorder.movepure.shadows.ShadowBaseUtils;
import com.recorder.movepure.shadows.ShadowFeatureOption;
import com.recorder.movepure.shadows.ShadowOS12FeatureUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowBaseUtils.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class UploadRecordXmlComposerTest {
    @Test
    public void should_returnFalse_when_startCompose_and_endCompose() {
        UploadRecordXmlComposer uploadRecordXmlComposer = new UploadRecordXmlComposer();
        Assert.assertTrue(uploadRecordXmlComposer.startCompose());
        Assert.assertTrue(uploadRecordXmlComposer.endCompose());
    }

    @Test
    public void should_notNull_when_getXmlInfo() {
        UploadRecordXmlComposer uploadRecordXmlComposer = new UploadRecordXmlComposer();
        Assert.assertNull(uploadRecordXmlComposer.getXmlInfo());
    }
}
