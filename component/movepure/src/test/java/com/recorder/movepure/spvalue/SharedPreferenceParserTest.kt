/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SharedPreferenceParserTest
 * Description:
 * Version: 1.0
 * Date: 2023/9/5
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/9/5 1.0 create
 */

package com.recorder.movepure.spvalue

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.recorder.movepure.shadows.ShadowBaseUtils
import com.recorder.movepure.shadows.ShadowFeatureOption
import com.recorder.movepure.shadows.ShadowOS12FeatureUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowBaseUtils::class, ShadowOS12FeatureUtil::class, ShadowFeatureOption::class]
)
class SharedPreferenceParserTest {

    private var parser: SharedPreferenceParser? = null

    @Before
    fun setUp() {
        parser = SharedPreferenceParser()
    }

    @Test
    fun should_return_string_when_getXmlName() {
        val xmlName = parser?.getXmlName()

        Assert.assertNotNull(xmlName)
        Assert.assertEquals("sharedPreference.xml", xmlName)
    }

    @Test
    fun should_notNull_when_createData() {
        val data = parser?.createData()
        Assert.assertNotNull(data)
    }

    @Test
    fun should_equals_when_setDataAttr() {
        parser?.let {
            it.setDataAttr(Unit, "key", "10")
            val value = StorageManager.getIntPref(BaseApplication.getAppContext(), "key", 0)
            Assert.assertEquals(10, value)
        }
    }
}