/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * <p>
 * FileName       : ShadowRecorderUtilBelowQ.java
 * Version Number : 1.0
 * Description    :
 * Author         : LI Kun
 * Date           : 2019/10/10
 * History        :( ID,     Date,         Author, Description)
 * v1.0, 2019/10/10, LI Kun, create
 ************************************************************/

package com.recorder.movepure.shadows;

import android.content.Context;

import com.soundrecorder.base.utils.BaseUtil;

import org.robolectric.annotation.Implementation;
import org.robolectric.annotation.Implements;


@Implements(BaseUtil.class)
public class ShadowBaseUtilBelowQ {

    @Implementation
    public static boolean isAndroidQOrLater() {
        return false;
    }

    @Implementation
    public static void enableBackgroundService(Context context) {

    }
}
