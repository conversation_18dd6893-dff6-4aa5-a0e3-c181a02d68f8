package com.recorder.movepure;

import static com.recorder.movepure.RecorderBackupPlugin.BACKUP_RESTORE;
import static com.recorder.movepure.RecorderBackupPlugin.PHONE_CLONE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.spy;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.oplus.backup.sdk.common.host.BREngineConfig;
import com.oplus.backup.sdk.common.utils.ApplicationFileInfo;
import com.oplus.backup.sdk.component.BRPluginHandler;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.databean.ConvertRecord;
import com.soundrecorder.common.databean.KeyWord;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.databean.UploadRecord;
import com.soundrecorder.common.db.KeyWordDbUtils;
import com.soundrecorder.common.db.PictureMarkDbUtils;
import com.recorder.movepure.shadows.ShadowBaseUtils;
import com.recorder.movepure.shadows.ShadowFeatureOption;
import com.recorder.movepure.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.common.utils.ConvertDbUtil;
import com.soundrecorder.common.db.UploadDbUtil;

import java.io.File;
import java.io.FileDescriptor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowBaseUtils.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class RecorderBackupPluginTest {
    private Context mContext;
    private MockedStatic<BaseApplication> mMockBaseApplication;
    @Before
    public void setUp() {
        mContext = Mockito.spy(ApplicationProvider.getApplicationContext());
        mMockBaseApplication = mockStatic(BaseApplication.class);
        mMockBaseApplication.when(() -> BaseApplication.getAppContext()).thenReturn(mContext);
    }

    @Test
    public void should_returnFalse_when_genUploadXml() {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        boolean mIsCancel = Whitebox.getInternalState(recorderBackupPlugin, "mIsCancel");
        BRPluginHandler brPluginHandler = Mockito.mock(BRPluginHandler.class);
        BREngineConfig config = new BREngineConfig();
        recorderBackupPlugin.onCreate(mContext, brPluginHandler, config);
        Assert.assertFalse(mIsCancel);
        recorderBackupPlugin.onBackup(null);
        Assert.assertFalse(mIsCancel);
        recorderBackupPlugin.onCancel(null);
        Assert.assertFalse(mIsCancel);
        recorderBackupPlugin.onContinue(null);
        Assert.assertFalse(mIsCancel);
        recorderBackupPlugin.onDestroy(null);
        Assert.assertFalse(mIsCancel);

        Whitebox.setInternalState(recorderBackupPlugin, "mRecorderXMl", mock(RecorderXmlComposer.class));
        Whitebox.setInternalState(recorderBackupPlugin, "mConvertRecordXMl", mock(ConvertRecordXmlComposer.class));
        Whitebox.setInternalState(recorderBackupPlugin, "mUploadRecordXmlComposer", mock(UploadRecordXmlComposer.class));
        Whitebox.setInternalState(recorderBackupPlugin, "mPictureMarkXmlComposer", mock(PictureMarkXmlComposer.class));
        Whitebox.setInternalState(recorderBackupPlugin, "mKeyWordComposer", mock(KeyWordComposer.class));
        Whitebox.setInternalState(recorderBackupPlugin, "mRecorderCusor", mock(Cursor.class));
        Whitebox.setInternalState(recorderBackupPlugin, "mIsCancel", true);
        Whitebox.setInternalState(recorderBackupPlugin, "mOutInfoList", new ArrayList<ApplicationFileInfo>());
        Assert.assertNotNull(recorderBackupPlugin.onDestroy(null));

        recorderBackupPlugin.onPrepare(null);
        Assert.assertFalse(mIsCancel);
//        recorderBackupPlugin.onPreview(null);
//        Assert.assertFalse(mIsCancel);
    }

    @Test
    public void verify_value_when_onPreview() {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        BRPluginHandler brPluginHandler = Mockito.mock(BRPluginHandler.class);
        BREngineConfig config = new BREngineConfig();
        config.setSource(PHONE_CLONE);
        recorderBackupPlugin.onCreate(mContext, brPluginHandler, config);
        Cursor mockCursor = Mockito.mock(Cursor.class);
        Whitebox.setInternalState(recorderBackupPlugin, "mRecorderCusor", mockCursor);
        Bundle bundle = recorderBackupPlugin.onPreview(new Bundle());
        Assert.assertNotNull(bundle);

        config.setSource(BACKUP_RESTORE);
        bundle = recorderBackupPlugin.onPreview(new Bundle());
        Assert.assertNotNull(bundle);

        config.setSource("123");
        bundle = recorderBackupPlugin.onPreview(new Bundle());
        Assert.assertNotNull(bundle);
    }

    @Test
    public void verify_value_when_getExtraFileSize() throws Exception {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        long result = Whitebox.invokeMethod(recorderBackupPlugin, "getExtraFileSize", (File[])null, 0);
        Assert.assertEquals(0, result);

        File[] files = new File[1];
        files[0] = spy(new File("recorder/123" + Constants.AMP_FILE_SUFFIX));
        Mockito.when(files[0].length()).thenReturn(10L);
        Mockito.when(files[0].isFile()).thenReturn(true);
        result = Whitebox.invokeMethod(recorderBackupPlugin, "getExtraFileSize", files, 0);
        Assert.assertEquals(10, result);

        files = new File[1];
        files[0] = mock(File.class);
        Mockito.when(files[0].length()).thenReturn(10L);
        result = Whitebox.invokeMethod(recorderBackupPlugin, "getExtraFileSize", files, 1);
        Assert.assertEquals(0, result);
    }

    @Test
    public void verify_value_when_getBackupDataSize() throws Exception {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        Whitebox.setInternalState(recorderBackupPlugin, "mContext", mContext);
        long size = Whitebox.invokeMethod(recorderBackupPlugin, "getBackupDataSize");
        Assert.assertNotEquals(0, size);
    }

    @Test
    public void verify_value_when_estimateRecordBackupSize() throws Exception {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        long size = Whitebox.invokeMethod(recorderBackupPlugin, "estimateRecordBackupSize");
        Assert.assertEquals(BackupFileUtil.DEFAULT_SIZE, size);

        Cursor mockCursor = Mockito.mock(Cursor.class);
        Whitebox.setInternalState(recorderBackupPlugin, "mRecorderCusor", mockCursor);
        size = Whitebox.invokeMethod(recorderBackupPlugin, "estimateRecordBackupSize");
        Assert.assertEquals(BackupFileUtil.DEFAULT_SIZE, size);
    }

    @Test
    public void verify_value_when_getMaxCount() throws Exception {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        Whitebox.setInternalState(recorderBackupPlugin, "mRecorderCusor", mock(Cursor.class));
        int maxCount = Whitebox.invokeMethod(recorderBackupPlugin, "getMaxCount");
        Assert.assertEquals(0, maxCount);

        Whitebox.setInternalState(recorderBackupPlugin, "mRecorderCusor", (Cursor)null);
        Context context = mock(Context.class);
        Whitebox.setInternalState(recorderBackupPlugin, "mContext", context);
        Mockito.when(context.getContentResolver()).thenReturn(mock(ContentResolver.class));
        maxCount = Whitebox.invokeMethod(recorderBackupPlugin, "getMaxCount");
        Assert.assertEquals(0, maxCount);
    }

    @Test
    public void verify_value_when_parseCursor() throws Exception {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        Record value = Whitebox.invokeMethod(recorderBackupPlugin, "parseCursor", (Cursor) null);
        Assert.assertNull(value);

        value = Whitebox.invokeMethod(recorderBackupPlugin, "parseCursor", mock(Cursor.class));
        Assert.assertNotNull(value);
    }

    @Test
    public void verify_value_when_writeToFile() throws Exception {
        RecorderBackupPlugin recorderBackupPlugin = mock(RecorderBackupPlugin.class);
        Mockito.when(recorderBackupPlugin.getFileDescriptor(any())).thenReturn((FileDescriptor) null, mock(FileDescriptor.class));

        boolean value = Whitebox.invokeMethod(recorderBackupPlugin, "writeToFile", "123.mp3", new byte[200]);
        Assert.assertFalse(value);

        value = Whitebox.invokeMethod(recorderBackupPlugin, "writeToFile", "123.mp3", new byte[200]);
        Assert.assertFalse(value);
    }

    @Test
    public void verify_value_when_getFolderSize() throws Exception {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        long size = Whitebox.invokeMethod(recorderBackupPlugin, "getFolderSize");
        Assert.assertEquals(0, size);

        Context context = mock(Context.class);
        Whitebox.setInternalState(recorderBackupPlugin, "mContext", context);
        Mockito.when(context.getContentResolver()).thenReturn(mock(ContentResolver.class));
        size = Whitebox.invokeMethod(recorderBackupPlugin, "getFolderSize");
        Assert.assertEquals(0, size);
    }

    @Test
    public void verify_value_when_genConvertRecordXML() throws Exception {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        mockStatic(ConvertDbUtil.class);
        List<ConvertRecord> convertRecord = new ArrayList<>();
        convertRecord.add(new ConvertRecord(1));
        convertRecord.add(new ConvertRecord(2, 2, "record", "123.txt", "123", 1));
        convertRecord.add(new ConvertRecord(3, 3, "record", "123123.txt", "123123", 1));
        Mockito.when(ConvertDbUtil.selectAll()).thenReturn(convertRecord);
        mockStatic(FileUtils.class);
        Mockito.when(FileUtils.isFileExist((Uri) any())).thenReturn(false, false, true);
        Map<Long,String> mockedMap = new HashMap<>();
        Whitebox.setInternalState(recorderBackupPlugin, "mConvertRecordXMl", mock(ConvertRecordXmlComposer.class));
        Whitebox.setInternalState(recorderBackupPlugin, "mMediaMap", mockedMap);
        Whitebox.invokeMethod(recorderBackupPlugin, "genConvertRecordXML");
        Assert.assertNotEquals(0, mockedMap.size());
    }

    @Test
    public void verify_value_when_genUploadXml() throws Exception {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        mockStatic(UploadDbUtil.class);
        List<UploadRecord> data = new ArrayList<>();
        data.add(new UploadRecord());
        Mockito.when(UploadDbUtil.getAllUploadRecords(any())).thenReturn(data);
        Whitebox.setInternalState(recorderBackupPlugin, "mUploadRecordXmlComposer", mock(UploadRecordXmlComposer.class));
        Whitebox.invokeMethod(recorderBackupPlugin, "genUploadXml");
        Assert.assertNotNull(Whitebox.getInternalState(recorderBackupPlugin, "mUploadRecordXmlComposer"));
    }

    @Test
    public void verify_value_when_genPictureMarkXml() throws Exception {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        List<MarkDataBean> data = new ArrayList<>();
        data.add(new MarkDataBean(0L, 1));
        Mockito.when(BaseApplication.getAppContext().getContentResolver()).thenReturn(mock(ContentResolver.class));
        MockedStatic<PictureMarkDbUtils> mockedStatic = mockStatic(PictureMarkDbUtils.class);
        Mockito.when(PictureMarkDbUtils.queryAllPictureMarks()).thenReturn(data);
        mockedStatic.close();
        Whitebox.setInternalState(recorderBackupPlugin, "mPictureMarkXmlComposer", mock(PictureMarkXmlComposer.class));
        Whitebox.invokeMethod(recorderBackupPlugin, "genPictureMarkXml");
        Assert.assertNotNull(Whitebox.getInternalState(recorderBackupPlugin, "mPictureMarkXmlComposer"));
    }

    @Test
    public void verify_value_when_genKeyWordXml() throws Exception {
        RecorderBackupPlugin recorderBackupPlugin = new RecorderBackupPlugin();
        List<KeyWord> keyWords = new ArrayList<>();
        keyWords.add(new KeyWord("123", 0.5f, 1, 1, "ha"));
        keyWords.add(new KeyWord("456", 0.5f, 2, 2, "haha"));
        Mockito.when(BaseApplication.getAppContext().getContentResolver()).thenReturn(mock(ContentResolver.class));
        MockedStatic<KeyWordDbUtils> mockedStatic = mockStatic(KeyWordDbUtils.class);
        Mockito.when(KeyWordDbUtils.queryAllKeyWords()).thenReturn(keyWords);
        mockedStatic.close();
        Map<Long, String> mediaMap = new HashMap<>();
        mediaMap.put(1L, "123");
        Whitebox.setInternalState(recorderBackupPlugin, "mMediaMap", mediaMap);
        Whitebox.setInternalState(recorderBackupPlugin, "mKeyWordComposer", mock(KeyWordComposer.class));
        Whitebox.invokeMethod(recorderBackupPlugin, "genKeyWordXml");
        Assert.assertNotNull(Whitebox.getInternalState(recorderBackupPlugin, "mKeyWordComposer"));
    }

    @After
    public void tearDown() {
        mContext = null;
        if (mMockBaseApplication != null) {
            mMockBaseApplication.close();
            mMockBaseApplication = null;
        }
    }
}
