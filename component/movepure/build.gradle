apply from: "../../common_build.gradle"

android {
    namespace "com.recorder.movepure"
}

dependencies {
    // Koin for Android
    implementation(libs.koin)
    /*去品牌化后搬家SDK*/
    implementation libs.oplus.backup.pure
    implementation libs.gson

    implementation project(':common:libbase')
    implementation project(':common:modulerouter')
    implementation project(':common:libcommon')
    implementation project(':common:libimageload')
}