/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BaseNotificationTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.base

import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.widget.RemoteViews
import androidx.lifecycle.MutableLiveData
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.modulerouter.notification.NotificationModel
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.notification.CommonNotificationModel
import com.soundrecorder.notification.R
import com.soundrecorder.notification.base.BaseNotification.Companion.REFRESH_STATE_ENABLED
import com.soundrecorder.notification.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class BaseNotificationTest {

    @Test
    fun should_notNull_when_init() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews) {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return -1
            }

            override fun getFoldLayoutId(): Int? {
                return null
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }

        Assert.assertNotNull(baseNotification.notificationMgr)
    }

    @Test
    fun should_contains_when_getNormalEventFilter() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews) {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return -1
            }

            override fun getFoldLayoutId(): Int? {
                return null
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        val intentFilter: IntentFilter =
            Whitebox.invokeMethod(baseNotification, "getNormalEventFilter")
        Assert.assertTrue(intentFilter.hasAction(Intent.ACTION_LOCALE_CHANGED))
    }

    @Test
    fun should_notNull_when_initRemoteView() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int {
                return R.layout.layout_notification_fast_play_fold
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Whitebox.invokeMethod<Void>(baseNotification, "initRemoteView")
        Assert.assertNotNull(baseNotification.remoteViews)
    }

    @Test
    fun should_notNull_when_setRemoteViewText() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int? {
                return R.layout.layout_notification_fast_play_fold
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Whitebox.invokeMethod<Void>(baseNotification, "initRemoteView")
        Whitebox.invokeMethod<Void>(baseNotification, "setRemoteViewText", R.id.title_tv, "123", "123")
        Whitebox.invokeMethod<Void>(baseNotification, "setTitleOrContentText", R.id.title_tv, "1", "1")
        Assert.assertNotNull(baseNotification.remoteViews)
    }

    @Test
    fun should_equals_when_showNotification() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int {
                return R.layout.layout_notification_fast_play_fold
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        baseNotification.notificationModel =
            CommonNotificationModel().also { it.canJumpIntent = true }
        baseNotification.showNotification(null)
        Assert.assertEquals(
            REFRESH_STATE_ENABLED,
            Whitebox.getInternalState(baseNotification, "refreshState")
        )
    }

    @Test
    fun should_notNull_when_sendNotification() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int? {
                return null
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Whitebox.invokeMethod<Void>(baseNotification, "initRemoteView")
        baseNotification.sendNotification(true)
        Assert.assertNotNull(baseNotification.notification)
    }

    @Test
    fun should_notNull_when_refreshNotification() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int? {
                return null
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Whitebox.invokeMethod<Void>(baseNotification, "refreshNotification")
        Assert.assertNotNull(baseNotification.notification)
    }

    @Test
    fun should_release_when_cancelNotification() {
        var invokeRelease = false
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                invokeRelease = true
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int? {
                return null
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        baseNotification.cancelNotification()
        Assert.assertTrue(invokeRelease)
    }

    @Test
    fun should_release_when_getNotificationId() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int? {
                return null
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Assert.assertEquals(0, baseNotification.getNotificationId())
    }

    @Test
    fun should_release_when_getGroupId() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int? {
                return null
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Assert.assertEquals(1024, baseNotification.getGroupId())
    }

    @Test
    fun should_return_true_when_isPlaying() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int? {
                return null
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        baseNotification.notificationModel = CommonNotificationModel().also {
            it.setPlayStatus(MutableLiveData(NotificationModel.RECORD_STATUS_PLAYING))
        }
        Assert.assertTrue(Whitebox.invokeMethod(baseNotification, "isPlaying"))
    }

    @Test
    fun should_return_true_when_doInMainThread() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int? {
                return null
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        var invokeRunnable = false
        val runnable = Runnable {
            invokeRunnable = true
        }
        Whitebox.invokeMethod<Void>(baseNotification, "doInMainThread", runnable)
        Assert.assertTrue(invokeRunnable)
    }

    @Test
    fun should_notNull_when_postInMainHandler() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int? {
                return null
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Whitebox.invokeMethod<Void>(baseNotification, "postInMainHandler", Runnable {})
        Assert.assertNotNull(Whitebox.getInternalState(baseNotification, "handler"))
    }

    @Test
    fun should_null_when_releaseHandler() {
        val baseNotification = object : BaseNotification(1024, 0) {
            override fun setRemoteViewData(remoteViews: RemoteViews)  {
                //do nothing
            }

            override fun observeData() {
                //do nothing
            }

            override fun onRelease() {
                //do nothing
            }

            override fun getLayoutId(): Int {
                return R.layout.layout_notification_fast_play
            }

            override fun getFoldLayoutId(): Int? {
                return null
            }

            override fun getJumpIntent(): Intent? {
                return null
            }

            override fun getChannelName(): String? {
                return null
            }

            override fun getChannelId(): String {
                return NotificationUtils.PLAYBACK_CID
            }

            override fun getOldChannelId(): String {
                return NotificationUtils.PLAYBACK_OLD_CID
            }
        }
        Whitebox.invokeMethod<Void>(baseNotification, "postInMainHandler", Runnable {})
        Whitebox.invokeMethod<Void>(baseNotification, "releaseHandler")
        Assert.assertNull(Whitebox.getInternalState(baseNotification, "handler"))
    }
}