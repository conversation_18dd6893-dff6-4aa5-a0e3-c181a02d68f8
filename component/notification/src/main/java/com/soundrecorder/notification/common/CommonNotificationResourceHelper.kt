/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CommonNotificationResouceHelper
 * Description:
 * Version: 1.0
 * Date: 2023/3/28
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/3/28 1.0 create
 */

package com.soundrecorder.notification.common

import android.content.Context
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.notification.R

object CommonNotificationResourceHelper {

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    /**
     * 快捷播放/详情播放/裁切播放通知栏播放状态按钮talkBar点击后描述
     */
    @JvmStatic
    fun getPlayButtonText(context: Context, isPlayingState: Boolean): String = if (isPlayingState) {
        context.resources.getString(R.string.talkback_pause)
    } else {
        context.resources.getString(R.string.talkback_play)
    }

    /**
     * 录制通知栏 录制状态按钮 talkBar描述
     * 录制状态应该播报 “正在录音”
     * 暂停状态应该播报 “录音暂停”
     */
    @JvmStatic
    fun getPlayButtonRecordContentDec(context: Context, isPlayingState: Boolean): String = if (isPlayingState) {
        context.resources.getString(R.string.recording_notify_talk_back)
    } else {
        context.resources.getString(R.string.record_pause_tips)
    }

    /**
     * 录制通知，在副屏通过action显示文字按钮内容
     */
    @JvmStatic
    fun getPlayButtonRecordText(context: Context, isPlayingState: Boolean): String = if (isPlayingState) {
        context.resources.getString(R.string.talkback_pause)
    } else {
        context.resources.getString(R.string.record_continue)
    }

    /**
     * 录制卡片状态描述
     * 正在录音... or 录音暂停 or 正在保存...
     */
    @JvmStatic
    fun getRecordStatusContent(
        context: Context,
        isRecording: Boolean,
        isSaving: Boolean
    ): Pair<String, String> {
        return if (isSaving) {
            Pair(
                context.getString(R.string.is_saving),
                context.getString(R.string.is_saving_talk_back)
            )
        } else if (isRecording) {
            Pair(
                context.getString(R.string.recording_notify),
                context.getString(R.string.recording_notify_talk_back)
            )
        } else {
            val str = BaseApplication.getAppContext().getString(
                if (recorderViewModelApi?.isAudioModeChangePause() == true) {
                    R.string.state_call_record_paused
                } else {
                    R.string.record_pause_tips
                }
            )
            Pair(str, str)
        }
    }

    /**
     * 录制/快捷播放/详情播放/裁切播放通知栏 录制、播放状态切换按钮图标
     */
    @JvmStatic
    fun getPlayButtonImageResId(isPlayingState: Boolean): Int = if (isPlayingState) {
        getPlayButtonPlayStateResId()
    } else {
        getPlayButtonPauseStateResId()
    }

    @JvmStatic
    private fun getPlayButtonPlayStateResId(): Int = if (BaseUtil.isAndroidSOrLater) {
        R.drawable.notification_pause_btn
    } else {
        R.drawable.notification_pause_btn_below_s
    }

    @JvmStatic
    private fun getPlayButtonPauseStateResId(): Int = if (BaseUtil.isAndroidSOrLater) {
        R.drawable.notification_play_btn
    } else {
        R.drawable.notification_play_btn_below_s
    }

    @JvmStatic
    fun getMarkButtonResId(): Int = if (BaseUtil.isAndroidSOrLater) {
        R.drawable.notification_mark_btn
    } else {
        R.drawable.notification_mark_btn_below_s
    }
}