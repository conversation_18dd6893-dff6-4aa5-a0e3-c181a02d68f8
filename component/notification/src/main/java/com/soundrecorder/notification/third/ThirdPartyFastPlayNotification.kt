/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ThirdPartyFastPlayNotification
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/07/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.third

import android.content.Intent
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.notification.NotificationUtils
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.notification.R

class ThirdPartyFastPlayNotification(groupId: Int, notificationId: Int) :
    ThirdPartyNotification(groupId, notificationId) {

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    override var logTag: String
        get() = "ThirdPartyFastPlayNotification"
        set(value) {}

    override fun getOldChannelId(): String {
        return NotificationUtils.BROWSFILE_OLD_CID
    }

    override fun getChannelId(): String {
        return NotificationUtils.BROWSFILE_CID
    }

    override fun getChannelName(): String {
        return defaultContext.resources.getString(R.string.play_shortcut_channel_name)
    }

    override fun getJumpIntent(): Intent? {
        return browseFileApi?.createBrowseFileIntent(defaultContext)?.also {
            it.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
        }
    }
}