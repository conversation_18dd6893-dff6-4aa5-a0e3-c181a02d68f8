/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ThirdPartyNotification
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/07/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.notification.third

import android.app.Notification
import android.app.PendingIntent
import android.app.Service
import android.graphics.BitmapFactory
import android.widget.RemoteViews
import androidx.lifecycle.Observer
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.task.AppTaskUtil
import com.soundrecorder.common.utils.AppCardUtils.addContinueFlag
import com.soundrecorder.common.utils.AppCardUtils.launchDisplay
import com.soundrecorder.common.utils.DisplayUtils
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.common.utils.RecordModeUtil.isFromCall
import com.soundrecorder.notification.R
import com.soundrecorder.notification.base.BaseNotification

abstract class ThirdPartyNotification(groupId: Int, notificationId: Int) : BaseNotification(groupId, notificationId) {

    override var logTag: String
        get() = "ThirdPartyNotification"
        set(value) {}

    private val playNameObserver = Observer<String> {
//        DebugUtil.d(logTag, "playNameObserver: playName = $it, refreshState = $refreshState")
        if (refreshState == REFRESH_STATE_ENABLED) {
            DebugUtil.d(logTag, "playName = $it, id=$this")
            refreshNotification()
        }
    }

    private val curTimeObserver = Observer<Long> {
//        DebugUtil.d(logTag, "curTimeObserver: currentTime == $it, refreshState = $refreshState")
        if (refreshState == REFRESH_STATE_ENABLED) {
            DebugUtil.d(logTag, "curTimeObserver: currentTime = $it, id=$this")
            refreshNotification()
        }
    }

    private val playStatusObserver = Observer<Int> {
//        DebugUtil.d(logTag, "currentPlayStatus = $it, refreshState = $refreshState")
        if (refreshState == REFRESH_STATE_ENABLED) {
            DebugUtil.d(logTag, "currentPlayStatus = $it, id=$this")
            refreshNotification()
        }
    }

    private val saveStateObserver = Observer<Int> {
        refreshNotification()
    }

    override fun observeData() {
        doInMainThread {
            screenStateLiveData.observeForever(screenStateObserver)
            notificationModel?.playName?.observeForever(playNameObserver)
            notificationModel?.curTime?.observeForever(curTimeObserver)
            notificationModel?.playStatus?.observeForever(playStatusObserver)
            notificationModel?.saveState?.observeForever(saveStateObserver)
            defaultContext.registerReceiver(normalEventReceiver, getNormalEventFilter())
        }
    }

    @Suppress("TooGenericExceptionCaught")
    override fun onRelease() {
        doInMainThread {
            screenStateLiveData.removeObserver(screenStateObserver)
            notificationModel?.playName?.removeObserver(playNameObserver)
            notificationModel?.curTime?.removeObserver(curTimeObserver)
            notificationModel?.playStatus?.removeObserver(playStatusObserver)
            notificationModel?.saveState?.removeObserver(saveStateObserver)
            try {
                defaultContext.unregisterReceiver(normalEventReceiver)
            } catch (e: Exception) {
                DebugUtil.w(logTag, "onRelease, unRegisterReceiver")
            }
            releaseHandler()
            DebugUtil.d(logTag, "onRelease success, id=$this")
        }
    }

    override fun showNotification(service: Service?) {
        this.service = service

        refreshState = REFRESH_STATE_UNINITED
        observeData()
        sendNotification(false)
        refreshState = REFRESH_STATE_ENABLED
    }

    override fun refreshNotification() {
        sendNotification(true)
    }

    override fun getLayoutId(): Int {
        return -1
    }

    override fun getFoldLayoutId(): Int? {
        return null
    }

    override fun initNotification() {
        val builder = if (BaseUtil.isAndroidOOrLater) {
            Notification.Builder(BaseApplication.getAppContext(), getChannelId())
        } else {
            Notification.Builder(BaseApplication.getAppContext())
        }
        val jumpIntent = getJumpIntent()?.addContinueFlag()
        if (jumpIntent != null) {
            builder.setContentIntent(
                PendingIntent.getActivity(
                    defaultContext,
                    0,
                    jumpIntent,
                    PendingIntent.FLAG_IMMUTABLE,
                    DisplayUtils.mainId.launchDisplay()
                )
            )
        }
        notification = builder.setContentTitle(getContentTitle().first)
            .setContentText(getContentText().first)
            .setSmallIcon(com.soundrecorder.common.R.drawable.ic_launcher_recorder)
            .setLargeIcon(
                BitmapFactory.decodeResource(
                    defaultContext.resources,
                    com.soundrecorder.common.R.drawable.ic_launcher_recorder
                )
            )
            .setOnlyAlertOnce(true)
            .build()
        notification?.let {
            it.flags = it.flags or Notification.FLAG_ONGOING_EVENT or Notification.FLAG_NO_CLEAR
        }
    }

    override fun getContentTitle(): Pair<String, String> {
        return (notificationModel?.playName?.value ?: "").run {
            Pair(this, this)
        }
    }

    override fun getContentText(): Pair<String, String> {
        val str = if (isPlaying()) {
            defaultContext.getString(R.string.play_record)
        } else {
            defaultContext.getString(R.string.pause_recordfile)
        }
        return Pair(str, str)
    }

    override fun setRemoteViewData(remoteViews: RemoteViews) {
        //do nothing 系统默认remoteView
    }
}