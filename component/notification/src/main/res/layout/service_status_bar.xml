<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:internal="http://schemas.android.com/apk/prv/res/android"
    android:id="@+id/status_bar_layout"
    android:layout_width="match_parent"
    android:layout_height="64dip"
    android:baselineAligned="false"
    android:gravity="center_vertical"
    android:minHeight="@dimen/service_statusbar_minheight"
    android:orientation="horizontal" >

    <FrameLayout
            android:layout_width="@dimen/service_statusbar_layoutwidht"
            android:layout_height="@dimen/service_statusbar_layoutheight"
             >

        <ImageView
                android:id="@+id/icon_big_back"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:src="@drawable/ic_launcher_recorder"
                android:contentDescription="@null" />

        <ImageView
                android:id="@+id/icon_big"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:contentDescription="@null" />
    </FrameLayout>

    <LinearLayout
        android:id="@+id/statusbar_info_content"
        android:layout_width="@dimen/statusbar_infocontent_layout_width"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingStart="@dimen/statusbar_info_paddingleft"
        android:paddingEnd="@dimen/statusbar_info_paddingright" >

       <TextView
            android:id="@+id/line_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:fadingEdge="horizontal"
            android:singleLine="true"
            android:textColor="@color/service_status_bar_line1_textColor"
            android:textSize="11sp" />

        <TextView
            android:id="@+id/line_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/statusbar_info_line2marginTop"
            android:ellipsize="end"
            android:fadingEdge="horizontal"
            android:singleLine="true"
            android:textColor="@color/service_status_bar_line2_textColor"
            android:textSize="17sp" />
    </LinearLayout>

</LinearLayout>