<?xml version="1.0" encoding="utf-8"?>
<com.soundrecorder.common.widget.ClickScaleCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:forceDarkAllowed="false"
    android:elevation="0dp"
    android:clickable="true"
    android:longClickable="true"
    app:cardCornerRadius="@dimen/dp10"
    app:couiCardCornerWeight="@dimen/coui_round_corner_m_weight"
    android:tag="MARK_LIST_DIALOG"
    app:exclude_view_tags="btnMenuMore,img_mark_photo">

    <View
        android:id="@+id/divider_line"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginStart="@dimen/dp16"
        android:layout_marginEnd="@dimen/dp16"
        android:background="?attr/couiColorDivider"
        app:layout_constraintTop_toBottomOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">
    </View>

    <!-- 第一行 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/first_line_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/divider_line"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/text_mark_time"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginTop="@dimen/dp20"
            android:layout_marginBottom="@dimen/dp19.5"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp24"
            android:fontFamily="sans-serif-medium"
            android:fontFeatureSettings="tnum"
            android:textFontWeight="500"
            android:gravity="center_vertical|start"
            android:focusableInTouchMode="false"
            android:textColor="@color/coui_color_label_secondary"
            android:textSize="@dimen/dp16"
            android:forceDarkAllowed="false"/>

        <ImageButton
            android:id="@+id/btnMenuMore"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_width="@dimen/dp24"
            android:layout_height="@dimen/dp24"
            android:layout_marginTop="@dimen/dp20"
            android:layout_marginBottom="@dimen/dp19.5"
            android:layout_marginEnd="@dimen/dp20"
            android:background="@drawable/menu_more_pressed"
            android:contentDescription="@string/abc_action_menu_overflow_description"
            android:foreground="@null"
            android:scaleType="center"
            android:src="@drawable/icon_mark_more"
            android:tag="btnMenuMore" />

        <LinearLayout
            android:id="@+id/mark_content_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp16"
            android:layout_marginEnd="@dimen/dp16"
            android:layout_marginBottom="@dimen/dp19.5"
            app:layout_constraintStart_toEndOf="@id/text_mark_time"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btnMenuMore"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_mark_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp20"
                android:gravity="start"
                android:textColor="@color/coui_color_label_primary"
                android:textSize="@dimen/dp16"
                android:textAlignment="viewStart"
                android:fontFamily="sans-serif-medium"
                android:textFontWeight="500"
                tools:text="标记 2">
            </TextView>

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/img_mark_photo"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_width="@dimen/dp68"
                android:layout_height="@dimen/dp68"
                android:layout_marginTop="@dimen/dp12"
                android:contentDescription="@string/talkback_preview_mark_picture"
                android:forceDarkAllowed="false"
                android:foreground="@null"
                android:scaleType="centerCrop"
                android:tag="img_mark_photo"
                android:visibility="visible"
                app:strokeColor="@color/picture_mark_icon_bord_color"
                app:strokeWidth="0.5dp" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</com.soundrecorder.common.widget.ClickScaleCardView>