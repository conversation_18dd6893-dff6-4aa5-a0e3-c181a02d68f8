/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File       : WaveDataMap.kt
 ** Description: WaveDataMap.kt
 ** Version    : 1.0
 ** Date       : 2025/06/12
 ** Author     : <EMAIL>
 * <p>
 ** ---------------------Revision History: ---------------------
 **  <author>   <data>   <version >    <desc>
 **  W9017070  2025/06/12      1.0     create file
 ****************************************************************/
package com.soundrecorder.wavemark.wave.entity

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.modulerouter.waveMark.WaveConstant.COMPRESS_48_AMP_TYPE
import com.soundrecorder.modulerouter.waveMark.WaveConstant.COMPRESS_90_AMP_TYPE
import com.soundrecorder.modulerouter.waveMark.WaveConstant.FULL_AMP_TYPE
import com.soundrecorder.wavemark.wave.utils.AmpCompressUtils

class WaveDataMap() :
    LinkedHashMap<String, List<Int>>() {
    companion object {
        const val TAG = "WaveDataMap"

        const val TARGET_COUNT_90 = 90
        const val TARGET_COUNT_48 = 48
    }


    constructor(originalAmpWaveData: List<Int>) : this() {
        // Specification data is assembled using raw waveforms
        if (originalAmpWaveData.isEmpty()) {
            DebugUtil.w(TAG, "originalAmpWaveData is Empty !")
        } else {
            this.apply {
                put(FULL_AMP_TYPE, originalAmpWaveData)
                put(
                    COMPRESS_90_AMP_TYPE,
                    AmpCompressUtils.processAmplitudes(originalAmpWaveData, TARGET_COUNT_90)
                )
                put(
                    COMPRESS_48_AMP_TYPE,
                    AmpCompressUtils.processAmplitudes(originalAmpWaveData, TARGET_COUNT_48)
                )
            }
        }
    }

    /**
     * To get the amplitude data of the special type or segments
     *
     */
    fun getAmpByTypeOrSegments(ampTypeOrSegments: String): List<Int>? {
        if (this.isEmpty()) {
            DebugUtil.w(TAG, "getAmpByTypeOrSegments data is Empty !")
            return null
        }

        return if (ampTypeOrSegments in this.keys) {
            // FULL_AMP_TYPE,TARGET_COUNT_90,TARGET_COUNT_48
            this[ampTypeOrSegments]
        } else {
            // other amplitude type(segments)
            if (ampTypeOrSegments.toIntOrNull() == null) {
                DebugUtil.e(TAG, "getAmpByTypeOrSegments segments  is illegal !")
                return null
            }
            this[FULL_AMP_TYPE]?.let {
                AmpCompressUtils.processAmplitudes(it, ampTypeOrSegments.toInt())
            }
        }
    }
}