package com.soundrecorder.wavemark.mark

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.db.PictureMarkDbUtils
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils

object ClearDataUtils {


    private const val TAG = "ClearDataUtils"

    /**
     * picture mark ,clear not use data
     */
    @DelicateCoroutinesApi
    @JvmStatic
    fun clearPictureMark() {
        DebugUtil.i(TAG, "clearPictureMark start")
        GlobalScope.launch(Dispatchers.IO) {
            val allPictureMarks = PictureMarkDbUtils.queryAllPictureMarks()
            allPictureMarks.forEach {
                clearPictureMark(it)
            }
            clearPictureFile()
        }
        DebugUtil.i(TAG, "clearPictureMark end")
    }

    /**
     * clear picture mark
     */
    @JvmStatic
    private fun clearPictureMark(markDataBean: MarkDataBean) {
        if (markDataBean.pictureFilePath.isNotEmpty()) {
            val appFile = FileUtils.getAppFile(markDataBean.pictureFilePath, false)
            if (!appFile.exists()) {
                PictureMarkDbUtils.deletePictureMark(markDataBean.keyId, markDataBean.timeInMills)
                DebugUtil.i(TAG, "clearPictureMark deletePictureMark , MarkDataBean=$markDataBean")
            }
        }

        val recorder = RecorderDBUtil.getInstance(BaseApplication.getAppContext()).getRecordById(markDataBean.keyId)
        if (recorder == null) {
            PictureMarkDbUtils.deletePictureMark(markDataBean.keyId, markDataBean.timeInMills)
            DebugUtil.i(TAG, "clearPictureMark deletePictureMark recorder is null, MarkDataBean=$markDataBean")
        }
    }


    /**
     * clear picture file
     */
    @JvmStatic
    fun clearPictureFile() {
        val parentFile = FileUtils.getAppFile("", false).parentFile
        parentFile?.let { it ->
            val listFiles = it.listFiles()
            listFiles?.forEach { file ->
                val count = PictureMarkDbUtils.queryPictureMarksCntByPicturePath(file.name)
                if (count <= 0) {
                    file.delete()
                    DebugUtil.i(TAG, "clearPictureFile file delete , filePath${file.absoluteFile}")
                }
            }
        }
    }
}