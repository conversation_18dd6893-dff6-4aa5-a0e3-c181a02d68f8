/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveMarkApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/31
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark

import android.net.Uri
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.modulerouter.waveMark.IIPictureMarkListener
import com.soundrecorder.modulerouter.waveMark.IPictureMarkDelegate
import com.soundrecorder.modulerouter.waveMark.IPictureMarkLifeOwnerProvider
import com.soundrecorder.modulerouter.waveMark.WaveConstant
import com.soundrecorder.modulerouter.waveMark.WaveMarkInterface
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.wavemark.picturemark.PictureMarkDelegate
import com.soundrecorder.wavemark.wave.load.AmplitudeListUtil
import com.soundrecorder.wavemark.wave.load.AmpTaskManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers

object WaveMarkApi : WaveMarkInterface {

    override fun decodeAmplitudeByUri(uri: Uri?): String {
        return AmplitudeListUtil.decodeAmplitudeByUri(uri)
    }

    override fun <T, R> newPictureMarkDelegate(
        ownerProvider: IPictureMarkLifeOwnerProvider,
        isActivityRecreate: Boolean,
        listener: IIPictureMarkListener<T, R>?,
    ): IPictureMarkDelegate<T> {
        return PictureMarkDelegate(
            ownerProvider,
            isActivityRecreate,
            listener as? IIPictureMarkListener<MarkMetaData, MarkDataBean>
        ) as IPictureMarkDelegate<T>
    }

    override fun getMergeMarkList(path: String, playUri: Uri, isRecycle: Boolean): List<Any> {
        val amplitudeListUtil =
            AmplitudeListUtil(BaseApplication.getAppContext(), path, playUri, isRecycle)
        val markList = amplitudeListUtil.mergeMarkList
        amplitudeListUtil.release()
        return markList
    }

    /**
     * getAmplitudeByType
     * @param path 音频文件路径
     * @param playUri 音频文件uri
     * @param isRecycle 是否回收
     * @param ampType 波形类型
     * @param callBack 拿到波形数据后的回调
     */
    override fun getAmplitudeByType(
        path: String?,
        playUri: Uri?,
        isRecycle: Boolean,
        ampTypeOrSegments: String,
        doInIOThread: Boolean,
        ampReadyCallback: ((waveReadyDataList: List<Int>?) -> Unit)?,
        decodeFinishCallback: (waveDataMap: List<Int>?) -> Unit
    ) {
        if (doInIOThread) {
            CoroutineUtils.doInIOThread({
                AmpTaskManager.getAmplitudeByType(path, playUri, isRecycle, ampTypeOrSegments, ampReadyCallback, decodeFinishCallback)
            }, CoroutineScope(Dispatchers.IO))
        } else {
            AmpTaskManager.getAmplitudeByType(path, playUri, isRecycle, ampTypeOrSegments, ampReadyCallback, decodeFinishCallback)
        }
    }

    /**
     * getAmplitudeByType
     * @param path 音频文件路径
     * @param uri 音频文件uri
     * @param amplitudeList 原始波形数据
     */
    override fun asyncUpdateAmpFileAndDB(
        path: String,
        uri: Uri,
        amplitudeList: List<Int>
    ) {
        AmpTaskManager.asyncUpdateAmpFileAndDB(path, uri, amplitudeList)
    }

    override fun initAmplitudeListUtil(
        path: String?,
        playUri: Uri?,
        isRecycle: Boolean
    ) {
        AmpTaskManager.addGetAmplitudeTask(path, playUri, isRecycle, WaveConstant.FULL_AMP_TYPE)
    }

    override fun getMarkStringByPath(path: String?): String? {
        return AmpTaskManager.getMarkStringByPath(path)
    }

    override fun getMergeMarkListByPath(path: String?): List<Any>? {
        return AmpTaskManager.getMergeMarkListByPath(path)
    }

    override fun getAmpFormDbOrCustomTagByPath(
        path: String?,
        playUri: Uri?,
        ampTypeOrSegments: String
    ): List<Int>? {
        return AmpTaskManager.getAmpFormDbOrCustomTagByPath(path, playUri, ampTypeOrSegments)
    }
    override fun releaseMp3ByPath(path: String?) { AmpTaskManager.releaseMp3ByPath(path) }
    override fun genAmpFromSoundFile(
        path: String?,
        playUri: Uri?,
        isRecycle: Boolean,
        decodeReadyCallback: ((List<Int>?) -> Unit),
        decodeFinishCallback: ((List<Int>?) -> Unit)
    ) {
        AmpTaskManager.genAmpFromSoundFile(
            path, playUri, isRecycle,
            { readyAmpList -> decodeReadyCallback.invoke(readyAmpList) },
            { fullAmpList -> decodeFinishCallback.invoke(fullAmpList) })
    }
    override fun releaseSoundByPath(path: String?) {
        AmpTaskManager.releaseSoundByPath(path)
    }
    override fun releaseAmplitudeListUtilByPath(path: String?) {
        AmpTaskManager.removeTaskByPath(path)
    }
}