package com.soundrecorder.wavemark.model;

import java.util.List;

import com.soundrecorder.common.databean.markdata.MarkDataBean;

public class AmpAndMarkModel {
    //amp
    private List<Integer> mAmpList;
    private List<Integer> mAmpList90;

    //mark
    private String mMarkString; //for old text markDataBeans
    private List<MarkDataBean> markDataBeans; //for new markDataBeans

    public List<Integer> getAmpList() {
        return mAmpList;
    }

    public void setAmpList(List<Integer> ampList) {
        this.mAmpList = ampList;
    }

    public List<Integer> getAmpList90() {
        return mAmpList90;
    }

    public void setAmpList90(List<Integer> ampList90) {
        this.mAmpList90 = ampList90;
    }

    public String getMarkString() {
        return mMarkString;
    }

    public void setMarkString(String markString) {
        this.mMarkString = markString;
    }

    public List<MarkDataBean> getMarkDataBeans() {
        return markDataBeans;
    }

    public void setMarkDataBeans(List<MarkDataBean> markDataBeans) {
        this.markDataBeans = markDataBeans;
    }
}
