/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File       : AmpTaskManager.kt
 ** Description: AmpTaskManager.kt
 ** Version    : 1.0
 ** Date       : 2025/06/27
 ** Author     : <EMAIL>
 * <p>
 ** ---------------------Revision History: ---------------------
 **  <author>   <data>   <version >    <desc>
 **  W9017070  2025/06/27      1.0     create file
 ****************************************************************/

package com.soundrecorder.wavemark.wave.load

import android.net.Uri
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.AmpFileUtil
import com.soundrecorder.common.utils.AmpFileUtil.convertStringToInt
import com.soundrecorder.common.utils.CoroutineUtils
import com.soundrecorder.modulerouter.waveMark.WaveConstant
import com.soundrecorder.wavemark.wave.entity.WaveDataMap
import com.soundrecorder.wavemark.wave.entity.WaveDataMap.Companion.TARGET_COUNT_90
import com.soundrecorder.wavemark.wave.utils.AmpCompressUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import java.util.concurrent.ConcurrentHashMap

object AmpTaskManager {
    private const val TAG = "AmpTaskManager"

    /**
     * The same path corresponds to the same amplitudeListUtil instance.
     */
    @JvmStatic
    private var ampTaskMap = ConcurrentHashMap<String, ArrayList<AmpTaskData>>()

    /**
     * To get amplitude by type or segments
     */
    @JvmStatic
    fun getAmplitudeByType(
        path: String?,
        playUri: Uri?,
        isRecycle: Boolean,
        ampTypeOrSegments: String,
        ampReadyCallback: ((waveReadyDataList: List<Int>?) -> Unit)?,
        decodeFinishCallback: (waveDataList: List<Int>?) -> Unit
    ) {
        if (path.isNullOrEmpty() || playUri == null) {
            DebugUtil.e(TAG, "getAmplitudeByType path:$path or playUri:$playUri is null !")
            return
        }
        val isExistTask = addGetAmplitudeTask(
            path,
            playUri,
            isRecycle,
            ampTypeOrSegments,
            { ampReadyList -> ampReadyCallback?.invoke(ampReadyList) },
            { ampSegmentsList -> decodeFinishCallback.invoke(ampSegmentsList) }
        )
        if (isExistTask) {
            DebugUtil.d(TAG, "getAmplitudeByType exist task path:$path")
            return
        }
        // It will be time-consuming.
        ampTaskMap.getAmplitudeListUtilByPath(path)?.apply {
            val currentTime = System.currentTimeMillis()
            ampList.also {
                val getAmpListTime = System.currentTimeMillis()
                DebugUtil.d(TAG, "getAmplitudeByType getAmpList cost ${getAmpListTime - currentTime}ms")
                if (it.isNullOrEmpty().not()) {
                    /**
                     * ->source one: json file or mp3 file customTag.
                     * If the data is cached, directly return the amplitude data.
                     */

                    if (ampStringFromMp3 != null) {
                        // amplitude data from customTag. Asynchronously update amp_file_path, amp file
                        asyncUpdateAmpFileAndDB(path, playUri, convertStringToInt(ampStringFromMp3))
                    }
                    dispatchReadyOrFinishCallBack(path = path, soundFile = null, waveDataMap = it)
                    removeTaskByPath(path)
                } else {
                    /**
                     *  ->source two: decode file.
                     * Use audio file samples to parse the recorded waveform and assemble the data.
                     */
                    startDecodeSoundFile(this, path, playUri)
                }
            }
        }
    }

    /**
     * Decode the file
     */
    @JvmStatic
    fun startDecodeSoundFile(
        amplitudeListUtil: AmplitudeListUtil,
        path: String,
        playUri: Uri,
    ) {
        amplitudeListUtil.apply {
            releaseMp3ByPath(path)
            DebugUtil.d(TAG, "startDecodeSoundFile s")
            val startTime = System.currentTimeMillis()
            setDecodeReady { _, soundFile ->
                DebugUtil.d(TAG, "startDecodeSoundFile setDecodeReady soundFile.amplitudeList.size:${soundFile?.amplitudeList?.size}")
                dispatchReadyOrFinishCallBack(false, path, soundFile, null)
            }
            setDecodeFinish { ampString ->
                DebugUtil.d(TAG, "startDecodeSoundFile setDecodeFinish spend time:${System.currentTimeMillis() - startTime}")
                if (ampString.isNullOrEmpty()) {
                    DebugUtil.e(TAG, "startDecodeSoundFile ampString is NullOrEmpty !")
                    dispatchReadyOrFinishCallBack(path = path, soundFile = null, waveDataMap = null)
                } else {
                    handlerAmp(path, playUri, ampString)
                }
                removeTaskByPath(path)
            }
            getAmpFromSoundFile()
        }
    }

    @JvmStatic
    fun updateAmpFileAndDB(
        path: String,
        uri: Uri,
        amplitudeList: List<Int>
    ) {
        if (!AmpFileUtil.ampFileIsExists(BaseApplication.getAppContext(), path)) {
            DebugUtil.d(TAG, "updateAmpFileAndDB start path:$path")
            AmplitudeListUtil.writeAmpData(
                BaseApplication.getAppContext(),
                path,
                uri,
                amplitudeList
            )
        } else {
            DebugUtil.d(TAG, "updateAmpFileAndDB ampFile exists")
        }
    }

    /**
     * handlerAmp : dispatchReadyOrFinishCallBack and updateAmpFileAndDB
     */
    @JvmStatic
    fun handlerAmp(path: String, playUri: Uri, ampString: String) {
        val originalAmpIntList = convertStringToInt(ampString)
        val waveDataMap = WaveDataMap(originalAmpIntList)
        dispatchReadyOrFinishCallBack(path = path, soundFile = null, waveDataMap = waveDataMap)
        updateAmpFileAndDB(path, playUri, originalAmpIntList)
    }

    /**
     * asyncUpdateAmpFileAndDB
     */
    @JvmStatic
    fun asyncUpdateAmpFileAndDB(path: String, uri: Uri, amplitudeList: List<Int>) {
        CoroutineUtils.doInIOThread({
            updateAmpFileAndDB(
                path,
                uri,
                amplitudeList
            )
        }, CoroutineScope(Dispatchers.IO))
    }

    /**
     * To dispatch ampFinishCallBack according to path,or dispatch ampReadyCallBack
     */
    @JvmStatic
    fun dispatchReadyOrFinishCallBack(
        isFinishCallBack: Boolean = true,
        path: String,
        soundFile: SoundFile?,
        waveDataMap: WaveDataMap?
    ) {
        if (isFinishCallBack) {
            ampTaskMap[path]?.forEach { ampTaskData ->
                ampTaskData.apply {
                    if (typeOrSegments.isNullOrEmpty()) {
                        DebugUtil.e(TAG, "dispatchReadyOrFinishCallBack typeOrSegments is null !")
                    } else {
                        ampFinishCallBack?.invoke(waveDataMap?.getAmpByTypeOrSegments(typeOrSegments))
                    }
                }
            }
        } else {
            ampTaskMap[path]?.forEach { ampTaskData ->
                ampTaskData.apply {
                    if (soundFile?.amplitudeList != null) {
                        //To save part of the waveform data of the ampReadyCallBack, which may be used for PlaybackContainerViewModel.
                        waveReadyDataList = soundFile.amplitudeList

                        when (typeOrSegments) {
                            WaveConstant.FULL_AMP_TYPE -> ampReadyCallBack?.invoke(soundFile.amplitudeList)
                            WaveConstant.COMPRESS_90_AMP_TYPE ->
                                ampReadyCallBack?.invoke(AmpCompressUtils.processAmplitudes(soundFile.amplitudeList, TARGET_COUNT_90))
                            else ->
                                // Other types do not exist theoretically
                                DebugUtil.w(TAG, "dispatchReadyOrFinishCallBack other typeOrSegments:$typeOrSegments")
                        }
                    } else {
                        DebugUtil.e(TAG, "dispatchReadyOrFinishCallBack soundFile.amplitudeList is null !")
                    }
                }
            }
        }
    }

    /**
     * getMarkStringByPath
     */
    fun getMarkStringByPath(path: String?): String? {
        if (path.isNullOrEmpty()) {
            DebugUtil.e(TAG, "getMarkStringByPath path is null !")
            return null
        }
        return ampTaskMap.getAmplitudeListUtilByPath(path)?.markString
    }

    /**
     * getMergeMarkListByPath
     */
    fun getMergeMarkListByPath(path: String?): List<Any>? {
        if (path.isNullOrEmpty()) {
            DebugUtil.e(TAG, "getMergeMarkListByPath path is null !")
            return null
        }
        return ampTaskMap.getAmplitudeListUtilByPath(path)?.mergeMarkList
    }

    /**
     *  getAmpFormDbOrCustomTagByPath (Initialization is required -> addGetAmplitudeTask())
     */
    fun getAmpFormDbOrCustomTagByPath(path: String?, playUri: Uri?, ampTypeOrSegments: String): List<Int>? {
        if (path.isNullOrEmpty()) {
            DebugUtil.e(TAG, "getAmpFormDbOrCustomTagByPath path is null !")
            return null
        }
        val waveDataMap = ampTaskMap.getAmplitudeListUtilByPath(path)?.ampList
        if (waveDataMap.isNullOrEmpty() || playUri == null) {
            DebugUtil.e(TAG, "getAmpFormDbOrCustomTagByPath ampList:$waveDataMap or playUri:$playUri is null !")
        } else {
            //check if need update amp file and amp_file_path in db
            waveDataMap.getAmpByTypeOrSegments(WaveConstant.FULL_AMP_TYPE)?.let {
                asyncUpdateAmpFileAndDB(path, playUri, it)
            }
        }
        return waveDataMap?.getAmpByTypeOrSegments(ampTypeOrSegments)
    }

    fun releaseMp3ByPath(path: String?) {
        if (path.isNullOrEmpty()) {
            DebugUtil.e(TAG, "releaseMp3ByPath path is null !")
            return
        }
        ampTaskMap.getAmplitudeListUtilByPath(path)?.releaseMp3()
    }

    fun genAmpFromSoundFile(
        path: String?,
        playUri: Uri?,
        isRecycle: Boolean,
        decodeReadyCallback: (List<Int>?) -> Unit,
        decodeFinishCallback: (List<Int>?) -> Unit
    ) {
        if (path.isNullOrEmpty() || playUri == null) {
            DebugUtil.e(TAG, "genAmpFromSoundFile path:$path or playUri:$playUri is null !")
            return
        }
        ampTaskMap.apply {
            getAmplitudeListUtilByPath(path)?.also {
                addGetAmplitudeTask(
                    path, playUri, isRecycle, WaveConstant.FULL_AMP_TYPE,
                    { readyAmpList -> decodeReadyCallback.invoke(readyAmpList) },
                    { fullAmpList -> decodeFinishCallback.invoke(fullAmpList) }
                )

                /**
                 * 1,waveReadyDataList is not empty ->It has been generated, invoke it.
                 * 2,Otherwise -> decode the file directly.
                 */
                val waveReadyDataList =  get(path)?.get(0)?.waveReadyDataList
                if (waveReadyDataList != null) {
                    decodeReadyCallback.invoke(waveReadyDataList)
                } else {
                    startDecodeSoundFile(it, path, playUri)
                }
            }
        }
    }

    fun releaseSoundByPath(path: String?) {
        if (path.isNullOrEmpty()) {
            DebugUtil.e(TAG, "releaseSoundByPath path is null !")
            return
        }
        ampTaskMap.getAmplitudeListUtilByPath(path)?.releaseSound()
    }


    fun removeTaskByPath(path: String?) {
        if (path.isNullOrEmpty()) {
            DebugUtil.e(TAG, "removeTaskByPath path is null !")
            return
        }

        ampTaskMap.apply {
            getAmplitudeListUtilByPath(path)?.release()
            remove(path)
        }
    }


    fun addGetAmplitudeTask(
        path: String?,
        playUri: Uri?,
        isRecycle: Boolean,
        ampTypeOrSegments: String,
        decodeReadyCallback: ((waveDataList: List<Int>?) -> Unit)? = null,
        decodeFinishCallback: ((ampSegmentsList: List<Int>?) -> Unit)? = null
    ): Boolean {
        if (path.isNullOrEmpty() || playUri == null) {
            DebugUtil.e(TAG, "addGetAmplitudeTask path:$path or playUri:$playUri is null !")
            return false
        }
        if (ampTaskMap.containsKey(path)) {
            DebugUtil.d(TAG, "addGetAmplitudeTask ampTaskMap.containsKey(path) path:$path")
            val amplitudeListUtil = ampTaskMap.getAmplitudeListUtilByPath(path)
            ampTaskMap[path]?.apply {
                add(
                    AmpTaskData(
                        amplitudeListUtil,
                        null,
                        { readyAmpList -> decodeReadyCallback?.invoke(readyAmpList) },
                        { ampSegmentsList -> decodeFinishCallback?.invoke(ampSegmentsList) },
                        ampTypeOrSegments
                    )
                )
            }
            return true
        } else {
            val amplitudeListUtil = AmplitudeListUtil(BaseApplication.getAppContext(), path, playUri, isRecycle)
            ampTaskMap.put(
                path, arrayListOf(
                    AmpTaskData(
                        amplitudeListUtil,
                        null,
                        { readyAmpList -> decodeReadyCallback?.invoke(readyAmpList) },
                        { ampSegmentsList -> decodeFinishCallback?.invoke(ampSegmentsList) },
                        ampTypeOrSegments
                    )
                )
            )
            return false
        }
    }

    fun ConcurrentHashMap<String, ArrayList<AmpTaskData>>.getAmplitudeListUtilByPath(path: String?): AmplitudeListUtil? {
        if (path.isNullOrEmpty()) {
            DebugUtil.e(TAG, "getAmplitudeListUtilByPath path is null !")
            return null
        }
        return this.get(path)?.get(0)?.amplitudeListUtil
    }
}

data class AmpTaskData(
    // AmplitudeListUtil instance
    val amplitudeListUtil: AmplitudeListUtil? = null,
    // a part of amplitudeList
    var waveReadyDataList: List<Int>?,
    // ampReadyCallBack(invoke a part of amplitudeList)
    val ampReadyCallBack: ((waveReadyDataList: List<Int>?) -> Unit)? = null,
    // full amp callback
    val ampFinishCallBack: ((waveDataList: List<Int>?) -> Unit)? = null,
    // amplitudeList segments
    val typeOrSegments: String?
)