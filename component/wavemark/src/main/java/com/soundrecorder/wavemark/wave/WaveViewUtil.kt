/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/07
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.wavemark.wave

import android.content.Context
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.wavemark.R
import java.util.Locale
import java.util.Objects

object WaveViewUtil {
    private const val TAG = "WaveViewUtil"

    const val DURATION_INTERVAL: Long = 500L

    /**
     * Time scale count for each WaveView
     */
    const val TIME_SCALE_COUNT: Int = 12

    /**
     * Duration of each WaveView(ms)
     */
    const val ONE_WAVE_VIEW_DURATION: Long = DURATION_INTERVAL * TIME_SCALE_COUNT
    const val ONE_WAVE_VIEW_DURATION_SECOND: Int = 6
    const val DEFAUL_CENTER_TIME: Int = 5000 //5s
    const val AMPLITUDE_SCALE: Float = 1f
    const val MAX_AMPLITUDE: Int = 32768
    const val NUM_ONE: Int = 1
    const val NUM_TWO: Int = 2
    const val NUM_3: Int = 3
    const val NUM_4: Int = 4
    const val NUM_8: Int = 8
    const val NUM_TEN: Int = 10
    const val NUM_255: Int = 255
    const val PERCENT_0: Float = 0f
    const val PERCENT_8: Float = 0.08f
    const val PERCENT_10: Float = 0.1f
    const val PERCENT_15: Float = 0.15f
    const val PERCENT_20: Float = 0.2f
    const val PERCENT_30: Float = 0.3f
    const val PERCENT_70: Float = 0.7f
    const val PERCENT_90: Float = 0.9f
    const val PERCENT_100: Float = 1f
    const val PERCENT_110: Float = 1.1f
    const val FLOAT_0: Float = 0.0f
    const val FLOAT_1: Float = 1.0f
    const val NUM_TWO_F: Float = 2f

    private const val MINUTES_60 = 60
    private const val HOUR_10 = 10
    private const val SIX_MINUTES_3600 = 3600

    /**
     * 新版本：按波形图类型缓存的相关初始化参数
     */
    private val sTimeLineGapHashMap = HashMap<WaveType, Float>()
    private val sAmplitueLineGapHashMap = HashMap<WaveType, Float>()
    private val sOneWaveItemWidthHashMap = HashMap<WaveType, Float>()
    private val sOneWaveItemAmplitueLineCountHashMap = HashMap<WaveType, Float>()
    private val sOneWaveLineTimeHashMap = HashMap<WaveType, Float>()

    /**
     * 老版本：大波形图相关初始化参数
     */
    private var sLargeWaveTimeLineGap = 0
    private var sLargeWaveAmplitueLineGap = 0f
    private var sOneLargeWaveItemWidth = 0f
    private var sOneLargeWaveItemAmplitueLineCount = 0f
    private var sOneLargeWaveLineTime = 0f


    @JvmStatic
    fun getLargeWaveTimeLineGap(context: Context): Float {
        if (sLargeWaveTimeLineGap == 0) {
            sLargeWaveTimeLineGap = context.resources.getDimensionPixelSize(R.dimen.wave_time_line_gap_dp24)
            DebugUtil.i(TAG, "getTimeLineGap: $sLargeWaveTimeLineGap")
        }
        return sLargeWaveTimeLineGap.toFloat()
    }

    @JvmStatic
    fun getLargeWaveAmplitueLineGap(context: Context): Float {
        if (sLargeWaveAmplitueLineGap == 0f) {
            val px500s = getLargeWaveTimeLineGap(context)
            val ampGap = px500s * Constants.WAVE_SAMPLE_INTERVAL_TIME / DURATION_INTERVAL
            DebugUtil.i(TAG, "getAmpGap: ampGap $ampGap, getTimeLineGap: $px500s")
            sLargeWaveAmplitueLineGap = ampGap
        }
        return sLargeWaveAmplitueLineGap
    }

    @JvmStatic
    fun getOneLargeWaveItemWidth(context: Context): Float {
        if (sOneLargeWaveItemWidth == 0f) {
            sOneLargeWaveItemWidth = getLargeWaveTimeLineGap(context) * TIME_SCALE_COUNT
        }
        return sOneLargeWaveItemWidth
    }

    @JvmStatic
    fun getOneLargeWaveItemAmplitueLineCount(context: Context): Float {
        if (sOneLargeWaveItemAmplitueLineCount == 0f) {
            sOneLargeWaveItemAmplitueLineCount = getOneLargeWaveItemWidth(context) / getLargeWaveAmplitueLineGap(context)
        }
        return sOneLargeWaveItemAmplitueLineCount
    }

    @JvmStatic
    fun getOneLargeWaveLineTime(context: Context): Float {
        if (java.lang.Float.compare(sOneLargeWaveLineTime, 0f) <= 0) {
            sOneLargeWaveLineTime = ONE_WAVE_VIEW_DURATION.toFloat() / getOneLargeWaveItemAmplitueLineCount(context)
        }
        return sOneLargeWaveLineTime
    }

    @JvmStatic
    fun getTimeLineGapByWaveType(context: Context, waveType: WaveType): Float {
        var timeLineGap = sTimeLineGapHashMap[waveType]
        if (timeLineGap == null || timeLineGap == 0f) {
            timeLineGap = if (Objects.requireNonNull(waveType) == WaveType.LARGE) {
                context.resources.getDimensionPixelSize(R.dimen.wave_time_line_gap_dp24).toFloat()
            } else {
                context.resources.getDimensionPixelSize(R.dimen.wave_time_line_gap_dp17_5).toFloat()
            }
            sTimeLineGapHashMap[waveType] = timeLineGap
            DebugUtil.i(TAG, "getTimeLineGapByWaveType: " + waveType.name + ", timeLineGap: " + timeLineGap)
        }
        return timeLineGap
    }

    @JvmStatic
    fun getAmplitueLineGapByWaveType(context: Context, waveType: WaveType): Float {
        var amplitueLineGap = sAmplitueLineGapHashMap[waveType]
        if (amplitueLineGap == null || amplitueLineGap == 0f) {
            val px500s = getTimeLineGapByWaveType(context, waveType)
            amplitueLineGap = px500s * Constants.WAVE_SAMPLE_INTERVAL_TIME / DURATION_INTERVAL
            DebugUtil.i(TAG, "getAmplitueLineGapByWaveType: " + waveType.name + ",ampGap: " + amplitueLineGap + ", px500s: " + px500s)
            sAmplitueLineGapHashMap[waveType] = amplitueLineGap
        }
        return amplitueLineGap
    }

    @JvmStatic
    fun getOneWaveItemWidthByWaveType(context: Context, waveType: WaveType): Float {
        var oneWaveItemWidth = sOneWaveItemWidthHashMap[waveType]
        if (oneWaveItemWidth == null || oneWaveItemWidth == 0f) {
            oneWaveItemWidth = getTimeLineGapByWaveType(context, waveType) * TIME_SCALE_COUNT
            sOneWaveItemWidthHashMap[waveType] = oneWaveItemWidth
            DebugUtil.i(TAG, "getOneWaveItemWidthByWaveType: " + waveType.name + ", oneWaveItemWidth: " + oneWaveItemWidth)
        }
        return oneWaveItemWidth
    }

    @JvmStatic
    fun getOneWaveItemAmplitueLineCountByWaveType(context: Context, waveType: WaveType): Float {
        var oneWaveItemAmplitueLineCount = sOneWaveItemAmplitueLineCountHashMap[waveType]
        if (oneWaveItemAmplitueLineCount == null || oneWaveItemAmplitueLineCount == 0f) {
            oneWaveItemAmplitueLineCount = getOneWaveItemWidthByWaveType(context, waveType) / getAmplitueLineGapByWaveType(context, waveType)
            sOneWaveItemAmplitueLineCountHashMap[waveType] = oneWaveItemAmplitueLineCount
            DebugUtil.i(
                TAG, ("getOneWaveItemAmplitueLineCountByWaveType: " + waveType.name
                        + ", oneWaveItemAmplitueLineCount: " + oneWaveItemAmplitueLineCount)
            )
        }
        return oneWaveItemAmplitueLineCount
    }

    @JvmStatic
    fun getOneWaveLineTimeByWaveType(context: Context, waveType: WaveType): Float {
        var oneWaveLineTime = sOneWaveLineTimeHashMap[waveType]
        if (oneWaveLineTime == null || java.lang.Float.compare(oneWaveLineTime, 0f) <= 0) {
            oneWaveLineTime = ONE_WAVE_VIEW_DURATION.toFloat() / getOneWaveItemAmplitueLineCountByWaveType(context, waveType)
            sOneWaveLineTimeHashMap[waveType] = oneWaveLineTime
            DebugUtil.i(TAG, "getOneWaveLineTimeByWaveType: " + waveType.name + ", oneWaveLineTime: " + oneWaveLineTime)
        }
        return oneWaveLineTime
    }

    @JvmStatic
    fun getStringBySecond(seconds: Int): String {
        val hour = seconds / SIX_MINUTES_3600
        val minute = seconds / MINUTES_60 % MINUTES_60
        val second = seconds % MINUTES_60
        val locale = Locale.getDefault()
        var timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_default), minute, second, 0)

        if (hour > HOUR_10) {
            timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_ten_hour), hour, minute, second, 0)
            return timeStr.substring(0, timeStr.length - Constants.THREE)
        }

        if (hour > 0) {
            timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_one_hour), hour, minute, second, 0)
            return timeStr.substring(0, timeStr.length - Constants.THREE)
        }

        if (seconds >= 0) {
            timeStr = String.format(locale, BaseApplication.getAppContext().getString(R.string.time_default), minute, second, 0)
            return timeStr.substring(0, timeStr.length - Constants.THREE)
        }
        return timeStr
    }

    @JvmStatic
    fun clearAll() {
        DebugUtil.i(TAG, "WaveViewUtil clear all")
        sLargeWaveTimeLineGap = 0
        sLargeWaveAmplitueLineGap = 0f
        sOneLargeWaveItemWidth = 0f
        sOneLargeWaveItemAmplitueLineCount = 0f
        sOneLargeWaveLineTime = 0f

        sTimeLineGapHashMap.clear()
        sAmplitueLineGapHashMap.clear()
        sOneWaveItemWidthHashMap.clear()
        sOneWaveItemAmplitueLineCountHashMap.clear()
        sOneWaveLineTimeHashMap.clear()
    }

    //波形图尺寸类型
    enum class WaveType {
        //大尺寸
        LARGE,

        //中尺寸
        MIDDLE,

        //小尺寸
        SMALL
    }

    //波形图缩放类型
    enum class ZoomType {
        //整体缩放
        ZOOM_IN,

        //局部细节（放到）
        ZOOM_OUT
    }

    //波形图拖动类型
    enum class DragType {
        //可拖动
        DRAG_ENABLE,

        //不可拖动
        DRAG_UN_ENABLE
    }
}

