/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AmplitudeListUtil
 Description:
 Version: 1.0
 Date: 2022/8/9
 Author: W9013333(<EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9013333 2022/8/9 1.0 create
 */
package com.soundrecorder.wavemark.wave.load;

import android.content.Context;
import android.net.Uri;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.db.PictureMarkDbUtils;
import com.soundrecorder.common.db.RecorderDBUtil;
import com.soundrecorder.common.utils.amplitude.AmpFileUtil;
import com.soundrecorder.common.utils.MarkProcessUtil;
import com.soundrecorder.common.utils.MarkSerializUtil;
import com.soundrecorder.wavemark.wave.WaveViewUtil;
import com.soundrecorder.wavemark.wave.entity.WaveDataMap;
import com.soundrecorder.wavemark.wave.id3tool.Mp3File;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import kotlin.collections.CollectionsKt;

public class AmplitudeListUtil {
    private static final String TAG = "AmplitudeListUtil";
    private static final String SYMBOL_COMMA = ",";
    private static final int INVALID_INDEX = -1;
    private final String mPlayPath;
    private final Uri mPlayUri;
    private final Context mContext;
    private SoundFile mSoundFile = null;
    private String mMarkStringFromMp3 = null;
    private String mAmpStringFromMp3 = null;
    private DecodeFinish mDecodeFinish = null;
    private DecodeReady mDecodeReady = null;
    private Mp3File mMp3File = null;
    private SoundFile.DecodedReadyListener mSoundDecodeReady = null;
    private SoundFile.DecodedSoundFileListener mSoundDecodeFinish = null;
    private SoundFile.ProgressCallback mProgressCallback = null;
    private boolean mIsDecodeMp3 = false;

    private boolean mIsRecycle = false;

    public AmplitudeListUtil(Context context, String path, Uri playUri, boolean isRecycle) {
        mContext = context;
        mPlayPath = path;
        mPlayUri = playUri;
        mIsRecycle = isRecycle;
    }

    private String getAmpFromDB() {
        return RecorderDBUtil.getInstance(mContext).getAmpStringByPath(mPlayPath, mIsRecycle);
    }

    private String getOldMarkStringFromDB() {
        return RecorderDBUtil.getInstance(mContext).getMarkStringByPath(mPlayPath);
    }

    private void decodeMp3() {
        if ((mPlayPath == null) || (mPlayUri == null)) {
            DebugUtil.d(TAG, "mPlayName " + FileUtils.getDisplayNameByPath(mPlayPath) + " mPlayUri " + mPlayUri);
            return;
        }

        // 通过mimeType判断是否为mp3，拦截 非mp3文件
        String mimeType = mContext.getContentResolver().getType(mPlayUri);
        if (!RecordConstant.MIMETYPE_MP3.equals(mimeType)) {
            DebugUtil.d(TAG, "mime type is not mp3: " + mimeType);
            return;
        }
        mIsDecodeMp3 = true;

        try {
            mMp3File = new Mp3File();
            mMp3File.create(mPlayUri, mContext);
            mMp3File.extractMp3FileWithDefault(true);
            byte[] tag = mMp3File.getCustomTag();
            if (tag == null) {
                DebugUtil.i(TAG, "mMp3File.getCustomTag is null.");
                return;
            }
            String markTagString = new String(tag, Constants.UTF_8);
            int index = markTagString.indexOf(MarkSerializUtil.SPLIT_BETWEEN_MARK_AND_AMPLITUDE);
            if (index > 0) {
                mAmpStringFromMp3 = markTagString.substring(index + 1);
                mMarkStringFromMp3 = markTagString.substring(0, index);
            } else {
                mMarkStringFromMp3 = markTagString;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "extract mp3 file error", e);
            mAmpStringFromMp3 = null;
            mMarkStringFromMp3 = null;
        } finally {
            if (mMp3File != null) {
                mMp3File.release();
            }
        }
    }

    public void getAmpFromSoundFile() {
        try {
            mSoundFile = new SoundFile();
            mSoundFile.setDecodedSoundFileListener(mSoundDecodeFinish);
            mSoundFile.setWaitAmplitudes((int) WaveViewUtil.getOneLargeWaveItemAmplitueLineCount(mContext), mSoundDecodeReady);
            mSoundFile.setOneWaveLineTime(WaveViewUtil.getOneLargeWaveLineTime(mContext));
            if (BaseUtil.isAndroidQOrLater() && !mIsRecycle) {
                mSoundFile.create(FileUtils.getDisplayNameByPath(mPlayPath), mPlayUri, mContext);
            } else {
                mSoundFile.create(mPlayPath, null, mContext);
            }

        } catch (Exception e) {
            DebugUtil.e(TAG, "getAmpFromSoundFile error ", e);
        }
    }

    private String soundList2String(List<Integer> list) {
        StringBuilder ampString = new StringBuilder();
        if ((list != null) && !list.isEmpty()) {
            for (int i = 0; i < list.size(); i++) {
                ampString.append(list.get(i));
                if (i != list.size()) {
                    ampString.append(",");
                }
            }
        }
        return ampString.toString();
    }

    /**
     * 获取转换后的波形数据，来源是amp_file_path或者decode mp3File的customTag。
     */
    public WaveDataMap getAmpList() {
        String ampString = getAmpString();
        DebugUtil.d(TAG, "getAmpList(), ampString:" + ampString);
        WaveDataMap waveDataMap = jsonString2WaveDataMap(ampString);
        if (waveDataMap != null) {
            return waveDataMap;
        } else {
            DebugUtil.w(TAG,"getAmpList() failed to parse waveDataMap !");
            if (ampString != null && !ampString.isEmpty()) { // ampString from customTag
                List<Integer> ampList = AmpFileUtil.convertStringToInt(ampString);
                return new WaveDataMap(ampList);
            }
            return new WaveDataMap();
        }
    }

    /**
     * 获取波形数据，
     * 通过amp_file_path读取波形文件流数据。
     * 或者file的 customTag，获取波形数据
     */
    public String getAmpString() {
        String ampString = getAmpFromDB();
        if ((ampString != null) && !TextUtils.isEmpty(ampString)) {
            DebugUtil.d(TAG, "getAmpString getAmpFromDB");
            return ampString;
        }

        if (!mIsDecodeMp3) {
            decodeMp3();
        }
        ampString = mAmpStringFromMp3;
        if ((ampString != null) && !ampString.isEmpty()) {
            DebugUtil.d(TAG, "getAmpString getAmpFromMP3");
            return ampString;
        }

        return ampString;
    }

    public String getMarkString() {
        String markString = getOldMarkStringFromDB();
        if ((markString != null) && !TextUtils.isEmpty(markString)) {
            DebugUtil.d(TAG, "getMarkString getMarkFromDB");
            return markString;
        }

        if (!mIsDecodeMp3) {
            decodeMp3();
        }
        markString = mMarkStringFromMp3;
        if ((markString != null) && !TextUtils.isEmpty(markString)) {
            DebugUtil.d(TAG, "getMarkString getMarkFromMp3");
            return markString;
        }
        DebugUtil.d(TAG, "getMark is null ");
        return markString;
    }

    public List<MarkDataBean> getMergeMarkList() {
        List<MarkDataBean> oldMarkList = new ArrayList<>();
        String oldMarkString = getMarkString();
        if (oldMarkString != null) {
            oldMarkList = MarkSerializUtil.INSTANCE.parseMarkDataBeanListFromString(oldMarkString);
        }
        List<MarkDataBean> newMarkList = getNewMarkList();
        return MarkProcessUtil.mergeOldAndNewMarkList(oldMarkList, newMarkList);
    }

    private List<MarkDataBean> getNewMarkList() {
        List<MarkDataBean> result = new ArrayList<>();
        String keyId = RecorderDBUtil.getKeyIdByPath(mPlayPath);
        if (!TextUtils.isEmpty(keyId)) {
            result = PictureMarkDbUtils.INSTANCE.queryPictureMarks(keyId);
        }
        return result;
    }

    public List<Integer> string2Integer(List<String> list) {
        if ((list == null) || list.isEmpty()) {
            return new ArrayList<>();
        }
        List<Integer> stringList = new ArrayList<>(list.size());
        try {
            for (int i = 0; i < list.size(); i++) {
                stringList.add(Integer.valueOf(list.get(i)));
            }
        } catch (NumberFormatException e) {
            DebugUtil.d(TAG, e.toString());
        }
        return stringList;
    }

    public interface DecodeFinish {
        void decodeFinish(String ampString);
    }

    public void setDecodeFinish(DecodeFinish decodeFinish) {
        mDecodeFinish = decodeFinish;
        mSoundDecodeFinish = isFinish -> {
            DebugUtil.i(TAG, "decodedSoundFileFinish Callback " + isFinish);
            if ((mDecodeFinish != null) && (mSoundFile != null) && isFinish) {
                mDecodeFinish.decodeFinish(soundList2String(mSoundFile.getAmplitudeList()));
            }
            return false;
        };
        if (mSoundFile != null) {
            mSoundFile.setDecodedSoundFileListener(mSoundDecodeFinish);
        }
    }

    public interface DecodeReady {
        void onReady(String string, SoundFile soundFile);
    }

    public void setDecodeReady(DecodeReady decodeReady) {
        mDecodeReady = decodeReady;
        mSoundDecodeReady = () -> {
            DebugUtil.i(TAG, "onReady Callback");
            if ((mDecodeReady != null) && (mSoundFile != null)) {
                mDecodeReady.onReady(soundList2String(mSoundFile.getAmplitudeList()), mSoundFile);
            }
        };
        if (mSoundFile != null) {
            mSoundFile.setWaitAmplitudes((int) WaveViewUtil.getOneLargeWaveItemAmplitueLineCount(mContext), mSoundDecodeReady);
        }
    }

    public void release() {
        if (mSoundDecodeReady != null) {
            mSoundDecodeReady = null;
        }
        if (mSoundDecodeFinish != null) {
            mSoundDecodeFinish = null;
        }
        if (mProgressCallback != null) {
            mProgressCallback = null;
        }

        releaseSound();
        releaseMp3();
    }

    public void releaseSound() {
        if (mSoundFile != null) {
            mSoundFile.setStopDecoded(true);
            mSoundFile = null;
        }
    }

    public void releaseMp3() {
        if (mMp3File != null) {
            mMp3File.setCancleScanFlag(true);
            mIsDecodeMp3 = false;
            mMp3File = null;
        }
    }

    public static String getAmplitudeString(List<Integer> amplitudeList) {
        if (amplitudeList == null || amplitudeList.isEmpty()) {
            return Constants.EMPTY;
        }
        int size = amplitudeList.size();
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < size; i++) {
            Integer v = CollectionsKt.getOrNull(amplitudeList, i);
            if (v == null) {
                v = 0;
            }
            if (i == size - 1) {
                builder.append(v);
            } else {
                builder.append(v).append(SYMBOL_COMMA);
            }
        }
        return builder.toString();
    }

    /**
     * To generated amplitude file and update db
     *
     * @param context
     * @param path
     * @param uri
     * @param amplitudeList: old amplitude data. e.g:[1171, ...... 963]
     */
    public static void writeAmpData(Context context, String path, Uri uri, List<Integer> amplitudeList) {
        if (TextUtils.isEmpty(path)) {
            DebugUtil.i(TAG, "input name is empty, return");
            return;
        }
        if (FileUtils.checkFileNotExistOrFileSizeZero(path, uri)) {
            return;
        }
        String ampString = contactAmpListToString(amplitudeList);
        if (TextUtils.isEmpty(ampString)) {
            DebugUtil.i(TAG, "markString is empty , return");
            return;
        }
        Record record = RecorderDBUtil.getInstance(context).qureyRecordByPath(path);
        if (record != null) {
            boolean isUpdateSuccess = RecorderDBUtil.getInstance(context).updateRecordAmplitudeById(record.getId(), ampString);
            if (!isUpdateSuccess) {
                DebugUtil.d(TAG, "writeAmpData update record successful, record.getId():" + record.getId());
            }
        } else {
            DebugUtil.e(TAG, "writeAmpData no record found in recordDb, update error");
        }
    }

    /**
     * 将Amplitude List转换为标准段数的json字符串
     *
     * @param amplitudeList
     * @return
     * e.g:
     * [1171......963]
     * <p>
     * parse to ===》
     * {"full_amp":[1171......963],
     * "compress_96_amp":[1171, ...... 963],
     * "compress_48_amp":[1171, ......, 963]}
     */
    @NotNull
    private static String contactAmpListToString(List<Integer> amplitudeList) {
        return waveDataMap2JsonString(new WaveDataMap(amplitudeList).toString());
    }

    public static String decodeAmplitudeByUri(Uri uri) {
        Record recordFormMedia = MediaDBUtils.getRecordFromMediaByUriId(uri);
        if (recordFormMedia == null) {
            return "";
        }

        SoundFile soundFile = new SoundFile();
        soundFile.setOneWaveLineTime(Constants.WAVE_SAMPLE_INTERVAL_TIME);
        try {
            soundFile.create(recordFormMedia.getDisplayName(), uri, BaseApplication.getAppContext());
            return contactAmpListToString(soundFile.getAmplitudeList());
        } catch (IOException e) {
            DebugUtil.w(TAG, "decodeAmplitudeByUri error: " + e);
            return "";
        }
    }

    /**
     * Parse a JSON string
     * @param ampString: standard JSON object
     *  e.g:
     *  {"full_amp":[1171......963],
     * "compress_96_amp":[1171, ...... 963],
     * "compress_48_amp":[1171, ......, 963]}
     *
     * @return WaveDataMap
     */
    public static WaveDataMap jsonString2WaveDataMap(String ampString) {
        if (ampString == null || ampString.trim().isEmpty()) {
            DebugUtil.e(TAG, "jsonString2WaveDataMap ampString is null or empty !");
            return null;
        }
        try {
            JsonElement el = JsonParser.parseString(ampString);
            if (el.isJsonObject()) {
                return parseJsonStringToWaveDataMap(el.getAsJsonObject());
            } else {
                DebugUtil.e(TAG, "jsonString2WaveDataMap error, not a standard WaveDataMap json object !");
                return null;
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Parse a JSON element.
     *
     * @param waveDataMap: standard JSON object
     * @return standard json data
     *
     * e.g:
     *  WaveDataMap({full_amp=[1171,......963],
     *  compress_96_amp=[1171, ...... 963],
     *  compress_48_amp=[1171, ......, 963]})
     *
     *  parse to ===》
     *  {"full_amp":[1171......963],
     *  "compress_96_amp":[1171, ...... 963],
     *  "compress_48_amp":[1171, ......, 963]}
     */
    public static String waveDataMap2JsonString(String waveDataMap) {
        // To get the real JSON string, we need to find the content between the first { to the last } and parse them to JSON object.
        if (waveDataMap == null || waveDataMap.isEmpty()) {
            DebugUtil.e(TAG, "waveDataMap2JsonString error, waveDataMap is null or empty !");
            return "";
        }
        // 1. To get string between the first { to the last }
        int start = waveDataMap.indexOf('{');
        int end = waveDataMap.lastIndexOf('}');
        if (start == INVALID_INDEX || end == INVALID_INDEX || start > end) {
            DebugUtil.e(TAG, "waveDataMap2JsonString error, start=" + start + ", end=" + end);
            return "";
        }
        String jsonLike = waveDataMap.substring(start, end + 1);

        // 2. key with double quotation marks, "=" and replace with ":"
        jsonLike = jsonLike.replaceAll("(\\w+)=\\[", "\"$1\":[");
        jsonLike = jsonLike.replaceAll(",\\s*(\\w+)=\\[", ", \"$1\":[");
        return jsonLike;
    }

    /**
     * Parse a JSON object to WaveDataMap.
     *
     * @param waveDataJson: standard JSON object
     * @return WaveDataMap
     */
    public static WaveDataMap parseJsonStringToWaveDataMap(JsonObject waveDataJson) {
        WaveDataMap waveDataMap = new WaveDataMap();
        for (Map.Entry<String, JsonElement> entry : waveDataJson.entrySet()) {
            waveDataMap.put(entry.getKey(), parseJsonElementToList(entry.getValue()));
        }
        return waveDataMap;
    }

    /**
     * Parse a JSON element to List<Integer>.
     *
     * @param waveListElement :wave arrayList JSON element
     * @return List<Integer>
     */
    public static List<Integer> parseJsonElementToList(JsonElement waveListElement) {
        Type listType = new TypeToken<List<Integer>>() {
        }.getType();
        return new Gson().fromJson(waveListElement, listType);
    }

    /**
     * Get the amplitude string from mp3 file.
     *
     * @return mAmpStringFromMp3
     */
    public String getAmpStringFromMp3() {
        return mAmpStringFromMp3;
    }
}
