package com.soundrecorder.wavemark.mark.dialog

import android.app.Activity
import android.view.View
import com.soundrecorder.wavemark.R
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.dialog.AbsEditAlertDialog

class ReMarkNameAlertDialog(
    activity: Activity,
    private val markText: String,
    private val callback: (String) -> Unit
) : AbsEditAlertDialog(activity) {

    override fun onInitCustomView(customView: View) {
        getEditText()?.apply {
            hint = activity.getString(R.string.custom_mark_description)
            contentDescription = activity.getString(R.string.talkback_input_flag_name)
        }
    }

    override fun getTitleText() = R.string.rename_mark
    override fun onSave() {
        val newMarkText = getNewContent()
        if (newMarkText.isEmpty()) {
            ToastManager.showLongToast(activity, R.string.custom_mark_description)
            return
        }
        callback(newMarkText)
        dismiss()
    }

    override fun onCancel() {
        dismiss()
    }

    override fun getOriginalContent() = markText
}