/********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  WaveItemView
 * * Description :
 * * Version     : 1.0
 * * Date        : 2018/11/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
// OPLUS Java File Skip Rule:MethodLength,MethodComplexity
package com.soundrecorder.wavemark.wave.view;

import static com.soundrecorder.wavemark.wave.WaveViewUtil.FLOAT_1;
import static com.soundrecorder.wavemark.wave.WaveViewUtil.NUM_TWO;
import static com.soundrecorder.wavemark.wave.view.DrawPlayAmpExtKt.drawNormalPlayAmp;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.os.Build;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.Choreographer;
import android.view.View;
import android.view.ViewParent;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.NightModeUtil;
import com.soundrecorder.base.utils.NumberConstant;
import com.soundrecorder.base.utils.ScreenUtil;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.databean.DirectRecordTime;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.utils.FontUtils;
import com.soundrecorder.common.utils.ViewUtils;
import com.soundrecorder.imageload.ILoadImageResultListener;
import com.soundrecorder.wavemark.R;
import com.soundrecorder.wavemark.wave.WaveViewUtil;
import com.soundrecorder.wavemark.wave.WaveViewUtil.WaveType;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

public class WaveItemView extends View {

    public static final int TIME_GAP = 1000;

    private static final String TAG = "WaveItemView";
    private static final int MAX_SCALE = 2;
    private static final int MID_SCALE = 3;
    private static final int MIN_SCALE = 1;
    private static final int SECOND_TIMETEXT_INDEX = 4;
    private static final int FAKE_AMPLITUDE = 500;
    private static final float DP_VALUE_ONE_HALF = 0.5f;
    private static final float DP_VALUE_2 = 2f;
    private static final float MAX_SCALE_HEIGHT = 0.35f;
    private static final float MID_SCALE_HEIGHT = 0.25f;
    private static final float MIN_SCALE_HEIGHT = 0.1f;
    /* 30%透明度标记线*/
    private static final int ALPHA_30_LINE_MARK = (int) (255 * 0.3);
    private static final int ALPHA_16_PERCENT = (int) (255 * 0.16);
    private static final int ALPHA_26_PERCENT = (int) (255 * 0.26);

    private static final int ALPHA_15_AMPLITUDE_BG = (int) (255 * 0.08);

    protected Paint mAmplitudePaint;
    protected Paint mEnhanceAmplitudePaint;
    protected Paint mTimeLinesPaint;
    protected Paint mTimeTextPaint;
    protected Paint mBookmarksPaint;
    protected Paint mBordPaint;
    protected Paint mHorizontalDashPaint;
    protected Paint mBooklinesPaint;
    protected Paint mNewAddBooklinesPaint;

    protected Paint mBackgroundPaint;

    protected float mViewHeight;
    protected int mTimeLineGap;
    protected int mViewIndex = 0;
    protected int mMeterBlockGap;
    protected int mTotalCount;
    protected long mTotalTime = 0;
    protected float mPxPerMs;
    protected float mVirtualAmpGap = 0;

    protected float mScreenWidth = 0;
    protected float mCenterLineX = 0;
    protected float mEndItemWidth = 0;
    protected float mAmplitudeWidth = 4;
    protected float mDirectAmpWidth = 4;
    protected int mWaveDirectColor = 0;
    /* 水平虚线的高度*/
    protected float mDottedLineHeight = 2;
    /* 顶部标记区域的高度*/
    protected int mMarkViewHeight;
    //波形图尺寸类型
    protected WaveType mWaveType = WaveType.LARGE;
    //最近一次开启定向录音的时间
    protected long mLastDirectOnTime = 0;

    protected boolean mNeedInvalidate = false;
    //是否显示时间轴线
    private boolean mNeedShowTimeLine = true;

    private long mWaveRulerLineHeight = 0;
    private List<Integer> mAmplitudes;
    /* soundFile硬解码出来的，绘制波形时有特殊处理逻辑*/
    private List<Integer> mDecodedAmplitudeList;
    private ArrayList<MarkDataBean> mMarkTimeList;
    private int mMinHeight;
    private int mWaveNormalColor = 0;
    private int mWaveUnPlayColor = 0;

    private int mTimeScaleColor = 0;
    private boolean mNightMode = false;
    private int mMaxWaveHeight = 0;
    private float mMaxWaveHeightRadio = 0; // 波形最大高度占波形区域高度的百分占比
    private MarkOnClickListener mMarkOnClickListener;
    private BookmarkAnimator mBookmarkAnimator;

    /*定向录音是否开启*/
    private boolean mIsEnhance = false;

    private String mDirectTime;
    private List<DirectRecordTime> mDirectTimeList;
    private int mWaveDirectBgColor = 0;
    private long mDuration;
    //缓存当前WaveItemView里波形柱的绘制起点
    private CopyOnWriteArrayList<Float> mCachedAmpStartXList;
    private Choreographer.VsyncCallback mVsyncCallback;

    public WaveItemView(Context context) {
        this(context, null);
    }

    public WaveItemView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public WaveItemView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        TypedArray array = context.obtainStyledAttributes(attrs, R.styleable.WaveItemView);
        try {
            initTypeAttr(context, array);
        } catch (Exception e) {
            DebugUtil.e(TAG, "onCreate error", e);
        } finally {
            array.recycle();
        }
        initBasicInfo(context);
        initPaint(context);
    }

    protected void initTypeAttr(Context context, TypedArray array) {
        mMaxWaveHeightRadio = array.getFloat(R.styleable.WaveItemView_item_max_wave_radio, NumberConstant.NUM_1);
    }

    protected void initBasicInfo(Context context) {
        Resources r = context.getResources();
        mMeterBlockGap = r.getDimensionPixelSize(R.dimen.wave_line_gap);
        if (mMeterBlockGap == 0) {
            mMeterBlockGap = 1;
        }
        mMinHeight = r.getDimensionPixelSize(R.dimen.wave_line_min_height);
        mWaveRulerLineHeight = r.getDimensionPixelOffset(R.dimen.wave_time_line_height);
        int resId = mWaveType == WaveViewUtil.WaveType.LARGE ? R.dimen.bold_wave_one_amplitude_width : R.dimen.wave_one_amplitude_width;
        mAmplitudeWidth = r.getDimensionPixelOffset(resId);
        mDirectAmpWidth = r.getDimensionPixelOffset(resId);
        mDottedLineHeight = r.getDimensionPixelOffset(com.soundrecorder.common.R.dimen.dp1);
        mMarkViewHeight = isSmallWaveType() ? 0 : r.getDimensionPixelOffset(R.dimen.record_wave_view_mark_area_height);

        //mVirtualAmpGap is the float value which is for 70ms in x-axis , which is used for drawAmplitude to evaluate the current x
        mVirtualAmpGap = WaveViewUtil.getAmplitueLineGapByWaveType(context, mWaveType);
        mTimeLineGap = (int) WaveViewUtil.getTimeLineGapByWaveType(context, mWaveType);
        mNightMode = NightModeUtil.isNightMode(context);
        mPxPerMs = (float) mTimeLineGap / WaveViewUtil.DURATION_INTERVAL;
        mScreenWidth = ScreenUtil.getRealScreenWidth();
        mCenterLineX = isSmallWaveType() ? getWidth() * FLOAT_1 / NUM_TWO : mScreenWidth * FLOAT_1 / NUM_TWO;
    }

    /**
     * 获取最近一次开启定向录音的时间
     */
    public long getLastDirectOnTime() {
        return mLastDirectOnTime;
    }

    /**
     * 设置最近一次启定向录音的时间
     *
     * @param lastDirectOnTime 定向录音开始时间
     */
    public void setLastDirectOnTime(long lastDirectOnTime) {
        mLastDirectOnTime = lastDirectOnTime;
    }

    public void setNeedShowTimeLine(boolean bShowTimeLine) {
        mNeedShowTimeLine = bShowTimeLine;
    }

    public boolean isNeedShowTimeLine() {
        return mNeedShowTimeLine;
    }

    public void setWaveType(WaveViewUtil.WaveType waveType) {
        if (mWaveType != waveType) {
            mWaveType = waveType;
            //再初始化一遍基础参数
            initBasicInfo(getContext());
        }
    }

    /**
     * 定向录音波形粗细变化
     *
     * @param isEnhance
     */
    public void setLineEnhanceRecording(boolean isEnhance) {
        this.mIsEnhance = isEnhance;
        int resId = mWaveType == WaveViewUtil.WaveType.LARGE ? R.dimen.bold_wave_one_amplitude_width : R.dimen.wave_one_amplitude_width;
        mAmplitudeWidth = getContext().getResources().getDimensionPixelOffset(resId);
        mDirectAmpWidth = getContext().getResources().getDimensionPixelOffset(resId);
    }

    public void setPlayLineEnhanceRecording(boolean isEnhance) {
        this.mIsEnhance = isEnhance;
        mAmplitudeWidth = getContext().getResources().getDimensionPixelOffset(R.dimen.wave_one_amplitude_width);
        mDirectAmpWidth = getContext().getResources().getDimensionPixelOffset(R.dimen.bold_wave_one_amplitude_width);
    }

    public CopyOnWriteArrayList<Float> getCachedAmpStartXList() {
        if (mCachedAmpStartXList == null) {
            mCachedAmpStartXList  = new CopyOnWriteArrayList<>();
        }
        return mCachedAmpStartXList;
    }

    public void cacheAmpStartX(float startX) {
        getCachedAmpStartXList().add(startX);
    }

    public boolean isEnhance() {
        return mIsEnhance;
    }

    public void setDirectTime(String directTime) {
        this.mDirectTime = directTime;
        addDirectRecordTimeList();
    }

    public String getDirectTime() {
        return mDirectTime;
    }

    public List<DirectRecordTime> getDirectTimeList() {
        return mDirectTimeList;
    }

    public void setDirectTimeList(List<DirectRecordTime> mDirectTimeList) {
        this.mDirectTimeList = mDirectTimeList;
    }

    private void addDirectRecordTimeList() {
        if (!TextUtils.isEmpty(mDirectTime)) {
            mDirectTimeList = new ArrayList<>();
            String[] listTime = mDirectTime.split(",");
            if (listTime != null) {
                DirectRecordTime recordTime = null;
                for (String itemTime : listTime) {
                    String[] arrays = itemTime.split("-");
                    if (arrays.length > 1) {
                        long startTime = Long.parseLong(arrays[0]);
                        long endTime = Long.parseLong(arrays[1]);
                        recordTime = new DirectRecordTime(startTime, endTime);
                        mDirectTimeList.add(recordTime);
                    }
                }
                DebugUtil.d(TAG, "addDirectRecordTimeList:" + mDirectTimeList);
            }
        }
    }

    public void setDuration(long mDuration) {
        this.mDuration = mDuration;
    }

    public long getDuration() {
        return mDuration;
    }

    public boolean isNightMode() {
        return mNightMode;
    }

    public void setNightMode(boolean mNightMode) {
        this.mNightMode = mNightMode;
    }

    protected boolean isReverseLayout() {
        ViewParent parent = getParent();
        if (parent instanceof RecyclerView) {
            RecyclerView.LayoutManager layoutManager = ((RecyclerView) parent).getLayoutManager();
            if (layoutManager instanceof LinearLayoutManager) {
                return ((LinearLayoutManager) layoutManager).getReverseLayout();
            }
        }
        return false;
    }

    private float caculateLineScaleValue(Context context) {
        float px500s = WaveViewUtil.getTimeLineGapByWaveType(context, mWaveType);
        float ampGap = px500s * Constants.WAVE_SAMPLE_INTERVAL_TIME / WaveViewUtil.DURATION_INTERVAL;
        float result = ampGap / mMeterBlockGap;
        //caculate the linescale value, linescale value like 3.5 in 19065 Qhdi, linescale value like 2.75 in 19065 Fhdi
        DebugUtil.i(TAG, "caculatLineScaleValue: ampGap " + ampGap + ", result: " + result);
        return result;
    }

    public void setEndItemWidth(float mEndItemWidth) {
        this.mEndItemWidth = mEndItemWidth;
    }

    protected void initPaint(Context context) {
        //时间刻度
        mTimeLinesPaint = new Paint();
        mTimeLinesPaint.setColor(context.getColor(R.color.wave_time_line_color));
        mTimeLinesPaint.setStrokeWidth(getResources().getDimensionPixelOffset(R.dimen.wave_time_ruler_width));
        mTimeLinesPaint.setStyle(Paint.Style.STROKE);

        //时间文字
        mTimeScaleColor = context.getColor(R.color.wave_scale_item_color);
        mTimeTextPaint = new Paint();
        mTimeTextPaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mTimeTextPaint.setColor(mTimeScaleColor);
        mTimeTextPaint.setTextSize(context.getResources().getDimensionPixelSize(R.dimen.wave_time_text_size));
        mTimeTextPaint.setStrokeWidth(NumberConstant.NUM_1);
        mTimeTextPaint.setAntiAlias(true);
        mTimeTextPaint.setTextAlign(Paint.Align.CENTER);
        mTimeTextPaint.setTypeface(FontUtils.getNewSysSansEnNormal());
        mTimeTextPaint.setFontFeatureSettings("tnum");

        //波形paint
        mWaveNormalColor = context.getColor(R.color.wave_normal_item_color);
        mWaveUnPlayColor = context.getColor(R.color.wave_normal_item_color_undraw);
        mWaveDirectColor = context.getColor(R.color.wave_direct_amplitude_color);

        //普通音柱画笔
        mAmplitudePaint = new Paint();
        mAmplitudePaint.setColor(mWaveNormalColor);
        mAmplitudePaint.setAntiAlias(true);
        mAmplitudePaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mAmplitudePaint.setStrokeCap(Paint.Cap.ROUND);
        //定向录音音柱画笔
        mEnhanceAmplitudePaint = new Paint();
        mEnhanceAmplitudePaint.setColor(mWaveDirectColor);
        mEnhanceAmplitudePaint.setAntiAlias(true);
        mEnhanceAmplitudePaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mEnhanceAmplitudePaint.setStrokeCap(Paint.Cap.ROUND);

        //普通标记paint
        mBookmarksPaint = new Paint();
        mBookmarksPaint.setAntiAlias(true);

        //标记竖线
        mBooklinesPaint = new Paint();
        mBooklinesPaint.setAntiAlias(true);
        mBooklinesPaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mBooklinesPaint.setStrokeCap(Paint.Cap.ROUND);
        mBooklinesPaint.setColor(ContextCompat.getColor(BaseApplication.getAppContext(), com.support.appcompat.R.color.coui_color_label_theme_blue));
        mBooklinesPaint.setAlpha(ALPHA_30_LINE_MARK);

        //新增标记时 标记竖线
        mNewAddBooklinesPaint = new Paint();
        mNewAddBooklinesPaint.setAntiAlias(true);
        mNewAddBooklinesPaint.setStyle(Paint.Style.FILL_AND_STROKE);
        mNewAddBooklinesPaint.setStrokeCap(Paint.Cap.ROUND);
        mNewAddBooklinesPaint.setColor(ContextCompat.getColor(BaseApplication.getAppContext(),
                com.support.appcompat.R.color.coui_color_label_theme_blue));

        //图片标记边框paint
        mBordPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.FILTER_BITMAP_FLAG);
        mBordPaint.setColor(ContextCompat.getColor(BaseApplication.getAppContext(), R.color.picture_mark_icon_bord_color));
        mBordPaint.setAntiAlias(true);
        mBordPaint.setStrokeWidth(ViewUtils.dp2px(DP_VALUE_ONE_HALF, true));
        mBordPaint.setStyle(Paint.Style.STROKE);

        //水平虚线paint
        mHorizontalDashPaint = new Paint();
        mHorizontalDashPaint.setStrokeCap(Paint.Cap.ROUND);
        mHorizontalDashPaint.setStyle(Paint.Style.FILL);
        mHorizontalDashPaint.setColor(mWaveUnPlayColor);
        mHorizontalDashPaint.setStrokeWidth(mAmplitudeWidth);

        mBookmarkAnimator = new BookmarkAnimator(this);

        //定向录音背景色
        mWaveDirectBgColor = context.getColor(R.color.wave_direct_amplitude_bg_color);
        mBackgroundPaint = new Paint();
        mBackgroundPaint.setColor(mWaveDirectBgColor);
        mBackgroundPaint.setAntiAlias(true);
        mBackgroundPaint.setStyle(Paint.Style.FILL);
//        mBackgroundPaint.setAlpha(ALPHA_15_AMPLITUDE_BG);
    }

    public void updatePaintColor() {
        if ((mTimeTextPaint != null) && (mAmplitudePaint != null)) {
            mWaveNormalColor = getContext().getColor(R.color.wave_normal_item_color);
            mWaveUnPlayColor = getContext().getColor(R.color.wave_normal_item_color_undraw);
            mTimeScaleColor = getContext().getColor(R.color.wave_scale_item_color);
            mWaveDirectBgColor = getContext().getColor(R.color.wave_direct_amplitude_bg_color);

            DebugUtil.v(TAG, "------------nightMode:" + mNightMode);
            mTimeTextPaint.setColor(mTimeScaleColor);
            mAmplitudePaint.setColor(mWaveNormalColor);

            mHorizontalDashPaint.setColor(mWaveUnPlayColor);
            mBackgroundPaint.setColor(mWaveDirectBgColor);
            postInvalidate();
        }
    }

    public void setTotalCount(int count) {
        mTotalCount = count;
    }

    /**
     * setCurViewIndex比较在setMarkTimeList之前
     */
    public void setMarkTimeList(ArrayList<MarkDataBean> markTimeList) {
        ArrayList<MarkDataBean> marks = new ArrayList<>();
        long startTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION; //ms
        long endTime = mViewIndex * WaveViewUtil.ONE_WAVE_VIEW_DURATION; //ms
        markTimeList.forEach(dataBean -> {
            long time = dataBean.getCorrectTime();
            if ((time >= startTime) && (time < endTime)) {
                dataBean.setOnSuccess(new LoadImageListener(this));
                marks.add(dataBean);
            }
        });
        mMarkTimeList = marks;

    }

    public List<MarkDataBean> getMarkTimeList() {
        if (mMarkTimeList == null) {
            return new ArrayList<>();
        }
        return mMarkTimeList.stream().filter(mark -> !TextUtils.isEmpty(mark.getPictureFilePath())).collect(Collectors.toList());

    }

    public void setDecodedAmplitudeList(List<Integer> decodedAmplitudeList) {
        mDecodedAmplitudeList = decodedAmplitudeList;
    }

    public List<Integer> getDecodedAmplitudeList() {
        return mDecodedAmplitudeList;
    }

    public List<Integer> getAmplitudes() {
        return mAmplitudes;
    }

    public void setAmplitudes(List<Integer> mAmplitudes) {
        this.mAmplitudes = mAmplitudes;
    }

    public void setTotalTime(long time) {
        mTotalTime = time;
    }

    public void updateScreenWidth(int screenWidth) {
        mScreenWidth = screenWidth;
        mCenterLineX = screenWidth / DP_VALUE_2;
    }

    public void setEventOnClick(MarkDataBean markDataBean) {
        if (mMarkOnClickListener != null) {
            mMarkOnClickListener.onClick(markDataBean);
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int height = getMeasuredHeight();
        if (mViewHeight != height) {
            mViewHeight = height;
            /*height包含了波形上方时间刻度小旗帜的高度，占比是针对波形区域的，所以需要减去上面部分*/
            mMaxWaveHeight = (int) ((mViewHeight - mMarkViewHeight) * mMaxWaveHeightRadio);
            DebugUtil.i(TAG, "mViewHeight=" + mViewHeight + ",mMaxWaveHeight=" + mMaxWaveHeight);
        }
    }

    @Override
    public void onDraw(Canvas canvas) {
        //绘制刻度尺和时间文字
        drawRuler(canvas);
        //绘制录制/播放波形
        drawAmplitude(canvas);
        //绘制标记
        drawBookmark(canvas);
    }

    private float getAllXByTime(Long time) {
        return time * mPxPerMs;
    }

    /**
     * draw time scale ruler
     * 绘制时间刻度和时间文字
     * 一个item为6s，0.25s绘制一个刻度，每1s绘制一个text
     * 每1s的时间刻度高度为4，其他时间刻度高度为2.
     */
    protected void drawRuler(Canvas canvas) {
        //不需要绘制时间轴，则直接跳过
        if (!isNeedShowTimeLine()) {
            return;
        }
        float itemWidth = mTimeLineGap;
        float rightX = 0f;
        float startX = 0f;
        int viewWidth = getWidth();
        if (mViewIndex != 0) { // first view should not draw time text.
            startX = 0;
            int time = 0;
            String timeBySecond = null;
            float timeStrWidth = 0f;
            //一个item是6s，totalIndexCnt = 12
            int totalIndexCnt = (int) Math.ceil((double) viewWidth / (WaveViewUtil.DURATION_INTERVAL * mPxPerMs));
            for (int index = 0; index <= totalIndexCnt; index++) {
                rightX = startX + itemWidth * index;
                /*
                 * 因为前一个item的最后一个刻度就是下一个item的第一个刻度，所以会绘制2次，计算x坐标有细微的误差，
                 * 就会导致每6s的刻度颜色变深，字体看起来更粗，所以每个item 都不绘制最后一个刻度。
                 */
                if (index != totalIndexCnt) {
                    //每1s绘制时间text,就是每隔2个大刻度,不用绘制最后一个时间text，因为下一个item的第一个text跟上一个的最后一个text重合，会导致当前text颜色变深，字体变粗。
                    if (index % NUM_TWO == 0) {
                        //draw text
                        if (index == 0) {
                            time = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION_SECOND;
                        } else {
                            time += WaveViewUtil.NUM_ONE;
                        }
                        timeBySecond = WaveViewUtil.getStringBySecond(time);
                        float newStartX = rightX;
                        if (isReverseLayout()) {
                            newStartX = viewWidth - rightX;
                        }
                        canvas.drawText(
                                timeBySecond,
                                newStartX,
                                mMarkViewHeight - mWaveRulerLineHeight - mTimeTextPaint.getFontMetricsInt().bottom,
                                mTimeTextPaint
                        );
                    }
                    //绘制刻度线，totalIndexCnt = 12 ，绘制12次，6s绘制12次相当于0.5s绘制一次。
                    if (index % NUM_TWO == 0) {
                        float newStartX = rightX;
                        if (isReverseLayout()) {
                            newStartX = viewWidth - rightX;
                        }
                        //每1s大刻度高度为8
                        mTimeLinesPaint.setAlpha(ALPHA_26_PERCENT);
                        canvas.drawLine(newStartX, (mMarkViewHeight - mWaveRulerLineHeight), newStartX, mMarkViewHeight, mTimeLinesPaint);
                    } else {
                        //小刻度高度为4, 但是原有设计是每0.5s绘制一个小刻度； 新设计要求每0.25s绘制一个，所以这里要额外在0.5s刻度前后再画两条等分刻度
                        float newStartX = rightX;
                        if (isReverseLayout()) {
                            newStartX = viewWidth - rightX;
                        }
                        float halfItemWidth = itemWidth / 2f;
                        float halfRulerLineHeight = mWaveRulerLineHeight / 2f;
                        mTimeLinesPaint.setAlpha(ALPHA_16_PERCENT);
                        canvas.drawLine(newStartX - halfItemWidth, (mMarkViewHeight - halfRulerLineHeight),
                                newStartX - halfItemWidth, mMarkViewHeight, mTimeLinesPaint);
                        canvas.drawLine(newStartX, (mMarkViewHeight - halfRulerLineHeight), newStartX, mMarkViewHeight, mTimeLinesPaint);
                        canvas.drawLine(newStartX + halfItemWidth, (mMarkViewHeight - halfRulerLineHeight),
                                newStartX + halfItemWidth, mMarkViewHeight, mTimeLinesPaint);
                    }
                }
            }
        }
    }

    protected int calculateAmpStartIndex() {
        if (mViewIndex <= 1) {
            return 0;
        } else {
            float ampCountF = (mViewIndex - 1) * WaveViewUtil.getOneWaveItemWidthByWaveType(getContext(), mWaveType) / mVirtualAmpGap;
            return (int) Math.ceil(ampCountF);
        }
    }

    protected float calculateAmpStartX(long startAmpIndex) {
        if (mViewIndex <= 1) {
            return 0;
        } else {
            long newStartAmpIndex = startAmpIndex;
            if (startAmpIndex == -1) {
                newStartAmpIndex = calculateAmpStartIndex();
            }
            float expectX = getAllXByTime(newStartAmpIndex * Constants.WAVE_SAMPLE_INTERVAL_TIME);
            float startX = expectX - (mViewIndex - 1) * WaveViewUtil.getOneWaveItemWidthByWaveType(getContext(), mWaveType);
            //DebugUtil.d(TAG,"交界处：startX =" + startX);
            if (Math.ceil(startX) > mVirtualAmpGap) {
                return 0;
            }
            return (int) Math.ceil(startX);
        }
    }

    /**
     * draw amplitude wave lines.In order to be compatible with old data, one amplitude draws two or three lines.
     * 二：波形界面，波形可以滑动，中心线左右两侧都是波形数据，但是paint颜色不一样，区分已播放和未播放。
     * 1.播放界面波形绘制没有动画，一次性绘制完成单个波形。
     * 2.播放界面波形滑动的过程中，会动态刷新波形，使左右两边波形颜色不一样。
     * 3.播放界面拖动seekBar导致波形移动，会动态刷新波形，使左右两边波形颜色不一样。
     * 4.播放界面的数据有两种来源
     * 5.播放界面假波形的高度都是一致的，看起来是很长的一段直线。
     */
    protected void drawAmplitude(Canvas canvas) {
        if (mViewIndex == 0) {
            drawFirstItemAmplitude(canvas);
            return;
        }

        drawPlayAmplitude(canvas);
    }

    /**
     * 绘制第一个item的水平虚线
     * 宽度为屏幕的一半，绘制高度为2的波形虚线
     */
    protected void drawFirstItemAmplitude(Canvas canvas) {
        float currentX = 0;
        int waveStartY = 0;
        int waveEndY = 0;
        int viewWidth = getWidth();

        while (currentX <= viewWidth) {
            if (currentX + mAmplitudeWidth > mCenterLineX) {
                break;
            }
            waveStartY = getStartYByHeight(mDottedLineHeight);
            waveEndY = getEndYByHeight(mDottedLineHeight);
            canvas.drawLine(currentX, waveStartY, currentX, waveEndY, mHorizontalDashPaint);
            currentX += mVirtualAmpGap;
        }
    }

    /**
     * 当未获取到数据时是否需要绘制默认虚线
     *
     * @return
     */
    protected boolean drawDottedLineWhenNoData() {
        return false;
    }

    /**
     * 绘制播放类型的波形，非第一个item
     * 播放波形没有动效
     *
     * @param canvas
     */
    private void drawPlayAmplitude(Canvas canvas) {
        float currentX = 0;
        int amplitueIndex = 0;
        int amplitueVaule = 0;
        int preAmplitueVaule = 0;
        int waveStartY = 0;
        int waveEndY = 0;
        float lineHeight = 0;
        boolean isRTL = isReverseLayout();

        //波形实际上是画从第二个item开始绘制（index = 1）
        amplitueIndex = calculateAmpStartIndex();
        // startX is start draw postion on x orientation of the first item in mRecordAmplitudeList or mPlayAmplitudes.
        currentX = calculateAmpStartX(amplitueIndex);
        //清理已缓存的波形音柱开始绘制的位置
        getCachedAmpStartXList().clear();

        int viewWidth = getWidth();
        Integer ampValue = 0;
        while (currentX <= viewWidth) {
            if ((mAmplitudes != null) && (mAmplitudes.size() > 0)) { // the amplitude is from mp3 book mark
                if (amplitueIndex < mAmplitudes.size()) {
                    if (amplitueIndex - 1 >= 0) {
                        ampValue = mAmplitudes.get(amplitueIndex - 1);
                        preAmplitueVaule = (ampValue == null) ? 0 : ampValue;
                    } else {
                        preAmplitueVaule = 0;
                    }
                    ampValue = mAmplitudes.get(amplitueIndex);
                    amplitueVaule = (ampValue == null) ? 0 : ampValue;
                    amplitueIndex++;
                } else {
                    drawDottedLine(canvas, isRTL, currentX, viewWidth);
                    currentX += mVirtualAmpGap;
                    continue;
                }
            } else { // the amplitude is from decoding sound files
                if ((mDecodedAmplitudeList != null) && (mDecodedAmplitudeList.size() > 0)) {
                    if (amplitueIndex < mDecodedAmplitudeList.size()) {
                        if (amplitueIndex - 1 >= 0) {
                            ampValue = mDecodedAmplitudeList.get(amplitueIndex - 1);
                            preAmplitueVaule = (ampValue == null) ? 0 : ampValue;
                        } else {
                            preAmplitueVaule = 0;
                        }
                        ampValue = mDecodedAmplitudeList.get(amplitueIndex);
                        amplitueVaule = (ampValue == null) ? 0 : ampValue;
                        amplitueIndex++;
                    } else {
                        boolean usedFakeAmplitude = false;
                        float oneWaveLineTime = WaveViewUtil.getOneWaveLineTimeByWaveType(getContext(), mWaveType);
                        boolean decodeIsBeforeTotal = oneWaveLineTime * mDecodedAmplitudeList.size() < mTotalTime;
                        boolean nowIsBeforeTotal = oneWaveLineTime * amplitueIndex < mTotalTime;
                        if (decodeIsBeforeTotal && nowIsBeforeTotal) {
                            //In this case, we temporary use fake amplitude, for bug 1646242
                            DebugUtil.d(TAG, "In this case, we temporary use fake amplitude, amp : "
                                    + ", mPlayTotalTime: " + mTotalTime + ", mDecodedAmplitudeList size : "
                                    + mDecodedAmplitudeList.size());
                            usedFakeAmplitude = true;
                            amplitueVaule = (int) (FAKE_AMPLITUDE * Math.random());
                            preAmplitueVaule = (int) (FAKE_AMPLITUDE * Math.random());
//                            ampPaintWith = mMeterBlockGap;

                            if (mViewIndex == mTotalCount - 1) {
                                amplitueIndex++;
                            }
                        }
                        if (!usedFakeAmplitude) {
                            drawDottedLine(canvas, isRTL, currentX, viewWidth);
                            currentX += mVirtualAmpGap;
                            continue;
                        }
                    }
                } else {
                    if (!drawDottedLineWhenNoData()) {
                        DebugUtil.d(TAG, "---drawAmplitude no waveform data---");
                        return;
                    }
                    waveStartY = getStartYByHeight(mDottedLineHeight);
                    waveEndY = getEndYByHeight(mDottedLineHeight);
                    canvas.drawLine(currentX, waveStartY, currentX, waveEndY, mHorizontalDashPaint);
                    currentX += mVirtualAmpGap;
                    continue;
                }
            }
            //步骤二：计算波形的上下左右位置的坐标，准备绘制波形矩形
            lineHeight = getWaveLineHeight(preAmplitueVaule, amplitueVaule);
            //步骤三、步骤四
            currentX = drawNormalPlayAmp(this, canvas, currentX, lineHeight);
//            currentX = drawPlayAmplitudeForCurrentX(this, canvas, currentX, lineHeight);
        }
    }

    protected float getXByTime(Long time) {
        return (time - (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION) * mPxPerMs;
    }

    /**
     * 绘制水平虚线
     */
    protected void drawDottedLine(Canvas canvas, boolean isRtl, float currentX, int viewWidth) {
        int waveStartY = getStartYByHeight(mDottedLineHeight);
        int waveEndY = getEndYByHeight(mDottedLineHeight);
        float newCurrentX = currentX;
        if (isRtl) {
            newCurrentX = viewWidth - currentX;
        }
        canvas.drawLine(newCurrentX, waveStartY, newCurrentX, waveEndY, mHorizontalDashPaint);
    }

    /**
     * 从已绘制的音柱列表中，获取标记时间最靠近的音柱起始X 值
     * @param markTime 标记的时间值
     */
    protected float getMarkClosestAmpX(long markTime) {
        long startTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION; //ms
        long markDeltaTime = markTime - startTime;
        long ampIndex = markDeltaTime / Constants.WAVE_SAMPLE_INTERVAL_TIME;
        return getCachedAmpStartXList().get((int) ampIndex);
    }

    protected void drawBookmark(Canvas canvas) {
        if ((mMarkTimeList == null) || mMarkTimeList.isEmpty()) {
            //删除为空时要调用一次动画
            WaveRecyclerView parent = (WaveRecyclerView) getParent();
            if ((parent != null) && (parent.getRemoveMarkData() != null)) {
                long startTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION; //ms
                mBookmarkAnimator.drawRemoveAnimIfNeed(canvas, startTime, isReverseLayout(), mMarkViewHeight, mViewHeight);
            }
            return;
        }
        if ((mViewIndex == 0)) {
            return;
        }
        long startTime = (mViewIndex - 1) * WaveViewUtil.ONE_WAVE_VIEW_DURATION; //ms
        try {
            for (MarkDataBean mark : mMarkTimeList) {
                //绘制标记的初始位置 从已绘制的波形中，获取跟当前标记最靠近的波形柱起始位置
                long markTime = mark.getCorrectTime();
                //获取当前波形中，最接近markTime的波形音柱
                float ampStartX = getMarkClosestAmpX(markTime);
                mark.checkIfNeedUpdatePaintAlpha(this);
                Paint bookLinePaint = mBooklinesPaint;
                //标记的添加时间与当前时间比较，小于1s则表示时刚新增的标记
                boolean bNewAddMark = mark.isNewMark();
                if (bNewAddMark) {
                    mNewAddBooklinesPaint.setAlpha(mark.getColorAlpha());
                    bookLinePaint = mNewAddBooklinesPaint;
                }
                float drawEndX = isReverseLayout() ? ampStartX - mAmplitudeWidth : ampStartX + mAmplitudeWidth;
                DebugUtil.i(TAG, "drawBookmark, drawStartX = " + ampStartX + ",drawEndX=" + drawEndX + ",mViewIndex=" + mViewIndex);
                canvas.drawRoundRect(ampStartX, mMarkViewHeight, drawEndX, mViewHeight, mAmplitudeWidth, mAmplitudeWidth, bookLinePaint);
                mBookmarkAnimator.drawRemoveAnimIfNeed(canvas, startTime, isReverseLayout(), mMarkViewHeight, mViewHeight);
            }
        } catch (Exception e) {
            DebugUtil.i(TAG, "drawBookmark, mark bitmap decode fail");
        }
    }

    public Paint getBookmarksPaint() {
        return mBookmarksPaint;
    }

    public float getPxPerMs() {
        return mPxPerMs;
    }

    /**
     * 根据传递的上一个波形和当前波形，计算出一个相对差距较小的波形值，使整体波形看起来更平滑
     *
     * @param preValue 上一个波形的时间高度
     * @param curValue 当前波形的时间高度
     * @return 重新计算后的当前位置波形高度
     */
    protected float getWaveLineHeight(int preValue, int curValue) {
        int value = (curValue + preValue) / WaveViewUtil.NUM_TWO;
        float dScale = value * 1f / WaveViewUtil.MAX_AMPLITUDE;
        if (dScale > 1f) {
            dScale = 1f;
        } else if (dScale < 0f) {
            dScale = 0f;
        }
        float height = mMaxWaveHeight * dScale;
        if (Float.compare(height, mMaxWaveHeight * MAX_SCALE_HEIGHT) < 0) {
            if (Float.compare(height, mMaxWaveHeight * MIN_SCALE_HEIGHT) < 0) {
                height = height * MAX_SCALE;
            } else if (Float.compare(height, mMaxWaveHeight * MID_SCALE_HEIGHT) < 0) {
                height = height * MID_SCALE;
            } else {
                height = height * MIN_SCALE;
            }
        }
        if (Float.compare(height, mMaxWaveHeight * MIN_SCALE_HEIGHT) <= 0) {
            // if volume is low but not 0, make sure the height is higher than the minHeight
            height += mMinHeight;
        }
        return height;
    }

    protected int getStartYByHeight(float lineHeight) {
        return (int) (mViewHeight / WaveViewUtil.NUM_TWO - lineHeight / WaveViewUtil.NUM_TWO + mMarkViewHeight / WaveViewUtil.NUM_TWO);
    }

    protected int getEndYByHeight(float lineHeight) {
        return (int) (mViewHeight / WaveViewUtil.NUM_TWO + lineHeight / WaveViewUtil.NUM_TWO + mMarkViewHeight / WaveViewUtil.NUM_TWO);
    }

    protected boolean isSmallWaveType() {
        return mWaveType == WaveViewUtil.WaveType.SMALL;
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (mVsyncCallback == null) {
                mVsyncCallback = new Choreographer.VsyncCallback() {
                    @Override
                    public void onVsync(@NonNull Choreographer.FrameData frameData) {
                        if (mNeedInvalidate) {
                            invalidate();
                            mNeedInvalidate = false;
                        }
                        Choreographer.getInstance().postVsyncCallback(this);
                    }

                };
            }
            Choreographer.getInstance().postVsyncCallback(mVsyncCallback);
        }
    }

    @Override
    public void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        /*
         * PictureMarkTarget.onSuccess回调内存泄漏
         */
        if (mMarkTimeList != null) {
            mMarkTimeList.forEach(markDataBean -> markDataBean.setOnSuccess(null));
        }
        if (mBookmarkAnimator != null) {
            mBookmarkAnimator.release();
        }
        if (mVsyncCallback != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                Choreographer.getInstance().removeVsyncCallback(mVsyncCallback);
            }
            mVsyncCallback = null;
        }
    }

    public void setCurViewIndex(int index) {
        mViewIndex = index;
    }

    public void setMarkOnClickListener(MarkOnClickListener listener) {
        this.mMarkOnClickListener = listener;
    }

    public interface MarkOnClickListener {
        void onClick(MarkDataBean mark);
    }

    private static class LoadImageListener implements ILoadImageResultListener {
        private final WeakReference<WaveItemView> mWeakView;

        public LoadImageListener(WaveItemView view) {
            mWeakView = new WeakReference<>(view);
        }

        @Override
        public void onLoadImageSuccess() {
            final View view = mWeakView.get();
            if (view != null) {
                view.postInvalidate();
            }
        }
    }
}

