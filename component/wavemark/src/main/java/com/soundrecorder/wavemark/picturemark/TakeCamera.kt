package com.soundrecorder.wavemark.picturemark

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Environment
import android.provider.MediaStore
import androidx.activity.result.contract.ActivityResultContract
import androidx.core.content.FileProvider
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.utils.EnableAppUtil
import java.io.File

class TakeCamera : ActivityResultContract<Unit, Uri?>() {

    companion object {
        private const val TAG = "TakeCamera"
        private val cameraPackages = listOf(
            "com.oppo.camera",
            "com.oneplus.camera",
            "com.oplus.camera"
        )

        fun getCameraPackageName(): Pair<String?, Int> {
            cameraPackages.forEach {
                val result = EnableAppUtil.isAppInstallEnabled(BaseApplication.getAppContext(), it)
                if (result != EnableAppUtil.APP_NOT_INSTALLED) {
                    return Pair(it, result)
                }
            }
            return Pair(null, EnableAppUtil.APP_NOT_INSTALLED)
        }

        private var uri: Uri? = null
        private val file by lazy {
            val context = BaseApplication.getAppContext()
            File(
                "${context.getExternalFilesDir(Environment.DIRECTORY_DCIM)}",
                "img_sounder_original.jpg"
            )
        }
    }

    var cameraPackageName: String? = null


    override fun createIntent(context: Context, viod: Unit): Intent {
        val packageName = cameraPackageName ?: getCameraPackageName().first
        val intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE, null).apply {
            putExtra(MediaStore.EXTRA_VIDEO_QUALITY, 1)
            if (BaseUtil.isAndroidNOrLater) {
                uri = FileProvider.getUriForFile(
                    context,
                    "${DatabaseConstant.PACKAGE_NAME}.fileProvider", file
                )
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            } else {
                uri = Uri.fromFile(file)
            }
            putExtra(MediaStore.EXTRA_OUTPUT, uri)
            putExtra("return-data", true)
            if (!packageName.isNullOrEmpty()) {
                setPackage(packageName)
            }
        }
        return intent
    }


    override fun getSynchronousResult(context: Context, unit: Unit): SynchronousResult<Uri?>? {
        return null
    }

    override fun parseResult(resultCode: Int, intent: Intent?): Uri? {
        return if (resultCode == Activity.RESULT_OK) {
            uri
        } else {
            if (file.exists()) {
                file.delete()
            }
            null
        }
    }
}