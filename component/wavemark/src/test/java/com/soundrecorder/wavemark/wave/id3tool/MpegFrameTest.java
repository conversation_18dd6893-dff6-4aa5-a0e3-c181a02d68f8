package com.soundrecorder.wavemark.wave.id3tool;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;

import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class MpegFrameTest {

    @Test
    public void should_use_when_value_is_1() throws Exception {
        MpegFrame mpegFrame = new MpegFrame();
        Whitebox.invokeMethod(mpegFrame, "setLayer", 1);
        Assert.assertEquals(mpegFrame.getLayer(), "III");
        Whitebox.invokeMethod(mpegFrame, "setVersion", 0);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 1);
        Assert.assertEquals(mpegFrame.getBitrate(), 8);
        Whitebox.invokeMethod(mpegFrame, "setSampleRate", 1);
        Assert.assertEquals(mpegFrame.getSampleRate(), 12000);
        Whitebox.invokeMethod(mpegFrame, "setPadding", 1);
        Assert.assertEquals(mpegFrame.hasPadding(), true);
        Whitebox.invokeMethod(mpegFrame, "setPrivate", 1);
        Assert.assertEquals(mpegFrame.isPrivate(), true);
        Whitebox.invokeMethod(mpegFrame, "setChannelMode", 1);
        Assert.assertEquals(mpegFrame.getChannelMode(), "Joint stereo");
        Whitebox.invokeMethod(mpegFrame, "setModeExtension", 1);
        Assert.assertEquals(mpegFrame.getModeExtension(), "Intensity stereo");
        Whitebox.invokeMethod(mpegFrame, "setCopyright", 1);
        Assert.assertEquals(mpegFrame.isCopyright(), true);
        Whitebox.invokeMethod(mpegFrame, "setOriginal", 1);
        Assert.assertEquals(mpegFrame.isOriginal(), true);
        Whitebox.invokeMethod(mpegFrame, "setEmphasis", 1);
        Assert.assertEquals(mpegFrame.getEmphasis(), "50/15 ms");
    }

    @Test
    public void should_use_when_value_is_2() throws Exception {
        MpegFrame mpegFrame = new MpegFrame();
        Whitebox.invokeMethod(mpegFrame, "setLayer", 3);
        Assert.assertEquals(mpegFrame.getLayer(), "I");
        Whitebox.invokeMethod(mpegFrame, "setLayer", 2);
        Assert.assertEquals(mpegFrame.getLayer(), "II");
        Whitebox.invokeMethod(mpegFrame, "setVersion", 0);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 2);
        Assert.assertEquals(mpegFrame.getBitrate(), 16);
        Whitebox.invokeMethod(mpegFrame, "setSampleRate", 2);
        Assert.assertEquals(mpegFrame.getSampleRate(), 8000);
        Whitebox.invokeMethod(mpegFrame, "setChannelMode", 2);
        Assert.assertEquals(mpegFrame.getChannelMode(), MpegFrame.CHANNEL_MODE_DUAL_MONO);
        Whitebox.invokeMethod(mpegFrame, "setModeExtension", 2);
        Assert.assertEquals(mpegFrame.getModeExtension(), MpegFrame.MODE_EXTENSION_NA);
        Whitebox.invokeMethod(mpegFrame, "setEmphasis", 3);
        Assert.assertEquals(mpegFrame.getEmphasis(), MpegFrame.EMPHASIS_CCITT_J_17);
        Whitebox.invokeMethod(mpegFrame, "setVersion", 2);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 2);
        Assert.assertEquals(mpegFrame.getBitrate(), 16);
    }

    @Test
    public void should_use_when_extractField() {
        MpegFrame mpegFrame = new MpegFrame();
        int length = mpegFrame.extractField(12l, 11l);
        Assert.assertEquals(length, 8);
    }

    @Test
    public void should_getBitRate_when_value_is_3() throws Exception {
        MpegFrame mpegFrame = new MpegFrame();
        Whitebox.invokeMethod(mpegFrame, "setLayer", 3);
        Whitebox.invokeMethod(mpegFrame, "setVersion", 3);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 1);
        Assert.assertEquals(mpegFrame.getBitrate(), 32);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 2);
        Assert.assertEquals(mpegFrame.getBitrate(), 64);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 3);
        Assert.assertEquals(mpegFrame.getBitrate(), 96);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 4);
        Assert.assertEquals(mpegFrame.getBitrate(), 128);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 5);
        Assert.assertEquals(mpegFrame.getBitrate(), 160);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 6);
        Assert.assertEquals(mpegFrame.getBitrate(), 192);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 7);
        Assert.assertEquals(mpegFrame.getBitrate(), 224);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 8);
        Assert.assertEquals(mpegFrame.getBitrate(), 256);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 9);
        Assert.assertEquals(mpegFrame.getBitrate(), 288);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 10);
        Assert.assertEquals(mpegFrame.getBitrate(), 320);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 11);
        Assert.assertEquals(mpegFrame.getBitrate(), 352);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 12);
        Assert.assertEquals(mpegFrame.getBitrate(), 384);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 13);
        Assert.assertEquals(mpegFrame.getBitrate(), 416);
        Whitebox.invokeMethod(mpegFrame, "setBitRate", 14);
        Assert.assertEquals(mpegFrame.getBitrate(), 448);
    }

    @Test
    public void should_getvalue_when_getLengthInBytes() throws Exception {
        MpegFrame mpegFrame = new MpegFrame();
        Whitebox.invokeMethod(mpegFrame, "setVersion", 3);
        Whitebox.invokeMethod(mpegFrame, "setSampleRate", 2);
        Assert.assertEquals(mpegFrame.getLengthInBytes(), 0);
    }
}
