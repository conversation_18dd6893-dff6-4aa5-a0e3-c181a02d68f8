package com.soundrecorder.wavemark.wave.id3tool;

import android.os.Build;

import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.annotation.Config;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class ID3WrapperTest {
    @Test
    public void should_returnValue_getandset() {
        ID3v1 id3v1 = new ID3v1Tag();
        ID3v2 id3v2 = new ID3v22Tag();
        ID3Wrapper id3Wrapper = new ID3Wrapper(id3v1, id3v2);
        id3Wrapper.setAlbum("123");
        Assert.assertEquals(id3Wrapper.getAlbum(), "123");
        id3Wrapper.setYear("2009-12-3");
        Assert.assertEquals(id3Wrapper.getYear(), "2009-12-3");
        id3Wrapper.setGenre(1);
        Assert.assertEquals(id3Wrapper.getGenre(), 1);
        Assert.assertNotNull(id3Wrapper.getGenreDescription());
        id3Wrapper.setComment("test");
        Assert.assertEquals(id3Wrapper.getComment(), "test");
        id3Wrapper.setComposer("test");
        Assert.assertEquals(id3Wrapper.getComposer(), "test");
        id3Wrapper.setOriginalArtist("test");
        Assert.assertEquals(id3Wrapper.getOriginalArtist(), "test");
        id3Wrapper.setAlbumArtist("test");
        Assert.assertEquals(id3Wrapper.getAlbumArtist(), "test");
        id3Wrapper.setCopyright("test");
        Assert.assertEquals(id3Wrapper.getCopyright(), "test");
        id3Wrapper.setUrl("http://www.csdn.com");
        Assert.assertEquals(id3Wrapper.getUrl(), "http://www.csdn.com");
        id3Wrapper.setEncoder("test");
        Assert.assertEquals(id3Wrapper.getEncoder(), "test");
        id3Wrapper.setAlbumImage("test".getBytes(), "test");
        Assert.assertNotNull(id3Wrapper.getAlbumImage());
        id3Wrapper.setLyrics("test");
        Assert.assertEquals(id3Wrapper.getLyrics(), "test");
    }

    @Test
    public void should_returnNotnull_when_clear() {
        ID3v1 id3v1 = new ID3v1Tag();
        ID3v2 id3v2 = new ID3v22Tag();
        ID3Wrapper id3Wrapper = new ID3Wrapper(id3v1, id3v2);
        id3Wrapper.clearComment();
        Assert.assertNull(id3Wrapper.getComment());
        id3Wrapper.clearCopyright();
        Assert.assertNull(id3Wrapper.getCopyright());
        id3Wrapper.clearEncoder();
        Assert.assertNull(id3Wrapper.getEncoder());
    }
}

