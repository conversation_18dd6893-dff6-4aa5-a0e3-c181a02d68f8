package com.soundrecorder.wavemark.wave.view;

import static com.soundrecorder.common.utils.MarkSerializUtil.VERSION_NEW;
import static org.mockito.Mockito.times;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.wavemark.R;
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;
import com.soundrecorder.wavemark.wave.WaveViewUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@PrepareForTest({WaveItemView.class, WaveViewUtil.class})
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class WaveItemViewTest {
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }


    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_returnFalse_when_isNightMode() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        Assert.assertFalse(waveItemView.isNightMode());
    }

    @Test
    public void should_returnTrue_when_setNightMode() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        Assert.assertFalse(waveItemView.isNightMode());
        waveItemView.setNightMode(true);
        Assert.assertTrue(waveItemView.isNightMode());
    }

    @Test
    public void should_returnFalse_when_isReverseLayout() throws Exception {
        WaveItemView waveItemView = new WaveItemView(mContext);
        boolean isFlag = Whitebox.invokeMethod(waveItemView, "isReverseLayout");
        Assert.assertFalse(isFlag);
    }

    @Test
    public void should_returnNotNull_when_caculatLineScaleValue() throws Exception {
        WaveItemView waveItemView = new WaveItemView(mContext);
        float isFlag = Whitebox.invokeMethod(waveItemView, "caculateLineScaleValue", mContext);
        Assert.assertNotEquals(isFlag, 0);
    }

    @Test
    public void should_correct_when_setEndItemWidth() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        waveItemView.setEndItemWidth(120);
        float itemWidth = Whitebox.getInternalState(waveItemView, "mEndItemWidth");
        Assert.assertEquals(itemWidth, 120, 0f);
    }

    @Test
    public void should_returnFalse_when_initPaint() throws Exception {
        WaveItemView waveItemView = new WaveItemView(mContext);
        Whitebox.invokeMethod(waveItemView, "initPaint", mContext);
        Paint mAmplitudePaint = Whitebox.getInternalState(waveItemView, "mAmplitudePaint");
        Assert.assertNotNull(mAmplitudePaint);
    }

    @Test
    public void should_returnValue_when_updatePaintColor() throws Exception {
        WaveItemView waveItemView = new WaveItemView(mContext);
        Whitebox.invokeMethod(waveItemView, "initPaint", mContext);
        waveItemView.updatePaintColor();
        int mWaveNormalColor = Whitebox.getInternalState(waveItemView, "mWaveNormalColor");
        Assert.assertNotEquals(mWaveNormalColor, 0);
    }

    @Test
    public void should_NotNull_when_setAmplitudes() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        Object list = Whitebox.getInternalState(waveItemView, "mAmplitudes");
        Assert.assertNull(list);

        List<Integer> arrayList = new ArrayList<>();
        waveItemView.setAmplitudes(arrayList);
        list = Whitebox.getInternalState(waveItemView, "mAmplitudes");
        Assert.assertNotNull(list);

        arrayList.add(111);
        waveItemView.setAmplitudes(arrayList);
        List<String> list2 = Whitebox.getInternalState(waveItemView, "mAmplitudes");
        Assert.assertEquals(1, list2.size());
    }

    @Test
    public void should_NotNull_when_setDecodedAmplitudeList() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        Object list = Whitebox.getInternalState(waveItemView, "mDecodedAmplitudeList");
        Assert.assertNull(list);

        List<Integer> arrayList = new ArrayList<>();
        waveItemView.setDecodedAmplitudeList(arrayList);
        list = Whitebox.getInternalState(waveItemView, "mDecodedAmplitudeList");
        Assert.assertNotNull(list);

        arrayList.add(111);
        waveItemView.setDecodedAmplitudeList(arrayList);
        List<String> list2 = Whitebox.getInternalState(waveItemView, "mDecodedAmplitudeList");
        Assert.assertEquals(1, list2.size());
    }

    @Test
    public void should_NotDefault_when_setTotalCount() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        int count = Whitebox.getInternalState(waveItemView, "mTotalCount");
        Assert.assertEquals(0, count);

        waveItemView.setTotalCount(10);
        count = Whitebox.getInternalState(waveItemView, "mTotalCount");
        Assert.assertEquals(10, count);
    }

    @Test
    public void should_returnCorrectValue_when_setMarkTimeList() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        //mMarkTimeList初始化为空
        Assert.assertNull(Whitebox.getInternalState(waveItemView, "mMarkTimeList"));
        //index默认为0，但是第0个item是屏幕的一半，所以是没有标记的
        int index = Whitebox.getInternalState(waveItemView, "mViewIndex");
        Assert.assertEquals(0, index);

        ArrayList<MarkDataBean> markTimeList = new ArrayList<>();
        MarkDataBean markDataBean1 = new MarkDataBean(2 * 1000L, VERSION_NEW);//2s
        MarkDataBean markDataBean2 = new MarkDataBean(5 * 1000L, VERSION_NEW);//5s
        MarkDataBean markDataBean3 = new MarkDataBean(16 * 1000L, VERSION_NEW);//16s
        MarkDataBean markDataBean4 = new MarkDataBean(16 * 1000L, VERSION_NEW);//16s
        markDataBean1.setPictureFilePath("fakePath");//在规定时间内且 filePath 不为空
        markDataBean2.setPictureFilePath("");//在规定时间内但是 filePath 为空
        markDataBean3.setPictureFilePath("fakePath");//filePath 不为空但是在规定时间外
        markDataBean4.setPictureFilePath("");//filePath 为空但是在规定时间外

        markTimeList.add(markDataBean1);
        markTimeList.add(markDataBean2);
        markTimeList.add(markDataBean3);
        markTimeList.add(markDataBean4);
        waveItemView.setMarkTimeList(markTimeList);

        Assert.assertNotNull(waveItemView.getMarkTimeList());
        System.out.println("getMarkTimeList = " + (waveItemView.getMarkTimeList().toString()));
        //index为0则应该没有标记
        Assert.assertEquals(0, waveItemView.getMarkTimeList().size());

        //设置当前的index为1
        waveItemView.setCurViewIndex(1);
        waveItemView.setMarkTimeList(markTimeList);
        System.out.println("getMarkTimeList = " + (waveItemView.getMarkTimeList().toString()));
        //期望值添加了一个有效数据
        Assert.assertEquals(1, waveItemView.getMarkTimeList().size());
    }

    @Test
    public void should_returnValue_when_setTotalTime() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        long mPlayTotalTime = Whitebox.getInternalState(waveItemView, "mTotalTime");
        Assert.assertEquals(0L, mPlayTotalTime);

        waveItemView.setTotalTime(2000L);
        mPlayTotalTime = Whitebox.getInternalState(waveItemView, "mTotalTime");
        Assert.assertEquals(2000L, mPlayTotalTime);
    }

    @Test
    public void should_returnValue_when_updateScreenWidth() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        waveItemView.updateScreenWidth(1080);
        float mScreenWidth = Whitebox.getInternalState(waveItemView, "mScreenWidth");
        float mCenterLineX = Whitebox.getInternalState(waveItemView, "mCenterLineX");
        Assert.assertEquals(1080F, mScreenWidth, 0F);
        Assert.assertEquals(540F, mCenterLineX, 0F);
    }

    @Test
    public void should_returnValue_when_getAllXByTime() throws Exception {
        WaveItemView waveItemView = new WaveItemView(mContext);
        float pxPerMs = Whitebox.getInternalState(waveItemView, "mPxPerMs");
        float result = Whitebox.invokeMethod(waveItemView, "getAllXByTime", 5000L);
        Assert.assertEquals(5000L * pxPerMs, result, 0F);
    }

    @Test
    public void should_returnValue_when_calculateAmpStartIndex() throws Exception {
        WaveItemView waveItemView = new WaveItemView(mContext);
        int leng = Whitebox.invokeMethod(waveItemView, "calculateAmpStartIndex");
        Assert.assertEquals(leng, 0);

        waveItemView.setCurViewIndex(2);
        float virtualAmpGap = Whitebox.getInternalState(waveItemView, "mVirtualAmpGap");
        float oneWaveItemWidth = WaveViewUtil.getOneLargeWaveItemWidth(mContext);
        int leng2 = Whitebox.invokeMethod(waveItemView, "calculateAmpStartIndex");
        Assert.assertEquals((int) Math.ceil(oneWaveItemWidth / virtualAmpGap), leng2);
    }

    @Test
    public void should_returnValue_when_calculateAmpStartX() throws Exception {
        WaveItemView waveItemView = new WaveItemView(mContext);
        float aFloat = Whitebox.invokeMethod(waveItemView, "calculateAmpStartX", 1L);
        Assert.assertEquals(aFloat, 0F, 0F);

        waveItemView.setCurViewIndex(2);
        float pxPerMs = Whitebox.getInternalState(waveItemView, "mPxPerMs");
        float expectX = Constants.WAVE_SAMPLE_INTERVAL_TIME * pxPerMs;
        float oneWaveItemWidth = WaveViewUtil.getOneLargeWaveItemWidth(mContext);
        float aFloat2 = Whitebox.invokeMethod(waveItemView, "calculateAmpStartX", 1L);
        Assert.assertEquals((int) Math.ceil(expectX - oneWaveItemWidth), aFloat2, 0F);
    }

    @Test
    public void verify_drawLine_times_when_drawDottedLine() throws Exception {
        WaveItemView waveItemView = new WaveItemView(mContext);
        Canvas mockCanvas = Mockito.spy(Canvas.class);
        Whitebox.invokeMethod(waveItemView, "drawDottedLine", mockCanvas, false, 0F, 540);
        PowerMockito.verifyPrivate(mockCanvas, times(1)).invoke("drawLine", Mockito.anyFloat(), Mockito.anyFloat(), Mockito.anyFloat(), Mockito.anyFloat(), Mockito.any(Paint.class));
        Whitebox.invokeMethod(waveItemView, "drawDottedLine", mockCanvas, true, 0F, 540);
        //这里次数会算上上面的次数，就是2次
        PowerMockito.verifyPrivate(mockCanvas, times(2)).invoke("drawLine", Mockito.anyFloat(), Mockito.anyFloat(), Mockito.anyFloat(), Mockito.anyFloat(), Mockito.any(Paint.class));
    }

    @Test
    public void verify_drawBitmap_times_when_drawBookmark() throws Exception {
        WaveItemView waveItemView = new WaveItemView(mContext);
        Canvas mockCanvas = Mockito.spy(Canvas.class);

        waveItemView.setCurViewIndex(0);
        Whitebox.invokeMethod(waveItemView, "drawBookmark", mockCanvas);
        PowerMockito.verifyPrivate(mockCanvas, Mockito.never()).invoke("drawBitmap", Mockito.any(Bitmap.class), Mockito.anyFloat(), Mockito.anyFloat(), Mockito.any(Paint.class));

        Whitebox.setInternalState(waveItemView, "mMarkTimeList", (Object) null);
        Whitebox.invokeMethod(waveItemView, "drawBookmark", mockCanvas);
        PowerMockito.verifyPrivate(mockCanvas, Mockito.never()).invoke("drawBitmap", Mockito.any(Bitmap.class), Mockito.anyFloat(), Mockito.anyFloat(), Mockito.any(Paint.class));

        ArrayList<MarkDataBean> markTimeList = new ArrayList<>();
        Whitebox.setInternalState(waveItemView, "mMarkTimeList", markTimeList);
        Whitebox.invokeMethod(waveItemView, "drawBookmark", mockCanvas);
        PowerMockito.verifyPrivate(mockCanvas, Mockito.never()).invoke("drawBitmap", Mockito.any(Bitmap.class), Mockito.anyFloat(), Mockito.anyFloat(), Mockito.any(Paint.class));
        //第1个item，标记范围是0s - 6s
        waveItemView.setCurViewIndex(1);
        ArrayList<MarkDataBean> markList = getMarkList();
        //setMarkTimeList的时候会过滤标记时间，不符合的标记会被过滤
        waveItemView.setMarkTimeList(markList);
        ArrayList<MarkDataBean> list = Whitebox.getInternalState(waveItemView, "mMarkTimeList");
        Assert.assertEquals(4, list.size());

        Mockito.when(markList.get(2).getIcon(mContext, R.drawable.ic_mark_point, R.drawable.ic_picture_mark_def)).thenReturn(null);
        Mockito.when(markList.get(3).getIcon(mContext, R.drawable.ic_mark_point, R.drawable.ic_picture_mark_def)).thenThrow(new RuntimeException());
        Whitebox.invokeMethod(waveItemView, "drawBookmark", mockCanvas);
        PowerMockito.verifyPrivate(mockCanvas, Mockito.times(2)).invoke("drawBitmap", Mockito.any(Bitmap.class), Mockito.anyFloat(), Mockito.anyFloat(), Mockito.any(Paint.class));
    }

    private ArrayList<MarkDataBean> getMarkList() {
        ArrayList<MarkDataBean> markTimeList = new ArrayList<>();

        MarkDataBean markDataBean1 = Mockito.spy(new MarkDataBean(100, VERSION_NEW));
        MarkDataBean markDataBean2 = Mockito.spy(new MarkDataBean(5000, VERSION_NEW));
        MarkDataBean markDataBean3 = Mockito.spy(new MarkDataBean(2000, VERSION_NEW));
        MarkDataBean markDataBean4 = Mockito.spy(new MarkDataBean(3100, VERSION_NEW));
        MarkDataBean markDataBean5 = Mockito.spy(new MarkDataBean(7000, VERSION_NEW));
        markDataBean1.setPictureFilePath("testPath");
        markTimeList.add(markDataBean1);
        markTimeList.add(markDataBean2);
        markTimeList.add(markDataBean3);
        markTimeList.add(markDataBean4);
        markTimeList.add(markDataBean5);

        Mockito.doNothing().when(markDataBean1).setOnSuccess(Mockito.any());
        Mockito.doNothing().when(markDataBean2).setOnSuccess(Mockito.any());
        Mockito.doNothing().when(markDataBean3).setOnSuccess(Mockito.any());
        Mockito.doNothing().when(markDataBean4).setOnSuccess(Mockito.any());
        Mockito.doNothing().when(markDataBean5).setOnSuccess(Mockito.any());
        return markTimeList;
    }

//    @Test
//    public void should_returnValue_when_getWaveLineHeight() throws Exception {
//        WaveItemView waveItemView = new WaveItemView(mContext);
//        float maxScaleLineLen = Whitebox.getInternalState(waveItemView, "mMaxScaleLineLen");//35.0
//        float midScaleLineLen = Whitebox.getInternalState(waveItemView, "mMidScaleLineLen");//25.0
//        float minScaleLineLen = Whitebox.getInternalState(waveItemView, "mMinScaleLineLen");//10.0
//        int maxWaveHeight = Whitebox.getInternalState(waveItemView, "mMaxWaveHeight");
//
//        int preValue = WaveViewUtil.MAX_AMPLITUDE * 2 + 1;
//        int currValue = WaveViewUtil.MAX_AMPLITUDE * 2;
//        float height = Whitebox.invokeMethod(waveItemView, "getWaveLineHeight", preValue, currValue);
//        Assert.assertTrue(height > 0);
//
//        height = Whitebox.invokeMethod(waveItemView, "getWaveLineHeight", preValue * -1, currValue);
//        Assert.assertTrue(height > 0);
//
////        (currValue * maxWaveHeight)/ (WaveViewUtil.NUM_TWO * WaveViewUtil.MAX_AMPLITUDE) < maxScaleLineLen;
//        currValue = (int) ((WaveViewUtil.NUM_TWO * WaveViewUtil.MAX_AMPLITUDE * maxScaleLineLen) / maxWaveHeight - 10);
//        height = Whitebox.invokeMethod(waveItemView, "getWaveLineHeight", 0, currValue);
//        Assert.assertTrue(height > 0);
//
//        currValue = (int) ((WaveViewUtil.NUM_TWO * WaveViewUtil.MAX_AMPLITUDE * minScaleLineLen) / maxWaveHeight - 10);
//        height = Whitebox.invokeMethod(waveItemView, "getWaveLineHeight", 0, currValue);
//        Assert.assertTrue(height > 0);
//
//        currValue = (int) ((WaveViewUtil.NUM_TWO * WaveViewUtil.MAX_AMPLITUDE * midScaleLineLen) / maxWaveHeight - 10);
//        height = Whitebox.invokeMethod(waveItemView, "getWaveLineHeight", 0, currValue);
//        Assert.assertTrue(height > 0);
//    }

    @Test
    public void should_returnValue_when_setAndGet() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        waveItemView.setCurViewIndex(1);
        int mViewIndex = Whitebox.getInternalState(waveItemView, "mViewIndex");
        Assert.assertEquals(mViewIndex, 1);
    }

    @Test
    public void should_returnNotNull_when_onDraw() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        waveItemView.setCurViewIndex(1);
        ArrayList<MarkDataBean> markTimeList = new ArrayList<>();
        markTimeList.add(new MarkDataBean(11L, VERSION_NEW));
        markTimeList.add(new MarkDataBean(12L, VERSION_NEW));
        waveItemView.setMarkTimeList(markTimeList);
        waveItemView.onDraw(new Canvas());
        Paint mAmplitudePaint = Whitebox.getInternalState(waveItemView, "mAmplitudePaint");
        Assert.assertNotNull(mAmplitudePaint);
    }



    @Test
    public void verify_value_when_onLoadImageSuccess() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        waveItemView.onLoadImageSuccess();
    }

    @Test
    public void verify_value_when_onDetachedFromWindow() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        waveItemView.setCurViewIndex(1);
        ArrayList<MarkDataBean> list = new ArrayList<>();
        list.add(new MarkDataBean(520, VERSION_NEW));
        list.add(new MarkDataBean(1600, VERSION_NEW));

        waveItemView.setMarkTimeList(list);
        waveItemView.onDetachedFromWindow();
    }

    @Test
    public void verify_value_when_setMarkOnClickListener() {
        WaveItemView waveItemView = new WaveItemView(mContext);
        waveItemView.setMarkOnClickListener(mark -> {
        });
        Assert.assertNotNull(Whitebox.getInternalState(waveItemView, "mMarkOnClickListener"));
    }
}