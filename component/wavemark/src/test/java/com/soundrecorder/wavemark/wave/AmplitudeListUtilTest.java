package com.soundrecorder.wavemark.wave;

import static com.soundrecorder.modulerouter.waveMark.WaveConstant.FULL_AMP_TYPE;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.utils.amplitude.AmpFileUtil;
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;
import com.soundrecorder.wavemark.wave.load.AmplitudeListUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;

import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowFeatureOption.class})
public class AmplitudeListUtilTest {
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_returnTrue_when_decodeMp3() throws Exception {
        Uri uri = Mockito.mock(Uri.class);
        AmplitudeListUtil amplitudeListUtil = new AmplitudeListUtil(mContext, "emulated/0/Music/Recordings/Standard Recordings/1.mp3", uri, false);
        Whitebox.invokeMethod(amplitudeListUtil, "decodeMp3");
        String mMarkStringFromMp3 = Whitebox.getInternalState(amplitudeListUtil, "mMarkStringFromMp3");
        Assert.assertNull(mMarkStringFromMp3);

        amplitudeListUtil = new AmplitudeListUtil(mContext, null, uri, false);
        Whitebox.invokeMethod(amplitudeListUtil, "decodeMp3");
        mMarkStringFromMp3 = Whitebox.getInternalState(amplitudeListUtil, "mMarkStringFromMp3");
        Assert.assertNull(mMarkStringFromMp3);

        amplitudeListUtil = new AmplitudeListUtil(mContext, "emulated/0/Music/Recordings/Standard Recordings/1.mp3", null, false);
        Whitebox.invokeMethod(amplitudeListUtil, "decodeMp3");
        mMarkStringFromMp3 = Whitebox.getInternalState(amplitudeListUtil, "mMarkStringFromMp3");
        Assert.assertNull(mMarkStringFromMp3);

        Mockito.when(mContext.getContentResolver().getType(uri)).thenReturn("audio/mpeg");
        amplitudeListUtil = new AmplitudeListUtil(mContext, "emulated/0/Music/Recordings/Standard Recordings/1.mp3", uri, false);
        Whitebox.invokeMethod(amplitudeListUtil, "decodeMp3");
        mMarkStringFromMp3 = Whitebox.getInternalState(amplitudeListUtil, "mMarkStringFromMp3");
        Assert.assertNull(mMarkStringFromMp3);
    }

    @Test
    public void should_getSize_when_soundList2String() throws Exception {
        Uri uri = Mockito.mock(Uri.class);
        AmplitudeListUtil amplitudeListUtil = new AmplitudeListUtil(mContext, "emulated/0/Music/Recordings/Standard Recordings/1.mp3", uri, false);
        List<Integer> list = new ArrayList<>();
        list.add(1);
        list.add(2);
        list.add(3);
        String str = Whitebox.invokeMethod(amplitudeListUtil, "soundList2String", list);
        Assert.assertNotEquals(str, "1,2,3");

        Whitebox.invokeMethod(amplitudeListUtil, "soundList2String", (ArrayList<Integer>) null);
        Assert.assertNotEquals(str, "");

        Whitebox.invokeMethod(amplitudeListUtil, "soundList2String", new ArrayList<Integer>());
        Assert.assertNotEquals(str, "");
    }

    @Test
    public void should_returnNotnull_when_getAmpString() {
        Uri uri = Mockito.mock(Uri.class);
        AmplitudeListUtil amplitudeListUtil = new AmplitudeListUtil(mContext, "emulated/0/Music/Recordings/Standard Recordings/1.mp3", uri, false);
        Assert.assertNull(amplitudeListUtil.getAmpString());
    }

    @Test
    public void should_returnEquals_when_getAmpList() {
        Uri uri = Mockito.mock(Uri.class);
        AmplitudeListUtil amplitudeListUtil = new AmplitudeListUtil(mContext, "emulated/0/Music/Recordings/Standard Recordings/1.mp3", uri, false);
        AmplitudeListUtil amplitudeListUtilSpy = Mockito.spy(amplitudeListUtil);
        Mockito.doReturn("1,2,3,4,5,6,7,8,9,10").when(amplitudeListUtilSpy).getAmpString();
        List<Integer> ampList = amplitudeListUtilSpy.getAmpList().getAmpByTypeOrSegments(FULL_AMP_TYPE);
        Assert.assertEquals(10, ampList.size());
    }

    @Test
    public void should_returnEquals_when_convertStringToInt() {
        List<Integer> ampList = AmpFileUtil.convertStringToInt("1,2,3,4,5,6,7,8,9,10");
        Assert.assertEquals(10, ampList.size());
    }

    @Test
    public void should_returnNotnull_when_getMarkString() {
        Uri uri = Mockito.mock(Uri.class);
        AmplitudeListUtil amplitudeListUtil = new AmplitudeListUtil(mContext, "emulated/0/Music/Recordings/Standard Recordings/1.mp3", uri, false);
        Assert.assertNull(amplitudeListUtil.getMarkString());
    }

    @Ignore
    @Test
    public void should_returnNotnull_when_getMergeMarkList() {
        Uri uri = Mockito.mock(Uri.class);
        AmplitudeListUtil amplitudeListUtil = new AmplitudeListUtil(mContext, "emulated/0/Music/Recordings/Standard Recordings/1.mp3", uri, false);
        Assert.assertNotNull(amplitudeListUtil.getMergeMarkList());
    }

    @Test
    public void should_returnNotnull_when_string2Integer() {
        Uri uri = Mockito.mock(Uri.class);
        AmplitudeListUtil amplitudeListUtil = new AmplitudeListUtil(mContext, "emulated/0/Music/Recordings/Standard Recordings/1.mp3", uri, false);
        List<String> list = new ArrayList<>();
        list.add("1");
        list.add("2");
        list.add("3");
        List<Integer> list2 = amplitudeListUtil.string2Integer(list);
        Assert.assertEquals(list2.get(0).intValue(), 1);

        list2 = amplitudeListUtil.string2Integer(null);
        Assert.assertEquals(0, list2.size());

        list.add("a");
        list2 = amplitudeListUtil.string2Integer(list);
        Assert.assertEquals(list.size() - 1, list2.size());
    }

    @Test
    public void should_returnNotnull_when_release() {
        Uri uri = Mockito.mock(Uri.class);
        AmplitudeListUtil amplitudeListUtil = new AmplitudeListUtil(mContext, "emulated/0/Music/Recordings/Standard Recordings/1.mp3", uri, false);
        amplitudeListUtil.setDecodeFinish(Mockito.spy(AmplitudeListUtil.DecodeFinish.class));
        amplitudeListUtil.setDecodeReady(Mockito.spy(AmplitudeListUtil.DecodeReady.class));
        amplitudeListUtil.getAmpFromSoundFile();
        amplitudeListUtil.release();
        boolean isflag = Whitebox.getInternalState(amplitudeListUtil, "mIsDecodeMp3");
        Assert.assertFalse(isflag);
    }

    @Test
    public void should_returnNotNull_getAmplitudeString_withNormal() {
        assertTrue(TextUtils.isEmpty(AmplitudeListUtil.getAmplitudeString(null)));
        List<Integer> amplitudes = new ArrayList<>();
        amplitudes.add(111);
        amplitudes.add(222);
        amplitudes.add(888);
        assertNotNull(AmplitudeListUtil.getAmplitudeString(amplitudes));
    }
}
