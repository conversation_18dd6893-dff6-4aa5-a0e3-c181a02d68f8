package com.soundrecorder.wavemark.wave;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowFeatureOption.class})
public class WaveViewUtilTest {
    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        WaveViewUtil.clearAll();
    }

    @Test
    public void should_notZero_when_getLargeWaveTimeLineGap() {
        float lineGap = 0;
        lineGap = WaveViewUtil.getLargeWaveTimeLineGap(mContext);
        Assert.assertNotEquals(0, lineGap, 0.0);
    }

    @Test
    public void should_notZero_when_getLargeWaveAmplitueLineGap() {
        float amplitueLineGap = 0;
        amplitueLineGap = WaveViewUtil.getLargeWaveAmplitueLineGap(mContext);
        Assert.assertNotEquals(0, amplitueLineGap, 0.0);
    }

    @Test
    public void should_notZero_when_getOneLargeWaveItemWidth() {
        float oneWaveItemWidthp = 0;
        oneWaveItemWidthp = WaveViewUtil.getOneLargeWaveItemWidth(mContext);
        Assert.assertNotEquals(0, oneWaveItemWidthp, 0.0);
    }

    @Test
    public void should_notZero_when_getOneLargeWaveItemAmplitueLineCount() {
        float oneWaveItemAmplitueLineCount = 0;
        oneWaveItemAmplitueLineCount = WaveViewUtil.getOneLargeWaveItemAmplitueLineCount(mContext);
        Assert.assertNotEquals(0, oneWaveItemAmplitueLineCount, 0.0);
    }

    @Test
    public void should_notNull_when_getStringBySecond() {
        String timeStr = null;
        String timeStrHour = null;
        timeStr = WaveViewUtil.getStringBySecond(50);
        timeStrHour = WaveViewUtil.getStringBySecond(3700);
        Assert.assertNotNull(timeStr);
        Assert.assertNotNull(timeStrHour);
        Assert.assertTrue(timeStr.contains(":"));
        Assert.assertTrue(timeStrHour.contains(":"));
        Assert.assertTrue(timeStr.length() < timeStrHour.length());
    }

    @After
    public void tearDown() {
        WaveViewUtil.clearAll();
    }
}