package com.recorder.cloudkit.sync.bean

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.heytap.cloudkit.libsync.service.CloudIOFile
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode.RESULT_SWITCH_CLOSE
import com.soundrecorder.common.databean.Record
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CloudSyncResultTest {

    @Test
    fun should_when_constructor() {
        val result = CloudSyncResult()

        Assert.assertNotNull(result)
        Assert.assertNull(result.cloudKitError)
        Assert.assertFalse(result.isSuccess())

        Assert.assertFalse(result.isCanTryCode())
        Assert.assertFalse(result.isCanTry(0, 0))

        result.errorCode = SyncErrorCode.RESULT_NETWORK_ERROR
        Assert.assertTrue(result.isCanTryCode())
        Assert.assertTrue(result.isCanTry(1, 2))
        Assert.assertFalse(result.isCanTry(1, 1))
        Assert.assertFalse(result.isCanTry(3, 1))
        Assert.assertFalse(result.errorFromMetaData())
    }

    @Test
    fun should_when_createSuccess() {
        val result = CloudSyncResult.createSuccess()

        Assert.assertNotNull(result)
        Assert.assertTrue(result.isSuccess())
        Assert.assertFalse(result.isCanTryCode())
    }

    @Test
    fun should_when_canTryCode() {
        Assert.assertNotNull(SyncErrorCode.CAN_TRY_SERVER_ERROR_CODE)
        Assert.assertTrue(
            SyncErrorCode.CAN_TRY_SERVER_ERROR_CODE.contains(
                SyncErrorCode.RESULT_IO_SERVER_TIMEOUT))
        Assert.assertFalse(SyncErrorCode.CAN_TRY_SERVER_ERROR_CODE.contains(RESULT_SWITCH_CLOSE))
    }


    @Test
    fun should_when_recordTransferFile_constructor() {
        val md5 = "202d77f57a1c4bade191615529a1e0a7"
        val uuid = "202d77f57a1c4bade191615529a1e0a7"
        val transferFile = RecordTransferFile(Record().apply { mUuid = uuid }, CloudIOFile.createUploadFile("", "", "cache://", md5))
        Assert.assertNotNull(transferFile)
        Assert.assertNotNull(transferFile.record)
        Assert.assertNotNull(transferFile.cloudIOFile)

        Assert.assertTrue(transferFile.uuid == uuid)
    }
}