package com.recorder.cloudkit.tipstatus

import android.content.Context
import android.content.res.Resources
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.recorder.cloudkit.R
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class TipStatusTest {
    val HALF_SPACE = "\u2000"

    private var mContext: Context? = null
    private var res: Resources? = null
    private var mMockedBaseApplication: MockedStatic<BaseApplication>? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        res = mContext?.resources
        mMockedBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockedBaseApplication?.`when`<Context> {
            BaseApplication.getAppContext()
        }?.thenReturn(mContext)
    }

    @After
    fun tearDown() {
        mMockedBaseApplication?.close()
        mMockedBaseApplication = null
        mContext = null
        res = null
    }

    @Test
    fun should_when_NONE_tipText() {
        val tipText = TipStatus.NONE.tipText()
        Assert.assertNotNull(tipText)

        Assert.assertEquals(tipText, Pair("", ""))
    }

    @Test
    fun should_when_CLOUD_OFF_tipText() {
        val tipText = TipStatus.CLOUD_OFF.tipText()
        Assert.assertNotNull(tipText)

        Assert.assertEquals(tipText, Pair("", ""))
    }

    @Test
    fun should_when_NO_ALL_ACCESS_PERMISSION_tipText() {
        val tipText = TipStatus.NO_ALL_ACCESS_PERMISSION.tipText()
        Assert.assertNotNull(tipText)
        val linkText = res?.getString(com.soundrecorder.common.R.string.cloud_recordings_reason) ?: ""
        Assert.assertEquals(linkText, tipText.second)
    }

    @Test
    fun should_when_NO_CLOUD_SPACE_tipText() {
        val tipText = TipStatus.NO_CLOUD_SPACE.tipText()
        Assert.assertNotNull(tipText)
        val linkText = res?.getString(R.string.upgrade_cloud_space) ?: ""
        Assert.assertEquals(linkText, tipText.second)
    }

    @Test
    fun should_when_QUERY_tipText() {
        val tipText = TipStatus.QUERY.tipText()
        Assert.assertNotNull(tipText)
        val text = res?.getString(R.string.cloudkit_querying) ?: ""
        Assert.assertEquals(text, tipText.first)
        Assert.assertEquals("", tipText.second)
    }

    @Test
    fun should_when_SYNCING_tipText() {
        val tipText = TipStatus.SYNCING.tipText()
        Assert.assertNotNull(tipText)
        val text = res?.getString(R.string.cloudkit_syncing) ?: ""
        Assert.assertEquals(text, tipText.first)
        Assert.assertEquals("", tipText.second)
    }

    @Test
    fun should_when_FAILURE_tipText() {
        val tipText = TipStatus.FAILURE.tipText()
        Assert.assertNotNull(tipText)
        val linkText = res?.getString(com.soundrecorder.common.R.string.cloud_recordings_reason) ?: ""
        Assert.assertEquals(linkText, tipText.second)
    }


    @Test
    fun should_when_COMPLETED_tipText() {
        val tipText = TipStatus.COMPLETED.tipText()
        Assert.assertNotNull(tipText)
        val linkText = res?.getString(com.soundrecorder.common.R.string.cloud_recordings_reason) ?: ""
        Assert.assertEquals(linkText, tipText.second)
    }

    @Test
    fun should_when_getFailureText() {
        val tipText = TipStatus.FAILURE.getFailureText()
        Assert.assertNotNull(tipText)
        val tips = res?.getString(com.soundrecorder.common.R.string.cloud_recordings_error, "") ?: ""
        Assert.assertEquals(tips, tipText.first)

        val linkText = res?.getString(com.soundrecorder.common.R.string.cloud_recordings_reason) ?: ""
        Assert.assertEquals(linkText, tipText.second)
    }

    @Test
    fun should_when_getFailureTextForNoCloudSpace() {
        val tipText = TipStatus.NO_CLOUD_SPACE.getFailureTextForNoCloudSpace()
        Assert.assertNotNull(tipText)
        val tips = (res?.getString(R.string.cloud_space_not_limit) ?: "") + HALF_SPACE
        Assert.assertEquals(tips, tipText.first)

        val linkText = res?.getString(R.string.upgrade_cloud_space) ?: ""
        Assert.assertEquals(linkText, tipText.second)
    }

    @Test
    fun should_when_getQueryingText() {
        val tipText = TipStatus.QUERY.getQueryingText()
        Assert.assertNotNull(tipText)
        val tips = res?.getString(R.string.cloudkit_querying) ?: ""
        Assert.assertEquals(tips, tipText.first)
    }

    @Test
    fun should_when_getSyncingText() {
        val tipText = TipStatus.SYNCING.getSyncingText()
        Assert.assertNotNull(tipText)
        val tips = res?.getString(R.string.cloudkit_syncing) ?: ""
        Assert.assertEquals(tips, tipText.first)
    }

    @Test
    fun should_when_getCompletedText() {
        val tipText = TipStatus.COMPLETED.getCompletedText()
        Assert.assertNotNull(tipText)
        val tips = res?.getString(R.string.cloudkit_syncing_complete, "") ?: ""
        Assert.assertEquals(tips, tipText.first)

        val linkText = res?.getString(com.soundrecorder.common.R.string.cloud_recordings_reason) ?: ""
        Assert.assertEquals(linkText, tipText.second)
    }

}