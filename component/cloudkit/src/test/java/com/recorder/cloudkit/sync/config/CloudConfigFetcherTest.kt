/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: CloudConfigFetcherTest
 * Description:
 * Version: 1.0
 * Date: 2022/7/28
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/7/28 1.0 create
 */

package com.recorder.cloudkit.sync.config

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.google.gson.Gson
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.heytap.cloudkit.libsync.ext.ICloudRecoveryMetaData
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord
import com.heytap.cloudkit.libsync.service.CloudDataType
import com.soundrecorder.base.BaseApplication
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.CloudSyncAgent
import com.recorder.cloudkit.sync.RecordSyncChecker
import com.recorder.cloudkit.sync.bean.CloudSyncResult
import com.recorder.cloudkit.sync.bean.SyncCheckResult
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.ArgumentMatchers.anyString
import org.mockito.ArgumentMatchers.any
import org.mockito.ArgumentMatchers.anyInt
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.mockito.Mockito.mock
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import java.lang.IllegalArgumentException

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CloudConfigFetcherTest {
    private var mManagerMockedStatic: MockedStatic<CloudSyncManager>? = null
    private val context: Context by lazy { ApplicationProvider.getApplicationContext() }
    private var mockedApplication: MockedStatic<BaseApplication>? = null

    @Before
    fun setUp() {
        mockedApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockedApplication?.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(context)
        mManagerMockedStatic = Mockito.mockStatic(CloudSyncManager::class.java).apply {
            `when`<Any> { CloudSyncManager.getInstance() }.thenReturn(mock(
                CloudSyncManager::class.java))
        }
    }

    @After
    fun release() {
        mManagerMockedStatic?.close()
        mockedApplication?.close()
        mManagerMockedStatic = null
        mockedApplication = null
    }

    @Test
    fun should_errorForManualStop_when_getCloudConfig() {
        CloudSyncAgent.getInstance().setSyncStop(true, SyncErrorCode.RESULT_CANCEL)
        CloudConfigFetcher.getCloudConfig(object : CloudConfigFetcher.ConfigCallback {
            override fun onSuccess(data: List<CloudConfigBean>) {
                throw IllegalArgumentException()
            }

            override fun onError(error: CloudSyncResult) {
                Assert.assertNotNull(error)
                Assert.assertEquals(SyncErrorCode.RESULT_CANCEL, error.errorCode)
            }
        })
        CloudSyncAgent.release()
    }

    @Test
    fun should_errorForSwitchClose_when_getCloudConfig() {
        val stateHelperMockedStatic = Mockito.mockStatic(CloudSynStateHelper::class.java)
        stateHelperMockedStatic.`when`<Boolean> { CloudSynStateHelper.isLoginFromCache() }.thenReturn(false)

        CloudConfigFetcher.getCloudConfig(object : CloudConfigFetcher.ConfigCallback {
            override fun onSuccess(data: List<CloudConfigBean>) {
                throw IllegalArgumentException()
            }

            override fun onError(error: CloudSyncResult) {
                Assert.assertNotNull(error)
                Assert.assertEquals(SyncErrorCode.RESULT_NOT_LOGIN, error.errorCode)
            }
        })

        stateHelperMockedStatic.close()
    }

    @Test
    fun should_successCallBack_when_getCloudConfig() {
        val mockRecordSyncChecker = Mockito.mockStatic(RecordSyncChecker::class.java)
        mockRecordSyncChecker.`when`<SyncCheckResult> { RecordSyncChecker.checkCloudSwitchState(context, true) }.thenReturn(SyncCheckResult())


        val gsonMock = mock(Gson::class.java)
        Whitebox.setInternalState(CloudConfigFetcher::class.java, "mGson", gsonMock)
       /* `when`(gsonMock.fromJson<CloudConfigBean>(anyString(), any())).thenReturn(CloudConfigBean("").apply {
            value = ""
        })*/
        mManagerMockedStatic?.`when`<Any> {
            CloudSyncManager.getInstance().startRecoveryMetaData(anyString(), anyString(), any(), any(), anyInt(), any())
        }?.thenAnswer {
            val data = CloudMetaDataRecord().apply {
                fields = "{\"description\":\"查看云端数据\",\"region\":\n" +
                        ", \"value\":\"{\\\"linkUrl\\\":\\\"http://sg-ocloud-webv2-test.wanyol.com\\\"，\\\"type\\\":\\\"h5\\\",\\\"order\\\":1}\", " +
                        "\"version\":\"1.0\", \"key\":\"record.ocloudH5\"}"
            }
            (it.arguments[5] as? ICloudRecoveryMetaData)?.onSuccess(it.arguments[3] as CloudDataType,
                mutableListOf<CloudMetaDataRecord?>().apply { add(data) },
                false,
                0,
                0)
        }

        CloudConfigFetcher.getCloudConfig(object : CloudConfigFetcher.ConfigCallback {
            override fun onSuccess(data: List<CloudConfigBean>) {
                Assert.assertNotNull(data)
            }

            override fun onError(error: CloudSyncResult) {
                throw IllegalArgumentException()
            }
        })
        mockRecordSyncChecker.close()
    }

    @Test
    fun should_errorCallBack_when_getCloudConfig() {
        val mockRecordSyncChecker = Mockito.mockStatic(RecordSyncChecker::class.java)
        mockRecordSyncChecker.`when`<SyncCheckResult> { RecordSyncChecker.checkCloudSwitchState(context, true) }.thenReturn(SyncCheckResult())

        mManagerMockedStatic?.`when`<Any> {
            CloudSyncManager.getInstance().startRecoveryMetaData(anyString(), anyString(), any(), any(), anyInt(), any())
        }?.thenAnswer {
            (it.arguments[5] as? ICloudRecoveryMetaData)?.onError(it.arguments[3] as CloudDataType, CloudKitError.GET_FILE_RSP_NETWORK_ERROR)
        }

        CloudConfigFetcher.getCloudConfig(object : CloudConfigFetcher.ConfigCallback {
            override fun onSuccess(data: List<CloudConfigBean>) {
                throw IllegalArgumentException()
            }

            override fun onError(error: CloudSyncResult) {
                Assert.assertNotNull(error)
            }
        })

        mockRecordSyncChecker.close()
    }
}