package com.recorder.cloudkit.account

import android.os.Build
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import org.junit.Assert
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class AccountBeanTest {

    @Test
    fun should_when_constructor() {
        val accountBean = AccountBean(false)

        Assert.assertNotNull(accountBean)
        Assert.assertFalse(accountBean.isLogin)
        Assert.assertNull(accountBean.userId)

        accountBean.userId = "1111"
        Assert.assertNotNull(accountBean.userId)
    }
}