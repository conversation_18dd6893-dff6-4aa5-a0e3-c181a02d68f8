package com.recorder.cloudkit.util

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.heytap.cloudkit.libcommon.netrequest.CloudHttpStatusCode.BizFocusServerCode
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.recorder.cloudkit.shadows.ShadowFeatureOption
import com.recorder.cloudkit.utils.CloudKitErrorExt.calFinalErrorCode
import com.recorder.cloudkit.utils.CloudKitErrorExt.canContinueSyncProcess
import com.recorder.cloudkit.utils.CloudKitErrorExt.canRetry
import com.recorder.cloudkit.utils.CloudKitErrorExt.logMessage
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class])
class CloudKitErrorExtTest {
    private var mContext: Context? = null

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
    }

    @Test
    fun should_return_false_when_canTry() {
        val kitError = CloudKitError.createNotLoginError()
        Assert.assertFalse(kitError.canRetry(mContext!!))
        val kitErrora = CloudKitError.createStopError(
            CloudKitError.ERROR_BACKUP_RESPONSE_NULL,
            BizFocusServerCode.HTTP_REQUEST_TOO_FREQUENT
        )
        Assert.assertFalse(kitErrora.canRetry(mContext!!))
    }

    @Test
    fun should_return_true_when_canTry() {
        val kitError = CloudKitError.createStopError(
            CloudKitError.ERROR_BACKUP_RESPONSE_NULL,
            BizFocusServerCode.HTTP_IO_SERVER_ERROR
        ).apply {
            subServerErrorCode = BizFocusServerCode.HTTP_IO_SERVER_ERROR
        }
        Assert.assertTrue(kitError.canRetry(mContext!!))
    }

    @Test
    fun should_return_true_when_canContinueSyncProcess() {
        var kitError = CloudKitError.createSuccess()
        Assert.assertTrue(kitError.canContinueSyncProcess(mContext!!))

        kitError = CloudKitError.createStopError(
            CloudKitError.ERROR_BACKUP_RESPONSE_NULL,
            BizFocusServerCode.HTTP_REQUEST_TOO_FREQUENT
        )
        Assert.assertFalse(kitError.canContinueSyncProcess(mContext!!))

        kitError = CloudKitError.createNotLoginError()
        Assert.assertFalse(kitError.canContinueSyncProcess(mContext!!))
    }

    @Test
    fun should_return_correct_when_calFinalErrorCode() {
        var kitError = CloudKitError.createNotLoginError()
        Assert.assertEquals(
            BizFocusServerCode.HTTP_CLIENT_NOT_LOGIN_IN,
            kitError.calFinalErrorCode()
        )

        kitError = CloudKitError.createStopError(CloudKitError.ERROR_BACKUP_STOPPED_LIMIT, 999)
        Assert.assertEquals(999, kitError.calFinalErrorCode())
    }

    @Test
    fun should_nonNull_when_logMessage() {
        val kitError = CloudKitError.createNotLoginError()
        Assert.assertNotNull(kitError.logMessage())
    }
}