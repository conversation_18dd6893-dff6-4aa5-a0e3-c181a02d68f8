package com.recorder.cloudkit.tipstatus.dialog

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.net.ConnectivityManager

class NetWorkStatusRecevier(private val callBack: () -> Unit) : BroadcastReceiver() {

    override fun onReceive(p0: Context?, p1: Intent?) {
        if (p1?.action == ConnectivityManager.CONNECTIVITY_ACTION) {
            callBack.invoke()
        }
    }
}