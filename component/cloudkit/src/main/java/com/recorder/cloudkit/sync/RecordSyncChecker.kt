package com.recorder.cloudkit.sync

import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.PowerManager
import android.os.SystemClock
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.recorder.cloudkit.global.CloudGlobalStateManager
import com.recorder.cloudkit.sync.bean.SyncCheckResult
import com.recorder.cloudkit.sync.bean.constant.SyncErrorCode
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.recorder.cloudkit.utils.CloudPermissionUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.StorageUtil
import com.soundrecorder.base.utils.SyncTimeUtils
import kotlin.math.abs

object RecordSyncChecker {
    const val TAG = "CloudSyncChecker"
    const val SP_KEY_LAST_FULL_RECOVERY_TIME = "sp_key_last_full_recovery_time"
    const val TIME_HALF_ONE_SECONDS = 500

    /*本地可用空间最小值*/
    const val LOCAL_MIN_SPACE = 300 * 1024 * 1024L

    /*电池充电中，满足最低电量值*/
    const val BATTERY_CHARGING_MIN_TEMP = 10

    /*电池未充电中，满足最低电量值*/
    const val BATTERY_UN_CHARGING_MIN_TEMP = 20

    /*电池温度最高值*/
    const val BATTERY_MAX_TEMPERATURE = 40

    /**
     * 是否需要录音媒体库全量对比
     */
    @JvmStatic
    fun checkLastEnterInOneHour(appContext: Context): Boolean {
        val lastEnterTime = PrefUtil.getSharedPreferences(appContext)?.getLong(PrefUtil.KEY_LAST_ENTER_TIME, 0L) ?: return false
        if (lastEnterTime <= 0) {
            DebugUtil.i(TAG, "checkLastEnterInOneHour  lastEnterTime <= 0,return false", true)
            return false
        }
        // intervalTime再手机重启后进入，差值为负数，需要使用绝对值
        val intervalTime = abs(SystemClock.elapsedRealtime() - lastEnterTime)
        DebugUtil.i(TAG, "checkLastEnterInOneHour  intervalTime ${SystemClock.elapsedRealtime() - lastEnterTime}", true)
        // 差值大于 500ms，避免 onResume 触发了媒体库对比全量
        if ((intervalTime >= TIME_HALF_ONE_SECONDS) && (intervalTime < SyncTimeUtils.TIME_1_HOUR)) {
            DebugUtil.i(TAG, "checkLastEnterInOneHour  return true", true)
            return true
        }
        return false
    }

    /**
     * 是否需要全量拉取云端数据
     */
    @JvmStatic
    fun checkNeedFullRecovery(appContext: Context): Boolean {
        val lastFullSyncTime = PrefUtil.getSharedPreferences(appContext)?.getLong(SP_KEY_LAST_FULL_RECOVERY_TIME, 0L) ?: return false
        val intervalTime = SystemClock.elapsedRealtime() - lastFullSyncTime
        if ((lastFullSyncTime == 0L) || (intervalTime > SyncTimeUtils.TIME_1_HOUR)) {
            return true
        }
        return false
    }

    @JvmStatic
    fun updateFullRecoveryTime(appContext: Context) {
        PrefUtil.getSharedPreferences(appContext)?.edit()?.apply {
            putLong(SP_KEY_LAST_FULL_RECOVERY_TIME, SystemClock.elapsedRealtime()).commit()
        }
    }

    /**
     * 检查云同步开关状态、网络是否满足
     * @param getRealSwitch 是否获取实时开关状态
     * true：从SDK拿当前真实的开关状态
     * false：从TipStatusManager中获取当前进程页面中的开关状态
     */
    @JvmStatic
    fun checkCloudSwitchState(context: Context, getRealSwitch: Boolean = true): SyncCheckResult {
        if (!CloudSynStateHelper.isLoginFromCache()) {
            return SyncCheckResult(SyncErrorCode.RESULT_NOT_LOGIN)
        }

        var syncSwitchState = 0
        if (getRealSwitch) {
            val getSyncSwitchResult = CloudSyncManager.getInstance().syncSwitchCompat
            DebugUtil.d(TAG, "checkSwitchState：  getSyncSwitchResult:$getSyncSwitchResult", true)
            if (!getSyncSwitchResult.cloudKitError.isSuccess) {
                DebugUtil.e(TAG, "checkSwitchState：" + " error " + getSyncSwitchResult.cloudKitError)
                return SyncCheckResult(SyncErrorCode.RESULT_SWITCH_ERROR)
            }
            syncSwitchState = getSyncSwitchResult.switchState
        } else {
            syncSwitchState = TipStatusManager.syncSwitch
        }
        if (syncSwitchState == SwitchState.CLOSE.state) {
            DebugUtil.i(TAG, "checkSwitchState: switch closed ")
            return SyncCheckResult(SyncErrorCode.RESULT_SWITCH_CLOSE)
        }
        val netState = NetworkUtils.getNetState(context)
        if (netState == NetworkUtils.NETWORK_NONE) {
            DebugUtil.i(TAG, "getNetState: no network ", true)
            //只要网络状态为none均不可可同步。
            return SyncCheckResult(SyncErrorCode.RESULT_NETWORK_NO_CONNECT)
        }
        if ((syncSwitchState == SwitchState.OPEN_ONLY_WIFI.state) && (netState != NetworkUtils.NETWORK_WIFI)) {
            //开关状态为仅wifi可同步，只要网络状态不为wifi均不可同步。
            DebugUtil.i(TAG, "checkSwitchState： netState is not match,netState: $netState", true)
            return SyncCheckResult(SyncErrorCode.RESULT_NETWORK_TYPE_MISMATCH)
        }
        if (!CloudGlobalStateManager.canDoSyncProcess()) {
            return SyncCheckResult(CloudGlobalStateManager.stateToErrorCode())
        }
        return SyncCheckResult()
    }


    /**
     * @param checkLocalStorage 是否check条件4
     * 检查业务条件：
     * 1.权限 开启存储、文件管理权限
     * 2.省电模式
     * 3.电量、电池温度>40°
     * 4.本地空间 > 300M
     */
    @JvmStatic
    fun checkSyncPreconditionsMet(context: Context, checkLocalStorage: Boolean = false): SyncCheckResult {
        // check permission
        val permissionResult = checkPermission(context)
        if (!permissionResult.success()) {
            return permissionResult
        }

        //check power saving mode
        if (isPowerSaveMode(context)) {
            DebugUtil.e(TAG, "power saving mode")
            return SyncCheckResult(SyncErrorCode.RESULT_POWER_SAVING_MODE)
        }

        // check power level and temperature
        val batteryInfo = getBatteryInfo(context)
        DebugUtil.i(TAG, "get battery info is:$batteryInfo", true)
        if (batteryInfo != null) {
            // 温度过高
            // 温度过高 如果当前过滤高温为true
            if (!CloudSyncAgent.getInstance().ignoreCheckHighTemperature && batteryInfo.temperature > BATTERY_MAX_TEMPERATURE * 10) {
                DebugUtil.e(TAG, "battery temperature more than $BATTERY_MAX_TEMPERATURE")
                return SyncCheckResult(SyncErrorCode.RESULT_TEMPERATURE_HIGH)
            }
            val isCharging: Boolean = batteryInfo.chargeStatus == BatteryManager.BATTERY_STATUS_CHARGING
                    || batteryInfo.chargeStatus == BatteryManager.BATTERY_STATUS_FULL
            //  充电：电量小于10%
            if (isCharging && batteryInfo.batteryLevel < BATTERY_CHARGING_MIN_TEMP) {
                DebugUtil.e(TAG, "battery is charging, battery level is less than $BATTERY_CHARGING_MIN_TEMP")
                return SyncCheckResult(SyncErrorCode.RESULT_LOW_BATTERY_CHARGING)
            }
            // 未充电：电量小于20%
            if (!isCharging && batteryInfo.batteryLevel < BATTERY_UN_CHARGING_MIN_TEMP) {
                DebugUtil.e(TAG, "battery is not charging, battery level is less than $BATTERY_UN_CHARGING_MIN_TEMP")
                return SyncCheckResult(SyncErrorCode.RESULT_LOW_BATTERY)
            }
        }

        // check local storage available
        if (checkLocalStorage && !isLocalStorageAvailable(context)) {
            DebugUtil.e(TAG, " local storage less $LOCAL_MIN_SPACE")
            return SyncCheckResult(SyncErrorCode.RESULT_LOCAL_INSUFFICIENT_SPACE)
        }
        return SyncCheckResult()
    }

    @JvmStatic
    fun checkPermission(context: Context): SyncCheckResult {
        if (!CloudPermissionUtils.hasCloudRequirePermission()) {
            DebugUtil.e(TAG, "check permission error")
            return SyncCheckResult(SyncErrorCode.RESULT_PERMISSION_DENIED)
        }
        return SyncCheckResult()
    }

    /**
     * 是否有网络连接
     */
    @JvmStatic
    fun hasInternetConnect(context: Context) = NetworkUtils.getNetState(context) != NetworkUtils.NETWORK_NONE

    /**
     * 本地空间是否够用，目前判断 300M，
     */
    @JvmStatic
    fun isLocalStorageAvailable(context: Context): Boolean =
        StorageUtil.getAvailableSpace(context) >= LOCAL_MIN_SPACE


    /**
     * 获取电池相关信息，如电量、温度等信息
     */
    @JvmStatic
    fun getBatteryInfo(context: Context): BatteryInfo? {
        val intentFilter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        context.registerReceiver(null, intentFilter)?.let { batteryStatus ->
            return BatteryInfo().apply {
                batteryLevel = batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
                scale = batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
                lowBattery = batteryStatus.getBooleanExtra(BatteryManager.EXTRA_BATTERY_LOW, false)
                chargeStatus = batteryStatus.getIntExtra(BatteryManager.EXTRA_STATUS, -1)
                temperature = batteryStatus.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, -1)
            }
        }
        return null
    }

    /**
     * 获取手机是否处于省电模式中
     */
    @JvmStatic
    fun isPowerSaveMode(context: Context): Boolean =
        (context.getSystemService(Context.POWER_SERVICE) as PowerManager).isPowerSaveMode

    class BatteryInfo {
        var batteryLevel: Int = -1 // 剩余电量
        var scale: Int = -1
        var lowBattery: Boolean = false// 是否为低电量
        var chargeStatus: Int = -1 // 根据状态值判断是否充电
        var temperature: Int = -1 // 电池温度

        override fun toString(): String {
            return "BatteryInfo(batteryLevel=$batteryLevel, scale=$scale, lowBattery=$lowBattery, chargeStatus=$chargeStatus, temperature=$temperature)"
        }
    }
}