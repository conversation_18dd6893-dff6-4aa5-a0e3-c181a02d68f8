package com.recorder.cloudkit.api

import android.content.Context
import androidx.lifecycle.LiveData
import com.recorder.cloudkit.oldcompat.CloudPermissionActivity
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.ui.SettingRecordSyncActivity
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.buryingpoint.RecorderUserAction
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudTipManagerAction
import com.soundrecorder.modulerouter.cloudkit.SYNC_TYPE_RECOVERY_START_APP
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ICloudSwitchChangeListener
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ITipStatus
import com.soundrecorder.modulerouter.utils.Injector

object CloudTipManagerApi : CloudTipManagerAction {
    private const val TAG = "CloudTipManagerApi"

    /**
     * 初始化云同步相关初始值，如：登录状态、开关状态
     */
    override fun initCloudState() {
        TipStatusManager.init()
    }

    /**
     * 注册云同步开关变化通知
     */
    override fun registerCloudSwitchChangeListener(listener: ICloudSwitchChangeListener?) {
        DebugUtil.i(TAG, "registerCloudSwitchChangeListener")
        TipStatusManager.registerCloudListener(listener)
    }

    /**
     * 反注册云同步开关变化通知
     */
    override fun unregisterCloudSwitchChangeListener(listener: ICloudSwitchChangeListener?) {
        TipStatusManager.unregisterCloudToggleListener(listener)
    }

    /**
     * 获取云同步状态
     */
    override fun getCloudStatusLiveData(): LiveData<ITipStatus> {
        DebugUtil.i(TAG, "getCloudStatusLiveData")
        return TipStatusManager.tipStatusLiveData
    }

    /**
     * 获取云同步结果码
     */
    override fun getCloudSyncResultCode(): Int {
        DebugUtil.i(TAG, "getCloudSyncResultCode")
        return TipStatusManager.syncResult
    }

    /**
     * 云同步开关装填
     * true：开启
     * false：关闭
     */
    override fun isCloudSwitchOn(): Boolean {
        return TipStatusManager.isCloudOn()
    }

    /**
     * 打开云同步开关设置页面
     */
    override fun launchCloudSettingPage(context: Context?) {
        DebugUtil.i(TAG, "launchCloudSettingPage")
        BuryingPoint.addRecordSettingIconClick(RecorderUserAction.VALUE_NAME_RECORD_CLOUD, RecorderUserAction.VALUE_OPTION_RECORD_TYPE_DEFAULT)
        TipStatusManager.toCloudSetting(context)
    }

    /**
     * 打开云同步开关设置页面
     */
    override fun getRecordCloudSettingActivityName(): String {
        return SettingRecordSyncActivity::class.java.name
    }

    override fun getCloudPermissionActivityName(): String {
        return CloudPermissionActivity::class.java.name
    }

    override fun checkSyncAbnormalStop(context: Context): Boolean {
        DebugUtil.i(TAG, "checkSyncAbnormalStop")
        var isTrig = false
        // 不满足媒体库全量对比条件，是否满足 主动触发一次同步要求
        TipStatusManager.apply {
            if (needSyncCount > 0) {
                if (syncResult in ABNORMAL_STOP_SYNC_CODES) {
                    DebugUtil.i(TAG, "trig sync cloud onResume sync result in abnormal stop sync reason.")
                    Injector.injectFactory<CloudKitInterface>()?.trigCloudSync(context, SYNC_TYPE_RECOVERY_START_APP)
                    isTrig = true
                }
            }
        }
        return isTrig
    }

    override fun isNeedShowCloudGuide(): Boolean {
        return TipStatusManager.isNeedShowGuide()
    }

    override fun isRefreshUIOfSync(): Boolean {
        return TipStatusManager.isRefreshUIOfSync()
    }

    override fun isSyncing(): Boolean {
        return TipStatusManager.isSyncing()
    }

    override fun checkNeedSyncFullRecovery(): Boolean {
        return TipStatusManager.checkNeedSyncFullRecovery()
    }

    override fun isLoginFromCache(): Boolean {
        return CloudSynStateHelper.isLoginFromCache()
    }

    override fun accountRequestLogin(context: Context, callback: ((Boolean) -> Unit)?) {
        return CloudSynStateHelper.accountRequestLogin(context, callback)
    }

    override fun releaseAccountReqCallback() {
        CloudSynStateHelper.releaseAccountReqCallback()
    }
}