package com.recorder.cloudkit.sync.bean.constant;

import com.heytap.cloudkit.libcommon.netrequest.CloudHttpStatusCode;
import com.heytap.cloudkit.libcommon.netrequest.error.CloudBizError;

/**
 * 基于以下：录音自定义状态码范围定义在44000-50000
 * 1、基础服务状态码[0-999]
 * 2、 业务状态码 ODS[1000,1999]
 * 3、 业务状态码 switch[3000,3999]
 * bizErrorCode:目前0-12
 * subServerErrorCode：服务端返回的错误码，9000+的范围需要认下(目前9400-9600)；
 * 6位数的几个错误码是SDK本地拦截后返回的，范围是100000-101000
 * bizSubErrorCode：业务方自己透传过来的子错误码，用于接口调用场景判断的，目前只有快稳省stop传递limitErrorCode
 */
public class SyncErrorCode {
    public static final int RESULT_SUCCESS = CloudBizError.SUCCESS.getCode();
    public static final int RESULT_FAIL = CloudBizError.FAIL.getCode();
    /*网络异常,如弱网、超时*/
    public static final int RESULT_NETWORK_ERROR = CloudBizError.NETWORK.getCode();
    /*取消同步，业务方手动调用停止*/
    public static final int RESULT_CANCEL = CloudBizError.MANUAL_STOP.getCode();

    /*录音自定义错误码*/
    /*该状态对应ui显示(云数据下载过程)：“正在查询云端数据…”
     * 一个完整流程，只能显示一次，不能跟 正在同步 交替显示，
     * so 1. 正常流程仅第一页元数据下载过程为 UI_STATE_QUERYING，其他都为UI_STATE_SYNCING
     * 2. 有补刀操作（优先下载上次未下载文件）直接为UI_STATE_SYNCING，无UI_STATE_QUERYING状态
     * */
    public static final int UI_STATE_QUERYING = 44000;
    /*该状态对应ui显示（元数据上传、文件上传下载过程）：“ 数据正在同步...“*/
    public static final int UI_STATE_SYNCING = 44001;
    /*没权限*/
    public static final int RESULT_PERMISSION_DENIED = 44100;
    /*无网络*/
    public static final int RESULT_NETWORK_NO_CONNECT = 44101;
    /*网络情况与实际开关所需不匹配*/
    public static final int RESULT_NETWORK_TYPE_MISMATCH = 44102;
    /*云同步开关关闭*/
    public static final int RESULT_SWITCH_CLOSE = 44103;
    /*获取云同步开关异常*/
    public static final int RESULT_SWITCH_ERROR = 44104;
    /*本地空间不足*/
    public static final int RESULT_LOCAL_INSUFFICIENT_SPACE = 44109;
    /*开启省电模式*/
    public static final int RESULT_POWER_SAVING_MODE = 46000;
    /*低电量*/
    public static final int RESULT_LOW_BATTERY = 46001;
    /*低电量充电中*/
    public static final int RESULT_LOW_BATTERY_CHARGING = 46002;
    /*手机温度过高*/
    public static final int RESULT_TEMPERATURE_HIGH = 46003;

    /*===============业务方必须处理的服务器错误码================*/
    /*账号token 鉴权失败， 业务方需要调用账号sdk 重新刷新token*/
    public static final int RESULT_AUTH_ERROR = CloudHttpStatusCode.BizFocusServerCode.HTTP_INVALID_USER_TOKEN;
    /*客户端本地检查请求时是否登陆，未登录返回该错误， 业务方需要跳转账号登陆界面*/
    public static final int RESULT_NOT_LOGIN = CloudHttpStatusCode.BizFocusServerCode.HTTP_CLIENT_NOT_LOGIN_IN;
    /*云端用户空间不足， 业务方需要提示给用户*/
    public static final int RESULT_INSUFFICIENT_SPACE = CloudHttpStatusCode.BizFocusServerCode.HTTP_NO_CLOUD_SPACE;
    /*数据冷存储,用户的资源被锁定，业务方需要处理这种用户的资源暂不可用的状态*/
    public static final int RESULT_DATA_COLD_STANDBY = CloudHttpStatusCode.BizFocusServerCode.HTTP_DATA_COLD_STANDBY;
    /*---------------------------------逻辑层处理，不会抛给UI层错误码--------------------------------------------*/
    // BACK备份元数据errorData错误码
    // 1. errorCode = 1103, RECORDTYPE_NOTMATCH, recordtype不匹配
    // 2. errorCode = 1104, SYS_VERSION_ISNULL, sysVersion为空(deleteAndReplace/replace/recycleAndReplace/resumeAndReplace时会出现)
    // 3. errorCode = 1107, OPERATOR_TYPE_ISNULL, operator_type is null
    // 4. errorCode = 1108, FILE_UPLOAD_EXPIRED, 文件上传过期，即文件上传很久后才上传对应的元数据，此时云端文件会被删除，需重新上传文件（该过期时间由后台控制）
    // 5. errorCode = 1109, FILE_UPLOAD_NOTMATCH, 文件上传数据不匹配，上传元数据时云端解密fileCheckPayload字段拿到的ocloudId和上传的ocloudId不匹配
    // 6. errorCode = 1110, DATA_TYPE_NOTMATCH, datatype不匹配
    // 7. errorCode = 1111, FIELDS_NULL, fields信息为空
    // 8. errorCode = 1114, RECORD_FIELDS_FORMAT_ERROR, fields格式不合法
    // 9. errorCode = 1200, EXISTS, 尝试创建资源已存在(create时sysRecordId与云端数据重复)
    // 10. errorCode = 1201, NOT_FOUND, 上传数据的sysRecordId在云端不存在(deleteAndReplace/replace/recycleAndReplace/resumeAndReplace)
    // 11. errorCode = 1202, NEED_FETCH, 上传的sysVersion小于云端的数据, 需要先fetch(deleteAndReplace/replace/recycleAndReplace/resumeAndReplace)
    /*1108：提交元数据时候，附带文件过期了，需要进行重新上传*/
    public static final int ERROR_RECORD_FILE_UPLOAD_EXPIRED = CloudHttpStatusCode.BizFocusServerCode.HTTP_RECORD_FILE_UPLOAD_EXPIRED;
    /*1202：备份元数据时候，发现云端对应云端数据有变动，需要进行恢复拉取下元数据*/
    public static final int ERROR_RECORD_NEED_FETCH = CloudHttpStatusCode.BizFocusServerCode.HTTP_RECORD_NEED_FETCH;
    // 1200：上传元数据,云端存在相同sysRecordId数据
    public static final int ERROR_SAME_SYS_RECORD_ID = 1200;
    public static final int ERROR_SYS_RECORD_ID_NOT_FOUND = 1201;
    public static final int RECORD_FIELDS_FORMAT_ERROR = 1114;
    public static final int ERROR_FIELDS_NULL = 1111;
    public static final int ERROR_DATA_TYPE_NOTMATCH = 1110;
    public static final int ERROR_FILE_UPLOAD_NOTMATCH = 1109;
    public static final int OPERATOR_TYPE_ISNULL = 1107;
    public static final int SYS_VERSION_ISNULL = 1104;
    public static final int RECORDTYPE_NOTMATCH = 1103;

    // 传入sysuniqueid is null
    public static final int BACK_UP_ERROR_UNIQUEID_IS_NULL = 1203;
    // 云端已存在相同uniqueid数据
    public static final int BACK_UP_ERROR_UNIQUEID_EXIST = 1204;


    /*===============业务方必须处理的服务器错误码================*/

    /*=============== 服务端返回可重试错误码start（对应subServerErrorCode）================*/
    /*请求频繁，限流,需要按照服务端返回的delay时间去delay重试*/
    public static final int RESULT_REQUEST_TOO_FREQUENT = CloudHttpStatusCode.BizFocusServerCode.HTTP_REQUEST_TOO_FREQUENT;
    /*全局拒绝访问时间段错误码 , 按照服务端返回的delay时间去delay重试*/
    public static final int RESULT_SERVER_LIMIT_DURATION = CloudHttpStatusCode.BizFocusServerCode.HTTP_SERVER_LIMIT_DURATION;
    /*IO传输文件接口 服务端一般性内部逻辑错误，客户端可重试*/
    public static final int RESULT_IO_SERVER_ERROR = CloudHttpStatusCode.BizFocusServerCode.HTTP_IO_SERVER_ERROR;
    /*IO传输文件接口 服务端内部服务调用处理超时，客户端可重试*/
    public static final int RESULT_IO_SERVER_TIMEOUT = CloudHttpStatusCode.BizFocusServerCode.HTTP_IO_SERVER_TIMEOUT;
    /*IO传输文件接口 oos保存文件元数据失败，客户端可重试*/
    public static final int RESULT_IO_SAVE_FILE_ERROR = CloudHttpStatusCode.BizFocusServerCode.HTTP_IO_SAVE_FILE_ERROR;
    /*元数据请求受到限制，请稍后重试*/
    public static final int RESULT_METADATA_LIMIT_ERROR = CloudHttpStatusCode.BizFocusServerCode.HTTP_METADATA_LIMIT_ERROR;
    /*元数据请求内部错误，请稍后重试*/
    public static final int RESULT_METADATA_INNER_ERROR = CloudHttpStatusCode.BizFocusServerCode.HTTP_METADATA_INNER_ERROR;
    /*元数据迁移中，请稍后重试*/
    public static final int RESULT_METADATA_MIGRATION_ERROR = CloudHttpStatusCode.BizFocusServerCode.HTTP_METADATA_MIGRATION_ERROR;

    public static final int[] CAN_TRY_SERVER_ERROR_CODE = new int[]{RESULT_IO_SERVER_ERROR
            , RESULT_IO_SERVER_TIMEOUT, RESULT_IO_SAVE_FILE_ERROR, RESULT_METADATA_LIMIT_ERROR, RESULT_METADATA_INNER_ERROR, RESULT_METADATA_MIGRATION_ERROR};
    /**
     * 不同限流的错误码
     * 429 ：  服务端业务返回的delay限流
     * 100002：  ck根据 429下发的限流区间，直接ck拦截
     * 100000：  qps客户端根据服务端下发配置，限制多少时间内 最多个请求
     * 100001：全局拒绝访问时间段错误码 , 按照服务端下发配置，返回的delay时间去delay重试
     */
    public static final int[] SERVER_LIMITING_ERROR_CODE = new int[]{
            RESULT_REQUEST_TOO_FREQUENT,
            RESULT_SERVER_LIMIT_DURATION,
            CloudHttpStatusCode.HTTP_SERVER_LIMIT_QPS,
            CloudHttpStatusCode.HTTP_SERVER_LIMIT_IN_DELAY
    };

    /*===============服务端返回可重试错误码end（对应subServerErrorCode）================*/

    /**
     * 账号不可用云服务
     */
    public static final int CODE_ACCOUNT_DISABLE = 100007;
    /**
     * 设备不可用云服务
     */
    public static final int CODE_DEVICE_DISABLE = 100008;
}
