package com.recorder.cloudkit.sync

import android.content.Context
import android.os.Build
import androidx.annotation.WorkerThread
import com.heytap.cloud.sdk.base.CloudStatusHelper
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState
import com.heytap.cloudkit.libsync.cloudswitch.compat.CloudKitSwitchCompatUtil
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.recorder.cloudkit.CloudSyncConfigModule
import com.recorder.cloudkit.account.AccountBean
import com.recorder.cloudkit.account.AccountManager
import com.recorder.cloudkit.account.IAccountInfoCallback
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState.CLOSE
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState.OPEN_ALL
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState.OPEN_ONLY_WIFI

object CloudSynStateHelper {

    private const val TAG = "CloudSynStateHelper"
    private var accountReqLoginCallBack: IAccountInfoCallback? = null

    //是否支持云同步
    //系统分身不支持
    @JvmStatic
    fun isRegionCloudSupport(): Boolean {
        //主系统为true
        return (CloudSyncConfigModule.sRegionSupportCloud && BaseApplication.sIsMainSystem)
    }

    @JvmStatic
    fun getSwitchState(checkLogin: Boolean = true): Int {
        if (checkLogin && !isLoginFromCache()) {
            DebugUtil.i(TAG, "getSwitchState cloud is not login", true)
            return CloudSwitchState.NOT_LOGIN
        }
        val result = CloudSyncManager.getInstance().syncSwitchCompat
        if (result.cloudKitError.isSuccess) {
            return result.switchState
        } else {
            DebugUtil.i(TAG, "getSwitchState Cloud synchronous not successful ${result.switchState}", true)
        }
        return CloudSwitchState.CLOSE
    }

    /**
     * 获取老版本云同步开关查询方式
     */
    @JvmStatic
    fun getSwitchStateOldVersion(): Int {
        return CloudStatusHelper.query(BaseApplication.getAppContext(), TipStatusManager.RECORD_MODULE, CloudStatusHelper.Key.SYNC_SWITCH)
    }

    @JvmStatic
    fun isLogin(): Boolean {
        return AccountManager.sAccountManager.isLogin(BaseApplication.getAppContext())
    }

    @JvmStatic
    fun isLoginFromCache(): Boolean {
        return AccountManager.sAccountManager.isLoginFromCache(BaseApplication.getAppContext())
    }

    //判断当前云服务版本是否支持自己的开关
    @JvmStatic
    fun isSupportSwitch(): Boolean {
        val result = CloudKitSwitchCompatUtil.isSupportSwitch(BaseApplication.getAppContext())
        return result.isSuccess
    }

    @WorkerThread
    @JvmStatic
    fun setSyncSwitch(state: Int, report: Boolean = true): CloudKitError? {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val switchState = when (state) {
                OPEN_ALL -> SwitchState.OPEN_ALL
                OPEN_ONLY_WIFI -> SwitchState.OPEN_ONLY_WIFI
                CLOSE -> SwitchState.CLOSE
                else -> SwitchState.CLOSE
            }
            return CloudSyncManager.getInstance().setSyncSwitch(switchState, report)
        }
        return null
    }

    /**
     * 调用账号登录
     */
    @WorkerThread
    @JvmStatic
    fun accountRequestLogin(context: Context, callback: ((Boolean) -> Unit)? = null) {
        if (accountReqLoginCallBack == null) {
            accountReqLoginCallBack = object : IAccountInfoCallback {
                override fun onComplete(account: AccountBean) {
                    DebugUtil.i(TAG, "accountRequestLogin,onComplete login == ${account.isLogin}")
                    if (account.isLogin || CloudSynStateHelper.isLoginFromCache()) {
                        //openSwitchOn(true)
                        callback?.invoke(true)
                    } else {
                        callback?.invoke(false)
                        DebugUtil.i(TAG, "accountRequestLogin,reqSignInAccount: is failure")
                    }
                }
            }
        }
        AccountManager.sAccountManager.reqSignInAccount(context, accountReqLoginCallBack)
    }

    @JvmStatic
    fun releaseAccountReqCallback() {
        accountReqLoginCallBack = null
    }
}