package com.recorder.cloudkit.sync.ui

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import com.recorder.cloudkit.R
import com.recorder.cloudkit.global.CloudGlobalStateManager
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.flexible.FollowHandPanelUtils
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant

class SettingRecordSyncActivity : PrivacyPolicyBaseActivity() {

    companion object {
        private const val FRAGMENT_TAG = "SettingRecordSyncFragment"
        private const val TAG = "SettingRecordSyncActivity"
        /*用于识别,统计页面 勿改*/
        private const val FUNCTION_NAME = "SettingSync"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_setting_record_sync)
        supportFragmentManager.beginTransaction().replace(R.id.root_layout, getSettingFragment(), FRAGMENT_TAG).commit()
        FollowHandPanelUtils.checkActivityClickOutOfBounds(this, findViewById(R.id.root_layout))
    }

    override fun onTopResumedActivityChanged(isTopResumedActivity: Boolean) {
        super.onTopResumedActivityChanged(isTopResumedActivity)
        DebugUtil.i(TAG, "onTopResumedActivityChanged,isTop $isTopResumedActivity")
        if ((isTopResumedActivity) && (isInMultiWindowMode)) {
            refreshUIData()
        }
    }

    private fun getSettingFragment(): SettingRecordSyncFragment {
        var settingFragment: SettingRecordSyncFragment? =
            supportFragmentManager.findFragmentByTag(FRAGMENT_TAG) as SettingRecordSyncFragment?
        if (settingFragment == null) {
            settingFragment = SettingRecordSyncFragment.newInstance()
        }
        return settingFragment
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        DebugUtil.i(TAG, "onOptionsItemSelected item:" + item.itemId)
        if (ClickUtils.isFastDoubleClick()) {
            DebugUtil.i(TAG, "onOplusOptionsMenuItemSelected() isFastDoubleClick return")
            return false
        }
        when (item.itemId) {
            android.R.id.home -> {
                setResult(RESULT_CANCELED)
                finish()
            }
        }
        return super.onOptionsItemSelected(item)
    }

    private fun refreshUIData() {
        val fragments = supportFragmentManager.fragments
        for (fragment in fragments) {
            if (fragment is SettingRecordSyncFragment) {
                DebugUtil.i(TAG, " initCloudSync ")
                fragment.initCloudSync()
                break
            }
        }
    }

    override fun onPrivacyPolicySuccess(type: Int, pageFrom: Int?) {
        if (type == PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD) {
            openCloudSync(type)
        }

        if (type in intArrayOf(
                PrivacyPolicyConstant.TYPE_USER_NOTICE,
                PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC,
                PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL,
                PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS,
                PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE)) {
            refreshUIData()
        }
    }

    private fun openCloudSync(type: Int) {
        val fragments = supportFragmentManager.fragments
        for (fragment in fragments) {
            if (fragment is SettingRecordSyncFragment) {
                fragment.onPrivacyPolicySuccess(type)
                break
            }
        }
    }
}