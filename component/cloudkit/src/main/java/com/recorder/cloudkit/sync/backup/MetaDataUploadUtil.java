package com.recorder.cloudkit.sync.backup;

import static com.soundrecorder.common.constant.DatabaseConstant.RecordUri.RECORD_CONTENT_URI;
import static com.soundrecorder.common.constant.DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION;
import static com.soundrecorder.common.db.RecorderDBUtil.applyContentProviderOperation;
import static com.soundrecorder.common.db.RecorderDBUtil.getRecordData;

import android.content.ContentProviderOperation;
import android.content.ContentProviderResult;
import android.content.ContentValues;
import android.content.Context;

import com.google.gson.Gson;
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudBackupResponseRecord;
import com.heytap.cloudkit.libsync.netrequest.metadata.CloudMetaDataRecord;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.buryingpoint.CloudStaticsUtil;
import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.GroupInfoDbUtil;
import com.soundrecorder.common.db.RecorderDBUtil;
import com.recorder.cloudkit.sync.SyncDataConstants;
import com.recorder.cloudkit.sync.bean.CloudRecordField;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class MetaDataUploadUtil {
    public static final String TAG = "MetaDataUploadUtil";

    /**
     * GroupInfo元数据上传云端备份成功，更新本地数据库
     *
     * @param successData
     */
    public static void onServerAddedOrUpdateGroupInfoDbForBackup(Context context, List<CloudBackupResponseRecord> successData) {
        if ((successData == null) || (successData.isEmpty())) {
            return;
        }
        CloudStaticsUtil.addCloudLog(TAG, "onServerAddedOrUpdateGroupInfoDbForBackup, data.size=" + successData.size());
        final ArrayList<ContentProviderOperation> operations = new ArrayList<ContentProviderOperation>();
        for (CloudBackupResponseRecord kitRecord : successData) {
            switch (kitRecord.getOperatorType()) {
                // 云端彻底删除元数据-本地文件设置为私密文件
                case SyncDataConstants.OPERATOR_TYPE_DELETE:
                    // 云端数据移至云端回收站：本地删除音频文件
                case SyncDataConstants.OPERATOR_TYPE_RECYCLE:
                    // 本地删除同步到云端后，远端回调到此处
                    deleteFromGroupDb(context, kitRecord, operations);
                    break;
                // 新新增音频文件
                case SyncDataConstants.OPERATOR_TYPE_CREATE:
                    addGroupInfoDb(kitRecord, operations);
                    break;
                // 本地元数据有更新
                case SyncDataConstants.OPERATOR_TYPE_REPLACE:
                case SyncDataConstants.OPERATOR_TYPE_MODIFY:
                    updateGroupInfoDb(kitRecord, operations);
                    break;
                // 云数据从回收站恢复出来,录音不涉及此项
                case SyncDataConstants.OPERATOR_TYPE_RESUME:
                    DebugUtil.i(TAG, "onServerAddedOrUpdateGroupInfoDbForBackup operatorType is OPERATOR_TYPE_RESUME", true);
                    break;
                default:
                    DebugUtil.e(TAG, "onServerAddedOrUpdateGroupInfoDbForBackup operatorType is other:" + kitRecord.getOperatorType(), true);
                    break;
            }
        }

        ArrayList<ArrayList<ContentProviderOperation>> operationsList = RecorderDBUtil
                .splitForBatch(operations);
        applyContentProviderOperations(context, DatabaseConstant.AUTHORITY, operationsList);
    }

    /**
     * 元数据上传云端备份成功，更新本地数据库
     *
     * @param successData
     */
    public static void onServerAddedOrUpdateForBackup(Context context, List<CloudBackupResponseRecord> successData) {
        if ((successData == null) || (successData.isEmpty())) {
            return;
        }
        CloudStaticsUtil.addCloudLog(TAG, "onServerAddedOrUpdateForBackup, data.size=" + successData.size());
        final ArrayList<ContentProviderOperation> operations = new ArrayList<ContentProviderOperation>();
        for (CloudBackupResponseRecord kitRecord : successData) {
            switch (kitRecord.getOperatorType()) {
                // 云端彻底删除元数据-本地文件设置为私密文件
                case SyncDataConstants.OPERATOR_TYPE_DELETE:
                    // 云端数据移至云端回收站：本地删除音频文件
                case SyncDataConstants.OPERATOR_TYPE_RECYCLE:
                    //该数据云端已经被删除
                case SyncDataConstants.RESPONSE_OPERATOR_TYPE_DELETE:
                    // 本地删除同步到云端后，远端回调到此处
                    deleteFromRecorderDb(context, kitRecord, operations);
                    break;
                // 新新增音频文件
                case SyncDataConstants.OPERATOR_TYPE_CREATE:
                    addRecorderDb(kitRecord, operations);
                    break;
                // 本地元数据有更新
                case SyncDataConstants.OPERATOR_TYPE_REPLACE:
                case SyncDataConstants.OPERATOR_TYPE_MODIFY:
                    updateRecorderDb(kitRecord, operations);
                    break;

                // 云数据从回收站恢复出来,录音不涉及此项
                case SyncDataConstants.OPERATOR_TYPE_RESUME:
                    DebugUtil.i(TAG, "onServerAddedOrUpdateForBackup operatorType is OPERATOR_TYPE_RESUME", true);
                    break;
                default:
                    DebugUtil.e(TAG, "onServerAddedOrUpdateForBackup operatorType is other:" + kitRecord.getOperatorType(), true);
                    break;
            }
        }

        ArrayList<ArrayList<ContentProviderOperation>> operationsList = RecorderDBUtil
                .splitForBatch(operations);
        applyContentProviderOperations(context, DatabaseConstant.AUTHORITY, operationsList);
    }

    /**
     * 从回收站移到正常状态
     *
     * @param successData
     * @param metaDataRecordList
     */
    public static void onRecordResumeOperateForBackUp(Context context, List<CloudBackupResponseRecord> successData, List<CloudMetaDataRecord> metaDataRecordList) {
        if (successData == null || successData.isEmpty()) {
            DebugUtil.e(TAG, "onRecordResumeOperateForBackUp success size is 0");
            return;
        }
        if (metaDataRecordList == null || metaDataRecordList.isEmpty()) {
            DebugUtil.e(TAG, "onRecordResumeOperateForBackUp metaData size is 0");
            return;
        }
        CloudStaticsUtil.addCloudLog(TAG, "onRecordResumeOperateForBackUp, data.size=" + successData.size() + ",metaDataRecordList=" + metaDataRecordList.size());

        final ArrayList<ContentProviderOperation> operations = new ArrayList<ContentProviderOperation>();
        Map<String, CloudBackupResponseRecord> tempSuccessMap = new HashMap<>();
        for (CloudBackupResponseRecord responseRecord : successData) {
            tempSuccessMap.put(responseRecord.getSysRecordId(), responseRecord);
        }
        CloudBackupResponseRecord itemResponse;
        CloudRecordField itemFiled;
        Gson gson = new Gson();
        for (CloudMetaDataRecord metaDataRecord : metaDataRecordList) {
            itemResponse = tempSuccessMap.get(metaDataRecord.getSysRecordId());
            if (itemResponse != null) {
                try {
                    itemFiled = gson.fromJson(metaDataRecord.getFields(), CloudRecordField.class);
                    if (itemFiled != null) {
                        metaDataRecord.setSysVersion(itemResponse.getSysVersion());
                        updateRecorderDbForResume(itemFiled.getItemId(), metaDataRecord, operations);
                    } else {
                        DebugUtil.e(TAG, "onRecordResumeOperateForBackUp error itemFiled is null, " + itemResponse);
                    }
                } catch (Exception e) {
                    DebugUtil.e(TAG, "onRecordResumeOperateForBackUp itemResponse " + itemResponse + "\nerror " + e);
                }

            }
        }

        ArrayList<ArrayList<ContentProviderOperation>> operationsList = RecorderDBUtil
                .splitForBatch(operations);
        applyContentProviderOperations(context, DatabaseConstant.AUTHORITY, operationsList);
    }

    /**
     * 备份元数据类型为：Create
     *
     * @param kitRecord
     * @param operations
     */
    private static void addRecorderDb(CloudBackupResponseRecord kitRecord, ArrayList<ContentProviderOperation> operations) {
        if (kitRecord == null) {
            DebugUtil.e(TAG, "RecordEntityUtils.toRecord return null , break ");
            return;
        }
        final String uuId = kitRecord.getSysRecordId();
        final String globalId = String.valueOf(kitRecord.getSysRecordId());
        final Long sysVersion = kitRecord.getSysVersion();
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID + " = ?";
        String[] selectionArgs = new String[]{uuId};
        ContentProviderOperation operation = null;
        operation = ContentProviderOperation
                .newUpdate(DatabaseConstant.RecordUri.RECORD_CONTENT_URI)
                .withSelection(selection, selectionArgs)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID, globalId)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, RecordConstant.RECORD_NOT_DIRTY)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS, RecordConstant.SYNC_STATUS_BACKUP_MEGADATA_SUC)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS, RecordConstant.RECORD_PRIVATE_NORMAL)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION, sysVersion)
                .withYieldAllowed(true)
                .build();
        operations.add(operation);
    }

    /**
     * 备份元数据类型：update
     *
     * @param kitRecord
     * @param operations
     */
    private static void updateRecorderDb(CloudBackupResponseRecord kitRecord, ArrayList<ContentProviderOperation> operations) {
        if (kitRecord == null) {
            DebugUtil.e(TAG, "RecordEntityUtils.toRecord return null , break ");
            return;
        }
        DebugUtil.i(TAG, "updateRecorderDb " + kitRecord.getSysVersion(), true);
        final String globalId = String.valueOf(kitRecord.getSysRecordId());
        final Long sysVersion = kitRecord.getSysVersion();
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID + " = ?";
        String[] selectionArgs = new String[]{globalId};
        ContentProviderOperation operation = null;
        operation = ContentProviderOperation
                .newUpdate(DatabaseConstant.RecordUri.RECORD_CONTENT_URI)
                .withSelection(selection, selectionArgs)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID, globalId)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, RecordConstant.RECORD_NOT_DIRTY)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS, RecordConstant.SYNC_STATUS_BACKUP_MEGADATA_SUC)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS, RecordConstant.RECORD_PRIVATE_NORMAL)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION, sysVersion)
                .withYieldAllowed(true)
                .build();
        operations.add(operation);
    }

    private static void updateRecorderDbForResume(String uuId, CloudMetaDataRecord kitRecord, ArrayList<ContentProviderOperation> operations) {
        if (kitRecord == null) {
            DebugUtil.e(TAG, "RecordEntityUtils.toRecord return null , break ");
            return;
        }
        DebugUtil.i(TAG, "updateRecorderDbForResume,uuId " + uuId + " kitRecord " + kitRecord);
        final String globalId = String.valueOf(kitRecord.getSysRecordId());
        final Long sysVersion = kitRecord.getSysVersion();
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID + " = ?";
        String[] selectionArgs = new String[]{uuId};
        ContentProviderOperation operation = null;
        operation = ContentProviderOperation
                .newUpdate(DatabaseConstant.RecordUri.RECORD_CONTENT_URI)
                .withSelection(selection, selectionArgs)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID, globalId)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, RecordConstant.RECORD_NOT_DIRTY)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS, RecordConstant.SYNC_STATUS_BACKUP_MEGADATA_SUC)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS, RecordConstant.RECORD_PRIVATE_NORMAL)
                .withValue(DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION, sysVersion)
                .withYieldAllowed(true)
                .build();
        operations.add(operation);
    }

    /**
     * 备份元数据类型：delete
     *
     * @param kitRecord
     * @param operations
     */
    private static void deleteFromRecorderDb(Context context, CloudBackupResponseRecord kitRecord, ArrayList<ContentProviderOperation> operations) {
        if (kitRecord == null) {
            DebugUtil.e(TAG, "RecordEntityUtils.toRecord return null , break ");
            return;
        }
        DebugUtil.i(TAG, "onServerDeletedForBackup: kitRecord: " + kitRecord, true);
        final String globalId = String.valueOf(kitRecord.getSysRecordId());
        String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID + " = ?";
        // 删除数据库中记录(非加密文件)
        /**String[] selectionArgs;
         String deleteSelection = selection + " AND " + DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS + " = ?";
         String[] deleteSelectionArg = new String[]{globalId, String.valueOf(RECORD_PRIVATE_NORMAL)};*/
        /*删除波形文件
         * 删除convert。table中记录、转文本文件
         * 删除图片标记记录、图片文件
         * 删除record。db中记录*/
        String[] args = new String[]{globalId};
        List<Record> recordData = getRecordData(context, null, selection, args, null);
        for(Record record: recordData) {
            if(record.isRecycle()) {
                //如果录音文件进入了回收站，则本地不执行删除record表等操作，只需要更新record表
                ContentValues values = new ContentValues();
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, 0);
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID, "");
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_ID, "");
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID, UUID.randomUUID().toString());
                RecorderDBUtil.updateRecordData(context, values, selection, args);
                return;
            }
        }
        RecorderDBUtil.getInstance(context).deleteRecordData(context, selection, args);

        // 删除数据中globalId\fileId（加密音频文件）
        /**selection = selection + " AND " + DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS + " = ?";
         selectionArgs = new String[]{globalId, String.valueOf(RECORD_PRIVETE_ENCRYPT)};
         ContentProviderOperation operation = ContentProviderOperation
         .newUpdate(DatabaseConstant.RecordUri.RECORD_CONTENT_URI)
         .withValue(COLUMN_NAME_GLOBAL_ID, null)
         .withValue(COLUMN_NAME_FILE_ID, null)
         .withValue(COLUMN_NAME_CLOUD_SYS_VERSION, 0)
         .withValue(COLUMN_NAME_DIRTY, RECORD_NOT_DIRTY)
         .withValue(COLUMN_NAME_DELETE, RECORD_NORMAL)
         .withValue(COLUMN_NAME_SYNC_TYPE, SYNC_TYPE_BACKUP)
         .withValue(COLOUM_NAME_SYNC_DOWNLOAD_STATUS, SYNC_STATUS_DEFAULT)
         .withValue(COLUMN_NAME_SYNC_UPLOAD_STATUS, SYNC_STATUS_DEFAULT)
         .withValue(COLUMN_NAME_SYNC_DATE, 0)
         .withValue(COLUMN_NAME_FAIL_COUNT, 0)
         .withValue(COLUMN_NAME_LAST_FAIL_TIME, 0)
         .withSelection(selection, selectionArgs)
         .withYieldAllowed(true).build();
         operations.add(operation);*/
    }

    /**
     * 备份GroupInfo元数据类型为：Create
     *
     * @param kitRecord
     * @param operations
     */
    private static void addGroupInfoDb(CloudBackupResponseRecord kitRecord, ArrayList<ContentProviderOperation> operations) {
        if (kitRecord == null) {
            DebugUtil.e(TAG, "RecordEntityUtils.toRecord return null , break ");
            return;
        }
        final String uuId = kitRecord.getSysRecordId();
        final String globalId = String.valueOf(kitRecord.getSysRecordId());
        final Long sysVersion = kitRecord.getSysVersion();
        String selection = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID + " = ?";
        String[] selectionArgs = new String[]{uuId};
        ContentProviderOperation operation = null;
        operation = ContentProviderOperation
                .newUpdate(DatabaseConstant.GroupInfoUri.GROUP_INFO_URI)
                .withSelection(selection, selectionArgs)
                .withValue(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID, globalId)
                .withValue(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_CLOUD_SYS_VERSION,sysVersion)
                .withValue(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DIRTY, RecordConstant.RECORD_NOT_DIRTY)
                .withYieldAllowed(true)
                .build();
        operations.add(operation);
    }

    /**
     * 备份GroupInfo元数据类型：update
     *
     * @param kitRecord
     * @param operations
     */
    private static void updateGroupInfoDb(CloudBackupResponseRecord kitRecord, ArrayList<ContentProviderOperation> operations) {
        if (kitRecord == null) {
            DebugUtil.e(TAG, "RecordEntityUtils.toRecord return null , break ");
            return;
        }
        DebugUtil.i(TAG, "updateRecorderDb " + kitRecord.getSysVersion(), true);
        final String globalId = String.valueOf(kitRecord.getSysRecordId());
        final Long sysVersion = kitRecord.getSysVersion();
        String selection = DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID + " = ?";
        String[] selectionArgs = new String[]{globalId};
        ContentProviderOperation operation = null;
        operation = ContentProviderOperation
                .newUpdate(DatabaseConstant.GroupInfoUri.GROUP_INFO_URI)
                .withSelection(selection, selectionArgs)
                .withValue(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID, globalId)
                .withValue(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_CLOUD_SYS_VERSION,sysVersion)
                .withValue(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DIRTY, RecordConstant.RECORD_NOT_DIRTY)
                .withYieldAllowed(true)
                .build();
        operations.add(operation);
    }

    /**
     * 备份GroupInfo元数据类型：delete
     *
     * @param kitRecord
     * @param operations
     */
    private static void deleteFromGroupDb(Context context, CloudBackupResponseRecord kitRecord, ArrayList<ContentProviderOperation> operations) {
        if (kitRecord == null) {
            DebugUtil.e(TAG, "deleteFromGroupDb return null , break ");
            return;
        }
        DebugUtil.i(TAG, "onServerDeletedForBackup: kitRecord: " + kitRecord, true);
        final String globalId = String.valueOf(kitRecord.getSysRecordId());
        GroupInfoDbUtil.deleteByUUId(context, globalId);
    }


    private static ContentProviderResult[] applyContentProviderOperations(Context context, String authority, ArrayList<ArrayList<ContentProviderOperation>> operationsList) {
        if ((operationsList != null) && !operationsList.isEmpty()) {
            if (operationsList.size() == 1) {
                ArrayList<ContentProviderOperation> operations = operationsList.get(0);
                return applyContentProviderOperation(context, authority, operations);
            } else {
                ArrayList<ContentProviderResult> resultList = new ArrayList<ContentProviderResult>();
                for (ArrayList<ContentProviderOperation> operations : operationsList) {
                    ContentProviderResult[] results = applyContentProviderOperation(context, authority, operations);
                    if ((results != null) && (results.length > 0)) {
                        resultList.addAll(Arrays.asList(results));
                    }
                }
                final int size = resultList.size();
                ContentProviderResult[] ret = new ContentProviderResult[size];
                for (int i = 0; i < size; i++) {
                    ret[i] = resultList.get(i);
                }
                return ret;
            }
        }
        return null;
    }

    /**
     * 批量清理 录音表
     * COLUMN_NAME_CLOUD_SYS_VERSION
     */
    public static void clearRecordTableSysVersion() {
        try {
            ContentValues contentValues = new ContentValues();
            contentValues.put(COLUMN_NAME_CLOUD_SYS_VERSION, 0);
            BaseApplication.getAppContext().getContentResolver().update(RECORD_CONTENT_URI, contentValues, null, null);
        } catch (Exception e) {
            DebugUtil.d(TAG, "clearRecordTableSysVersion, e:" + e);
        }
    }

    /**
     * 批量清理 录音分组表
     * COLUMN_NAME_CLOUD_SYS_VERSION
     */
    public static void clearGroupInfoTableSysVersion() {
        try {
            ContentValues contentValues = new ContentValues();
            contentValues.put(DatabaseConstant.GroupInfoColumn.COLUMN_NAME_CLOUD_SYS_VERSION, 0);
            BaseApplication.getAppContext().getContentResolver().update(DatabaseConstant.GroupInfoUri.GROUP_INFO_URI, contentValues, null, null);
        } catch (Exception e) {
             DebugUtil.e(TAG, "clearGroupInfoTableSysVersion, e:" + e);
        }
    }
}
