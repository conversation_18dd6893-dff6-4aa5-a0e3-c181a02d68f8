package com.recorder.cloudkit.tipstatus

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.RtlUtils
import com.recorder.cloudkit.R
import com.soundrecorder.modulerouter.cloudkit.tipstatus.ITipStatus

enum class TipStatus : ITipStatus {
    NONE {
        override var state: Int = ITipStatus.STATE_NONE

        override var tipIconResId = 0
        override fun tipText(): Pair<String, String> = Pair("", "")
    },
    CLOUD_OFF {
        override var state: Int = ITipStatus.STATE_CLOUD_OFF

        override var tipIconResId = 0
        override fun tipText(): Pair<String, String> = Pair("", "")
    },
    NO_ALL_ACCESS_PERMISSION {
        override var state: Int = ITipStatus.STATE_NO_ALL_ACCESS_PERMISSION

        override var tipIconResId = R.drawable.ic_cloudkit_sync_error
        override fun tipText(): Pair<String, String> = getFailureText()
    },
    NO_CLOUD_SPACE {
        override var state: Int = ITipStatus.STATE_NO_CLOUD_SPACE

        override var tipIconResId = R.drawable.ic_cloudkit_sync_error
        override fun tipText(): Pair<String, String> = getFailureTextForNoCloudSpace()
    },
    QUERY {
        override var state: Int = ITipStatus.STATE_QUERY

        override var tipIconResId = R.drawable.ic_cloudkit_sync_querying
        override fun tipText(): Pair<String, String> = getQueryingText()
    },
    SYNCING {
        override var state: Int = ITipStatus.STATE_SYNCING

        override var tipIconResId = R.drawable.ic_cloudkit_sync_doing
        override fun tipText(): Pair<String, String> = getSyncingText()
    },
    FAILURE {
        override var state: Int = ITipStatus.STATE_FAILURE

        override var tipIconResId = R.drawable.ic_cloudkit_sync_error
        override fun tipText(): Pair<String, String> = getFailureText()
    },
    CLOUD_SETTINGS {
        override var state: Int = ITipStatus.STATE_SETTINGS

        override var tipIconResId = R.drawable.ic_cloudkit_sync_error
        override fun tipText(): Pair<String, String> = getCloudSettingsText()
    },
    COMPLETED {
        override var state: Int = ITipStatus.STATE_COMPLETED

        override var tipIconResId = R.drawable.ic_cloudkit_sync_success
        override fun tipText(): Pair<String, String> = getCompletedText()
    };

    // 副标题图标
//    open var tipIconResId: Int = 0

    /**
     * @param listener 副标题内容中 clickSpan点击事件
     * @return 副标题文案
     */
//    fun tipText(): Pair<String, String>
    fun getFailureText(): Pair<String, String> {
        val res = BaseApplication.getAppContext()?.resources
        val linkText = res?.getString(com.soundrecorder.common.R.string.cloud_recordings_reason) ?: ""
        val tipText = res?.getString(com.soundrecorder.common.R.string.cloud_recordings_error, "") ?: ""
        return Pair(tipText, linkText)
    }

    /**
     * 【由于云空间不足导致同步失败】的副标题文案
     */
    fun getFailureTextForNoCloudSpace(): Pair<String, String> {
        val res = BaseApplication.getAppContext()?.resources
        val linkText = res?.getString(R.string.upgrade_cloud_space) ?: ""
        val tipText = (res?.getString(R.string.cloud_space_not_limit) ?: "") + RtlUtils.HALF_SPACE
        return Pair(tipText, linkText)
    }

    /**
     * 【同步查询中】的副标题文案
     */
    fun getQueryingText(): Pair<String, String> {
        val context = BaseApplication.getAppContext()
        val formatText = context?.resources?.getString(R.string.cloudkit_querying) ?: ""
        return Pair(formatText, "")
    }

    /**
     * 【同步中】的副标题文案
     */
    fun getSyncingText(): Pair<String, String> {
        val context = BaseApplication.getAppContext()
        val formatText = context?.resources?.getString(R.string.cloudkit_syncing) ?: ""
        return Pair(formatText, "")
    }

    /**
     * 【同步成功】完成的副标题文案
     */
    fun getCompletedText(): Pair<String, String> {
        val res = BaseApplication.getAppContext()?.resources
        val linkText = res?.getString(com.soundrecorder.common.R.string.cloud_recordings_reason) ?: ""
        val tipText = res?.getString(R.string.cloudkit_syncing_complete, "") ?: ""
        return Pair(tipText, linkText)
    }

    /**
     * 从服务端获取到商业化词条
     */
    fun getCloudSettingsText(): Pair<String, String> {
        val res = BaseApplication.getAppContext()?.resources
        val linkText = res?.getString(com.soundrecorder.common.R.string.cloud_recordings_reason) ?: ""
        val tipText = res?.getString(R.string.cloudkit_syncing_complete, "") ?: ""
        return Pair(tipText, linkText)
    }
}