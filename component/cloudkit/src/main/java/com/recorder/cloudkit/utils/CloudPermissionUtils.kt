package com.recorder.cloudkit.utils

import android.content.Context
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.FunctionOption
import java.util.Date

object CloudPermissionUtils {
    const val TAG = "PermissionUtils"
    private const val PERMISSION_APPLIED = 0x01
    private const val PERMISSION_NOT_APPLY = 0x00
    private const val FIRST_APPLY_PERMISSION_CLOUD = "first_apply_permission_cloud"
    private const val CLOUD_PERMISSION = "cloud_permission"

    /**
     * 云同步所需权限
     * androidQ: 存储权限
     * R及以后：所有文件管理权限
     */
    @JvmStatic
    fun hasCloudRequirePermission(): Boolean {
        if (!BaseUtil.isAndroidROrLater) {
            return PermissionUtils.hasStoragePermission()
        }
        return PermissionUtils.hasAllFilePermission()
    }

    @JvmStatic
    fun isStatementCloudGranted(context: Context): Boolean {
        //卖场模式，默认授予转文本权限
        val hasSellMode = FunctionOption.isSupportSellMode()
        val defaultValue = if (hasSellMode) PERMISSION_APPLIED else PERMISSION_NOT_APPLY
        val appContext = context.applicationContext
        return PERMISSION_APPLIED == StorageManager.getIntPref(appContext, FIRST_APPLY_PERMISSION_CLOUD, defaultValue)
    }

    @JvmStatic
    fun setCloudGrantedStatus(context: Context) {
        StorageManager.setIntPrefApply(
            context.applicationContext,
            FIRST_APPLY_PERMISSION_CLOUD,
            PERMISSION_APPLIED
        )
        val grant: Boolean = isStatementCloudGranted(BaseApplication.getAppContext())
        DebugUtil.d(
            CLOUD_PERMISSION,
            "setCloudGrantedStatus in " + Date() + ", and cloud permission is open" + grant
        )
    }

    @JvmStatic
    fun clearCloudGrantedStatus() {
        StorageManager.setIntPrefApply(
            BaseApplication.getAppContext(),
            FIRST_APPLY_PERMISSION_CLOUD,
            PERMISSION_NOT_APPLY
        )
        val grant: Boolean = isStatementCloudGranted(BaseApplication.getAppContext())
        DebugUtil.d(
            CLOUD_PERMISSION,
            "clearCloudGrantedStatus in " + Date() + ", and cloud permission is open" + grant
        )
    }

    @JvmStatic
    fun isNetWorkNoticeGranted(context: Context): Boolean {
        if (BaseUtil.isEXP()) {
            return true
        }
        return PermissionUtils.isNetWorkGranted(context)
    }
}