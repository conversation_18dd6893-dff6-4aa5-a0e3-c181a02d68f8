package com.recorder.cloudkit.sync.ui

import android.annotation.SuppressLint
import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import androidx.preference.Preference
import com.coui.appcompat.clickablespan.COUIClickableSpan
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.preference.COUISwitchPreference
import com.coui.appcompat.toolbar.COUIToolbar
import com.heytap.cloudkit.libcommon.account.CloudAccountManager
import com.heytap.cloudkit.libcommon.netrequest.error.CloudKitError
import com.heytap.cloudkit.libguide.CloudSyncGuideDialogBuilder
import com.heytap.cloudkit.libguide.track.CloudGuideTrack
import com.heytap.cloudkit.libsync.cloudswitch.bean.SwitchState
import com.heytap.cloudkit.libsync.ext.CloudSyncManager
import com.recorder.cloudkit.R
import com.recorder.cloudkit.account.AccountBean
import com.recorder.cloudkit.account.AccountManager
import com.recorder.cloudkit.account.AccountPref
import com.recorder.cloudkit.account.IAccountInfoCallback
import com.recorder.cloudkit.global.CloudGlobalStateManager
import com.recorder.cloudkit.sync.CloudSynStateHelper
import com.recorder.cloudkit.sync.CloudSynStateHelper.getSwitchState
import com.recorder.cloudkit.sync.CloudSynStateHelper.isRegionCloudSupport
import com.recorder.cloudkit.sync.SyncDataConstants
import com.recorder.cloudkit.tipstatus.CloudSwitchStatusHelper
import com.recorder.cloudkit.tipstatus.TipStatusManager
import com.recorder.cloudkit.utils.CloudPermissionUtils
import com.recorder.cloudkit.view.LoadingSwitchPreference
import com.soundrecorder.base.BaseActivity
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils.isFastDoubleClick
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.OS12FeatureUtil.isColorOS14OrLater
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.base.view.RootViewPersistentInsetsCallback
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity
import com.soundrecorder.common.db.CloudSyncRecorderDbUtil
import com.soundrecorder.common.utils.ViewUtils
import com.soundrecorder.common.utils.taskbar.TaskBarUtil
import com.soundrecorder.common.widget.AutoIndentPreferenceFragment
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.cloudkit.CloudSwitchState
import com.soundrecorder.modulerouter.cloudkit.ICloudGlobalStateCallBack
import com.soundrecorder.modulerouter.cloudkit.VerifyCallBack
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant
import com.soundrecorder.modulerouter.utils.Injector
import com.support.appcompat.R.attr.couiColorPrimary
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class SettingRecordSyncFragment : AutoIndentPreferenceFragment(), Preference.OnPreferenceChangeListener,
    CloudSwitchStatusHelper.CloudSwitchStateChangeListener {

    companion object {
        private val TAG = "SettingRecordSyncFragment"
        const val ACCOUNT_LOGOUT_RECEIVER = "account_logout_receiver"
        fun newInstance(): SettingRecordSyncFragment {
            return SettingRecordSyncFragment()
        }
    }

    private var mAutomaticSyn: LoadingSwitchPreference? = null
    private var mAllowMobileData: COUISwitchPreference? = null
    private val SYN_AUTOMATIC: String = "automatic_syn"
    private val SYN_ALLMOBILEDATA: String = "allow_data"
    //此界面弹窗皆不重建
    private var mExitCloudSynDialog: AlertDialog? = null
    private var mSyncSwitchState: Int = SwitchState.CLOSE.state
    private var mGuideDialogBuilder: CloudSyncGuideDialogBuilder? = null
    private var accountReqLoginCallBack: IAccountInfoCallback? = null

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        val view = super.onCreateView(inflater, container, savedInstanceState)
        initActionBar(view)
        listView?.isForceDarkAllowed = false
        if (FeatureOption.OPLUS_VERSION_EXP) {
            addPreferencesFromResource(R.xml.preference_setting_syn)
        } else {
            addPreferencesFromResource(R.xml.domestic_preference_setting_syn)
        }
        mAutomaticSyn = findPreference<Preference>(SYN_AUTOMATIC) as? LoadingSwitchPreference?
        mAllowMobileData = findPreference<Preference>(SYN_ALLMOBILEDATA) as COUISwitchPreference?
        mAutomaticSyn?.setClickListener {
            if (!isFastDoubleClick()) {
                if (mSyncSwitchState > SwitchState.CLOSE.state) {
                    showClosedCloudDialog()
                } else if (!BaseUtil.isEXP()) {
                    showCloudPrivacyOrOpenSync()
                } else {
                    switchCloudOn()
                }
            }
        }
        mAutomaticSyn?.onPreferenceChangeListener = this
        mAllowMobileData?.onPreferenceChangeListener = this
        initAutomaticSynView()
        registerReceiver()
        TipStatusManager.addCloudSwitchChangeListener(this)
        return view
    }

    /**
     * 自动打开云同步
     */
    @SuppressLint("NewApi")
    private fun autoOpenSwitchOn() {
        if (isColorOS14OrLater()) {
            val reqContext = activity ?: return
            if (NetworkUtils.isNetworkInvalid(reqContext.applicationContext)) {
                ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
                return
            }

            lifecycleScope.launch(Dispatchers.IO) {
                if (CloudAccountManager.getInstance().isLogin) {
                    if (cloudKitApi?.checkAccountIsVerified() == true) {
                        val callback = object : VerifyCallBack {
                            override fun onSuccess() {
                                //校验成功
                                autoOpenSwitchOnAfterVerify()
                            }

                            override fun onFail() {
                                DebugUtil.v(TAG, "openSync onFail")
                            }
                        }
                        activity?.let {
                            cloudKitApi?.checkLoginAndVerify(it, callback)
                        }
                    } else {
                        autoOpenSwitchOnAfterVerify()
                    }
                }
            }
        }
    }

    @RequiresApi(Build.VERSION_CODES.R)
    private fun autoOpenSwitchOnAfterVerify() {
        val error = CloudSyncManager.getInstance().setSyncSwitch(SwitchState.OPEN_ONLY_WIFI)
        if (error.isSuccess) {
            notifySwitchUi(SwitchState.OPEN_ONLY_WIFI.state)
            CloudPermissionUtils.setCloudGrantedStatus(BaseApplication.getAppContext())
            clearLastUserData()
        } else {
            DebugUtil.w(TAG, "openSync error $error")
        }
    }

    private fun initActionBar(view: View?) {
        if (view == null) {
            return
        }
        val activity = activity as? AppCompatActivity? ?: return
        val toolbar = view.findViewById<COUIToolbar>(com.support.panel.R.id.toolbar)
        activity.setSupportActionBar(toolbar)
        val actionBar = activity.supportActionBar
        actionBar?.setHomeButtonEnabled(true)
        actionBar?.setDisplayHomeAsUpEnabled(true)
        actionBar?.setTitle(R.string.sync_record)
        initiateWindowInsets(activity)
    }

    private fun initiateWindowInsets(activity: Activity) {
        WindowCompat.setDecorFitsSystemWindows(activity.window, false)
        activity.findViewById<View>(R.id.root_layout)?.let {
            val callback: RootViewPersistentInsetsCallback = object : RootViewPersistentInsetsCallback() {
                override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                    super.onApplyInsets(v, insets)
                    DebugUtil.i(TAG, "onApplyInsets")
                    TaskBarUtil.setNavigationColorOnSupportTaskBar(
                        navigationHeight = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars()).bottom,
                        activity = activity,
                        defaultNoTaskBarColor = (activity as? BaseActivity)?.navigationBarColor()
                    )
                }
            }
            ViewCompat.setOnApplyWindowInsetsListener(it, callback)
        }
    }

    private fun initAutomaticSynView() {
        if (FeatureOption.OPLUS_VERSION_EXP) {
            val linkText = getString(R.string.cloudkit_privacy_statement)
            val tipText = getString(R.string.cloudkit_automatic_settings_export, linkText)
            val builder = SpannableStringBuilder(tipText)
            val startIndex = tipText.indexOf(linkText)
            if (startIndex > 0) {
                val endIndex = startIndex + linkText.length
                val span = COUIClickableSpan(BaseApplication.getAppContext())
                span.setStatusBarClickListener {
                    openPrivacy()
                }
                builder.setSpan(span, startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
            mAutomaticSyn?.summary = builder
        }
    }

    /**
     * 跳转外部浏览器
     * 内销：不涉及
     * 外销：https://static-cn01a-ocloud.heytapimage.com/heytapprivacystatement.html?new_open=true&BRAND-SHOW-TYPE=1&OCLOUD-LANG=en_US&OCLOUD-EXP=true
     */
    private fun openPrivacy() {
        try {
            context?.let {
                val intent = Intent()
                intent.action = Intent.ACTION_VIEW
                intent.data = Uri.parse(SyncDataConstants.CLOUD_PRIVACY_URL)
                it.startActivity(intent)
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, e.message)
        }
    }

    override fun onResume() {
        super.onResume()
        // 为了解决云服务APP打开开关页面，改变状态，更新不及时
        // 该场景无主进程，可能没有注册上开关变化listener
        initCloudSync()
    }

    fun onPrivacyPolicySuccess(type: Int) {
        if (type == PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD) {
            DebugUtil.d(TAG, "onPrivacyPolicySuccess, type:$type")
            switchCloudOn()
        }
    }

    fun initCloudSync() {
        if (!isRegionCloudSupport()) {
            DebugUtil.i(TAG, "current area not support cloud return")
            return
        }
        lifecycleScope.launch(Dispatchers.IO) {
            notifySwitchUi(getSwitchState(false))
        }
    }

    private fun registerReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(ACCOUNT_LOGOUT_RECEIVER)
        LocalBroadcastManager.getInstance(BaseApplication.getAppContext())
            .registerReceiver(mLogoutReceiver, intentFilter)
    }

    override fun onPreferenceChange(preference: Preference?, newValue: Any?): Boolean {
        DebugUtil.i(TAG, "onSharedPreferenceChanged:Key = $newValue")
        lifecycleScope.launch(Dispatchers.IO) {
            when (preference?.key) {
                SYN_ALLMOBILEDATA -> {
                    if (mSyncSwitchState == SwitchState.OPEN_ONLY_WIFI.state) {
                        if (NetworkUtils.isNetworkInvalid(BaseApplication.getAppContext())) {
                            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
                            return@launch
                        }
                    }
                    var newState =
                        if (mSyncSwitchState == SwitchState.OPEN_ONLY_WIFI.state) SwitchState.OPEN_ALL else SwitchState.OPEN_ONLY_WIFI
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        if (cloudKitApi?.checkAccountIsVerified() == true) {
                            val callback = object : VerifyCallBack {
                                override fun onSuccess() {
                                    //校验成功
                                    openSyncAfterVerify(newState)
                                }

                                override fun onFail() {
                                    DebugUtil.v(TAG, "preference openSync onFail")
                                }
                            }
                            DebugUtil.w(TAG, "checkLoginAndVerify start")
                            activity?.let { cloudKitApi?.checkLoginAndVerify(it, callback) }
                        } else {
                            openSyncAfterVerify(newState)
                        }
                    } else {
                        DebugUtil.w(TAG, "open switch error below R")
                    }
                }
            }
        }
        return false
    }

    @RequiresApi(Build.VERSION_CODES.R)
    private fun openSyncAfterVerify(newState: SwitchState) {
        val result = CloudSyncManager.getInstance().setSyncSwitch(newState)
        if (result.isSuccess) {
            notifySwitchUi(newState.state)
        } else {
            DebugUtil.w(TAG, "open switch $newState error $result")
        }
    }

    /**
     * 开启云同步
     */
    private fun switchCloudOn() {
        val requireContext = context ?: return

        if (NetworkUtils.isNetworkInvalid(requireContext.applicationContext)) {
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
            return
        }

        if (!CloudPermissionUtils.hasCloudRequirePermission()) {
            val it = Intent("com.oplus.soundrecorder.openCloudSwitch")
            it.setPackage(requireContext.packageName)
            it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            startActivity(it)
            return
        }
        if (!CloudSynStateHelper.isLoginFromCache()) {
            if (accountReqLoginCallBack == null) {
                accountReqLoginCallBack = object : IAccountInfoCallback {
                    override fun onComplete(account: AccountBean) {
                        DebugUtil.i(TAG, "switchCloudOn,reqSignInAccount login == ${account.isLogin}")
                        if (account.isLogin || CloudSynStateHelper.isLoginFromCache()) {
                            doOpenSyncWhenLogined()
                        } else {
                            DebugUtil.i(TAG, "switchCloudOn,reqSignInAccount: is failure")
                        }
                    }
                }
            }
            AccountManager.sAccountManager.reqSignInAccount(requireContext.applicationContext, accountReqLoginCallBack)
        } else {
            doOpenSyncWhenLogined()
        }
    }

    private fun doOpenSyncWhenLogined() {
        val requireContext = context ?: return
        if (NetworkUtils.isNetworkInvalid(requireContext.applicationContext)) {
            ToastManager.showShortToast(context, com.soundrecorder.common.R.string.network_disconnect)
            return
        }

        val openSyncFun = {
            if (isColorOS14OrLater()) {
                autoOpenSwitchOn()
            } else {
                openSync()
            }
        }

        DebugUtil.w(TAG, "doOpenSyncWhenLogined  ")
        lifecycleScope.launch(Dispatchers.Main) {
            mAutomaticSyn?.startLoading()
            CloudGlobalStateManager.judgeOCloudGlobalState(
                true,
                object : ICloudGlobalStateCallBack {
                    override fun onSuccess(changed: Boolean, support: Boolean, state: String?) {
                        if (activity == null || activity?.isFinishing == true || isRemoving) {
                            DebugUtil.w(
                                TAG,
                                "doOpenSyncWhenLogined  resultFun return by activity finish"
                            )
                            return
                        }
                        lifecycleScope.launch(Dispatchers.Main) {
                            mAutomaticSyn?.stopLoading()
                            if (support) {
                                openSyncFun.invoke()
                            } else {
                                val activity = activity ?: return@launch
                                CloudGlobalStateManager.showErrorDialog(activity, state) {
                                    activity.finish()
                                }
                            }
                        }
                    }

                    override fun onFailure() {
                        lifecycleScope.launch(Dispatchers.Main) {
                            mAutomaticSyn?.stopLoading()
                        }
                    }
                })
        }
    }

    private fun showCloudPrivacyOrOpenSync() {
        //云同步权限未获取的时候
        val reqContext = activity ?: return
        if (!CloudPermissionUtils.isStatementCloudGranted(reqContext)) {
            lifecycleScope.launch(Dispatchers.Main) {
                (activity as? PrivacyPolicyBaseActivity)?.getPrivacyPolicyDelegate()
                    ?.resumeShowDialog(PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD, true)
            }
        } else {
            switchCloudOn()
        }
    }

    private fun notifySwitchUi(switchState: Int) {
        mSyncSwitchState = switchState
        lifecycleScope.launch(Dispatchers.Main) {
            when (switchState) {
                SwitchState.CLOSE.state,
                CloudSwitchState.NOT_LOGIN -> {
                    mAutomaticSyn?.isChecked = false
                    mAllowMobileData?.isChecked = false
                    mAllowMobileData?.isVisible = canShowMobileSwitch(false)
                }
                SwitchState.OPEN_ALL.state -> {
                    mAutomaticSyn?.isChecked = true
                    mAllowMobileData?.isVisible = canShowMobileSwitch(true)
                    mAllowMobileData?.isChecked = true
                }
                SwitchState.OPEN_ONLY_WIFI.state -> {
                    mAutomaticSyn?.isChecked = true
                    mAllowMobileData?.isVisible = canShowMobileSwitch(true)
                    mAllowMobileData?.isChecked = false
                }
            }
        }
    }

    /**
     * 允许移动数据下云同步开关显示
     */
    private fun canShowMobileSwitch(switchOn: Boolean): Boolean {
        // 云同步开关关闭，不显示二级开关
        if (!switchOn) {
            return false
        }
        // 云同步开关打开+支持蜂窝网络，显示二级开关
        return FeatureOption.checkHasTelePhonyFeature(BaseApplication.getAppContext())
    }

    private fun showClosedCloudDialog() {
        // 避免快速点击，弹出2个弹窗
        if (mExitCloudSynDialog?.isShowing == true) {
            return
        }
        lifecycleScope.launch(Dispatchers.Main) {
            context?.let {
                val builder = COUIAlertDialogBuilder(it, com.support.dialog.R.style.COUIAlertDialog_Bottom)
                builder.setBlurBackgroundDrawable(true)
                builder.setTitle(R.string.allow_mobile_data_syn_of_settings)
                builder.setMessage(it.getString(R.string.closed_record_not_automatic_cloud))
                val closeString: String = it.getString(com.soundrecorder.common.R.string.close)
                builder.setPositiveButton(closeString) { dialog, _ ->
                    dialog.dismiss()
                    lifecycleScope.launch(Dispatchers.IO) {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                            val result = CloudSyncManager.getInstance().setSyncSwitch(SwitchState.CLOSE)
                            CloudPermissionUtils.clearCloudGrantedStatus()
                            if (result.isSuccess) {
                                notifySwitchUi(SwitchState.CLOSE.state)
                            } else {
                                DebugUtil.w(TAG, "close cloud switch error $result")
                            }
                        } else {
                            DebugUtil.w(TAG, "showClosedCloudDialog close cloud below R")
                        }
                    }
                }
                builder.setNegativeButton(com.soundrecorder.common.R.string.cancel, null)
                mExitCloudSynDialog = builder.create()
                mExitCloudSynDialog?.setCancelable(true)
                mExitCloudSynDialog?.show()
                ViewUtils.updateWindowLayoutParams(mExitCloudSynDialog?.window)
            }
        }
    }

    private fun openSync() {
        context?.let {
            // 判断开关是否打开，已打开不处理弹窗逻辑
            if (mSyncSwitchState > SwitchState.CLOSE.state) {
                DebugUtil.i(TAG, "openSync, switch has opened,return")
                return
            }
            if (mGuideDialogBuilder == null) {
                mGuideDialogBuilder = CloudSyncGuideDialogBuilder(it)
                    .setClickArea(CloudGuideTrack.ClickArea.SETTING)
                    .setThemeColorAttr(couiColorPrimary)
                    .setSwitchState(SwitchState.OPEN_ONLY_WIFI)
            }
            mGuideDialogBuilder?.show { error: CloudKitError ->
                if (error.isSuccess) {
                    notifySwitchUi(SwitchState.OPEN_ONLY_WIFI.state)
                    CloudPermissionUtils.setCloudGrantedStatus(BaseApplication.getAppContext())
                    clearLastUserData()
                } else {
                    DebugUtil.w(TAG, "openSync error $error")
                }
                // 弹窗消失，置空，避免弹过一次后，onConfigureChange无效调用
                mGuideDialogBuilder = null
            }
        }
    }

    /**
     * 该处理主要针对杀死录音后，切换账号，从云服务APP拉起页面打开云同步开关。
     * 该场景若不处理，会导致用户打开录音checkAccount开关又被关掉
     *
     * 正常录音进程中不需要此操作
     */
    private fun clearLastUserData() {
        lifecycleScope.launch(Dispatchers.IO) {
            context?.applicationContext?.let {
                // 当前登录账号同SP中存的非空值不相同，执行一次关闭开关操作，更新Sp中的值
                if (checkUserIdChanged(it)) {
                    CloudSyncRecorderDbUtil.clearLocalSyncStatusForLogingout(it)
                    CloudSyncManager.getInstance().clearUserDataOnLogout()
                    AccountPref.setAccountUId(it, AccountManager.getInstance().getLoginIdFromCache(it))
                }
            }
        }
    }

    /**
     * 验证SP中的用户不为null，且跟当前登录用户不一致
     */
    private fun checkUserIdChanged(context: Context): Boolean {
        val oldId = AccountPref.getAccountUId(context)
        return (!oldId.isNullOrBlank()) && (oldId != AccountManager.getInstance().getLoginIdFromCache(context))
    }

    private val mLogoutReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            DebugUtil.i(TAG, "onReceive ${intent?.action}")
            if (intent?.action.equals(ACCOUNT_LOGOUT_RECEIVER)) {
                notifySwitchUi(SwitchState.CLOSE.state)
            }
        }
    }

    override fun onDestroy() {
        accountReqLoginCallBack = null
        super.onDestroy()
        mAutomaticSyn?.stopLoading()
        if (mExitCloudSynDialog != null) {
            mExitCloudSynDialog?.dismiss()
            mExitCloudSynDialog = null
        }
        LocalBroadcastManager.getInstance(BaseApplication.getAppContext())
            .unregisterReceiver(mLogoutReceiver)
        TipStatusManager.removeCloudSwitchChangeListener(this)
    }

    override fun onCloudSwitchChanged(switchState: SwitchState) {
        DebugUtil.i(TAG, "onCloudSwitchChanged switchState: $switchState, before $mSyncSwitchState")
        if (mSyncSwitchState != switchState.state) {
            notifySwitchUi(switchState.state)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        mGuideDialogBuilder?.onConfigurationChanged(newConfig)
    }
}