package com.recorder.cloudkit.sync;

import com.soundrecorder.base.utils.XORUtil;
import com.recorder.cloudkit.BuildConfig;

public class SyncDataConstants {
    public static final int XOR_KEY = 3;
    /*云同步外销隐私政策URL*/
    public static final String CLOUD_PRIVACY_URL = XORUtil.enOrDecrypt(BuildConfig.HOST_PRIVCY_URL, XOR_KEY) + "?new_open=true&BRAND-SHOW-TYPE=1&OCLOUD-LANG=en_US&OCLOUD-EXP=true";
    /*星链后台申请container名称：recorder*/
    public static final String MODULE_RECORDER = "recordData";
    public static final String ZONE_RECORDER = "recordData";
    /*获取录音公共配置zone，目前主要：获取跳转云服务H5地址*/
    public static final String ZONE_CONFIG = "config";
    public static final String ZONE_GROUP = "groupData";
    /*后台创建recordType*/
    public static final String META_DATA_TYPE_RECORD = "item_info";
    public static final String META_DATA_TYPE_GROUP = "group_item_info";

    public static final long PROTOCOL_VERSION = 0L;
    /*filed 版本控制，服务端对于老版本会过滤新字段*/
    public static final int RECORD_TYPE_VERSION = RecordTypeVersion.VERSION_DEFAULT;

    /*备份传入operatorType start*/
    // 仅用于元数据备份传入操作类型operator_type字段值
    public static final String OPERATOR_TYPE_CREATE = "create";//新建元数据
    public static final String OPERATOR_TYPE_REPLACE = "replace";//替换整个元数据（确保元数据是新的）
    public static final String OPERATOR_TYPE_MODIFY = "modify";//只修改提交的元数据字段，不是替换整个元数据，注意区别于replace
    // 只有私密保险箱加密，才是彻底删除云端数据，若是普通删除，则是将云端数据移动到云端回收站，云端有自动清理逻辑
    public static final String OPERATOR_TYPE_DELETE = "deleteAndReplace";//彻底删除元数据
    public static final String OPERATOR_TYPE_RECYCLE = "recycleAndReplace";//把元数据挪到回收站
    public static final String OPERATOR_TYPE_RESUME = "resumeAndReplace";//把元数据从回收站 恢复出来
    /**备份删除数据，返回type为delete：该数据在云端已经为删除数据*/
    public static final String RESPONSE_OPERATOR_TYPE_DELETE = "delete";
    /*备份传入operatorType end*/

    // 对应恢复元数据：sysStatus字段：元数据状态（0正常   1已删除   2回收站）
    public static final int SYS_STATUS_NORMAL = 0; // 新增元数据or元数据有更新
    public static final int SYS_STATUS_DELETED = 1; // 云端元数据文件彻底被删除(业务层对应 加密)
    public static final int SYS_STATUS_RECYCLE = 2; // 云端元数据文件被移动到回收站(对应业务普通删除)

    /**
     * 主要用于标识当前请求(新增、更新)的recordType（fields）版本。值从0开始，只能递增。后续若业务方在baas平台对该recordType新增了字段，baas平台会返回一个新的recordTypeVersion，业务方需用这个从baas平台拿到的recordTypeVersion调用cloudkit接口
     */
    public static class RecordTypeVersion {
        public static final int VERSION_DEFAULT = 2;
    }
}
