<?xml version="1.0" encoding="utf-8"?>
<androidx.preference.PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.preference.Preference
        android:layout="@layout/preference_header"
        android:selectable="false" />

    <com.coui.appcompat.preference.COUIPreferenceCategory
        android:selectable="false"
        app:isFirstCategory="true">

        <com.recorder.cloudkit.view.CustomSwitchPreference
            android:defaultValue="true"
            android:key="automatic_syn"
            android:persistent="false"
            android:summary="@string/automatic_syn_cloud_convenient_to_see_of_settings"
            android:title="@string/automatic_syn_of_settings"
            app:couiEnalbeClickSpan="true"
            app:defaultValue="false" />

        <com.coui.appcompat.preference.COUISwitchPreference
            android:defaultValue="true"
            android:key="allow_data"
            android:persistent="false"
            app:isPreferenceVisible="false"
            app:defaultValue="false"
            android:title="@string/allow_mobile_data_syn_new" />

    </com.coui.appcompat.preference.COUIPreferenceCategory>

</androidx.preference.PreferenceScreen>

