/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: WaveRecyclerViewTest
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(v-zhen<PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.views.wave

import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import org.junit.Assert
import org.junit.Ignore
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito.*
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S])
@Ignore
class SmallCardWaveRecyclerViewTest {

    @Test
    fun should_correct_when_runDoEnterAnimator() {
        val waveRecyclerView = spy(SmallCardWaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.doEnterAnimator()
        Assert.assertTrue(Whitebox.getInternalState<Long>(waveRecyclerView, "doEnterAnimationTime") > 0L)
    }

    @Test
    fun should_correct_when_setSelectTime() {
        val waveRecyclerView = spy(SmallCardWaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.setSelectTime(0)
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "preTime") == 0)
        waveRecyclerView.setSelectTime(1)
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "preTime") > 0)
    }

    @Test
    fun should_correct_when_refreshColor() {
        val waveRecyclerView = spy(SmallCardWaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.refreshColor(1, 2, 3)
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "cardWaveColor") == 1)
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "cardDashWaveColor") == 2)
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "markLineColor") == 3)
    }

    @Suppress("SENSELESS_COMPARISON")
    @Test
    fun should_correct_when_createNewItemView() {
        val waveRecyclerView = spy(SmallCardWaveRecyclerView(ApplicationProvider.getApplicationContext()))
        val waveItemView = waveRecyclerView.createNewItemView(waveRecyclerView)
        Assert.assertTrue(waveItemView != null)
    }

    @Test
    fun should_correct_when_onBindItemView() {
        val waveRecyclerView = spy(SmallCardWaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.onBindItemView(waveRecyclerView.createNewItemView(waveRecyclerView), 1)
    }

    @Test
    fun should_correct_when_halfWidth() {
        val waveRecyclerView = spy(SmallCardWaveRecyclerView(ApplicationProvider.getApplicationContext()))
        doReturn(200).doCallRealMethod().`when`(waveRecyclerView).width
        Assert.assertTrue(waveRecyclerView.halfWidth() > 0)
    }

    @Test
    fun should_correct_when_stopRecorderMove() {
        val waveRecyclerView = spy(SmallCardWaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.stopRecorderMove(1, listOf(200))
        Assert.assertTrue(!Whitebox.getInternalState<Boolean>(waveRecyclerView, "hasRecording"))
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "ampsSize") == 1)
    }

    @Test
    fun should_correct_when_recorderIntervalUpdate() {
        val waveRecyclerView = spy(SmallCardWaveRecyclerView(ApplicationProvider.getApplicationContext()))
        waveRecyclerView.recorderIntervalUpdate(2, listOf(100, 200), true)
        waveRecyclerView.recorderIntervalUpdate(2, listOf(100, 200), false)
        Assert.assertTrue(Whitebox.getInternalState(waveRecyclerView, "hasRecording"))
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "ampsSize") == 2)
        waveRecyclerView.recorderIntervalUpdate(4, listOf(100, 200, 300, 400), true)
        waveRecyclerView.recorderIntervalUpdate(4, listOf(100, 200, 300, 400), false)
        Assert.assertTrue(Whitebox.getInternalState(waveRecyclerView, "hasRecording"))
        Assert.assertTrue(Whitebox.getInternalState<Int>(waveRecyclerView, "ampsSize") == 4)
    }
}