/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: MarkDataBean
 * Description:
 * Version: 1.0
 * Date: 2023/10/30
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2023/10/30 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.bean

import androidx.annotation.Keep

@Keep
data class MarkDataBean(var timeInMills: Long, var version: Int = 1) {
    var isDefault: Boolean = true
    var defaultNo: Int = 0
    var markText: String = ""
    /**
     * 校正后的时间
     */
    var correctTime: Long = 0
}