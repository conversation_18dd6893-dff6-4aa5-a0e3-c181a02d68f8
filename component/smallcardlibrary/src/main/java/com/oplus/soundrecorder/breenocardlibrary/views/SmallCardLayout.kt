/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: SmallCardViewHolder
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.views

import android.app.ActivityOptions
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Looper
import android.util.AttributeSet
import android.util.TypedValue
import android.view.MotionEvent
import android.view.View
import android.view.View.OnClickListener
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.Keep
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import com.airbnb.lottie.LottieAnimationView
import com.oplus.soundrecorder.breenocardlibrary.R
import com.oplus.soundrecorder.breenocardlibrary.bean.ClickAction
import com.oplus.soundrecorder.breenocardlibrary.bean.RecorderState
import com.oplus.soundrecorder.breenocardlibrary.bean.SaveFileState
import com.oplus.soundrecorder.breenocardlibrary.bean.SmallCardData
import com.oplus.soundrecorder.breenocardlibrary.runnable.SmallCardRunnable
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.DURATION_5000
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.METHOD_JUST_START
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.METHOD_START_SERVICE
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.SHOULD_AUTO_FIND_FILE_NAME
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.SMALL_CARD_METHOD
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.doAction
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.doOnLayoutChange
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.fixTextFlash
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.getAppContext
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.log
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.runBackground
import com.oplus.soundrecorder.breenocardlibrary.utils.CardDataUtils
import com.oplus.soundrecorder.breenocardlibrary.utils.CardVersionUtils
import com.oplus.soundrecorder.breenocardlibrary.utils.ScreenUtil.getFloatValue
import com.oplus.soundrecorder.breenocardlibrary.utils.ScreenUtil.isNightMode
import com.oplus.soundrecorder.breenocardlibrary.views.button.AppCardButton
import com.oplus.soundrecorder.breenocardlibrary.views.wave.SmallCardWaveRecyclerView
import kotlin.math.min

@Keep
@Suppress("TooGenericExceptionCaught")
internal class SmallCardLayout(context: Context, attrs: AttributeSet? = null) : FrameLayout(context, attrs), OnClickListener {
    companion object {
        const val RECORDER_TIME_LONG_VALUE = 5
    }
    private var isOnVisible = false
    private lateinit var data: SmallCardData
    private val cardRootView by lazy { findViewById<View>(R.id.root_view) }
    private val flMarkView by lazy { findViewById<View>(R.id.iv_wave_mark_flag) }
    private val loadingView by lazy { findViewById<View>(R.id.loadingView) }
    private val lottieView by lazy { findViewById<LottieAnimationView>(R.id.lottieView) }
    private val tvLoading by lazy { findViewById<TextView>(R.id.tvLoading) }
    private val tvSavingFileName by lazy { findViewById<TextView>(R.id.tvSavingFileName) }
    private val recordWaveView by lazy { findViewById<ViewGroup>(R.id.recordWaveView) }
    private val lottieSuccessView by lazy { findViewById<LottieAnimationView>(R.id.lottie_success) }
    private val saveSuccessView by lazy { findViewById<ViewGroup>(R.id.saveSuccessView) }
    private val groupOldWaveViews by lazy { findViewById<View>(R.id.group_old_wave_views) }
    private val waveView by lazy { findViewById<WaveView>(R.id.wv_record) }
    private val waveViewContainer by lazy { findViewById<ViewGroup>(R.id.fl_wave_container) }
    private val waveRecycleView by lazy { findViewById<SmallCardWaveRecyclerView>(R.id.rv_wave) }
    private val tvTimeText by lazy { findViewById<TextView>(R.id.recordTime) }
    private val tvFileName by lazy { findViewById<TextView>(R.id.tvFileName) }
    private val tvSaveSuccess by lazy { findViewById<TextView>(R.id.tvSaveSuccess) }
    private val btnAddTextMark by lazy { findViewById<AppCardButton>(R.id.ivAddMark) }
    private val btnSaveFile by lazy { findViewById<AppCardButton>(R.id.ivSaveFile) }
    private val btnSwitchState by lazy { findViewById<ImageView>(R.id.ivStartOrPause) }
    private val animateControl by lazy { SmallCardAnimateControl() }
    private val startRecorderView by lazy { findViewById<View>(R.id.ivStartView) }
    private val restoreCardDataRunnable = SmallCardRunnable(this) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            CardDataUtils.obtainData()
        }
        addRestoreCardDataRunnable()
    }
    private var recorderAppCardVersionCode = -1

    init {
        inflate(context, R.layout.layout_record, this)
        btnAddTextMark.setOnClickListener(this)
        btnSaveFile.setOnClickListener(this)
        btnSwitchState.setOnClickListener(this)
        startRecorderView.setOnTouchListener { v, event ->
            if (!isCardDataInitialized() || data.recordState == RecorderState.INIT) {
                if (event.actionMasked == MotionEvent.ACTION_UP) {
                    btnSwitchState.callOnClick()
                }
                true
            } else {
                false
            }
        }
        saveSuccessView.setOnClickListener(this)
        recordWaveView.setOnClickListener(this)
        saveSuccessView.setOnClickListener(this)
        waveRecycleView.setOnTouchListener { _, motionEvent ->
            if (motionEvent.actionMasked == MotionEvent.ACTION_UP) {
                onClick(waveRecycleView)
            }
            return@setOnTouchListener true
        }
        doOnLayoutChange { view, newRect, _ ->
            if (newRect.width() > 0) {
                val radio = getWHRadio()
                "doOnLayoutChange,radio=$radio,newRect.width()=${newRect.width()}, height=${newRect.height()}".log()
                updateLoadingSaveTextSize(radio)
            }
        }
        "SmallCardLayout init".log()
    }

    /**
     * 处理卡片缩放字体大小适配
     */
    private fun updateLoadingSaveTextSize(radio: Float) {
        val loadingTextSizeDefault =
            resources.getDimension(R.dimen.small_card_loading_text_size)
        val targetLoadingSize = loadingTextSizeDefault * radio
        tvLoading.setTextSizePX(targetLoadingSize)
        tvSavingFileName.setTextSizePX(targetLoadingSize)

        val successTextSizeDefault =
            resources.getDimension(R.dimen.small_card_succes_text_size)
        val targetSuccessSize = successTextSizeDefault * radio
        tvSaveSuccess.setTextSizePX(targetSuccessSize)
        tvFileName.setTextSizePX(targetSuccessSize)
    }

    override fun onClick(v: View?) {
        if (AppCardUtils.isFastDoubleClick()) {
            return
        }
        when (v) {
            btnAddTextMark -> {
                if (isCardDataInitialized() && data.recordState != RecorderState.INIT) {
                    context.addTextMark()
                }
            }
            btnSwitchState -> {
                if (!isCardDataInitialized() || data.recordState == RecorderState.INIT) {
                    context.startRecorderService()
                } else {
                    context.switchRecorderState()
                }
            }
            btnSaveFile -> {
                if (isCardDataInitialized()) {
                    context.saveRecorderFile()
                }
            }
            saveSuccessView -> context.startBrowseFileActivityToViewFile(data.fileName)
            waveRecycleView, recordWaveView, saveSuccessView -> context.launchSoundRecorderToFront()
        }
    }

    fun refreshData(data: SmallCardData) {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            setData(data)
        } else {
            post(SmallCardRunnable(this) {
                setData(data)
            })
        }
    }

    private fun setData(data: SmallCardData) {
        try {
            if (!isVisible) {
                return
            }
            AppCardUtils.appPackageName = data.packageName
            recorderAppCardVersionCode = data.versionCode
            if (recorderAppCardVersionCode == -1) {
                recorderAppCardVersionCode = CardVersionUtils.getRecordAppVersionCode(context, data.packageName)
            }
            setCardBackgroundColor(data)
            setTimeText(data)
            setImageRes(data)
            setSaveFileView(data)
            // 标记、保持按钮的显示隐藏
            setShowOrHideView(data)
            setWaveViewData(data)
            setShowOrHideLoadingView(data)
            setContentDescription(data)
            this.data = data
            addRestoreCardDataRunnable()
        } catch (e: Exception) {
            e.log()
        }
    }

    private fun isRecordAppVersionCode3OrLater(): Boolean =
        recorderAppCardVersionCode >= CardVersionUtils.VERSION_CODE_3

    private fun setCardBackgroundColor(data: SmallCardData) {
        if (data.cardBgDrawableRes > 0) {
            cardRootView.setBackgroundDrawableNecessary(data.cardBgDrawableRes)
        } else if (!isRecordAppVersionCode3OrLater()) {
            // 老版本录音，显示老卡片波形样式，背景默认为白色
            cardRootView.setBackgroundResourceNecessary(R.color.breeno_card_background_color)
        } else {
            cardRootView.setBackgroundResourceNecessary(R.drawable.breeno_card_background)
        }
    }

    private fun setContentDescription(data: SmallCardData) {
        try {
            tvTimeText.contentDescription = data.timeDes
            recordWaveView.contentDescription = data.timeDes
            val markDesc = data.markDesc ?: ""
            if (btnAddTextMark.contentDescription != markDesc) {
                btnAddTextMark.contentDescription = markDesc
            }
            val recordStateDesc = data.recordStateDesc ?: ""
            if (btnSwitchState.contentDescription != recordStateDesc) {
                btnSwitchState.contentDescription = recordStateDesc
            }
            val saveDesc = data.saveDesc ?: ""
            if (btnSaveFile.contentDescription != saveDesc) {
                btnSaveFile.contentDescription = saveDesc
            }
            if (data.loadingTitleTalkBack.isNotBlank() && tvLoading.contentDescription != data.loadingTitleTalkBack) {
                tvLoading.contentDescription = data.loadingTitleTalkBack
            }
            if (tvSavingFileName.contentDescription != data.stateText) {
                tvSavingFileName.contentDescription = data.stateText
            }
        } catch (_: Exception) {
        }
    }

    private fun addRestoreCardDataRunnable() {
        removeRestoreCardDataRunnable()
        if (isCardDataInitialized() && isOnVisible) {
            if (data.recordState == RecorderState.PAUSED || data.recordState == RecorderState.RECORDING) {
                postDelayed(restoreCardDataRunnable, DURATION_5000)
            }
        }
    }

    private fun removeRestoreCardDataRunnable() {
        removeCallbacks(restoreCardDataRunnable)
    }

    private fun setShowOrHideLoadingView(data: SmallCardData) {
        try {
            if (lottieView.isAnimating && !data.showLoadingDialog) {
                lottieView.cancelAnimation()
            }
            if (loadingView.isVisible != data.showLoadingDialog) {
                loadingView.isVisible = data.showLoadingDialog
                if (data.showLoadingDialog) {
                    if (context.isNightMode()) {
                        lottieView.setAnimation("coui_lottie_loading_large_night.json")
                    } else {
                        lottieView.setAnimation("coui_lottie_loading_large.json")
                    }
                    lottieView.playAnimation()
                }
            }
            if (loadingView.isVisible) {
                tvLoading.setTextNecessary(data.loadingTitle)
                tvSavingFileName.setTextNecessary(data.stateText)
            }
        } catch (e: Exception) {
            e.log()
        }
    }

    /**
     * 等比缩放，取宽高最小值
     */
    private fun getWHRadio(): Float {
        return AppCardUtils.getTextSizeScaleRadio(context, min(width, height))
    }

    private fun setShowOrHideView(data: SmallCardData) {
        try {
            if (data.needRunMarkAndSaveAnimation) {
                //卡片点击开始录制，授权之后开始录音，执行展开动效
                animateControl.showMarkAndSaveFileView(true, this, btnAddTextMark, btnSaveFile) {
                    setMarkAndSaveViewPadding()
                }
            } else {
                if (data.recordState == RecorderState.INIT) {
                    hideMarkAndSaveFileView()
                } else {
                    animateControl.showMarkAndSaveFileView(
                        false,
                        this,
                        btnAddTextMark,
                        btnSaveFile
                    ) {
                        setMarkAndSaveViewPadding()
                    }
                }
            }
        } catch (_: Exception) {
        }
    }

    private fun setImageRes(data: SmallCardData) {
        kotlin.runCatching {
            if (isRecordAppVersionCode3OrLater()) {
                btnAddTextMark.fakeDisable = !data.markEnable
            } else {
                // 老版本卡片走ripple效果
                btnAddTextMark.fakeDisable = true
                btnSaveFile.fakeDisable = true
            }
            btnAddTextMark.setImageResourceNecessary(data.markSrc)
            btnSaveFile.setImageResourceNecessary(data.saveFileSrc)
            btnAddTextMark.setBackgroundDrawableNecessary(data.markBackGroundSrc)
            btnSaveFile.setBackgroundDrawableNecessary(data.saveBackGroundSrc)
            setRecordStateImg(data)
        }.onFailure { it.log() }
    }

    private fun setRecordStateImg(data: SmallCardData) {
        when (data.recordState) {
            RecorderState.INIT -> btnSwitchState.setImageResourceNecessary(data.recordInitSrc)
            RecorderState.PAUSED -> btnSwitchState.setImageResourceNecessary(data.recordPauseSrc)
            RecorderState.RECORDING -> btnSwitchState.setImageResourceNecessary(data.recordResumeSrc)
        }
    }

    private fun setTimeText(data: SmallCardData) {
        try {
            val radio = getWHRadio()
            if (isRecordAppVersionCode3OrLater()) {
                val textSize = radio * resources.getDimension(
                    if (data.timeText.length > RECORDER_TIME_LONG_VALUE) {
                        R.dimen.record_time_small_size
                    } else {
                        R.dimen.record_time_size
                    }
                )
                // 新卡：录制时长大于5（00:00:00）则将字体改小
                tvTimeText.setTextSizePX(textSize)
            } else {
                // 老版本录音+新卡，录制时长字体大小更改为老卡一致
                tvTimeText.setTextSizePX(radio * resources.getDimension(R.dimen.record_time_size_old_version))
            }
            tvTimeText.fixTextFlash(data.timeText)
            if (isCardDataInitialized()) {
                if (this.data.timeTextColor != data.timeTextColor) {
                    tvTimeText.setTextColor(context.getAppContext().getColor(data.timeTextColor))
                }
            } else {
                tvTimeText.setTextColor(context.getAppContext().getColor(data.timeTextColor))
            }
        } catch (_: Exception) {
        }
    }

    private fun setSaveFileView(data: SmallCardData) {
        try {
            if (isCardDataInitialized()) {
                if (this.data.saveFileState != data.saveFileState) {
                    doSetSaveFileView(data)
                }
            } else {
                doSetSaveFileView(data)
            }
        } catch (_: Exception) {
        }
    }

    private fun doSetSaveFileView(data: SmallCardData) {
        when (data.saveFileState) {
            SaveFileState.SHOW_DIALOG -> {
                recordWaveView.isVisible = false
                saveSuccessView.isVisible = false
            }
            SaveFileState.SUCCESS -> {
                recordWaveView.isVisible = false
                doSuccessAnimation()
                tvSaveSuccess.text = data.stateText
                tvFileName.text = data.fileNameWithOutType
            }

            else -> {
                recordWaveView.isVisible = true
                saveSuccessView.isVisible = false
                cancelSuccessAnimation()
            }
        }
    }

    private fun doSuccessAnimation() {
        runCatching {
            if (!saveSuccessView.isVisible) {
                if (context.isNightMode()) {
                    lottieSuccessView.setAnimation(R.raw.breeno_card_success_dark)
                } else {
                    lottieSuccessView.setAnimation(R.raw.breeno_card_success_light)
                }
                saveSuccessView.isVisible = true
                lottieSuccessView.playAnimation()
            }
        }.onFailure { it.log() }
    }

    private fun cancelSuccessAnimation() {
        if (lottieSuccessView.isAnimating) {
            lottieSuccessView.cancelAnimation()
        }
    }

    private fun setWaveViewData(data: SmallCardData) {
        kotlin.runCatching {
            if (isRecordAppVersionCode3OrLater()) {
                // 新录音：显示新卡UI
                setWaveDataVersionCode3(data)
                if (isCardDataInitialized() && this.data.needRunMarkAndSaveAnimation
                    != data.needRunMarkAndSaveAnimation && data.needRunMarkAndSaveAnimation) {
                    waveRecycleView.doEnterAnimator()
                }
            } else {
                // 老版本录音，显示老卡波形、标记
                setWaveDataBelowVersionCode3(data)
            }
        }.onFailure { "setWaveViewData error $it".log() }
    }

    private fun setWaveDataVersionCode3(data: SmallCardData) {
        // 隐藏老版本波形VIEW
        flMarkView.isVisible = false
        groupOldWaveViews.isVisible = false
        // 显示新波形view
        if (!waveViewContainer.isVisible) {
            waveViewContainer.isVisible = true
        }

        if (data.recordState == RecorderState.RECORDING) {
            waveRecycleView.recorderIntervalUpdate(
                data.ampTotalSize,
                data.lastAmps ?: listOf(),
                true
            )
        } else {
            waveRecycleView.stopRecorderMove(data.ampTotalSize, data.lastAmps ?: listOf())
        }
        waveRecycleView.setMarkTimeList(data.lastMarks, data.recordTimeMill)
    }

    /**
     * 卡片版本<3的卡片样式，兼容老版本录音-新卡片样式
     */
    private fun setWaveDataBelowVersionCode3(data: SmallCardData) {
        kotlin.runCatching {
            if (waveViewContainer.isVisible) {
                waveViewContainer.isVisible = false
            }
            // 添加文字标记flag小旗子
            if (flMarkView.isVisible != data.showMarkNotice) {
                flMarkView.isVisible = data.showMarkNotice
            }
            groupOldWaveViews.isVisible = true
            waveView.apply {
                setAmpRadius(data.waveRadius)
                setAmpColor(data.ampColor)
                setAmpAnimatorDuration(data.animatorDuration)
                setAmpMinHeight(data.ampMinHeight)
                setAmpMaxHeight(data.ampMaxHeight)
                setAmpHeight(data.waveData)
                setMaxRandomHeightDP(data.ampMaxRandomHeight)
                if (Looper.getMainLooper().isCurrentThread) {
                    if (data.recordState == RecorderState.RECORDING) {
                        startDrawAmp()
                    } else {
                        stopDrawAmp()
                    }
                }
            }
        }
    }

    private fun hideMarkAndSaveFileView() {
        if (data.needRunMarkAndSaveAnimation) {
            // view在做展开动效，此时录制状态还为默认值
            "hideMarkAndSaveFileView return by needRunMarkAndSaveAnimation true".log()
            return
        }
        if (btnAddTextMark.alpha == 0f) {
            return
        }
        "hideMarkAndSaveFileView".log()
        btnAddTextMark.alpha = 0f
        btnAddTextMark.updateLayoutParams<ConstraintLayout.LayoutParams> {
            endToEnd = R.id.ivStartOrPause
            startToStart = ConstraintLayout.LayoutParams.UNSET
        }
        btnSaveFile.alpha = 0f
        btnSaveFile.updateLayoutParams<ConstraintLayout.LayoutParams> {
            startToStart = R.id.ivStartOrPause
            endToEnd = ConstraintLayout.LayoutParams.UNSET
        }
    }

    private fun setMarkAndSaveViewPadding() {
        val btnPadding: Int
        val btnWidthHeightPercent: Float
        val btnWidthHeight: Int
        if (isRecordAppVersionCode3OrLater()) {
            btnPadding = 0
            btnWidthHeightPercent =
                context.getFloatValue(R.dimen.small_card_mark_button_width_percent)
            btnWidthHeight = 0
        } else {
            // 老版本录音，传入老数据，老卡样式按钮无背景，需要增加padding扩大点击域
            btnPadding = resources.getDimensionPixelOffset(R.dimen.dp3)
            btnWidthHeightPercent = 1f
            btnWidthHeight = ViewGroup.LayoutParams.WRAP_CONTENT
        }
        btnAddTextMark.setPadding(btnPadding, btnPadding)
        btnAddTextMark.updateWidthPercent(btnWidthHeightPercent)
        btnAddTextMark.updateWidthHeight(btnWidthHeight)

        btnSaveFile.updateWidthPercent(btnWidthHeightPercent)
        btnSaveFile.setPadding(btnPadding, btnPadding)
        btnSaveFile.updateWidthHeight(btnWidthHeight)
    }

    private fun isCardDataInitialized(): Boolean {
        return this::data.isInitialized
    }

    private fun Context.doAction(action: String): Bundle? {
        return doAction(action, getWidgetCode())
    }

    private fun getWidgetCode(): String {
        return if (isCardDataInitialized()) {
            data.widgetCode
        } else {
            ""
        }
    }

    private fun Context.saveRecorderFile() {
        runBackground(SmallCardRunnable(this@SmallCardLayout) {
            doAction(ClickAction.CARD_SAVE_RECORDER_FILE)
        })
    }

    private fun Context.switchRecorderState() {
        "switchRecorderState".log()
        runBackground(SmallCardRunnable(this@SmallCardLayout) {
            doAction(ClickAction.CARD_SWITCH_RECORDER_STATUS)
        })
    }

    private fun Context.addTextMark() {
        runBackground(SmallCardRunnable(this@SmallCardLayout) {
            doAction(ClickAction.CARD_ADD_TEXT_MARK)
        })
    }

    /**
     * 启动透明activity，走侧边栏录音的流程
     */
    private fun Context.startRecorderService() {
        "startRecorderService".log()
        checkCanStartService { canStart ->
            if (canStart) {
                clearAllTask()
                checkRecorderPermission { hasPermission ->
                    // 按钮展开动效统一放到data.needRunMarkAndSaveAnimation中处理，避免应用锁场景无效展开导致UI错误
                    startTransparentActivity(METHOD_START_SERVICE)
                }
            }
        }
    }

    private fun Context.checkRecorderPermission(function: (Boolean) -> Unit) {
        runBackground(SmallCardRunnable(this@SmallCardLayout) {
            val flag = doAction(ClickAction.CHECK_RECORDER_PERMISSION)?.getBoolean("data", false) ?: false
            "checkRecorderPermission flag = $flag".log()
            post {
                function(flag)
            }
        })
    }

    /**
     * 检查是否能启动service
     */
    private fun Context.checkCanStartService(function: (Boolean) -> Unit) {
        runBackground(SmallCardRunnable(this@SmallCardLayout) {
            val flag = doAction(ClickAction.CHECK_CAN_START_SERVICE)?.getBoolean("data", false) ?: false
            "checkCanStartService flag = $flag".log()
            post {
                function(flag)
            }
        })
    }

    /**
     * 检查是否能启动service
     */
    private fun Context.clearAllTask() {
        doAction(ClickAction.CLEAR_ALL_TASK)
    }

    /**
     * 点击卡片整体布局，拉起录音，可能是录制界面，也可能是其他页面
     */
    private fun Context.launchSoundRecorderToFront() {
        startTransparentActivity(METHOD_JUST_START)
    }

    /**
     * 点击保存成功的页面，跳转到首页查看文件
     */
    private fun Context.startBrowseFileActivityToViewFile(fileName: String) {
        //理论上不会在保存成功的时候，正在录制，所以直接clear没问题
        clearAllTask()
        val intent = Intent("oplus.intent.action.provider.start_browse_file_from_smallcard").apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            putExtra(SHOULD_AUTO_FIND_FILE_NAME, fileName)
        }
        startActivity(intent)
    }

    /**
     * 启动透明界面，启动service或者仅仅拉起录音
     */
    private fun Context.startTransparentActivity(method: String = METHOD_JUST_START) {
        val intent = Intent("oplus.intent.action.provider.start_transparent_from_smallcard").apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            putExtra(SMALL_CARD_METHOD, method)
        }
        startActivity(intent, ActivityOptions.makeTaskLaunchBehind().toBundle())
    }

    private fun TextView.setTextNecessary(content: String) {
        if (text != content) {
            text = content
        }
    }

    private fun ImageView.setImageResourceNecessary(resId: Int) {
        if (getTag(R.id.tag_image_resource) != resId) {
            setImageDrawable(ContextCompat.getDrawable(context.getAppContext(), resId))
            setTag(R.id.tag_image_resource, resId)
        }
    }

    private fun View.setBackgroundDrawableNecessary(resId: Int) {
        if (getTag(R.id.tag_background_resource) != resId) {
            background = ContextCompat.getDrawable(context.getAppContext(), resId)
            setTag(R.id.tag_background_resource, resId)
        }
    }

    private fun View.setBackgroundResourceNecessary(resId: Int) {
        if (getTag(R.id.tag_background_resource) != resId) {
            setBackgroundResource(resId)
            setTag(R.id.tag_background_resource, resId)
        }
    }

    private fun View.setPadding(leftPadding: Int, rightPadding: Int) {
        if (paddingLeft != left || paddingRight != right) {
            updatePadding(left = leftPadding, right = rightPadding)
        }
    }

    private fun View.updateWidthPercent(widthPercent: Float) {
        (layoutParams as? ConstraintLayout.LayoutParams)?.let {
            if (it.matchConstraintPercentWidth != widthPercent) {
                updateLayoutParams<ConstraintLayout.LayoutParams> {
                    matchConstraintPercentWidth = widthPercent
                }
            }
        }
    }

    private fun View.updateWidthHeight(newValue: Int) {
        if (getTag(R.id.tag_view_width) != newValue) {
            updateLayoutParams<MarginLayoutParams> {
                width = newValue
                height = newValue
                setTag(R.id.tag_view_width, newValue)
            }
        }
    }

    private fun TextView.setTextSizePX(pxValue: Float) {
        if (getTag(R.id.tag_textview_size) != pxValue) {
            setTextSize(TypedValue.COMPLEX_UNIT_PX, pxValue)
            setTag(R.id.tag_textview_size, pxValue)
        }
    }

    fun onVisible() {
        isOnVisible = true
        CardDataUtils.register(context, this)
        "onVisible $this".log()
    }

    fun onInVisible() {
        isOnVisible = false
        CardDataUtils.unregister(context, this)
        removeRestoreCardDataRunnable()
        "onInVisible this".log()
        // 卡片不可见，波形停止滚动，避免录制中再切到可见时，中线左侧显示虚线
        if (isCardDataInitialized() && waveRecycleView.isVisible) {
            waveRecycleView.stopRecorderMove(data.ampTotalSize, data.lastAmps ?: listOf())
        }
    }

    /**
     * 负一屏、桌面卡片多屏拖拽不会触发onVisible、onInVisible会先执行onDetachedFromWindow，在执行onAttachedToWindow
     */
    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        CardDataUtils.register(context, this)
        "onAttachedToWindow $this".log()
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        CardDataUtils.unregister(context, this)
        removeRestoreCardDataRunnable()
        "onDetachedFromWindow $this".log()
    }
}