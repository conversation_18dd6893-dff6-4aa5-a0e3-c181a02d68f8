/*
 Copyright (C), 2008-2023 OPLUS Mobile Comm Corp., Ltd.
 File: CardDataUtils
 Description:
 Version: 1.0
 Date: 2023/2/16
 Author: ********(v-zhengt<PERSON><PERSON><EMAIL>)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2023/2/16 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.utils

import android.content.Context
import android.database.ContentObserver
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import com.oplus.soundrecorder.breenocardlibrary.bean.SmallCardData
import com.oplus.soundrecorder.breenocardlibrary.utils.AppCardUtils.log
import com.oplus.soundrecorder.breenocardlibrary.views.SmallCardLayout
import java.lang.ref.WeakReference

@Suppress("TooGenericExceptionCaught")
internal object CardDataUtils {
    private const val AUTHORITY = "com.soundrecorder.common.provider.recorder.smallcard"
    private val CONTENT_URI = Uri.parse("content://$AUTHORITY")
    private const val REFRESH_DATA = "refresh_data"
    private val mainHandler = Handler(Looper.getMainLooper())
    private val contentObserver = object : ContentObserver(mainHandler) {
        override fun onChange(selfChange: Boolean) {
            obtainData()
        }
    }
    private val listeners = arrayListOf<WeakReference<SmallCardLayout>>()
    private var applicationContext: Context? = null
    private var isRegister = false

    @JvmStatic
    fun obtainData() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) return
        AppCardUtils.runBackground {
            try {
                val contentProviderClient = applicationContext?.contentResolver?.acquireUnstableContentProviderClient(CONTENT_URI)
                val bundle = contentProviderClient?.call(REFRESH_DATA, null, null)
                //val bundle = applicationContext?.contentResolver?.call(AUTHORITY, REFRESH_DATA, null, null)
                val jsonData = bundle?.getString("data", "")
                val data = AppCardUtils.gson.fromJson(jsonData, SmallCardData::class.java)
                if (data != null) {
                    for (index in 0 until listeners.size) {
                        listeners.getOrNull(index)?.get()?.refreshData(data)
                    }
                }
            } catch (e: Exception) {
                e.log()
            }
        }
    }

    @JvmStatic
    fun register(context: Context, smallCardLayout: SmallCardLayout) {
        this.applicationContext = context.applicationContext
        listeners.removeIf { it.get() == null }
        if (listeners.none { it.get() == smallCardLayout }) {
            listeners.add(WeakReference(smallCardLayout))
        }
        if (!isRegister) {
            try {
                context.applicationContext.contentResolver.registerContentObserver(CONTENT_URI, true, contentObserver)
                isRegister = true
            } catch (e: Exception) {
                e.log()
            }
        }
    }

    @JvmStatic
    fun unregister(context: Context, smallCardLayout: SmallCardLayout) {
        listeners.removeIf { it.get() == null || it.get() == smallCardLayout }
        if (listeners.isEmpty() && isRegister) {
            try {
                context.applicationContext.contentResolver.unregisterContentObserver(contentObserver)
                isRegister = false
            } catch (e: Exception) {
                e.log()
            }
        }
    }

    /**
     * 获取当前内容观察者是否已注册的状态
     * @return Boolean true表示已注册，false表示未注册
     */
    @JvmStatic
    fun isContentObserverRegistered(): Boolean = isRegister
}