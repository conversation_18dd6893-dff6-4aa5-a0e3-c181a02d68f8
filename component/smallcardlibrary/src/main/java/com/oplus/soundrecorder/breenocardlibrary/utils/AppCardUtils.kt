/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: AppCardUtils
 Description:
 Version: 1.0
 Date: 2022/8/29
 Author: ********(v-z<PERSON><PERSON><PERSON><PERSON><PERSON>@oppo.com)
 -----------Revision History-----------
 <author> <date> <version> <desc>
 ******** 2022/8/29 1.0 create
 */

package com.oplus.soundrecorder.breenocardlibrary.utils

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.os.Build
import android.os.Bundle
import android.os.SystemClock
import android.text.TextUtils
import android.util.Log
import android.view.Gravity
import android.view.View
import android.widget.TextView
import androidx.core.view.updatePadding
import com.google.gson.Gson
import com.oplus.smartenginecustomlib.BuildConfig
import com.oplus.soundrecorder.breenocardlibrary.R
import com.oplus.soundrecorder.breenocardlibrary.utils.ScreenUtil.dp2px
import java.lang.ref.WeakReference
import java.util.Locale
import java.util.concurrent.Executors
import kotlin.math.abs
import kotlin.math.max

object AppCardUtils {
    const val PACKAGE_NAME = "com.coloros.soundrecorder"
    const val PROVIDER_AUTH = "com.soundrecorder.common.provider.recorder.smallcard"
    const val SMALL_CARD_METHOD = "small_card_method"
    const val METHOD_START_SERVICE = "method_start_service"
    const val METHOD_JUST_START = "method_just_start"
    const val SHOULD_AUTO_FIND_FILE_NAME = "should_auto_find_file_name"
    private val newSingleThreadExecutor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors())
    const val DURATION_5000 = 5_000L
    const val DURATION_1000 = 1_000L
    const val FLOAT_1 = 1F

    const val SMALL_CARD_WIDTH_DEFAULT = 154

    //录制时间字体大小 / 卡片大小的默认值
    const val DEFAULT_TEXT_SIZE_PERCENT = 16F / 154F
    var appPackageName: String? = null
    private var wkAppContext: WeakReference<Context>? = null
    private const val TAG = "SmallCardLayout"
    private const val CLICK_THRESHOLD = 500
    val gson by lazy { Gson() }
    private var lastTime: Long = 0

    @JvmStatic
    fun Context.getAppContext(): Context {
        var ctx = wkAppContext?.get()
        return if (ctx == null) {
            ctx = createPackageContext(appPackageName ?: PACKAGE_NAME, Context.CONTEXT_IGNORE_SECURITY)
            wkAppContext = WeakReference(ctx)
            ctx
        } else {
            ctx
        }
    }

    @JvmStatic
    fun isFastDoubleClick(): Boolean {
        val time = SystemClock.elapsedRealtime()
        if (time - lastTime <= CLICK_THRESHOLD) {
            return true
        }
        lastTime = time
        return false
    }

    @JvmStatic
    @SuppressLint("RtlHardcoded")
    fun TextView.fixTextFlash(text: String, forceUpdate: Boolean = false) {
        this.setTag(R.id.tag_textview_old_length, length())
        setText(text)
        val function = fun(force: Boolean) {
            val newText = this.text.toString()
            val newLength = newText.length
            val oLength = this.getTag(R.id.tag_textview_old_length) as? Int
            gravity = Gravity.LEFT or Gravity.CENTER_VERTICAL
            val textWidth = paint.measureText(newText)
            val paddingS = max(0, ((width - textWidth) / 2).toInt())
            if (newLength != oLength || force) {
                updatePadding(paddingS)
            } else if (paddingS != paddingLeft && abs(paddingS - paddingLeft) > resources.getDimension(R.dimen.dp5)) {
                updatePadding(paddingS)
            }
        }
        if (width <= 0) {
            if (this.getTag(R.id.tag_textview_by_layout_change) != null) return
            this.setTag(R.id.tag_textview_by_layout_change, this)
            doOnLayoutChange { _, _, _ ->
                function.invoke(true)
            }
            return
        }
        function.invoke(forceUpdate)
    }

    @JvmStatic
    inline fun View.doOnLayoutChange(crossinline onLayout: (view: View, newRect: Rect, oldRect: Rect) -> Unit) {
        val newRect = Rect()
        val oldRect = Rect()
        val listener = View.OnLayoutChangeListener { v, l, t, r, b, ll, tt, rr, bb ->
            newRect.set(l, t, r, b)
            oldRect.set(ll, tt, rr, bb)
            if (newRect != oldRect) {
                onLayout(v, newRect, oldRect)
            }
        }
        addOnLayoutChangeListener(listener)
        addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(view: View) {}

            override fun onViewDetachedFromWindow(view: View) {
                removeOnAttachStateChangeListener(this)
                removeOnLayoutChangeListener(listener)
            }
        })
    }

    @JvmStatic
    fun runBackground(runnable: Runnable) {
        newSingleThreadExecutor.execute(runnable)
    }

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun Context.doAction(action: String, widgetCode: String): Bundle? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val bundle = Bundle()
                bundle.putString("key_widget_code", widgetCode)
                contentResolver.acquireUnstableContentProviderClient(PROVIDER_AUTH)?.use {
                    it.call(action, null, bundle)
                }
            } else {
                null
            }
        } catch (e: Exception) {
            e.log()
            null
        }
    }

    @JvmStatic
    fun Any.log() {
        if (BuildConfig.DEBUG) {
            if (this is Exception) {
                Log.i(TAG, "", this)
            } else {
                Log.i(TAG, "$this")
            }
        }
    }

    @JvmStatic
    fun isRTL(): Boolean {
        return TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL
    }

    @JvmStatic
    fun getTextSizeScaleRadio(context: Context, cardHeight: Int): Float {
        if (cardHeight <= 0) {
            return 1.0f
        }
        return cardHeight / context.dp2px(SMALL_CARD_WIDTH_DEFAULT)
    }
}