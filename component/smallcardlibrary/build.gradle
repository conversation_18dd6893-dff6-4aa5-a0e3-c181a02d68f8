plugins {
    id 'com.android.library'
    id 'org.jetbrains.kotlin.android'
}

apply from: "../../common_build.gradle"

android {
    namespace 'com.oplus.soundrecorder.breenocardlibrary'

    compileSdkVersion prop_compileSdkVersion
    buildToolsVersion prop_buildToolsVersion

    defaultConfig {
        minSdkVersion 26
        targetSdkVersion prop_targetSdkVersion
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        encoding 'UTF-8'
        sourceCompatibility prop_targetCompatibility
        targetCompatibility prop_targetCompatibility
    }

    //版本号由模块版本号和后缀组成，固定配置
    version = "${prop_smallCardVersionName}" + "${versionSuffix}"
    apply plugin: 'obuildplugin'
    OBuildConfig {
        //可选，如果是独立的SDK模块，可以配置这个参数，配置后编译task必须为  publishAllPublicationsToReleaseRepository
        //standAloneSdk = true
        //debug = true
        groupId = "${prop_archivesGroupName}"
        //配置SDK的artifactId，如果不配置，会使用当前module的目录名为sdkArtifactId
        sdkArtifactId = "smallCardLib"
        //执行SDK打包任务，约定为assembleReleaseSplitPixel，执行后，会将SDK推送到SNAPSHOT-maven：并同时发布到JFrog，以便后续正式提测发布；
        //内置assembleOapm，assembleRelease都可以出包；如果配置了 standAloneSdk = true，则task需要变为 publishAllPublicationsToReleaseRepository
//        sdkExecuteTask = "assembleReleaseSplitPixel"
        sdkExecuteTask = "assemblePublishToReleaseRepository"
        moduleDescription = "smallCard lib sdk, only use for small card(2x2)."
    }
}

dependencies {

    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.material
    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core

    implementation libs.oplus.smartengine.lib
    implementation libs.oplus.smartengine.annotation
    //如果依赖其他组件，使用compileOnly，避免传递依赖，导致版本不一致
    implementation libs.gson
    implementation libs.lottie
    implementation libs.oplus.coui.core
    implementation libs.oplus.coui.recyclerview
}
