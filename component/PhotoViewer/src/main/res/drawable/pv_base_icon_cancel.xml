<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/pv_color_ripple"
    android:radius="23dp">
    <item>
        <layer-list>
            <item>
                <shape>
                    <size
                        android:width="46dp"
                        android:height="46dp" />
                    <solid android:color="#4DFFFFFF" />
                    <corners android:radius="23dp" />
                </shape>
            </item>
            <item android:gravity="center">
                <vector
                    android:width="14dp"
                    android:height="14dp"
                    android:viewportWidth="14"
                    android:viewportHeight="14">
                    <path
                        android:fillColor="#FFFFFFFF"
                        android:pathData="M13.3651,1.6142L12.3034,0.5525L9.6521,3.2042L7.0009,5.8558L1.6984,0.5525L0.6367,1.6142L5.9401,6.9167L0.6367,12.2192L1.6984,13.2808L7.0009,7.9775L12.3034,13.2808L13.3651,12.2192L8.0617,6.9167L13.3651,1.6142Z" />
                </vector>
            </item>
        </layer-list>
    </item>
</ripple>

