/*
 * Copyright 2018 stfalcon.com
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.photoviewer

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Parcelable
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import com.photoviewer.ui.PhotoViewerActivity

object PhotoViewer {
    @SuppressLint("StaticFieldLeak")
    internal var INSTANCE: PhotoViewerInfo<Parcelable>? = null

    /**
     * 启动大图预览界面
     */
    @JvmStatic
    fun AppCompatActivity.startWith(data: PhotoViewerInfo<Parcelable>, requestCode: Int) {
        INSTANCE = data
        if (INSTANCE == null) {
            throw IllegalArgumentException("INSTANCE not init,please use fun with(data: PhotoViewerInfo<Parcelable>)")
        }
        startActivityForResult(Intent(this, PhotoViewerActivity::class.java), requestCode)
    }

    /**
     * 更新动画targetView
     */
    @JvmStatic
    fun updateImageView(targetView: ImageView?) {
        INSTANCE?.targetView = targetView
    }

    val scaleType: ImageView.ScaleType
        get() = INSTANCE?.scaleType ?: ImageView.ScaleType.FIT_CENTER

    val onPageSelected: ((Int) -> Unit)?
        get() = INSTANCE?.onPageSelected

    val imageLoader: ((ImageView, Int) -> Unit)?
        get() = INSTANCE?.imageLoader

    val size: Int
        get() = INSTANCE?.images?.size ?: 0

    val releaseCache: ((ImageView, Int) -> Unit)?
        get() = INSTANCE?.releaseDataListener
    val index: Int
        get() = INSTANCE?.startPosition ?: 0
    val src: ((Int) -> Any?)?
        get() = INSTANCE?.src

    /**
     * 资源释放
     */
    internal fun release() {
        INSTANCE?.let {
            it.images = emptyList()
            it.releaseDataListener = null
            it.onPageSelected = null
            it.targetView = null
            it.imageLoader = null
            it.onResult = null
        }
        INSTANCE = null
    }
}

class PhotoViewerInfo<T : Parcelable>(
    var images: List<T> = emptyList(),
    var imageLoader: ((ImageView, Int) -> Unit)?,
    var releaseDataListener: ((ImageView, Int) -> Unit)?,
    var startPosition: Int = 0,
    var onPageSelected: ((Int) -> Unit)? = null,
    var isZoomingAllowed: Boolean = true,
    var isSwipeToDismissAllowed: Boolean = true,
    var targetView: ImageView? = null,
    var onResult: ((List<T>) -> Unit)? = null,
    var scaleType: ImageView.ScaleType = ImageView.ScaleType.FIT_CENTER,
    var src: (Int) -> Any?
)

interface PhotoDataSrc : Parcelable {
    /**
     * @return File Uri等等
     *
     */
    fun src(): Any
}