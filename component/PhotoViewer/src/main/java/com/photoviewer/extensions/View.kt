/*
 * Copyright 2018 stfalcon.com
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.photoviewer.extensions

import android.graphics.Rect
import android.view.View
import androidx.viewpager2.widget.ViewPager2
import com.soundrecorder.common.utils.PathInterpolatorHelper

internal val View?.globalVisibleRect: Rect
    get() = Rect().also { this?.getGlobalVisibleRect(it) }

internal fun View.doAnimateAlpha(to: Float, duration: Long) {
    clearAnimation()
    animate()
        .alpha(to)
        .setDuration(duration)
        .setInterpolator(PathInterpolatorHelper.couiMoveEaseInterpolator)
        .start()
}

internal fun ViewPager2.doOnPageSelected(onPageSelected: ((Int) -> Unit)? = null) {
    if (onPageSelected == null) {
        return
    }
    addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
        private val onPageChangeCallback = object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                onPageSelected.invoke(position)
            }
        }

        override fun onViewAttachedToWindow(view: View) {
            <EMAIL>(onPageChangeCallback)
        }

        override fun onViewDetachedFromWindow(view: View) {
            <EMAIL>(onPageChangeCallback)
            removeOnAttachStateChangeListener(this)
        }
    })
}

internal fun ViewPager2.hasIdle() = scrollState == ViewPager2.SCROLL_STATE_IDLE

