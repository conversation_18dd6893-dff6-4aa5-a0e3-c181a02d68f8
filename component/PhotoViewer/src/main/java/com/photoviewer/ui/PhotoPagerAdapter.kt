package com.photoviewer.ui

import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.recyclerview.widget.RecyclerView
import com.photoviewer.PhotoViewer
import com.photoviewer.photoview.PhotoView

internal class PhotoPager2Adapter : RecyclerView.Adapter<PhotoViewPager2ViewHolder>() {
    private val holders = mutableListOf<PhotoViewPager2ViewHolder>()
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ) = PhotoViewPager2ViewHolder(parent).apply {
        holders.add(this)
    }

    override fun onBindViewHolder(holder: PhotoViewPager2ViewHolder, position: Int) {
        holder.loadImage(position)
    }

    override fun getItemCount() = PhotoViewer.size

    internal fun onReleaseCaches() {
        holders.forEach {
            it.releaseCache()
        }
        holders.clear()
    }

    internal fun isScaled(index: Int): Boolean {
        return holders.firstOrNull { it.index == index }?.isScaled ?: false
    }

    internal fun resetScale(index: Int) {
        holders.firstOrNull { it.index == index }?.resetScale()
    }

    override fun onDetachedFromRecyclerView(recyclerView: RecyclerView) {
        holders.forEach {
            it.releaseCache()
        }
        holders.clear()
    }
}

internal class PhotoViewPager2ViewHolder(parent: ViewGroup) :
    RecyclerView.ViewHolder(FrameLayout(parent.context).apply {
        layoutParams = ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        addView(
            PhotoView(context).apply {
                isEnabled = PhotoViewer.INSTANCE?.isZoomingAllowed ?: false
                scaleType = PhotoViewer.scaleType
            },
            ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
        )
    }) {
    private val photoView by lazy { (itemView as ViewGroup).getChildAt(0) as PhotoView }

    internal var index = -1

    internal fun loadImage(index: Int) {
        releaseCache()
        this.index = index
        PhotoViewer.imageLoader?.invoke(photoView, index)
    }

    internal fun releaseCache() {
        if (index >= 0) {
            PhotoViewer.releaseCache?.invoke(photoView, index)
        }
    }

    internal val isScaled: Boolean
        get() = photoView.scale > 1f

    internal fun resetScale() = photoView.resetScale(true)
}