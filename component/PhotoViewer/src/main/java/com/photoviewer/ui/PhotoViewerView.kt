/*
 * Copyright 2018 stfalcon.com
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.photoviewer.ui

import android.content.Context
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.viewpager2.widget.MarginPageTransformer
import androidx.viewpager2.widget.ViewPager2
import com.soundrecorder.base.utils.BaseUtil
import com.photoviewer.PhotoViewer
import com.photoviewer.extensions.doAnimateAlpha
import com.photoviewer.extensions.doOnPageSelected
import com.photoviewer.extensions.hasIdle
import com.photoviewer.swipe.SwipeDirection
import com.photoviewer.swipe.SwipeDirectionDetector
import com.photoviewer.swipe.SwipeToDismissListener
import com.stfalcon.imageviewer.R

internal class PhotoViewerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {
    internal var currentPosition: Int
        get() = imagesPager.currentItem
        set(value) {
            imagesPager.currentItem = value
        }

    internal var onDismiss: (() -> Unit)? = null

    internal val isScaled
        get() = imagesAdapter.isScaled(currentPosition)

    private val rootContainer by lazy { findViewById<FrameLayout>(R.id.rootContainer) }
    private val backgroundView by lazy { findViewById<View>(R.id.backgroundView) }
    private val dismissContainer by lazy { findViewById<FrameLayout>(R.id.dismissContainer) }
    private val transitionImageContainer by lazy { findViewById<FrameLayout>(R.id.transitionImageContainer) }
    private val transitionImageView by lazy { findViewById<ImageView>(R.id.transitionImageView) }

    private val imagesPager by lazy { findViewById<ViewPager2>(R.id.imagesPager) }
    private val imagesAdapter by lazy { PhotoPager2Adapter() }
    private var transitionImageAnimator: TransitionImageAnimator? = null
    internal var onScaleChange: (() -> Unit)? = null
    internal var onClick: (() -> Unit)? = null
    internal var onSwipe: ((Float) -> Unit)? = null
    private var swipeDirection: SwipeDirection? = null
    private val directionDetector by lazy {
        SwipeDirectionDetector(context) {
            swipeDirection = it
        }
    }
    private var wasDoubleTapped = false
    private val gestureDetector by lazy {
        GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onDoubleTap(e: MotionEvent): Boolean {
                wasDoubleTapped = true
                onScaleChange?.invoke()
                return false
            }

            override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
                onClick?.invoke()
                return false
            }
        })
    }
    private val scaleDetector by lazy {
        ScaleGestureDetector(context, object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
            override fun onScale(detector: ScaleGestureDetector): Boolean {
                onScaleChange?.invoke()
                return false
            }
        })
    }

    private var wasScaled = false
    private val swipeToDismissHandler by lazy {
        SwipeToDismissListener(
            backgroundView,
            dismissContainer,
            onDismiss = { animateClose() },
            onSwipe = {
                onSwipe?.invoke(it)
            })
    }

    init {
        View.inflate(context, R.layout.view_photo_viewer, this)
        imagesPager.setPageTransformer(MarginPageTransformer(resources.getDimensionPixelOffset(R.dimen.pop_page_margin)))
        imagesPager.doOnPageSelected {
            PhotoViewer.imageLoader?.invoke(transitionImageView, it)
            PhotoViewer.onPageSelected?.invoke(it)
        }
        imagesPager.adapter = imagesAdapter
        imagesPager.setCurrentItem(PhotoViewer.index, false)
        PhotoViewer.imageLoader?.invoke(transitionImageView, PhotoViewer.index)
        // 禁止反色
        if (BaseUtil.isAndroidQOrLater) {
            isForceDarkAllowed = false
        }
    }

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        if (transitionImageAnimator?.isAnimating == true) {
            return true
        }
        if (wasDoubleTapped && event.action == MotionEvent.ACTION_MOVE && event.pointerCount == 1) {
            return true
        }
        handleUpDownEvent(event)
        if (swipeDirection == null && (scaleDetector.isInProgress || event.pointerCount > 1 || wasScaled)) {
            wasScaled = true
            return imagesPager.dispatchTouchEvent(event)
        }
        return if (isScaled) super.dispatchTouchEvent(event) else handleTouchIfNotScaled(event)
    }

    private fun handleTouchIfNotScaled(event: MotionEvent): Boolean {
        directionDetector.handleTouchEvent(event)
        val isSwipeToDismissAllowed = PhotoViewer.INSTANCE?.isSwipeToDismissAllowed ?: false
        return when (swipeDirection) {
            SwipeDirection.UP, SwipeDirection.DOWN -> {
                if (isSwipeToDismissAllowed && !wasScaled && imagesPager.hasIdle()) {
                    swipeToDismissHandler.onTouch(rootContainer, event)
                } else {
                    true
                }
            }
            SwipeDirection.LEFT, SwipeDirection.RIGHT -> {
                imagesPager.dispatchTouchEvent(event)
            }
            else -> true
        }
    }

    private fun handleUpDownEvent(event: MotionEvent) {
        if (event.action == MotionEvent.ACTION_UP) {
            handleEventActionUp(event)
        }
        if (event.action == MotionEvent.ACTION_DOWN) {
            handleEventActionDown(event)
        }
        scaleDetector.onTouchEvent(event)
        gestureDetector.onTouchEvent(event)
    }

    private fun handleEventActionDown(event: MotionEvent) {
        swipeDirection = null
        wasScaled = false
        imagesPager.dispatchTouchEvent(event)
        swipeToDismissHandler.onTouch(rootContainer, event)
    }

    private fun handleEventActionUp(event: MotionEvent) {
        wasDoubleTapped = false
        swipeToDismissHandler.onTouch(rootContainer, event)
        imagesPager.dispatchTouchEvent(event)
    }

    internal fun open(animate: Boolean, doOnStart: (Long) -> Unit) {
        prepareViewsForTransition()
        transitionImageAnimator = createTransitionImageAnimator()
        if (animate) {
            animateOpen(doOnStart)
        } else {
            doOnStart(0)
            prepareViewsForViewer()
        }
    }


    internal fun resetScale() {
        imagesAdapter.resetScale(currentPosition)
    }

    private fun animateOpen(doOnStart: (Long) -> Unit) {
        transitionImageAnimator?.animateOpen(
            onTransitionStart = { duration ->
                doOnStart.invoke(duration)
                backgroundView.doAnimateAlpha(1f, duration)
            }
        ) {
            prepareViewsForViewer()
        }
    }

    internal fun animateClose() {
        prepareViewsForTransition()
        transitionImageAnimator = createTransitionImageAnimator()
        transitionImageAnimator?.animateClose(
            onTransitionStart = { duration ->
                backgroundView.doAnimateAlpha(0f, duration)
            }
        ) { onDismiss?.invoke() }
    }

    private fun prepareViewsForTransition() {
        transitionImageContainer.isVisible = true
        imagesPager.isGone = true
    }

    private fun prepareViewsForViewer() {
        backgroundView.alpha = 1f
        transitionImageContainer.isGone = true
        imagesPager.isVisible = true
    }

    private fun createTransitionImageAnimator(): TransitionImageAnimator {
        return TransitionImageAnimator(getTargetView(), transitionImageView)
    }

    private fun getTargetView(): ImageView {
        val targetView = PhotoViewer.INSTANCE?.targetView
        return if (targetView?.isAttachedToWindow == true) {
            targetView
        } else {
            PhotoViewer.updateImageView(null)
            findViewById(R.id.centerTargetView)
        }
    }

    fun releaseAdapterCache() {
        imagesAdapter.onReleaseCaches()
    }
}

