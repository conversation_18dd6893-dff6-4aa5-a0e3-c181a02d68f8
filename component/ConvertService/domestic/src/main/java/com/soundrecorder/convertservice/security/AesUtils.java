/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AesUtils
 * * Description :  加密模式：AES(256)/CTR/NoPadding
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.security;


import java.security.NoSuchAlgorithmException;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import com.soundrecorder.common.constant.Constants;

import com.soundrecorder.base.utils.DebugUtil;

public class AesUtils {

    private static final String TRANSFORMATION = "AES/CTR/NoPadding";

    private static final String ALGORITHM = "AES";

    private static final int KEY_BYTE_SIZE = 256;

    private static final String IV_CONNECT = "%IV1%";

    public static String genKey() throws NoSuchAlgorithmException {
        KeyGenerator kgen = KeyGenerator.getInstance(ALGORITHM);
        kgen.init(KEY_BYTE_SIZE);
        SecretKey skey = kgen.generateKey();
        return Base64.getEncoder().encodeToString(skey.getEncoded());
    }

    /**
     * @param secretKey aes对称秘钥
     * @param content
     * @return
     */
    public static String encrypt(String secretKey, String content) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(Base64.getDecoder().decode(secretKey), ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
        byte[] iv = cipher.getIV();
        if (iv == null) {
            throw new EncryptException();
        }
        DebugUtil.i("AesUtils", "secretKey: " + secretKey + ", ivString: " + new String(iv, Constants.UTF_8));
        String ivStr = Base64.getEncoder().encodeToString(iv);
        byte[] enData = cipher.doFinal(content.getBytes(Constants.UTF_8));
        String enDataStr = Base64.getEncoder().encodeToString(enData);
        return enDataStr + IV_CONNECT + ivStr;
    }

    /**
     * @param secretKey aes对称秘钥
     * @param content
     * @return
     */
    public static String decrypt(String secretKey, String content) throws Exception {
        String[] dataIvs = content.split(IV_CONNECT);
        byte[] enData = Base64.getDecoder().decode(dataIvs[0]);
        byte[] iv = Base64.getDecoder().decode(dataIvs[1]);
        SecretKeySpec secretKeySpec = new SecretKeySpec(Base64.getDecoder().decode(secretKey), ALGORITHM);
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
        byte[] deData = cipher.doFinal(enData);
        return new String(deData, Constants.UTF_8);
    }

}

