/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  OkhttpHelperImpl
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.okhttphelper

import android.accounts.NetworkErrorException
import android.net.Uri
import com.soundrecorder.convertservice.bean.Constant
import com.google.gson.Gson
import com.google.gson.JsonParseException
import com.google.gson.reflect.TypeToken
import com.soundrecorder.common.databean.KeyWord
import com.soundrecorder.base.utils.DebugUtil
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import com.soundrecorder.convertservice.process.Code
import com.soundrecorder.convertservice.security.AesUtils
import com.soundrecorder.convertservice.security.EncryptException
import com.soundrecorder.convertservice.security.RSAUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.common.databean.BeanConvertText
import com.soundrecorder.convertservice.bean.*
import com.soundrecorder.convertservice.convert.ConvertServiceUtils.getSHA256HEX
import org.json.JSONObject
import org.json.JSONTokener
import java.io.ByteArrayOutputStream
import java.io.FileInputStream
import java.io.FileNotFoundException
import java.lang.reflect.Type
import java.util.*


class OkhttpHelperImpl : IOkhttpHelper {

    companion object {
        private const val TAG: String = "OkhttpHelperImpl"
    }

    private val mOkHttpClient: OkHttpClient = OkHttpClient()


    fun sign(
        appId: String,
        timestamp: String,
        params: Map<String?, String?>?,
        body: String,
        secret: String
    ): String {
        val sb = StringBuilder(appId + timestamp)
        if (params != null && params.isNotEmpty()) {
            val treeMap: TreeMap<String?, String?> = TreeMap(params)
            treeMap.forEach { (key: String?, value: String?) ->
                sb.append(key).append("=").append(value).append("&")
            }
            sb.substring(0, sb.length - 1)
        }
        val signStr = sb.toString() + body + secret
        return getSHA256HEX(signStr)
    }

    private fun getRequestBuilderWithHeader(requestId: String, json: String): Request.Builder {
        val ts = System.currentTimeMillis().toString()
        val sign = sign(Constant.APPID, ts, null, json, ConstantUrls.SECRET)
        DebugUtil.i(
            TAG, "DUID: ${HeaderHelper.duid} \r\n, ro.product.name: ${HeaderHelper.model} \r\n" +
                    ", otaVersion ${HeaderHelper.otaVersion} \r\n, ro.build.version.opporom: ${HeaderHelper.colorOSVersion} \r\n" +
                    ", ro.build.version.release ${HeaderHelper.androidVersion} \r\n, persist.sys.oppo.region: ${HeaderHelper.uRegion}\r\n" +
                    "serverPlanCode: ${HeaderHelper.serverPlanCode}\r\n,brand:${HeaderHelper.brand}"
        )
        return Request.Builder()
            .addHeader("model", HeaderHelper.model)
            .addHeader("otaVersion", HeaderHelper.otaVersion)
            .addHeader("romVersion", HeaderHelper.romVersion)
            .addHeader("colorOSVersion", HeaderHelper.colorOSVersion)
            .addHeader("androidVersion", HeaderHelper.androidVersion)
            .addHeader("uRegion", HeaderHelper.uRegion)
            .addHeader("uLang", HeaderHelper.uLang)
            .addHeader("clientVersionCode", HeaderHelper.clientVersionCode)
            .addHeader("clientPackage", HeaderHelper.clientPackage)
            .addHeader("duid", HeaderHelper.duid)
            .addHeader("timeStamp", ts)
            .addHeader("appId", Constant.APPID)
            .addHeader("reqId", requestId)
            .addHeader("sign", sign)
            .addHeader("serverPlanCode", HeaderHelper.serverPlanCode.toString())
            .addHeader("brand", HeaderHelper.brand)
    }

    private fun getCipherText(aesKey: String, plainText: String): String {
        val dataAesJson = AesUtils.encrypt(aesKey, plainText)
        val dataAesKey = RSAUtils.publicEncrypt(aesKey, ConstantUrls.PUBKEY)
        return Gson().toJson(BeanEncryption(dataAesKey, dataAesJson))
    }

    override fun getPresignedURLs(
        requestId: String,
        key: String,
        n: Int,
        uploadId: String
    ): BaseResponse<BeanGetPresignedURLs> {
        DebugUtil.i(TAG, "==getPresignedURLs in:requestId:$requestId,key:$key,n:$n")
        val plainJson = Gson().toJson(RequestGetPresignedURLs(key, n, uploadId))
        DebugUtil.i(TAG, "plainJson: $plainJson")

        val aesKey = AesUtils.genKey()
        val cipherJson = try {
            getCipherText(aesKey, plainJson)
        } catch (e: EncryptException) {
            DebugUtil.e(TAG, "getPresignedURLs encrypt failed!", e)
            throw e
        }

        val body = RequestBody.create(
            "application/json;charset=utf-8".toMediaType(), cipherJson
        )
        val request = getRequestBuilderWithHeader(requestId, cipherJson)
            .url(ConstantUrls.BACK_GETPREURL)
            .post(body)
            .build()

        val call = mOkHttpClient.newCall(request)
        val response = call.execute()
        val message = response.message
        val code = response.code
        val responseBody = response.body
        DebugUtil.i(TAG, "--->reponse: $response,message: $message ; code:$code")
        if (response.isSuccessful) {
            try {
                val stringResponseBody = responseBody?.string() ?: ""
                val plainJsonData = parsingResponseBodyToPlainJson(stringResponseBody, aesKey)
                checkUserTimeOut(stringResponseBody, plainJsonData)
                val data: BeanGetPresignedURLs? =
                    Gson().fromJson(plainJsonData, BeanGetPresignedURLs::class.java)
                data?.print()
                return BaseResponse(code, message, data)
            } catch (e: JsonParseException) {
                DebugUtil.e(TAG, "JSon Exception: $e")
                throw e
            }
        } else {
            DebugUtil.e(TAG, "response is not Successful!")
            throw NetworkErrorException("response is not Successful!\n response:$response")
        }
    }

    private fun checkUserTimeOut(stringResponseBody: String, plainJsonData: String) {
        val jsonTokener = JSONTokener(stringResponseBody)
        val jsonObject = jsonTokener.nextValue() as JSONObject
        val jsonCode = jsonObject.get("code")
        DebugUtil.i(TAG, "check jsonCode: $jsonCode")
        if (jsonCode == Code.TASKCODE_1011) {
            DebugUtil.i(TAG, "userTimeOut = $plainJsonData")
            throw UserTimeOutException(plainJsonData)
        }
    }


    override fun uploadOCS(
        requestId: String,
        presignedOCSURL: String,
        uri: Uri
    ): BaseResponse<Boolean> {
        DebugUtil.i(TAG, "====uploadOCS in")
        if (!FileUtils.isFileExist(uri)) {
            DebugUtil.e(TAG, "Uri file do not exists")
            throw FileNotFoundException("file do not exists!")
        }
        try {
            val inputStream =
                BaseApplication.getAppContext().getContentResolver().openInputStream(uri)
            val byteArray = ByteArray(Constant.BUFFER_SIZE_4096)
            val outputStream = ByteArrayOutputStream()
            while (inputStream?.read(byteArray, 0, byteArray.size) != -1) {
                outputStream.write(byteArray, 0, byteArray.size)
            }
            val body = RequestBody.create("text/plain".toMediaType(), outputStream.toByteArray())
            val request = Request.Builder()
                .url(presignedOCSURL)
                .put(body)
                .build()

            val call = mOkHttpClient.newCall(request)
            val response = call.execute()
            val message = response.message
            val code = response.code
            DebugUtil.i(TAG, "--->reponse: $response")
            if (response.isSuccessful) {
                DebugUtil.i(TAG, "send OCS is Success!")
                return BaseResponse(code, message, true)
            } else {
                DebugUtil.e(TAG, "response is not Successful!")
                throw NetworkErrorException("response is not Successful!\n response：$response")
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, e.toString())
            throw e
        }
    }


    override fun uploadOCS(
        requestId: String,
        presignedOCSURL: String,
        uri: Uri,
        begingOffset: Int,
        endOffset: Int
    ): BaseResponse<String> {
        DebugUtil.i(TAG, "==>uploadOCS in:begingOffset:$begingOffset ;endOffset:$endOffset")

        if (!FileUtils.isFileExist(uri)) {
            throw FileNotFoundException("file do not exists!")
        }
        val builder = MultipartBody.Builder()
        builder.setType(MultipartBody.FORM)
        try {
            val inputStream = BaseApplication.getAppContext().getContentResolver()
                .openInputStream(uri) as FileInputStream
            val byteArray = ByteArray(Constant.BUFFER_SIZE_4096)
            val outputStream = ByteArrayOutputStream()
            inputStream.skip(begingOffset.toLong())
            var cunrrentOffset = begingOffset
            var length = -1
            while (true) {
                length = inputStream.read(byteArray, 0, byteArray.size)
                if (length < 0) {
                    break
                }
                if (cunrrentOffset >= endOffset) {
                    break
                }
                outputStream.write(byteArray, 0, length)
                outputStream.flush()
                cunrrentOffset += length
            }

            val body = outputStream.toByteArray().toRequestBody("text/plain".toMediaType())
            val request = Request.Builder()
                .url(presignedOCSURL)
                .put(body)
                .build()

            val call = mOkHttpClient.newCall(request)
            val response = call.execute()
            val message = response.message
            val code = response.code
            val eTag = response.header("Etag")
            DebugUtil.i(TAG, "=====response $response; eTag $eTag")
            if (response.isSuccessful) {
                return BaseResponse(code, message, eTag)
            } else {
                DebugUtil.e(TAG, "response is not Successful!")
                throw NetworkErrorException("response is not Successful!\n response=$response")
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, e.toString())
            throw e
        }
    }

    override fun getUploadResult(
        requestId: String,
        uploadId: String?,
        key: String,
        abortFlag: Boolean,
        partETags: List<RequestGetUploadResult.ETags>?
    ): BaseResponse<BeanUploadResult> {
        DebugUtil.i(TAG, "===getUploadResult in,requestId=$requestId,uploadId=$uploadId,key=$key,abortFlag=$abortFlag")

        val plainJson = Gson().toJson(RequestGetUploadResult(uploadId, key, abortFlag, partETags))
        val aesKey = AesUtils.genKey()
        val cipherJson = try {
            getCipherText(aesKey, plainJson)
        } catch (e: EncryptException) {
            DebugUtil.e(TAG, "getUploadResult encrypt failed!", e)
            throw e
        }

        val body = RequestBody.create(
            "application/json;charset=utf-8".toMediaType(), cipherJson
        )
        val request = getRequestBuilderWithHeader(requestId, cipherJson)
            .url(ConstantUrls.BACK_GET_UPLOADRESULT)
            .post(body)
            .build()


        val call = mOkHttpClient.newCall(request)
        val response = call.execute()
        val message = response.message
        val code = response.code
        val responseBody = response.body
        DebugUtil.i(TAG, "======response $response")

        if (response.isSuccessful) {
            try {
                val plainJsonData = parsingResponseBodyToPlainJson(responseBody, aesKey)
                val data: BeanUploadResult? =
                    Gson().fromJson(plainJsonData, BeanUploadResult::class.java)
                return BaseResponse(code, message, data)
            } catch (e: JsonParseException) {
                DebugUtil.e(TAG, "JSon Exception: $e")
                throw e
            }
        } else {
            DebugUtil.e(TAG, "response is not Successful!")
            throw NetworkErrorException("response is not Successful!\n response:$response")
        }
    }


    override fun addTask(
        requestId: String,
        objectURL: String,
        audioTimeLine: String,
        codecFlag: Boolean?,
        duration: Long?,
        audioAllowedSave: Boolean?,
        roleType: Int?,
        roleNum: Int?,
    ): BaseResponse<BeanConvert> {
        DebugUtil.i(
            TAG,
            "===addTask in, requestId=$requestId, objectURL=$objectURL, duration=$duration"
        )

        val plainJson = Gson().toJson(
            RequestAddTask(
                objectURL,
                audioTimeLine,
                codecFlag,
                duration,
                audioAllowedSave,
                roleType,
                roleNum
            )
        )
        val aesKey = AesUtils.genKey()
        val cipherJson = try {
            getCipherText(aesKey, plainJson)
        } catch (e: EncryptException) {
            DebugUtil.e(TAG, "addTask encrypt failed!", e)
            throw e
        }

        val body = RequestBody.create(
            "application/json;charset=utf-8".toMediaType(), cipherJson
        )
        val request = getRequestBuilderWithHeader(requestId, cipherJson)
            .url(ConstantUrls.BACK_ADD_TASK)
            .post(body)
            .build()

        val call = mOkHttpClient.newCall(request)
        val response = call.execute()
        val message = response.message
        val code = response.code
        val responseBody = response.body
        DebugUtil.i(TAG, "=============response $response")

        if (response.isSuccessful) {
            try {
                val stringResponseBody = responseBody?.string() ?: ""
                DebugUtil.e(TAG, "addTask: stringResponseBody=$stringResponseBody")
                val plainJsonData = parsingResponseBodyToPlainJson(stringResponseBody, aesKey)
                val beanConvertBase =
                    Gson().fromJson(stringResponseBody, BeanConvertBase::class.java)
                beanConvertBase.print()
                val data = Gson().fromJson(plainJsonData, BeanConvertText::class.java)
                val beanConvert = BeanConvert(beanConvertBase, data)
                beanConvert.print()
                return BaseResponse(code, message, beanConvert)
            } catch (e: JsonParseException) {
                DebugUtil.e(TAG, "JSon Exception: $e")
                throw e
            }
        } else {
            DebugUtil.e(TAG, "response is not Successful!")
            throw NetworkErrorException("response is not Successful!\n response:$response")
        }
    }

    @Suppress("LongMethod")
    override fun queryTask(
        requestId: String,
        taskId: String,
        duration: Long?,
        keyWord: Int
    ): BaseResponse<BeanConvert> {
        DebugUtil.i(
            TAG,
            "queryTask: requestId=$requestId,taskID = $taskId,duration = $duration, keyWord = $keyWord"
        )
        val plainJson = JSONObject().put("taskID", taskId)
            .put("duration", duration)
            .put("keyWord", keyWord)
            .toString()
        val aesKey = AesUtils.genKey()
        val cipherJson = try {
            getCipherText(aesKey, plainJson)
        } catch (e: EncryptException) {
            DebugUtil.e(TAG, "queryTask encrypt failed!", e)
            throw e
        }

        val body = RequestBody.create(
            "application/json;charset=utf-8".toMediaType(), cipherJson
        )
        val request = getRequestBuilderWithHeader(requestId, cipherJson)
            .url(ConstantUrls.BACK_QUERY_TASK)
            .post(body)
            .build()

        val call = mOkHttpClient.newCall(request)
        val response = call.execute()
        val message = response.message
        val code = response.code
        val responseBody = response.body

        if (response.isSuccessful) {
            try {
                val stringResponseBody = responseBody?.string() ?: ""
                DebugUtil.e(TAG, "queryTask: stringResponseBody=$stringResponseBody")
                val beanConvertBase =
                    Gson().fromJson(stringResponseBody, BeanConvertBase::class.java)
                beanConvertBase.print()
                //todo change it
                var data = BeanConvertText("", "", "", "", null, null)
                if (beanConvertBase.code == 200) {
                    val plainJsonData = parsingResponseBodyToPlainJson(stringResponseBody, aesKey)
                    data = Gson().fromJson(plainJsonData, BeanConvertText::class.java)
                }

                if (beanConvertBase.code == 2001 || beanConvertBase.code == 2002) {
                    val plainJsonData = parsingResponseBodyToPlainJson(stringResponseBody, aesKey)
                    val jsonTokener = JSONTokener(plainJsonData)
                    val jsonObject = jsonTokener.nextValue() as JSONObject
                    val process = jsonObject.getString("process")
                    data = BeanConvertText(
                        "",
                        "",
                        "",
                        process,
                        null,
                        null
                    )
                }

                val beanConvert = BeanConvert(beanConvertBase, data)
                beanConvert.print()
                return BaseResponse(code, message, beanConvert)
            } catch (e: JsonParseException) {
                DebugUtil.e(TAG, "Exception: $e \n responseBody=$responseBody")
                throw e
            }
        } else {
            DebugUtil.e(TAG, "response is not Successful!")
            throw NetworkErrorException("response is not Successful!\n response=$response")
        }
    }


    override fun abortTask(requestId: String, taskId: String): BaseResponse<BeanConvert> {
        DebugUtil.i(TAG, "==abortTask in: requestId$requestId, taskId:$taskId")
        val plainJson = JSONObject().put("taskID", taskId).toString()

        val aesKey = AesUtils.genKey()
        val cipherJson = try {
            getCipherText(aesKey, plainJson)
        } catch (e: EncryptException) {
            DebugUtil.e(TAG, "abortTask encrypt failed!", e)
            throw e
        }
        val body = RequestBody.create(
            "application/json;charset=utf-8".toMediaType(), cipherJson
        )
        val request = getRequestBuilderWithHeader(requestId, cipherJson)
            .url(ConstantUrls.BACK_ABORT_TASK)
            .post(body)
            .build()


        val call = mOkHttpClient.newCall(request)
        val response = call.execute()
        val message = response.message
        val code = response.code
        val responseBody = response.body

        if (response.isSuccessful) {
            try {
                val stringResponseBody = responseBody?.string() ?: ""
                DebugUtil.e(TAG, "abortTask: stringResponseBody=$stringResponseBody")
                val beanConvertBase =
                    Gson().fromJson(stringResponseBody, BeanConvertBase::class.java)
                beanConvertBase.print()
                //todo change it
                val data: BeanConvertText =
                    BeanConvertText(
                        "",
                        "",
                        "",
                        "",
                        null,
                        null
                    )
                val beanConvert = BeanConvert(beanConvertBase, data)
                beanConvert.print()
                return BaseResponse(code, message, beanConvert)

//                val plainJsonData = parsingResponseBodyToPlainJson(responseBody, aesKey)
//                val data = Gson().fromJson(plainJsonData, BeanConvert::class.java)
//                //val data: BeanConvert? = BeanConvert(beanConvertBase)
//                return BaseResponse(code, message, data)
            } catch (e: JsonParseException) {
                DebugUtil.e(TAG, "Exception: $e")
                throw e
            }
        } else {
            DebugUtil.e(TAG, "response is not Successful!")
            throw NetworkErrorException("response is not Successful!")
        }
    }

    /**
     * 提取关键词
     */
    override fun extractKeyWords(requestId: String, texts: List<String>): BaseData<List<KeyWord>> {
        val json = Gson().toJson(texts)
        DebugUtil.i(TAG, "==> extractKeyWords start...")

        val aesKey = AesUtils.genKey()
        val cipherJson = try {
            getCipherText(aesKey, json)
        } catch (e: EncryptException) {
            DebugUtil.e(TAG, "abortTask encrypt failed!", e)
            throw e
        }

        val body = RequestBody.create(
            "application/json;charset=utf-8".toMediaType(), cipherJson
        )

        val request = getRequestBuilderWithHeader(requestId, cipherJson)
            .url(ConstantUrls.BACK_EXTRACT_KEY_WORDS)
            .post(body)
            .build()

        val call = mOkHttpClient.newCall(request)
        val response = call.execute()
        val message = response.message
        val code = response.code
        val responseBody = response.body

        if (response.isSuccessful) {
            try {
                val stringResponseBody = responseBody?.string() ?: ""
                DebugUtil.e(TAG, "<== extractKeyWords: stringResponseBody=$stringResponseBody")

                val baseData = Gson().fromJson(stringResponseBody, BaseData::class.java)
                // 解密
                val plainJsonData = parsingResponseBodyToPlainJson(stringResponseBody, aesKey)

                val type: Type = object : TypeToken<List<List<KeyWord>>>() {}.type
                val keyWords: List<List<KeyWord>> = Gson().fromJson(plainJsonData, type)

                val list = mutableListOf<KeyWord>()
                keyWords.forEach {
                    list.addAll(it)
                }
                return BaseData(
                    baseData.code,
                    baseData.msg,
                    baseData.serverPlanCode,
                    baseData.traceId,
                    baseData.showSwitch,
                    list
                )
            } catch (e: JsonParseException) {
                DebugUtil.e(TAG, "extractKeyWords Exception: ", e)
                throw e
            }
        } else {
            DebugUtil.e(
                TAG,
                "extractKeyWords response is not Successful! code: $code msg: $message"
            )
            throw NetworkErrorException("extractKeyWords response is not Successful!")
        }
    }

    private fun parsingResponseBodyToPlainJson(body: ResponseBody?, aesKey: String): String {
        val bodyJson = body?.string() ?: ""
        return parsingResponseBodyToPlainJson(bodyJson, aesKey)
    }

    private fun parsingResponseBodyToPlainJson(body: String?, aesKey: String): String {
        val bodyJson = body ?: ""
        DebugUtil.i(TAG, "parsingResponseBodyToPlainJson  json: $bodyJson")

        val jsonTokener = JSONTokener(bodyJson)
        val jsonObject = jsonTokener.nextValue() as JSONObject
        val jsonData = jsonObject.get("data")
        val plainJsonData = AesUtils.decrypt(aesKey, jsonData.toString())

        DebugUtil.i(TAG, "plainJsonData: ${plainJsonData.length}")
        return plainJsonData
    }

    class UserTimeOutException(override var message: String) : Exception()
}