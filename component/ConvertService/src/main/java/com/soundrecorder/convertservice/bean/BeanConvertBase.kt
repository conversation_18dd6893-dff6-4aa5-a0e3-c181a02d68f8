/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  BaseConvertBase
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.bean

import com.soundrecorder.base.utils.DebugUtil

open class BeanConvertBase(
    val code: Int,
    val msg: String,
    val serverPlanCode: Int,
    val traceId: String,
    val showSwitch: Boolean?
) {
    companion object {
        const val TAG = "BeanConvertBase"
    }

    open fun print() {
        DebugUtil.i(TAG, "code: $code, msg = $msg,serverPlanCode = $serverPlanCode,traceId=$traceId ")
    }
}
