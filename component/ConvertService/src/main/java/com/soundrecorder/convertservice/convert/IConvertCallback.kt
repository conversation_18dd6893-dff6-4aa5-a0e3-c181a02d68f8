/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  IConvertCallback
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.convert

import com.soundrecorder.common.databean.BeanConvertText
import com.soundrecorder.common.databean.ConvertStatus

interface IConvertCallback {

    fun onConvertStatusChange(
        mediaId: Long,
        convertStatus:
        ConvertStatus,
        errorCode: Int,
        errorMessage: String = "",
        convertAiTitle: Boolean = false
    )

    fun onConvertTextReceived(
        mediaId: Long,
        convertType: Int,
        convertTextResult: BeanConvertText,
        showSwitch: Boolean,
        convertAiTitle: Boolean = false
    )

    fun onConvertProgressChanged(mediaId: Long, uploadProgress: Int, convertProgress: Int, serverPlanCode: Int)

    fun onConvertEnd(mediaId: Long) {}
}