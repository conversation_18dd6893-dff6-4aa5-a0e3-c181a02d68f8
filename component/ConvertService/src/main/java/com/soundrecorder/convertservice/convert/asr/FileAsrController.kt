/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: FileAsrController
 * Description:
 * Version: 1.0
 * Date: 2025/3/27
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2025/3/27 1.0 create
 */

package com.soundrecorder.convertservice.convert.asr

import android.content.Context
import android.database.Cursor
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import android.provider.MediaStore
import androidx.core.os.bundleOf
import com.oplus.recorderlog.util.GsonUtil
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.NetworkUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.common.databean.DmpInsertBean
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.CollectionInfoDbUtils
import com.soundrecorder.common.db.CursorHelper
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.FileDealUtil
import com.soundrecorder.common.utils.SingleThread
import com.soundrecorder.convertservice.bean.FileAsrResult
import com.soundrecorder.convertservice.convert.IConvertCallback
import com.soundrecorder.convertservice.convert.IConvertTextRunnableProgress
import com.soundrecorder.convertservice.convert.NewConvertResultUtil
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.convertService.beans.DmpInsertBeanInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.translate.asr.file.IFileAsrKit
import com.soundrecorder.translate.asr.file.AIFileAsrClient
import com.soundrecorder.translate.asr.FileAsrManager
import com.soundrecorder.translate.asr.listener.IFileAsrCallBack
import com.soundrecorder.translate.AsrConstants
import java.io.File
import java.util.*
import java.util.concurrent.atomic.AtomicBoolean

class FileAsrController(val context: Context, val convertAiTitle: Boolean, val mediaId: Long, val callId: String = UUID.randomUUID().toString()) {
    companion object {
        const val MSG_START_ASR = 0x1
        const val MSG_STOP_ASR = 0x2
        const val MSG_DESTROY = 0x3
        const val MSG_ASR_OVER_TIME = 0x4
    }

    private val logTag = "FileAsrController"
    private var handlerThread: HandlerThread? = null
    private var workHandler: Handler? = null
    private var fileAsrManager: FileAsrManager? = null
    private val stopFlag = AtomicBoolean(false)
    var statusController: FileAsrStatusController? = null

    private val browseFileApi by lazy {
        Injector.injectFactory<BrowseFileInterface>()
    }

    private val fileAsrCancelThread = SingleThread("fileAsrCancelThread")

    fun initHandlerThread() {
        DebugUtil.i(logTag, "initHandlerThread")
        handlerThread = HandlerThread("FileAsr-$mediaId")
        handlerThread?.start()
        handlerThread?.looper?.let {
            workHandler = object : Handler(it) {
                // 消息处理的操作
                override fun handleMessage(msg: Message) {
                    when (msg.what) {
                        MSG_START_ASR -> doStartAsr()
                        MSG_STOP_ASR -> doStopAsr()
                        MSG_DESTROY -> doDestroy()
                    }
                }
            }
        }
    }

    fun initStatusController(convertUiCallback: IConvertCallback?, runCallback: IConvertTextRunnableProgress?) {
        if (statusController == null) {
            statusController = FileAsrStatusController(mediaId, convertUiCallback, runCallback)
        }
        statusController?.timeOutListener = {
            doConvertEnd()
        }
    }

    /**
     * 开始asr
     */
    fun startAsr() {
        workHandler?.sendEmptyMessage(MSG_START_ASR)
    }

    /**
     * 停止asr
     */
    fun stopAsr() {
        workHandler?.sendEmptyMessage(MSG_STOP_ASR)
    }

    /**
     * 销毁asr
     */
    fun destroy(isCancel: Boolean = false) {
        DebugUtil.d(logTag, "destroy, isCancel:$isCancel")
        workHandler?.removeCallbacksAndMessages(null)
        /*
        ID:9463565 标题:【AI录音】【STR4】【必现】【24018内销】非实时转文本，点击取消转写无效
        因AIunit插件侧在开始asr时加了线程阻塞10分钟，客户端取消转写时使用新的线程 fileAsrCancelThread
        */
        fileAsrCancelThread.post {
            if (isCancel) {
                statusController?.onAsrCancel(convertAiTitle)
            }
            statusController?.onProcessEnd(convertAiTitle)
            doDestroy()
        }
    }

    private fun doStartAsr() {
        DebugUtil.i(logTag, "doStartAsr callId = $callId, mediaId = $mediaId")
        runCatching {
            if (fileAsrManager == null) {
                fileAsrManager = FileAsrManager(mediaId, object : IFileAsrCallBack {
                    override fun onAsrStart() {
                        workHandler?.post {
                            val record = MediaDBUtils.queryRecordById(mediaId)
                            if (record == null) {
                                DebugUtil.e(logTag, "doStartAsr onAsrStart record is null")
                                return@post
                            }
                            ConvertDbUtil.deleteByRecordId(mediaId)
                            ConvertDbUtil.insert(initReq(record))
                        }
                    }

                    override fun onStatus(code: Int?, msg: String?) {
                        if (stopFlag.get()) {
                            DebugUtil.i(logTag, "onStatus stop...")
                            return
                        }

                        if (!ignoreStatusCode(code)) {
                            workHandler?.post {
                                /*优先判断是否没网*/
                                val errorCode = if (NetworkUtils.isNetworkInvalid(context)) {
                                    ConvertStatus.CONVERT_STATUS_NO_NETWORK
                                } else {
                                    ConvertStatus.EXCEPTION
                                }
                                statusController?.onConvertStatusChange(errorCode)
                                doConvertEnd()
                            }
                        }
                    }

                    override fun onAsrResult(result: String?) {
                        if (stopFlag.get()) {
                            DebugUtil.i(logTag, "onAsrResult stop...")
                            return
                        }
                        DebugUtil.i(logTag, "onAsrResult(),${result?.length}")
                        workHandler?.post {
                            runCatching {
                                val resultBean = GsonUtil.fromJson(result, FileAsrResult::class.java)?.toBeanConvertText()
                                if (resultBean == null) {
                                    toastAsrComplete(true)
                                    DebugUtil.w(logTag, "onAsrResult data is null")
                                    return@runCatching
                                } else {
                                    DebugUtil.i(logTag, "onAsrResult success")
                                    val context = BaseApplication.getAppContext()
                                    val convertTxtName = NewConvertResultUtil.genConvertTxtFileName(mediaId)
                                    val convertTxtPath = NewConvertResultUtil.genConvertTextPath(context, convertTxtName)
                                    NewConvertResultUtil.reWriteConvertFile(context, convertTxtName, resultBean)
                                    val speakerRoleNumber = resultBean.getSpeakerRoleNumber()
                                    /*外销不支持讲话人分离*/
                                    val showSwitch = !BaseUtil.isEXP() && speakerRoleNumber > 0
                                    val showRoleSwitch = if (showSwitch) ConvertDbUtil.SHOW_SWITH_TRUE else ConvertDbUtil.SHOW_SWITH_FALSE
                                    ConvertDbUtil.updateCanShowSpeakerRole(mediaId, showRoleSwitch)
                                    ConvertDbUtil.updateCompleteAndFilePathByRecordId(mediaId, convertTxtPath)
                                    ConvertDbUtil.updateSpeakerRoleOriginalNumber(mediaId, speakerRoleNumber)
                                    CollectionInfoDbUtils.addConvertCollectionInfo(content = mediaId.toString())
                                    statusController?.onAsrResultSuccess(mediaId, convertAiTitle, showSwitch, resultBean)
                                    if (!convertAiTitle) {
                                        val isEmptyConvert = resultBean.sublist.isNullOrEmpty()
                                        toastAsrComplete(isEmptyConvert)
                                        updateDmpAppIndex(mediaId, convertTxtPath)
                                    }
                                }
                            }.onFailure {
                                DebugUtil.e(logTag, "onAsrResult error $it")
                                statusController?.onConvertStatusChange(ConvertStatus.EXCEPTION)
                            }
                            doConvertEnd()
                        }
                    }

                    override fun onAsrStop() {
                    }
                })
            }
            // ui 提前渲染，init需要时间
            statusController?.onAsrStart()
            // 初始化asr
            fileAsrManager?.initAsr(bundleOf().apply { putString(IFileAsrKit.ASR_PARAM_CALL_ID, callId) }) {
                workHandler?.post {
                    if (stopFlag.get()) {
                        DebugUtil.i(logTag, "init result return by stop")
                        return@post
                    }
                    statusController?.onHeartBeat(FileAsrStatusController.ASR_INIT_OVER_TIME)
                    if (it) {
                        val record = MediaDBUtils.queryRecordById(mediaId)
                        statusController?.onSpecifyRecord(record)
                        val params = bundleOf().apply {
                            putLong(AIFileAsrClient.EXTRA_FILE_RECORD_ID, mediaId)
                            putLong(AIFileAsrClient.EXTRA_FILE_DURATION, record.duration)
                            putString(AIFileAsrClient.EXTRA_FILE_PATH, record.data)
                            putParcelable(AIFileAsrClient.EXTRA_FILE_URI, FileDealUtil.getUriForFile(File(record.data)))
                        }
                        // 初始化成功，才能start asr
                        fileAsrManager?.startAsr(params)
                    } else {
                        // 初始化失败，结束asr
                        statusController?.onConvertStatusChange(ConvertStatus.EXCEPTION)
                        doConvertEnd()
                    }
                }
            }
        }.onFailure {
            DebugUtil.e(logTag, "doStartAsr error $it")
        }
    }

    /**
     * updateDmpAppIndex
     *
     * @param mediaId 媒体ID
     * @param convertTxtPath 转换文本路径
     *
     * @return 成功失败
     */
    private fun updateDmpAppIndex(mediaId: Long, convertTxtPath: String?) {
        if (convertTxtPath.isNullOrEmpty() || mediaId <= 0) {
            DebugUtil.e(logTag, "updateDmpAppIndex input parameter is illegal, convertTxtPath:$convertTxtPath mediaId:$mediaId")
            return
        }

        val mediaDataBean = queryUpdateDmpBeanByIds(mediaId)
        if (mediaDataBean == null || mediaDataBean.mediaPath.isEmpty()) {
            DebugUtil.e(logTag, "updateDmpAppIndex fail to find audio record, media id:$mediaId.")
            return
        }

        mediaDataBean.textPath = convertTxtPath
        val dmpDataBeanList = mutableListOf<DmpInsertBeanInterface>().apply { add(mediaDataBean) }
        val result = browseFileApi?.updateDmpIndexFromOuter(dmpDataBeanList)
        DebugUtil.w(logTag, "updateDmpAppIndex browseFile.updateDmpIndexFromOuter result:$result.")
    }

    /**
     * queryUpdateDmpBeanByIds
     *
     * @param mediaId 媒体Id
     *
     * @return DmpInsertBean 数据Bean
     */
    private fun queryUpdateDmpBeanByIds(mediaId: Long): DmpInsertBean? {
        val selection = MediaStore.Audio.Media.SIZE + "!=0 AND " + MediaStore.Audio.Media._ID + " = " + mediaId + " AND " + MediaStore.Audio.Media
            .MIME_TYPE + CursorHelper.getsAcceptableAudioTypesSQL()
        // 限制录音指定mimeType
        val selectionArgs = CursorHelper.getsAcceptableAudioTypes()
        val recordProject = arrayOf(
            MediaStore.Audio.Media._ID, MediaStore.Audio.Media.DATA, MediaStore.Audio.Media.DISPLAY_NAME,
            MediaStore.Audio.Media.SIZE, MediaStore.Audio.Media.DATE_MODIFIED, MediaStore.Audio.Media.DURATION, MediaStore.Audio.Media.RELATIVE_PATH,
            MediaStore.Audio.Media.MIME_TYPE
        )

        var cursor: Cursor? = null
        var dmpInsertBean: DmpInsertBean? = null
        kotlin.runCatching {
            cursor = BaseApplication.getAppContext().contentResolver
                .query(MediaDBUtils.BASE_URI, recordProject, selection, selectionArgs, null)
            cursor?.use { cursor ->
                if (cursor.count > 0) {
                    DebugUtil.w(logTag, "queryUpdateDmpBeanByIds count:${cursor.count}")
                    while (cursor.moveToNext()) {
                        val tempDmpInsertBean = DmpInsertBean(cursor)
                        if (tempDmpInsertBean.mediaPath.isNotEmpty() && tempDmpInsertBean.id == mediaId) {
                            dmpInsertBean = tempDmpInsertBean
                            break
                        }
                        DebugUtil.w(logTag, "queryUpdateDmpBeanByIds media path:${tempDmpInsertBean.mediaPath} mediaId:${tempDmpInsertBean.id}.")
                    }
                }
            }
        }.onFailure {
            DebugUtil.e(logTag, "queryUpdateDmpBeanByIds query media data error:$it.")
        }.onSuccess {
            DebugUtil.i(logTag, "queryUpdateDmpBeanByIds query media data success.")
        }
        return dmpInsertBean
    }

    private fun toastAsrComplete(isEmptyConvert: Boolean) {
        val msg = if (isEmptyConvert) {
            context.getString(com.soundrecorder.common.R.string.convert_text_empty)
        } else {
            context.getString(
                com.soundrecorder.common.R.string.convert_end,
                MediaDBUtils.queryRecordById(mediaId)?.displayName?.title() ?: ""
            )
        }
        ToastManager.showShortToast(context, msg)
    }
    private fun doStopAsr() {
        DebugUtil.i(logTag, "doStopAsr, stopFlag=$stopFlag")
        runCatching {
            if (!stopFlag.get()) {
                stopFlag.set(true)
                fileAsrManager?.stopAsr(mediaId)
            }
        }.onFailure {
            DebugUtil.e(logTag, "doStopAsr error $it")
        }
    }

    private fun doDestroy() {
        DebugUtil.i(logTag, "doDestroy stopFlag $stopFlag")
        runCatching {
            if (!stopFlag.get()) {
                stopFlag.set(true)
                fileAsrManager?.stopAsr(mediaId)
            }
            fileAsrManager?.release(mediaId)
            fileAsrManager = null
            workHandler?.removeCallbacksAndMessages(null)
            handlerThread?.quitSafely()
            workHandler = null
            handlerThread = null
            fileAsrCancelThread.clearQueue()
            fileAsrCancelThread.quitThread()
        }.onFailure {
            DebugUtil.e(logTag, "doDestroy stop asr error $it")
        }
    }

    private fun ignoreStatusCode(code: Int?): Boolean {
        if (code == AsrConstants.Status.ASR_READ_FILE_NET_STATUS_NO_NET.code) {
            DebugUtil.d(logTag, "onStatus(),ignore.... no net cancel query runnable")
            return true
        }

        if (code == AsrConstants.Status.ASR_READ_FILE_NO_NET_QUERY_ASR.code) {
            DebugUtil.d(logTag, "onStatus(),ignore....asr query no net")
        }

        if (code == AsrConstants.Status.ASR_READ_FILE_NET_STATUS_HAS_NET.code) {
            DebugUtil.d(logTag, "onStatus(),ignore.... has net")
            return true
        }

        if (code == AsrConstants.Status.ASR_READ_FILE_NO_NET_SUMMARY_CONSUME_COUNT.code) {
            DebugUtil.d(logTag, "onStatus(),ignore....")
            return true
        }

        if (code == AsrConstants.Status.ASR_READ_FILE_QUERY_COMPLETE.code) {
            DebugUtil.d(logTag, "onStatus(),ignore....")
            return true
        }

        if (code == AsrConstants.Status.ASR_READ_FILE_UPLOAD_LOADING.code || code == AsrConstants.Status.ASR_READ_FILE_QUERY_LOADING.code) {
            DebugUtil.d(logTag, "onStatus(),ignore....")
            return true
        }
        if (code == AsrConstants.Status.ASR_READ_FILE_UPLOAD_SUCCESS.code) {
            DebugUtil.d(logTag, "onStatus(),ignore....file upload success")
            statusController?.onAsrFileUploadSuccess()
            return true
        }

        if (code == AsrConstants.Status.ASR_OFFLINE_HEART_BEAT.code) {
            DebugUtil.d(logTag, "onStatus(),heat....")
            statusController?.onHeartBeat(FileAsrStatusController.ASR_TASK_OVER_TIME)
            return true
        }
        return false
    }

    private fun initReq(record: Record): ConvertRecord {
        val convertRecord = ConvertRecord(mediaId)
        convertRecord.completeStatus = ConvertDbUtil.CONVERT_COMP_STATUS_TRANSLATING
        convertRecord.uploadAllUrl = null
        convertRecord.uploadRequestId = ""
        convertRecord.taskId = null
        convertRecord.convertTextfilePath = null
        convertRecord.mediaPath = record.data
        convertRecord.version = ConvertDbUtil.VERSION_2_SPEAKER
        convertRecord.serverPlanCode = ConvertDbUtil.SERVER_PLAN_ASR
        return convertRecord
    }

    private fun doConvertEnd() {
        destroy()
    }
}