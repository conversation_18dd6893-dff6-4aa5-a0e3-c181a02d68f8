/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertCheckUtils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.convert

import android.content.Context
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.RecordConstant
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.convertservice.bean.Constant

object ConvertCheckUtils {
    const val TAG = "ConvertCheckUtils"

    /**
     * 文件大小是否满足最小限制
     */
    fun isFileSizeMinMet(mFileSize: Long): Boolean {
        if (mFileSize <= Constant.INT_0) {
            return false
        }
        return true
    }

    /**
     * 文件大小是否满足最大限制
     */
    fun isFileSizeMaxMet(mFileSize: Long): Boolean {
        if (mFileSize >= Constant.SIZE_500M) {
            return false
        }
        return true
    }

    /**
     * 文件时长是否满足最小限制
     */
    fun isFileDurationMinMet(mFileDuration: Long): Boolean {
        if (mFileDuration <= Constant.INT_0) {
            return false
        }

        return true
    }

    /**
     * 文件时长是否满足最大限制
     */
    fun isFileDurationMaxMet(mFileDuration: Long): Boolean {
        if (mFileDuration >= Constant.TIME_5H) {
            return false
        }
        return true
    }

    /**
     * 文件mimeType是否满足条件
     */
    fun isFileFormatMet(mFileFormat: String): Boolean {
        DebugUtil.i(TAG, "isFileFormatMet: $mFileFormat")
        return when (mFileFormat) {
            RecordConstant.MIMETYPE_MP3,
            RecordConstant.MIMETYPE_3GPP,
            RecordConstant.MIMETYPE_AMR,
            RecordConstant.MIMETYPE_AMR_WB,
            -> true
            else -> {
                FunctionOption.IS_SUPPORT_WAV_AND_AAC && mFileFormat in arrayOf(
                    RecordConstant.MIMETYPE_WAV,
                    RecordConstant.MIMETYPE_ACC,
                    RecordConstant.MIMETYPE_ACC_ADTS
                )
            }
        }
    }

    /**
     * 根据转文本的uploadStatus和convertStatus 给用户的错误提示
     * 错误码有新增变动需要同步逻辑到ConvertServiceManager方法ConvertServiceManager
     */
    @JvmStatic
    fun getErrorMsgByStatus(
        context: Context,
        uploadStatus: Int,
        convertStatus: Int,
        errorMessage: String
    ): String {
        when (uploadStatus) {
            ConvertStatus.USERTIMEOUT_EXCEPTION -> return errorMessage

            ConvertStatus.UPLOAD_STATUS_NO_NETWORK,
            ConvertStatus.NETWORKERROR_EXCEPTION ->
                return context.getString(com.soundrecorder.common.R.string.network_disconnect)

            ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_FORMAT ->
                return context.getString(com.soundrecorder.common.R.string.convert_error_format)

            ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_DURATION ->
                return context.getString(com.soundrecorder.common.R.string.convert_error_duration_long)

            ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_SIZE ->
                return context.getString(com.soundrecorder.common.R.string.convert_error_size_long)

            ConvertStatus.UPLOAD_STATUS_UNSUPPORTED_FILE_DURATION_ZERO ->
                return context.getString(com.soundrecorder.common.R.string.convert_error_damage_file)

            ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL,
            ConvertStatus.ENCRYPT_EXCEPTION,
            ConvertStatus.JSONPARSE_EXCEPTION,
            ConvertStatus.EXCEPTION -> return context.getString(com.soundrecorder.common.R.string.convert_text_error)
            ConvertStatus.UPLOAD_STATUS_UPLOAD_ABORT_SUC -> return context.getString(com.soundrecorder.common.R.string.convert_text_stopped)
        }
        when (convertStatus) {
            ConvertStatus.CONVERT_STATUS_NO_NETWORK,
            ConvertStatus.NETWORKERROR_EXCEPTION ->  return context.getString(com.soundrecorder.common.R.string.network_disconnect)

            ConvertStatus.CONVERT_STATUS_QUERY_FAIL,
            ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL,
            ConvertStatus.CONVERT_STATUS_QUERY_TASK_TIMEOUT,
            ConvertStatus.ENCRYPT_EXCEPTION,
            ConvertStatus.JSONPARSE_EXCEPTION,
            ConvertStatus.EXCEPTION ->  return context.getString(com.soundrecorder.common.R.string.convert_text_error)

            ConvertStatus.CONVERT_STATUS_ABORTTASK_SUC,
            ConvertStatus.CONVERT_STATUS_ABORTTASK_FAIL -> return context.getString(com.soundrecorder.common.R.string.convert_text_stopped)
        }
        return errorMessage
    }
}