/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  SmartNameTaskManager
 * * Description: SmartNameTaskManager
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: W9035969
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9035969    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

import android.content.Intent
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.SmartNameParam
import com.soundrecorder.modulerouter.SeedingInterface
import com.soundrecorder.modulerouter.convertService.ConvertThreadManageAction
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_RECORD_ID
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_STATUS
import com.soundrecorder.modulerouter.playback.NOTIFY_SMART_NAME_STATUS_UPDATE
import com.soundrecorder.modulerouter.smartname.ISmartNameCallback
import com.soundrecorder.modulerouter.utils.Injector
import java.util.LinkedList
import java.util.Queue
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

object SmartNameTaskManager : ISmartNameProcess {

    const val LIMIT_SIZE = 3
    private const val TAG = "SmartNameTaskManager"
    private const val ALREADY_RUNNING = 1
    private const val OVER_LIMIT = 2
    private const val CAN_ADD_NEW = 3
    private const val CONVERT_RUNNING = 4

    private var taskMaps: ConcurrentHashMap<Long, ISmartNameRunnable> = ConcurrentHashMap(LIMIT_SIZE)
    var mUICallbacks: ConcurrentHashMap<Long, CopyOnWriteArrayList<ISmartNameCallback>> = ConcurrentHashMap(
        LIMIT_SIZE
    )

    var summarySessionIdList: ConcurrentHashMap<Long, String?> = ConcurrentHashMap()
    //private var mRunningTasks: Int = 0
    private var mWaitingTasks: Queue<Long> = LinkedList()

    private val convertThreadManageAction by lazy {
        Injector.injectFactory<ConvertThreadManageAction>()
    }

    private val seedingAction by lazy {
        Injector.injectFactory<SeedingInterface>()
    }

    fun addSessionId(mediaId: Long, sessionId: String) {
        if (!summarySessionIdList.containsKey(mediaId)) {
            summarySessionIdList[mediaId] = sessionId
        }
    }

    fun removeSessionId(mediaId: Long) {
        if (summarySessionIdList.containsKey(mediaId)) {
            summarySessionIdList.remove(mediaId)
        }
    }

    object RunnableProgress : ISmartNameProgressCallback {

        override fun preStartSmartName(mediaId: Long) {
        }

        override fun postSmartNameEnd(mediaId: Long) {
            removeTask(mediaId)
            waitTaskQueuePoll()
            checkFinalTaskEnd(mediaId)
        }

        override fun postCancelSmartName(mediaId: Long) {
            removeTask(mediaId)
        }
    }

    private fun removeTask(mediaId: Long) {
        if (taskMaps.containsKey(mediaId)) {
            DebugUtil.d(TAG, "removeTask, mediaId:$mediaId")
            taskMaps.remove(mediaId)
        }
    }

    fun waitTaskQueuePoll(fromConvertEnd: Boolean = false) {
        synchronized(SmartNameTaskManager::class) {
            if (!mWaitingTasks.isEmpty()) {
                val mediaId = mWaitingTasks.poll()
                DebugUtil.d(TAG, "waitTaskQueuePoll, waitRecord:$mediaId")
                if (mediaId != null) {
                    addOrResumeSmartNameTask(mediaId, null, fromConvertEnd)
                }
            }
        }
    }

    fun checkFinalTaskEnd(mediaId: Long) {
        if (taskMaps.isEmpty()) {
            DebugUtil.i(TAG, "checkFinalTaskEnd: onFinalJobEnd $mediaId")
            seedingAction?.sendRecordFileInnerRename(mediaId)
        }
    }

    private fun getConvertUiCallback(): ISmartNameCallback {
        return object : ISmartNameCallback {

            override fun onSmartNameStart(mediaId: Long, extras: Map<String, Any>?) {
                mUICallbacks[mediaId]?.forEach {
                    it.onSmartNameStart(mediaId, extras)
                }
            }

            override fun onSmartNameStop(mediaId: Long, extras: Map<String, Any>?) {
                mUICallbacks[mediaId]?.forEach {
                    it.onSmartNameStop(mediaId, extras)
                }
            }

            override fun onSmartNameFinished(
                mediaId: Long,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                mUICallbacks[mediaId]?.forEach {
                    it.onSmartNameFinished(mediaId, jsonResult, extras)
                }
            }

            override fun onSmartNameError(mediaId: Long, errorCode: Int, errorMsg: String?) {
                mUICallbacks[mediaId]?.forEach {
                    it.onSmartNameError(mediaId, errorCode, errorMsg)
                }
            }

            override fun onSmartNameEnd(mediaId: Long) {
                mUICallbacks[mediaId]?.forEach {
                    it.onSmartNameEnd(mediaId)
                }
            }
        }
    }

    private fun cancelTask(mediaId: Long): Boolean {
        val result = if (!taskMaps.containsKey(mediaId)) {
            DebugUtil.i(TAG, "maps not contains mediaId: $mediaId, no need to add task again")
            false
        } else {
            taskMaps[mediaId]?.cancelSmartName()
            true
        }
        return result
    }

    fun cancelAllTask() {
        taskMaps.forEach { (_, value) ->
            value.cancelSmartName()
        }
    }

    fun checkIsTaskRunning(mediaId: Long): Boolean {
        return taskMaps.containsKey(mediaId)
    }

    fun checkNoTaskRunning(): Boolean {
        DebugUtil.i(TAG, "checkNoTaskRunning = ${taskMaps.isEmpty()}")
        return taskMaps.isEmpty()
    }

    fun checkHasTaskRunning(): Boolean {
        DebugUtil.i(TAG, "checkHasTaskRunning = ${taskMaps.isNotEmpty()}")
        return taskMaps.isNotEmpty()
    }

    fun checkTaskRunning(excludeMedia: Long): Boolean {
        if (taskMaps.size > 1) {
            return true
        }
        return !taskMaps.isEmpty() && !taskMaps.containsKey(excludeMedia)
    }

    /**
     * 判断当前进行中的任务是否超过最大值3
     */
    fun checkTaskRunningMaxLimitSize(): Boolean {
        return taskMaps.size >= LIMIT_SIZE
    }

    fun release(mediaId: Long) {
        DebugUtil.i(TAG, "release: $mediaId")
        taskMaps[mediaId]?.release()
    }

    fun checkCanAddNewTask(mediaId: Long): Int {
        if (convertThreadManageAction?.checkIsTaskRunning(mediaId) == true) {
            DebugUtil.d(TAG, "checkCanAddNewTask, converting:$mediaId")
            return CONVERT_RUNNING
        }
        return if (taskMaps.containsKey(mediaId)) {
            DebugUtil.i(TAG, "checkCanAddNewTask: $mediaId already running, no need to ")
            ALREADY_RUNNING
        } else if (taskMaps.size >= LIMIT_SIZE) {
            DebugUtil.i(TAG, "maps limit reached : runnable $mediaId, add waiting")
            OVER_LIMIT
        } else {
            CAN_ADD_NEW
        }
    }

    fun releaseAll() {
        DebugUtil.i(TAG, "release all")
        mUICallbacks.clear()
        for (mediaId in taskMaps.keys) {
            release(mediaId)
        }
        summarySessionIdList.clear()
        mWaitingTasks.clear()
    }

    override fun startSmartName(mediaId: Long,  params: SmartNameParam?): Boolean {
        return addOrResumeSmartNameTask(mediaId, params)
    }

    /**
     * 添加智能命名任务
     */
    private fun addOrResumeSmartNameTask(mediaId: Long, params: SmartNameParam?, fromConvertEnd: Boolean = false): Boolean {
        val result = when (checkCanAddNewTask(mediaId)) {
            ALREADY_RUNNING -> {
                DebugUtil.i(TAG, "maps already contains mediaId: $mediaId, no need to add task again")
                true
            }
            OVER_LIMIT -> {
                DebugUtil.i(TAG, "maps limit reached : runnable $mediaId, add to waiting")
                addWaitingTasks(mediaId)
                false
            }
            CONVERT_RUNNING -> {
                DebugUtil.i(TAG, "maps convert running : runnable $mediaId, add to waiting")
                addWaitingTasks(mediaId)
                sendBroadcastNotifySmartNameStatus(mediaId)
                false
            }
            else -> {
                val smartNameRunnable = SmartNameRunnable(mediaId, params)
                smartNameRunnable.registerSmartNameUiCallback(getConvertUiCallback())
                smartNameRunnable.registerProgressCallback(RunnableProgress)
                taskMaps[mediaId] = smartNameRunnable
                smartNameRunnable.startSmartName()
                DebugUtil.i(TAG, "smartNameRunnable $mediaId start run, fromConvertEnd:$fromConvertEnd")
                if (!fromConvertEnd) {
                    sendBroadcastNotifySmartNameStatus(mediaId)
                }
                true
            }
        }
        return result
    }

    private fun sendBroadcastNotifySmartNameStatus(mediaId: Long, status: Boolean = true) {
        //send broadcast notify browseFile refresh list smart name status
        DebugUtil.d(TAG, "sendBroadcastNotifySmartNameStatus status:$status")
        val intent = Intent(NOTIFY_SMART_NAME_STATUS_UPDATE)
        intent.putExtra(KEY_NOTIFY_RECORD_ID, mediaId)
        intent.putExtra(KEY_NOTIFY_SMART_NAME_STATUS, status)
        LocalBroadcastManager.getInstance(BaseApplication.getAppContext()).sendBroadcast(intent)
    }

    fun checkIsWaitingTask(mediaId: Long): Boolean {
        return mWaitingTasks.contains(mediaId)
    }

    private fun addWaitingTasks(mediaId: Long) {
        if (!checkIsTaskRunning(mediaId) && !mWaitingTasks.contains(mediaId)) {
            DebugUtil.d(TAG, "addWaitingTasks, mediaId:$mediaId")
            mWaitingTasks.add(mediaId)
        }
    }

    override fun cancelSmartName(mediaId: Long): Boolean {
        return cancelTask(mediaId)
    }

    override fun releaseSmartName(mediaId: Long) {
        release(mediaId)
    }

    override fun registerSmartNameCallback(mediaId: Long, callback: ISmartNameCallback) {
        if (mUICallbacks[mediaId] == null) {
            mUICallbacks[mediaId] = CopyOnWriteArrayList()
        }
        mUICallbacks[mediaId]?.run {
            if (!contains(callback)) {
                add(callback)
            }
        }
        DebugUtil.i(TAG, "register ui callback $callback")
    }

    override fun unregisterSmartNameCallback(mediaId: Long, callback: ISmartNameCallback) {
        mUICallbacks[mediaId]?.run {
            remove(callback)
            if (isEmpty()) {
                mUICallbacks.remove(mediaId)
            }
        }
        DebugUtil.i(TAG, "unregister ui callback $callback, size = ${mUICallbacks[mediaId]?.size}")
    }
}