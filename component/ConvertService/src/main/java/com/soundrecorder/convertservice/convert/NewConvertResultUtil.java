/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  NewConvertResultUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.convert;

import static com.soundrecorder.base.utils.NumberConstant.NUM_0;
import static com.soundrecorder.base.utils.NumberConstant.NUM_100;
import static com.soundrecorder.base.utils.NumberConstant.NUM_255;

import android.content.Context;
import android.os.Environment;
import android.text.TextUtils;
import androidx.annotation.NonNull;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.ext.ExtKt;

import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.convertservice.util.RoleNameUtil;
import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import com.soundrecorder.common.databean.ConvertVad;
import com.soundrecorder.common.databean.BeanConvertText;

public class NewConvertResultUtil {

    public static final String TRANS_POSTFIX = ".txt";
    public static final String SPLIT_FLAG = "/";
    public static final String SPLIT_SPACE = " ";
    public static final String TAG = "NewConvertResultUtil";
    public static final String LINE_END = "\n";
    public static final String TRANS_PREFIX = "convert_";
    public static final String TRANS_SPLIT = "_";

    public static final String MP3_POSTFIX = ".mp3";
    public static final String AMR_POSTFIX = ".amr";


    /**
     * the number should match the field number of {@link ConvertVad}
     */
    public static final int CONTENT_LEN = 3;
    public static final String CONVERT_PATH_FLAG = "/convert";
    public static final int CONTENT_3 = 3;
    public static final int CONTENT_5 = 5;
    public static final int CONTENT_7 = 7;
    public static final int POS_0 = 0;
    public static final int POS_1 = 1;
    public static final int POS_2 = 2;
    public static final int POS_3 = 3;
    public static final int POS_4 = 4;
    public static final int POS_5 = 5;
    public static final int POS_6 = 6;
    public static final long US = 1000000L;
    public static final long MS = 1000L;

    public static String getConvertSavePath(Context context) {
        return context.getFilesDir() + CONVERT_PATH_FLAG;
    }

    public static synchronized List<ConvertVad> readConvertFile(Context appContext, String filename) throws IOException {
        long startTime = System.currentTimeMillis();
        if (!isExternalStorageReadable()) {
            throw new IOException("external storage not readable!");
        }
        File file = new File(getConvertSavePath(appContext), filename);
        DebugUtil.d(TAG, "readConvertFile: " + file.getName());
        if (!file.exists()) {
            throw new IOException(filename + " not found!");
        }

        List<ConvertVad> convertVads = new ArrayList<>();

        BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(file), StandardCharsets.UTF_8));
        try {
            while (true) {
                String line = br.readLine();
                if (TextUtils.isEmpty(line)) {
                    break;
                }
                String[] contents = line.split(SPLIT_FLAG);
                ConvertVad vad = null;
                if (contents.length == CONTENT_LEN - 1) {
                    vad = new ConvertVad(parseLong(contents[0]), parseLong(contents[1]), "");
                } else if (contents.length < CONTENT_7) {
                    vad = new ConvertVad(parseLong(contents[0]), parseLong(contents[1]), contents[2]);
                } else {
                    vad = new ConvertVad(parseLong(contents[0]), parseLong(contents[1]), contents[2], parseInt(contents[5]), contents[6]);
                }
                convertVads.add(vad);
            }
        } finally {
            br.close();
        }
        long endTime = System.currentTimeMillis();
        DebugUtil.d(TAG, "readConvertFile: size:" + convertVads.size() + ", time:" + (endTime - startTime));
        return convertVads;
    }

    private static int parseInt(String str) {
        int i = 0;
        try {
            i = Integer.parseInt(str);
        } catch (NumberFormatException e) {
            DebugUtil.e(TAG, "parseInt Exception: ", e);
        }
        return i;
    }

    private static long parseLong(String str) {
        long i = 0;
        try {
            i = Long.parseLong(str);
        } catch (NumberFormatException e) {
            DebugUtil.e(TAG, "parseLong Exception: ", e);
        }
        return i;
    }

    public static synchronized void reWriteConvertFile(Context appContext, String filename, BeanConvertText beanConvertText)
            throws IOException {
        long startTime = System.currentTimeMillis();
        if (!isExternalStorageWritable()) {
            throw new IOException("external storage not writeable!");
        }
        File file = checkOrCreateFile(appContext, filename);
        DebugUtil.d(TAG, "writeConvertFile: " + filename);

        FileOutputStream fileOutputStream = null;
        OutputStreamWriter outputStreamWriter = null;
        BufferedWriter bw = null;
        List<BeanConvertText.SubItem> sentences = beanConvertText.getSublist();
        if ((sentences != null) && (sentences.size() > 0)) {
            try {
                fileOutputStream = new FileOutputStream(file);
                outputStreamWriter = new OutputStreamWriter(fileOutputStream, StandardCharsets.UTF_8);
                bw = new BufferedWriter(outputStreamWriter);
                for (BeanConvertText.SubItem vad : sentences) {
                    if (!TextUtils.isEmpty(vad.getRecgText())) {
                        bw.write(combineContents(vad));
                        bw.flush();
                    }
                }
            } catch (Exception e) {
                DebugUtil.e(TAG, "writeConvertFile error", e);
            } finally {
                try {
                    if (fileOutputStream != null) {
                        fileOutputStream.close();
                    }
                } catch (Exception e) {
                    DebugUtil.e(TAG, "writeConvertFile fileOutputStream close error", e);
                }
                try {
                    if (outputStreamWriter != null) {
                        outputStreamWriter.close();
                    }
                } catch (Exception e) {
                    DebugUtil.e(TAG, "writeConvertFile outputStreamWriter close error", e);
                }
                try {
                    if (bw != null) {
                        bw.close();
                    }
                } catch (Exception e) {
                    DebugUtil.e(TAG, "writeConvertFile BufferedWriter close error", e);
                }
            }
        }
        long endTime = System.currentTimeMillis();
        DebugUtil.d(TAG, "writeConvertFile: size:" + ((sentences == null) ? 0 : sentences.size()) + ", time:" + (endTime - startTime));
    }

    public static synchronized void appendConvertVad(Context appContext, String filename, ConvertVad vad) throws IOException {
        long startTime = System.currentTimeMillis();
        if (!isExternalStorageWritable()) {
            throw new IOException("external storage not writeable!");
        }
        File file = checkOrCreateFile(appContext, filename);
        BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file, true), StandardCharsets.UTF_8));
        try {
            bw.append(combineContents(vad));
            bw.flush();
        } finally {
            bw.close();
        }
        long endTime = System.currentTimeMillis();
        DebugUtil.d(TAG, "appendConvertVad:  time:" + (endTime - startTime));
    }

    public static boolean isExternalStorageWritable() {
        String state = Environment.getExternalStorageState();
        return Environment.MEDIA_MOUNTED.equals(state);
    }

    public static boolean isExternalStorageReadable() {
        String state = Environment.getExternalStorageState();
        return Environment.MEDIA_MOUNTED.equals(state)
                || Environment.MEDIA_MOUNTED_READ_ONLY.equals(state);
    }

    private static String combineContents(@NonNull ConvertVad vad) {
        StringBuilder sb = new StringBuilder();
        sb.append(vad.getMStartTime()).append(SPLIT_FLAG);
        sb.append(vad.getMEndTime()).append(SPLIT_FLAG);
        sb.append(vad.getMText()).append(SPLIT_FLAG);
        sb.append(LINE_END);
        return sb.toString();
    }

    private static String combineContents(@NonNull BeanConvertText.SubItem sentence) {
        StringBuilder sb = new StringBuilder();
        long startTime = (long) (Float.parseFloat(sentence.getBeginTime()) * US);
        long endTime = (long) (Float.parseFloat(sentence.getEndTime()) * US);
        sb.append(startTime).append(SPLIT_FLAG);
        sb.append(endTime).append(SPLIT_FLAG);
        String text = sentence.getRecgText().replaceAll(SPLIT_FLAG, SPLIT_SPACE);
        sb.append(text).append(SPLIT_FLAG);
        String rawText = sentence.getRawText();
        if (rawText != null) {
            String str = rawText.replaceAll(SPLIT_FLAG, SPLIT_SPACE);
            sb.append(str).append(SPLIT_FLAG);
        }
        sb.append(sentence.getTimestamp()).append(SPLIT_FLAG);//add for deleted Timestamp
        sb.append(sentence.getRoleId()).append(SPLIT_FLAG);
        sb.append(RoleNameUtil.Companion.genRoleNameByRoleId(sentence.getRoleId())).append(SPLIT_FLAG);
        sb.append(LINE_END);
        return sb.toString();
    }

    public static File checkOrCreateFile(Context appContext, String filename) throws IOException {
        File fileDir = new File(getConvertSavePath(appContext));
        boolean dirMakeSuc = true;
        if (fileDir.exists()) {
            if (!fileDir.isDirectory()) {
                fileDir.delete();
                dirMakeSuc = fileDir.mkdirs();
            }
        } else {
            dirMakeSuc = fileDir.mkdirs();
        }
        if (!dirMakeSuc) {
            throw new IOException(filename + " make file path failed!");
        }
        File file = new File(fileDir, filename);
        if (!file.exists()) {
            if (!file.createNewFile()) {
                throw new IOException(filename + " make file failed!");
            }
        }
        DebugUtil.d(TAG, "checkOrCreateFile, file:" + file.getAbsolutePath());
        return file;
    }

    public static String genConvertTextPath(Context context, String fileName) {
        return NewConvertResultUtil.getConvertSavePath(context) + File.separator + fileName;
    }

    public static String genConvertTxtFileName(long recordId) {
        Record mediaRecord = MediaDBUtils.queryRecordById(recordId);
        if (mediaRecord == null) {
            DebugUtil.w(TAG, "genConvertTxtFileName mediaRecord is null");
            return "";
        }
        return genFileName(mediaRecord.getId(), ExtKt.title(mediaRecord.getDisplayName()));
    }

    public static String genFileName(long recordId, String mediaFileName) {
        String subNameWithoutPost = mediaFileName.toLowerCase();
        if (mediaFileName.endsWith(MP3_POSTFIX)) {
            subNameWithoutPost = mediaFileName.replace(MP3_POSTFIX, "");
        }
        if (mediaFileName.endsWith(AMR_POSTFIX)) {
            subNameWithoutPost = mediaFileName.replace(AMR_POSTFIX, "");
        }
        String name = TRANS_PREFIX + recordId + TRANS_SPLIT + subNameWithoutPost;
        byte[] buff = name.getBytes(StandardCharsets.UTF_8);
        DebugUtil.d(TAG, "genFileName, buff.length:" + buff.length);

        String newFileName = "";
        if (buff.length > NUM_255) {
            newFileName = name.substring(NUM_0, NUM_100);
        } else {
            newFileName = name;
        }
        return newFileName + TRANS_POSTFIX;
    }

    public static long getConvertFileSize(String filePath) {
        long size = 0;
        File file = new File(filePath);
        String[] contents = filePath.split(File.separator);
        String filename = contents[contents.length - 1];
        if (file.exists()) {
            size = file.length();
        } else {
            DebugUtil.e(TAG, filename + " not found.");
        }
        DebugUtil.d(TAG, "getConvertFileSize: " + filename + " size = " + size);
        return size;
    }
}
