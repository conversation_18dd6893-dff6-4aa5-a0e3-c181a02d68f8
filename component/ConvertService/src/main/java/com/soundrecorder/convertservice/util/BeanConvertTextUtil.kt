/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.util

import androidx.annotation.VisibleForTesting
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem


object BeanConvertTextUtil {

    private const val TAG = "BeanConvertTextUtil"
    private const val TIME_UNIT = 1000

    @JvmStatic
    fun genTextWithWords(inString: String?): List<String>? {
        if (inString.isNullOrEmpty()) {
            return null
        }
        val listString = ArrayList<String>()
        for (i in inString.indices) {
            if (i % 2 == 0) {
                listString.add(inString[i].toString())
            }
        }
        return listString
    }

    @JvmStatic
    fun genTextWithWordsTimeStamp(inString: String?): List<String>? {
        if (inString.isNullOrEmpty()) {
            return null
        }
        var listTimeStampString = inString.split(" ")
        listTimeStampString = listTimeStampString.filter {
            it != ""
        }
        if (listTimeStampString.isNullOrEmpty()) {
            return null
        }
        val listString = ArrayList<String>()
        for (i in listTimeStampString.indices) {
            listString.add(listTimeStampString[i])
        }
        return listString
    }

    @JvmStatic
    @Suppress("LongMethod")
    fun genListSubSentence(item: ConvertContentItem, isSupportXunFeiOrByte: Boolean) {
        item.apply {
            if (listSubSentence != null) {
                DebugUtil.e(TAG, "====>genListSubSentence is finished")
                return
            }
            listSubSentence = arrayListOf()
            /*
             * textWithWords == null || textWithWordsTimeStamp == null 此时没有字的时间戳 不支持分句标红和跳转播放，
             * 整段即一句
             *  item.textWithWords?.size != item.textWithWordsTimeStamp?.size 此时分字和时间戳不能一一对应， 兼容逻辑处理
             *  不支持分句 整段即一句
             */

            if (textWithWords == null
                || textWithWordsTimeStamp == null
                || item.textWithWords?.size != item.textWithWordsTimeStamp?.size
                || item.textContent != combineList(textWithWords!!)
            ) {

                /*
                if (textWithWords == null || textWithWordsTimeStamp == null) {
                    DebugUtil.i(
                        TAG,
                        "====>No textWithWordsTimeStamp, one sentence  is one Paragraph!"
                    )
                } else {
                    DebugUtil.i(
                        TAG,
                        "====>textWithWords.size = ${textWithWords?.size}," +
                                " textWithWordsTimeStamp.size = ${textWithWordsTimeStamp?.size}," +
                                " one sentence  is one Paragraph!"
                    )
                }
                 */

                val sentence = ConvertContentItem.SubSentence(
                    0, textContent.length - 1, item.startTime.toFloat(), textContent
                )
                listSubSentence?.add(sentence)
                return
            }
            val sb = StringBuilder()
            var startChar = 0
            for (i in textWithWords!!.indices) {
                sb.append(textWithWords!![i])
                if (checkIsSpiltFlag(textWithWords!![i]) || i == textWithWords!!.size - 1) {
                    val sentence =
                        ConvertContentItem.SubSentence(
                            startChar,
                            i,
                            //切自研以及字节后，服务端返回时间 timestamp与原有的有差异，单独处理
                            if (isSupportXunFeiOrByte) item.startTime.toFloat() else genTime(item, startChar),
                            sb.toString()
                        )
                    listSubSentence?.add(sentence)
                    sb.clear()
                    startChar = i + 1
                }
            }
        }
    }

    @JvmStatic
    @VisibleForTesting
    fun combineList(stringList: List<String>): String {
        val sb = StringBuffer()
        for (s in stringList) {
            sb.append(s)
        }
        return sb.toString()
    }

    @JvmStatic
    @VisibleForTesting
    fun genTime(item: ConvertContentItem, startChar: Int): Float {
        if (startChar < 0) {
            return item.startTime.toFloat()
        }
        if (startChar > item.textWithWordsTimeStamp!!.size - 1) {
            return item.endTime.toFloat()
        }
        return item.startTime + item.textWithWordsTimeStamp!![startChar].toFloat() * TIME_UNIT
    }

    @JvmStatic
    @VisibleForTesting
    fun checkIsSpiltFlag(inString: String?): Boolean {
        if (inString in setOf("。", "?", "!", "？", "！")) {
            return true
        }
        return false
    }

    @JvmStatic
    fun genStartPlayTime(item: ConvertContentItem?, start: Int, markPlaceLength: Int): Long {
        if (item?.listSubSentence?.size ?: 0 <= 0) {
            DebugUtil.e(TAG, "===>genStartPlayTime, item is null!")
            return -1
        }
        if (start < 0) {
            DebugUtil.e(TAG, "===>genStartPlayTime, start < 0!")
            return -1
        }
        var index = start
        for (i in item!!.listSubSentence!!.indices) {
            val sentence = item.listSubSentence!![i]
            val hasMark = sentence.onlyHasSimpleMark
            if (hasMark) {
                index -= markPlaceLength
            }
            if (index >= sentence.startCharSeq && index <= sentence.endCharSeq) {
                return sentence.time.toLong()
            }
        }
        DebugUtil.e(TAG, "===>genStartPlayTime, find not result")
        return -1
    }
}