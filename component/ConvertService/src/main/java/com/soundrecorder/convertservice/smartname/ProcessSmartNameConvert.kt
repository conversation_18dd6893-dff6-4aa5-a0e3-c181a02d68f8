/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ProcessAiTitleConvert
 * * Description: ProcessAiTitleConvert
 * * Version: 1.0
 * * Date : 2025/4/16
 * * Author: ********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  ********    2025/4/16   1.0    build this module
 ****************************************************************/
package com.soundrecorder.convertservice.smartname

import android.app.RecoverableSecurityException
import android.content.Intent
import android.net.Uri
import android.os.Handler
import android.os.HandlerThread
import android.os.Message
import android.text.TextUtils
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.oplus.recorderlog.util.GsonUtil
import com.oplus.unified.summary.sdk.UnifiedSummaryKit
import com.oplus.unified.summary.sdk.callback.ISummaryInitCallback
import com.oplus.unified.summary.sdk.common.SummaryInitParam
import com.oplus.unified.summary.sdk.common.SummaryResultType
import com.oplus.unified.summary.sdk.speech.DialogContent
import com.oplus.unified.summary.sdk.speech.SpeechSummaryRequest
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.suffix
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.R
import com.soundrecorder.common.buryingpoint.AIErrorBuryingPoint
import com.soundrecorder.common.buryingpoint.SmartNameStatisticsUtil
import com.soundrecorder.common.buryingpoint.SmartNameStatisticsUtil.VALUE_SMART_NAME_ERROR_CODE_DEFAULT
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.BeanConvertText
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.ConvertRecord
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.SmartNameParam
import com.soundrecorder.common.databean.SmartSummaryResult
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.utils.AudioNameUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.RecordModeUtil
import com.soundrecorder.common.utils.calling.ContactCallRecordUtil
import com.soundrecorder.convertservice.ConvertSupportManager
import com.soundrecorder.convertservice.convert.ConvertTaskThreadManager
import com.soundrecorder.convertservice.convert.IConvertCallback
import com.soundrecorder.convertservice.convert.NewConvertResultUtil
import com.soundrecorder.convertservice.util.ConvertToUtils
import com.soundrecorder.modulerouter.BrowseFileInterface
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_RECORD_ID
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_CONVERT_COMPLETE
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_NAME_TEXT
import com.soundrecorder.modulerouter.playback.KEY_NOTIFY_SMART_NAME_STATUS
import com.soundrecorder.modulerouter.playback.NOTIFY_SMART_NAME_STATUS_UPDATE
import com.soundrecorder.modulerouter.playback.PlayBackInterface
import com.soundrecorder.modulerouter.smartname.ISmartNameCallback
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.utils.Injector
import java.io.File
import java.util.UUID

class ProcessSmartNameConvert(val mediaId: Long, var callback: ISmartNameCallback?, var params: SmartNameParam?) {

    companion object {
        private const val TAG = "ProcessSmartNameConvert"
        private const val MAX_RETRY_COUNT = 3
        private const val MSG_START_CONVERT_OR_SMART = 1
        private const val MSG_START_SMART_NAME = 2
        private const val MSG_RELEASE = 3
        private const val DELAY_DURATION = 500L
        private const val BIZ_TAG_AI_RECORDING = "ai_recording"
    }

    @Volatile
    var mIsAbort = false
    var mProgressCallback: ISmartNameProgressCallback? = null
    var mErrorRetryCount: Int = 0

    private var mHandlerThread: HandlerThread? = null
    private var mWorkHandler: Handler? = null
    private var isThreadQuit = false
    //private var mRetryCount = 0
    private var mSummaryKit: UnifiedSummaryKit? = null

    private val cloudKitAction by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    fun initHandlerThread() {
        DebugUtil.d(TAG, "initHandlerThread")
        isThreadQuit = false
        mHandlerThread = HandlerThread("SmartName-$mediaId")
        mHandlerThread?.start()
        mHandlerThread?.looper?.let {
            mWorkHandler = object : Handler(it) {
                override fun handleMessage(msg: Message) {
                    when (msg.what) {
                        MSG_START_CONVERT_OR_SMART -> processStartConvertOrSmartName()
                        MSG_START_SMART_NAME -> processSmartName()
                        MSG_RELEASE -> release()
                    }
                }
            }
        }
    }

    private fun getBizTag(): String {
        return "${BaseApplication.getAppContext().packageName}-smartName-$mediaId"
    }

    private fun release() {
        DebugUtil.d(TAG, "quitHandlerThread")
        releaseSummaryKit()
        mWorkHandler?.removeCallbacksAndMessages(null)
        mHandlerThread?.quitSafely()
        mHandlerThread = null
        mWorkHandler = null
        isThreadQuit = true
        params = null
        mErrorRetryCount = 0
    }

    private fun releaseSummaryKit() {
        mSummaryKit?.release(getBizTag())
        mSummaryKit = null
    }

    private var mConvertCallback: IConvertCallback? = object : IConvertCallback {
        override fun onConvertStatusChange(
            mediaId: Long,
            convertStatus: ConvertStatus,
            errorCode: Int,
            errorMessage: String,
            convertAiTitle: Boolean
        ) {
            DebugUtil.d(TAG, "onConvertStatusChange, mediaId:$mediaId, convertStatus：$convertStatus," +
                    " errorCode:$errorCode, errorMessage:$errorMessage")
            mWorkHandler?.post {
                handleConvertStatus(convertStatus, mediaId, errorMessage)
            }
        }

        override fun onConvertTextReceived(
            mediaId: Long,
            convertType: Int,
            convertTextResult: BeanConvertText,
            showSwitch: Boolean,
            convertAiTitle: Boolean
        ) {
            DebugUtil.i(TAG, "<<< onConvertTextReceived, convertAiTitle:$convertAiTitle")
            mWorkHandler?.post {
                processSmartName(SmartNameParam.toConvertSmartNameParam(convertTextResult))
            }
        }

        override fun onConvertProgressChanged(mediaId: Long, uploadProgress: Int, convertProgress: Int, serverPlanCode: Int) {
            DebugUtil.i(
                TAG, """
                        onConvertProgressChanged mediaId = $mediaId
                        uploadProgress = $uploadProgress
                        convertProgress = $convertProgress
                         serverPlanCode = $serverPlanCode
                        """)
        }
    }

    private fun handleConvertStatus(
        convertStatus: ConvertStatus,
        mediaId: Long,
        errorMessage: String
    ) {
        when (convertStatus.convertStatus) {
            ConvertStatus.CONVERT_STATUS_NO_NETWORK,
            ConvertStatus.NETWORKERROR_EXCEPTION -> {
                processSmartNameError(mediaId, SmartNameAction.NETWORK_ERROR, errorMessage, false)
                return
            }

            ConvertStatus.CONVERT_STATUS_QUERY_FAIL,
            ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL,
            ConvertStatus.CONVERT_STATUS_QUERY_TASK_TIMEOUT,
            ConvertStatus.ENCRYPT_EXCEPTION,
            ConvertStatus.JSONPARSE_EXCEPTION,
            ConvertStatus.EXCEPTION -> {
                processSmartNameError(mediaId, SmartNameAction.SERVER_ERROR, errorMessage, false)
                return
            }
        }
    }

    fun doStartSmartName() {
        if (params != null) {
            mWorkHandler?.sendEmptyMessage(MSG_START_SMART_NAME)
        } else {
            mWorkHandler?.sendEmptyMessage(MSG_START_CONVERT_OR_SMART)
        }
    }

    private fun processStartConvertOrSmartName(): Boolean {
        val convertRecord = ConvertDbUtil.selectByRecordId(mediaId)
        val isConvertComplete = ConvertDbUtil.checkAlreadyConvertComplete(convertRecord)
        if (isConvertComplete) {
            return startConvertCompleteSmartName(convertRecord)
        } else {
            /*不是主栈没有入口，所以这里直接传true*/
            val convertSupportType = ConvertSupportManager.getConvertSupportType(true)
            if (convertSupportType == ConvertSupportManager.CONVERT_DISABLE) {
                //不支持转文本时，立即结束当前智能命名任务
                DebugUtil.d(TAG, "startConvertOrSmartName, convertSupportType disable")
                processSmartNameError(mediaId, SmartNameAction.CONVERT_TEXT_UNSUPPORT)
                return false
            }
            val mediaRecord = MediaDBUtils.queryRecordById(mediaId)
            DebugUtil.i(TAG, "startConvertOrSmartName start. record:$mediaRecord")
            if (PermissionUtils.hasReadAudioPermission()) {
                ConvertDbUtil.updateRecordIdByMediaPath(mediaRecord?.data, mediaId)
            }

            mConvertCallback?.let {
                ConvertTaskThreadManager.registerCallback(mediaId, it)
            }
            val result = ConvertTaskThreadManager.startOrResumeConvert(
                mediaId,
                convertSupportType, true)
            DebugUtil.i(TAG, "startConvertOrSmartName result = $result")
            return result
        }
    }

    private fun startConvertCompleteSmartName(convertRecord: ConvertRecord?): Boolean {
        val convertFileName = getConvertFileName(convertRecord)
        val convertContentItems: ArrayList<ConvertContentItem>? =
            if (TextUtils.isEmpty(convertFileName)) {
                null
            } else {
                // 如果是OShare目录下的文件，采用对应文本解析规则
                if (convertRecord?.isOShareFile == true) {
                    ConvertToUtils.readOShareConvertContent(
                        convertRecord.convertTextfilePath,
                        convertRecord.serverPlanCode
                    )
                } else {
                    convertRecord?.serverPlanCode?.let {
                        ConvertToUtils.readConvertContent(
                            BaseApplication.getAppContext(), convertFileName,
                            it
                        )
                    }
                }
            }
        return processSmartName(SmartNameParam.toConvertSmartNameParam(convertContentItems))
    }

    private fun getConvertFileName(convertRecord: ConvertRecord?): String? {
        val convertFilePath: String? = convertRecord?.convertTextfilePath
        var convertFileName: String? = null
        if (!TextUtils.isEmpty(convertFilePath)) {
            val savePathDir = NewConvertResultUtil.getConvertSavePath(BaseApplication.getAppContext()) + File.separator
            convertFileName = convertFilePath?.replace(savePathDir, "")
        }
        DebugUtil.i(TAG, "getConvertFileName, convertFileName:$convertFileName convertFilePath = $convertFilePath")
        return convertFileName
    }

    fun registerNameProgressCallback(progressCallback: ISmartNameProgressCallback?) {
        mProgressCallback = progressCallback
    }

    private fun processSmartName(smartNameParam: SmartNameParam? = null): Boolean {
        if (smartNameParam != null) {
            params = smartNameParam
        }
        mConvertCallback?.let {
            ConvertTaskThreadManager.unregisterCallback(mediaId, it)
        }
        val useParams = params
        if (useParams == null) {
            DebugUtil.d(TAG, "createPhoneSummaryRequest fail param is null")
            processSmartNameError(mediaId, SmartNameAction.CONTENT_LESS_ERROR, "")
            return false
        }

        if (mIsAbort) {
            //do success
            return true
        }
        return createSmartNameRequestRetry()
    }

    private fun convertParamsToContents(params: SmartNameParam): ArrayList<DialogContent> {
        //val language = LanguageUtil.getCurrentLanguageFromSystem()
        val contents: ArrayList<DialogContent> = arrayListOf()
        if (params.dialogs.isNotEmpty()) {
            params.dialogs.forEach { content ->
                contents.add(
                    DialogContent(
                        id = content.id,
                        content = content.content ?: "",
                        timestamp = content.timemap,
                        speakerType = content.speakerType,
                        language = content.language
                    )
                )
            }
        }
        return contents
    }

    private fun createSmartNameRequestRetry(): Boolean {
        val useParams = params ?: return false
        val sessionId = useParams.sessionId.ifBlank { UUID.randomUUID().toString() }
        val retryCount = SmartRetryCacheTask.getCurrentRetryCount(mediaId)
        //其中语音类请求，summaryType填入 SummaryResultType.MINI ,则输出标题
        val request = SpeechSummaryRequest.createPhoneSummaryRequest(
            sessionId = sessionId,
            dialogs = convertParamsToContents(useParams),
            timeout = useParams.timeout,
            otherName = useParams.otherName ?: "",    //对话者名字
            inputLanguage = useParams.inputLanguage,
            outputLanguage = useParams.outputLanguage,
            summaryType = SummaryResultType.MINI,
            retryCount = retryCount,
            isRetry = (retryCount > 0)
        )
        SmartRetryCacheTask.addRetryTaskToCache(mediaId, retryCount)
        DebugUtil.d(TAG, "createPhoneSummaryRequest, retryCount:$retryCount, request sessionId:${request.sessionId}" +
                "params:$params")
        return createSummaryInitKit(request)
    }

    private fun stopSummary() {
        if (SmartNameTaskManager.summarySessionIdList.isEmpty()) {
            return
        }
        if (SmartNameTaskManager.summarySessionIdList.containsKey(mediaId)) {
            val sessionId = SmartNameTaskManager.summarySessionIdList.get(mediaId)
            if (!sessionId.isNullOrEmpty() && sessionId != "") {
                DebugUtil.d(TAG, "stopSmartNameTask, sessionId:$sessionId")
                mSummaryKit?.stopTask(sessionId)
            }
        }
    }

    fun cancel() {
        mWorkHandler?.post {
            mIsAbort = true
            stopSummary()
            mErrorRetryCount = 0
            sendBroadcastNotifySmartNameStatus(mediaId, false)
            mProgressCallback?.postCancelSmartName(mediaId)
            mWorkHandler?.sendEmptyMessage(MSG_RELEASE)
        }
    }

    private fun createSummaryInitKit(request: SpeechSummaryRequest, isRetry: Boolean = false): Boolean {
        var result: Boolean = false
        mProgressCallback?.preStartSmartName(mediaId)
        if (!isRetry) {
            callback?.onSmartNameStart(mediaId, null)
        }
        mSummaryKit = UnifiedSummaryKit(BaseApplication.getAppContext())
        mSummaryKit?.initKit(
            SummaryInitParam(getBizTag(), true, appId = BIZ_TAG_AI_RECORDING), object : ISummaryInitCallback {
                override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                    AIErrorBuryingPoint.addAISummaryErrorMsg(request.summaryType, request.themeCode,
                        AIErrorBuryingPoint.VALUE_ERR_INIT_KIT, request.sessionId, errorCode, errorMsg)

                    DebugUtil.d(TAG, "initUnifiedSummary, onError:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg")
                    processSmartNameError(mediaId, SmartNameAction.SERVER_ERROR)
                }

                override fun onSuccess(bizTag: String?, extras: Map<String, Any>?) {
                    DebugUtil.d(TAG, "initUnifiedSummary, onSuccess:$bizTag, extras:$extras")
                    result = startSummaryForSmartName(request)
                }
            }
        )
        return result
    }

    private fun startSummaryForSmartName(request: SpeechSummaryRequest): Boolean {
        var result  = false
        DebugUtil.d(TAG, "startSummary, summaryKit:$mSummaryKit")
        mSummaryKit?.getSummary(request, object : ISmartSummaryCallback {
            override fun onStart(sessionId: String?, extras: Map<String, Any>?) {
                DebugUtil.d(TAG, "onStart, sessionId:$sessionId, extras:$extras")
                mWorkHandler?.post {
                    if (!sessionId.isNullOrBlank()) {
                        SmartNameTaskManager.addSessionId(mediaId, sessionId)
                    }
                }
            }

            override fun onError(sessionId: String, errorCode: Int, errorMsg: String?) {
                DebugUtil.d(
                    TAG,
                    "onError, sessionId:$sessionId, errorCode:$errorCode, errorMsg:$errorMsg"
                )
                AIErrorBuryingPoint.addAISummaryErrorMsg(request.summaryType, request.themeCode,
                    AIErrorBuryingPoint.VALUE_ERR_GET_SUMMARY, request.sessionId, errorCode, errorMsg)

                mWorkHandler?.post {
                    if (errorCode == SmartNameAction.PLUGIN_INIT_ERROR && mErrorRetryCount <= MAX_RETRY_COUNT) {
                        mErrorRetryCount += 1
                        DebugUtil.d(TAG, "startRetrySmartName, errorRetryCount:$mErrorRetryCount")
                        result = startRetrySmartName(request)
                    } else {
                        SmartNameTaskManager.removeSessionId(mediaId)
                        processSmartNameError(mediaId, errorCode, errorMsg)
                        result = false
                    }
                }
            }

            /**
             * 流式数据结束回调
             * @param sessionId 当前请求ID
             * @param jsonResult 流式数据对象的json字符串
             * @param extras 扩展保留参数
             */
            override fun onFinished(
                sessionId: String,
                jsonResult: String,
                extras: Map<String, Any>?
            ) {
                DebugUtil.d(TAG, "onFinished, jsonResult:$jsonResult, extras:$extras")
                mWorkHandler?.post {
                    SmartNameTaskManager.removeSessionId(mediaId)
                    processSmartNameFinished(mediaId, jsonResult, extras)
                    result = true
                }
            }

            override fun onStop(sessionId: String, extras: Map<String, Any>?) {
                DebugUtil.d(TAG, "onStop, sessionId:$sessionId, extras:$extras")
                if (mIsAbort) {
                    mWorkHandler?.post {
                        callback?.onSmartNameStop(mediaId, extras)
                        SmartNameTaskManager.removeSessionId(mediaId)
                        postSmartNameEnd(mediaId)
                    }
                }
            }
        })
        callback?.onSmartNameEnd(mediaId)
        return result
    }

    private fun checkTrigSyncWhenFinish(needTrigSync: Boolean = false) {
        if (!SmartNameTaskManager.checkTaskRunning(mediaId) && needTrigSync) {
            cloudKitAction?.trigBackupNow(BaseApplication.getAppContext())
            DebugUtil.d(TAG, "checkTrigSyncWhenFinish, trig sync")
        }
    }

    /**
     * 重试
     */
    private fun startRetrySmartName(request: SpeechSummaryRequest): Boolean {
        //SmartNameTaskManager.releaseSummaryKit()
        releaseSummaryKit()
        return createSummaryInitKit(request, true)
    }

    fun toastMessage(resId: Int) {
        ToastManager.showShortToast(BaseApplication.getAppContext(), resId)
    }

    private fun processSmartNameError(
        mediaId: Long,
        errorCode: Int,
        errorMsg: String? = null,
        convertComplete: Boolean = true
    ) {
        DebugUtil.d(TAG, "processSmartNameError, mediaId:$mediaId, errorCode:$errorCode, errorMsg:$errorMsg")
        when (errorCode) {
            SmartNameAction.AIUNIT_ERROR,
            SmartNameAction.PLUGIN_INIT_ERROR,
            SmartNameAction.REQUEST_TIMEOUT,
            SmartNameAction.SERVER_ERROR -> toastMessage(R.string.smart_title_failure_server_error)
            SmartNameAction.CONTENT_LESS_ERROR -> toastMessage(R.string.less_recorded_content_intelligent_name_failure)
            SmartNameAction.NETWORK_ERROR -> toastMessage(R.string.no_network_intelligent_name_failure)
            SmartNameAction.FILE_SIZE_UNSUPPORT_ZERO -> toastMessage(R.string.smart_name_error_damage_file)
            SmartNameAction.FILE_SIZE_MAX_UNSUPPORT -> toastMessage(R.string.smart_name_error_size_long)
            SmartNameAction.DURATION_MIN_UNSUPPORT_ZERO -> toastMessage(R.string.smart_name_error_damage_file)
            SmartNameAction.DURATION_MAX_UNSUPPORT -> toastMessage(R.string.smart_name_error_duration_long)
            SmartNameAction.FILE_FORMAT_UNSUPPORT -> toastMessage(R.string.smart_name_error_format)
            SmartNameAction.FILE_NOT_EXIST -> toastMessage(R.string.record_file_not_exist)
            SmartNameAction.INVALID_PARAM,
            SmartNameAction.UNSUPPORTED_LANGUAGE,
            SmartNameAction.LANGUAGE_NOT_SUPPORT -> toastMessage(R.string.smart_name_error_language_not_sopport)
            SmartNameAction.AI_TITLE_SUPPORT_ERROR,
            SmartNameAction.RISK_CONTENT -> toastMessage(R.string.smart_name_error_no_valid_content)
            else -> toastMessage(R.string.smart_title_failure_server_error)
        }
        mConvertCallback?.let {
            ConvertTaskThreadManager.unregisterCallback(mediaId, it)
        }
        val statisticsCode: Int = if (errorCode == SmartNameAction.NETWORK_ERROR) {
            SmartNameStatisticsUtil.VALUE_SMART_NAME_ERROR_CODE_NETWORK_ERROR
        } else {
            errorCode
        }
        SmartNameStatisticsUtil.addSmartNameResultEvent(SmartNameStatisticsUtil.VALUE_SMART_NAME_RESULT_FAILURE, statisticsCode)
        callback?.onSmartNameError(mediaId, errorCode, errorMsg)
        sendBroadcastNotifySmartNameStatus(mediaId, false, convertComplete = convertComplete)
        postSmartNameEnd(mediaId)
    }

    private fun processSmartNameFinished(
        mediaId: Long,
        jsonResult: String,
        extras: Map<String, Any>?
    ) {
        val summaryResult = GsonUtil.fromJson(jsonResult, SmartSummaryResult::class.java)
        val resultName = summaryResult?.lastSummary
        if (summaryResult == null || resultName.isNullOrBlank()) {
            DebugUtil.d(TAG, "process smartName less content")
            processSmartNameError(mediaId, SmartNameAction.CONTENT_LESS_ERROR)
            return
        }
        val mediaRecord = MediaDBUtils.queryRecordById(mediaId)
        if (mediaRecord == null) {
            DebugUtil.d(TAG, "processSmartNameFinished, mediaRecord is null")
            processSmartNameError(mediaId, SmartNameAction.FILE_NOT_EXIST)
            return
        }
        DebugUtil.d(TAG, "processSmartNameFinished, mediaRecord:$mediaRecord")
        parseCallName(mediaId, mediaRecord) { callName ->
            renameSmartNameRecord(callName, resultName, mediaId, mediaRecord, summaryResult)
        }
        callback?.onSmartNameFinished(mediaId, jsonResult, extras)
    }

    private fun postSmartNameEnd(mediaId: Long, needTrigSync: Boolean = false) {
        DebugUtil.d(TAG, "postSmartNameEnd, mediaId:$mediaId")
        mProgressCallback?.postSmartNameEnd(mediaId)
        if (!isThreadQuit) {
            mWorkHandler?.sendEmptyMessage(MSG_RELEASE)
        }
        checkTrigSyncWhenFinish(needTrigSync)
    }

    /**
     * 重命名智能标题
     */
    private fun renameSmartNameRecord(
        callName: String?,
        resultName: String?,
        mediaId: Long,
        mediaRecord: Record,
        summaryResult: SmartSummaryResult
    ) {
        var smartNewDisplayName = if (!callName.isNullOrEmpty()) {
            "$resultName-$callName"
        } else {
            resultName
        }
        val mediaUri = MediaDBUtils.genUri(mediaId)
        DebugUtil.d(TAG, "renameSmartNameRecord, smartNewDisplayName:$smartNewDisplayName, mediaUri:$mediaUri")
        val oldFileExist = FileUtils.isFileExist(mediaUri)
        if (!oldFileExist) {
            DebugUtil.d(TAG, "renameSmartNameRecord, oldFile not exist")
            processSmartNameError(mediaId, SmartNameAction.FILE_NOT_EXIST)
            return
        }

        val displayName = mediaRecord.displayName
        val suffix = displayName.suffix()
        val newFileNameWithSuffix = smartNewDisplayName + suffix
        /* 如果文件名没有变化，直接返回 */
        if (displayName == newFileNameWithSuffix) {
            DebugUtil.d(TAG, "renameSmartNameRecord, file name not changed")
            sendSmartNameResult(mediaId, smartNewDisplayName, summaryResult.code)
            postSmartNameEnd(mediaId)
            return
        }

        val relativePath = mediaRecord.relativePath
        val newFileAlreadyExist = FileUtils.isFileExist(relativePath, newFileNameWithSuffix)
        /* 处理文件名冲突，如果文件已存在，使用录音裁剪保存的文件名处理逻辑 */
        if (newFileAlreadyExist) {
            // 生成新的文件标题，会自动处理文件名冲突
            smartNewDisplayName = AudioNameUtils.genDefaultFileTitle(relativePath, newFileNameWithSuffix)
            DebugUtil.d(TAG, "renameSmartNameRecord,newFile already exist, generating new name with suffix " +
                    "generated new name: $smartNewDisplayName")
        }
        if (PermissionUtils.hasAllFilePermission()) {
            sendSmartNameResult(mediaId, smartNewDisplayName, summaryResult.code)
        }
        updateMediaDB(
            mediaUri,
            smartNewDisplayName,
            suffix,
            mediaRecord,
            displayName
        )
    }

    /**
     * 更新数据库操作
     */
    private fun updateMediaDB(
        mediaUri: Uri,
        smartNewDisplayName: String?,
        suffix: String?,
        mediaRecord: Record,
        displayName: String
    ) {
        var result: Int = -1
        try {
            result = MediaDBUtils.rename(mediaUri, smartNewDisplayName, suffix, mediaRecord.mimeType)
        } catch (e: RecoverableSecurityException) {
            DebugUtil.e(TAG, "catch delete RecoverableSecurityException", e)
            sendSmartNameResult(mediaId, mediaRecord.displayName.title(), VALUE_SMART_NAME_ERROR_CODE_DEFAULT)
            PermissionUtils.sendFilePermissionBroadcast(BaseApplication.getAppContext())
        }
        //updateCallback?.invoke(result != -1)
        onChangeFileUpdate(result, smartNewDisplayName, suffix, displayName, mediaRecord)
        postSmartNameEnd(mediaId, true)
    }

    private fun onChangeFileUpdate(
        result: Int,
        smartNewDisplayName: String?,
        suffix: String?,
        displayName: String?,
        mediaRecord: Record
    ) {
        if (result <= 0) {
            return
        }
        val newName = smartNewDisplayName + suffix
        val originalData = mediaRecord.data
        var newPath = originalData
        val lastIndexOfFileDescriptor = originalData.lastIndexOf(File.separator)
        if (lastIndexOfFileDescriptor > 0) {
            newPath = originalData.substring(
                0,
                lastIndexOfFileDescriptor
            ) + File.separator + newName
        }
        val context = BaseApplication.getAppContext()
        ContactCallRecordUtil.updatePathIfCallingRecord(context, mediaRecord, newPath)
        val rowId = mediaRecord.id
        val mediaUri = MediaDBUtils.genUri(rowId)
        val mediaRecordReal = MediaDBUtils.getRecordFromMediaByUriId(mediaUri)
        // 获取修改后的record 的originalName。
        val updateDbSuc = RecorderDBUtil.getInstance(context)
            .updateDisplayName(
                displayName,
                newName,
                mediaRecord.relativePath,
                true,
                if (RecorderDBUtil.getRecordByData(mediaRecord.data) != null) {
                    RecorderDBUtil.getRecordByData(mediaRecord.data).originalName
                } else {
                    displayName
                }
            )
        if (!updateDbSuc) {
            DebugUtil.i(TAG, " renameSuc ,but db not exist,insert db ")
            // 更新的该条音频数据不在record中，需要此时插入，以保留origin name。
            RecorderDBUtil.insertCallRecordingsIfNeed(
                originalData,
                mediaRecordReal,
                displayName,
                newName,
                newPath
            )
            return
        }
        val convertRecord = ConvertDbUtil.selectByRecordId(mediaId)
        val convertIsEmpty = convertRecord == null
        if (!convertIsEmpty) {
            convertRecord?.mediaPath = newPath
            ConvertDbUtil.update(convertRecord)
        }
        DebugUtil.d(TAG, "onFileUpdateSuccess, smart newPath:$newPath")
        Injector.injectFactory<BrowseFileInterface>()?.onFileUpdateSuccess(mediaId, newPath)
    }

    private fun sendSmartNameResult(
        mediaId: Long,
        smartNewDisplayName: String?,
        resultCode: Int
    ) {
        DebugUtil.d(TAG, "sendSmartNameResult")
        sendBroadcastNotifySmartNameStatus(mediaId, false, name = smartNewDisplayName)
        SmartNameStatisticsUtil.addSmartNameResultEvent(
            SmartNameStatisticsUtil.VALUE_SMART_NAME_RESULT_SUCCESS,
            resultCode
        )
        //mSmartCallback?.let { mSmartNameService?.unregisterSmartNameCallback(mediaId, it) }
    }

    private fun sendBroadcastNotifySmartNameStatus(mediaId: Long, status: Boolean = true, name: String? = null, convertComplete: Boolean = true) {
        //send broadcast notify browseFile refresh list convert status
        DebugUtil.d(TAG, "sendBroadcastNotifySmartNameStatus status:$status, name: $name")
        val intent = Intent(NOTIFY_SMART_NAME_STATUS_UPDATE)
        intent.putExtra(KEY_NOTIFY_RECORD_ID, mediaId)
        intent.putExtra(KEY_NOTIFY_SMART_NAME_NAME_TEXT, name)
        intent.putExtra(KEY_NOTIFY_SMART_NAME_STATUS, status)
        intent.putExtra(KEY_NOTIFY_SMART_NAME_CONVERT_COMPLETE, convertComplete)

        LocalBroadcastManager.getInstance(BaseApplication.getAppContext()).sendBroadcast(intent)
    }

    /**
     * 解析通话录音联系人
     */
    private fun parseCallName(mediaId: Long, mediaRecord: Record, callback: ((String?) -> Unit)?) {
        val recordType = RecordModeUtil.getRecordTypeForMediaRecord(mediaRecord)
        DebugUtil.d(TAG, "parseCallName, recordType:$recordType, mimeType:${mediaRecord.mimeType}")
        if (recordType == RecordModeConstant.RECORD_TYPE_CALL) {
            val recordDb = RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                .getRecordById(mediaId.toString())
            var callName = recordDb?.callName
            val isParseCallName = recordDb?.isIsParseCallName ?: false
            if (callName.isNullOrEmpty() || !isParseCallName) {
                callName = Injector.injectFactory<PlayBackInterface>()?.parseCallName(
                    BaseApplication.getAppContext(),
                    mediaRecord.data,
                    mediaId,
                    mediaRecord.mimeType
                )
                RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                    .updateCallNameByRecordId(mediaId.toString(), callName, false)
            }
            DebugUtil.d(TAG, "parseCallName, parse call name:$callName")
            callback?.invoke(callName)
        } else {
            callback?.invoke(null)
        }
    }
}