/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ConvertCheckUtilsApi
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.api

import android.content.Context
import com.soundrecorder.convertservice.convert.ConvertCheckUtils
import com.soundrecorder.modulerouter.convertService.ConvertCheckUtilsAction

object ConvertCheckUtilsApi : ConvertCheckUtilsAction {

    override fun isFileSizeMinMet(fileSize: Long): Boolean {
        return ConvertCheckUtils.isFileSizeMinMet(fileSize)
    }

    override fun isFileSizeMaxMet(fileSize: Long): Boolean {
        return ConvertCheckUtils.isFileSizeMaxMet(fileSize)
    }

    override fun isFileDurationMinMet(duration: Long): Boolean {
        return ConvertCheckUtils.isFileDurationMinMet(duration)
    }

    override fun isFileDurationMaxMet(mFileDuration: Long): Boolean {
        return ConvertCheckUtils.isFileDurationMaxMet(mFileDuration)
    }

    override fun isFileFormatMet(fileFormat: String): Boolean {
        return ConvertCheckUtils.isFileFormatMet(fileFormat)
    }

    override fun getErrorMsgByStatus(
        context: Context,
        uploadStatus: Int,
        convertStatus: Int,
        errorMessage: String
    ): String {
        return ConvertCheckUtils.getErrorMsgByStatus(
            context,
            uploadStatus,
            convertStatus,
            errorMessage
        )
    }
}