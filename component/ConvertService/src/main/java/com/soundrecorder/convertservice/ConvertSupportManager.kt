/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: ConvertSupportManager
 * Description:
 * Version: 1.0
 * Date: 2025/3/12
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/12 1.0 create
 */

package com.soundrecorder.convertservice

import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.utils.FunctionOption
import com.soundrecorder.translate.AIAsrManager

object ConvertSupportManager {
    /*不支持转文本*/
    const val CONVERT_DISABLE = 0x0
    /*支持转文本：ai sdk能力*/
    const val CONVERT_AI_CONVERT = 0X1
    /*支持转文本：默认内销原有能力,*/
    const val CONVERT_DOMESTIC_DEFAULT = 0X2
    private const val TAG = "ConvertSupportManager"

    /**
     * 获取转文本能力支持
     */
    @JvmStatic
    fun getConvertSupportType(isMainProcess: Boolean): Int {
        // 1.非主进程，不支持；如：从拨号盘-通话录音进入
        if (!isMainProcess) {
            DebugUtil.w(TAG, "getConvertSupportType is not main process")
            return CONVERT_DISABLE
        }
        // 2.支持 AI 转文本，则优先使用AI能力
        if (AIAsrManager.isSupportAIAsr(BaseApplication.getAppContext())) {
            DebugUtil.d(TAG, "getConvertSupportType ai asr")
            return CONVERT_AI_CONVERT
        }
        // 兼容老版本内销转文本能力
        if (FunctionOption.loadSpeechToTextFeature()) {
            DebugUtil.d(TAG, "getConvertSupportType default")
            return CONVERT_DOMESTIC_DEFAULT
        }
        DebugUtil.d(TAG, "getConvertSupportType disable")
        return CONVERT_DISABLE
    }

    /**
     * 是否支持asr
     * 修复：从电话本跳转仍在主进程中，不应禁用转文本功能
     */
    @JvmStatic
    fun isSupportConvert(fromMainProcess: Boolean = true): Boolean {
        if (!fromMainProcess) {
            DebugUtil.w(TAG, "supportConvert is not main process")
            return false
        }
        if (!BaseUtil.isEXP() && !BaseUtil.isLightOS()) {
            // 内销全量一定支持转文本(可以走老逻辑)
            return true
        }
        //其余根据是否支持asr
        return AIAsrManager.isSupportAIAsr(BaseApplication.getAppContext())
    }
}