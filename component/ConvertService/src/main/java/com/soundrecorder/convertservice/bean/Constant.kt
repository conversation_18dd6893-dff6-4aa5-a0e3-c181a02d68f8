/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  Constant
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.bean

class Constant {
    companion object {
        private const val TAG: String = "Constant"
        const val SIZE_1M = 1024 * 1024
        const val SIZE_10M = 10 * 1024 * 1024
        const val SIZE_500M = 500 * 1024 * 1024

        const val TIME_5H = 5 * 60 * 60 * 1000

        const val INT_0 = 0
        const val INT_60 = 60
        const val INT_1000 = 1000
        const val LONG_1000 = 1000L
        const val BUFFER_SIZE_4096 = 4096
        const val BUFFER_SIZE_1024 = 4096

        const val APPID = "atot-sync-server"
    }
}