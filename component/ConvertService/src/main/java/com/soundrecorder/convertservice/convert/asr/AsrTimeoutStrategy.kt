/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: - AsrTimeoutStrategy.kt
 * Description:
 *     The strategy for calculating the timeout for ASR
 *
 * Version: 1.0
 * Date: 2025-05-21
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * B<PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2025-05-21   1.0    Create this module
 *********************************************************************************/
package com.soundrecorder.convertservice.convert.asr

import kotlin.math.max

internal object AsrTimeoutStrategy {

    private const val TO_MILLISECONDS = 1000L
    private const val CARRY_OFFSET = 0.9f
    private const val BASE_TIMEOUT = 40
    private const val TIMEOUT_STEP_RATE = 35f
    private const val TIMEOUT_STEP_DURATION_OFFSET = 300f
    private const val TIMEOUT_STEP_SIZE_GEAR1 = 5
    private const val TIMEOUT_STEP_SIZE_GEAR2 = 10
    private const val TIMEOUT_STEP_GEAR2_DURATION = 30

    /**
     * 计算ASR转换超时时间。
     *
     * 根据录音时长计算ASR（自动语音识别）转换的超时时间。录音时长极限为5小时，超时时间基于录音时长动态计算。
     * 如果录音时长超过30秒，超时时间会根据录音时长进行调整，确保超时时间以10秒为单位向上取整。
     *
     * @param recordDuration 录音时长，单位为毫秒。
     * @return 超时时间，单位为毫秒。
     */
    @JvmStatic
    fun calculateAsrConvertTimeout(recordDuration: Long): Long {
        // 录音时长极限为5小时
        val durationSecond = recordDuration / TO_MILLISECONDS
        val timeoutStep =
            ((durationSecond - TIMEOUT_STEP_DURATION_OFFSET) / TIMEOUT_STEP_RATE + CARRY_OFFSET)
        var timeoutSecond = BASE_TIMEOUT + TIMEOUT_STEP_SIZE_GEAR1 * max(0, timeoutStep.toInt())
        if (durationSecond > TIMEOUT_STEP_GEAR2_DURATION) {
            val remainder = timeoutSecond % TIMEOUT_STEP_SIZE_GEAR2
            if (remainder > 0) {
                timeoutSecond = timeoutSecond - remainder + TIMEOUT_STEP_SIZE_GEAR2
            }
        }
        return timeoutSecond * TO_MILLISECONDS
    }
}