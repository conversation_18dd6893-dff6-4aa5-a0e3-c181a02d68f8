package com.soundrecorder.convertservice.process;

import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import android.accounts.NetworkErrorException;
import android.content.Context;
import android.os.Build;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import com.soundrecorder.common.databean.BeanConvertText;
import com.soundrecorder.convertservice.security.EncryptException;
import com.soundrecorder.convertservice.shadows.ShadowFeatureOption;
import com.soundrecorder.convertservice.shadows.ShadowOS12FeatureUtil;
import com.google.gson.JsonParseException;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.databean.ConvertRecord;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;
import org.robolectric.shadows.ShadowToast;
import java.util.ArrayList;
import java.util.List;
import com.soundrecorder.common.databean.ConvertStatus;
import com.soundrecorder.convertservice.bean.BaseResponse;
import com.soundrecorder.convertservice.bean.BeanConvert;
import com.soundrecorder.convertservice.okhttphelper.OkhttpHelperImpl;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ProcessConvertTest {

    private static final String TEST_ONLY_ID = "test_only_id";
    private static final String TEST_TASK_ID = "test_task_id";
    private static final String TEST_UPLOAD_ALL_URL = "test_upload_all_url";


    private Context mContext;
    private ProcessConvert mProcessConvert;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
        System.setProperty("javax.net.ssl.trustStore", "NONE");
        mProcessConvert = new ProcessConvert(null, null, false);
    }

    @After
    public void tearDown() {
        mContext = null;
        mProcessConvert = null;
    }

    @Test
    public void should_correct_when_process() {
        ConvertRecord req = new ConvertRecord();
        boolean actualRes = true;
        req.setUploadStatus(ConvertStatus.UPLOAD_STATUS_UPLOAD_FAIL);
        actualRes = mProcessConvert.process(req);
        Assert.assertFalse(actualRes);
        Assert.assertEquals(ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL, req.getConvertStatus());
        req.setUploadStatus(ConvertStatus.UPLOAD_STATUS_UPLOAD_SUC);
        mProcessConvert.setMIsAbort(true);
        actualRes = mProcessConvert.process(req);
        Assert.assertTrue(actualRes);
        Assert.assertEquals(ConvertStatus.CONVERT_STATUS_ABORTTASK_SUC, req.getConvertStatus());
        mProcessConvert.setMIsAbort(false);
        actualRes = mProcessConvert.process(req);
        Assert.assertFalse(actualRes);
        Assert.assertEquals(ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL, req.getConvertStatus());
    }

    @Test
    public void should_correct_test_when_handleText() throws Exception {
        List<BeanConvertText.SubItem> subList = new ArrayList<>();
        subList.add(new BeanConvertText.SubItem(0,0,"RECG//TEXT","",""));
        subList.add(new BeanConvertText.SubItem(0,1,"RECG/TEXT","",""));
        subList.add(new BeanConvertText.SubItem(0,2,"RECGTEXT","",""));
        BeanConvertText beanConvertText = new BeanConvertText(TEST_TASK_ID, "TIMECOST", "R/ECGTEX/T", "PROCESS", subList,null, null);
        Whitebox.invokeMethod(mProcessConvert, "handleText", beanConvertText);
        Assert.assertEquals("R ECGTEX T", beanConvertText.getRecogText());
        Assert.assertEquals("RECG  TEXT", beanConvertText.getSublist().get(0).getRecgText());
        Assert.assertEquals("RECG TEXT", beanConvertText.getSublist().get(1).getRecgText());
        Assert.assertEquals("RECGTEXT", beanConvertText.getSublist().get(2).getRecgText());
    }

    @Test
    public void should_return_role_number_when_getSpeakerRoleNumber() throws Exception {
        List<BeanConvertText.SubItem> subList = new ArrayList<>();
        subList.add(new BeanConvertText.SubItem(0,0,"","",""));
        subList.add(new BeanConvertText.SubItem(0,0,"","",""));
        subList.add(new BeanConvertText.SubItem(0,1,"","",""));
        BeanConvertText beanConvertText = new BeanConvertText(TEST_TASK_ID, "TIMECOST", "RECOGTEST", "PROCESS", null,null, null);
        int actual = Whitebox.invokeMethod(mProcessConvert, "getSpeakerRoleNumber", beanConvertText);
        Assert.assertEquals(0, actual);
        beanConvertText = new BeanConvertText(TEST_TASK_ID, "TIMECOST", "RECOGTEST", "PROCESS", subList,null, null);
        actual = Whitebox.invokeMethod(mProcessConvert, "getSpeakerRoleNumber", beanConvertText);
        Assert.assertEquals(2, actual);
    }

    @Test
    public void should_correct_status_when_handleException() throws Exception {
        ConvertRecord convertRecord = new ConvertRecord();
        int code = Code.EXCEPTION;
        Exception e = new EncryptException();
        Whitebox.invokeMethod(mProcessConvert, "handleException", convertRecord, e, code);
        Assert.assertEquals(ConvertStatus.ENCRYPT_EXCEPTION, convertRecord.getConvertStatus());
        e = new JsonParseException("");
        Whitebox.invokeMethod(mProcessConvert, "handleException", convertRecord, e, code);
        Assert.assertEquals(ConvertStatus.JSONPARSE_EXCEPTION, convertRecord.getConvertStatus());
        e = new NetworkErrorException();
        Whitebox.invokeMethod(mProcessConvert, "handleException", convertRecord, e, code);
        Assert.assertEquals(ConvertStatus.NETWORKERROR_EXCEPTION, convertRecord.getConvertStatus());
        e = new NullPointerException();
        Whitebox.invokeMethod(mProcessConvert, "handleException", convertRecord, e, code);
        Assert.assertEquals(ConvertStatus.EXCEPTION, convertRecord.getConvertStatus());
    }

    @Test
    @Ignore
    public void should_show_toast_when_toastCompletedConvert() throws Exception {
        ConvertRecord convertRecord = new ConvertRecord();
        Whitebox.invokeMethod(mProcessConvert, "toastCompletedConvert", convertRecord);
        Assert.assertNotNull(ShadowToast.getLatestToast());
        //String expectedToastText = mContext.getResources().getString(R.string.convert_end, "\"null\"");
        //Assert.assertEquals(expectedToastText, ShadowToast.getTextOfLatestToast());
    }

    @Test
    public void should_true_when_isNeedAddTask() throws Exception {
        ConvertRecord req = Mockito.mock(ConvertRecord.class);
        Assert.assertTrue(Whitebox.invokeMethod(mProcessConvert, "isNeedAddTask", req));
        when(req.getTaskId()).thenReturn(TEST_TASK_ID);
        Assert.assertFalse(Whitebox.invokeMethod(mProcessConvert, "isNeedAddTask", req));
    }

    @Test
    public void should_correct_when_addTask() throws Exception {
        ConvertRecord req = new ConvertRecord();
        Assert.assertFalse(Whitebox.invokeMethod(mProcessConvert, "addTask", req));
        Assert.assertEquals(ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL, req.getConvertStatus());
        req.setOnlyId(TEST_ONLY_ID);
        req.setUploadAllUrl(TEST_UPLOAD_ALL_URL);
        OkhttpHelperImpl okhttpHelper = Mockito.mock(OkhttpHelperImpl.class);
        Whitebox.setInternalState(mProcessConvert, "mOkhttpHelper", okhttpHelper);
        BaseResponse<BeanConvert> addResult = Mockito.mock(BaseResponse.class);
        when(okhttpHelper.addTask(anyString(), anyString(), anyString(), anyBoolean(), anyLong(), anyBoolean(), anyInt(), anyInt())).thenReturn(addResult);
        when(addResult.getHttpData()).thenReturn(null);
        Assert.assertFalse(Whitebox.invokeMethod(mProcessConvert, "addTask", req));
        Assert.assertEquals(ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL, req.getConvertStatus());
        BeanConvert httpData = Mockito.mock(BeanConvert.class);
        when(addResult.getHttpData()).thenReturn(httpData);
        BeanConvertText data = Mockito.mock(BeanConvertText.class);
        when(httpData.getData()).thenReturn(data);
        when(data.getTaskID()).thenReturn(null);
        Assert.assertFalse(Whitebox.invokeMethod(mProcessConvert, "addTask", req));
        Assert.assertEquals(ConvertStatus.CONVERT_STATUS_ADD_TASK_FAIL, req.getConvertStatus());
        when(data.getTaskID()).thenReturn(TEST_TASK_ID);
        Assert.assertTrue(Whitebox.invokeMethod(mProcessConvert, "addTask", req));
        Assert.assertEquals(ConvertStatus.CONVERT_STATUS_ADD_TASK_SUC, req.getConvertStatus());
    }

    @Test
    public void should_correct_when_queryTask() throws Exception {
        ConvertRecord req = new ConvertRecord();
        req.setRecordId(-1L);
        Assert.assertEquals(ProcessConvert.QueryStatus.QUERY_FAIL, Whitebox.invokeMethod(mProcessConvert, "queryTask", req));
        Assert.assertEquals(ConvertStatus.CONVERT_STATUS_QUERY_FAIL, req.getConvertStatus());
        req.setOnlyId(TEST_ONLY_ID);
        req.setTaskId(TEST_TASK_ID);
        mProcessConvert.setMIsAbort(true);
        Assert.assertEquals(ProcessConvert.QueryStatus.QUERY_ABORT, Whitebox.invokeMethod(mProcessConvert, "queryTask", req));
        mProcessConvert.setMIsAbort(false);
        OkhttpHelperImpl okhttpHelper = Mockito.mock(OkhttpHelperImpl.class);
        Whitebox.setInternalState(mProcessConvert, "mOkhttpHelper", okhttpHelper);
        BaseResponse<BeanConvert> queryResult = Mockito.mock(BaseResponse.class);
        when(okhttpHelper.queryTask(anyString(), anyString(), anyLong(), anyInt())).thenReturn(queryResult);
        when(queryResult.getHttpData()).thenReturn(null);
        Assert.assertEquals(ProcessConvert.QueryStatus.QUERY_FAIL, Whitebox.invokeMethod(mProcessConvert, "queryTask", req));
        Assert.assertEquals(ConvertStatus.CONVERT_STATUS_QUERY_FAIL, req.getConvertStatus());
    }
}
