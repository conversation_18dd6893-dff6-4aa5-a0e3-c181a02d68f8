/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  ExtractKeyWordTaskTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/6/23
 * * Author      : W9013204
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.convertservice.keyword

import android.content.Context
import android.os.Build
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.databean.KeyWord
import com.soundrecorder.common.db.KeyWordDbUtils
import com.soundrecorder.convertservice.shadows.*
import org.junit.*
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config
import org.robolectric.shadows.ShadowLog

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowLog::class,
        ShadowOS12FeatureUtil::class,
        ShadowFeatureOption::class]
)
class ExtractKeyWordTaskTest {

    private var mMockBaseApplication: MockedStatic<BaseApplication>? = null
    private var context: Context? = null
    private var task: ExtractKeyWordTask? = null
    private var recordId: Long = 1L

    private val callback = object : IExtractKeywordCallback {

        var data: MutableList<KeyWord> = mutableListOf()
        var code: Int = 0
        var msg: String = ""

        override fun onSuccess(data: List<KeyWord>) {
            this.data.clear()
            this.data.addAll(data)
        }

        override fun onFailed(code: Int, msg: String) {
            this.code = code
            this.msg = msg
        }

        override fun getConvertText(): List<String>? {
            return mutableListOf()
        }
    }

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mMockBaseApplication = Mockito.mockStatic(BaseApplication::class.java)
        mMockBaseApplication?.`when`<Any> { BaseApplication.getAppContext() }?.thenReturn(context)
        task = ExtractKeyWordTask(recordId, callback)
    }

    @After
    fun tearDown() {
        KeyWordDbUtils.deleteKeyWords(recordId)
        mMockBaseApplication?.close()
        mMockBaseApplication = null
        context = null
        task = null
    }

    @Test
    fun should_void_when_saveKeyWord() {
        val list: MutableList<KeyWord> = mutableListOf()
        Whitebox.invokeMethod<Void>(task, "saveKeyWord", list)
        var queryKeyWords = KeyWordDbUtils.queryKeyWords(recordId)
        println("saveKeyWord result:$queryKeyWords")
        Assert.assertEquals(0, queryKeyWords.size)

        list.add(KeyWord("测试", 0.5f))
        Whitebox.invokeMethod<Void>(task, "saveKeyWord", list)
        queryKeyWords = KeyWordDbUtils.queryKeyWords(recordId)
        println("saveKeyWord result:$queryKeyWords")
        Assert.assertEquals(0, queryKeyWords.size)
    }

    @Test
    fun should_void_when_onSuccess() {
        val list: MutableList<KeyWord> = mutableListOf()
        task?.onSuccess(list)
        Assert.assertEquals(0, callback.data.size)

        list.add(KeyWord("测试1", 0.5f))
        list.add(KeyWord("测试2", 0.5f))
        task?.onSuccess(list)
        Assert.assertEquals(2, callback.data.size)
    }

    @Test
    fun should_void_when_onFailed() {
        task?.onFailed(100, "无网络")
        Assert.assertEquals(100, callback.code)

        task?.onFailed(404, "Not Found")
        Assert.assertEquals(404, callback.code)
    }


    @Test
    @Ignore
    fun should_when_run() {
        task?.run()
    }
}