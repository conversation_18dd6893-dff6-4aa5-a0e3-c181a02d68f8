package com.soundrecorder.convertservice.convert;

import android.content.Context;
import android.os.Build;
import android.os.IBinder;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.convertservice.convert.ConvertTaskThreadManager;
import com.soundrecorder.convertservice.convert.NewConvertTextBinder;
import com.soundrecorder.convertservice.convert.NewConvertTextService;
import com.soundrecorder.convertservice.shadows.ShadowFeatureOption;
import com.soundrecorder.base.BaseApplication;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.Robolectric;
import org.robolectric.android.controller.ServiceController;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowFeatureOption.class})
public class NewConvertTextServiceTest {

    private Context mContext;
    private ServiceController<NewConvertTextService> controller;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
        controller = Robolectric.buildService(NewConvertTextService.class);
    }

    @After
    public void tearDown() {
        controller = null;
        mContext = null;
    }

    @Test
    public void should_not_null_when_onCreate() {
        NewConvertTextService service = controller.create().get();
        Assert.assertNotNull(service);
        Assert.assertNotNull(service.getMConvertTaskManager());
        Assert.assertNotNull(service.getMConvertTaskManager().getJobManagerLifeCycleCallback());
    }

    @Test
    public void should_not_null_when_onBind() {
        NewConvertTextService service = controller.create().get();
        IBinder binder = service.onBind(null);
        Assert.assertNotNull(binder);
        Assert.assertTrue(binder instanceof NewConvertTextBinder);
    }

    @Test
    public void should_null_when_onDestroy() {
        NewConvertTextService service = controller.create().get();
        ConvertTaskThreadManager manager = service.getMConvertTaskManager();
        service.onDestroy();
        Assert.assertEquals(0, manager.getMUICallbacks().size());
        Assert.assertNull(manager.getJobManagerLifeCycleCallback());
        Assert.assertNull(service.getMConvertTaskManager());
        Assert.assertNull(Whitebox.getInternalState(service, "mMainHandler"));
    }
}
