/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/01/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.convertservice.convert;

import android.content.Context;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.common.databean.BeanConvertText;
import com.soundrecorder.common.databean.ConvertVad;
import com.soundrecorder.common.databean.KeyWord;
import com.soundrecorder.convertservice.shadows.ShadowFeatureOption;
import com.soundrecorder.convertservice.shadows.ShadowNewConvertResultUtil;
import com.soundrecorder.convertservice.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class, ShadowNewConvertResultUtil.class})
public class NewConvertResultUtilTest {

    private static final String CONVERT_PATH_FLAG = "/convert";
    private static final String TRANS_PREFIX = "convert_";
    private static final String TRANS_SPLIT = "_";
    private static final String TRANS_POSTFIX = ".txt";
    private static final Long MEDIA_RECORD_ID_TEST = 8L;
    private static final String SPLIT_FLAG = "/";
    private static final String LINE_END = "\n";
    private static final String TEST_FILE_NAME = "test_file";
    private static final String TEST_FILE_NAME_WITH_POSTFIX = "test_file.mp3";
    private static final String TEST_TEXT = "This is convert text.";
    private static final String COMBINE_CONTENTS = "combineContents";

    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
        ShadowLog.stream = System.out;
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    public void should_return_true_when_getConvertSavePath() {
        String convertSavePath = NewConvertResultUtil.getConvertSavePath(mContext);
        Assert.assertNotNull(convertSavePath);
        String expectedPath = mContext.getFilesDir() + CONVERT_PATH_FLAG;
        Assert.assertEquals(expectedPath, convertSavePath);
    }

    @Test
    public void should_return_contents_when_combineContents_vad() throws Exception {
        long startTime = System.currentTimeMillis();
        long endTime = startTime + 1000L;
        ConvertVad vad = new ConvertVad(startTime, endTime, TEST_TEXT);
        String actual = Whitebox.invokeMethod(NewConvertResultUtil.class, COMBINE_CONTENTS, vad);
        Assert.assertNotNull(actual);
        String expected = startTime + SPLIT_FLAG + endTime + SPLIT_FLAG + TEST_TEXT + SPLIT_FLAG + LINE_END;
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void should_return_not_null_when_combineContents_sentence() throws Exception {
        String testTime = String.valueOf(System.currentTimeMillis());
        BeanConvertText.SubItem subItem = new BeanConvertText.SubItem(0, 0, TEST_TEXT, testTime, testTime);
        String actual = Whitebox.invokeMethod(NewConvertResultUtil.class, COMBINE_CONTENTS, subItem);
        Assert.assertNotNull(actual);
    }

   /* @Test
    public void should_return_not_null_when_combineContents_item() throws Exception {
        long startTime = System.currentTimeMillis();
        long endTime = startTime + 1000L;
        ConvertContentItem convertContentItem = new ConvertContentItem(startTime, endTime, TEST_TEXT, false, 0, "", null, null, null);
        String actual = Whitebox.invokeMethod(NewConvertResultUtil.class, COMBINE_CONTENTS, convertContentItem);
        Assert.assertNotNull(actual);
    }*/

    @Test
    public void should_return_file_when_checkOrCreateFile() throws Exception {
        File file = NewConvertResultUtil.checkOrCreateFile(mContext, TEST_FILE_NAME);
        Assert.assertNotNull(file);
    }

    @Test
    public void should_return_true_when_genFileName() {
        String fileName = NewConvertResultUtil.genFileName(0L, "");
        Assert.assertNotNull(fileName);
        fileName = NewConvertResultUtil.genFileName(MEDIA_RECORD_ID_TEST, TEST_FILE_NAME);
        Assert.assertNotNull(fileName);
        String expectedFileName = TRANS_PREFIX + MEDIA_RECORD_ID_TEST + TRANS_SPLIT + TEST_FILE_NAME.toLowerCase() + TRANS_POSTFIX;
        Assert.assertEquals(expectedFileName, fileName);
        fileName = NewConvertResultUtil.genFileName(MEDIA_RECORD_ID_TEST, TEST_FILE_NAME_WITH_POSTFIX);
        Assert.assertNotNull(fileName);
        Assert.assertEquals(expectedFileName, fileName);
    }

    @Test
    public void should_return_file_size_when_getConvertFileSize() {
        long expected = 0L;
        long actual = NewConvertResultUtil.getConvertFileSize(mContext.getFilesDir() + "/convert/" + TEST_FILE_NAME);
        Assert.assertEquals(expected, actual);
    }

    @Test
    public void should_return_when_readConvertFile() {
        try {
            NewConvertResultUtil.readConvertFile(mContext, TEST_FILE_NAME);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void should_return_when_parseInt() {
        try {
            int num = Whitebox.invokeMethod(NewConvertResultUtil.class, "parseInt", "10");
            Assert.assertTrue(num == 10);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void should_return_when_parseLong() {
        try {
            int num = Whitebox.invokeMethod(NewConvertResultUtil.class, "parseLong", "10");
            Assert.assertTrue(num == 10);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void should_return_when_reWriteConvertFile() {
        try {
            List<BeanConvertText.SubItem> subItems = new ArrayList<>();
            BeanConvertText.SubItem item = new BeanConvertText.SubItem(
                    1, 100, "ceshiyixia", "0", "1000", "/", "asd");
            subItems.add(item);
            List<KeyWord> keyWords = new ArrayList<>();
            KeyWord word = new KeyWord("e", 1, 1, 1, "");
            keyWords.add(word);
            BeanConvertText textBean = new BeanConvertText("111", "15453", "asdasd", "a", subItems, keyWords,null);
            NewConvertResultUtil.reWriteConvertFile(mContext, TEST_FILE_NAME, textBean);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void should_return_when_appendConvertVad() {
        try {
            long startTime = System.currentTimeMillis();
            long endTime = startTime + 1000L;
            ConvertVad vad = new ConvertVad(startTime, endTime, TEST_TEXT);
            NewConvertResultUtil.appendConvertVad(mContext, TEST_FILE_NAME, vad);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
