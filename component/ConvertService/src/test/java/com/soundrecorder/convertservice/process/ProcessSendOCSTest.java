package com.soundrecorder.convertservice.process;

import android.content.Context;
import android.os.Build;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.reflect.Whitebox;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import com.soundrecorder.common.constant.RecordConstant;
import com.soundrecorder.common.databean.UploadRecord;
import com.soundrecorder.convertservice.bean.Constant;
import com.soundrecorder.convertservice.bean.RequestGetUploadResult;
import com.soundrecorder.convertservice.shadows.ShadowFeatureOption;
import com.soundrecorder.convertservice.shadows.ShadowOS12FeatureUtil;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.common.databean.ConvertRecord;
import com.soundrecorder.common.utils.ConvertDbUtil;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class ProcessSendOCSTest {

    private static final long TEST_RECORD_ID = 1L;
    private static final String TEST_ONLY_ID = "test_only_id";
    private static final String TEST_UPLOAD_KEY = "test_upload_key";
    private static final String TEST_TASK_ID = "test_task_id";
    private static final String TEST_UPLOAD_ALL_URL = "test_upload_all_url";
    private static final String TEST_MY_URL = "test_my_url";

    private Context mContext;
    private ProcessSendOCS mProcessSendOCS;

    @Before
    public void setUp() {
        ShadowLog.stream = System.out;
        mContext = BaseApplication.getAppContext();
        System.setProperty("javax.net.ssl.trustStore", "NONE");
        mProcessSendOCS = new ProcessSendOCS(null, null);
    }

    @After
    public void tearDown() {
        mContext = null;
        mProcessSendOCS = null;
    }

    @Test
    public void should_correct_when_checkIsDirtyDataFromDb() throws Exception {
        ConvertRecord convertRecord = new ConvertRecord();
        convertRecord.setVersion(ConvertDbUtil.VERSION_2_SPEAKER);
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataFromDb", convertRecord));

        convertRecord.setOnlyId(TEST_ONLY_ID);
        convertRecord.setUploadKey(TEST_UPLOAD_KEY);
        convertRecord.setPartCount(1);
        List<UploadRecord> uploadRecordList = new ArrayList<>();
        uploadRecordList.add(new UploadRecord());
        convertRecord.setUploadRecordList(uploadRecordList);
        Assert.assertFalse(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataFromDb", convertRecord));

        convertRecord.setVersion(ConvertDbUtil.VERSION_0);
        convertRecord.setCompleteStatus(ConvertDbUtil.CONVERT_COMP_STATUS_TRANSLATING);
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataFromDb", convertRecord));
    }

    @Test
    public void should_correct_when_checkIsDirtyDataGetURLs() throws Exception {
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataGetURLs", new Object[]{null}));
        ConvertRecord convertRecord = new ConvertRecord();
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataGetURLs", convertRecord));
        convertRecord.setOnlyId(TEST_ONLY_ID);
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataGetURLs", convertRecord));
        convertRecord.setUploadKey(TEST_UPLOAD_KEY);
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataGetURLs", convertRecord));
        convertRecord.setPartCount(1);
        Assert.assertFalse(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataGetURLs", convertRecord));
    }

    @Test
    public void should_correct_when_checkIsDirtyDataSendOCS() throws Exception {
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataSendOCS", new Object[]{null, null}));
        ConvertRecord convertRecord = new ConvertRecord();
        UploadRecord uploadRecord = new UploadRecord();
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataSendOCS", convertRecord, uploadRecord));
        convertRecord.setOnlyId(TEST_ONLY_ID);
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataSendOCS", convertRecord, uploadRecord));
        uploadRecord.setMUrl(TEST_MY_URL);
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataSendOCS", convertRecord, uploadRecord));
        uploadRecord.setMFileStartRange(1);
        uploadRecord.setMFileEndRange(3);
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "checkIsDirtyDataSendOCS", convertRecord, uploadRecord));
    }

    @Test
    public void should_check_all_part_down_when_checkAllPartDone() throws Exception {
        ConvertRecord req = new ConvertRecord();
        List<UploadRecord> uploadRecordList = new ArrayList<>();
        uploadRecordList.add(new UploadRecord(0, "", 0, 0, "etag_1", "", 1));
        uploadRecordList.add(new UploadRecord(0, "", 0, 0, "etag_2", "", 2));
        uploadRecordList.add(new UploadRecord(0, "", 0, 0, "etag_0", "", 0));
        req.setUploadRecordList(uploadRecordList);
        UploadRecord uploadRecord = Whitebox.invokeMethod(mProcessSendOCS, "checkAllPartDone", req);
        Assert.assertNull(uploadRecord);
        uploadRecordList.add(new UploadRecord(0, "", 0, 0, "", "", -1));
        uploadRecord = Whitebox.invokeMethod(mProcessSendOCS, "checkAllPartDone", req);
        Assert.assertNotNull(uploadRecord);
        Assert.assertEquals(-1, uploadRecord.getMSeqNumber());
    }

    @Test
    public void should_gen_etag_list_when_genETagList() throws Exception {
        ConvertRecord req = new ConvertRecord();
        List<UploadRecord> uploadRecordList = new ArrayList<>();
        uploadRecordList.add(new UploadRecord(0, "", 0, 0, "etag_1", "", 1));
        uploadRecordList.add(new UploadRecord(0, "", 0, 0, "etag_2", "", 2));
        uploadRecordList.add(new UploadRecord(0, "", 0, 0, "etag_0", "", 0));
        req.setUploadRecordList(uploadRecordList);
        List<RequestGetUploadResult.ETags> eTagsList = Whitebox.invokeMethod(mProcessSendOCS, "genETagList", req);
        Assert.assertNotNull(eTagsList);
        Assert.assertEquals(0, eTagsList.get(0).getPartNumber());
    }

    @Test
    public void should_correct_when_isConditionMet() throws Exception {
        mProcessSendOCS.setMFileFormat(RecordConstant.MIMETYPE_WAV);
        Assert.assertFalse(Whitebox.invokeMethod(mProcessSendOCS, "isConditionMet", TEST_RECORD_ID));
        mProcessSendOCS.setMFileFormat(RecordConstant.MIMETYPE_MP3);
        Assert.assertFalse(Whitebox.invokeMethod(mProcessSendOCS, "isConditionMet", TEST_RECORD_ID));
        mProcessSendOCS.setMFileSize(Constant.SIZE_500M);
        Assert.assertFalse(Whitebox.invokeMethod(mProcessSendOCS, "isConditionMet", TEST_RECORD_ID));
        mProcessSendOCS.setMFileSize(Constant.SIZE_10M);
        Assert.assertFalse(Whitebox.invokeMethod(mProcessSendOCS, "isConditionMet", TEST_RECORD_ID));
        mProcessSendOCS.setMFileDuration(Constant.TIME_5H);
        Assert.assertFalse(Whitebox.invokeMethod(mProcessSendOCS, "isConditionMet", TEST_RECORD_ID));
        mProcessSendOCS.setMFileDuration(Constant.LONG_1000);
        Assert.assertTrue(Whitebox.invokeMethod(mProcessSendOCS, "isConditionMet", TEST_RECORD_ID));
    }

    @Test
    public void should_gen_part_count_when_genPartCount() throws Exception {
        int result = Whitebox.invokeMethod(mProcessSendOCS, "genPartCount", 0L);
        Assert.assertEquals(0, result);
        result = Whitebox.invokeMethod(mProcessSendOCS, "genPartCount", (long) Constant.SIZE_10M);
        Assert.assertEquals(1, result);
    }

    @Test
    public void should_returnFalse_when_getPresignedURLs() throws Exception {
        ConvertRecord req = new ConvertRecord();
        boolean result = Whitebox.invokeMethod(mProcessSendOCS, "getPresignedURLs", req);
        Assert.assertFalse(result);
        req.setOnlyId("1");
        req.setPartCount(2);
        req.setUploadKey("1");
        Assert.assertFalse(result);
        boolean result1 = Whitebox.invokeMethod(mProcessSendOCS, "getPresignedURLs", req);
        Assert.assertFalse(result1);
    }

    @Test
    public void should_returnFalse_when_sendOCS() throws Exception {
        ConvertRecord req = new ConvertRecord();
        boolean result = Whitebox.invokeMethod(mProcessSendOCS, "sendOCS", req);
        Assert.assertFalse(result);
    }

    @Test
    public void should_returnFalse_when_sendOCSPartly() throws Exception {
        ConvertRecord req = new ConvertRecord();
        req.setUploadRecordList(new ArrayList<>());
        Assert.assertNotNull(Whitebox.invokeMethod(mProcessSendOCS, "sendOCSPartly", req));
    }

    @Test
    public void should_returnFalse_when_getUploadResult() throws Exception {
        ConvertRecord req = new ConvertRecord();
        req.setUploadRecordList(new ArrayList<>());
        boolean result = Whitebox.invokeMethod(mProcessSendOCS, "getUploadResult", req);
        Assert.assertFalse(result);
        req.setOnlyId("1");
        req.setPartCount(2);
        req.setUploadKey("1");
        req.setUploadRequestId("1");
        boolean result1 = Whitebox.invokeMethod(mProcessSendOCS, "getUploadResult", req);
        Assert.assertFalse(result1);
        Assert.assertFalse(result1);
    }

    @Test
    public void should_returnFalse_when_filterDirtyDataFromDb() throws Exception {
        ConvertRecord req = new ConvertRecord();
        ConvertRecord result = Whitebox.invokeMethod(mProcessSendOCS, "filterDirtyDataFromDb", req);
        Assert.assertNull(result);
    }

    @Test
    public void should_returnFalse_when_genRequestId() throws Exception {
        ConvertRecord req = new ConvertRecord();
        req.setUploadKey("1");
        String result = Whitebox.invokeMethod(mProcessSendOCS, "genRequestId", req);
        Assert.assertNotEquals(result, "1-" + UUID.randomUUID().toString());
    }

    @Test
    @Ignore
    public void should_returnFalse_when_genUploadKey() throws Exception {
        ConvertRecord req = new ConvertRecord();
        req.setUploadKey("1");
        String result = Whitebox.invokeMethod(mProcessSendOCS, "genUploadKey", req);
        Assert.assertNotNull(result);
    }

    @Test
    public void should_returnFalse_when_isNeedSendOCS() throws Exception {
        ConvertRecord req = new ConvertRecord();
        req.setUploadKey("1");
        boolean result = Whitebox.invokeMethod(mProcessSendOCS, "isNeedSendOCS", req);
        Assert.assertTrue(result);
    }

    @Test
    public void should_returnFalse_when_genUploadRecordList() throws Exception {
        ConvertRecord req = new ConvertRecord();
        req.setPartCount(3);
        req.setOnlyId("2");
        List<UploadRecord> result = Whitebox.invokeMethod(mProcessSendOCS, "genUploadRecordList", req);
        Assert.assertNotNull(result);
    }
}
