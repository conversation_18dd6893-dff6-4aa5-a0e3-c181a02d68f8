apply from: "../../common_build.gradle"
apply from: "./share_env_config.gradle"

android {
    namespace 'com.soundrecorder.share'
    defaultConfig {
        def config = project.recordShareLinkProdEnv.toBoolean() ? prodConfig : testConfig
        buildConfigField "String", "HTTP", config.HTTP
        buildConfigField "String", "GLOBAL_BASE_URL", config.BASE_URL
        buildConfigField "String", "RSA_PUBLIC_KEY", config.RSA_KEY
        buildConfigField "String", "APP_KEY_CONTENT", config.APP_KEY
        buildConfigField "String", "SECRET", config.SECRET
    }

    buildFeatures {
        buildConfig true
    }
}

dependencies {
    implementation project(':common:libbase')
    implementation project(':common:libcommon')
    implementation project(':common:modulerouter')

    // Koin for Android
    implementation(libs.koin)
    implementation libs.androidx.core.ktx
    implementation(libs.heytap.nearx.http) {
        /*#4118634: clientId sdk引入了android.permission.READ_PHONE_STATE权限，外销需去掉该sdk，否则有安规问题*/
        exclude group: 'com.heytap.baselib', module: 'clientId'
    }
    // add for AES\RSA
    implementation libs.commons.codec
    implementation libs.gson
}