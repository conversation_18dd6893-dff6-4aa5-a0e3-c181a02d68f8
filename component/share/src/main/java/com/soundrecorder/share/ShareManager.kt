/***********************************************************
 * * Copyright (C), 2010-2024, OPLUS Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File:  ShareManager
 * * Description: ShareManager
 * * Version: 1.0
 * * Date : 2025/3/6
 * * Author: W9066446
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * *  W9066446    2025/3/6   1.0    build this module
 ****************************************************************/
package com.soundrecorder.share

import android.app.Activity
import android.content.ClipData
import android.content.ClipboardManager
import android.content.ContentUris
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Parcelable
import android.provider.MediaStore
import android.text.TextUtils
import androidx.core.content.FileProvider
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.MD5Utils
import com.soundrecorder.base.utils.RecorderICUFormateUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.ConvertStaticsUtil
import com.soundrecorder.common.buryingpoint.ShareStatisticsUtil
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.common.flexible.FollowHandDialogUtils
import com.soundrecorder.common.share.OShareConvertUtil
import com.soundrecorder.common.share.ShareSummaryCopy
import com.soundrecorder.common.share.ShareSummaryText
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.common.share.ShareTypeCopy
import com.soundrecorder.common.share.ShareTypeDoc
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.common.share.ShareTypeNote
import com.soundrecorder.common.share.ShareTypeRecorderAndText
import com.soundrecorder.common.share.ShareTypeText
import com.soundrecorder.common.utils.FileDealUtil.REQUEST_CODE_SHARE
import com.soundrecorder.modulerouter.share.ERROR_CODE_REDIRECT_FAILED
import com.soundrecorder.modulerouter.share.ERROR_CODE_RESOURCE_LOADING_FAILED
import com.soundrecorder.modulerouter.share.ERROR_CODE_UNKNOWN
import com.soundrecorder.modulerouter.share.IShareListener
import com.soundrecorder.modulerouter.share.ShareType
import com.soundrecorder.share.normal.doc.ExportDocXmlComposer
import com.soundrecorder.share.normal.doc.ExportFormatDocUtils
import com.soundrecorder.share.normal.link.manager.LinkShareManager
import com.soundrecorder.share.normal.link.manager.listener.ILinkShareListener
import com.soundrecorder.share.normal.note.ExportNoteHelper
import com.soundrecorder.share.normal.text.ShareTxtUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.util.Date

object ShareManager {
    private const val TAG = "ShareManager"
    private const val CLIPBOARD_LABEL = "transfer"
    private const val MIN_TIME_SHOW_CLOSE_WAITING_TIPS: Long = 1000
    private val shareListeners: MutableSet<IShareListener> = mutableSetOf()
    private var generationLinkStartTime = 0L

    private var linkShareListener = object : ILinkShareListener {
        override fun onLinkShare(mediaId: Long, progress: Int) {
            DebugUtil.d(TAG, "onLinkShare mediaId=$mediaId progress=$progress")
        }

        override fun onLinkShareComplete(mediaId: Long, link: String) {
            DebugUtil.d(TAG, "onLinkShareComplete mediaId=$mediaId link=$link")
            ShareStatisticsUtil.addGenerationLinkTimeEvent(
                System.currentTimeMillis() - generationLinkStartTime,
                ShareStatisticsUtil.VALUE_LINK_GENERATION_SUCCESS
            )
            shareSuccess(mediaId, ShareTypeLink(link))
            unregisterLinkShareListener()
        }

        override fun onLinkShareError(mediaId: Long, error: Int) {
            DebugUtil.d(TAG, "onLinkShareError mediaId=$mediaId error=$error")
            ShareStatisticsUtil.addGenerationLinkTimeEvent(
                System.currentTimeMillis() - generationLinkStartTime,
                error
            )
            shareFailed(mediaId, ShareTypeLink(), error, "")
            unregisterLinkShareListener()
        }
    }

    private fun unregisterLinkShareListener() {
        LinkShareManager.instance.unregisterLinkShareListener(linkShareListener)
    }

    @JvmStatic
    fun share(
        activity: Activity?,
        shareTextContent: ShareTextContent,
        coroutineScope: CoroutineScope?,
        shareListener: IShareListener?,
        type: ShareType
    ) {
        DebugUtil.d(TAG, "share type:$type  shareTextContent:$shareTextContent")
        when (type) {
            is ShareTypeCopy ->
                clipboard(activity, coroutineScope, shareListener, shareTextContent, type)

            is ShareTypeNote ->
                shareToNote(activity, coroutineScope, shareListener, shareTextContent, type)

            is ShareTypeText ->
                shareText(activity, coroutineScope, shareListener, shareTextContent, type)

            is ShareTypeDoc ->
                shareToDoc(coroutineScope, activity, shareListener, shareTextContent, type)

            is ShareTypeLink ->
                shareLink(shareTextContent, type)

            is ShareTypeRecorderAndText ->
                shareRecordAndText(coroutineScope, activity, shareListener, shareTextContent, type)

            is ShareSummaryCopy -> clipboardSummary(activity, coroutineScope, shareListener, shareTextContent, type)
            is ShareSummaryText -> shareSummaryText(activity, coroutineScope, shareListener, shareTextContent, type)
        }
    }

    @JvmStatic
    fun registerShareListener(listener: IShareListener) {
        shareListeners.add(listener)
    }

    @JvmStatic
    fun unregisterShareListener(listener: IShareListener) {
        shareListeners.remove(listener)
    }

    private fun showShareWaitingDialog(
        mediaId: Long,
        type: ShareType,
        listener: IShareListener? = null
    ) {
        listener?.onShowShareWaitingDialog(mediaId, type)
        shareListeners.forEach {
            it.onShowShareWaitingDialog(mediaId, type)
        }
    }

    private fun shareSuccess(mediaId: Long, type: ShareType, listener: IShareListener? = null) {
        listener?.onShareSuccess(mediaId, type)
        shareListeners.forEach {
            it.onShareSuccess(mediaId, type)
        }
    }

    private fun shareFailed(
        mediaId: Long,
        type: ShareType,
        error: Int,
        message: String,
        listener: IShareListener? = null
    ) {
        listener?.onShareFailed(mediaId, type, error, message)
        shareListeners.forEach {
            it.onShareFailed(mediaId, type, error, message)
        }
    }

    private fun shareText(
        activity: Activity?,
        coroutineScope: CoroutineScope?,
        listener: IShareListener?,
        shareTextContent: ShareTextContent,
        type: ShareTypeText
    ) {
        DebugUtil.d(TAG, "shareText")
        if (activity == null) {
            DebugUtil.i(TAG, "shareText activity is null")
            return
        }
        if (coroutineScope == null) {
            DebugUtil.i(TAG, "shareText coroutineScope is null")
            return
        }
        coroutineScope.launch(Dispatchers.IO) {
            var needShowDialog = false
            var mShowDialogDuration: Long = 0
            if (shareTextContent.convertFileSize >= Constants.SHOW_WAITING_DIALOG_THRESHOLD) {
                needShowDialog = true
                mShowDialogDuration = System.currentTimeMillis()
                withContext(Dispatchers.Main) {
                    showShareWaitingDialog(shareTextContent.mediaRecordId, type, listener)
                }
                DebugUtil.d(TAG, "shareText needShowDialog:$needShowDialog")
            }
            val textFile = ShareTxtUtils.getShareTextFile(
                activity,
                shareTextContent.covertFileName,
                type.contentString,
                type
            )
            //显示不足1s，则延迟1s
            if (needShowDialog && System.currentTimeMillis() - mShowDialogDuration < MIN_TIME_SHOW_CLOSE_WAITING_TIPS) {
                delay(MIN_TIME_SHOW_CLOSE_WAITING_TIPS)
            }
            if (textFile == null) {
                withContext(Dispatchers.Main) {
                    DebugUtil.e(TAG, "shareText textFile is null")
                    shareFailed(
                        shareTextContent.mediaRecordId,
                        type,
                        ERROR_CODE_RESOURCE_LOADING_FAILED,
                        "get share text file failed",
                        listener
                    )
                }
                return@launch
            }
            DebugUtil.d(TAG, "shareText textFile: $textFile")
            withContext(Dispatchers.Main) {
                shareSuccess(shareTextContent.mediaRecordId, type, listener)
            }
            ShareTxtUtils.doExport(activity, null, textFile, shareTextContent.covertFileName)
        }
    }

    @Suppress("LongMethod")
    private fun shareToDoc(
        lifecycleScope: CoroutineScope?,
        activity: Activity?,
        listener: IShareListener?,
        shareTextContent: ShareTextContent,
        type: ShareTypeDoc
    ) {
        if (activity == null) {
            DebugUtil.i(TAG, "shareToDoc activity is null")
            return
        }
        if (lifecycleScope == null) {
            DebugUtil.i(TAG, "shareToDoc lifecycleScope is null")
            return
        }
        lifecycleScope.launch(Dispatchers.IO) {
            var showingDialog = false
            val startTime = System.currentTimeMillis()
            if (shareTextContent.convertFileSize > Constants.SHOW_WAITING_DIALOG_THRESHOLD) {
                withContext(Dispatchers.Main) {
                    showingDialog = true
                    showShareWaitingDialog(shareTextContent.mediaRecordId, type, listener)
                }
            }
            ExportFormatDocUtils.deleteExportDocTransferFile()
            val roleNameHashMap = LinkedHashMap<String, String>()
            val exportFile = exportConvertFile(
                shareTextContent.mediaRecordId,
                type.isShowSwitchInConvertModel,
                roleNameHashMap,
                shareTextContent.isShowSpeaker,
                shareTextContent.textContentItems
            )
            if (showingDialog && System.currentTimeMillis() - startTime < MIN_TIME_SHOW_CLOSE_WAITING_TIPS) {
                delay(MIN_TIME_SHOW_CLOSE_WAITING_TIPS)
            }
            /**
             * 返回结果，关闭waitingDialog，onShareFailed()不表示失败，只是为了关闭dialog
             * 因为不管跳转是否成功，都会关闭dialog
             */
            withContext(Dispatchers.Main) {
                DebugUtil.i(TAG, "executeExportFormatDoc invoke onShareFailed")
                showingDialog = false
                if (exportFile != null) {
                    val result = ExportFormatDocUtils.jumpToWpsExport(
                        activity = activity,
                        isShowSpeaker = shareTextContent.isShowSpeaker,
                        hasSpeakerName = roleNameHashMap.isNotEmpty(),
                        fileUri = genFileProviderUri(activity, exportFile)
                    )
                    if (result) {
                        shareSuccess(shareTextContent.mediaRecordId, type, listener)
                    } else {
                        val tips = "jumpToWpsExport failed"
                        DebugUtil.e(TAG, "shareToDoc $tips")
                        shareFailed(
                            shareTextContent.mediaRecordId,
                            type,
                            ERROR_CODE_REDIRECT_FAILED,
                            tips,
                            listener
                        )
                    }
                } else {
                    val tips = "exportFile is null"
                    DebugUtil.e(TAG, "shareToDoc $tips")
                    shareFailed(
                        shareTextContent.mediaRecordId,
                        type,
                        ERROR_CODE_RESOURCE_LOADING_FAILED,
                        tips,
                        listener
                    )
                }
            }
        }
    }

    private fun shareToNote(
        activity: Activity?,
        coroutineScope: CoroutineScope?,
        listener: IShareListener?,
        content: ShareTextContent,
        type: ShareTypeNote
    ) {
        if (activity == null) {
            DebugUtil.i(TAG, "ShareTypeNote activity is null")
            return
        }
        if (coroutineScope == null) {
            DebugUtil.i(TAG, "ShareTypeNote coroutineScope is null")
            return
        }
        coroutineScope.launch(Dispatchers.IO) {
            if (type.hasImg) {
                shareToNoteWithImg(activity, listener, content, type)
            } else {
                shareToNoteOnlyText(activity, listener, content, type)
            }
        }
    }

    private fun shareLink(shareTextContent: ShareTextContent, type: ShareTypeLink) {
        DebugUtil.d(TAG, "shareLink")
        generationLinkStartTime = System.currentTimeMillis()
        showShareWaitingDialog(shareTextContent.mediaRecordId, type)
        LinkShareManager.instance.registerLinkShareListener(linkShareListener)
        GlobalScope.launch(Dispatchers.IO) {
            LinkShareManager.instance.linkShare(shareTextContent, type)
        }
    }

    //添加 SnackBar 导出便签成功
    private suspend fun shareToNoteWithImg(
        activity: Activity,
        listener: IShareListener?,
        shareTextContent: ShareTextContent,
        type: ShareTypeNote
    ) {
        withContext(Dispatchers.IO) {
            var showingDialog = false
            val startTime = System.currentTimeMillis()
            if (shareTextContent.convertFileSize > Constants.SHOW_WAITING_DIALOG_THRESHOLD) {
                withContext(Dispatchers.Main) {
                    showingDialog = true
                    showShareWaitingDialog(shareTextContent.mediaRecordId, type, listener)
                }
            }
            val exportNoteHelper = ExportNoteHelper(
                    activity,
                    shareTextContent.mediaRecordId,
                    shareTextContent.textContentItems,
                    shareTextContent.isShowSpeaker
                )
            //该方法内 执行跳转便签
            val result = exportNoteHelper.shareToNoteWithImg()
            if (showingDialog && System.currentTimeMillis() - startTime < MIN_TIME_SHOW_CLOSE_WAITING_TIPS) {
                delay(MIN_TIME_SHOW_CLOSE_WAITING_TIPS)
            }
            withContext(Dispatchers.Main) {
                if (result) {
                    shareSuccess(shareTextContent.mediaRecordId, type, listener)
                    ConvertStaticsUtil.addSendToNote()
                    ToastManager.showShortToast(
                        activity,
                        com.soundrecorder.common.R.string.save_success
                    )
                } else {
                    val tips = activity.getString(com.soundrecorder.common.R.string.save_failed_tip)
                    shareFailed(
                        shareTextContent.mediaRecordId, type, ERROR_CODE_UNKNOWN, tips, listener
                    )
                    ToastManager.showShortToast(BaseApplication.getAppContext(), tips)
                }
            }
            DebugUtil.i(TAG, "shareToNoteWithImg result:$result")
        }
    }

    //添加 SnackBar 导出便签成功
    private suspend fun shareToNoteOnlyText(
        activity: Activity,
        listener: IShareListener?,
        shareTextContent: ShareTextContent,
        type: ShareTypeNote
    ) {
        withContext(Dispatchers.IO) {
            var showingDialog = false
            val startTime = System.currentTimeMillis()
            if (shareTextContent.convertFileSize > Constants.SHOW_WAITING_DIALOG_THRESHOLD) {
                withContext(Dispatchers.Main) {
                    showingDialog = true
                    showShareWaitingDialog(shareTextContent.mediaRecordId, type, listener)
                }
            }
            val textContent =
                getTextContent(
                    shareTextContent.isShowSpeaker,
                    shareTextContent.textContentItems
                )
            if (TextUtils.isEmpty(textContent)) {
                val tips = activity.getString(com.soundrecorder.common.R.string.export_no_text)
                ToastManager.showShortToast(BaseApplication.getAppContext(), tips)
                shareFailed(
                    shareTextContent.mediaRecordId,
                    type,
                    ERROR_CODE_RESOURCE_LOADING_FAILED,
                    tips,
                    listener
                )
                return@withContext
            }
            val exportNoteHelper = ExportNoteHelper(
                activity,
                shareTextContent.mediaRecordId,
                shareTextContent.textContentItems,
                shareTextContent.isShowSpeaker
            )
            val result = exportNoteHelper.shareToNoteOnlyText(textContent)
            if (showingDialog && System.currentTimeMillis() - startTime < MIN_TIME_SHOW_CLOSE_WAITING_TIPS) {
                delay(MIN_TIME_SHOW_CLOSE_WAITING_TIPS)
            }
            withContext(Dispatchers.Main) {
                if (!result) {
                    val tips = activity.getString(com.soundrecorder.common.R.string.save_failed_tip)
                    shareFailed(
                        shareTextContent.mediaRecordId, type, ERROR_CODE_UNKNOWN, tips, listener
                    )
                    ToastManager.showShortToast(BaseApplication.getAppContext(), tips)
                } else {
                    shareSuccess(shareTextContent.mediaRecordId, type, listener)
                }
            }
        }
    }

    private fun genFileProviderUri(activity: Activity?, txtFile: File?): Uri? {
        if ((activity == null) || (txtFile == null)) return null
        return FileProvider.getUriForFile(activity, activity.packageName + ".fileProvider", txtFile)
    }

    private fun getTimeString(recordId: Long?, playName: String?): String {
        val date = Date(
            RecorderDBUtil.getInstance(BaseApplication.getAppContext())
                .getCreateTimeByPath(
                    recordId
                        ?: -1, playName?.endsWith(".amr") ?: false
                )
        )
        return RecorderICUFormateUtils.formatDateTime(date)
    }

    /**
     * 导出转文本文件
     */
    private fun exportConvertFile(
        mediaRecordId: Long,
        isShowSwitchInConvertModel: Boolean,
        roleNameHashMap: LinkedHashMap<String, String>,
        isShowSpeaker: Boolean,
        convertContentList: List<ConvertContentItem>?
    ): File? {
        val exportFile: File?
        if (convertContentList == null) {
            DebugUtil.e(TAG, "exportConvertFile convertContentList is null")
            return null
        } else {
            val record = MediaDBUtils.queryRecordById(mediaRecordId)
            val fileName = record?.displayName?.title() ?: ""
            if (isShowSwitchInConvertModel) {
                setRoleName(convertContentList, roleNameHashMap)
            }
            exportFile = ExportFormatDocUtils.convertToOoxmlFileByVadList(
                fileDisplayName = fileName,
                timeString = getTimeString(mediaRecordId, record.displayName),
                convertList = convertContentList,
                roleNameHashMap = roleNameHashMap,
                isShowSpeaker = isShowSpeaker
            )
            DebugUtil.d(TAG, "convertToOoxmlFileByVadList exportFile null ${exportFile == null}")
        }
        return exportFile
    }

    private fun setRoleName(
        convertContentList: List<ConvertContentItem>?,
        roleNameHashMap: LinkedHashMap<String, String>
    ) {
        var i = 0
        convertContentList?.forEach {
            it.roleName?.apply {
                if (isNotEmpty() && !roleNameHashMap.containsKey(this)) {
                    roleNameHashMap[this] =
                        if (i < ExportDocXmlComposer.sSpeakerColorArray.size) {
                            ExportDocXmlComposer.sSpeakerColorArray[i]
                        } else {
                            ExportDocXmlComposer.sSpeakerColorArray[0]
                        }
                    i++
                }
            }
        }
    }

    /**
     * 分享录音文件与转写文本文件
     * 互传能力需要依赖互传APK，"send_entrance"字段是互传自定义的
     */
    @Suppress("LongMethod")
    private fun shareRecordAndText(
        coroutineScope: CoroutineScope?,
        activity: Activity?,
        listener: IShareListener?,
        content: ShareTextContent,
        type: ShareTypeRecorderAndText
    ) {
        if (activity == null) {
            DebugUtil.i(TAG, "shareRecordAndText activity is null")
            return
        }
        if (coroutineScope == null) {
            DebugUtil.i(TAG, "shareRecordAndText coroutineScope is null")
            return
        }
        val mediaId = content.mediaRecordId
        if ((mediaId <= 0L)) {
            DebugUtil.i(TAG, "shareRecordAndText send error, mediaId:$mediaId")
            return
        }
        coroutineScope.launch(Dispatchers.IO) {
            var showingDialog = false
            val startTime = System.currentTimeMillis()
            if (content.convertFileSize > Constants.SHOW_WAITING_DIALOG_THRESHOLD) {
                withContext(Dispatchers.Main) {
                    showingDialog = true
                    showShareWaitingDialog(content.mediaRecordId, type, listener)
                }
            }
            val record = MediaDBUtils.queryRecordById(mediaId)
            if (record == null) {
                val tips = "record is null"
                DebugUtil.i(TAG, "shareRecordAndText $tips")
                shareFailed(
                    content.mediaRecordId, type, ERROR_CODE_RESOURCE_LOADING_FAILED, tips, listener
                )
                return@launch
            }
            val recordFile = File(record.data)
            if (!recordFile.exists()) {
                val tips = "recordFile is not exits"
                DebugUtil.i(TAG, "shareRecordAndText $tips")
                shareFailed(
                    content.mediaRecordId, type, ERROR_CODE_RESOURCE_LOADING_FAILED, tips, listener
                )
                return@launch
            }
            val recordMd5 = MD5Utils.getMD5(recordFile)
            val title = record.displayName.title() ?: ""
            val fileName =
                OShareConvertUtil.getOShareTextFileName(title, recordMd5)
            if (fileName.isEmpty()) {
                val tips = "title is null or empty"
                DebugUtil.i(TAG, "shareRecordAndText $tips")
                shareFailed(
                    content.mediaRecordId, type, ERROR_CODE_RESOURCE_LOADING_FAILED, tips, listener
                )
                return@launch
            }
            //获取拼接好的文本
            val textContent = OShareConvertUtil.getTextContent(
                record, content.textContentItems
            )
            //写入text文件
            val textFileUri = ShareTxtUtils.getShareTextFile(activity, fileName, textContent)
            val audioUri =
                ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, mediaId)
            if (textFileUri == null) {
                val tips = "textFileUri or audioUri is null"
                DebugUtil.i(
                    TAG,
                    "shareRecordAndText $tips  textFileUri:${true}  audioUri:${false}"
                )
                shareFailed(
                    content.mediaRecordId, type, ERROR_CODE_RESOURCE_LOADING_FAILED, tips, listener
                )
                return@launch
            }
            if (showingDialog && System.currentTimeMillis() - startTime < MIN_TIME_SHOW_CLOSE_WAITING_TIPS) {
                delay(MIN_TIME_SHOW_CLOSE_WAITING_TIPS)
            }
            DebugUtil.i(TAG, "shareRecordAndText, mediaId:$mediaId")
            shareSuccess(content.mediaRecordId, type, listener)
            withContext(Dispatchers.Main) {
                val intent = Intent(Intent.ACTION_SEND_MULTIPLE)
                //用于标识互传相关，send_entrance是互传自定义参数
                intent.putExtra(OShareConvertUtil.OPPO_SHARE_SEND_ENTRANCE, activity.packageName)
                intent.putParcelableArrayListExtra(
                    Intent.EXTRA_STREAM,
                    ArrayList<Parcelable?>().apply {
                        //录音文件uri
                        add(audioUri)
                        //转写文本文件uri
                        add(textFileUri)
                    })
                val mineType = "*/*"
                intent.type = mineType
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                FollowHandDialogUtils.addShareDialogAnchor(type.anchor, intent)
                startChooserActivity(activity, intent)
            }
        }
    }

    private fun getTextContent(
        isShowSpeaker: Boolean,
        contents: List<ConvertContentItem>?
    ): String {
        val stringBuilder = StringBuilder()
        contents?.forEach {
            if (isShowSpeaker) {
                stringBuilder.append("${it.roleName}  ")
            }
            stringBuilder.append("${it.startTime.durationInMsFormatTimeExclusive()}\r\n")
            stringBuilder.append("${it.textContent}\r\n\r\n")
        }
        return stringBuilder.toString()
    }

    @Suppress("LongMethod")
    private fun clipboard(
        activity: Activity?,
        coroutineScope: CoroutineScope?,
        listener: IShareListener?,
        shareTextContent: ShareTextContent,
        type: ShareTypeCopy
    ) {
        if (activity == null) {
            DebugUtil.i(TAG, "clipboard activity is null")
            return
        }
        if (coroutineScope == null) {
            DebugUtil.i(TAG, "clipboard coroutineScope is null")
            return
        }
        coroutineScope.launch(Dispatchers.IO) {
            shareTextContent.apply {
                var showingDialog = false
                val startTime = System.currentTimeMillis()
                if (convertFileSize > Constants.SHOW_WAITING_DIALOG_THRESHOLD) {
                    withContext(Dispatchers.Main) {
                        showingDialog = true
                        showShareWaitingDialog(mediaRecordId, type, listener)
                    }
                }
                val manager =
                    activity.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
                if (manager == null) {
                    val tips = "ClipboardManager is null."
                    DebugUtil.i(TAG, "clipboard $tips")
                    shareFailed(mediaRecordId, type, ERROR_CODE_UNKNOWN, tips, listener)
                    return@launch
                }
                val textContent = getTextContent(isShowSpeaker, textContentItems)
                if (showingDialog && System.currentTimeMillis() - startTime < MIN_TIME_SHOW_CLOSE_WAITING_TIPS) {
                    delay(MIN_TIME_SHOW_CLOSE_WAITING_TIPS)
                }
                withContext(Dispatchers.Main) {
                    if (TextUtils.isEmpty(textContent)) {
                        val tips = "textContent is empty."
                        DebugUtil.i(TAG, "clipboard $tips")
                        shareFailed(
                            mediaRecordId, type, ERROR_CODE_RESOURCE_LOADING_FAILED, tips, listener
                        )
                        return@withContext
                    }
                    val data = ClipData.newPlainText(CLIPBOARD_LABEL, textContent)
                    kotlin.runCatching {
                        manager.setPrimaryClip(data)
                        shareSuccess(mediaRecordId, type, listener)
                        ToastManager.showLongToast(
                            activity,
                            com.soundrecorder.common.R.string.copied
                        )
                    }.onFailure {
                        DebugUtil.e(TAG, "clipboard", it)
                        shareFailed(
                            mediaRecordId, type, ERROR_CODE_UNKNOWN, it.message ?: "", listener
                        )
                    }
                }
            }
        }
    }

    @JvmStatic
    private fun clipboardSummary(
        activity: Activity?,
        coroutineScope: CoroutineScope?,
        listener: IShareListener?,
        shareTextContent: ShareTextContent,
        type: ShareSummaryCopy
    ) {
        activity ?: run {
            DebugUtil.i(TAG, "clipboardSummary activity is null")
            return
        }
        coroutineScope ?: run {
            DebugUtil.i(TAG, "clipboardSummary coroutineScope is null")
            return
        }
        coroutineScope.launch(Dispatchers.IO) {
            shareTextContent.apply {
                val manager =
                    activity.getSystemService(Context.CLIPBOARD_SERVICE) as? ClipboardManager
                        ?: run {
                            val tips = "ClipboardManager is null."
                            DebugUtil.i(TAG, "clipboardSummary $tips")
                            shareFailed(mediaRecordId, type, ERROR_CODE_UNKNOWN, tips, listener)
                            return@launch
                        }
                withContext(Dispatchers.Main) {
                    if (type.contentString.isEmpty()) {
                        val tips = "textContent is empty."
                        DebugUtil.i(TAG, "clipboardSummary $tips")
                        shareFailed(mediaRecordId, type, ERROR_CODE_RESOURCE_LOADING_FAILED, tips, listener)
                        return@withContext
                    }
                    val data = ClipData.newPlainText(CLIPBOARD_LABEL, type.contentString)
                    kotlin.runCatching {
                        manager.setPrimaryClip(data)
                        shareSuccess(mediaRecordId, type, listener)
                        ToastManager.showLongToast(activity, com.soundrecorder.common.R.string.copied)
                    }.onFailure {
                        DebugUtil.e(TAG, "clipboardSummary", it)
                        shareFailed(mediaRecordId, type, ERROR_CODE_UNKNOWN, it.message ?: "", listener)
                    }
                }
            }
        }
    }

    private fun shareSummaryText(
        activity: Activity?,
        coroutineScope: CoroutineScope?,
        listener: IShareListener?,
        shareTextContent: ShareTextContent,
        type: ShareSummaryText
    ) {
        DebugUtil.d(TAG, "shareSummaryText")
        if (activity == null) {
            DebugUtil.i(TAG, "shareText activity is null")
            return
        }
        if (coroutineScope == null) {
            DebugUtil.i(TAG, "shareText coroutineScope is null")
            return
        }

        coroutineScope.launch(Dispatchers.IO) {
            var needShowDialog = false
            var mShowDialogDuration: Long = 0
            if (shareTextContent.convertFileSize >= Constants.SHOW_WAITING_DIALOG_THRESHOLD) {
                needShowDialog = true
                mShowDialogDuration = System.currentTimeMillis()
                withContext(Dispatchers.Main) {
                    showShareWaitingDialog(shareTextContent.mediaRecordId, type, listener)
                }
                DebugUtil.d(TAG, "shareText needShowDialog:$needShowDialog")
            }
            val textFile = ShareTxtUtils.getShareTextFile(
                activity,
                shareTextContent.covertFileName,
                type.contentString,
                type
            )
            //显示不足1s，则延迟1s
            if (needShowDialog && System.currentTimeMillis() - mShowDialogDuration < MIN_TIME_SHOW_CLOSE_WAITING_TIPS) {
                delay(MIN_TIME_SHOW_CLOSE_WAITING_TIPS)
            }
            if (textFile == null) {
                withContext(Dispatchers.Main) {
                    shareFailed(
                        shareTextContent.mediaRecordId,
                        type,
                        ERROR_CODE_RESOURCE_LOADING_FAILED,
                        "get share summarytext file failed",
                        listener
                    )
                }
                return@launch
            }
            DebugUtil.d(TAG, "shareSummaryText textFile: $textFile")
            withContext(Dispatchers.Main) {
                shareSuccess(shareTextContent.mediaRecordId, type, listener)
            }
            ShareTxtUtils.doExport(activity, null, textFile, shareTextContent.covertFileName)
        }
    }

    private fun startChooserActivity(activity: Activity?, intent: Intent) {
        val chooserIntent = Intent.createChooser(
            intent, BaseApplication.getAppContext().getString(
                com.soundrecorder.common.R.string.send
            )
        )
        activity?.startActivityForResult(chooserIntent, REQUEST_CODE_SHARE)
    }
}