/***********************************************************
 * * Copyright (C), 2010-2025, OPLUS Mobile Comm Corp., Ltd.
 * * COLOROS_EDIT
 * * File:  LinkShareManager.kt
 * * Description: LinkShareManager
 * * Version: 1.0
 * * Date : 2025/3/7
 * * Author: zhangmeng
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <date>    <version>    <desc>
 * <EMAIL>    2025/3/7    1.0    create
 ****************************************************************/
package com.soundrecorder.share.normal.link.manager

import android.provider.Settings
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.OpenIdUtils
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.PrefUtil.KEY_LINK_SHARE_DOMAIN_NAME
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.share.ShareTextContent
import com.soundrecorder.common.share.ShareTypeLink
import com.soundrecorder.modulerouter.share.ERROR_CODE_CONTENT_RISK
import com.soundrecorder.share.normal.link.bean.ActionResult
import com.soundrecorder.share.normal.link.bean.ShareRegisterResponseContent
import com.soundrecorder.share.normal.link.bean.ZoneServerAddressResponseContent
import com.soundrecorder.share.normal.link.manager.listener.ILinkShareListener
import com.soundrecorder.share.normal.link.manager.listener.ILinkShareTaskListener
import com.soundrecorder.share.normal.link.utils.AesUtils
import com.soundrecorder.share.normal.link.utils.AesUtils.AES_KEY_LENGTH
import com.soundrecorder.share.normal.link.utils.ConstantUtils
import com.soundrecorder.share.normal.link.utils.HttpUtils
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Response
import java.io.File
import java.io.IOException
import java.util.Locale

class LinkShareManager private constructor() {

    companion object {
        private const val TAG = "ShareLinkManger"
        private const val GET_DOMAIN_NAME_PROGRESS_CODE = 100
        private const val GET_DOMAIN_NAME_ERROR_CODE = -100
        private const val SHARE_REGISTRATION_PROGRESS_CODE = 101
        private const val SHARE_REGISTRATION_ERROR_CODE = -101
        private const val RECORD_IS_NULL_ERROR_CODE = -102
        private const val CALL_SOURCE_UNKNOWN = 1001
        private const val BYTES_IN_KB = 1024L
        private const val GET_SHARE_LINK_REQUEST_MAX_COUNT = 2
        private const val CONVERT_CONTENT_DEFAULT_VERSION = "1.0"
        private const val CONVERT_CONTENT_APPROVED = "APPROVED"
        val instance: LinkShareManager by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            LinkShareManager()
        }
    }
    private var mRegionDomainMap = mutableMapOf<String, String>()
    private var mRegion: String? = null
    private var mRecordId: Long? = null
    private var mGetShareLinkRequestCount = 0
    private var mLinkShareListenerSet: MutableSet<ILinkShareListener> = HashSet()
    private val mDeviceId by lazy { OpenIdUtils.INSTANCE.duid ?: "" }

    private var mLinkShareTaskListener = object : ILinkShareTaskListener {
        override fun onLinkShareTask(path: String?, progress: Int) {
            DebugUtil.d(TAG, "onLinkShareTask path=$path progress=$progress")
        }

        override fun onLinkShareTaskComplete(path: String?, link: String?) {
            DebugUtil.d(TAG, "onLinkShareTaskComplete path=$path link=$link")
            unregisterLinkShareTaskListener()
        }

        override fun onLinkShareTaskError(path: String?, error: Int) {
            DebugUtil.d(TAG, "onLinkShareTaskError path=$path error=$error")
            unregisterLinkShareTaskListener()
        }
    }

    private fun unregisterLinkShareTaskListener() {
        LinkShareTask.unregisterLinkShareListener(mLinkShareTaskListener)
    }

    fun registerLinkShareListener(listener: ILinkShareListener) {
        this.mLinkShareListenerSet.clear()
        this.mLinkShareListenerSet.add(listener)
    }

    fun unregisterLinkShareListener(listener: ILinkShareListener) {
        this.mLinkShareListenerSet.remove(listener)
    }

    private fun linkShare(mediaId: Long, progress: Int) {
        mLinkShareListenerSet.forEach { it.onLinkShare(mediaId, progress) }
    }

    private fun linkShareComplete(mediaId: Long, link: String) {
        mLinkShareListenerSet.forEach { it.onLinkShareComplete(mediaId, link) }
    }

    private fun linkShareError(mediaId: Long, error: Int) {
        mLinkShareListenerSet.forEach { it.onLinkShareError(mediaId, error) }
    }

    fun linkShare(shareTextContent: ShareTextContent, type: ShareTypeLink) {
        this.mRecordId = shareTextContent.mediaRecordId
        this.mRegion = Locale.getDefault().country
        this.mGetShareLinkRequestCount = 0
        if (mRegionDomainMap[mRegion].isNullOrEmpty()) {
            mRegionDomainMap = PrefUtil.getStringMap(BaseApplication.getAppContext(), KEY_LINK_SHARE_DOMAIN_NAME, mutableMapOf<String, String>())
        }
        getGlobalDomainAndDoCallBack {
            getShareLinkAndStartAudioUploadTask(shareTextContent, type)
        }
    }

    /**
     * 调用通过区域编码获取区域服务器地址接口
     * 根据区域获取域名
     * 若该区域有对应的域名，则直接执行callback()
     *
    * */
    fun getGlobalDomainAndDoCallBack(callback: () -> Unit) {
        mRegion = Locale.getDefault().country
        if (!mRegionDomainMap[mRegion].isNullOrEmpty()) {
            callback.invoke()
            return
        }
        DebugUtil.d(TAG, "getGlobalDomainAndDoCallBack")
        val map = mutableMapOf<String, String>()
        mRegion?.let {
            map[ConstantUtils.REGION] = it
        } ?: run {
            return
        }
        val requestBody = Gson().toJson(map)
        val aesKey = AesUtils.randomToken(AES_KEY_LENGTH)
        HttpUtils.sendAsynPostEncryptedRequest(
            ConstantUtils.GLOBAL_BASE_URL,
            ConstantUtils.GET_GLOBAL_DOMAIN_REQUEST_PATH,
            requestBody,
            aesKey,
            object : Callback {
                override fun onResponse(call: Call, response: Response) {
                    val responseData = response.body?.string()
                    val decryptedRealResponseBody = HttpUtils.getDecryptedRealResponseBody(responseData, aesKey, "getGlobalDomainAndDoCallBack")
                    val typeToken = object : TypeToken<ActionResult<ZoneServerAddressResponseContent>>() {}.type
                    val decryptedActionResult = Gson().fromJson<ActionResult<ZoneServerAddressResponseContent>>(decryptedRealResponseBody, typeToken)
                    if (decryptedActionResult.code == 0) {
                        val domainName = decryptedActionResult.data?.requestDomain
                        if (!domainName.isNullOrEmpty()) {
                            mRegion?.let { mRegionDomainMap[it] = domainName }
                            PrefUtil.putStringMap(BaseApplication.getAppContext(), KEY_LINK_SHARE_DOMAIN_NAME, mRegionDomainMap)
                            callback.invoke()
                        }
                        mRecordId?.let {
                            linkShare(it, GET_DOMAIN_NAME_PROGRESS_CODE)
                        }
                    } else {
                        mRecordId?.let {
                            linkShareError(it, decryptedActionResult.code)
                        }
                    }
                }

                override fun onFailure(call: Call, e: IOException) {
                    DebugUtil.e(TAG, " getGlobalDomainAndDoCallBack onFailure javaClass=${e.javaClass} ${e.message}")
                    mRecordId?.let {
                        linkShareError(it, GET_DOMAIN_NAME_ERROR_CODE)
                    }
                }
            })
    }

    /**
     * 调用分享注册接口
     * 获取分享链接
     *
     * */
    private fun getShareLinkAndStartAudioUploadTask(shareTextContent: ShareTextContent, type: ShareTypeLink) {
        val recordId = shareTextContent.mediaRecordId
        var record = type.record
        DebugUtil.d(TAG, "getShareLinkAndStartAudioUploadTask recordId=$recordId")
        if (record == null || !isValidRecordAndAudioFileExists(record)) {
            DebugUtil.i(TAG, "getShareLinkAndStartAudioUploadTask record is null or isValidRecordAndAudioFileExists is false")
            record = MediaDBUtils.queryRecordById(recordId)
        }
        if (record == null || !isValidRecordAndAudioFileExists(record)) {
            DebugUtil.e(TAG, "getShareLinkAndStartAudioUploadTask record is null or isValidRecordAndAudioFileExists is false")
            linkShareError(recordId, RECORD_IS_NULL_ERROR_CODE)
            return
        }
        mGetShareLinkRequestCount += 1
        val baseUrl = ConstantUtils.HTTP + mRegionDomainMap[mRegion]
        val items = shareTextContent.textContentItems
        val isShowSpeaker = shareTextContent.isShowSpeaker
        val requestBody = creationGetShareLinkInterfaceRequestBody(recordId, record, items, isShowSpeaker)
        val aesKey = AesUtils.randomToken(AES_KEY_LENGTH)
        HttpUtils.sendAsynPostEncryptedRequest(baseUrl, ConstantUtils.REGISTER_PATH, requestBody, aesKey, object :
            Callback {
            override fun onResponse(call: Call, response: Response) {
                val responseData = response.body?.string()
                val decryptedRealResponseBody = HttpUtils.getDecryptedRealResponseBody(responseData, aesKey, "getShareLinkAndStartAudioUploadTask")
                val typeToken = object : TypeToken<ActionResult<ShareRegisterResponseContent>>() {}.type
                val decryptedActionResult = Gson().fromJson<ActionResult<ShareRegisterResponseContent>>(decryptedRealResponseBody, typeToken)
                if (decryptedActionResult.code != 0) {
                    DebugUtil.e(TAG, "getShareLinkAndStartAudioUploadTask code=${decryptedActionResult.code}")
                    linkShareError(recordId, decryptedActionResult.code)
                    return
                }
                if (decryptedActionResult.data?.auditStatus.equals(CONVERT_CONTENT_APPROVED)) {
                    val url = decryptedActionResult.data?.url
                    if (url.isNullOrEmpty()) {
                        DebugUtil.e(TAG, "getShareLinkAndStartAudioUploadTask url is null")
                        return
                    }
                    linkShare(recordId, SHARE_REGISTRATION_PROGRESS_CODE)
                    linkShareComplete(recordId, url)
                    val shareId = decryptedActionResult.data?.shareId
                    if (shareId != null) {
                        LinkShareTask.registerLinkShareTaskListener(mLinkShareTaskListener)
                        LinkShareTask.start(shareId, record, recordId)
                    }
                } else {
                    linkShareError(recordId, ERROR_CODE_CONTENT_RISK)
                }
            }

            override fun onFailure(call: Call, e: IOException) {
                DebugUtil.e(TAG, " getShareLinkAndStartAudioUploadTask onFailure javaClass=${e.javaClass} ${e.message}")
                // 针对DNS解析失败、连接超时等异常进行重试操作
                if (mGetShareLinkRequestCount >= GET_SHARE_LINK_REQUEST_MAX_COUNT) {
                    linkShareError(recordId, SHARE_REGISTRATION_ERROR_CODE)
                } else {
                    // 请求的域名错误(UnknownHostException、UnknownServiceException、ConnectException等异常)，移除当前区域对应的域名的活动缓存与SP中的缓存数据
                    mRegionDomainMap.remove(mRegion)
                    PrefUtil.putStringMap(BaseApplication.getAppContext(), KEY_LINK_SHARE_DOMAIN_NAME, mRegionDomainMap)
                    // 重新再次请求一次
                    getGlobalDomainAndDoCallBack {
                        getShareLinkAndStartAudioUploadTask(shareTextContent, type)
                    }
                }
            }
        })
    }

    private fun isValidRecordAndAudioFileExists(record: Record): Boolean {
        val isValidRecord =
            record.displayName.isNotEmpty() && record.dateModied != 0L && record.duration != 0L && record.fileSize != 0L
        if (!isValidRecord) {
            DebugUtil.d(TAG, "isValidRecordAndAudioFile isValidRecord=false")
            return false
        }
        var isRecordFileExists = false
        val audioFilePath = record.data
        val file = audioFilePath?.let {
            File(it)
        }
        if (file != null && file.exists()) {
            isRecordFileExists = true
        }
        DebugUtil.d(TAG, "isValidRecordAndAudioFile isRecordFileExists=$isRecordFileExists")
        return isRecordFileExists
    }

    private fun creationGetShareLinkInterfaceRequestBody(
        recordId: Long,
        record: Record,
        textContentItems: List<ConvertContentItem>,
        isShowSpeaker: Boolean = false
    ): String {
        val title = record.displayName.substringBeforeLast(".")
        val roles = mutableSetOf<Map<String, Any>>()
        val covertItemsList = mutableListOf<Map<String, Any>>()
        textContentItems.forEach { item ->
            if (isShowSpeaker) {
                roles.add(mapOf(
                        ConstantUtils.Convert.ROLE to item.roleId,
                        ConstantUtils.Convert.ROLE_NAME to (item.roleName ?: "")
                    )
                )
                covertItemsList.add(mapOf(
                        ConstantUtils.Convert.TIME to item.startTime,
                        ConstantUtils.Convert.ROLE to item.roleId,
                        ConstantUtils.Convert.ROLE_NAME to (item.roleName ?: ""),
                        ConstantUtils.Convert.TEXT to item.textContent
                    )
                )
            } else {
                covertItemsList.add(mapOf(
                        ConstantUtils.Convert.TIME to item.startTime,
                        ConstantUtils.Convert.TEXT to item.textContent
                    )
                )
            }
        }
        val extra = mutableMapOf<Any, Any>()
        extra[ConstantUtils.RegisterExtra.TITLE] = title                          // H5标题
        extra[ConstantUtils.RegisterExtra.COVERT_TIME] = record.dateModied        // H5标题下方时间
        extra[ConstantUtils.RegisterExtra.TYPE] = record.recordType               // 录音文件类型,0普通录音 1会议录音  2采访录音 3通话录音
        extra[ConstantUtils.RegisterExtra.CALL_SOURCE] = CALL_SOURCE_UNKNOWN      // 当type是通话录音类型时,0传统通话录音 1微信通话 2QQ通话 1000其他通话(当前不能区分来源,默认使用1001)
        extra[ConstantUtils.RegisterExtra.FILE_LOCAL_ID] = recordId               // 本地文件media id
        extra[ConstantUtils.RegisterExtra.FILE_DURATION] = record.duration        // 音频文件持续时间，单位ms
        extra[ConstantUtils.RegisterExtra.FILE_SIZE] = record.fileSize / BYTES_IN_KB   // 音频文件大小，单位KB
        if (roles.isNotEmpty()) {
            extra[ConstantUtils.RegisterExtra.ROLES] = roles                        // 角色
        }
        //分享选择了音频 ,为-1L 代表没有选择音频，非-1L 代表选择了音频
        extra[ConstantUtils.RegisterExtra.HAS_RECORD_FILE] = record.getmHasRecordFile()
        if (!record.getmHasRecordSummary().isNullOrEmpty()) {
            extra[ConstantUtils.RegisterExtra.SUMMARY] = record.getmHasRecordSummary()  //分享选择了摘要
        }
        if (record.ismHasRecordCovertItems()) {
            extra[ConstantUtils.RegisterExtra.COVERT_ITEMS] = covertItemsList  // 分享选择了文本，录音转写文本内容
        }
        val map = mutableMapOf<String, Any>()
        map[ConstantUtils.RegisterParam.USER_ID] = ""
        map[ConstantUtils.RegisterParam.DEVICE_ID] = mDeviceId
        map[ConstantUtils.RegisterParam.TITLE] = title
        map[ConstantUtils.RegisterParam.VERSION] = CONVERT_CONTENT_DEFAULT_VERSION
        map[ConstantUtils.RegisterParam.LANG] = Locale.getDefault().language
        map[ConstantUtils.RegisterParam.REGION] = Locale.getDefault().country
        map[ConstantUtils.RegisterParam.EXTRA] = extra
        return Gson().toJson(map)
    }

    fun getDomainName(): String? {
        return mRegionDomainMap[mRegion]
    }
}