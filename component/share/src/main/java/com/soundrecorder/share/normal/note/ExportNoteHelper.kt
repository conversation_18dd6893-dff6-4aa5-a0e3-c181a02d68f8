/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.share.normal.note

import android.app.Activity
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.FileProvider
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.snackbar.COUISnackBar
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.ext.durationInMsFormatTimeExclusive
import com.soundrecorder.base.ext.title
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.constant.Constants
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.note.ExportNoteUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class ExportNoteHelper(
    private var activity: Activity,
    private var mediaRecordId: Long,
    private val contents: List<ConvertContentItem>,
    private var isShowSpeaker: Boolean,
) {

    companion object {

        private const val TAG = "ExportNoteHelper"
        private const val TYPE_EXTRA_TITLE = "extra_title"
        private const val TYPE_EXTRA_CONTENT = "extra_content"
        private const val TYPE_EXTRA_PACKAGE_NAME = "extra_package_name"
        private const val EXPORT_MAX_LENGTH = 200000

        //便签OPPO及一加内销
        const val PACKAGE_NAME_NOTE = "com.coloros.note"
        const val PACKAGE_NAME_NOTE_OPLUS = "com.oplus.note"

        //便签一加外销
        private const val PACKAGE_NAME_NOTE_ONEPLUS_EXPORT = "com.oneplus.note"

        /**
         * 便签图文导出参数
         */
        private const val PACKAGE_NAME_KEY = "package_name"
        private const val TITLE_KEY = "title"
        private const val CONTENT_KEY = "content"

        //便签首页-全部笔记列表
        private const val ACTION_TO_NOTE_HOME_PAGE = "action.nearme.note.allnote"

        //便签笔记详情页
        const val ACTION_TO_NOTE_DETAIL = "action.nearme.note.textnote"
        private const val GUID = "guid"

        const val NOTE_PROVIDER = "com.nearme.note.db.NotesProvider"
        const val NOTE_PROVIDER_META_DATA = "com.oplus.note.db.insert_pic_text_note"

        const val NOTE_KET_PACKAGE_NAME = "package_name"
        const val NOTE_KEY_TITLE = "title"
        const val NOTE_KEY_CONTENT = "content"


        //图片uri前后的分隔符
        private const val DIVISION = "__END_OF_PART__"

        //便签支持的最大字数，包含换行符空格符等
        private const val MAX_LENGTH = 30000
        private const val SNACKBAR_DURATION_TIME = 5000
    }

    private var mContentList = mutableListOf<String>()
    private var mInsertUriList = mutableListOf<Uri>()

    //转文本图片标记 Uri
    private var mImgUriList: MutableList<Uri> = mutableListOf()

    private var mCOUISnackBar: COUISnackBar? = null

    /**
     * 保存至便签（支持图文混排）
     */
    suspend fun shareToNoteWithImg(): Boolean {
        return withContext(Dispatchers.IO) {
            var isSuccess = false
            contents.let { data ->
                val insertUris = shareToNote(activity, data, isShowSpeaker)
                if (insertUris.isNotEmpty()) {
                    withContext(Dispatchers.Main) {
                        showExportSuccessSnackBar(insertUris)
                    }
                    isSuccess = true
                } else {
                    DebugUtil.w(TAG, "shareToNoteWithImg: insertUris is empty")
                }
            }
            DebugUtil.i(TAG, "shareToNoteWithImg is success:$isSuccess")
            return@withContext isSuccess
        }
    }

    private fun shareToNote(context: Context, data: List<ConvertContentItem>, isShowSpeaker: Boolean): MutableList<Uri> {
        mInsertUriList.clear()
        mContentList = createContent(context, data, isShowSpeaker)
        if (mContentList.isEmpty()) {
            return mInsertUriList
        }

        val record = MediaDBUtils.queryRecordById(mediaRecordId)
        val displayName = record?.displayName ?: return mInsertUriList
        val title = displayName.title() ?: ""

        var insertUri: Uri? = null

        //PACKAGE_NAME
        val values = ContentValues()
        values.put(PACKAGE_NAME_KEY, context.packageName)

        //标题+正文
        if (mContentList.size == 1) {
            if (title.isNotEmpty()) {
                values.put(TITLE_KEY, title)
            }
            values.put(CONTENT_KEY, mContentList[0])
            insertUri = ExportNoteUtil.insertToNote(context, values)
            if (insertUri != null) {
                mInsertUriList.add(insertUri)
            }
        } else {
            for (i in mContentList.indices) {
                if (title.isNotEmpty()) {
                    val suffix = " (${i + 1})"
                    values.put(TITLE_KEY, genNoteTitle(title, suffix))
                }
                values.put(CONTENT_KEY, mContentList[i])
                insertUri = ExportNoteUtil.insertToNote(context, values)
                if (insertUri != null) {
                    mInsertUriList.add(insertUri)
                } else {
                    ExportNoteUtil.deleteNote(context, mInsertUriList)
                    mInsertUriList.clear()
                    break
                }
            }
        }
        ExportNoteUtil.revokeUriPermission(context, mImgUriList)
        mImgUriList.clear()
        return mInsertUriList
    }

    private fun genNoteTitle(title: String, suffix: String): String {
        val suffixLen = suffix.length
        return if (title.length + suffixLen <= 50) {
            title + suffix
        } else {
            val end = title.length - suffixLen
            if (end > 0) title.subSequence(0, end).toString() + suffix else title
        }
    }

    private fun getSpeakerInfo(contentItem: ConvertContentItem, isShowSpeaker: Boolean): StringBuilder {
        val speakerInfo = StringBuilder()
        if (isShowSpeaker) {
            speakerInfo.append("${contentItem.roleName}  ")
        }
        speakerInfo.append("${contentItem.startTime.durationInMsFormatTimeExclusive()}\n")
        return speakerInfo
    }

    /**
     * 创建保存至便签的正文
     */
    private fun createContent(context: Context, data: List<ConvertContentItem>, isShowSpeaker: Boolean): MutableList<String> {
        val contentBuilder = StringBuilder()
        val imgBuilder = StringBuilder()
        val contentList = mutableListOf<String>()
        //段落
        for (index in data.indices) {
            val textOrImageItems = data[index].mTextOrImageItems ?: return contentList
            //1.加上讲话人信息是否超字数
            val speakerInfo = getSpeakerInfo(data[index], isShowSpeaker)
            val maxLen1 = contentBuilder.length - imgBuilder.length + speakerInfo.length
            if (maxLen1 > MAX_LENGTH) {
                contentList.add(contentBuilder.toString())
                contentBuilder.clear()
                imgBuilder.clear()
            }
            contentBuilder.append(speakerInfo)

            //图片在转文本item第一个，删除 startTime 后的 \n
            if (textOrImageItems[1] is ConvertContentItem.ImageMetaData) {
                contentBuilder.delete(contentBuilder.length - 1, contentBuilder.length)
            }
            //子句
            parsingSentence(context, data[index], contentBuilder, imgBuilder, contentList, textOrImageItems)
            //图片在转文本item最后一个
            if (textOrImageItems[textOrImageItems.size - 1] is ConvertContentItem.ImageMetaData) {
                contentBuilder.append("\n")
            } else {
                contentBuilder.append("\n\n")
            }
        }

        if (contentBuilder.isNotEmpty()) {
            contentList.add(contentBuilder.toString())
        }
        DebugUtil.i(TAG, "contentList size:${contentList.size}")
        return contentList
    }

    /**
     * 解析转写文本字句
     */
    private fun parsingSentence(
        context: Context,
        data: ConvertContentItem,
        contentBuilder: StringBuilder,
        imgBuilder: StringBuilder,
        contentList: MutableList<String>,
        textOrImageItems: MutableList<ConvertContentItem.ItemMetaData>
    ) {
        for (i in 0 until textOrImageItems.size) {
            val textOrImageItem = textOrImageItems[i]
            //纯文本
            if (textOrImageItem is ConvertContentItem.TextItemMetaData) {
                textOrImageItem.textParagraph?.let { paragraph ->
                    for (j in 0 until paragraph.size) {
                        //2.加上本句话是否超字数
                        val maxLen =
                            contentBuilder.length - imgBuilder.length + paragraph[j].text.length
                        DebugUtil.i(
                            TAG, "maxLen:$maxLen curParaLen:${paragraph[j].text.length} " +
                                    "contentLen:${contentBuilder.length} imgLen:${imgBuilder.length}}"
                        )
                        if (maxLen > MAX_LENGTH) {
                            contentList.add(contentBuilder.toString())
                            contentBuilder.clear()
                            imgBuilder.clear()
                            contentBuilder.append(getSpeakerInfo(data, isShowSpeaker))
                        }
                        contentBuilder.append(paragraph[j].text)
                        DebugUtil.i(
                            TAG,
                            "contentBuilder length:${contentBuilder.length - imgBuilder.length}"
                        )
                    }
                }
            } else if (textOrImageItem is ConvertContentItem.ImageMetaData) {
                //图片
                val imgPath = textOrImageItem.imageItem?.pictureFilePath
                val imageFile = FileUtils.getAppFile(imgPath, false)
                //图片路径
                var imageUri: Uri? = null
                kotlin.runCatching {
                    //获取图片的uri
                    imageUri = FileProvider.getUriForFile(
                        context,
                        context.packageName + ".fileProvider",
                        imageFile
                    )
                }.onFailure {
                    DebugUtil.e(TAG, "createContent Exception is ", it)
                }
                DebugUtil.i(TAG, "imageUri is $imageUri")

                imageUri?.let {
                    ExportNoteUtil.grantUriPermission(context, PACKAGE_NAME_NOTE, it)
                    //图片uri的前后要加上分隔符“__END_OF_PART__”
                    contentBuilder.append(DIVISION).append(it.toString()).append(DIVISION)
                    imgBuilder.append(DIVISION).append(it.toString()).append(DIVISION)
                    mImgUriList.add(it)
                }
            }
        }
    }

    private suspend fun jumpToNote(context: Context, uris: MutableList<Uri>): Boolean {
        var result = false
        if (uris.size == 1) {
            val localId = ExportNoteUtil.getLocalId(mInsertUriList[0])
            if (localId.isEmpty()) {
                return result
            }
            withContext(Dispatchers.Main) {
                result = jumpToNoteDetail(context, localId)
            }
        } else if (uris.size > 1) {
            withContext(Dispatchers.Main) {
                result = jumpToNoteHome(context)
            }
        }
        return result
    }

    private fun jumpToNoteHome(context: Context): Boolean {
        return try {
            val intent = Intent(ACTION_TO_NOTE_HOME_PAGE)
            intent.setPackage(PACKAGE_NAME_NOTE)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            DebugUtil.e(TAG, "jumpToNoteHome fail", e)
            false
        }
    }

    private fun jumpToNoteDetail(context: Context, localId: String): Boolean {
        return try {
            val intent = Intent(ACTION_TO_NOTE_DETAIL)
            intent.`package` = PACKAGE_NAME_NOTE
            intent.putExtra(GUID, localId)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
            context.startActivity(intent)
            true
        } catch (e: Exception) {
            DebugUtil.e(TAG, "jumpToNoteDetail fail", e)
            false
        }
    }

    fun shareToNoteOnlyText(textContent: String): Boolean {
        if (TextUtils.isEmpty(textContent)) {
            ToastManager.showShortToast(
                BaseApplication.getAppContext(),
                com.soundrecorder.common.R.string.export_no_text
            )
            return true
        }
        if (textContent.length > EXPORT_MAX_LENGTH) {
            ToastManager.showShortToast(
                BaseApplication.getAppContext(),
                com.soundrecorder.common.R.string.export_text_too_long
            )
            return true
        }

        // 使用ExportNoteUtil插入便签并获取URI
        val insertUri = insertTextToNote(textContent)
        if (insertUri != null) {
            // 导出成功，显示SnackBar
            showExportSuccessSnackBar(listOf(insertUri))
            return true
        } else {
            DebugUtil.e(TAG, "shareToNoteOnlyText: failed to insert note")
            return false
        }
    }

    /**
     * 插入纯文本到便签并返回URI
     */
    private fun insertTextToNote(textContent: String): Uri? {
        val record = MediaDBUtils.queryRecordById(mediaRecordId)
        val displayName = record?.displayName ?: return null
        val title = displayName.title() ?: ""
        // 准备便签数据
        val values = ContentValues().apply {
            put(NOTE_KET_PACKAGE_NAME, activity.packageName ?: "")
            put(NOTE_KEY_TITLE, title)
            put(NOTE_KEY_CONTENT, textContent)
        }
        // 插入便签
        val insertUri = ExportNoteUtil.insertToNote(activity, values)
        if (insertUri != null) {
            DebugUtil.d(TAG, "insertTextToNote: success, uri=$insertUri")
            return insertUri
        } else {
            DebugUtil.w(TAG, "insertTextToNote: failed to insert note")
            return null
        }
    }

    /**
     * 显示导出成功的SnackBar
     * 复用ExportTipsManager的显示逻辑，确保UI一致性
     */
    private fun showExportSuccessSnackBar(insertUris: List<Uri>) {
        // 检查Activity是否有效
        if (activity.isFinishing || activity.isDestroyed) {
            DebugUtil.w(TAG, "showExportSuccessSnackBar: Activity is finishing or destroyed")
            return
        }

        val message = activity.getString(
            com.soundrecorder.common.R.string.export_store_loacl_tips,
            activity.getString(com.soundrecorder.common.R.string.install_note_name)
        )
        val actionText = activity.getString(com.soundrecorder.common.R.string.export_view_look)
        val containerView = activity.findViewById<View>(android.R.id.content)
        val lifecycle = (activity as AppCompatActivity).lifecycleScope

        mCOUISnackBar?.dismiss()
        lifecycle.launch(Dispatchers.Main) {
            containerView?.let {
                mCOUISnackBar = COUISnackBar.make(it, message, SNACKBAR_DURATION_TIME).apply {
                    (parent as? ViewGroup)?.clipChildren = false
                    setOnAction(actionText) {
                        handleViewButtonClick(insertUris)
                    }
                    show()
                }
            }
        }
    }

    /**
     * 处理"查看"按钮点击事件
     */
    private fun handleViewButtonClick(insertUris: List<Uri>) {
        if (insertUris.size == 1) {
            // 单个便签，跳转到便签详情页
            val localId = ExportNoteUtil.getLocalId(insertUris[0])
            if (localId.isNotEmpty()) {
                jumpToNoteDetail(activity, localId)
            } else {
                DebugUtil.w(TAG, "handleViewButtonClick: localId is empty")
                jumpToNoteHome(activity)
            }
        } else if (insertUris.size > 1) {
            // 多个便签，跳转到便签首页
            jumpToNoteHome(activity)
        } else {
            DebugUtil.w(TAG, "handleViewButtonClick: insertUris is empty")
        }
    }
}