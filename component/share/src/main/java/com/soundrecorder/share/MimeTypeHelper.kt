/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/7/25
 * * Author      : zxj
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.share

import android.text.TextUtils
import android.webkit.MimeTypeMap
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.share.utils.FileNameUtils
import java.util.Locale

class MimeTypeHelper {

    object MimeType {
        const val MIMETYPE_UNKNOWN = "*/*"
        const val MIMETYPE_PDF = "application/pdf"
        const val MIMETYPE_DOC = "application/msword"
        const val MIMETYPE_PPT = "application/vnd.ms-powerpoint"
        const val MIMETYPE_TXT = "text/plain"
        const val MIMETYPE_HTML = "text/html"
    }

    companion object {
        const val TAG = "MimeTypeHelper"
        const val UNKNOWN_TYPE = 0x00000001
        const val HTML_TYPE = 0x00000100
        const val TXT_TYPE = 0x00000200
        const val EBK_TYPE = 0x00000005
        const val CHM_TYPE = 0x00000006
        const val DOC_TYPE = 0x00008000
        const val DOCX_TYPE = 0x00100000
        const val PPT_TYPE = 0x00800000
        const val PPTX_TYPE = 0x01000000
        const val PDF_TYPE = 0x02000000


        private val extTypeMap = mutableMapOf<String, Int>()

        init {
            extTypeMap["doc"] = DOC_TYPE
            extTypeMap["html"] = HTML_TYPE
            extTypeMap["htm"] = HTML_TYPE
            extTypeMap["docx"] = DOCX_TYPE
            extTypeMap["ppt"] = PPT_TYPE
            extTypeMap["pptx"] = PPTX_TYPE
            extTypeMap["pdf"] = PDF_TYPE
            extTypeMap["txt"] = TXT_TYPE
        }

        @JvmStatic
        fun getTypeFromPath(path: String?): Int {
            if (null == path) {
                return UNKNOWN_TYPE
            }
            try {
                val ext = FileNameUtils.getExtension(path)
                val type = getTypeFromExtension(ext)
                return if ((type == null) || (type == UNKNOWN_TYPE)) {
                    UNKNOWN_TYPE
                } else {
                    type
                }
            } catch (e: IllegalArgumentException) {
                DebugUtil.e(TAG, "getTypeFromPath path = $path exception = ${e.message}")
                return UNKNOWN_TYPE
            }
        }

        @JvmStatic
        fun getTypeFromExtension(extension: String?): Int? {
            if (TextUtils.isEmpty(extension)) {
                return UNKNOWN_TYPE
            }
            val ext = extension?.lowercase(Locale.US)
            if (extTypeMap.containsKey(ext)) {
                return extTypeMap[ext]
            } else {
                val mimeType = getMimeTypeFromExtension(ext)
                if (mimeType != null) {
                    return getTypeByMimeType(mimeType)
                }
            }
            return UNKNOWN_TYPE
        }

        @JvmStatic
        private fun getMimeTypeFromExtension(extension: String?): String? {
            val mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(
                extension?.lowercase(Locale.US)
            )
            return if (!TextUtils.isEmpty(mimeType)) {
                mimeType
            } else {
                MimeType.MIMETYPE_UNKNOWN
            }
        }

        @JvmStatic
        fun getTypeByMimeType(mimeType: String): Int {
            return when {
                mimeType.startsWith("text/") -> TXT_TYPE
                else -> UNKNOWN_TYPE
            }
        }

        @JvmStatic
        fun getMimeTypeByFileType(type: Int): String {
            return when (type) {
                PDF_TYPE -> MimeType.MIMETYPE_PDF
                DOC_TYPE, DOCX_TYPE -> MimeType.MIMETYPE_DOC
                PPT_TYPE, PPTX_TYPE -> MimeType.MIMETYPE_PPT
                TXT_TYPE, CHM_TYPE, EBK_TYPE -> MimeType.MIMETYPE_TXT
                HTML_TYPE -> MimeType.MIMETYPE_HTML
                else -> MimeType.MIMETYPE_UNKNOWN
            }
        }
    }
}