/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/7/25
 * * Author      : zxj
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.share.utils

import com.soundrecorder.base.utils.DebugUtil
import java.io.File
import kotlin.math.max

object FileNameUtils {

    private const val TAG = "FileNameUtils"

    private const val NOT_FOUND = -1

    private const val EMPTY_STRING = ""

    private const val WINDOWS_NAME_SEPARATOR = '\\'

    private const val UNIX_NAME_SEPARATOR = '/'

    const val EXTENSION_SEPARATOR: Char = '.'

    private val SYSTEM_NAME_SEPARATOR = File.separatorChar

    private val OTHER_SEPARATOR = flipSeparator(SYSTEM_NAME_SEPARATOR)

    private val isSystemWindows: Boolean
        get() = SYSTEM_NAME_SEPARATOR == WINDOWS_NAME_SEPARATOR

    @JvmStatic
    @Throws(IllegalArgumentException::class)
    fun getExtension(fileName: String?): String? {
        if (fileName == null) {
            return null
        }
        val index = indexOfExtension(fileName)
        if (index == NOT_FOUND) {
            return EMPTY_STRING
        }
        return fileName.substring(index + 1)
    }

    @JvmStatic
    fun indexOfExtension(fileName: String?): Int {
        if (fileName == null) {
            return NOT_FOUND
        }
        if (isSystemWindows) {
            val offset = fileName.indexOf(':', getAdsCriticalOffset(fileName))
            require(offset == -1) { "NTFS ADS separator (':') in file name is forbidden." }
        }
        val extensionPos = fileName.lastIndexOf(EXTENSION_SEPARATOR)
        val lastSeparator = indexOfLastSeparator(fileName)
        return if (lastSeparator > extensionPos) NOT_FOUND else extensionPos
    }

    @JvmStatic
    fun indexOfLastSeparator(fileName: String?): Int {
        if (fileName == null) {
            return NOT_FOUND
        }
        val lastUnixPos = fileName.lastIndexOf(UNIX_NAME_SEPARATOR)
        val lastWindowsPos = fileName.lastIndexOf(WINDOWS_NAME_SEPARATOR)
        return max(lastUnixPos.toDouble(), lastWindowsPos.toDouble()).toInt()
    }

    @JvmStatic
    fun getAdsCriticalOffset(fileName: String): Int {
        val offset1 = fileName.lastIndexOf(SYSTEM_NAME_SEPARATOR)
        val offset2 = fileName.lastIndexOf(OTHER_SEPARATOR)
        if (offset1 == -1) {
            if (offset2 == -1) {
                return 0
            }
            return offset2 + 1
        }
        if (offset2 == -1) {
            return offset1 + 1
        }
        return (max(offset1.toDouble(), offset2.toDouble()) + 1).toInt()
    }

    @JvmStatic
    fun flipSeparator(ch: Char): Char {
        if (ch == UNIX_NAME_SEPARATOR) {
            return WINDOWS_NAME_SEPARATOR
        }
        if (ch == WINDOWS_NAME_SEPARATOR) {
            return UNIX_NAME_SEPARATOR
        }
        DebugUtil.e(TAG, "flipSeparator error ch=$ch")
        throw IllegalArgumentException(ch.toString())
    }
}