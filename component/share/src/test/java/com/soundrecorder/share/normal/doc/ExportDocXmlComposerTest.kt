/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/10/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.share.normal.doc

import android.text.TextUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.share.ShareTextContent
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.mockito.Mockito.mock
import org.powermock.api.mockito.PowerMockito
import org.powermock.core.classloader.annotations.PrepareForTest
import org.powermock.modules.junit4.PowerMockRunner
import org.xmlpull.v1.XmlSerializer
import java.io.FileOutputStream

@RunWith(PowerMockRunner::class)
@PrepareForTest(TextUtils::class, DebugUtil::class)
class ExportDocXmlComposerTest {

    @Before
    fun setUp() {
        PowerMockito.mockStatic(DebugUtil::class.java)
    }

    @Test
    fun should_return_startsuffix_when_start_compose_end_compose() {
        val mockFileOutputStream = mock(FileOutputStream::class.java)
        val mockXmlSerializer = mock(XmlSerializer::class.java)
        val exportDocXmlComposer = ExportDocXmlComposer(mockFileOutputStream, mockXmlSerializer)
        exportDocXmlComposer.startCompose()
        exportDocXmlComposer.endCompose()
        Mockito.verify(mockXmlSerializer, Mockito.times(1))
            ?.startDocument("UTF-8", true)
        Mockito.verify(mockXmlSerializer, Mockito.times(1))
            ?.startTag(null, "w:document")
        Mockito.verify(mockXmlSerializer, Mockito.times(1))
            ?.startTag(null, "w:body")
    }

    @Test
    fun should_return_end_when_start_compose_end_compose() {
        val mockFileOutputStream = mock(FileOutputStream::class.java)
        val mockXmlSerializer = mock(XmlSerializer::class.java)
        val exportDocXmlComposer = ExportDocXmlComposer(mockFileOutputStream, mockXmlSerializer)
        exportDocXmlComposer.startCompose()
        exportDocXmlComposer.endCompose()
        Mockito.verify(mockXmlSerializer, Mockito.times(1))
            ?.endDocument()
        Mockito.verify(mockXmlSerializer, Mockito.times(1))
            ?.endTag(null, "w:document")
        Mockito.verify(mockXmlSerializer, Mockito.times(1))
            ?.endTag(null, "w:body")
    }

    @Test
    fun should_verify_time_when_add_export_title() {
        val mockFileOutputStream = mock(FileOutputStream::class.java)
        val mockXmlSerializer = mock(XmlSerializer::class.java)
        val exportDocXmlComposer = ExportDocXmlComposer(mockFileOutputStream, mockXmlSerializer)
        exportDocXmlComposer.addExportTitle("test")
        exportDocXmlComposer.startCompose()
        exportDocXmlComposer.endCompose()
        Mockito.verify(mockXmlSerializer, Mockito.times(1))
            ?.text("test")
    }

    @Test
    fun should_verify_time_when_add_sub_title() {
        val mockFileOutputStream = mock(FileOutputStream::class.java)
        val mockXmlSerializer = mock(XmlSerializer::class.java)
        val exportDocXmlComposer = ExportDocXmlComposer(mockFileOutputStream, mockXmlSerializer)
        exportDocXmlComposer.addSubTitle("sub_title_test")
        exportDocXmlComposer.addSubTitle("mark_test", hasMark = true)
        exportDocXmlComposer.addSubTitle(
            "isShowSpeaker_test",
            hasMark = true,
            isShowSpeaker = false
        )
        exportDocXmlComposer.startCompose()
        exportDocXmlComposer.endCompose()
        Mockito.verify(mockXmlSerializer, Mockito.times(1))
            ?.text("sub_title_test")
        Mockito.verify(mockXmlSerializer, Mockito.times(2))
            ?.startTag(null, "w:bookmarkStart")
        Mockito.verify(mockXmlSerializer, Mockito.times(1))
            ?.startTag(null, "w:vanish")
    }

    @Test
    fun should_verify_time_when_add_convert_vad() {
        val mockFileOutputStream = mock(FileOutputStream::class.java)
        val mockXmlSerializer = mock(XmlSerializer::class.java)
        val convertVad = ConvertContentItem(roleName = "test_name")
        val exportDocXmlComposer = ExportDocXmlComposer(mockFileOutputStream, mockXmlSerializer)
        exportDocXmlComposer.addConvertVad(convertVad, LinkedHashMap())
        exportDocXmlComposer.addConvertVad(convertVad, LinkedHashMap(), isShowSpeaker = false)
        exportDocXmlComposer.startCompose()
        exportDocXmlComposer.endCompose()
        Mockito.verify(mockXmlSerializer, Mockito.times(2))
            ?.text("test_name  ")
        Mockito.verify(mockXmlSerializer, Mockito.times(2))
            ?.startTag(null, "w:vanish")
    }
}