/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SellModeScreenStateLiveDataTest
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/5/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.sellmode

import android.os.Build
import androidx.fragment.app.FragmentActivity
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.sellmode.shadow.ShadowFeatureOption
import com.soundrecorder.sellmode.shadow.ShadowRecorderLogger
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowRecorderLogger::class])
class SellModeScreenStateLiveDataTest {

    private var controller: ActivityController<FragmentActivity>? = null

    @Before
    fun init() {
        controller = Robolectric.buildActivity(FragmentActivity::class.java)
    }

    @After
    fun tearDown() {
        controller = null
    }

    @Test
    fun should_success_when_init() {
        val mockedFeatureOption = Mockito.mockStatic(FeatureOption::class.java)
        Mockito.`when`(FeatureOption.isHasSellMode()).thenReturn(true)
        controller?.create()
        val liveData = SellModeScreenStateLiveData(controller!!.get(), null)
        controller?.resume()
        Assert.assertTrue(liveData.hasObservers())
        controller?.pause()?.stop()?.destroy()
        Assert.assertFalse(liveData.hasObservers())
        mockedFeatureOption.close()
    }
}