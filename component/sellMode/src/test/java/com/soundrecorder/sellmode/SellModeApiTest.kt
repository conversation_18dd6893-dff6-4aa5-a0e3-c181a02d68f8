/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SellModeApiTest
 * Description:
 * Version: 1.0
 * Date: 2023/12/20
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/12/20 1.0 create
 */

package com.soundrecorder.sellmode

import android.content.Context
import android.os.Build
import androidx.fragment.app.FragmentActivity
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.sellmode.SellModeService.Companion.checkAndStartSellModeService
import com.soundrecorder.sellmode.shadow.ShadowFeatureOption
import com.soundrecorder.sellmode.shadow.ShadowOplusCompactUtil
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.robolectric.Robolectric
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowFeatureOption::class, ShadowOplusCompactUtil::class])
class SellModeApiTest {
    private var controller: ActivityController<FragmentActivity>? = null

    @Before
    fun init() {
        controller = Robolectric.buildActivity(FragmentActivity::class.java)
    }

    @After
    fun tearDown() {
        controller = null
    }

    @Test
    fun should_correct_when_checkAndStartSellModeService() {
        val mockContext = Mockito.mock(Context::class.java)
        SellModeApi.checkAndStartSellModeService(mockContext)
        Mockito.verify(mockContext, Mockito.times(1)).checkAndStartSellModeService()
    }

    @Test
    fun should_correct_when_createSellModeScreenStateListener() {
        val controller = controller ?: return
        val mockedFeatureOption = Mockito.mockStatic(FeatureOption::class.java)
        Mockito.`when`(FeatureOption.isHasSellMode()).thenReturn(true)
        controller.create()
        SellModeApi.createSellModeScreenStateListener(controller.get(), null)

        mockedFeatureOption.close()
    }
}