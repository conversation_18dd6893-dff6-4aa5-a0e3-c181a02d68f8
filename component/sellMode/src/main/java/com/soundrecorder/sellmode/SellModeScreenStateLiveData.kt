/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SellModeScreenStateLiveData
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.sellmode

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.Observer
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.screenstate.ScreenStateLiveData
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FeatureOption
import com.soundrecorder.modulerouter.sellmode.SellModeInterface
import com.soundrecorder.modulerouter.utils.Injector

class SellModeScreenStateLiveData(
    lifecycleOwner: LifecycleOwner,
    private var screenChangedCheck: (() -> Boolean)?
) : ScreenStateLiveData(), DefaultLifecycleObserver {

    companion object {
        const val TAG = "SellModeScreenStateLiveData"
    }

    private val sellModeApi by lazy {
        Injector.injectFactory<SellModeInterface>()
    }

    private var screenStateObserver = Observer<Boolean> {
        DebugUtil.d(TAG, "screenStateObserver: screenOn = $it")
        if (!it && screenChangedCheck?.invoke() != false) {
            //息屏并且所有文件管理权限没有展示的时候才请求数据预置
            sellModeApi?.checkAndStartSellModeService(BaseApplication.getAppContext())
        }
    }

    init {
        DebugUtil.i(TAG, "init")
        if (FeatureOption.isHasSellMode()) {
            lifecycleOwner.lifecycle.addObserver(this)
        }
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        DebugUtil.i(TAG, "onCreate")
        this.observeForever(screenStateObserver)
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        DebugUtil.i(TAG, "onDestroy")
        screenChangedCheck = null
        owner.lifecycle.removeObserver(this)
        this.removeObserver(screenStateObserver)
    }
}