/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RealtimeOffsetManager
 * Description:
 * Version: 1.0
 * Date: 2025/5/12
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/5/12 1.0 create
 */

package com.soundrecorder.translate.asr

import android.os.SystemClock
import com.soundrecorder.base.utils.DebugUtil
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 记录ASR每一段开始、暂停时间，计算偏移量
 * 开始ASR-停止ASR，为A段
 * 再次开始ASR-再次停止ASR，为B段...
 * B段ASR服务端是从0开始算的，服务端返回time需要加上A段的时长。
 * */
class RealtimeOffsetManager {
    private val logTag = "RealtimeOffsetManager"
    private val channelIdList: CopyOnWriteArrayList<String> = CopyOnWriteArrayList()
    private val offsetMap: ConcurrentHashMap<String, TimeOffset> = ConcurrentHashMap()
    private val cacheOffsetSumeMap: ConcurrentHashMap<String, Long> = ConcurrentHashMap()

    /**
     * 发送空包造成的时间偏移量
     */
    var emptyOffsetTime: Long = 0

    /**
     * ASR异常停止持续的时间
     */
    var abnormalStopASRSpendTime: Long = 0

    /**
     * 主录音已经录制的时间
     * 对应场景：ASR断开后，时间会从0开始，计算偏移量的时候使用主录音的当前录制时间去计算
     */
    var mainRecordingTime: Long = 0

    /**
     * 记录channelId片段的开始时间
     */
    fun recordStartTime(channelId: String, offset: Int = 0) {
        if (!channelIdList.contains(channelId)) {
            channelIdList.add(channelId)
        }
        if (!offsetMap.containsKey(channelId)) {
            offsetMap[channelId] = TimeOffset(channelId).apply {
                startTime = SystemClock.elapsedRealtime() + offset
            }
        }
    }

    /**
     * 记录channelId片段的结束时间
     */
    fun recordStopTime(channelId: String, offset: Long = 0L) {
        DebugUtil.i(logTag, "recordStopTime channelId=$channelId, offset=$offset")
        if (offsetMap.containsKey(channelId)) {
            offsetMap[channelId]?.endTime = SystemClock.elapsedRealtime() + offset
        } else {
            DebugUtil.w(logTag, "recordStopTime no channelId $channelId")
        }
    }

    /**
     * 获取channelId片段前n个片段总时长
     */
    fun getAudioTimeDuration(channelId: String?): Long {
        if (channelId.isNullOrEmpty()) {
            return 0L
        }
        if (cacheOffsetSumeMap.containsKey(channelId)) {
            return cacheOffsetSumeMap[channelId] ?: 0
        }
        var offset = 0L
        channelIdList.forEach { id ->
            if (id == channelId) {
                // 到当前片段结束，当前片段时长不包含
                return@forEach
            }
            offsetMap[id]?.let {
                offset += it.getOffset()
            }
            cacheOffsetSumeMap[channelId] = offset
        }
        DebugUtil.i(logTag, "getAudioTimeDuration $offset,channelId=$channelId")
        return offset
    }

    fun clear() {
        cacheOffsetSumeMap.clear()
        channelIdList.clear()
        offsetMap.clear()
    }

    inner class TimeOffset(private val channelId: String) {
        var startTime: Long = 0
        var endTime: Long = 0
        private var calOffset: Long? = null

        fun getOffset(): Long {
            if (calOffset != null) {
                return calOffset!!
            }
            if (startTime <= 0 || endTime <= 0) {
                DebugUtil.w(logTag, "getOffset illegal-$channelId start=$startTime, end= $endTime")
                calOffset = 0
                return 0
            }
            calOffset = endTime - startTime
            if (calOffset!! < 0) {
                DebugUtil.w(logTag, "getOffset calOffset illegal-$channelId start=$startTime, end= $endTime")
                calOffset = 0
            }
            return calOffset!!
        }
    }
}