/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: AsrResult
 * Description:
 * Version: 1.0
 * Date: 2025/5/30
 * Author: W9085798
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9085798 2025/5/30 1.0 create
 */
package com.soundrecorder.translate.asr.bean

data class AsrResult(
    val msgId: String? = null,
    val type: String,
    val startOffset: Int,
    val endOffset: Int,
    val text: String,
    val speakId: Int? = null,
)
