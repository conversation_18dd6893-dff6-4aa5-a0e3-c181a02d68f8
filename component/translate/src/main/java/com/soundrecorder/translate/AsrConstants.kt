/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  Constants.java
 * * Description :
 * * Version     : 1.0
 * * Date        : 019/09/25
 * * Author      : xuruoyu
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
</desc></version></data></author> */
package com.soundrecorder.translate

@Suppress("MagicNumber")
object AsrConstants {
    /*区分不同业务*/
    const val APP_TAG = "NewSoundRecorder"

    /*同一业务，是否又有不同场景调用*/
    const val SUB_APP_TAG = ""

    /*AIUNIT包名*/
    const val PKG_NAME_AIUNIT = "com.oplus.aiunit"


    const val STATUS_NO_NETWORK = -1000

    enum class Status(var code: Int, var message: String) {
        SUCCESS(0, "SUCCESS"),

        FAILED(-1, "FAILED"),

        NO_NETWORK(STATUS_NO_NETWORK, "no network"),

        SILENCE_TIMEOUT(100, "SILENCE TIMEOUT"),

        WS_CONNECT_ERROR(-101, "CONNECT ERROR"),

        SERVER_ERROR(-102, "SERVER ERROR"),


        FAIL_START_RECONGNIZE(-103, "FAIL START RECONGNIZE"),

        WS_ONMESSAGE_ERROR(-104, "ONMESSAGE ERROR"),

        NOT_SUPPORT_LANGUAGE(-105, "NOT SUPPORT SDK_LANGUAGE"),

        WS_CONNECT_CLOSED(106, "CONNECT CLOSED"),

        WS_RECONNECT(107, "RECONNECT"),

        GET_SUPPORT_LANGUAGE_ERROR(-108, "GET SUPPORT LANGUAGE ERROR"),

        ASR_NOT_SUPPORT(-200, "ASR NOT SUPPORT ERROR"),

        ASR_INIT_ERROR(-201, "ASR INIT ERROR"),

        ASR_CONNECT_ERROR(-202, "ASR CONNECT ERROR"),

        ASR_CONNECT_PUSH_COMPLETE(-203, "ASR PUSH COMPLETE"),

        ASR_CONNECT_TASK_COMPLETE(-204, "ASR TASK COMPLETE"),

        ASR_ENCODE_IO_NO_SPACE(-205, "AUDIO SAVED NO SPACE"),

        ASR_ENCODE_EXCEPTION(-206, "AUDIO ENCODE EXCEPTION"),

        ASR_ERROR_OTHER(-207, "ASR ERROR"),

        ASR_ABANDON_CACHE_DATA(-208, "ASR ABANDON CACHE DATA"),

        ASR_TASK_NET_ERROR(-209, "ASR NET ERROR"),

        ASR_TASK_NET_TIMEOUT(-210, "ASR NET TIMEOUT"),

        ASR_INIT_SUCCESS(-211, "ASR INIT SUCCESS"),

        ASR_CREATE_RECORDER_ERROR(-212, "ASR CREATE RECORDER ERROR"),

        ASR_PARAM_INVALID(201, "ASR PARAM ERROR"),

        ASR_DATA_ERROR(202, "ASR RECOGNIZE DATA ERROR"),

        ASR_EMPTY_VOICE(203, "ASR NO VOICE ERROR"),

        ASR_VOICE_SESSION_IS_NULL(204, "ASR VOICE SESSION ERROR"),

        ASR_SESSION_IS_NULL(205, "ASR SESSION NULL ERROR"),

        ASR_CHANNEL_ALREADY_CLOSED(206, "ASR CHANNEL CLOSE ERROR"),

        ASR_RECORD_SESSION_IS_NULL(207, "ASR RECORD SESSION NULL"),

        ASR_AUTH_FAIL(208, "ASR AUTH ERROR"),

        ASR_NETTY_SERVER_EXCEPTION(209, "ASR NETTY SERVER EXCEPTION ERROR"),

        AIUNIT_DISCONNECT(210, "AIUNIT DISCONNECT"),

        ASR_OFFLINE_HEART_BEAT(211, "ASR READ FILE HEART BEAT"),

        ASR_READ_FILE_UPLOAD_FAIL(212, "file upload fail"),

        ASR_READ_FILE_UPLOAD_LOADING(213, "file uploading"),

        ASR_READ_FILE_UPLOAD_SUCCESS(214, "file upload success"),

        ASR_READ_FILE_QUERY_LOADING(218, "file asr query loading"),

        ASR_READ_SUMMARY_CONSUME_COMPLETE(215, "summary consume complete"),

        ASR_READ_SUMMARY_CONSUME_QUERY(216, "summary consume query"),

        ASR_READ_FILE_QUERY_FAIL(219, "file asr query fail"),

        ASR_READ_FILE_QUERY_COMPLETE(220, "file asr query complete"),

        ASR_READ_FILE_FORMAT_UNSUPPORT(221, "file asr format unsupport"),

        ASR_READ_FILE_NET_STATUS_NO_NET(222, "file asr not net"),

        ASR_READ_FILE_NET_STATUS_HAS_NET(223, "file asr has net"),

        ASR_READ_FILE_NO_NET_UPLOAD(224, "file asr not net upload"),

        ASR_READ_FILE_NO_NET_RETRY(225, "file asr not net retry"),

        ASR_READ_FILE_NO_NET_SUMMARY_CONSUME_COUNT(226, "file asr not net summary consume count"),

        ASR_READ_FILE_NO_NET_SUMMARY_QUERY(227, "file asr not net summary query"),

        ASR_READ_FILE_NO_NET_QUERY_ASR(228, "file asr not net query asr"),

        ASR_READ_SUMMARY_STOP(217, "summary stop"),

        ASR_NO_RECOGNIZE_LANGUAGE(229, "file no recognize language"),

        ASR_NOT_SUPPORT_LANGUAGE(230, "file not support language"),

        ASR_LANGUAGE_EMPTY_TEXT(231, "language empty text"),

        /* 实时asr */
        ASR_ERROR_SERVER_UNKNOWN(232, "asr server unknown"),

        ASR_ALGO_NOT_READY(233, "asr algo not ready"),

        ASR_REQ_TIMEOUT(234, "asr request timeout"),

        ASR_INVALID_ALGO(235, "asr invalid algo"),

        ASR_ERROR_REQ_POST(236, "asr error request post"),

        ASR_ERROR_ALGO_INTERNAL(237, "asr error algo internal"),

        ASR_ERROR_INVALID_TASK(238, "asr error invalid task"),

        ASR_ERROR_PROCESS_RESP(239, "asr error process resp"),

        ASR_ERROR_DECRYPT_REQ(240, "asr error decrypt req"),

        ASR_ERROR_ENCRYPT_ACK(241, "asr error encrypt ack"),

        ASR_ERROR_CONTENT_CHECK(242, "asr error content check"),

        ASR_ERROR_CONTENT_SAFE_CHECK(243, "asr error content safe check"),

        ASR_ERROR_DATA_UPLOAD(244, "asr error data upload"),

        ASR_ERROR_DECRYPT_ACK(245, "asr error decrypt ack"),

        ASR_ERROR_PROCESS(246, "asr error process"),

        ASR_ERROR_WAIT_FINAL_TIMEOUT(247, "asr error wait final timeout"),

        ASR_ERROR_NOT_INIT(248, "asr error not init"),

        ASR_ERROR_NET_FAILURE(249, "asr net failure"),

        ASR_NET_CONNECT(250, "asr net connect "),

        ASR_VOIP_FOCUS_LOST_ERROR(251, "ASR VOIP FOCUS LOST"),

        ASR_SERVICE_END_TASK(252, "asr service end task"),

        ASR_PLUGIN_ERROR_NO_INTERNET(253, "aiunit error no internet"),

        ERROR_INVALID_START_MULTI(254, "ERROR INVALID START MULTI"),

        ERROR_INVALID_START_LATE(255, "ERROR INVALID START LATE"),

        SUMMARY_ERROR_OTHER(-301, "SUMMARY ERROR"),

        SUMMARY_ERROR_STREAM_PARSE(-302, "SUMMARY ERROR STREAM PARSE"),

        SUMMARY_PARAM_INVALID(301, "SUMMARY PARAM INVALID"),

        SUMMARY_AUTH_FAIL(302, "SUMMARY AUTH ERROR"),

        SUMMARY_SUMMARIZE_DATA_ERROR(303, "SUMMARY SUMMARIZE DATA ERROR"),

        SUMMARY_SERVER_ERROR(304, "SUMMARY SERVER ERROR"),

        SUMMARY_TEXT_LIMIT_ERROR(305, "SUMMARY TEXT LIMIT ERROR"),

        SUMMARY_CLIENT_ERROR(306, "SUMMARY CLIENT ERROR"),

        SUMMARY_NET_TIMEOUT_ERROR(307, "SUMMARY NET TIMEOUT ERROR"),

        SUMMARY_CLIENT_NET_ERROR(308, "SUMMARY CLIENT NET ERROR"),

        SUMMARY_NO_DATA_ERROR(309, "SUMMARY NO DATA ERROR"),

        SUMMARY_ABORT_STREAM(310, "SUMMARY ABORT STREAM"),

        SUMMARY_CONTENT_SAFETY(311, "SUMMARY CONTENT SAFETY"),

        SUMMARY_TEXT_LIMIT_ERROR_HTTP(312, "SUMMARY TEXT LIMIT ERROR FROM HTTP"),

        SUMMARY_LOW_MEMORY_ERROR(313, "SUMMARY LOW MEMORY"),

        SUMMARY_LOW_BATTERY_ERROR(314, "SUMMARY LOW BATTERY"),

        SUMMARY_POWER_SAVE_MODEL_ERROR(315, "SUMMARY POWER SAVE MODEL"),

        SUMMARY_OVERLOAD_ERROR(316, "SUMMARY OVERLOAD"),

        SUMMARY_HIGH_TEMPERATURE(317, "SUMMARY HIGH TEMPERATURE"),

        SUMMARY_NO_RECOGNIZE_LANGUAGE(318, "SUMMARY NO RECOGNIZE LANGUAGE"),

        SUMMARY_NOT_SUPPORT_LANGUAGE(319, "SUMMARY NOT SUPPORT LANGUAGE"),

        SUMMARY_CONTENT_SAFETY_EXPORT(320, "SUMMARY CONTENT SAFETY EXPORT"),

        /**
         * 顺滑接口
         */
        SMOOTH_PARAM_DATA_EMPTY(-400, "SMOOTH PARAM DATA EMPTY"),

        SMOOTH_ERROR_OTHER(-401, "SMOOTH ERROR"),

        SMOOTH_PARAM_PARSE_ERROR(400, "SMOOTH PARAM PARSE ERROR"),

        /**
         * 实体组合接口
         */
        ENTITY_COMBINE_ERROR_OTHER(-501, "ENTITY COMBINE ERROR"),

        ENTITY_COMBINE_PARAM_INVALID(500, "ENTITY COMBINE PARAM INVALID"),

        ENTITY_COMBINE_EXTRACT_ERROR(501, "ENTITY COMBINE EXTRACT ERROR"),

        ENTITY_COMBINE_INVOKE_THIRD_ERROR(502, "ENTITY COMBINE INVOKE THIRD ERROR"),

        ENTITY_COMBINE_NET_TIMEOUT_ERROR(503, "ENTITY_COMBINE NET TIMEOUT ERROR"),

        ENTITY_COMBINE_CLIENT_NET_ERROR(504, "ENTITY_COMBINE CLIENT NET ERROR"),

        TTS_NETWORK_ERROR(601, "TTS NETWORK ERROR"),

        TTS_IO_EXCEPTION(-602, "TTS IO EXCEPTION"),

        TTS_PARAMS_TEXT_EMPTY_ERROR(-603, "TTS TEXT EMPTY"),

        TTS_PARAMS_TEXT_OVER_TIME_ERROR(-604, "TTS OVERTIME"),

        TTS_PARAMS_TEXT_INTERRUPT_ERROR(-605, "TTS INTERRUPT"),

        TTS_PARAMS_TEXT_OPUS_CODEC_LOAD_ERROR(-606, "TTS OPUS CODEC LOAD FAIL")
    }
}
