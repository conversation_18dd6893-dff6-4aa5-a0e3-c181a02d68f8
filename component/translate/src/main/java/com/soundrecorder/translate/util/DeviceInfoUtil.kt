/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: DeviceInfoUtil
 * Description:
 * Version: 1.0
 * Date: 2025/3/18
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2025/3/18 1.0 create
 */

package com.soundrecorder.translate.util

import android.os.Build
import com.soundrecorder.base.utils.OS12FeatureUtil
import com.soundrecorder.base.utils.OSDKCompatUtils

object DeviceInfoUtil {
    private const val TAG = "DeviceInfoUtil"
    private const val PERSIST_OPPO_REGION = "persist.sys.oppo.region"
    private const val PERSIST_OPLUS_REGION = "persist.sys.oplus.region"
    private var deviceModel: String? = null
    private var region: String? = null

    @JvmStatic
    fun getDeviceModel(): String {
        if (!deviceModel.isNullOrBlank()) {
            return deviceModel ?: ""
        }

        return Build.MODEL.apply {
            deviceModel = this
        }
    }

    @JvmStatic
    fun getRegion(): String {
        var region = this.region
        if (region?.isNotEmpty() == true) {
            return region
        }
        region = if (OS12FeatureUtil.isColorOS11Point3OrLater()) {
            OSDKCompatUtils.getFeatureProperty(PERSIST_OPLUS_REGION, "CN")
        } else {
            OSDKCompatUtils.getFeatureProperty(PERSIST_OPPO_REGION, "CN")
        }
        this.region = region
        return region
    }
}