/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - DatePredictor
 ** Description:
 **         v1.0:   Handling of dates in the schedule
 **
 ** Version: 1.0
 ** Date: 2023/04/21
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/4/21   1.0      Create this module
 ** <EMAIL>       2023/8/9   1.1   Fuzzy dates are no longer supported
 ********************************************************************************/
package com.oplus.soundrecorder.semantic.ssa.timexparser

import androidx.annotation.VisibleForTesting
import com.soundrecorder.base.utils.DebugUtil
import java.util.Calendar

data class DatePredictor(val y: Int, val m: Int, val d: Int) {

    var isUncertainDate = false
        private set

    class Builder {
        @get:VisibleForTesting
        var originalYear: String = ""
        @get:VisibleForTesting
        var originalMonth: String = ""
        @get:VisibleForTesting
        var originalDay: String = ""

        fun setOriginDate(year: String, month: String, day: String): Builder {
            this.originalYear = year
            this.originalMonth = month
            this.originalDay = day
            return this
        }

        @VisibleForTesting
        fun isFuzzyDate(): Boolean {
            return originalYear.isUncertainDate() || originalDay.isUncertainDate() || originalMonth.isUncertainDate()
        }


        /**
         * 识别规则fix 2023-08-09
         * 不在对年月日不清晰的日期做推断；
         */
        fun build(): DatePredictor? {
            val predictor = kotlin.runCatching {
                when {
                    (originalYear.isUncertainDate().not()
                    && originalMonth.isUncertainDate().not()
                    && originalDay.isUncertainDate().not()) -> {
                        DatePredictor(originalYear.toInt(), originalMonth.toInt() - 1, originalDay.toInt())
                    }

                    else -> {
                        DebugUtil.d(TAG, "not support when $originalYear-$originalMonth-$originalDay")
                        null
                    }
                }
            }.onFailure {
                DebugUtil.e(TAG, "build date error:${it.message}")
            }.getOrDefault(null)

            return predictor?.apply { isUncertainDate = isFuzzyDate() }
        }
    }

    override fun toString(): String {
        return "$y:$m:$d"
    }

    fun toDate(): Calendar {
        return Calendar.getInstance().apply {
            set(y, m, d)
        }
    }

    companion object {
        const val TAG = "DatePredictor"
    }
}

/**
 * Might be 2023-XX-XX or XXXX-04-XX OR 2023-xx-09
 */
fun String.isUncertainDate(): Boolean {
    return this.all { it == 'X' }
}