/*********************************************************************************
 ** Copyright (C), 2008-2023, Oplus, All rights reserved.
 **
 ** File: - HightRange
 ** Description:
 ** v1.0:   parse time in text
 **
 ** Version: 1.0
 ** Date: 2023/02/27
 ** Author: <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                 <date>       <version>      <desc>
 ** ------------------------------------------------------------------------------
 ** <PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot       2023/2/27   1.0      Create this module
 ********************************************************************************/
package com.oplus.soundrecorder.semantic.ssa.base

import androidx.annotation.Keep

//字符高亮范围
@Keep
data class HightRange(var start: Int, var end: Int) {
    fun startPlus(number: Int) {
        start += number
    }
    fun endPlus(number: Int) {
        end += number
    }
}