<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.recorder.move">

    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />

    <application>
        <service
            android:name=".RecorderBRPluginService"
            android:exported="true"
            android:permission="oppo.permission.OPPO_COMPONENT_SAFE">
            <meta-data
                android:name="uniqueID"
                android:value="1040"></meta-data>

            <meta-data
                android:name="version"
                android:value="1"></meta-data>

            <!--alm会报资源找不到，把这块挪动到主module下的androidmanifest中去了-->
           <!-- <meta-data
                android:name="backup_name_resId"
                android:value="@string/app_name_main"></meta-data>
                -->


            <meta-data
                android:name="isColorOSSupport"
                android:value="true"></meta-data>

            <intent-filter>
                <action android:name="com.coloros.br.service" />
                <action android:name="com.heytap.br.service" />

                <category android:name="android.intent.category.default" />
            </intent-filter>

        </service>
    </application>


</manifest>