/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  -
 * * Description :  分组表搬家解析相关类
 * * Version     : 1.0
 * * Date        : 2025/02/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.recorder.move

import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.databean.GroupInfo
import org.xmlpull.v1.XmlPullParser
import org.xmlpull.v1.XmlPullParserFactory
import java.io.StringReader
import java.lang.Exception

class GroupInfoXmlParser {
    companion object {
        const val TAG = "GroupInfoXmlParser"
    }

    private fun parseGroupInfo(parser: XmlPullParser, groupInfo: GroupInfo, tagName: String) {
        if (tagName == DatabaseConstant.ROOT) {
            val attrNum = parser.attributeCount
            var i = 0
            while (i < attrNum) {
                val name = parser.getAttributeName(i)
                val value = parser.getAttributeValue(i)
                when (name) {
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ID -> groupInfo.mId = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_UUID -> groupInfo.mUuId = value
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_NAME -> groupInfo.mGroupName = value
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_SORT -> groupInfo.mGroupSort = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_COLOR -> groupInfo.mGroupColor = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_COUNT -> groupInfo.mGroupCount = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GLOBAL_ID -> groupInfo.mGroupGlobalId = value
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DIRTY -> groupInfo.mDirty = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_DELETED -> groupInfo.mIsDeleted = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_IS_PRIVATE -> groupInfo.mIsPrivate = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_CLOUD_SYS_VERSION -> groupInfo.sysVersion = value.toLong()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_GROUP_TYPE -> groupInfo.mGroupType = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_TYPE -> groupInfo.syncType = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS -> groupInfo.syncUploadStatus = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_DOWNLOAD_STATUS -> groupInfo.syncDownloadStatus = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_ERROR_CODE -> groupInfo.errorCode = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_LOCAL_EDIT_STATUS -> groupInfo.editStatus = value.toInt()
                    DatabaseConstant.GroupInfoColumn.COLUMN_NAME_SYNC_DATE -> groupInfo.syncDate = value.toLong()
                }
                ++i
            }
        }
    }
    @Suppress("TooGenericExceptionCaught")
    fun parse(xmlString: String?): HashSet<GroupInfo>? {
        var groupInfo: GroupInfo? = null
        val list = HashSet<GroupInfo>()
        try {
            val factory = XmlPullParserFactory.newInstance()
            val parser = factory.newPullParser()
            parser.setInput(StringReader(xmlString))
            var eventType = parser.eventType
            var tagName = ""
            while (eventType != XmlPullParser.END_DOCUMENT) {
                when (eventType) {
                    XmlPullParser.START_DOCUMENT -> { }
                    XmlPullParser.START_TAG -> {
                        groupInfo = GroupInfo()
                        tagName = parser.name
                        parseGroupInfo(parser, groupInfo, tagName)
                    }
                    XmlPullParser.END_TAG -> {
                        if (parser.name == DatabaseConstant.ROOT && groupInfo != null) {
                            list.add(groupInfo)
                            DebugUtil.i(TAG, "parsed GroupInfo: $groupInfo")
                        }
                    }
                    else -> {
                    }
                }
                eventType = parser.next()
            }
        } catch (e: Exception) {
            DebugUtil.e(TAG, "GroupInfoXmlParser parse error", e)
        }
        return list
    }
}