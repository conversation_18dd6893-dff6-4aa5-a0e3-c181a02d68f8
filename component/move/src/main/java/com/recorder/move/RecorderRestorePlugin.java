/**********************************************************
 * Copyright 2010-2017 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description : RecorderRestorePlugin
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-3-12, chenlipeng ********, create
 ***********************************************************/

// OPLUS Java File Skip Rule:MethodLength
package com.recorder.move;

import static com.recorder.move.RecorderBackupPlugin.AMP_FOLDER;
import static com.recorder.move.RecorderBackupPlugin.CONVERT_FOLDER;
import static com.recorder.move.RecorderBackupPlugin.GROUP_INFO_BACKUP_XML;
import static com.recorder.move.RecorderBackupPlugin.UPLOAD_BACKUP_XML;
import static com.soundrecorder.common.constant.RecordConstant.RECORD_DIRTY_MEGA_ONLY;
import static com.soundrecorder.modulerouter.cloudkit.CloudKitInterfaceKt.SYNC_TYPE_RECOVERY_START_APP;

import android.content.ContentProviderOperation;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.os.Bundle;
import android.os.Environment;
import android.os.ParcelFileDescriptor;
import android.text.TextUtils;
import android.util.Pair;

import com.heytap.backup.sdk.common.host.BREngineConfig;
import com.heytap.backup.sdk.component.BRPluginHandler;
import com.heytap.backup.sdk.component.plugin.RestorePlugin;
import com.heytap.backup.sdk.host.listener.ProgressHelper;
import com.recorder.move.spvalue.SharedPreferenceComposer;
import com.recorder.move.spvalue.SharedPreferenceParser;
import com.recorder.move.spvalue.SmartSharedPreferenceParser;
import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.base.utils.MediaDataScanner;
import com.soundrecorder.common.constant.Constants;
import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.databean.ConvertRecord;
import com.soundrecorder.common.databean.GroupInfo;
import com.soundrecorder.common.databean.KeyWord;
import com.soundrecorder.common.databean.Record;
import com.soundrecorder.common.databean.UploadRecord;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.db.ConvertDeleteUtil;
import com.soundrecorder.common.db.GroupInfoDbUtil;
import com.soundrecorder.common.db.KeyWordDbUtils;
import com.soundrecorder.common.db.MediaDBUtils;
import com.soundrecorder.common.db.PictureMarkDbUtils;
import com.soundrecorder.common.db.RecorderDBUtil;
import com.soundrecorder.common.db.UploadDbUtil;
import com.soundrecorder.common.permission.PermissionUtils;
import com.soundrecorder.common.sync.RecordDataSyncHelper;
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface;
import com.soundrecorder.modulerouter.utils.KoinInterfaceHelper;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileDescriptor;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import kotlin.Unit;

/**
 * Created by ******** on 2019/5/5.
 */

public class RecorderRestorePlugin extends RestorePlugin {
    public static final String LOCAL_ACTION_MOVE_RESTORE_COMPLETED = "action.move.restore.completed";
    public static final String RECORDER_FOLDER = "Recorder";
    public static final String RECORDER_BACKUP_XML = "recorder_backup.xml";
    public static final String CONVERT_RECORD_BACKUP_XML = "convert_record_backup.xml";
    public static final String PICTURE_MARK_XML = "picture_mark.xml";
    public static final String KEY_WORD_XML = "key_word.xml";
    private static final int RECORD_NEED_INSERT = 0;
    private static final int RECORD_NEED_UPDATE = 1;
    private static final int RECORD_NEED_CANCEL = 2;
    private static final String TAG = "RecorderRestorePlugin";
    private static final int DEFAULT_BUFFER_SIZE = 512;
    private static final int TIME_OUT = 8000;

    private final Object mWaitObject = new Object();
    private final CloudKitInterface mCloudKitApi = KoinInterfaceHelper.INSTANCE.getCloudKitApi();
    private ArrayList<Record> mRecordList;
    private ArrayList<ContentProviderOperation> mRecordOps;
    private List<Record> mLocalRecordList;
    private Map<String, Record> mLocalRecordRelativePathMap;
    private Map<String, Record> mLocalRecordUuidMap;
    private HashSet<String> mMediaPath = null;
    private List<String> mAmpFilePath = new ArrayList<>();
    private List<String> mConvertTextFilePath = new ArrayList<>();
    private List<String> mConvertOnlyIds = new ArrayList<>();
    private List<String> mGroupInfoUuids = new ArrayList<>();
    private List<String> mPictureMarkPath = new ArrayList<>();
    private List<MarkDataBean> mLocalMarkList;
    private ArrayList<ContentProviderOperation> mConvertRecordOps;
    private ArrayList<ContentProviderOperation> mUpdateMediaRecordIdOps;
    private ArrayList<ContentProviderOperation> mUpdateMediaMarkDataBeanIdOps;
    // Pair<老的recordId,新的recordId>
    private final List<Pair<Long, Long>> mUpdateKeyWordRecordIds = new ArrayList<>();
    // key 为录音文件路径 , value为老的mediaId
    private final Map<String, Long> mMediaMap = new HashMap<>();

    private Context mContext;
    private String mRecorderRestorePath;
    private String mRecorderRestoreXmlPath;
    private String mConvertRecordRestoreXmlPath;
    private String mUploadRecordRestoreXmlPath;
    private String mGroupInfoRestoreXmlPath;
    private String mPictureMarkRestoreXmlPath;
    private String mKeyWordRestoreXmlPath;
    private String mSharedPreferenceXmlPath;

    private String mSmartSharedPreferenceXmlPath;
    private int mCompletedCount = 0;
    private int mMaxCount = 0;
    private boolean mIsCancel;
    private BREngineConfig mBREngineConfig;
    private BRPluginHandler mBRPluginHandler;
    private volatile boolean mScanFinished = false;


    @Override
    public Bundle onPreview(Bundle bundle) {
        return null;
    }

    @Override
    public void onCreate(Context context, BRPluginHandler brPluginHandler, BREngineConfig config) {
        super.onCreate(context, brPluginHandler, config);
        mContext = context;
        mIsCancel = false;
        mBRPluginHandler = brPluginHandler;
        mBREngineConfig = config;
        DebugUtil.d(TAG, "onCreate");
    }

    @Override
    public Bundle onPrepare(Bundle bundle) {
        mRecorderRestorePath = mBREngineConfig.getRestoreRootPath() + File.separator + RECORDER_FOLDER;
        mRecorderRestoreXmlPath = mRecorderRestorePath + File.separator + RECORDER_BACKUP_XML;
        mConvertRecordRestoreXmlPath = mRecorderRestorePath + File.separator + CONVERT_RECORD_BACKUP_XML;
        mUploadRecordRestoreXmlPath = mRecorderRestorePath + File.separator + UPLOAD_BACKUP_XML;
        mGroupInfoRestoreXmlPath = mRecorderRestorePath + File.separator + GROUP_INFO_BACKUP_XML;
        mPictureMarkRestoreXmlPath = mRecorderRestorePath + File.separator + PICTURE_MARK_XML;
        mKeyWordRestoreXmlPath = mRecorderRestorePath + File.separator + KEY_WORD_XML;
        mSharedPreferenceXmlPath = mRecorderRestorePath + File.separator + SharedPreferenceComposer.SHARED_PREFERENCE_XML_NAME;
        mSmartSharedPreferenceXmlPath = mRecorderRestorePath + File.separator + MoveUtils.getSmartXmlName();

        String content = getXmlInfo(mRecorderRestoreXmlPath);
        mRecordOps = new ArrayList<>();
        mConvertRecordOps = new ArrayList<>();
        mUpdateMediaRecordIdOps = new ArrayList<>();
        mUpdateMediaMarkDataBeanIdOps = new ArrayList<>();
        if (content != null) {
            mRecordList = new ArrayList<>(RecorderXmlParser.parse(content));
        } else {
            mRecordList = new ArrayList<>();
        }

        if ((mMaxCount == 0) && (mRecordList.size() > 0)) {
            mMaxCount = mRecordList.size();
        }
        Bundle prepare = new Bundle();
        DebugUtil.d(TAG, "onPrepare mMaxCount = " + mMaxCount);
        ProgressHelper.putMaxCount(prepare, mMaxCount);
        return prepare;
    }

    @Override
    public void onRestore(Bundle bundle) {
        DebugUtil.d(TAG, "onRestore");
        if (mAmpFilePath.size() > 0) {
            mAmpFilePath.clear();
        }
        if (!mIsCancel) {
            // sp数据
            restoreSharedPreferenceData();
            /*录音分组表数据 放在record表之前，因为record表依赖于group_info表*/
            restoreGroupInfo();
            // record表数据
            restoreRecordData();
            // 波形，依赖record恢复数据
            restoreAmpFile();
            // 图片标记db，依赖处理完的record表数据
            restorePictureMark();
            // 图片标记file，依赖picture_mark表
            restorePictureMarkFile();
            // 转文本db
            restoreConvertRecord();
            // 转文本file，依赖convert表数据
            restoreConvertRecordFile();
            // 转文本upload_db，依赖convert表数据
            restoreUploadRecord();
            // 关键词db
            restoreKeyWord();
            if (mMaxCount > 0) {
                updateRestoreProgress(new Bundle(), mCompletedCount, mMaxCount);
            }
        }
    }

    private void restoreRecordData() {
        if (mContext == null) {
            DebugUtil.e(TAG, "restoreRecordData mContext is null");
            return;
        }
        if ((mCompletedCount < mMaxCount) && (mRecordList != null)) {
            Bundle progress = new Bundle();
            Record record = null;
            ContentProviderOperation.Builder builder = null;
            ContentValues values = null;
            Record localSameDataRecord = null;
            do {
                record = mRecordList.get(mCompletedCount++);
                // 过筛被删除未同步无效脏数据
                if (record.isDeleted()) {
                    continue;
                }
                values = new ContentValues();
                localSameDataRecord = getSameRecordFromLocalList(record);
                int result = (localSameDataRecord == null || localSameDataRecord.isRecycle()) ? RECORD_NEED_INSERT : RECORD_NEED_UPDATE;
                DebugUtil.d(TAG, "the result is " + result);
                if (result == RECORD_NEED_INSERT) {
                    builder = ContentProviderOperation
                            .newInsert(DatabaseConstant.RecordUri.RECORD_CONTENT_URI);
                    if (isUUIDExist(record.getUuid())) {
                        DebugUtil.i(TAG, "isUUIDExist the uuid is exist need update uuid");
                        values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID, UUID.randomUUID().toString());
                    } else {
                        values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID, record.getUuid());
                    }
                } else if (result == RECORD_NEED_UPDATE) {
                    String selection = DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA + " COLLATE NOCASE = ?";
                    String[] selectionArgs = new String[]{record.getData()};
                    builder = ContentProviderOperation
                            .newUpdate(DatabaseConstant.RecordUri.RECORD_CONTENT_URI)
                            .withSelection(selection, selectionArgs);
                    if (isUUIDExistAndDifferentPath(record)) {
                        values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_UUID, UUID.randomUUID().toString());
                    }
                    if (!TextUtils.isEmpty(localSameDataRecord.getMD5()) && localSameDataRecord.getMD5().equals(record.getMD5())) {
                        DebugUtil.i(TAG, "same md5,display name is: " + localSameDataRecord.getDisplayName());
                    } else {
                        /*db中有相同data的数据，音频、db数据被覆盖，对应原来的转文本、图标标记也需要清空*/
                        ConvertDeleteUtil.deleteConvertData(mContext, localSameDataRecord.getData());
                        PictureMarkDbUtils.deletePictureMarksWithMarkPictureFiles(String.valueOf(localSameDataRecord.getId()));
                    }
                } /*else {
                        DebugUtil.w(TAG, "not support result " + result);
                        continue;
                    }*/
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DATA, record.getData());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SIZE, record.getFileSize());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME, record.getDisplayName());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_MIMETYPE, record.getMimeType());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_CREATED, record.getDateCreated());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DATE_MODIFIED, record.getDateModied());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_RECORD_TYPE, record.getRecordType());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_MARK_DATA, record.getMarkData());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_DATA, record.getAmpData());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DURATION, record.getDuration());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_BUCKET_DISPLAY_NAME, record.getBuckedDisplayName());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRTY, RECORD_DIRTY_MEGA_ONLY/*record.getDirty()*/);
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DELETE, record.isDeleted());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_MD5, record.getMD5());
                // 不搬云端数据，避免修改后非同一个用户同步失败
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_SYS_VERSION, 0/*record.getSysVersion()*/);
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_CLOUD_CHECK_PAYLOAD, (String) null/*record.getCheckPayload()*/);
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_FILE_ID, (String) null/*record.getFileId()*/);
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_GLOBAL_ID, (String) null/*record.getGlobalId()*/);
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_TYPE, record.getSyncType());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_UPLOAD_STATUS, record.getSyncStatus());
                values.put(DatabaseConstant.RecorderColumn.COLOUM_NAME_SYNC_DOWNLOAD_STATUS, record.getSyncDownlodStatus());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_ERROR_CODE, record.getErrorCode());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_LEVEL, record.getLevel());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_LOCAL_EDIT_STATUS, record.getEditStatus());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_SYNC_DATE, record.getSyncDate());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_FAIL_COUNT, record.getFailedCount());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_LAST_FAIL_TIME, record.getLastFailedTime());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_RELATIVE_PATH, record.getRelativePath());
                if (!TextUtils.isEmpty(record.getAmpFilePath())) {
                    values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_AMP_FILE_PATH, record.getAmpFilePath());
                    mAmpFilePath.add(record.getAmpFilePath());
                }
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_PRIVATE_STATUS, record.getSyncPrivateStatus());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_MIGRATE_STATUS, record.getSyncMigrateStatus());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_MARKLIST_SHOWING, record.getIsMarkListShowing());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_IS_DIRECT_ON, record.getDirectOn());
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_DIRECT_TIME, record.getDirectTime());

                String groupUuid = record.getGroupUuid();
                if (!TextUtils.isEmpty(groupUuid)) {
                    values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_UUID, groupUuid);
                    int groupId = GroupInfoDbUtil.getGroupIdByGroupUUID(mContext, groupUuid);
                    values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_GROUP_ID, groupId);
                }
                values.put(DatabaseConstant.RecorderColumn.COLUMN_NAME_ORIGINAL_NAME, record.getOriginalName());

                builder.withValues(values);
                mRecordOps.add(builder.build());
                // 最后一个进度等onRestore执行完再更新
                if (mMaxCount != mCompletedCount) {
                    updateRestoreProgress(progress, mCompletedCount, mMaxCount);
                }
            } while (mCompletedCount < mMaxCount);
            try {
                ContentResolver resolver = mContext.getContentResolver();
                resolver.applyBatch(DatabaseConstant.AUTHORITY, mRecordOps);
                resolver.notifyChange(DatabaseConstant.RecordUri.RECORD_CONTENT_URI, null);
            } catch (Exception e) {
                DebugUtil.e(TAG, "onRestore batch insert recorder infro failed.", new Throwable(e.toString()));
            }
        }
    }

    private void updateRestoreProgress(Bundle bundle, int progressCount, int maxCount) {
        ProgressHelper.putMaxCount(bundle, maxCount);
        ProgressHelper.putCompletedCount(bundle, progressCount);
        if (mBRPluginHandler != null) {
            mBRPluginHandler.updateProgress(bundle);
        }
    }

    @Override
    public void onPause(Bundle bundle) {
        DebugUtil.d(TAG, "onPause");
    }

    @Override
    public void onContinue(Bundle bundle) {
        DebugUtil.d(TAG, "onContinue");
        mIsCancel = false;
    }

    @Override
    public void onCancel(Bundle bundle) {
        DebugUtil.d(TAG, "onCancel");
        mIsCancel = true;
    }

    @Override
    public Bundle onDestroy(Bundle bundle) {
        DebugUtil.d(TAG, "onDestroy");
        if (mRecordList != null) {
            mRecordList.clear();
        }

        if (mRecordOps != null) {
            mRecordOps.clear();
            mRecordOps = null;
        }
        if (mLocalRecordRelativePathMap != null) {
            mLocalRecordRelativePathMap.clear();
            mLocalRecordRelativePathMap = null;
        }
        if (mLocalRecordUuidMap != null) {
            mLocalRecordUuidMap.clear();
            mLocalRecordUuidMap = null;
        }

        scanRecordings();
        Bundle result = new Bundle();
        ProgressHelper.putBRResult(result, mIsCancel ? ProgressHelper.BR_RESULT_CANCEL : ProgressHelper.BR_RESULT_SUCCESS);
        ProgressHelper.putMaxCount(result, mMaxCount);
        ProgressHelper.putCompletedCount(result, mCompletedCount);
        return result;
    }

    private List<Record> queryLocalRecorder() {
        Cursor cursor = null;
        ContentResolver resolver = mContext.getContentResolver();
        mLocalRecordList = new ArrayList<>();
        mLocalRecordList.clear();
        try {
            cursor = resolver.query(DatabaseConstant.RecordUri.RECORD_CONTENT_URI,
                    DatabaseConstant.getRecordProjection(), null, null, null);
        } catch (Exception e) {
            DebugUtil.e(TAG, "getMaxCount query recorder citys exception:" + e.toString());
        }
        if ((cursor != null) && cursor.moveToFirst()) {
            do {
                Record record = new Record(cursor, Record.TYPE_FROM_RECORD);
                mLocalRecordList.add(record);
            } while (cursor.moveToNext());
        }
        if (cursor != null) {
            cursor.close();
            cursor = null;
        }
        return mLocalRecordList;
    }

    private Record getSameRecordFromLocalList(Record record) {
        if ((mLocalRecordList == null) || (mLocalRecordList.size() == 0)) {
            mLocalRecordList = queryLocalRecorder();
        }
        if ((mLocalRecordList == null) || (mLocalRecordList.size() == 0)) {
            return null;
        } else if (mLocalRecordRelativePathMap == null) {
            mLocalRecordRelativePathMap = new HashMap<>();
            mLocalRecordList.forEach(localRecord -> mLocalRecordRelativePathMap.put(localRecord.getConcatRelativePath(), localRecord));
        }
        return mLocalRecordRelativePathMap.get(record.getConcatRelativePath());
    }

    private int containMarkDataBean(MarkDataBean markDataBean) {
        if ((mLocalMarkList == null) || (mLocalMarkList.size() == 0)) {
            mLocalMarkList = PictureMarkDbUtils.INSTANCE.queryAllPictureMarks();
        }
        int mNeedInsert = RECORD_NEED_INSERT;
        if ((mLocalMarkList == null) || (mLocalMarkList.size() == 0)) {
            mNeedInsert = RECORD_NEED_INSERT;
        } else {
            for (MarkDataBean mdb : mLocalMarkList) {
                if (markDataBean.getPictureFilePath().equals(mdb.getPictureFilePath())) {
                    mNeedInsert = RECORD_NEED_UPDATE;
                    break;
                }
            }
        }
        return mNeedInsert;
    }

    private String getXmlInfo(String fileName) {
        InputStream is = null;
        try {
            FileDescriptor fileDescriptor = getFileDescriptor(fileName, ParcelFileDescriptor.MODE_READ_ONLY);
            if (fileDescriptor == null) {
                return null;
            }
            is = new FileInputStream(fileDescriptor);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int len = -1;
            byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
            while ((len = is.read(buffer, 0, DEFAULT_BUFFER_SIZE)) != -1) {
                baos.write(buffer, 0, len);
            }
            return baos.toString("utf-8");
        } catch (IOException e) {
            DebugUtil.e(TAG, "getXmlInfo e=" + e);
        } catch (NullPointerException e) {
            DebugUtil.e(TAG, "getXmlInfo e=" + e);
        } catch (IndexOutOfBoundsException e) {
            DebugUtil.e(TAG, "getXmlInfo e=" + e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    DebugUtil.e(TAG, "getXmlInfo close e=" + e);
                }
            }
        }

        return null;
    }

    private void restorePictureMark() {
        DebugUtil.i(TAG, "restorePictureMark start");
        String content = getXmlInfo(mPictureMarkRestoreXmlPath);
        if (content == null) {
            DebugUtil.e(TAG, "PictureMarkRestoreXml content is null.");
            return;
        }
        HashSet<MarkDataBean> parse = new PictureMarkXmlParser().parse(content);
        ArrayList<MarkDataBean> data = new ArrayList<>(parse);

        Map<String, String> finalRestoreRecordIdToDataMap = new HashMap<>();
        if (mRecordList != null && mRecordList.size() > 0) {
            mRecordList.forEach(record -> finalRestoreRecordIdToDataMap.put(record.getTempId(), record.getData()));
        }

        for (MarkDataBean mdb : data) {
            if (!TextUtils.isEmpty(mdb.getPictureFilePath())) {
                mPictureMarkPath.add(mdb.getPictureFilePath());
            }
            String dataPath = finalRestoreRecordIdToDataMap.get(mdb.getKeyId());
            if (!TextUtils.isEmpty(dataPath)) {
                String keyId = RecorderDBUtil.getKeyIdByPath(dataPath);
                if (!TextUtils.isEmpty(keyId)) {
                    mdb.setKeyId(keyId);
                }
            }
            if (TextUtils.isEmpty(mdb.getKeyId())) {
                DebugUtil.d(TAG, "isEmpty continue " + mdb.getKeyId());
                continue;
            }
            ContentProviderOperation.Builder builder = null;
            ContentValues values = new ContentValues();

            int result = containMarkDataBean(mdb);
            if (result == RECORD_NEED_INSERT) {
                builder = ContentProviderOperation
                        .newInsert(PictureMarkDbUtils.INSTANCE.getPictureMarkUri());
            } else if (result == RECORD_NEED_UPDATE) {
                String selection = PictureMarkDbUtils.PICTURE_FILE_PATH + "  = ?";
                String[] selectionArgs = new String[]{mdb.getPictureFilePath()};
                builder = ContentProviderOperation
                        .newUpdate(PictureMarkDbUtils.INSTANCE.getPictureMarkUri())
                        .withSelection(selection, selectionArgs);
            } else {
                continue;
            }
            values.put(PictureMarkDbUtils.KEY_ID, mdb.getKeyId());
            values.put(PictureMarkDbUtils.TIME_IN_MILLS, mdb.getTimeInMills());
            values.put(PictureMarkDbUtils.VERSION, mdb.getVersion());
            values.put(PictureMarkDbUtils.DEFAULT_NO, mdb.getDefaultNo());
            values.put(PictureMarkDbUtils.MARK_TEXT, mdb.getMarkText());
            values.put(PictureMarkDbUtils.PICTURE_FILE_PATH, mdb.getPictureFilePath());
            builder.withValues(values);
            mUpdateMediaMarkDataBeanIdOps.add(builder.build());
        }
        try {
            ContentResolver resolver = mContext.getContentResolver();
            resolver.applyBatch(DatabaseConstant.AUTHORITY, mUpdateMediaMarkDataBeanIdOps);
            resolver.notifyChange(PictureMarkDbUtils.INSTANCE.getPictureMarkUri(), null);
        } catch (Exception e) {
            DebugUtil.e(TAG, "onRestore batch insert or update picture mark failed.", e);
        }
        DebugUtil.i(TAG, "restorePictureMark end");
    }

    private void restorePictureMarkFile() {
        DebugUtil.i(TAG, "restorePictureMarkFile start");
        File dir = mContext.getFilesDir();
        String srcFolderPath = mRecorderRestorePath + File.separator + PictureMarkDbUtils.TABLE_NAME_PICTURE_NAME;
        for (String fileName : mPictureMarkPath) {
            if (!TextUtils.isEmpty(fileName)) {
                String srcPath = srcFolderPath + File.separator + fileName;
                String destPath = dir.getAbsolutePath() + File.separator + PictureMarkDbUtils.TABLE_NAME_PICTURE_NAME + File.separator + fileName;
                boolean copySuc = MoveUtils.copyOverwriteFileForRestore(this, srcPath, destPath);
                DebugUtil.i(TAG, "restorePictureMarkFile: srcName: " + FileUtils.getDisplayNameByPath(srcPath) + ", destName: " + FileUtils.getDisplayNameByPath(destPath) + ", copySuc: " + copySuc);
            }
        }
        DebugUtil.i(TAG, "restorePictureMarkFile end");
    }

    private void restoreConvertRecordFile() {
        DebugUtil.i(TAG, "restoreConvertRecordFile start");
        File dir = mContext.getFilesDir();
        String srcFolderPath = mRecorderRestorePath + File.separator + CONVERT_FOLDER;
        for (String path : mConvertTextFilePath) {
            if (!TextUtils.isEmpty(path)) {
                String fileNameFromFullPath = FileUtils.getFileNameFromFullPath(path);
                String srcPath = srcFolderPath + File.separator + fileNameFromFullPath;
                String destPath = dir.getAbsolutePath() + File.separator + DatabaseConstant.PATH_CONVERT + File.separator + fileNameFromFullPath;
                boolean copySuc = MoveUtils.copyOverwriteFileForRestore(this, srcPath, destPath);
                DebugUtil.i(TAG, "restoreConvertRecordFile: srcName: " + FileUtils.getDisplayNameByPath(srcPath) + ", destName: " + FileUtils.getDisplayNameByPath(destPath) + ", copySuc: " + copySuc);
            }
        }
        mConvertTextFilePath.clear();
        DebugUtil.i(TAG, "restoreConvertRecordFile end");
    }

    private void restoreAmpFile() {
        DebugUtil.i(TAG, "restoreAmpFile start");
        File dir = mContext.getFilesDir();
        if (dir == null) {
            return;
        }
        String srcFolderPath = mRecorderRestorePath + File.separator + AMP_FOLDER;
        for (String path : mAmpFilePath) {
            if (!TextUtils.isEmpty(path)) {
                String fileNameFromFullPath = FileUtils.getFileNameFromFullPath(path);
                String destFilePath = dir.getAbsolutePath() + File.separator + fileNameFromFullPath;
                String srcFilePath = srcFolderPath + File.separator + fileNameFromFullPath;
                boolean copySuc = MoveUtils.copyOverwriteFileForRestore(this, srcFilePath, destFilePath);
                DebugUtil.i(TAG, "restoreAmpFile: srcPath: " + FileUtils.getDisplayNameByPath(srcFilePath) + ", destPath: " + FileUtils.getDisplayNameByPath(destFilePath) + ", copySuc: " + copySuc);
                //file.delete();
            }
        }
        DebugUtil.i(TAG, "restoreAmpFile end");
    }

    private void restoreConvertRecord() {
        DebugUtil.i(TAG, "restoreConvertRecord start");
        String content = getXmlInfo(mConvertRecordRestoreXmlPath);
        if (content == null) {
            DebugUtil.e(TAG, "ConvertRecordRestoreXml content is null.");
            return;
        }
        if (mConvertTextFilePath.size() > 0) {
            mConvertTextFilePath.clear();
        }
        if (mConvertOnlyIds.size() > 0) {
            mConvertOnlyIds.clear();
        }
        String[] projection = new String[]{DatabaseConstant.ConvertColumn.MEDIA_PATH};
        mMediaPath = queryConvertRecordMediaPath(projection, null, null);
        ArrayList<ConvertRecord> data = new ArrayList<>(ConvertRecordXmlParser.parse(content));
        ContentProviderOperation.Builder builder = null;
        ContentValues values = null;
        for (ConvertRecord record : data) {
            boolean isExists = (mMediaPath != null) && mMediaPath.contains(record.getMediaPath());
            if (isExists) {
                String selection = DatabaseConstant.ConvertColumn.MEDIA_PATH + " = ?";
                String[] selectionArgs = new String[]{record.getMediaPath()};
                builder = ContentProviderOperation.newUpdate(DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI).withSelection(selection, selectionArgs);
            } else {
                builder = ContentProviderOperation.newInsert(DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI);
            }
            values = new ContentValues();
            values.put(DatabaseConstant.ConvertColumn.RECORD_ID, record.getRecordId());
            values.put(DatabaseConstant.ConvertColumn.MEDIA_PATH, record.getMediaPath());
            values.put(DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH, record.getConvertTextfilePath());
            if (!TextUtils.isEmpty(record.getConvertTextfilePath())) {
                mConvertTextFilePath.add(record.getConvertTextfilePath());
            }
            values.put(DatabaseConstant.ConvertColumn.CHUNK_NAME, record.getChunkName());
            values.put(DatabaseConstant.ConvertColumn.COMPLETE_STATUS, record.getCompleteStatus());
            values.put(DatabaseConstant.ConvertColumn.ONLY_ID, record.getOnlyId());
            if (!TextUtils.isEmpty(record.getOnlyId())) {
                mConvertOnlyIds.add(record.getOnlyId());
            }
            values.put(DatabaseConstant.ConvertColumn.VERSION, record.getVersion());
            values.put(DatabaseConstant.ConvertColumn.TASKID, record.getTaskId());
            values.put(DatabaseConstant.ConvertColumn.UPLOAD_REQUEST_ID, record.getUploadRequestId());
            values.put(DatabaseConstant.ConvertColumn.UPLOAD_KEY, record.getUploadKey());
            values.put(DatabaseConstant.ConvertColumn.PART_COUNT, record.getPartCount());
            values.put(DatabaseConstant.ConvertColumn.UPLOAD_STATUS, record.getUploadStatus());
            values.put(DatabaseConstant.ConvertColumn.CONVERT_STATUS, record.getConvertStatus());
            values.put(DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL, record.getUploadAllUrl());

            values.put(DatabaseConstant.ConvertColumn.HISTORY_ROLENAME, record.getHistoryRoleName());
            values.put(DatabaseConstant.ConvertColumn.SERVER_PLAN_CODE, record.getServerPlanCode());
            values.put(DatabaseConstant.ConvertColumn.CAN_SHOW_SPEAKER_ROLE, record.getCanShowSpeakerRole());
            values.put(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ISSHOWING, record.getSpeakerRoleIsShowing());
            values.put(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER, record.getSpeakerRoleOriginalNumber());
            values.put(DatabaseConstant.ConvertColumn.SPEAKER_ROLE_HAS_FIRSTSHOW, record.getSpeakerRoleHasFirstshow());
            values.put(DatabaseConstant.ConvertColumn.IS_DIRECT_ON, record.getIsDirectOn());
            values.put(DatabaseConstant.ConvertColumn.DIRECT_TIME, record.getDirectTime());

            builder.withValues(values);
            mConvertRecordOps.add(builder.build());
            DebugUtil.i(TAG, "restoreConvertRecord:" + record.toString());
        }
        try {
            ContentResolver resolver = mContext.getContentResolver();
            resolver.applyBatch(DatabaseConstant.AUTHORITY, mConvertRecordOps);
            resolver.notifyChange(DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI, null);
        } catch (Exception e) {
            DebugUtil.e(TAG, "onRestore batch insert or update convert record failed.", e);
        }
        DebugUtil.i(TAG, "restoreConvertRecord end");
    }


    /**
     * 恢复关键词数据
     */
    private void restoreKeyWord() {
        // 从xml中读取数据
        String content = getXmlInfo(mKeyWordRestoreXmlPath);
        if (TextUtils.isEmpty(content)) {
            DebugUtil.e(TAG, "restoreKeyWord KeyWordRestoreXmlPath content is null.");
            return;
        }
        // 将数据解析为对应的java对象
        BaseXmlParser<KeyWord> parser = new KeyWordParser();
        List<KeyWord> data = new ArrayList<>(parser.parse(content));
        DebugUtil.e(TAG, "restoreKeyWord size:" + data.size());

        // 把相同的recordId的记录收集到一个list中
        Map<Long, List<KeyWord>> map = data.stream().collect(Collectors.groupingBy(KeyWord::getRecordId));
        map.forEach(new BiConsumer<Long, List<KeyWord>>() {
            @Override
            public void accept(Long recordId, List<KeyWord> keyWords) {
                if (keyWords.isEmpty()) {
                    return;
                }
                String mediaPath = keyWords.get(0).getMediaPath();
                mMediaMap.put(mediaPath, recordId); // 映射老的recordId和mediaPath

                // 插入数据到数据库，以覆盖的方式插入
                KeyWordDbUtils.INSTANCE.addKeyWords(keyWords, recordId);
            }
        });

        DebugUtil.e(TAG, "restoreKeyWord mediaMap:" + mMediaMap);
    }

    private void restoreGroupInfo() {
        String content = getXmlInfo(mGroupInfoRestoreXmlPath);
        if (content == null) {
            DebugUtil.e(TAG, "GroupInfoRestoreXml content is null.");
            return;
        }
        //delete same uuid records on this devices
        if ((mGroupInfoUuids != null) && (mGroupInfoUuids.size() > 0)) {
            for (String uuid : mGroupInfoUuids) {
                boolean bDeleted = GroupInfoDbUtil.deleteByUUId(BaseApplication.getAppContext(), uuid);
                DebugUtil.i(TAG, "restoreGroupInfo delete same uuid groupInfo input uuid: " + uuid + ", delete result: " + bDeleted);
            }
        }
        //insert restore upload records
        ArrayList<GroupInfo> data = new ArrayList<>(new GroupInfoXmlParser().parse(content));
        int insertCnt = GroupInfoDbUtil.batchInsertGroupInfo(BaseApplication.getAppContext(), data);
        DebugUtil.i(TAG, "insert groupInfo size " + data.size() + ", insertSuc cnt: " + insertCnt);
    }

    private void restoreUploadRecord() {
        String content = getXmlInfo(mUploadRecordRestoreXmlPath);
        if (content == null) {
            DebugUtil.e(TAG, "ConvertRecordRestoreXml content is null.");
            return;
        }
        //delete same onlyId records on this devices
        if ((mConvertOnlyIds != null) && (mConvertOnlyIds.size() > 0)) {
            for (String onlyId : mConvertOnlyIds) {
                int deleteCount = UploadDbUtil.deleteByOnlyId(BaseApplication.getAppContext(), onlyId);
                DebugUtil.i(TAG, "restoreUploadRecord delete same onlyId upload records, input onlyId: " + onlyId + ", delete cnt: " + deleteCount);
            }
        }
        //insert restore upload records
        ArrayList<UploadRecord> data = new ArrayList<>(new UploadRecordXmlParser().parse(content));
        int insertCnt = UploadDbUtil.insertUploadRecords(BaseApplication.getAppContext(), data);
        DebugUtil.i(TAG, "insert upload size " + data.size() + ", insertSuc cnt: " + insertCnt);
    }

    private void restoreSharedPreferenceData() {
        // 从xml中读取数据
        String content = getXmlInfo(mSharedPreferenceXmlPath);
        if (TextUtils.isEmpty(content)) {
            DebugUtil.d(TAG, "restoreSharedPreferenceData content is null.");
            return;
        }
        // 将数据解析为对应的java对象
        BaseXmlParser<Unit> parser = new SharedPreferenceParser();
        int itemSize = parser.parse(content).size();
        DebugUtil.d(TAG, "restoreSharedPreferenceData end,contentSize=" + itemSize);

        if (!BaseUtil.isRealme()) {
            String smartContent = getXmlInfo(mSmartSharedPreferenceXmlPath);
            if (TextUtils.isEmpty(smartContent)) {
                DebugUtil.d(TAG, "restoreSharedPreferenceData smartContent is null.");
                return;
            }
            BaseXmlParser<Unit> smartParser = new SmartSharedPreferenceParser();
            int smartItemSize = smartParser.parse(smartContent).size();
            DebugUtil.d(TAG, "restoreSmartPreferenceData end, smartItemSize=" + smartItemSize);
        }
    }

    private HashSet<String> queryConvertRecordMediaPath(String[] projection, String selection, String[] selectionArgs) {
        HashSet<String> data = null;
        Cursor cursor = null;
        try {
            cursor = mContext.getContentResolver().query(DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI, projection, selection, selectionArgs, null);
            if ((cursor != null)) {
                data = new HashSet<>();
                int pathIndex = cursor.getColumnIndex(DatabaseConstant.ConvertColumn.MEDIA_PATH);
                while (cursor.moveToNext()) {
                    if (pathIndex != -1) {
                        data.add(cursor.getString(pathIndex));
                    }
                }
            }
        } catch (Throwable e) {
            DebugUtil.e(TAG, "queryConvertRecordMediaPath failed.", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return data;
    }

    private void updateMediaRecordId() {
        synchronized (mWaitObject) {
            try {
                if (!mScanFinished) {
                    mWaitObject.wait(TIME_OUT);
                }
            } catch (InterruptedException e) {
                DebugUtil.e(TAG, "updateMediaRecordId InterruptedException", e);
            }
        }
        String[] projection = new String[]{DatabaseConstant.ConvertColumn.MEDIA_PATH};
        String selection = DatabaseConstant.ConvertColumn.RECORD_ID + " <= ? ";
        String[] selectionArgs = new String[]{String.valueOf(0)};
        mMediaPath = queryConvertRecordMediaPath(projection, selection, selectionArgs);
        if (mMediaPath != null) {
            for (String data : mMediaPath) {
                long mediaId = MediaDBUtils.queryIdByData(data);
                DebugUtil.i(TAG, "update media name:" + FileUtils.getDisplayNameByPath(data) + " , mediaId:" + mediaId);
                selection = DatabaseConstant.ConvertColumn.MEDIA_PATH + " = ?";
                selectionArgs = new String[]{data};
                ContentProviderOperation.Builder builder = ContentProviderOperation.newUpdate(DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI).withSelection(selection, selectionArgs);
                ContentValues values = new ContentValues();
                values.put(DatabaseConstant.ConvertColumn.RECORD_ID, mediaId);
                builder.withValues(values);
                mUpdateMediaRecordIdOps.add(builder.build());

                Long recordId = mMediaMap.get(data); // 通过mediaPath，将旧的mediaId和新的mediaId对应起来
                if (Objects.nonNull(recordId) && (recordId != 0L)) {
                    mUpdateKeyWordRecordIds.add(new Pair<>(recordId, mediaId));
                }
            }
            try {
                ContentResolver resolver = mContext.getContentResolver();
                resolver.applyBatch(DatabaseConstant.AUTHORITY, mUpdateMediaRecordIdOps);
                resolver.notifyChange(DatabaseConstant.ConvertUri.CONVERT_CONTENT_URI, null);
                // 批量更新recordId
                KeyWordDbUtils.INSTANCE.batchUpdateRecordId(mUpdateKeyWordRecordIds);
            } catch (Exception e) {
                DebugUtil.e(TAG, "updateMediaRecordId failed.", e);
            }
        } else {
            DebugUtil.i(TAG, "updateMediaRecordId mMediaPath is null");
        }
        notifyCenterSearchMoveComplete();
        Context appContext = BaseApplication.getAppContext();
        if (mCloudKitApi != null && mCloudKitApi.isCloudOn()) {
            // 清空锚点，触发一次全量云同步
            mCloudKitApi.stopSyncForClearAnchor(appContext);
            mCloudKitApi.trigMediaDBSync(appContext, SYNC_TYPE_RECOVERY_START_APP);
        } else {
            // 云同步未打开，只走全量
            RecordDataSyncHelper.scheduleSyncRunnable();
        }
    }

    private void notifyCenterSearchMoveComplete() {
        BaseUtil.sendLocalBroadcast(mContext, new Intent(LOCAL_ACTION_MOVE_RESTORE_COMPLETED));
    }

    private void scanRecordings() {
        DebugUtil.i(TAG, "scanRecordings");
        File dirRecordings = null;
        if (BaseUtil.INSTANCE.isAndroidQOrLater()) {
            dirRecordings = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC);
            dirRecordings = new File(dirRecordings, Constants.RECORDINGS);
        } else {
            dirRecordings = Environment.getExternalStorageDirectory();
            dirRecordings = new File(dirRecordings, Constants.RECORDINGS);
        }
        String[] paths = new String[]{dirRecordings.getAbsolutePath()};
        if (PermissionUtils.hasReadAudioPermission()) {
            MediaDataScanner.getInstance().mediaScanWithCallback(BaseApplication.getAppContext(), paths, (path, uri) -> {
                DebugUtil.i(TAG, "onScanCompleted name:" + FileUtils.getDisplayNameByPath(path)
                        + "uri: " + uri + ", threadId: " + Thread.currentThread());
                mScanFinished = true;
                synchronized (mWaitObject) {
                    mWaitObject.notifyAll();
                }
            });
        }
        updateMediaRecordId();
    }

    private boolean isUUIDExist(String uuid) {
        if (TextUtils.isEmpty(uuid)) {
            return false;
        }
        if ((mLocalRecordList == null) || (mLocalRecordList.size() == 0)) {
            mLocalRecordList = queryLocalRecorder();
        }
        if ((mLocalRecordList == null) || (mLocalRecordList.size() == 0)) {
            return false;
        } else if (mLocalRecordUuidMap == null) {
            mLocalRecordUuidMap = new HashMap<>();
            mLocalRecordList.forEach(localRecord -> mLocalRecordUuidMap.put(localRecord.getUuid().toLowerCase(), localRecord));
        }
        return mLocalRecordUuidMap.containsKey(uuid.toLowerCase());
    }

    private boolean isUUIDExistAndDifferentPath(Record cloudRecord) {
        if ((cloudRecord == null) || TextUtils.isEmpty(cloudRecord.getUuid())) {
            return false;
        }
        if ((mLocalRecordList == null) || (mLocalRecordList.size() == 0)) {
            mLocalRecordList = queryLocalRecorder();
        }
        if ((mLocalRecordList == null) || (mLocalRecordList.size() == 0)) {
            return false;
        } else if (mLocalRecordUuidMap == null) {
            mLocalRecordUuidMap = new HashMap<>();
            mLocalRecordList.forEach(localRecord -> mLocalRecordUuidMap.put(localRecord.getUuid().toLowerCase(), localRecord));
        }
        Record localSameUuidRecord = mLocalRecordUuidMap.get(cloudRecord.getUuid().toLowerCase());
        if (localSameUuidRecord != null && !cloudRecord.getData().equalsIgnoreCase(localSameUuidRecord.getData())) {
            DebugUtil.i(TAG, "isUUIDExist the uuid is exist need update uuid");
            return true;
        }
        return false;
    }
}
