/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: SharedPreferenceComposer
 * Description:
 * Version: 1.0
 * Date: 2023/8/31
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/8/31 1.0 create
 */

package com.recorder.move.spvalue

import com.recorder.move.BaseXmlComposer
import com.recorder.move.MoveUtils
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.StorageManager
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.permission.PermissionUtils

class SharedPreferenceComposer : BaseXmlComposer<Unit>() {
    companion object {
        const val SHARED_PREFERENCE_XML_NAME = "sharedPreference.xml"
        const val SHARED_PREFERENCE_TAG = "sharedPreference"
    }
    override fun getTag(): String {
        return SHARED_PREFERENCE_TAG
    }

    override fun composerData(data: Unit) {
        if (MoveUtils.checkCanBackUserNotice()) {
            DebugUtil.i("SharedPreferenceComposer", "composerData")
            val context = BaseApplication.getAppContext()

            PermissionUtils.getNextAction().run { // 用户须知sp
                if (this != PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
                    mSerializer.attribute("", PermissionUtils.FIRST_APPLY_STATEMENT_DIALOG, PermissionUtils.getNextAction().toString())
                    // 转文本隐私sp
                    val convertApplyValue =
                        StorageManager.getIntPref(context, PermissionUtils.FIRST_APPLY_PERMISSION_CONVERT, PermissionUtils.PERMISSION_NOT_APPLY)
                    mSerializer.attribute("", PermissionUtils.FIRST_APPLY_PERMISSION_CONVERT, convertApplyValue.toString())
                }

                // 升级弹窗sp
                val updateNoticeValue = StorageManager.getIntPref(BaseApplication.getAppContext(),
                    PermissionUtils.FIRST_APPLY_PERMISSION_USER_UPDATE, PermissionUtils.PERMISSION_NOT_APPLY)
                if (updateNoticeValue != PermissionUtils.PERMISSION_NOT_APPLY) {
                    mSerializer.attribute(
                        "",
                        PermissionUtils.FIRST_APPLY_PERMISSION_USER_UPDATE,
                        updateNoticeValue.toString()
                    )
                }
            }
        }
    }

    override fun getXmlName(): String = SHARED_PREFERENCE_XML_NAME
}