/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File: ConvertRecordXmlComposer
 ** Description:for move convert vad table generate xml.
 ** Version:1.0
 ** Date :2019-10-11
 ** Author: tianjun
 **
 ** v1.0, 2019-10-11, tianjun, create
 ****************************************************************/
package com.recorder.move;

import android.text.TextUtils;
import android.util.Xml;

import org.xmlpull.v1.XmlSerializer;

import java.io.IOException;
import java.io.StringWriter;

import com.soundrecorder.common.constant.DatabaseConstant;
import com.soundrecorder.common.databean.ConvertRecord;
import com.soundrecorder.base.utils.DebugUtil;

public class ConvertRecordXmlComposer {
    private String TAG = "ConvertRecordXmlComposer";
    private XmlSerializer mSerializer = null;
    private StringWriter mStringWriter = null;

    public boolean startCompose() {
        boolean result = false;
        mSerializer = Xml.newSerializer();
        mStringWriter = new StringWriter();
        try {
            mSerializer.setOutput(mStringWriter);
            mSerializer.startDocument(null, false);
            mSerializer.startTag("", "recorder");
            result = true;
        } catch (IOException e) {
            DebugUtil.e(TAG, "startCompose IOException error", e);
        } catch (IllegalArgumentException e) {
            DebugUtil.e(TAG, "startCompose IllegalArgumentException error", e);
        } catch (IllegalStateException e) {
            DebugUtil.e(TAG, "startCompose IllegalStateException error", e);
        }

        return result;
    }

    public boolean endCompose() {
        boolean result = false;
        try {
            mSerializer.endTag("", "recorder");
            mSerializer.endDocument();
            result = true;
        } catch (IllegalArgumentException e) {
            DebugUtil.e(TAG, "endCompose IllegalArgumentException error", e);
        } catch (IllegalStateException e) {
            DebugUtil.e(TAG, "endCompose IllegalStateException error", e);
        } catch (IOException e) {
            DebugUtil.e(TAG, "endCompose IOException error", e);
        }

        return result;
    }

    public void addConvertRecord(ConvertRecord record) {
        try {
            mSerializer.startTag("", DatabaseConstant.ROOT);

            mSerializer.attribute("", DatabaseConstant.ConvertColumn.MEDIA_PATH, TextUtils.isEmpty(record.getMediaPath()) ? "" : record.getMediaPath());
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.CONVERT_TEXTFILE_PATH, TextUtils.isEmpty(record.getConvertTextfilePath()) ? "" : record.getConvertTextfilePath());
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.CHUNK_NAME, TextUtils.isEmpty(record.getChunkName()) ? "" : record.getChunkName());
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.COMPLETE_STATUS, String.valueOf(record.getCompleteStatus()));
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.ONLY_ID, TextUtils.isEmpty(record.getOnlyId()) ? "" : record.getOnlyId());
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.VERSION, String.valueOf(record.getVersion()));
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.TASKID, TextUtils.isEmpty(record.getTaskId()) ? "" : record.getTaskId());
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.UPLOAD_REQUEST_ID, TextUtils.isEmpty(record.getUploadRequestId()) ? "" : record.getUploadRequestId());
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.UPLOAD_KEY, TextUtils.isEmpty(record.getUploadKey()) ? "" : record.getUploadKey());
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.PART_COUNT, String.valueOf(record.getPartCount()));
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.UPLOAD_STATUS, String.valueOf(record.getUploadStatus()));
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.CONVERT_STATUS, String.valueOf(record.getConvertStatus()));
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.UPLOAD_ALL_URL, TextUtils.isEmpty(record.getUploadAllUrl()) ? "" : record.getUploadAllUrl());

            mSerializer.attribute("", DatabaseConstant.ConvertColumn.HISTORY_ROLENAME, TextUtils.isEmpty(record.getHistoryRoleName()) ? "" : record.getHistoryRoleName());
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.SERVER_PLAN_CODE, String.valueOf(record.getServerPlanCode()));
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.CAN_SHOW_SPEAKER_ROLE, String.valueOf(record.getCanShowSpeakerRole()));
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ISSHOWING, String.valueOf(record.getSpeakerRoleIsShowing()));
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.SPEAKER_ROLE_ORIGINAL_NUMBER, String.valueOf(record.getSpeakerRoleOriginalNumber()));
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.SPEAKER_ROLE_HAS_FIRSTSHOW, String.valueOf(record.getSpeakerRoleHasFirstshow()));

            mSerializer.attribute("", DatabaseConstant.ConvertColumn.IS_DIRECT_ON, Boolean.toString(record.getIsDirectOn()));
            mSerializer.attribute("", DatabaseConstant.ConvertColumn.DIRECT_TIME,
                    TextUtils.isEmpty(record.getDirectTime()) ? "" : record.getDirectTime());

            mSerializer.endTag("", DatabaseConstant.ROOT);
        } catch (IllegalArgumentException e) {
            DebugUtil.e(TAG, "addConvertRecord IllegalArgumentException error", e);
        } catch (IllegalStateException e) {
            DebugUtil.e(TAG, "addConvertRecord IllegalStateException error", e);
        } catch (IOException e) {
            DebugUtil.e(TAG, "addConvertRecord IOException error", e);
        } catch (NullPointerException e) {
            DebugUtil.e(TAG, "addConvertRecord NullPointerException error", e);
        }
    }

    public String getXmlInfo() {
        try {
            if (mStringWriter != null) {
                String info = mStringWriter.toString();
                mStringWriter.close();
                return info;
            }
        } catch (IOException e) {
            DebugUtil.e(TAG, "mStringWriter close error", e);
        }
        return null;
    }
}