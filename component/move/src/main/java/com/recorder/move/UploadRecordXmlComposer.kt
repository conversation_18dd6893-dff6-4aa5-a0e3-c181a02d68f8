package com.recorder.move

import android.text.TextUtils
import android.util.Xml
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.constant.DatabaseConstant
import com.soundrecorder.common.databean.UploadRecord
import org.xmlpull.v1.XmlSerializer
import java.io.IOException
import java.io.StringWriter

class UploadRecordXmlComposer {

    private val TAG = "UploadRecordXmlComposer"
    private var mSerializer: XmlSerializer? = null
    private var mStringWriter: StringWriter? = null

    fun startCompose(): Boolean {
        var result = false
        mSerializer = Xml.newSerializer()
        mStringWriter = StringWriter()
        try {
            mSerializer!!.setOutput(mStringWriter)
            mSerializer!!.startDocument(null, false)
            mSerializer!!.startTag("", "uploadRecord")
            result = true
        } catch (e: IOException) {
            DebugUtil.e(TAG, "startCompose IOException error", e)
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "startCompose IllegalArgumentException error", e)
        } catch (e: IllegalStateException) {
            DebugUtil.e(TAG, "startCompose IllegalStateException error", e)
        }
        return result
    }

    fun endCompose(): Boolean {
        var result = false
        try {
            mSerializer?.endTag("", "uploadRecord")
            mSerializer?.endDocument()
            result = true
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "endCompose IllegalArgumentException error", e)
        } catch (e: IllegalStateException) {
            DebugUtil.e(TAG, "endCompose IllegalStateException error", e)
        } catch (e: IOException) {
            DebugUtil.e(TAG, "endCompose IOException error", e)
        }
        return result
    }

    fun addUploadRecord(record: UploadRecord) {
        try {
            mSerializer?.startTag("", DatabaseConstant.ROOT)
            mSerializer?.attribute("", DatabaseConstant.UploadColumn.ONLY_ID, if (TextUtils.isEmpty(record.mOnlyId)) "" else record.mOnlyId)
            mSerializer?.attribute("", DatabaseConstant.UploadColumn.UPLOAD_URL, if (TextUtils.isEmpty(record.mUrl)) "" else record.mUrl)
            mSerializer?.attribute("", DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_START, record.mFileStartRange.toString())
            mSerializer?.attribute("", DatabaseConstant.UploadColumn.UPLOAD_FILE_RANG_END, record.mFileEndRange.toString())
            mSerializer?.attribute("", DatabaseConstant.UploadColumn.UPLOAD_ETAG, if (TextUtils.isEmpty(record.mETag)) "" else record.mETag)
            mSerializer?.attribute("", DatabaseConstant.UploadColumn.UPLOAD_SEQ_NUM, record.mSeqNumber.toString())
            mSerializer?.endTag("", DatabaseConstant.ROOT)
            DebugUtil.i(TAG, "addUploadRecord: " + record)
        } catch (e: IllegalArgumentException) {
            DebugUtil.e(TAG, "addConvertRecord IllegalArgumentException error", e)
        } catch (e: IllegalStateException) {
            DebugUtil.e(TAG, "addConvertRecord IllegalStateException error", e)
        } catch (e: IOException) {
            DebugUtil.e(TAG, "addConvertRecord IOException error", e)
        } catch (e: NullPointerException) {
            DebugUtil.e(TAG, "addConvertRecord NullPointerException error", e)
        }
    }

    fun getXmlInfo(): String? {
        try {
            if (mStringWriter != null) {
                val info = mStringWriter.toString()
                mStringWriter!!.close()
                return info
            }
        } catch (e: IOException) {
            DebugUtil.e(TAG, "mStringWriter close IOException error", e)
        }
        return null
    }
}