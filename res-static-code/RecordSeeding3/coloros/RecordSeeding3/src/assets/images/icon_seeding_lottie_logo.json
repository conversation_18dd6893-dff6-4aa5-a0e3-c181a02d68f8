{"v": "5.9.6", "fr": 30, "ip": 0, "op": 76, "w": 78, "h": 60, "nm": "小状态栏波音频波形Lottie", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "矩形 白色 3", "sr": 1, "ks": {"o": {"k": [{"s": [0], "t": 5, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [4.059], "t": 6, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.62], "t": 7, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 8, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 13, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.59], "t": 14, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 15, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 20, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [4.059], "t": 21, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.62], "t": 22, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 23, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 28, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.59], "t": 29, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 30, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 35, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [4.059], "t": 36, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.62], "t": 37, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 38, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 43, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.59], "t": 44, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 45, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 50, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [4.059], "t": 51, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.62], "t": 52, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 53, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 58, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.59], "t": 59, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 60, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 65, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [4.059], "t": 66, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [22.62], "t": 67, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 68, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 73, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [12.59], "t": 74, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 75, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"k": [{"s": [85, 30, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [83.533, 30, 0], "t": 1, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [80.587, 30, 0], "t": 3, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [79.132, 30, 0], "t": 4, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [77.672, 30, 0], "t": 5, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [76.2, 30, 0], "t": 6, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [74.738, 30, 0], "t": 7, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [84.259, 30, 0], "t": 8, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [82.8, 30, 0], "t": 9, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [81.321, 30, 0], "t": 10, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [79.858, 30, 0], "t": 11, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [78.404, 30, 0], "t": 12, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [76.936, 30, 0], "t": 13, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [75.467, 30, 0], "t": 14, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [85, 30, 0], "t": 15, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [83.533, 30, 0], "t": 16, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [80.587, 30, 0], "t": 18, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [79.132, 30, 0], "t": 19, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [77.672, 30, 0], "t": 20, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [76.2, 30, 0], "t": 21, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [74.738, 30, 0], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [84.259, 30, 0], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [82.8, 30, 0], "t": 24, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [81.321, 30, 0], "t": 25, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [79.858, 30, 0], "t": 26, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [78.404, 30, 0], "t": 27, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [76.936, 30, 0], "t": 28, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [75.467, 30, 0], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [85, 30, 0], "t": 30, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [83.533, 30, 0], "t": 31, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [80.587, 30, 0], "t": 33, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [79.132, 30, 0], "t": 34, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [77.672, 30, 0], "t": 35, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [76.2, 30, 0], "t": 36, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [74.738, 30, 0], "t": 37, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [84.259, 30, 0], "t": 38, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [82.8, 30, 0], "t": 39, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [81.321, 30, 0], "t": 40, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [79.858, 30, 0], "t": 41, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [78.404, 30, 0], "t": 42, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [76.936, 30, 0], "t": 43, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [75.467, 30, 0], "t": 44, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [85, 30, 0], "t": 45, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [83.533, 30, 0], "t": 46, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [80.587, 30, 0], "t": 48, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [79.132, 30, 0], "t": 49, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [77.672, 30, 0], "t": 50, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [76.2, 30, 0], "t": 51, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [74.738, 30, 0], "t": 52, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [84.259, 30, 0], "t": 53, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [82.8, 30, 0], "t": 54, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [81.321, 30, 0], "t": 55, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [79.858, 30, 0], "t": 56, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [78.404, 30, 0], "t": 57, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [76.936, 30, 0], "t": 58, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [75.467, 30, 0], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [85, 30, 0], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [83.533, 30, 0], "t": 61, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [80.587, 30, 0], "t": 63, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [79.132, 30, 0], "t": 64, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [77.672, 30, 0], "t": 65, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [76.2, 30, 0], "t": 66, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [74.738, 30, 0], "t": 67, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [84.259, 30, 0], "t": 68, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [82.8, 30, 0], "t": 69, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [81.321, 30, 0], "t": 70, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [79.858, 30, 0], "t": 71, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [78.404, 30, 0], "t": 72, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [76.936, 30, 0], "t": 73, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [75.467, 30, 0], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [85, 30, 0], "t": 75, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-41.58, 41.58, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "矩形 白色 2", "sr": 1, "ks": {"o": {"a": 0, "k": 30, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"k": [{"s": [74, 30, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [72.533, 30, 0], "t": 1, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [71.064, 30, 0], "t": 2, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [69.596, 30, 0], "t": 3, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.142, 30, 0], "t": 4, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [66.679, 30, 0], "t": 5, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [65.2, 30, 0], "t": 6, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [63.741, 30, 0], "t": 7, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [73.262, 30, 0], "t": 8, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [71.8, 30, 0], "t": 9, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [70.328, 30, 0], "t": 10, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.868, 30, 0], "t": 11, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [67.413, 30, 0], "t": 12, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [64.467, 30, 0], "t": 14, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [74, 30, 0], "t": 15, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [72.533, 30, 0], "t": 16, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [71.064, 30, 0], "t": 17, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [69.596, 30, 0], "t": 18, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.142, 30, 0], "t": 19, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [66.679, 30, 0], "t": 20, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [65.2, 30, 0], "t": 21, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [63.741, 30, 0], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [73.262, 30, 0], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [71.8, 30, 0], "t": 24, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [70.328, 30, 0], "t": 25, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.868, 30, 0], "t": 26, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [67.413, 30, 0], "t": 27, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [64.467, 30, 0], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [74, 30, 0], "t": 30, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [72.533, 30, 0], "t": 31, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [71.064, 30, 0], "t": 32, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [69.596, 30, 0], "t": 33, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.142, 30, 0], "t": 34, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [66.679, 30, 0], "t": 35, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [65.2, 30, 0], "t": 36, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [63.741, 30, 0], "t": 37, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [73.262, 30, 0], "t": 38, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [71.8, 30, 0], "t": 39, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [70.328, 30, 0], "t": 40, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.868, 30, 0], "t": 41, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [67.413, 30, 0], "t": 42, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [64.467, 30, 0], "t": 44, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [74, 30, 0], "t": 45, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [72.533, 30, 0], "t": 46, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [71.064, 30, 0], "t": 47, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [69.596, 30, 0], "t": 48, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.142, 30, 0], "t": 49, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [66.679, 30, 0], "t": 50, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [65.2, 30, 0], "t": 51, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [63.741, 30, 0], "t": 52, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [73.262, 30, 0], "t": 53, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [71.8, 30, 0], "t": 54, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [70.328, 30, 0], "t": 55, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.868, 30, 0], "t": 56, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [67.413, 30, 0], "t": 57, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [64.467, 30, 0], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [74, 30, 0], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [72.533, 30, 0], "t": 61, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [71.064, 30, 0], "t": 62, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [69.596, 30, 0], "t": 63, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.142, 30, 0], "t": 64, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [66.679, 30, 0], "t": 65, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [65.2, 30, 0], "t": 66, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [63.741, 30, 0], "t": 67, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [73.262, 30, 0], "t": 68, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [71.8, 30, 0], "t": 69, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [70.328, 30, 0], "t": 70, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [68.868, 30, 0], "t": 71, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [67.413, 30, 0], "t": 72, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [64.467, 30, 0], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [74, 30, 0], "t": 75, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-41.58, 41.58, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "矩形 白色 4", "sr": 1, "ks": {"o": {"k": [{"s": [30], "t": 3, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [25.417], "t": 4, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [5.154], "t": 5, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 6, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 7, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 8, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 11, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.484], "t": 12, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 13, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 14, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 15, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 18, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [25.417], "t": 19, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [5.154], "t": 20, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 21, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 22, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 23, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 26, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.484], "t": 27, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 28, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 29, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 30, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 33, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [25.417], "t": 34, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [5.154], "t": 35, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 36, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 37, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 38, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 41, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.484], "t": 42, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 43, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 44, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 45, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 48, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [25.417], "t": 49, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [5.154], "t": 50, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 51, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 52, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 53, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 56, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.484], "t": 57, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 58, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 59, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 60, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 63, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [25.417], "t": 64, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [5.154], "t": 65, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 66, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 67, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 68, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 71, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [15.484], "t": 72, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 73, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [0], "t": 74, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}, {"s": [30], "t": 75, "i": {"x": [1], "y": [1]}, "o": {"x": [0], "y": [0]}}]}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"k": [{"s": [62.963, 30, 0], "t": 0, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.764, 30, 0], "t": 1, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.368, 30, 0], "t": 3, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [58.181, 30, 0], "t": 4, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [56.987, 30, 0], "t": 5, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [55.78, 30, 0], "t": 6, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [54.589, 30, 0], "t": 7, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.355, 30, 0], "t": 8, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.167, 30, 0], "t": 9, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.965, 30, 0], "t": 10, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [58.774, 30, 0], "t": 11, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [57.586, 30, 0], "t": 12, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [55.181, 30, 0], "t": 14, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.963, 30, 0], "t": 15, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.764, 30, 0], "t": 16, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.368, 30, 0], "t": 18, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [58.181, 30, 0], "t": 19, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [56.987, 30, 0], "t": 20, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [55.78, 30, 0], "t": 21, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [54.589, 30, 0], "t": 22, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.355, 30, 0], "t": 23, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.167, 30, 0], "t": 24, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.965, 30, 0], "t": 25, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [58.774, 30, 0], "t": 26, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [57.586, 30, 0], "t": 27, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [55.181, 30, 0], "t": 29, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.963, 30, 0], "t": 30, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.764, 30, 0], "t": 31, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.368, 30, 0], "t": 33, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [58.181, 30, 0], "t": 34, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [56.987, 30, 0], "t": 35, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [55.78, 30, 0], "t": 36, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [54.589, 30, 0], "t": 37, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.355, 30, 0], "t": 38, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.167, 30, 0], "t": 39, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.965, 30, 0], "t": 40, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [58.774, 30, 0], "t": 41, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [57.586, 30, 0], "t": 42, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [55.181, 30, 0], "t": 44, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.963, 30, 0], "t": 45, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.764, 30, 0], "t": 46, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.368, 30, 0], "t": 48, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [58.181, 30, 0], "t": 49, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [56.987, 30, 0], "t": 50, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [55.78, 30, 0], "t": 51, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [54.589, 30, 0], "t": 52, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.355, 30, 0], "t": 53, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.167, 30, 0], "t": 54, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.965, 30, 0], "t": 55, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [58.774, 30, 0], "t": 56, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [57.586, 30, 0], "t": 57, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [55.181, 30, 0], "t": 59, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.963, 30, 0], "t": 60, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.764, 30, 0], "t": 61, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.368, 30, 0], "t": 63, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [58.181, 30, 0], "t": 64, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [56.987, 30, 0], "t": 65, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [55.78, 30, 0], "t": 66, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [54.589, 30, 0], "t": 67, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.355, 30, 0], "t": 68, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [61.167, 30, 0], "t": 69, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [59.965, 30, 0], "t": 70, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [58.774, 30, 0], "t": 71, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [57.586, 30, 0], "t": 72, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [55.181, 30, 0], "t": 74, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"s": [62.963, 30, 0], "t": 75, "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-41.58, 41.58, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [12, 12], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "番茄红", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [50.989, 30, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [41.58, 41.58, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [14, 84], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.992156863213, 0.23137255013, 0.23137255013, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 番茄红", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 3, "nm": "位置变化", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [43.589, 30, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 75, "s": [-52.9, 30, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [41.58, 41.58, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 149.5, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "矩形 白色 11112", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 69.5, "s": [0]}, {"t": 71, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [271, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [14, 14], "t": 69, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.333], "t": 70, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 45], "t": 71, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 66.935], "t": 72, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 66.651], "t": 73, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 60.589], "t": 74, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 54.68], "t": 75, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "矩形 白色 111", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 60, "s": [0]}, {"t": 61.5, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [242, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [14, 14], "t": 60, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 20.667], "t": 61, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 28.986], "t": 62, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 31.482], "t": 63, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 30.07], "t": 64, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 28.02], "t": 65, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 26.368], "t": 66, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 25.277], "t": 67, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.637], "t": 68, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.292], "t": 69, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.12], "t": 70, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.042], "t": 71, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.009], "t": 72, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "矩形 白色 110", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 50.5, "s": [0]}, {"t": 52, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [213, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [14, 14], "t": 50, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 23], "t": 51, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 41], "t": 52, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 60.105], "t": 53, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 59.858], "t": 54, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 54.577], "t": 55, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 49.431], "t": 56, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 45.743], "t": 57, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 43.459], "t": 58, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 42.177], "t": 59, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 41.514], "t": 60, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 41.197], "t": 61, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 41.059], "t": 62, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 41.006], "t": 63, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "矩形 白色 19", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 41.5, "s": [0]}, {"t": 43, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [184, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [14, 14], "t": 0, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 14], "t": 75, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "矩形 白色 18", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 32, "s": [0]}, {"i": {"x": [0.684], "y": [1]}, "o": {"x": [0.329], "y": [0]}, "t": 33.5, "s": [100]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 63.5, "s": [100]}, {"t": 65.509765625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [155, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [14, 14], "t": 0, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 14], "t": 75, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "矩形 白色 17", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 22.5, "s": [0]}, {"i": {"x": [0.684], "y": [1]}, "o": {"x": [0.329], "y": [0]}, "t": 24, "s": [100]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 54.5, "s": [100]}, {"t": 56.509765625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [126, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic Controller", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [14, 14], "t": 22, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 17.333], "t": 23, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24], "t": 24, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 31.076], "t": 25, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 30.984], "t": 26, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 29.029], "t": 27, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 27.123], "t": 28, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 25.757], "t": 29, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.911], "t": 30, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.436], "t": 31, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.19], "t": 32, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.073], "t": 33, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.022], "t": 34, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.002], "t": 35, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24], "t": 54, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 22.427], "t": 55, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 15.573], "t": 56, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 12.927], "t": 57, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 12.39], "t": 58, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 12.693], "t": 59, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.135], "t": 60, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.49], "t": 61, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.725], "t": 62, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.863], "t": 63, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.937], "t": 64, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.974], "t": 65, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.991], "t": 66, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "矩形 白色 16", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 13, "s": [0]}, {"i": {"x": [0.684], "y": [1]}, "o": {"x": [0.329], "y": [0]}, "t": 14.5, "s": [100]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 45, "s": [100]}, {"t": 47.009765625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [97, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [14, 14], "t": 13, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 36], "t": 14, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 63.454], "t": 15, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 71.689], "t": 16, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 67.032], "t": 17, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 60.265], "t": 18, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 54.813], "t": 19, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 51.215], "t": 20, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 49.101], "t": 21, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 47.963], "t": 22, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 47.397], "t": 23, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 47.138], "t": 24, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 47.031], "t": 25, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 47], "t": 45, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 14], "t": 47, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 8.974], "t": 48, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 9.039], "t": 49, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 10.428], "t": 50, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 11.782], "t": 51, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 12.752], "t": 52, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.353], "t": 53, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.69], "t": 54, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.865], "t": 55, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.948], "t": 56, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.985], "t": 57, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.998], "t": 58, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "矩形 白色 15", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 4, "s": [0]}, {"i": {"x": [0.684], "y": [1]}, "o": {"x": [0.329], "y": [0]}, "t": 5.5, "s": [100]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 35.5, "s": [100]}, {"t": 37.509765625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [68, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [14, 14], "t": 4, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 20.667], "t": 5, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 28.986], "t": 6, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 31.482], "t": 7, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 30.07], "t": 8, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 28.02], "t": 9, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 26.368], "t": 10, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 25.277], "t": 11, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.637], "t": 12, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.292], "t": 13, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.12], "t": 14, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.042], "t": 15, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24.009], "t": 16, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 24], "t": 35, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 22.427], "t": 36, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 15.573], "t": 37, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 12.927], "t": 38, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 12.39], "t": 39, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 12.693], "t": 40, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.135], "t": 41, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.49], "t": 42, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.725], "t": 43, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.863], "t": 44, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.937], "t": 45, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.974], "t": 46, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 13.991], "t": 47, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "矩形 白色 5", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": -5.5, "s": [0]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": -4, "s": [100]}, {"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 26.5, "s": [100]}, {"t": 28.509765625, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [39, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 大小 - 矩形路径 1", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 50, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 200, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"k": [{"s": [14, 54.68], "t": 0, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 50.445], "t": 1, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 47.823], "t": 2, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 46.351], "t": 3, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 45.59], "t": 4, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 45.226], "t": 5, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 45.067], "t": 6, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 45.007], "t": 7, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [14, 45], "t": 26, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [13.363, 34.492], "t": 27, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12.235, 15.884], "t": 28, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [11.835, 9.282], "t": 29, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [11.749, 7.866], "t": 30, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [11.796, 8.636], "t": 31, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [11.865, 9.77], "t": 32, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [11.92, 10.685], "t": 33, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [11.957, 11.29], "t": 34, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [11.979, 11.646], "t": 35, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [11.99, 11.838], "t": 36, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [11.996, 11.933], "t": 37, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [11.999, 11.977], "t": 38, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}, {"s": [12, 11.995], "t": 39, "i": {"x": [1, 1], "y": [1, 1]}, "o": {"x": [0, 0], "y": [0, 0]}}]}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 124.5, "st": -25, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "矩形 白色 6", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 17, "s": [100]}, {"t": 19, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [10, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 1, "k": [{"i": {"x": [0.67, 0.67], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0, 0]}, "t": 17, "s": [14, 24]}, {"t": 19, "s": [12, 12]}], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "矩形 白色 14", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 7.5, "s": [100]}, {"t": 9.5, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-19, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 1, "k": [{"i": {"x": [0.67, 0.67], "y": [1, 1]}, "o": {"x": [0.33, 0.33], "y": [0, 0]}, "t": 7.5, "s": [14, 41]}, {"t": 9.5, "s": [12, 12]}], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "矩形 白色 13", "parent": 5, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.67], "y": [1]}, "o": {"x": [0.33], "y": [0]}, "t": 0, "s": [100]}, {"t": 0.5, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-48, 50, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [14, 14], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 7, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "矩形 白色", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 149.5, "st": 0, "ct": 1, "bm": 0}], "markers": [{"tm": 26.5, "cm": "1", "dr": 0}]}