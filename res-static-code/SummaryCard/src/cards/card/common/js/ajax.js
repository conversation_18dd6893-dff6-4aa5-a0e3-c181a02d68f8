import $fetch from '@system.fetch'
import { queryString } from './utils'

/**
 * 使用 fetch 方法发起网络请求
 * @param params 请求参数对象
 * @return {Promise} 返回 promise 对象
 */
function requestHandle(params) {
  return new Promise((resolve, reject) => {
    $fetch.fetch({
      url: params.url,
      method: params.method,
      data: params.data,
      success: response => {
        // 可根据接口数据结构自定义处理逻辑
        resolve(response)
        console.info('request success', response)
      },
      fail: (err, code) => {
        reject({
          code,
          err
        })
        console.info(`request fail, code = ${code}`, err)
      },
      complete: () => {
        console.info(`request @${params.url} has been completed.`)
      }
    })
  })
}

export default {
  /**
   * post 请求方法
   * @param url 请求地址
   * @param params 请求参数对象
   * @return {Promise} 返回 promise 对象
   */
  post: function(url, params = {}) {
    return requestHandle({
      method: 'POST',
      url: url,
      data: params
    })
  },
  /**
   * get 请求方法
   * @param url 请求地址
   * @param params 请求参数对象
   * @return {Promise} 返回 promise 对象
   */
  get: function(url, params = {}) {
    return requestHandle({
      method: 'GET',
      url: queryString(url, params)
    })
  }
}
