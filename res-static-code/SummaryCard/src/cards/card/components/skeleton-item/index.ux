<template>
  <div class="skeleton" style="{{ skeletonStyle }}" forcedark="false"></div>
</template>

<script>
import { SIZE_FACTOR } from '../../common/js/common.js'

export default {
  props: {
    isDark: {
      type: Boolean,
      default: false
    },
    width: {
      type: [Number, String],
      default: 0
    },
    height: {
      type: [Number, String],
      default: 0
    },
    marginTop: {
      type: Number,
      default: 0
    },
    borderRadius: {
      type: [Number, String],
      default: 5
    }
  },
  computed: {
    skeletonStyle() {
      const backgroundColor = this.isDark ? '#404040' : '#f8f8f8'
      return {
        width:
          typeof this.width === 'number'
            ? `${this.width * SIZE_FACTOR}px`
            : this.width,
        height:
          typeof this.height === 'number'
            ? `${this.height * SIZE_FACTOR}px`
            : this.height,
        borderRadius:
          typeof this.borderRadius === 'number'
            ? `${this.borderRadius * SIZE_FACTOR}px`
            : this.borderRadius,
        marginTop: `${this.marginTop * SIZE_FACTOR}px`,
        backgroundColor
      }
    }
  }
}
</script>
