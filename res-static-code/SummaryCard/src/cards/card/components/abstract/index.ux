<import name="generate-todo" src="./generateAbstract.ux"></import>
<import name="generate-doing" src="./generating.ux"></import>
<import name="generate-done" src="./generated.ux"></import>
<import name="error-page" src="../error-page/index.ux"></import>
 
<template>
  <div>
    <div class="loading-container" if="{{page === 'loading'}}">
      <div class="first"></div>
      <div class="second"></div>
      <div class="third"></div>
    </div>
    <error-page
      elif="{{page === 'error'}}"
      description="{{errorInfo}}"
      title="{{errorTitle}}"
      is-dark="{{ isDark }}"
      type="{{ recordObj.type }}"
    ></error-page>
    <div else class="container">
      <component
        is="{{ 'generate-' + status }}"
        is-dark="{{ isDark }}"
        type="{{ recordObj.type }}"
        title="{{ recordObj.title }}"
        onchange-status="changeStatus"
        percent="{{ percent }}"
        stasusCode="{{statusCode}}"
      ></component>
    </div>
  </div>
</template> 

<script>
import Channel from '../../common/js/channel.js'
import file from '@system.file'
import sharedStorage from '@hap.sharedStorage'
import storage from '@system.storage'
import router from '@system.router';
import { formatTime } from '../../common/utils'
export default {
  name: 'LoadingComponent',
  props: {
    isDark: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      status: 'todo',
      isLoading: true,
      errorInfo: '',
      errorTitle: '',
      hasInited: false,
      percent: 50,
      recordObj: {
        type: 'abstract',
        title: '',
        code: ''
      },
      recordData: '',
      jumpUri: '',
      statusCode: '',
      statusHandlers: {
        2000: { status: 'error', type: 'abstract' },  // 失败
        2001: { status: 'todo', type: 'abstract' },   // 转摘要
        2002: { status: 'todo', type: 'text' },  // 转文本
        2003: { status: 'done', type: 'abstract' },  // 摘要生成成功 查看摘要页面
        2004: { status: 'done', type: 'text' },  // 文本生成成功 查看文本
        2005: { status: 'doing', type: 'abstract' },   // 摘要生成中
        2006: { status: 'doing', type: 'text' },  // 文本生成中
        2007: { status: 'done', type: 'abstract' },  // 摘要生成之后，检测是否有文件可以跳转
        3000: { status: 'error', type: 'text' },  //  转文本失败 
        3001: { status: 'error', type: '' },   // 失败
      },
      persistentTitle: '',
      persistentTime: 0,
      channelIsClose: false,
    }
  },
  computed: {
    page() {
      if (this.isLoading) {
        return 'loading'
      }
      if (this.errorTitle) {
        return 'error'
      }
      return 'content'
    },
  },
  changeStatus(val) {
    this.sendChannel(this.recordData, val.detail)
  },
  async cardShowSend(code) {
    const reqId = Math.random()
      .toString(36)
      .slice(2)
    try {
      await this.Channel.send(reqId, this.recordData, code)
    } catch (error) {
      console.log('发送数据失败')
    }
  },
  async sendChannel(val, status) {
    const reqId = Math.random()
      .toString(36)
      .slice(2)
    try {
      if (this.status === 'done') {
        await this.Channel.send(reqId, val, this.recordObj.type === 'abstract' ? '1003' : '1007')
      } else if (this.status === 'todo') {
        await this.Channel.send(reqId, val, this.recordObj.type === 'abstract' ? '1001' : '1002')
      } else if (this.status === 'doing') {
        this.status = status
      }
    } catch (error) {
      console.log('发送数据失败')
    }
  },
  handleChannelError(code, data) {
    this.isLoading = false
    console.error(
      `create Channel error: code=${code}, data=${data}`
    )
    this.hasInited = true
    this.createChannelFailed = true
    this.errorTitle = '摘要生成失败'
    this.errorInfo = data
  },
  handleMessage(msgData) {
    try {
      console.log('需要处理的数据', JSON.stringify(msgData), typeof msgData)
      if (msgData.title) this.recordData = msgData
      if (msgData.title) this.persistentTitle = msgData.title
      if (msgData.time) this.persistentTime = msgData.time
      if (msgData.uri) this.jumpUri = msgData.uri
      this.statusCode = msgData.status;
      this.errorTitle = ''
      console.log(this.statusCode, typeof this.statusCode)
      // 进度处理
      if ([2005, 2006].includes(this.statusCode)) {
        this.percent = Number(msgData.progress)
        // 处理异常信息
      } else if ([2000, 3000, 3001].includes(this.statusCode)) {
        this.statusCode === 2000 ? this.handleChannelError(this.statusCode, msgData.errorMessage) : this.handleChannelError(this.statusCode, this.statusCode === 3000 ? '转文本生成失败' : '摘要生成失败')
        // 查看摘要的时候，判断是否跳转
      } else if (this.statusCode === 2007) {
        if (msgData.checkFileResult) {
          const res = router.push({
            uri: this.jumpUri,
          })
          console.log(res, 'res');
          return
        }
      } else {
        this.status = this.statusHandlers[this.statusCode].status
      }
      this.status = this.statusHandlers[this.statusCode].status
      // 统一生成标题
      const generateTitle = () => {
        const cleanTitle = this.persistentTitle ? this.persistentTitle.split('.')[0] : '';
        const formattedTime = this.persistentTime ?
          formatTime(this.persistentTime) : '';
        return `${cleanTitle}-${formattedTime}`;
      };
      // 更新记录对象
      this.recordObj = {
        type: this.statusHandlers[this.statusCode].type,
        title: generateTitle()
      };
    } catch (error) {
      console.log('处理数据报错了，', error)
      this.handleChannelError(this.statusCode, JSON.parse(msgData).errorMessage)
    }
  },
  createChannel() {
    const msgCallback = msg => {
      this.isLoading = false;
      console.log(`onmessage Channel <<<------>>> ${JSON.stringify(msg)},date=${new Date()}`);
      this.hasInited = true;
      const msgData = JSON.parse(msg.msgData);
      this.handleMessage(msgData)
    };

    const channelOpenCallback = () => {
      console.log('Channel实例Id', this.Channel._instId)
      this.channelOpenTime = new Date().getTime()
      this.getDataByChannel()
      this.channelIsClose = false
    }
    const channelErrorCallback = (code, data) => {
      console.log('Channel实例Id', this.Channel._instId)
      this.handleChannelError(code, data)
    }

    const channelCloseCallback = () => {
      this.channelIsClose = true
      console.log('Channel实例Id', this.Channel._instId)
      console.log('channel通道关闭，调用sharedStorage')
      this.getSharedStorageData()
    }
    this.Channel = new Channel('demo', msgCallback, channelOpenCallback, channelErrorCallback)

    // channel初始化超时
    setTimeout(() => {
      if (!this.channelOpenTime) {
        console.log(`Channel异常无回调,兜底处理`)
        this.hasInited = true
        this.isLoading = false
        this.createChannelFailed = true
        this.errorTitle = '获取数据异常'
        console.log('Channel实例Id', this.Channel._instId)
        console.log('获取sharedStorageData在210 index.ux行')
        this.getSharedStorageData()
      }
    }, 3000)
  },
  getSharedStorageData() {
    console.log('调用sharedStorage222222222com.coloros.soundrecorder')
    sharedStorage.getSharedData({
      key: 'audio_file_info_data',
      srcPkg: 'com.coloros.soundrecorder',
      success: (data) => {
        this.isLoading = false
        console.log(`获取sharedstorage数据成功: ${data}`)
        this.handleMessage(JSON.parse(data))
      },
      fail: (data, code) => {
        this.isLoading = false
        this.errorTitle = '获取数据异常'
        console.log(`获取sharedstorage数据失败, code = ${code}`)
      }
    })
  },
  async getDataByChannel() {
    try {
      if (!this.Channel) {
        console.error(`Channel this.Channel invalid, return`)
        return
      }
      const reqId = Math.random()
        .toString(36)
        .slice(2)
      const data = {
        key: 'value'
      }
      const res = await this.Channel.send(reqId, data)
      console.log(res, 'res')
    } catch (error) {
      console.log('getDataByChannel error >>> ', JSON.stringify(error))
    } finally {
      this.isLoading = false
    }
  },
  closeChannel() {
    try {
      this.Channel && this.Channel.close()
    } catch (e) {
      console.error('onDestroy >>> error')
    }
  },
  onDestroy() {
    this.closeChannel()
    this.cardShowSend(1006)
  },
  onInit() {
    console.log('卡片初始化1111111')
    this.createChannel()
    this.$watch('status', 'watchPropsChange')
  },
  watchPropsChange(newV, oldV) {
    if (newV) {
      storage.set({
        key: 'status',
        value: this.status,
        success: (res) => {
        }
      })
    } else if (newV === 'todo') {
      this.percent = 0
    }
  },
  pageLifecycles: {
    show() {
      console.log('卡片show方法', 11111111111111111)
      this.cardShowSend(1004)
      if (this.channelIsClose) {
        this.getSharedStorageData()
      }
      if (!this.hasInited) {
        return
      }
    },
    hide() {
      this.cardShowSend(1005)
    }
  }
}

</script>

<style scoped lang="less">
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  width: 100%;
  height: 100%;
  padding: 16dp;
  & > div {
    height: 22dp;
    width: 22dp;
  }
  .first {
    background-color: #e5e5e5;
    width: 22dp;
    height: 22dp;
    border-radius: 6dp;
  }

  .second {
    background-color: #e5e5e5;
    width: 100%;
    height: 40dp;
    border-radius: 3dp;
    margin-top: 10dp;
  }

  .third {
    background-color: #e5e5e5;
    border-radius: 15dp;
    width: 100%;
    height: 32dp;
    border-radius: 14dp;
    gap: 12dp;
    padding-top: 8dp;
    padding-right: 16dp;
    padding-bottom: 8dp;
    padding-left: 16dp;
    margin-top: 14dp;
  }
}

.loading-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 10dp;
  padding: 20dp;
  box-shadow: 0 2dp 10dp rgba(0, 0, 0, 0.1);
  background-color: #ffffff; /* 盒子背景颜色 */
}

.loading-icon {
  width: 60dp; /* 设置图标宽度 */
  height: 60dp; /* 设置图标高度 */
  background-color: #f0f0f0; /* 图标颜色 */
  border-radius: 10%; /* 圆角 */
  margin-bottom: 10dp; /* 图标与文字之间的空间 */
}

.loading-text {
  font-size: 16dp; /* 文字大小 */
  color: #999999; /* 文字颜色 */
}
.container {
  height: 100%;
  width: 100%;
}
</style>
 
 