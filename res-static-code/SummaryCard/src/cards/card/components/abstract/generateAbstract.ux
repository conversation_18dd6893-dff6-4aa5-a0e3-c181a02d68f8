<template>
  <div class="content">
    <div class="img-content">
      <image  src="../../images/{{isDark ? 'dark-state' :'light-state'}}.svg" class="imgs" ></image>
    </div>
    <div class="null"></div>
    <div class="text-content">
      <text class="text" style="color:{{ isDark ? 'white' : 'black' }}">{{ contentText.replace(" ",'')}}</text>
    </div>
    <div class="btn-content" style="background-image: {{ bgImageUrl }}" >
      <text class="btn-text {{ bgColor }}" onclick="generateAbstract">{{ type === 'text' ? $t('message.generateAbstract.transfer_text'):$t('message.generateAbstract.generate_summary') }}</text>
      </div>
    </div>
  </div>
</template>

<script>
import { formatTime } from '../../common/utils.js'
export default {
  props: {
    isDark: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      default: 'abstract'
    },
    title: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      length: this.title.length,
      indexOf: this.title.indexOf('-')
    }
  },
  computed: {
    imageUrl() {
      if (this.isDark) {
        return '/cards/card/images/dark-state.svg'
      }
      return '/cards/card/images/light-state.svg'
    },
    // 动态背景图
    bgImageUrl() {
      if (this.isDark) {
        return '/cards/card/images/generateAbstractbg2.png'
      } else {
        return '/cards/card/images/generateAbstractbg1.png'
      }
    },
    bgColor() {
      if (this.isDark) {
        return 'dark-bg'
      }
      return 'light-bg'
    },
    contentText() {
      return this.title
    },

    btnText() {
      if (this.type === 'abstract') {
        return '生成摘要' //生成摘要
      }
      return '转文本'  //转文本
    }
  },
  generateAbstract() {
    this.$emit('changeStatus', 'doing')
  },

}
</script>
<style lang="less" scoped>
.content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding-top: 16dp;
  .img-content {
    width: 100%;
    height: 12dp;
    display: flex;
    align-items: center;
    justify-content: center;
    .imgs {
      height: 12dp;
      width: 25dp;
      object-fit: contain;
    }
  }
  .null {
    width: 100%;
    height: 6dp;
  }

  .text-content {
    align-self: center;
    width: 100%;
    text-align: center;
    flex-wrap: wrap;
    display: flex;
    justify-content: center;
    align-content: flex-start;
    padding: 0dp 10dp;
    max-height: 42dp;

    .text {
      font-family: OPPO Sans 4 SC;
      font-size: 16dp;
      font-weight: 600;
      line-height: 20dp;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      lines: 2;
      text-overflow: ellipsis;
    }
  }
  .btn-content {
    width: 100%;
    height: 64dp;
    display: flex;
    position: fixed;
    bottom: 0dp;
    flex-direction: column;
    align-content: center;
    justify-content: center;
    /* margin-top: 10dp; */
    .btn-text {
      align-self: center;
      height: 32dp;
      width: 116dp;
      border-radius: 100dp;
      lines: 2;
      text-overflow: ellipsis;
      font-family: OPPO Sans 4;
      font-size: 12dp;
      font-weight: 500;
      line-height: 16dp;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }
    .dark-bg {
      background-color: #353f3e;
      color: rgba(255, 255, 255, 0.9);
    }
    .light-bg {
      background-color: #ffffff;
      color: rgba(0, 0, 0, 0.9);
    }
  }
}
</style>
