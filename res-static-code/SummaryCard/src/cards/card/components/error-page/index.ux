<template>
  <div class="widgetui-error">
    <div class="top-content" id="top-content">
      <div class="img">
        <image
          @resize="changeWidth"
          style="width: {{ iconWidth }}; height: {{ iconWidth }}"
          src="../../images/error-state.png"
        ></image>
      </div>
    </div>
    <div class="null"></div>
    <div class="text-content">
      <text class="text {{ bgColor }}">{{
        type === 'text'
          ? $t('message.generated.convert_text_error')
          : $t('message.generated.summarize_generate_error')
      }}</text>
    </div>
    <div style="height: 2%"></div>
    <div class="description">
      <text class="text {{ bgColor }}">{{ description }}</text>
    </div>
  </div>
</template>

<script>
/**
 * @file 缺省页组件
 */
// const widgetType = {
//   small: 'small',
//   medium: 'medium',
//   large: 'large',
// }

export default {
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '', // 	录音过短，不支持摘要生成
    },
    titleColor: {
      type: String,
      default: '',
    },
    description: {
      type: String,
      default: '',
    },
    descriptionColor: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      iconWidth: '0',
    }
  },
  computed: {
    titleStyle() {
      if (this.titleColor) {
        return {
          color: this.titleColor,
        }
      }

      return {
        color: this.isDark
          ? 'rgba(255, 255, 255, 0.85)'
          : 'rgba(0, 0, 0, 0.85)',
      }
    },

    descriptionTextStyle() {
      if (this.descriptionColor) {
        return {
          color: this.descriptionColor,
        }
      }

      return {
        color: this.isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
      }
    },

    imageStyle() {
      let width = 120,
        height = 90

      if (this.type === widgetType.large) {
        width = 168
        height = 120
      }

      return {
        marginTop: this.type === widgetType.large ? '0' : '20px',
        width: `${width}px`,
        height: `${height}px`,
      }
    },
    bgColor() {
      if (this.isDark) {
        return 'dark-bg'
      }
      return 'light-bg'
    },
  },
  changeWidth() {
    const that = this
    this.$element('top-content').getBoundingClientRect({
      success: function (data) {
        const { top, bottom, left, right, width, height } = data
        const imgWidth = height
        that.iconWidth = imgWidth + 'px'
      },
      fail: (errorData, errorCode) => {
        prompt.showToast({
          message: `错误原因：${JSON.stringify(
            errorData
          )}, 错误代码：${errorCode}`,
        })
      },
      complete: function () {
        console.info('complete')
      },
    })
  },
}
</script>

<style lang="less">
.widgetui-error {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 16dp;
  justify-content: center;
  align-content: center;
  flex-direction: column;
  .top-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-content: center;
    height: 34%;
  }
  .img {
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    image {
      width: 0;
      height: auto;
    }
  }
  .null {
    width: 100%;
    height: 6.8%;
  }
  .text-content {
    width: 100%;
    align-self: center;
    text-align: center;
    flex-wrap: wrap;
    display: flex;
    justify-content: center;
    align-items: center;
    .text {
      font-family: OPPO Sans 4 SC;
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      lines: 2;
      text-overflow: ellipsis;
    }
    .dark-bg {
      color: #ffffff;
    }
    .light-bg {
      color: #000000;
    }
  }

  .description {
    width: 100%;
    display: flex;
    justify-content: center;
    .text {
      font-size: 12px;
      /* padding-top: 16px; */
      line-height: 16px;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      lines: 2;
      text-overflow: ellipsis;
    }
    .dark-bg {
      color: #ffffff;
      opacity: 0.54;
    }
    .light-bg {
      color: rgba(0, 0, 0, 0.54);
    }
  }
}
</style>
