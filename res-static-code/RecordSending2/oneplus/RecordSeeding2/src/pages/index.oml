<template>
     <card-template category="graphic" entry="notification" onclick="cardClick">
            <lottie level="0:A*" src="{{$r('images.lottie_logo')}}" autoplay="{{recordInProgress}}" loop="true" voice-label="{{logoDescription}}"></lottie>
            <image level="0-1:A1*" src="{{$r('images.logo')}}" voice-label="{{logoDescription}}"></image>
            <text level="1:B*" transform-effect="none" voice-label="{{timeDescription}}">
              <span font-family="monospace">{{ dayText }}</span>
              <span>{{ daySeparator }}</span>
              <span font-family="monospace">{{ hourText }}</span>
              <span>{{ hourSeparator }}</span>
              <span font-family="monospace">{{ minuteText }}</span>
              <span>{{ minuteSeparator }}</span>
              <span font-family="monospace">{{ secondText }}</span>
            </text>

            <div level="6-1:G1" if="{{showG1}}">
              <text level="1-1:B1*" transform-effect="none" voice-label="{{timeDescription}}">
                <span font-family="monospace">{{ dayText }}</span>
                <span>{{ daySeparator }}</span>
                <span font-family="monospace">{{ hourText }}</span>
                <span>{{ hourSeparator }}</span>
                <span font-family="monospace">{{ minuteText }}</span>
                <span>{{ minuteSeparator }}</span>
                <span font-family="monospace">{{ secondText }}</span>
              </text>
              <text level="2-2:C2" transform-effect="none" voice-label="{{labelDescription}}">{{ labelText }}</text>
           </div>

           <div level="6-2:G2" else>
             <text level="1-2:B2*" transform-effect="none">{{ saveTitle }}</text>
             <text level="2-3:C3" transform-effect="none">{{ saveContent }}</text>
             <lottie level="3:D" src="{{ saveLottie }}"></lottie>
           </div>

           <button level="4-8:E8*" type="toggle"
             onclick= "recordBtnClick"
             disabled ="{{recordDisable}}"
             bg-color="{{recordBtnBgColor}}"
             checked="{{ recordInProgress }}"
             voice-label="{{recordBtnDescription}}"
             icon-on="{{recordImageOn}}"
             icon-off="{{recordImageOff}}"
             transition-on="{{transitionOn}}"
             transition-off="{{transitionOff}}"
             if="{{showG1}}">
           </button>
           <button level="4-9:E9" type="circle"
             onclick="markBtnClick"
             disabled ="{{labelDisable}}"
             color ="{{markBtnIconColor}}"
             bg-color ="{{$r('colors.mark_bg_color')}}"
             icon="{{$r('images.mark')}}"
             voice-label="{{labelBtnDescription}}"
             if="{{showG1}}">
           </button>

           <button level="4-7:E7" type="capsule"
             value="{{ saveBtn }}"
             onclick="saveClick"
             bg-color = "{{$r('colors.save_button_bg_color')}}"
             color="#ffffff"
             if="{{showG1}}">
           </button>
           <voice>{{ voiceLabel }}</voice>
     </card-template>
</template>

    <data>
    {
     "uiData": {
        "timeDescription": "",
        "labelText": "",
        "labelDescription":"",
        "labelBtnDescription":"",
        "recordBtnDescription":"",
        "recordDisable":false,
        "recordImageOn":"",
        "recordImageOff":"",
        "transitionOn":"$r('images.lottie_pause')",
        "transitionOff":"$r('images.lottie_start')",
        "labelDisable": false,
        "labelImage": "",
        "voiceLabel": true,
        "recordInProgress": true,
        "logoDescription":"",
        "recordBtnBgColor":"",
        "markBtnIconColor":"",
        "showG1":true,
        "saveBtn": "",
        "saveTitle":"",
        "saveContent":"",
        "saveLottie":"$r('images.lottie_saving')",
        "cardClickType":"",
        "fileName":"",
        "dayText":"",
        "hourText":"",
        "minuteText":"",
        "secondText":"",
        "daySeparator":"",
        "hourSeparator":"",
        "minuteSeparator":""
     },


     "uiEvent": {
        "recordBtnClick": {
            "type":'message',
            "uri":'com.oneplus.soundrecorder.card.event.provider',
            "method" :'pauseOrResume'
        },
        "markBtnClick": {
            "type":'message',
            "uri":'com.oneplus.soundrecorder.card.event.provider',
            "method" :'addLabel'
       },
       "saveClick": {
            "type":'message',
            "uri":'com.oneplus.soundrecorder.card.event.provider',
            "method" :'saveAudio'
       },
       "cardClick": {
             "type": "{{cardClickType}}",
             "package": 'com.oneplus.soundrecorder',
             "uri": 'nativeapp://oplus.intent.action.com.soundrecorder.SEEDLING_CARD',
             "params": {
                "should_auto_find_file_name": "{{fileName}}"
             }
       }
      }
  }
  </data>
